# API conventions & guidelines

## Context

In order to provide consistent behaviour by `data-platform` APIs, below are listed response formats and conventions used when
working with APIs. Of course that's not all we follow, but rather a small subset of guidelines lying on top of industry standard
best practices. Our reference for them is https://jsonapi.org/format/. We follow them, whenever something's not mentioned on this page.

## Goa Design and OpenAPI Spec

### General guidance
These are not entirely prescriptive and are more guidance to help unify our api docs and endpoints. If a point feels wrong or doesn't feel relevant in your case, raise it with the team to help come to a solution and update these docs!

#### Styling
- Service names should be capitalised "Service Name", not "service" or "Service name"
- Path parameters with an id should have only the "I" capitalised e.g. "groupId", not "groupID"
- Aim to use nouns in URL paths rather than verbs, as HTTP method should do this for you (`GET /users` rather than `GET /get-users`)
- When stats are aggregated for a resource, they should be referred to as `x-statistics` where x represents what we are getting statistics for. E.g. `/groups/{groupId}/charge-statistics` or `/groups/{groupId}/submitted-charge-statistics`
  - Statistics that are broken up into intervals should have the interval as a path parameter e.g. `/groups/{groupId}/charge-statistics/{interval}` where interval is `month`, `week`, `day` etc
- The "response type" should ideally come at the end url path as a general rule. I.e. `/groups/{groupId}/charges` is returning a list of charge entities, `/groups/{groupId}/drivers/charge-statistics` is returning a list of stats for each driver
- Use "users" and "userId" rather than "drivers" and "driverId", to avoid confusion with "EV Drivers" (an authoriser tier)
- Use "groups" and "groupId" instead of "organisations" and "organisationId"
- Try to add an example to a field in the goa design, to avoid it regenerating random numbers/strings each time, and helps with rfid tags etc.
- Aim to use `uuids` where possible to avoid surfacing database primary keys at the API level

#### Structure
- When to create a new goa "Service"
  - Aim to group methods within a service that makes functional sense, such as "Charge Statistics" all provide the service of giving statistics, whether that be for a group, site, etc.
  - One thing to consider, is if we were to split the API up into different APIs, could that service move as one and make sense as its own API?
  - Actions that result in creating commands and events can live with other Charge Commands unless another service feels more appropriate
- Things to include in a goa **_service_** description field:
  - Doesn't need to be overly prescriptive - service name should do the lifting here, but feel free to add anything that feels relevant
- Naming a goa **_method_**
  - The method name will be used in the code so it is important that it makes sense and is clear. You may have a "Charge Statistics" service, and have a method for "Sites", but this alone may be too vague when used alone in the codebase. It is recommended to name it "Site/Sites Charge Statistics"
  - Unfortunately, in the swagger doc, the service name is appended to the end of the method name and can cause some stutter, but clear code is more important than duplicate info in the api spec
- Things to include in a goa **_method_** description field:
  - Source of the data (projections vs DMS/podadmin)
  - Default ordering/sorting - which field? ASC/DESC?
  - Be clear on what the from/to ranges query on; are we dealing with plugged/unplugged/started/ended etc
  - Any non-standard filtering applied by the endpoint (e.g. does it show NEW & PROCESSED submitted-charges, or just NEW? `deleted_at IS NULL` checks can be assumed as standard)
- Put resources in order of their hierarchy within a path (group -> sites -> chargers, or groups -> drivers etc)
  - e.g. `POST /chargers/{chargerId}/users` with userId(s) in POST body, as we are linking a user to a charger rather than the other way round (and not like `/link-user/{userId}/charger/{chargerId}`)
  - e.g. `GET /groups/{groupId}/charge-statistics` (not `/charge-statistics/groups/{groupId}`)
- We can use multiple ways of listing a resource (e.g. charges), including:
  - These 3 endpoints can be separate (preferred):
    - `/groups/{groupId}/sites/{siteId}/charges`
    - `/groups/{groupId}/charges`
    - `/charges`
  - Or could be serviced by:
    - `/charges?groupId=&siteId=&...` - where it feels necessary for flexibility/requirements, as long as response shape can stay the same

## API response

### GET responses

As a base for all GET responses, we use the following structure at the top level:

```json
{
  "data": {},
  "meta": {},
  "errors": []
}
```

- `data` contains the actual data that was requested. Can be a collection of items or a single item.
- `meta` contains all the supplementary data related to the response. It's always a single item, but can contain various keys, like:
  - `summary` - any summary or stats related to returned data like count, but not explicitly requested (like aggregated by requested period) 
    and not domain data
  - `params` - the request params are always populated with all passed params
- `errors` - contains collection of all errors reported by the application, in unified format

### GET response types

Example response may look like one of below.

Single object:

```json
{
  "data": {
    "attr1": "some string",
    "attr2": 234,
    "attr3": false
  },
  "meta": {
    "params": {
      "param1": "abc"
    }
  }
}
```

Single object with nested objects or collections:

```json
{
  "data": {
    "summary": {
      "total": 938,
      "average": 10.5
    },
    "buckets": [
      {
        "attr1": 309,
        "attr2": 155
      }
    ]
  },
  "meta": {
    "params": {
      "param1": "abc"
    }
  }
}
```

Collection of objects:

```json
{
  "data": [
    {
      "attr1": "some string",
      "attr2": 234,
      "attr3": false
    },
    {
      "attr1": "other string",
      "attr2": 546,
      "attr3": true
    }
  ],
  "meta": {
    "params": {
      "param1": "abc",
      "param2": "def"
    },
    "summary": {
      "count": 122,
      "max": 600
    }
  }
}
```

### POST, PUT, PATCH, DELETE responses

No response payload is returned. The response codes are basing on standard REST responses: `201 Created` for `POST`
and `204 No Content` for `PUT`, `PATCH` and `DELETE`.

### Error responses

As outlined in the "Context" section, error responses will follow JSON API specification in a unified error response format like below.

```json
{
  "errors": [
    {
      "id": "qp3NOHkw",
      "title": "invalid_format",
      "detail":"chargeId must be formatted as a uuid but got value \"570b2ccf\", uuid: 570b2ccf: invalid UUID length: 20",
      "status": 400
    }
  ]
}
```

## Conventions / guidelines

### Do

- Use camel case for keys
- Use nested structures for related/hierarchical data (e.g. energy -> total, home, public)
- Return values using their respective types whenever possible (e.g. floating point returned as number, not string)

### Don't

- Use versioning, thus always need to be backward compatible 
- Use named keys for collections returned
