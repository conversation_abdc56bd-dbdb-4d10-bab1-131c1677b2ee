import {
  AttachDetachTestHelpersApi,
  GetPcbSwaps200Response,
  PcbSwapApi,
  PcbSwapStatus,
  SwapAPcb202Response,
  SwapPcbStatus200Response,
} from '@experience/shared/axios/assets-provisioning-api-client';
import { AxiosError, AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { PCBSwapsApi } from '@experience/installer/api/axios';
import { PCBSwapsNotificationService } from './pcb-swaps-notification.service';
import {
  PcbSwapError,
  PcbVirtualAttachError,
  PcbVirtualAttachNotWhitelistedError,
  PcbVirtualDetachError,
  PcbVirtualDetachNotWhitelistedError,
} from './pcb-swaps.errors';
import { PcbSwapsService } from './pcb-swaps.service';
import { SwapPcbResponse } from './pcb-swaps.dtos';
import { Test } from '@nestjs/testing';

jest.mock('@experience/shared/axios/assets-provisioning-api-client');

const assertExhaustiveSwitch = (c: never): never => {
  throw new TypeError(`Unhandled case: ${c}`);
};

describe('PcbSwapsService', () => {
  let service: PcbSwapsService;
  let pcbSwapApi: jest.MockedObject<PcbSwapApi>;
  let pcbAttachDetachApi: jest.MockedObject<AttachDetachTestHelpersApi>;
  let internalPcbSwapApi: DeepMocked<PCBSwapsApi>;

  afterEach(() => jest.clearAllMocks());

  beforeEach(async () => {
    pcbSwapApi = jest.mocked(new PcbSwapApi());
    pcbAttachDetachApi = jest.mocked(new AttachDetachTestHelpersApi());

    const moduleRef = await Test.createTestingModule({
      providers: [
        PcbSwapsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((param) => {
              switch (param) {
                case 'VIRTUAL_PSL_NUMBERS_ALLOWLIST':
                  return 'PSL-456789,PSL-123789';
                case 'PCB_SWAP_MAX_RETRY_COUNT':
                  return '1';
                default:
                  return undefined;
              }
            }),
          },
        },
        {
          provide: PcbSwapApi,
          useValue: pcbSwapApi,
        },
        {
          provide: AttachDetachTestHelpersApi,
          useValue: pcbAttachDetachApi,
        },
        {
          provide: PCBSwapsApi,
          useValue: createMock<PCBSwapsApi>(),
        },
        {
          provide: PCBSwapsNotificationService,
          useValue: createMock<PCBSwapsNotificationService>(),
        },
      ],
    }).compile();

    service = moduleRef.get<PcbSwapsService>(PcbSwapsService);
    internalPcbSwapApi = moduleRef.get(PCBSwapsApi);
  });

  type SwapStatues =
    | 'my_success' // this pcb swap
    | 'my_partial_success'
    | 'my_in_progress'
    | 'my_failed'
    | 'their_success' // another pcb swap
    | 'their_in_progress'
    | 'their_failed';
  type SwapResult =
    | 'immediately_succeed'
    | 'wait_and_succeed'
    | 'create_wait_and_succeed'
    | 'immediately_partially_succeed'
    | 'wait_and_partially_succeed'
    | 'create_wait_and_partially_succeed'
    | 'throw_in_progress';

  test.each<[SwapStatues[], SwapResult]>([
    // no swap history
    [[], 'create_wait_and_succeed'],
    [[], 'create_wait_and_partially_succeed'],
    // picking back up from an open attempt at this swap
    [['my_success', 'my_failed'], 'immediately_succeed'],
    [['my_in_progress', 'my_failed'], 'wait_and_succeed'],
    [['my_in_progress', 'my_failed'], 'wait_and_partially_succeed'],
    [['my_partial_success', 'my_failed'], 'immediately_partially_succeed'],
    // ...ignoring more recent failures if there's an older success/in-progress
    [['my_failed', 'my_failed', 'my_success'], 'immediately_succeed'],
    [['my_failed', 'my_failed', 'my_in_progress'], 'wait_and_succeed'],
    [
      ['my_failed', 'my_failed', 'my_partial_success'],
      'immediately_partially_succeed',
    ],
    [
      ['my_failed', 'my_failed', 'my_in_progress'],
      'wait_and_partially_succeed',
    ],
    // ... or starting a new swap if never successful
    [['my_failed', 'my_failed', 'my_failed'], 'create_wait_and_succeed'],
    [
      ['my_failed', 'my_failed', 'my_failed'],
      'create_wait_and_partially_succeed',
    ],
    // ignore swaps from different pcbs
    [['their_success'], 'create_wait_and_succeed'],
    [['their_failed'], 'create_wait_and_succeed'],
    [['their_success'], 'create_wait_and_partially_succeed'],
    [['their_failed'], 'create_wait_and_partially_succeed'],
    [['my_failed', 'their_failed', 'their_success'], 'create_wait_and_succeed'],
    [
      ['my_failed', 'their_failed', 'their_success'],
      'create_wait_and_partially_succeed',
    ],
    [['their_failed', 'my_success'], 'immediately_succeed'],
    [['their_failed', 'my_in_progress'], 'wait_and_succeed'],
    [['their_failed', 'my_partial_success'], 'immediately_partially_succeed'],
    [['their_failed', 'my_in_progress'], 'wait_and_partially_succeed'],
    [['my_failed', 'their_failed', 'my_failed'], 'create_wait_and_succeed'],
    [
      ['my_failed', 'their_failed', 'my_failed'],
      'create_wait_and_partially_succeed',
    ],
    // allow swapping an old pcb back in
    [['their_success', 'my_success'], 'create_wait_and_succeed'],
    [
      ['their_success', 'my_partial_success'],
      'create_wait_and_partially_succeed',
    ],
    // do not allow multiple in-progress swaps
    [['their_in_progress'], 'throw_in_progress'],
    [['my_failed', 'their_in_progress'], 'throw_in_progress'],
  ])(
    `Requesting a PCB Swap against a history %p should result in a %p`,
    async (statuses, result) => {
      const mySerialNumber = 'my-serial-number';
      const theirSerialNumber = 'their-serial-number';
      const myPpid = 'my-psl-number';

      const swaps = statuses.map((s, i) => {
        const [my, ...actions] = s.split('_');
        const action = actions.join('_');
        const serialNumber = my === 'my' ? mySerialNumber : theirSerialNumber;
        const status = {
          success: PcbSwapStatus.Success,
          partial_success: PcbSwapStatus.PartiallySuccessful,
          in_progress: PcbSwapStatus.InProgress,
          failed: PcbSwapStatus.Failed,
        }[action];
        return {
          id: `my-swap-id-${i}`,
          status,
          ppid: myPpid,
          serialNumber,
          timestamp: new Date().toISOString(),
          errorCode: 'my-error-code',
        };
      });
      pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
        status: 200,
        data: { data: swaps },
      } as AxiosResponse<GetPcbSwaps200Response, never>);

      pcbSwapApi.swapAPcb.mockResolvedValueOnce({
        status: 200,
        data: {
          id: 'my-new-swap-id',
        },
      } as AxiosResponse<SwapAPcb202Response, never>);

      switch (result) {
        case 'immediately_partially_succeed':
        case 'wait_and_partially_succeed':
        case 'create_wait_and_partially_succeed':
          {
            pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
              status: 200,
              data: {
                status: PcbSwapStatus.PartiallySuccessful,
              },
            } as AxiosResponse<SwapPcbStatus200Response, never>);
          }

          break;

        default: {
          pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
            status: 200,
            data: {
              status: PcbSwapStatus.Success,
            },
          } as AxiosResponse<SwapPcbStatus200Response, never>);

          break;
        }
      }

      let response: SwapPcbResponse;
      const params = {
        pcbSerialNumber: mySerialNumber,
        pslNumber: myPpid,
        unitSKU: 'my-sku',
      };
      try {
        response = await service.swapPcb(params);
      } catch (error) {
        if (result !== 'throw_in_progress') {
          expect(error).toBeUndefined();
        } else {
          expect(error).toEqual(
            new PcbSwapError({
              causes:
                'A PCB Swap (to Serial No. their-serial-number) is already in progress.',
              retryable: true,
            })
          );
        }
      }

      switch (result) {
        case 'immediately_succeed': {
          expect(response).toEqual({
            message: 'The PCB has been successfully swapped.',
            status: 'Success',
          });
          expect(pcbSwapApi.swapAPcb).not.toHaveBeenCalled();
          expect(pcbSwapApi.swapPcbStatus).not.toHaveBeenCalled();

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'success',
          });

          break;
        }
        case 'wait_and_succeed': {
          expect(response).toEqual({
            message: 'The PCB has been successfully swapped.',
            status: 'Success',
          });
          expect(pcbSwapApi.swapAPcb).not.toHaveBeenCalled();
          const id = statuses.findIndex((s) =>
            ['my_in_progress', 'my_success'].includes(s)
          );
          expect(pcbSwapApi.swapPcbStatus).toHaveBeenCalledWith(
            `my-swap-id-${id}`
          );

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'success',
          });

          break;
        }
        case 'create_wait_and_succeed': {
          expect(response).toEqual({
            status: 'Success',
            message: 'The PCB has been successfully swapped.',
          });
          expect(pcbSwapApi.swapAPcb).toHaveBeenCalledWith({
            ppid: myPpid,
            serialNumber: mySerialNumber,
          });
          expect(pcbSwapApi.swapPcbStatus).toHaveBeenCalledWith(
            'my-new-swap-id'
          );

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'success',
          });

          break;
        }
        case 'immediately_partially_succeed': {
          expect(response).toEqual({
            message: 'The PCB has been successfully swapped.',
            status: 'Partially Successful',
          });
          expect(pcbSwapApi.swapAPcb).not.toHaveBeenCalled();
          expect(pcbSwapApi.swapPcbStatus).not.toHaveBeenCalled();

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'partial_success',
          });

          break;
        }
        case 'wait_and_partially_succeed': {
          expect(response).toEqual({
            message: 'The PCB has been successfully swapped.',
            status: 'Partially Successful',
          });
          expect(pcbSwapApi.swapAPcb).not.toHaveBeenCalled();
          const id = statuses.findIndex((s) =>
            ['my_in_progress', 'my_success'].includes(s)
          );
          expect(pcbSwapApi.swapPcbStatus).toHaveBeenCalledWith(
            `my-swap-id-${id}`
          );

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'partial_success',
          });

          break;
        }
        case 'create_wait_and_partially_succeed': {
          expect(response).toEqual({
            status: 'Partially Successful',
            message: 'The PCB has been successfully swapped.',
          });
          expect(pcbSwapApi.swapAPcb).toHaveBeenCalledWith({
            ppid: myPpid,
            serialNumber: mySerialNumber,
          });
          expect(pcbSwapApi.swapPcbStatus).toHaveBeenCalledWith(
            'my-new-swap-id'
          );

          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledTimes(1);
          expect(
            internalPcbSwapApi.pcbSwapsControllerRegisterPcbSwap
          ).toHaveBeenCalledWith({
            ppid: params.pslNumber,
            serialNumber: params.pcbSerialNumber,
            status: 'partial_success',
          });

          break;
        }
        case 'throw_in_progress': {
          expect(response).toBeUndefined();
          expect(pcbSwapApi.swapAPcb).not.toHaveBeenCalled();
          expect(pcbSwapApi.swapPcbStatus).not.toHaveBeenCalled();
          break;
        }
        default:
          assertExhaustiveSwitch(result);
      }
    }
  );

  test('should gracefully handle the provisioning api being down', async () => {
    pcbSwapApi.getPcbSwaps.mockRejectedValueOnce(
      new AxiosError('Server Error', '500')
    );

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
      unitSKU: 'my-sku',
    };

    await expect(service.swapPcb(params)).rejects.toEqual(
      new PcbSwapError({
        causes: 'An unexpected error occurred while fetching PCB swaps.',
        retryable: false,
      })
    );
  });

  test.each([
    [
      new AxiosError('Bad Request', '400'),
      'An unexpected error occurred while swapping the PCB.',
      true,
    ],
    [
      new AxiosError('Conflict', '409', undefined, undefined, {
        status: 409,
        data: { message: 'my-error-message' },
      } as AxiosResponse),
      'my-error-message',
      true,
    ],
    [
      new AxiosError('Conflict', '409', undefined, undefined, {
        status: 400,
        data: { message: ['my-error-message-one', 'my-error-message-two'] },
      } as AxiosResponse),
      ['my-error-message-one', 'my-error-message-two'],
      true,
    ],
    [
      new AxiosError('Bad Request', '500'),
      'An unexpected error occurred while swapping the PCB.',
      false,
    ],
    [
      new AxiosError('Service Unavailable', '503', undefined, undefined, {
        status: 503,
        data: { message: 'Service Unavailable' },
      } as AxiosResponse),
      'Service Unavailable',
      false,
    ],
    [
      new Error('ECONNREFUSED'),
      'An unexpected error occurred while swapping the PCB.',
      false,
    ],
    [
      new TypeError('Swap request did not return a valid swap ID'),
      'An unexpected error occurred while swapping the PCB.',
      false,
    ],
  ])(
    'should gracefully handle errors when starting a new swap: %p becomes %p (retryable: %p)',
    async (error, cause, retryable) => {
      pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
        status: 200,
        data: { data: [] },
      } as AxiosResponse<GetPcbSwaps200Response, never>);
      pcbSwapApi.swapAPcb.mockRejectedValueOnce(error);

      const params = {
        pcbSerialNumber: 'my-serial-number',
        pslNumber: 'my-psl-number',
        unitSKU: 'my-sku',
      };

      await expect(service.swapPcb(params)).rejects.toEqual(
        new PcbSwapError({
          causes: cause,
          retryable,
        })
      );
    }
  );

  test('should handle swap request not returning id', async () => {
    pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
      status: 200,
      data: { data: [] },
    } as AxiosResponse<GetPcbSwaps200Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValue({
      status: 200,
      data: {},
    } as AxiosResponse<SwapAPcb202Response, never>);

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
      unitSKU: 'my-sku',
    };

    await expect(service.swapPcb(params)).rejects.toEqual(
      new PcbSwapError({
        causes: 'An unexpected error occurred while swapping the PCB.',
        retryable: false,
      })
    );
  });

  test('it should poll the provisioning api until a terminal status is reached', async () => {
    pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
      status: 200,
      data: { data: [] },
    } as AxiosResponse<GetPcbSwaps200Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValueOnce({
      status: 200,
      data: { id: 'my-swap-id' },
    } as AxiosResponse<SwapAPcb202Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: { status: PcbSwapStatus.InProgress },
    } as AxiosResponse<SwapPcbStatus200Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: { status: PcbSwapStatus.InProgress },
    } as AxiosResponse<SwapPcbStatus200Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: { status: PcbSwapStatus.Success },
    } as AxiosResponse<SwapPcbStatus200Response, never>);

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
    };

    const timeIn = Date.now();
    const response = await service.swapPcb(params);
    const timeOut = Date.now();
    expect(response).toEqual({
      message: 'The PCB has been successfully swapped.',
      status: 'Success',
    });
    expect(pcbSwapApi.swapPcbStatus).toHaveBeenCalledTimes(3);
    expect(timeOut - timeIn).toBeGreaterThanOrEqual(2000);
  });

  test('it should interpret errors from a successfully requested but failed swap request', async () => {
    pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
      status: 200,
      data: { data: [] },
    } as AxiosResponse<GetPcbSwaps200Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValueOnce({
      status: 200,
      data: { id: 'my-swap-id' },
    } as AxiosResponse<SwapAPcb202Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: {
        status: PcbSwapStatus.Failed,
        errorCode: 'EAP0006',
      },
    } as AxiosResponse<SwapPcbStatus200Response, never>);

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
      unitSKU: 'my-sku',
    };

    await expect(service.swapPcb(params)).rejects.toEqual(
      new PcbSwapError({
        causes: 'EAP0006: Failed to find the charging station.',
        retryable: false,
      })
    );
  });

  test('it should automatically retry on a failed swap request when the error code is automatically retryable', async () => {
    pcbSwapApi.getPcbSwaps.mockResolvedValueOnce({
      status: 200,
      data: { data: [] },
    } as AxiosResponse<GetPcbSwaps200Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValueOnce({
      status: 200,
      data: { id: 'my-swap-id' },
    } as AxiosResponse<SwapAPcb202Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValueOnce({
      status: 200,
      data: { id: 'my-swap-id' },
    } as AxiosResponse<SwapAPcb202Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: {
        status: PcbSwapStatus.Failed,
        errorCode: 'EAP0023',
      },
    } as AxiosResponse<SwapPcbStatus200Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValueOnce({
      status: 200,
      data: {
        status: PcbSwapStatus.Success,
      },
    } as AxiosResponse<SwapPcbStatus200Response, never>);

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
    };

    await expect(service.swapPcb(params)).resolves.toEqual({
      message: 'The PCB has been successfully swapped.',
      status: 'Success',
    });
  });

  test('it should throw an error on an automatically retried swap request when the attempts exceed the maximum', async () => {
    pcbSwapApi.getPcbSwaps.mockResolvedValue({
      status: 200,
      data: { data: [] },
    } as AxiosResponse<GetPcbSwaps200Response, never>);
    pcbSwapApi.swapAPcb.mockResolvedValue({
      status: 200,
      data: { id: 'my-swap-id' },
    } as AxiosResponse<SwapAPcb202Response, never>);
    pcbSwapApi.swapPcbStatus.mockResolvedValue({
      status: 200,
      data: {
        status: PcbSwapStatus.Failed,
        errorCode: 'EAP0023',
      },
    } as AxiosResponse<SwapPcbStatus200Response, never>);

    const params = {
      pcbSerialNumber: 'my-serial-number',
      pslNumber: 'my-psl-number',
      unitSKU: 'my-sku',
    };

    await expect(service.swapPcb(params)).rejects.toEqual(
      new PcbSwapError({
        causes: 'EAP0023: The new PCB is not currently online.',
        retryable: true,
      })
    );

    expect(pcbSwapApi.swapAPcb).toHaveBeenCalledTimes(2);
  });

  describe('attachVirtualPcb', () => {
    it('throws a PcbVirtualAttachNotWhitelistedError if the given pslNumber is not whitelisted', async () => {
      await expect(
        service.attachVirtualPcb({
          pslNumber: 'PSL-12345',
          pcbSerialNumber: '2300123456',
        })
      ).rejects.toThrow(PcbVirtualAttachNotWhitelistedError);
    });

    it('attempts to attach a PCB via the API', async () => {
      await service.attachVirtualPcb({
        pslNumber: 'PSL-456789',
        pcbSerialNumber: '2300123456',
      });

      expect(pcbAttachDetachApi.attachAPcb).toHaveBeenCalledTimes(1);
      expect(pcbAttachDetachApi.attachAPcb).toHaveBeenCalledWith('PSL-456789', {
        serialNumber: '2300123456',
      });
    });

    it('returns a success message on successful attaching of a virtual PCB', async () => {
      const res = await service.attachVirtualPcb({
        pslNumber: 'PSL-456789',
        pcbSerialNumber: '2300123456',
      });

      expect(res).toStrictEqual({
        message: 'The virtual PCB has been successfully attached.',
      });
    });

    it('throws a PcbVirtualAttachError if the virtual PCB fails to attach', async () => {
      pcbAttachDetachApi.attachAPcb.mockRejectedValue(new Error());

      await expect(
        service.attachVirtualPcb({
          pslNumber: 'PSL-456789',
          pcbSerialNumber: '2300123456',
        })
      ).rejects.toThrow(PcbVirtualAttachError);
    });
  });

  describe('detachVirtualPcb', () => {
    it('throws a PcbVirtualDetachNotWhitelistedError if the given pslNumber is not whitelisted', async () => {
      await expect(
        service.detachVirtualPcb({
          pslNumber: 'PSL-12345',
          pcbSerialNumber: '2300123456',
        })
      ).rejects.toThrow(PcbVirtualDetachNotWhitelistedError);
    });

    it('attempts to detach a PCB via the API', async () => {
      await service.detachVirtualPcb({
        pslNumber: 'PSL-456789',
        pcbSerialNumber: '2300123456',
      });

      expect(pcbAttachDetachApi.detachAPcb).toHaveBeenCalledTimes(1);
      expect(pcbAttachDetachApi.detachAPcb).toHaveBeenCalledWith(
        'PSL-456789',
        '2300123456'
      );
    });

    it('returns a successful message on successful detaching of a virtual PCB', async () => {
      const res = await service.detachVirtualPcb({
        pslNumber: 'PSL-456789',
        pcbSerialNumber: '2300123456',
      });

      expect(res).toStrictEqual({
        message: 'The virtual PCB has been successfully detached.',
      });
    });

    it('throws a PcbVirtualDetachError if the virtual PCB fails to detach', async () => {
      pcbAttachDetachApi.detachAPcb.mockRejectedValue(new Error());

      await expect(
        service.detachVirtualPcb({
          pslNumber: 'PSL-456789',
          pcbSerialNumber: '2300123456',
        })
      ).rejects.toThrow(PcbVirtualDetachError);
    });
  });
});
