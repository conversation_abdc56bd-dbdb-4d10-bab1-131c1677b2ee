import * as crypto from 'crypto';
import { BadRequestException } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { FirebaseRestService } from './firebase.rest.service';
import { OAuthAuthorizeService } from './oauth.authorize.service';
import { OAuthClientService } from './oauth.client.service';
import {
  OAuthCustomClaimsWithFirebaseExtensions,
  OAuthTokenService,
} from './oauth.token.service';
import { Test } from '@nestjs/testing';
import { getAuth } from '@experience/shared/firebase/admin';
import jwt from 'jsonwebtoken';

jest.mock('@experience/shared/firebase/admin');

describe(OAuthTokenService.name, () => {
  let service: OAuthTokenService;
  let oAuthClientService: jest.Mocked<Partial<OAuthClientService>>;
  let oAuthAuthorizeService: OAuthAuthorizeService;
  let firebaseRestService: jest.Mocked<Partial<FirebaseRestService>>;
  let firebaseAuth: jest.Mocked<ReturnType<typeof getAuth>>;

  beforeEach(async () => {
    oAuthClientService = {
      getClient: jest.fn(),
    } as typeof oAuthClientService;
    const configService = new ConfigService();
    oAuthAuthorizeService = new OAuthAuthorizeService(
      oAuthClientService as unknown as OAuthClientService,
      configService
    );
    firebaseRestService = {
      refreshToken: jest.fn(),
      signInWithPassword: jest.fn(),
      signInWithCustomToken: jest.fn(),
    } as typeof firebaseRestService;

    firebaseAuth = {
      createCustomToken: jest.fn(),
    } as Partial<typeof firebaseAuth> as unknown as typeof firebaseAuth;
    (getAuth as jest.MockedFunction<typeof getAuth>).mockReturnValue(
      firebaseAuth
    );

    const module = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        OAuthTokenService,
        {
          provide: OAuthClientService,
          useValue: oAuthClientService,
        },
        {
          provide: OAuthAuthorizeService,
          useValue: oAuthAuthorizeService,
        },
        {
          provide: FirebaseRestService,
          useValue: firebaseRestService,
        },
      ],
    }).compile();

    service = module.get<OAuthTokenService>(OAuthTokenService);
  });

  describe('getToken (authorization_code)', () => {
    it('exchanges a valid code for a token', async () => {
      oAuthClientService.getClient.mockResolvedValue({
        id: 'client-uuid',
        clientId: 'client-id',
        allowedScopes: [{ id: 1, name: 'read:example' }],
        grantType: 'code',
        imageUrl: 'https://example.com/image.png',
        websiteUrl: 'https://example.com',
        maximumSessionLengthSeconds: 3_600,
        name: 'Example',
        redirectUris: ['https://example.com/redirect'],
        secretSalt: 'salty',
      });
      const claims: OAuthCustomClaimsWithFirebaseExtensions = {
        'https://pod-point.com/oauth/nonce':
          'qdgLLRr1saFHT6DWfWU28VNPIi7e9ynEBnBG3Oadw9g.e42a0a5bb8748cbf839288d5fafd472345628a7f348f275713429fbfa56dd884',
        'https://pod-point.com/oauth/third_party': true,
        client_id: 'client-id',
        scope: 'read:example',
        email: '<EMAIL>',
        email_verified: true,
        auth_time: **********,
      };
      const idToken = jwt.sign(claims, 'secret-key');
      firebaseRestService.signInWithCustomToken.mockResolvedValue({
        idToken,
        expiresIn: '3600',
        refreshToken: 'refresh-token',
      });

      const customToken = jwt.sign(
        { iat: Math.floor(Date.now() / 1000) },
        'secret'
      );
      const iv = crypto.randomBytes(16);
      const key = crypto.createHash('sha256').update('salty').digest();
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let code = cipher.update(customToken, 'utf8', 'base64url');
      code += cipher.final('base64url');
      code = `${iv.toString('base64url')}.${code}}`;
      const token = await service.getToken({
        grantType: 'authorization_code',
        clientId: 'client-id',
        code,
        codeVerifier: 'code-verifier',
        redirectUri: 'https://example.com/redirect',
      });

      expect(token).toEqual({
        accessToken: idToken,
        tokenType: 'Bearer',
        expiresIn: 3600,
        refreshToken: 'refresh-token',
        scope: 'read:example',
      });
    });

    it('enforces that a token was issued to a verified user', async () => {
      oAuthClientService.getClient.mockResolvedValue({
        id: 'client-uuid',
        clientId: 'client-id',
        allowedScopes: [{ id: 1, name: 'read:example' }],
        grantType: 'code',
        imageUrl: 'https://example.com/image.png',
        websiteUrl: 'https://example.com',
        maximumSessionLengthSeconds: 3_600,
        name: 'Example',
        redirectUris: ['https://example.com/redirect'],
        secretSalt: 'salty',
      });
      const claims: OAuthCustomClaimsWithFirebaseExtensions = {
        'https://pod-point.com/oauth/nonce':
          'qdgLLRr1saFHT6DWfWU28VNPIi7e9ynEBnBG3Oadw9g.e42a0a5bb8748cbf839288d5fafd472345628a7f348f275713429fbfa56dd884',
        'https://pod-point.com/oauth/third_party': true,
        client_id: 'client-id',
        scope: 'read:example',
        auth_time: **********,
      };
      const idToken = jwt.sign(claims, 'secret-key');
      firebaseRestService.signInWithCustomToken.mockResolvedValue({
        idToken,
        expiresIn: '3600',
        refreshToken: 'refresh-token',
      });

      const customToken = jwt.sign(
        { iat: Math.floor(Date.now() / 1000) },
        'secret'
      );
      const iv = crypto.randomBytes(16);
      const key = crypto.createHash('sha256').update('salty').digest();
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let code = cipher.update(customToken, 'utf8', 'base64url');
      code += cipher.final('base64url');
      code = `${iv.toString('base64url')}.${code}}`;

      await expect(
        service.getToken({
          grantType: 'authorization_code',
          clientId: 'client-id',
          code,
          codeVerifier: 'code-verifier',
          redirectUri: 'https://example.com/redirect',
        })
      ).rejects.toThrow(BadRequestException);
    });

    it('enforces that a token was issued recently', async () => {
      oAuthClientService.getClient.mockResolvedValue({
        id: 'client-uuid',
        clientId: 'client-id',
        allowedScopes: [{ id: 1, name: 'read:example' }],
        grantType: 'code',
        imageUrl: 'https://example.com/image.png',
        websiteUrl: 'https://example.com',
        maximumSessionLengthSeconds: 3_600,
        name: 'Example',
        redirectUris: ['https://example.com/redirect'],
        secretSalt: 'salty',
      });
      const claims: OAuthCustomClaimsWithFirebaseExtensions = {
        'https://pod-point.com/oauth/nonce':
          'qdgLLRr1saFHT6DWfWU28VNPIi7e9ynEBnBG3Oadw9g.e42a0a5bb8748cbf839288d5fafd472345628a7f348f275713429fbfa56dd884',
        'https://pod-point.com/oauth/third_party': true,
        client_id: 'client-id',
        scope: 'read:example',
        email: '<EMAIL>',
        email_verified: true,
        auth_time: **********,
      };
      const idToken = jwt.sign(claims, 'secret-key');
      firebaseRestService.signInWithCustomToken.mockResolvedValue({
        idToken,
        expiresIn: '3600',
        refreshToken: 'refresh-token',
      });

      const customToken = jwt.sign(
        { iat: Math.floor(Date.now() / 1000) - 10 * 60 },
        'secret'
      );
      const iv = crypto.randomBytes(16);
      const key = crypto.createHash('sha256').update('salty').digest();
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let code = cipher.update(customToken, 'utf8', 'base64url');
      code += cipher.final('base64url');
      code = `${iv.toString('base64url')}.${code}}`;

      await expect(
        service.getToken({
          grantType: 'authorization_code',
          clientId: 'client-id',
          code,
          codeVerifier: 'code-verifier',
          redirectUri: 'https://example.com/redirect',
        })
      ).rejects.toThrow(BadRequestException);
    });
  });
});
