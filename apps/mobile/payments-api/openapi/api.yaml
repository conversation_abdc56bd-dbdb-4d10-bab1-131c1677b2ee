openapi: 3.0.0
paths:
  /health:
    get:
      operationId: HealthController_check
      summary: Get Payments API health
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example:
                      database:
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database:
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example:
                      database:
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis:
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database:
                        status: up
                      redis:
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      tags:
        - Healthcheck
  /bank-accounts:
    get:
      operationId: BankAccountController_searchBankAccounts
      summary: Search all bank accounts
      parameters:
        - name: authId
          required: true
          in: query
          description: The Auth ID of the user who the bank accounts belong to
          schema:
            type: string
        - name: recipientId
          required: true
          in: query
          description: The recipient id associated with the bank account
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BankAccountResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Bank Accounts
    post:
      operationId: BankAccountController_createBankAccount
      summary: Create a new bank account
      description: Create a new bank account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBankAccountRequestDTO'
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponse'
        '404':
          description: Returned when recipient not found
        '422':
          description: Returned when request failed confirmation of Payee
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmationOfPayeeDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Bank Accounts
  /bank-accounts/{bankAccountId}:
    get:
      operationId: BankAccountController_getBankAccountById
      summary: Retrieve a bank account by ID
      description: Retrieve a bank account by ID
      parameters:
        - name: bankAccountId
          required: true
          in: path
          description: The bank account ID to be retrieved
          schema:
            type: string
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponse'
        '404':
          description: Returned when bank account not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Bank Accounts
    delete:
      operationId: BankAccountController_archiveBankAccount
      summary: archive a bank account that will no longer be used
      description: Archive bank account that will no longer be used
      parameters:
        - name: bankAccountId
          required: true
          in: path
          description: The bank account ID which should be archived
          schema:
            type: string
      responses:
        '204':
          description: Returned when account is successfully archived
        '404':
          description: Returned when recipient or bank account not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Bank Accounts
  /recipients:
    get:
      operationId: RecipientsController_searchRecipients
      summary: Search all recipients
      parameters:
        - name: authId
          required: true
          in: query
          description: The Auth ID of the user who the recipient belong to
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RecipientDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Recipients
    post:
      operationId: RecipientsController_createRecipient
      summary: Create a new recipient
      description: Create a new recipient
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRecipientDTO'
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipientDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Recipients
  /recipients/{recipientId}:
    get:
      operationId: RecipientsController_getRecipientById
      summary: Retrieve a recipient by ID
      description: Retrieve a recipient by ID
      parameters:
        - name: recipientId
          required: true
          in: path
          description: The ID of the recipient to retrieve
          schema:
            type: string
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipientDTO'
        '404':
          description: Returned when recipient not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Recipients
  /transactions:
    get:
      operationId: TransactionsController_getTransactions
      summary: Searches all transactions
      description: Searches all transactions
      parameters:
        - name: authId
          required: true
          in: query
          description: The auth id associated with the transaction
          schema:
            type: string
        - name: recipientId
          required: true
          in: query
          description: The recipient id associated with the transaction
          schema:
            type: string
        - name: bankAccountId
          required: true
          in: query
          description: The bank accounk id associated with the transaction
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Transactions
    post:
      operationId: TransactionsController_createTransaction
      summary: Create a transaction to initiate a payment
      description: Create a transaction to initiate a payment
      parameters:
        - name: x-idempotency-key
          required: false
          in: header
          description: >-
            The idempotency key to use with this operation.  One will be
            generated if not provided
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTransactionDTO'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionDTO'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '403':
          description: Bank account does not belong to given user
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Bank account or recipient was not found for provided IDs
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Transactions
  /transactions/{transactionId}:
    get:
      operationId: TransactionsController_getTransactionById
      summary: Retrieve a transaction by ID
      description: Retrieve a transaction by ID
      parameters:
        - name: transactionId
          required: true
          in: path
          description: The ID of the transaction to retrieve
          schema:
            type: string
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionDTO'
        '404':
          description: Returned when recipient not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Transactions
  /webhook:
    post:
      operationId: WebhookController_handleWebhook
      parameters:
        - name: Stripe-Signature
          in: header
          description: Used for verifying the request
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Buffer'
      responses:
        '200':
          description: The received event has been successfully sent for processing
        '400':
          description: The event is not valid
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Webhook
info:
  title: Payments API
  description: Payments API service
  version: 1.0.0
  contact: {}
tags: []
servers: []
components:
  schemas:
    BankAccountResponse:
      type: object
      properties:
        id:
          type: string
          description: The ID of the bank account
        recipientId:
          type: string
          description: The id of the recipient this bank account is associated with
          example: 2b0685d6-3254-4207-9b35-02cc415dbd2d
        nameOnAccount:
          type: string
          description: The name associated with the bank account
        accountNumber:
          type: string
          description: The last 4 digits of the account number
        sortCode:
          type: string
          description: The sort code associated with the bank account
      required:
        - id
        - name
        - last4
    AddressDTO:
      type: object
      properties:
        city:
          type: string
          description: The city of the address
          example: London
        country:
          type: string
          description: The country of the address
          example: GB
        county:
          type: string
          description: The county of the address
          example: London
        line1:
          type: string
          description: The first line of the address
          example: 222 Gray's Inn Road
        line2:
          type: string
          description: The second line of the address
        postcode:
          type: string
          description: The post code of the address
          example: WC1X 8HB
      required:
        - city
        - country
        - county
        - line1
        - postcode
    CreateBankAccountRequestDTO:
      type: object
      properties:
        recipientId:
          type: string
          description: The id of the recipient this bank account is associated with
          example: 2b0685d6-3254-4207-9b35-02cc415dbd2d
        nameOnAccount:
          type: string
          description: The name associated with the bank account for verification
          example: Jenny Rosen
        accountNumber:
          type: string
          description: The account number of the bank account
          example: '********'
        sortCode:
          type: string
          description: The sort code of the bank account
          example: '108800'
      required:
        - authId
        - recipientId
        - nameOnAccount
        - accountNumber
        - sortCode
    ConfirmationOfPayeeDTO:
      type: object
      properties:
        status:
          type: string
          description: The status of the confirmation of payee check
          enum:
            - MATCH
            - MISMATCH
            - PARTIAL_MATCH
            - UNAVAILABLE
        provided:
          type: string
          description: The name provided
          example: Jenny Rosen
        suggested:
          type: string
          description: >-
            The suggested name associated with the bank account if status is
            PARTIAL
          example: Jennifier Rosen
          nullable: true
      required:
        - status
        - provided
        - suggested
    CreateRecipientDTO:
      type: object
      properties:
        authId:
          type: string
          description: The auth id of the user who the recipient belongs to
          example: 2b0685d6-3254-4207-9b35-02cc415dbd2d
        email:
          type: string
          description: The email for the recipient
        address:
          description: The address for the recipient
          allOf:
            - $ref: '#/components/schemas/AddressDTO'
        firstName:
          type: string
          description: The first name for the recipient
          example: Jenny
        lastName:
          type: string
          description: The last name of the recipient
          example: Rosen
      required:
        - email
        - address
        - firstName
        - lastName
    RecipientDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the recipient
        authId:
          type: string
          description: The auth id of the user who the recipient belongs to
          example: 2b0685d6-3254-4207-9b35-02cc415dbd2d
        email:
          type: string
          description: The email for the recipient
        address:
          description: The address for the recipient
          allOf:
            - $ref: '#/components/schemas/AddressDTO'
        firstName:
          type: string
          description: The first name for the recipient
          example: Jenny
        lastName:
          type: string
          description: The last name of the recipient
          example: Rosen
      required:
        - id
        - email
        - address
        - firstName
        - lastName
    TransactionDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the transaction
        authId:
          type: string
          description: The auth ID of the user who the payment is for
        recipientId:
          type: string
          description: The recipient ID whom the payment is being made to
        bankAccountId:
          type: string
          description: The ID of the bank account to make the payout from
        value:
          type: number
          description: >-
            The value of the transaction in the lowest currency unit (pence for
            GBP)
        currency:
          type: string
          description: The currency that the transaction is in
        status:
          type: string
          description: The current status of the transaction
          enum:
            - PENDING
            - CANCELLED
            - RETURNED
            - POSTED
            - FAILED
        metadata:
          allOf:
            - $ref: '#/components/schemas/TransactionMetadataDTO'
          description: The metadata associated with this transaction
        createdAt:
          type: string
          description: The date/time this payment was made
      required:
        - id
        - authId
        - recipientId
        - bankAccountId
        - value
        - currency
        - status
        - createdAt
    TransactionMetadataDTO:
      type: object
      properties:
        type:
          type: string
          description: The type of metadata associated with this transaction
          enum:
            - REWARDS
      required:
        - type
    CreateTransactionDTO:
      type: object
      properties:
        bankAccountId:
          type: string
          description: The ID of the bank account to make the payout from
        currency:
          type: string
          description: The currency to payout in
          enum:
            - GBP
        amount:
          type: number
          description: The amount to pay out in the lowest unit of the currency
        metadata:
          allOf:
            - $ref: '#/components/schemas/TransactionMetadataDTO'
          description: The metadata associated with this transaction
      required:
        - authId
        - recipientId
        - bankAccountId
        - currency
        - amount
    Buffer:
      type: object
      properties: {}
