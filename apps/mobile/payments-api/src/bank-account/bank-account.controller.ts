import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { ApiInternalServerException } from '@experience/mobile/nest/swagger';
import { AxiosHttpExceptionFilter } from '@experience/mobile/nest/exception';
import { BankAccountConfirmationOfPayeeFilter } from './bank-account.filter';
import { BankAccountInterceptor } from './bank-account.interceptor';
import {
  BankAccountResponse,
  ConfirmationOfPayeeDTO,
  CreateBankAccountRequestDTO,
} from './bank-account.types';
import { BankAccountService } from './bank-account.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { RecipientsService } from '../recipients/recipients.service';

@Controller('bank-accounts')
@ApiTags('Bank Accounts')
@UseFilters(AxiosHttpExceptionFilter, BankAccountConfirmationOfPayeeFilter)
@UseInterceptors(BankAccountInterceptor)
export class BankAccountController {
  constructor(
    private readonly bankAccountService: BankAccountService,
    private readonly recipientsService: RecipientsService
  ) {}

  @Get(':authId')
  @ApiOperation({
    summary: 'get all bank accounts for the given user',
  })
  @ApiParam({
    name: 'authId',
    description: 'The Auth ID of the user who the bank accounts belong to',
  })
  @ApiOkResponse({
    description: 'OK response',
    type: [BankAccountResponse],
  })
  @ApiInternalServerException()
  async getAllBankAccounts(
    @Param('authId') authId: string
  ): Promise<BankAccountResponse[]> {
    return this.bankAccountService.getAllBankAccounts(authId);
  }

  @Post(':authId')
  @ApiOperation({
    summary: 'create bank account for rewards payout',
    description: 'Create bank account for rewards payout',
  })
  @ApiParam({
    name: 'authId',
    description:
      'The auth id of the user for which a bank account is to be created',
  })
  @ApiCreatedResponse({
    description: 'OK response',
    type: BankAccountResponse,
  })
  @ApiNotFoundResponse({
    description: 'Returned when billing account not found',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Returned when request failed confirmation of Payee',
    type: ConfirmationOfPayeeDTO,
  })
  @ApiInternalServerException()
  async createBankAccount(
    @Param('authId') authId: string,
    @Body() request: CreateBankAccountRequestDTO
  ): Promise<BankAccountResponse> {
    await this.recipientsService.createOrUpdateRecipient(authId, request);

    return await this.bankAccountService.createBankAccount(authId, request);
  }

  @Put(':authId/:bankAccountId')
  @ApiParam({
    name: 'authId',
    description:
      'The auth id of the user for which the bank account is to be updated',
  })
  @ApiParam({
    name: 'bankAccountId',
    description: 'The bank account ID which should be updated',
  })
  @ApiOperation({
    summary:
      'update a bank account (archive old and create a new bank account)',
    description:
      'Update a bank account (archive and create a new bank account)',
  })
  @ApiCreatedResponse({
    description: 'Returned when account is successfully updated',
    type: BankAccountResponse,
  })
  @ApiNotFoundResponse({
    description: 'Returned when bank account not found',
  })
  @ApiUnprocessableEntityResponse({
    description: 'Returned when request failed confirmation of Payee',
    type: ConfirmationOfPayeeDTO,
  })
  @ApiInternalServerException()
  @HttpCode(201)
  async updateBankAccount(
    @Param('authId') authId: string,
    @Param('bankAccountId') bankAccountId: string,
    @Body() request: CreateBankAccountRequestDTO
  ): Promise<BankAccountResponse> {
    await this.recipientsService.createOrUpdateRecipient(authId, request);

    return await this.bankAccountService.updateBankAccount(
      authId,
      bankAccountId,
      request
    );
  }

  @Delete(':authId/:bankAccountId')
  @ApiParam({
    name: 'authId',
    description:
      'The auth id of the user for which the bank account is to be archived',
  })
  @ApiParam({
    name: 'bankAccountId',
    description: 'The bank account ID which should be archived',
  })
  @ApiOperation({
    summary: 'archive a bank account that will no longer be used',
    description: 'Archive bank account that will no longer be used',
  })
  @ApiNoContentResponse({
    description: 'Returned when account is successfully archived',
  })
  @ApiNotFoundResponse({
    description: 'Returned when billing account or bank account not found',
  })
  @ApiInternalServerException()
  @HttpCode(204)
  async archiveBankAccount(
    @Param('authId') authId: string,
    @Param('bankAccountId') bankAccountId: string
  ) {
    return await this.bankAccountService.archiveBankAccount(
      authId,
      bankAccountId
    );
  }
}
