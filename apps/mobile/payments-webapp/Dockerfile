FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb
ARG APPLICATION_VERSION
ARG SENTRY_RELEASE

ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE
WORKDIR /app

COPY dist/apps/mobile/payments-webapp .
RUN npm install
CMD [ "npx", "next", "start", "-p", "5110", "--keepAliveTimeout", "75000" ]
EXPOSE 5110
