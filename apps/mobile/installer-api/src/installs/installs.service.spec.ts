import * as sharedUtils from '@experience/shared/typescript/utils';
import { AppointmentBuilder } from '@experience/installer/types';
import { ConfigService } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { EXAMPLE_USER_ENTITY } from '../account/__fixtures__/account.interface';
import { I18nService } from 'nestjs-i18n';
import {
  INSTALL_IMAGE_REPOSITORY,
  INSTALL_REPOSITORY,
  INSTALL_REPOSITORY_READ,
  USER_REPOSITORY_READ,
} from '../database/constants';
import { Install } from '../database/entities/install.entity';
import { InstallImage } from '../database/entities/install-images.entity';
import { InstallImagesService } from '../install-images/install-images.service';
import { InstallsService } from './installs.service';
import { MockRepository, createMockRepository } from '../test.utils';
import { S3Module } from '@experience/shared/nest/aws/s3-module';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { SnsClientService } from '@experience/shared/nest/aws/sns-module';
import {
  TEST_AUTH_USER,
  TEST_CHARGER_SETTINGS_PAYLOAD,
  TEST_CHARGER_SETTINGS_PAYLOAD_DEVICE_CONFIG,
  TEST_CHARGER_SETTINGS_PAYLOAD_DEVICE_CONFIG_OPTIONAL,
  createTestInstall,
} from './__fixtures__/installs.interface';
import { Test } from '@nestjs/testing';
import { User } from '../database/entities/user.entity';

jest.mock('@experience/shared/typescript/utils', () => ({
  __esModule: true,
  ...jest.requireActual('@experience/shared/typescript/utils'),
}));

const expectedResult = createTestInstall(EXAMPLE_USER_ENTITY);
const getNoResult = jest.fn().mockReturnValue(null);

describe('InstallsService', () => {
  let service: InstallsService;
  let installImagesService: InstallImagesService;
  let installsRepository: MockRepository<Install>;
  let installsReadOnlyRepository: MockRepository<Install>;
  let userReadOnlyRepository: MockRepository<User>;
  let simpleEmailService: DeepMocked<SimpleEmailService>;
  let configService: DeepMocked<ConfigService>;
  let i18nService: DeepMocked<I18nService>;
  let snsClientService: DeepMocked<SnsClientService>;
  let injectParametersSpy: jest.SpyInstance;

  beforeEach(async () => {
    jest.useFakeTimers({
      now: new Date('2023-07-07T09:06:07Z'),
    });

    const module = await Test.createTestingModule({
      imports: [S3Module],
      providers: [
        InstallsService,
        { provide: ConfigService, useValue: createMock<ConfigService>() },
        InstallImagesService,
        {
          provide: INSTALL_REPOSITORY,
          useValue: createMockRepository<Install>(),
        },
        {
          provide: INSTALL_REPOSITORY_READ,
          useValue: createMockRepository<Install>(),
        },
        {
          provide: USER_REPOSITORY_READ,
          useValue: createMockRepository<User>(),
        },
        {
          provide: INSTALL_IMAGE_REPOSITORY,
          useValue: createMockRepository<InstallImage>(),
        },
        {
          provide: SimpleEmailService,
          useValue: createMock<SimpleEmailService>(),
        },
        {
          provide: I18nService,
          useValue: createMock<I18nService>(),
        },
        {
          provide: SnsClientService,
          useValue: createMock<SnsClientService>(),
        },
      ],
    }).compile();

    installImagesService =
      module.get<InstallImagesService>(InstallImagesService);
    installsRepository =
      module.get<MockRepository<Install>>(INSTALL_REPOSITORY);
    installsReadOnlyRepository = module.get<MockRepository<Install>>(
      INSTALL_REPOSITORY_READ
    );
    userReadOnlyRepository =
      module.get<MockRepository<User>>(USER_REPOSITORY_READ);
    simpleEmailService = module.get(SimpleEmailService);
    configService = module.get(ConfigService);
    i18nService = module.get(I18nService);
    snsClientService = module.get(SnsClientService);

    configService.get.mockReturnValue(
      'https://identity-installer.pod-point.com'
    );

    injectParametersSpy = jest.spyOn(
      sharedUtils,
      'injectParametersIntoTemplateString'
    );

    service = module.get<InstallsService>(InstallsService);
  });

  afterEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  describe('upsertInstall', () => {
    it('should be able to create a new install', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      expect(installsRepository.create).toHaveBeenCalledWith(expectedResult);
      expect(installsRepository.create).toHaveBeenCalledTimes(1);
    });

    it('should be able to create a new install with appointment', async () => {
      const installWithAppointment = createTestInstall(EXAMPLE_USER_ENTITY, {
        appointment: new AppointmentBuilder().build(),
      });
      installsRepository.create.mockReturnValue(installWithAppointment);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);
      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        appointment: new AppointmentBuilder().build(),
      });

      expect(installsRepository.create).toHaveBeenCalledWith(
        installWithAppointment
      );
      expect(installsRepository.create).toHaveBeenCalledTimes(1);
    });

    it('should be able to create a new install with a socket', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      expect(installsRepository.create).toHaveBeenCalledWith(expectedResult);
      expect(installsRepository.create).toHaveBeenCalledTimes(1);
    });

    it('should be able to update an existing install', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      expect(installsRepository.create).toHaveBeenCalledWith(expectedResult);

      installsReadOnlyRepository.findOneBy.mockReturnValue(expectedResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        completedAt: '2023-07-07T09:06:07Z',
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(installsRepository.save).toHaveBeenCalledWith(expectedResult);
      expect(installsRepository.create).toHaveBeenCalledTimes(1);
      expect(installsRepository.save).toHaveBeenCalledTimes(2);
    });

    it('should be able to update an existing install with an appointment', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      const installWithAppointment = createTestInstall(EXAMPLE_USER_ENTITY, {
        appointment: new AppointmentBuilder().build(),
      });

      installsReadOnlyRepository.findOneBy.mockReturnValue(
        installWithAppointment
      );

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        appointment: new AppointmentBuilder().build(),
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(installsRepository.save).toHaveBeenCalledWith(
        installWithAppointment
      );
      expect(installsRepository.create).toHaveBeenCalledTimes(1);
      expect(installsRepository.save).toHaveBeenCalledTimes(2);
    });

    it('should set default values when deviceConfig is present but optional values are not', async () => {
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);
      installsReadOnlyRepository.findOneBy.mockReturnValue(expectedResult);

      await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD_DEVICE_CONFIG,
        completedAt: '2023-07-07T09:06:07Z',
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();
      expect(installsRepository.save).toHaveBeenCalledWith({
        ...expectedResult,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD_DEVICE_CONFIG_OPTIONAL,
      });
      expect(installsRepository.save).toHaveBeenCalledTimes(1);
    });

    it('should return null firstCompletedAt for newly created install', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      const result = await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(result).toEqual({
        firstCompletedAt: null,
      });
    });

    it('should send an email notifying the installer of installation if it is a newly created install and has a completedAt date', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      i18nService.t.mockReturnValue('subject');

      const completedAt = new Date('07/07/2023 09:06');

      await service.upsertInstall(
        'my-guid',
        {
          authId: EXAMPLE_USER_ENTITY.authId,
          chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
          completedAt: completedAt.toISOString(),
        },
        true
      );

      expect(injectParametersSpy).toHaveBeenCalledTimes(2);
      expect(injectParametersSpy).toHaveBeenCalledWith(expect.any(String), {
        imageUrl: configService.get.mock.results[0].value,
        emailAddress: EXAMPLE_USER_ENTITY.email,
        installerName: EXAMPLE_USER_ENTITY.firstName,
        unitPSL: expectedResult.pslNumber,
        installationDate: '07/07/2023 09:06',
      });

      expect(i18nService.t).toHaveBeenCalledWith(
        'emails.successful_unit_installation.subject',
        {
          lang: 'en',
        }
      );

      expect(simpleEmailService.sendEmail).toHaveBeenCalledTimes(1);
      expect(simpleEmailService.sendEmail).toHaveBeenCalledWith(
        injectParametersSpy.mock.results[0].value,
        injectParametersSpy.mock.results[1].value,
        i18nService.t.mock.results[0].value,
        EXAMPLE_USER_ENTITY.email
      );
    });

    it('should send a SQS message with install completed details if it is a newly created install and has a completedAt date', async () => {
      const uuid = 'uuid-uuid-uuid-uuid-uuid';
      jest.spyOn(crypto, 'randomUUID').mockImplementationOnce(() => uuid);
      const expectedResultWithAppointment = createTestInstall(
        EXAMPLE_USER_ENTITY,
        {
          appointment: new AppointmentBuilder().build(),
          completedAt: new Date(),
        }
      );

      const eventPayload = {
        routingKey: `Installation.Completed.${TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.pslNumber}`,
        type: 'Installation.Completed',
        eventId: uuid,
        publishedAt: '2023-07-07T09:06:07.000Z',
        metadata: {},
        data: {
          chargingStation: {
            id: TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.pslNumber,
            pcbSerialNumber:
              TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.pcbSerialNumber,
            door: TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.socket,
            sku: TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.unitSKU,
          },
          installation: {
            id: expectedResultWithAppointment.guid,
            completedAt:
              expectedResultWithAppointment.completedAt.toISOString(),
            firmwareVersion:
              TEST_CHARGER_SETTINGS_PAYLOAD.deviceInformation.firmware,
          },
          installer: {
            authId: EXAMPLE_USER_ENTITY.authId,
          },
          appointment: {
            reference: expectedResultWithAppointment.appointment.reference,
          },
          order: {
            id: expectedResultWithAppointment.appointment.order.id,
            type: expectedResultWithAppointment.appointment.order.type,
          },
          address: {
            line1: expectedResultWithAppointment.appointment.address.line1,
            line2: expectedResultWithAppointment.appointment.address.line2,
            city: expectedResultWithAppointment.appointment.address.city,
            country: expectedResultWithAppointment.appointment.address.country,
            postcode:
              expectedResultWithAppointment.appointment.address.postcode,
          },
          location: {
            latitude:
              expectedResultWithAppointment.appointment.location.latitude,
            longitude:
              expectedResultWithAppointment.appointment.location.longitude,
            mpan: expectedResultWithAppointment.appointment.mpan,
          },
          useCase: 'domestic',
        },
      };

      installsRepository.create.mockReturnValue(expectedResultWithAppointment);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      i18nService.t.mockReturnValue('subject');

      const completedAt = new Date('07/07/2023 09:06');

      await service.upsertInstall(
        'my-guid',
        {
          authId: EXAMPLE_USER_ENTITY.authId,
          chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
          completedAt: completedAt.toISOString(),
        },
        false,
        true
      );

      expect(snsClientService.publishMessage).toHaveBeenCalledTimes(1);
      expect(snsClientService.publishMessage).toHaveBeenCalledWith(
        expect.any(String),
        {
          message: JSON.stringify(eventPayload),
        }
      );
    });

    it('should NOT send an email notifying the installer of installation if it is a newly created install but DOES NOT have a completedAt date', async () => {
      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      await service.upsertInstall(
        'my-guid',
        {
          authId: EXAMPLE_USER_ENTITY.authId,
          chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        },
        false,
        true
      );

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();
    });

    it('should NOT send a SQS message with install completed details if it is a newly created install but DOES NOT have a completedAt date', async () => {
      const uuid = 'uuid-uuid-uuid-uuid-uuid';
      jest.spyOn(crypto, 'randomUUID').mockImplementationOnce(() => uuid);

      installsRepository.create.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      installsReadOnlyRepository.setGetOne(getNoResult);

      i18nService.t.mockReturnValue('subject');

      await service.upsertInstall(
        'my-guid',
        {
          authId: EXAMPLE_USER_ENTITY.authId,
          chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        },
        true
      );

      expect(snsClientService.publishMessage).not.toHaveBeenCalled();
    });

    it('should return null firstCompletedAt for updated install if no completed at available', async () => {
      installsReadOnlyRepository.findOneBy.mockReturnValue(expectedResult);

      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);

      const result = await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(result).toEqual({
        firstCompletedAt: null,
      });
    });

    it('should return first completed at for updated install if available', async () => {
      const expectedResult = new Install();
      expectedResult.guid = 'my-guid';
      expectedResult.user = EXAMPLE_USER_ENTITY;
      expectedResult.chargerSettings = TEST_CHARGER_SETTINGS_PAYLOAD;

      const getOne = jest.fn().mockReturnValue(expectedResult);

      installsReadOnlyRepository.findOneBy.mockReturnValue(expectedResult);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);
      installsReadOnlyRepository.setGetOne(getOne);

      configService.get.mockReturnValue('installer-identity.pod-point.com');

      const result = await service.upsertInstall('my-guid', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        completedAt: '2023-07-07T09:06:07Z',
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(result).toEqual({
        firstCompletedAt: '2023-07-07T09:06:07.000Z',
      });
    });

    it('should return the first completed at after submitting a new installs with the same PSL number', async () => {
      const expectedResult = new Install();
      expectedResult.guid = 'my-guid';
      expectedResult.user = EXAMPLE_USER_ENTITY;
      expectedResult.chargerSettings = TEST_CHARGER_SETTINGS_PAYLOAD;
      expectedResult.completedAt = new Date('2023-07-07T09:06:07Z');

      const newlyCreatedInstall = new Install();
      newlyCreatedInstall.guid = 'my-guid-2';
      newlyCreatedInstall.user = EXAMPLE_USER_ENTITY;
      newlyCreatedInstall.chargerSettings = TEST_CHARGER_SETTINGS_PAYLOAD;

      const getOne = jest.fn().mockReturnValue(expectedResult);

      installsReadOnlyRepository.findOneBy.mockReturnValue(null);
      installsRepository.create.mockReturnValue(newlyCreatedInstall);
      userReadOnlyRepository.findOneBy.mockReturnValue(EXAMPLE_USER_ENTITY);
      installsReadOnlyRepository.setGetOne(getOne);

      configService.get.mockReturnValue('installer-identity.pod-point.com');

      const result = await service.upsertInstall('my-guid-2', {
        authId: EXAMPLE_USER_ENTITY.authId,
        chargerSettings: TEST_CHARGER_SETTINGS_PAYLOAD,
        completedAt: '2023-08-09T10:00:00Z',
      });

      expect(simpleEmailService.sendEmail).not.toHaveBeenCalled();

      expect(result).toEqual({
        firstCompletedAt: '2023-07-07T09:06:07.000Z',
      });
    });
  });

  describe('getInstallsByChargerPPID', () => {
    let signedImageUrlSpy: jest.SpyInstance;

    beforeEach(
      () =>
        (signedImageUrlSpy = jest
          .spyOn(installImagesService, 'getSignedImageUrl')
          .mockResolvedValue('https://example.com'))
    );

    it('gets all installs based on a given PPID', async () => {
      const whereMock = jest.fn().mockReturnThis();
      const leftJoinAndMapManyMock = jest.fn().mockReturnThis();
      const leftJoinAndMapOneMock = jest.fn().mockReturnThis();
      const getManyMock = jest.fn().mockResolvedValue([]);

      installsReadOnlyRepository.setWhere(whereMock);
      installsReadOnlyRepository.setGetMany(getManyMock);
      installsReadOnlyRepository.setLeftJoinAndMapMany(leftJoinAndMapManyMock);
      installsReadOnlyRepository.setLeftJoinAndMapOne(leftJoinAndMapOneMock);

      await service.getInstallsByChargerPPID('example-ppid');

      expect(
        installsReadOnlyRepository.createQueryBuilder
      ).toHaveBeenCalledTimes(1);

      expect(whereMock).toHaveBeenCalledTimes(1);
      expect(whereMock).toHaveBeenCalledWith(`install.psl_number = :ppid`, {
        ppid: 'example-ppid',
      });

      expect(leftJoinAndMapManyMock).toHaveBeenCalledTimes(1);
      expect(leftJoinAndMapManyMock).toHaveBeenCalledWith(
        'install.images',
        InstallImage,
        'image',
        'image.install_guid = install.guid'
      );
      expect(leftJoinAndMapOneMock).toHaveBeenCalledTimes(1);
      expect(leftJoinAndMapOneMock).toHaveBeenCalledWith(
        'install.user',
        User,
        'user',
        'user.id = install.user_id'
      );

      expect(getManyMock).toHaveBeenCalledTimes(1);
    });

    it('adds a signed url of each image', async () => {
      const getManyMock = jest.fn().mockResolvedValue([
        {
          user: TEST_AUTH_USER,
          images: [
            {
              file: '991a238-dbac-4ea8-a9cd-1eb2444294c3/2361d2fe-8eaa-4323-a34a-76b9e229afcc.jpeg',
            },
            {
              file: '075352ab-282d-4e39-84cc-eda96afcdb6a/36674cfe-817f-40e0-b732-901c8b2602f0.jpeg',
            },
          ],
        },
        {
          user: TEST_AUTH_USER,
          images: [
            {
              file: 'ab1f5c35-2f0c-480e-9bfd-a448b9dd0265/b9e58e70-1c70-46b3-9b92-6402ffce2144.jpeg',
            },
            {
              file: '9557acc2-51e1-4f8c-a4e8-f7459603ecb6/e226f4f7-4b5d-4ee7-a499-5a7adf7b9ee1.jpeg',
            },
          ],
        },
      ]);

      installsReadOnlyRepository.setGetMany(getManyMock);

      const res = await service.getInstallsByChargerPPID('example-ppid');

      expect(signedImageUrlSpy).toHaveBeenCalledTimes(4);

      res.forEach((install) => {
        install.images.forEach((image) => {
          expect(image).toHaveProperty('url');
          expect(image.url).toEqual('https://example.com');
        });
      });
    });

    it('returns appointment reference if present', async () => {
      const getManyMock = jest.fn().mockResolvedValue([
        {
          user: TEST_AUTH_USER,
          appointment: {
            reference: 'PP12345678190',
          },
          images: [
            {
              file: 'ab1f5c35-2f0c-480e-9bfd-a448b9dd0265/b9e58e70-1c70-46b3-9b92-6402ffce2144.jpeg',
            },
          ],
        },
      ]);

      installsReadOnlyRepository.setGetMany(getManyMock);

      const res = await service.getInstallsByChargerPPID('example-ppid');
      expect(res[0].appointment).toEqual({
        reference: 'PP12345678190',
      });
    });
  });

  it('returns null for appointment if appointment is null', async () => {
    const getManyMock = jest.fn().mockResolvedValue([
      {
        user: TEST_AUTH_USER,
        appointment: null,
        images: [
          {
            file: 'ab1f5c35-2f0c-480e-9bfd-a448b9dd0265/b9e58e70-1c70-46b3-9b92-6402ffce2144.jpeg',
          },
        ],
      },
    ]);

    installsReadOnlyRepository.setGetMany(getManyMock);

    const res = await service.getInstallsByChargerPPID('example-ppid');
    expect(res[0].appointment).toBeNull();
  });
});
