import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  CHARGER_STATUS_OFFLINE,
  CHARGER_SUBSTATUS_UNKNOWN,
} from './chargers.constants';
import { ChargerNotFoundError } from '@experience/mobile/nest/exception';
import { ChargersApi } from '@experience/shared/axios/data-platform-api-client';
import { ChargersService } from './chargers.service';
import { ConfigModule } from '@nestjs/config';
import { ConnectivityStatusApi } from '@experience/shared/axios/connectivity-service-client';
import {
  ConnectivityStatusNotFoundError,
  ConnectivityStatusUnknownError,
  RestrictionsBadRequestError,
  RestrictionsNotFoundError,
} from './chargers.exception';
import {
  EnergyOfferStatusApi,
  FlexibilityRequestsApi,
} from '@experience/shared/axios/smart-charging-service-client';
import { Enrolment<PERSON><PERSON> } from '@experience/shared/axios/competitions-service-client';
import { FlexRequest } from './chargers.types';
import { HttpStatusCode } from 'axios';
import { InfoApi } from '@experience/shared/axios/firmware-upgrade-client';
import {
  TEST_CHARGER_AUTHORISER_UUID,
  TEST_CHARGER_DNO_REGION_RESPONSE,
  TEST_CHARGER_EVSE_ID,
  TEST_CHARGER_FLEX_ENROLMENT_RESPONSE,
  TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH2,
  TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3,
  TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH5,
  TEST_CHARGER_LIMIT_API_ALLOWED_RESPONSE,
  TEST_CHARGER_LIMIT_API_NOT_ALLOWED_RESPONSE,
  TEST_CHARGER_PPID,
  TEST_CHARGER_PPID_ARCH5,
  TEST_CHARGER_RESTRICTIONS_ALLOWED_RESPONSE,
  TEST_CHARGER_RESTRICTIONS_NOT_ALLOWED_RESPONSE,
  TEST_CONNECTIVITY_SERVICE_EVSE_ARCH2,
  TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3,
  TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5,
  TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH2,
  TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
  TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5,
  TEST_CONNECTOR_IDLE,
  TEST_FIRMWARE_STATUS_RESPONSE,
  TEST_FLEX_REQUEST_INCREASE,
  TEST_FLEX_REQUEST_REDUCE,
  TEST_FLEX_REQUEST_RESPONSE_INCREASE,
  TEST_FLEX_REQUEST_RESPONSE_REDUCE,
  TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
  TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE,
  TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE_ARCH5,
  addEvsesConnectorToConnectivityStatus,
} from './__fixtures__';
import { Test, TestingModule } from '@nestjs/testing';

describe('ChargersService', () => {
  let service: ChargersService;
  let chargersApi: ChargersApi;
  let connectivityStatusApi: ConnectivityStatusApi;
  let enrolmentApi: EnrolmentApi;
  let energyOfferStatusApi: EnergyOfferStatusApi;
  let flexibilityRequestsApi: FlexibilityRequestsApi;
  let firmwareInfoApi: InfoApi;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        ChargersService,
        {
          provide: ChargersApi,
          useValue: new ChargersApi(),
        },
        {
          provide: ConnectivityStatusApi,
          useValue: new ConnectivityStatusApi(),
        },
        {
          provide: EnrolmentApi,
          useValue: new EnrolmentApi(),
        },
        {
          provide: EnergyOfferStatusApi,
          useValue: new EnergyOfferStatusApi(),
        },
        {
          provide: FlexibilityRequestsApi,
          useValue: new FlexibilityRequestsApi(),
        },
        {
          provide: InfoApi,
          useValue: new InfoApi(),
        },
      ],
    }).compile();

    service = module.get<ChargersService>(ChargersService);
    chargersApi = module.get<ChargersApi>(ChargersApi);
    connectivityStatusApi = module.get<ConnectivityStatusApi>(
      ConnectivityStatusApi
    );
    enrolmentApi = module.get<EnrolmentApi>(EnrolmentApi);
    energyOfferStatusApi =
      module.get<EnergyOfferStatusApi>(EnergyOfferStatusApi);
    flexibilityRequestsApi = module.get<FlexibilityRequestsApi>(
      FlexibilityRequestsApi
    );
    firmwareInfoApi = module.get<InfoApi>(InfoApi);
  });

  describe('getFirmwareStatus', () => {
    it('should return results', async () => {
      const mockFirmwareApi = jest
        .spyOn(firmwareInfoApi, 'getCurrentFirmware')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_FIRMWARE_STATUS_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const results = await service.getFirmwareStatus(TEST_CHARGER_PPID);

      expect(mockFirmwareApi).toHaveBeenCalledTimes(1);
      expect(mockFirmwareApi).toHaveBeenCalledWith(TEST_CHARGER_PPID);
      expect(results).toEqual(TEST_FIRMWARE_STATUS_RESPONSE);
    });

    it('should return empty array if API call fails', async () => {
      const mockFirmwareApi = jest
        .spyOn(firmwareInfoApi, 'getCurrentFirmware')
        .mockRejectedValueOnce({
          status: HttpStatusCode.InternalServerError,
        });

      const results = await service.getFirmwareStatus(TEST_CHARGER_PPID);

      expect(mockFirmwareApi).toHaveBeenCalledTimes(1);
      expect(mockFirmwareApi).toHaveBeenCalledWith(TEST_CHARGER_PPID);
      expect(results).toEqual([]);
    });
  });

  describe('getDnoRegion', () => {
    it('should return results', async () => {
      const mockDnoRegion = jest
        .spyOn(chargersApi, 'chargersRetrieveChargerRegion')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_CHARGER_DNO_REGION_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const results = await service.getDnoRegion(TEST_CHARGER_PPID);

      expect(mockDnoRegion).toHaveBeenCalledTimes(1);
      expect(mockDnoRegion).toHaveBeenCalledWith(TEST_CHARGER_PPID);
      expect(results).toEqual(TEST_CHARGER_DNO_REGION_RESPONSE);
    });

    it.each([
      // Test un-nested error format
      [{ status: HttpStatusCode.NotFound }, NotFoundException],
      [{ status: HttpStatusCode.BadRequest }, BadRequestException],
      // Test nested in "response" error format
      [{ response: { status: HttpStatusCode.NotFound } }, NotFoundException],
      [
        { response: { status: HttpStatusCode.BadRequest } },
        BadRequestException,
      ],
    ])(
      `throws expected error when %s received from underlying service`,
      async (errorResponse, expectedThrow) => {
        jest
          .spyOn(chargersApi, 'chargersRetrieveChargerRegion')
          .mockRejectedValueOnce(errorResponse);

        await expect(() =>
          service.getDnoRegion(TEST_CHARGER_PPID)
        ).rejects.toThrow(expectedThrow);
      }
    );

    it.each([
      // Test un-nested error format
      [{ status: null }],
      [{ status: HttpStatusCode.BadGateway }],
      [{ status: HttpStatusCode.NotImplemented }],
      [{ status: HttpStatusCode.InternalServerError }],
      // Test nested in "response" error format
      [{ response: { status: null } }],
      [{ response: { status: HttpStatusCode.BadGateway } }],
      [{ response: { status: HttpStatusCode.NotImplemented } }],
      [{ response: { status: HttpStatusCode.InternalServerError } }],
    ])(
      `throws InternalServerErrorException when %s received from underlying service`,
      async (errorResponse) => {
        jest
          .spyOn(chargersApi, 'chargersRetrieveChargerRegion')
          .mockRejectedValueOnce(errorResponse);

        await expect(() =>
          service.getDnoRegion(TEST_CHARGER_PPID)
        ).rejects.toThrow(InternalServerErrorException);
      }
    );
  });

  describe('getRestrictions', () => {
    it('should return restrictions for an allowed charge', async () => {
      const mockLimitApi = jest
        .spyOn(chargersApi, 'chargersRetrieveChargingLimit')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_CHARGER_LIMIT_API_ALLOWED_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const result = await service.getRestrictions(
        TEST_CHARGER_PPID,
        TEST_CHARGER_AUTHORISER_UUID
      );
      expect(mockLimitApi).toHaveBeenCalledTimes(1);
      expect(mockLimitApi).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        TEST_CHARGER_AUTHORISER_UUID
      );
      expect(result).toEqual(TEST_CHARGER_RESTRICTIONS_ALLOWED_RESPONSE);
    });

    it('should return restrictions for a not allowed charge', async () => {
      const mockLimitApi = jest
        .spyOn(chargersApi, 'chargersRetrieveChargingLimit')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_CHARGER_LIMIT_API_NOT_ALLOWED_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const result = await service.getRestrictions(
        TEST_CHARGER_PPID,
        TEST_CHARGER_AUTHORISER_UUID
      );
      expect(mockLimitApi).toHaveBeenCalledTimes(1);
      expect(mockLimitApi).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        TEST_CHARGER_AUTHORISER_UUID
      );
      expect(result).toEqual(TEST_CHARGER_RESTRICTIONS_NOT_ALLOWED_RESPONSE);
    });

    it.each([
      // Test un-nested error format
      [{ status: HttpStatusCode.NotFound }, RestrictionsNotFoundError],
      [{ status: HttpStatusCode.BadRequest }, RestrictionsBadRequestError],
      // Test nested in "response" error format
      [
        { response: { status: HttpStatusCode.NotFound } },
        RestrictionsNotFoundError,
      ],
      [
        { response: { status: HttpStatusCode.BadRequest } },
        RestrictionsBadRequestError,
      ],
    ])(
      `throws expected error when %s received from charger limit api`,
      async (errorResponse, expectedThrow) => {
        jest
          .spyOn(chargersApi, 'chargersRetrieveChargingLimit')
          .mockRejectedValueOnce(errorResponse);

        await expect(() =>
          service.getRestrictions(
            TEST_CHARGER_PPID,
            TEST_CHARGER_AUTHORISER_UUID
          )
        ).rejects.toThrow(expectedThrow);
      }
    );
  });

  describe('getConnectivityStatus', () => {
    it.each([
      [
        'arch2',
        TEST_CHARGER_PPID,
        TEST_CHARGER_EVSE_ID,
        TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH2,
        TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE,
        TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH2,
      ],
      [
        'arch3',
        TEST_CHARGER_PPID,
        TEST_CHARGER_EVSE_ID,
        TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
        TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE,
        TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3,
      ],
      [
        'arch5',
        TEST_CHARGER_PPID_ARCH5,
        TEST_CHARGER_EVSE_ID,
        TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5,
        TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE_ARCH5,
        TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH5,
      ],
    ])(
      'should return status for the given PPID (%s)',
      async (
        _scenario,
        ppid,
        evseId,
        connectivityFixture,
        energyOfferFixture,
        expectedResponse
      ) => {
        const mockConnectivityStatus = jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: connectivityFixture,
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        const mockEnergyOfferStatus = jest
          .spyOn(energyOfferStatusApi, 'getChargingStationEnergyOfferStatus')
          .mockResolvedValueOnce({
            config: null,
            data: energyOfferFixture,
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        const status = await service.getConnectivityStatus(ppid, false);

        expect(mockConnectivityStatus).toHaveBeenCalledTimes(1);
        expect(mockConnectivityStatus).toHaveBeenCalledWith(ppid);
        expect(mockEnergyOfferStatus).toHaveBeenCalledTimes(1);
        expect(mockEnergyOfferStatus).toHaveBeenCalledWith(ppid, evseId);

        expect(status).toEqual(expectedResponse);
      }
    );

    it('should throw ConnectivityStatusNotFoundError if connectivity API returns 404', async () => {
      const mockConnectivityStatus = jest
        .spyOn(connectivityStatusApi, 'getChargingStationStatus')
        .mockRejectedValueOnce({ status: HttpStatusCode.NotFound });

      await expect(() =>
        service.getConnectivityStatus(TEST_CHARGER_PPID, false)
      ).rejects.toThrow(new ConnectivityStatusNotFoundError());

      expect(mockConnectivityStatus).toHaveBeenCalledTimes(1);
      expect(mockConnectivityStatus).toHaveBeenCalledWith(TEST_CHARGER_PPID);
    });

    it('should order evses components desc by connectivity state last message timestamp', async () => {
      const oldestEvsesComponent = structuredClone(
        TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3
      );
      oldestEvsesComponent.connectivityState.lastMessageAt =
        '2023-08-21T15:00:00Z';
      const newestEvsesComponent = structuredClone(
        TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3
      );
      newestEvsesComponent.connectivityState.lastMessageAt =
        '2023-10-05T15:00:00Z';
      const data = structuredClone(
        TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3
      );
      data.evses = [oldestEvsesComponent, newestEvsesComponent];
      jest
        .spyOn(connectivityStatusApi, 'getChargingStationStatus')
        .mockResolvedValueOnce({
          config: null,
          data: data,
          headers: null,
          status: 200,
          statusText: 'OK',
        });
      jest
        .spyOn(energyOfferStatusApi, 'getChargingStationEnergyOfferStatus')
        .mockResolvedValue({
          config: null,
          data: TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const status = await service.getConnectivityStatus(
        TEST_CHARGER_PPID,
        true
      );

      expect(status.evses.length).toEqual(2);
      expect(status.evses[0].connectivityState.lastMessageAt).toEqual(
        '2023-10-05T15:00:00Z'
      );
      expect(status.evses[1].connectivityState.lastMessageAt).toEqual(
        '2023-08-21T15:00:00Z'
      );
    });

    describe('idle charging state fix', () => {
      it('should NOT be applied if the request is NOT from the legacy Driver app', async () => {
        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: addEvsesConnectorToConnectivityStatus(
              TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
              TEST_CONNECTOR_IDLE
            ),
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        jest.spyOn(service, 'getEnergyOfferStatus').mockResolvedValueOnce({
          ...TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
          isOfferingEnergy: false,
        });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID,
          false
        );

        expect(status.evses[0].connectors[0].chargingState).toEqual('IDLE');
      });

      it('should NOT be applied if energy offer status is true', async () => {
        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: addEvsesConnectorToConnectivityStatus(
              TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
              TEST_CONNECTOR_IDLE
            ),
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        jest.spyOn(service, 'getEnergyOfferStatus').mockResolvedValueOnce({
          ...TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
          isOfferingEnergy: true,
        });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID,
          true
        );

        expect(status.evses[0].connectors[0].chargingState).toEqual('IDLE');
      });

      it('should NOT be applied if arch 5', async () => {
        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: addEvsesConnectorToConnectivityStatus(
              TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5,
              TEST_CONNECTOR_IDLE
            ),
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        jest.spyOn(service, 'getEnergyOfferStatus').mockResolvedValueOnce({
          ...TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
          isOfferingEnergy: true,
        });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID,
          true
        );

        expect(status.evses[0].connectors[0].chargingState).toEqual('IDLE');
      });

      it('should be applied if energy offer status is false', async () => {
        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: addEvsesConnectorToConnectivityStatus(
              TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
              TEST_CONNECTOR_IDLE
            ),
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        jest.spyOn(service, 'getEnergyOfferStatus').mockResolvedValueOnce({
          ...TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
          isOfferingEnergy: false,
        });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID,
          true
        );

        expect(status.evses[0].connectors[0].chargingState).toEqual(
          'SUSPENDED_EVSE'
        );
      });
    });

    describe('UNKNOWN connectivity state fix', () => {
      beforeEach(() => {
        jest.spyOn(service, 'getEnergyOfferStatus').mockResolvedValueOnce({
          ...TEST_GET_ENERGY_OFFER_STATUS_RESPONSE,
          isOfferingEnergy: true,
        });
      });

      it('should return offline status if offline charger has not provided update within last 2 hours', async () => {
        const offlineEvsesComponent = structuredClone(
          TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3
        );
        offlineEvsesComponent.connectivityState.connectivityStatus =
          CHARGER_STATUS_OFFLINE;

        const data = structuredClone(
          TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3
        );
        data.evses = [offlineEvsesComponent];

        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: data,
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID,
          false
        );

        expect(status.evses[0].connectivityState.connectivityStatus).toEqual(
          CHARGER_STATUS_OFFLINE
        );
      });

      it.each([
        [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH2],
        [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3],
        [TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5],
      ])(
        'should throw NotFoundException if offline charger is OFFLINE with UNKNOWN offlineSubStatus',
        async (evse) => {
          const offlineEvsesComponent = structuredClone(evse);
          offlineEvsesComponent.connectivityState.connectivityStatus =
            CHARGER_STATUS_OFFLINE;
          offlineEvsesComponent.connectivityState.offlineSubStatus =
            CHARGER_SUBSTATUS_UNKNOWN;
          const data = structuredClone(
            TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH2
          );
          data.evses = [offlineEvsesComponent];

          jest
            .spyOn(connectivityStatusApi, 'getChargingStationStatus')
            .mockResolvedValueOnce({
              config: null,
              data: data,
              headers: null,
              status: 200,
              statusText: 'OK',
            });

          await expect(() =>
            service.getConnectivityStatus(TEST_CHARGER_PPID, false)
          ).rejects.toThrow(new ConnectivityStatusUnknownError());
        }
      );

      it.each([
        [
          TEST_CONNECTIVITY_SERVICE_EVSE_ARCH2,
          TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH2,
        ],
        [
          TEST_CONNECTIVITY_SERVICE_EVSE_ARCH3,
          TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH3,
        ],
        [
          TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5,
          TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5,
        ],
      ])(
        'should not throw NotFoundException if offline unit has no UNKNOWN offlineSubStatus',
        async (evse, response) => {
          expect.assertions(2);

          const offlineEvsesComponent = structuredClone(evse);
          offlineEvsesComponent.connectivityState.connectivityStatus =
            CHARGER_STATUS_OFFLINE;

          const data = structuredClone(response);
          data.evses = [offlineEvsesComponent];

          jest
            .spyOn(connectivityStatusApi, 'getChargingStationStatus')
            .mockResolvedValueOnce({
              config: null,
              data: data,
              headers: null,
              status: 200,
              statusText: 'OK',
            });

          expect(async () => {
            const status = await service.getConnectivityStatus(
              TEST_CHARGER_PPID,
              false
            );

            expect(
              status.evses[0].connectivityState.connectivityStatus
            ).toEqual(CHARGER_STATUS_OFFLINE);
          }).not.toThrow(new ConnectivityStatusUnknownError());
        }
      );

      it('should not apply the check to arch5 chargers', async () => {
        const offlineEvsesComponent = structuredClone(
          TEST_CONNECTIVITY_SERVICE_EVSE_ARCH5
        );
        offlineEvsesComponent.connectivityState.connectivityStatus =
          CHARGER_STATUS_OFFLINE;

        const data = structuredClone(
          TEST_CONNECTIVITY_SERVICE_GET_CHARGING_STATION_STATUS_RESPONSE_ARCH5
        );
        data.evses = [offlineEvsesComponent];

        jest
          .spyOn(connectivityStatusApi, 'getChargingStationStatus')
          .mockResolvedValueOnce({
            config: null,
            data: data,
            headers: null,
            status: 200,
            statusText: 'OK',
          });

        const status = await service.getConnectivityStatus(
          TEST_CHARGER_PPID_ARCH5,
          false
        );

        expect(status.evses[0].connectivityState.connectivityStatus).toEqual(
          CHARGER_STATUS_OFFLINE
        );
      });
    });
  });

  describe('getEnergyOfferStatus', () => {
    it('should return the energy offer status', async () => {
      const mockEnergyOfferStatus = jest
        .spyOn(energyOfferStatusApi, 'getChargingStationEnergyOfferStatus')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_SMART_CHARGING_SERVICE_GET_CHARGING_STATION_ENERGY_OFFER_STATUS_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const energyOfferStatus = await service.getEnergyOfferStatus(
        TEST_CHARGER_PPID,
        TEST_CHARGER_EVSE_ID
      );

      expect(mockEnergyOfferStatus).toHaveBeenCalledTimes(1);
      expect(mockEnergyOfferStatus).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        TEST_CHARGER_EVSE_ID
      );

      expect(energyOfferStatus).toEqual(TEST_GET_ENERGY_OFFER_STATUS_RESPONSE);
    });
  });

  describe('getFlexEnrolment', () => {
    it('should return flex enrolment for the given PPID', async () => {
      const mockGetFlexEnrolmentStatus = jest
        .spyOn(enrolmentApi, 'getChargingStationEnrolmentStatus')
        .mockResolvedValueOnce({
          config: null,
          data: TEST_CHARGER_FLEX_ENROLMENT_RESPONSE,
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const chargerFlexEnrolment = await service.getFlexEnrolment(
        TEST_CHARGER_PPID
      );

      expect(mockGetFlexEnrolmentStatus).toHaveBeenCalledTimes(1);
      expect(mockGetFlexEnrolmentStatus).toHaveBeenCalledWith(
        TEST_CHARGER_PPID
      );
      expect(chargerFlexEnrolment).toEqual(
        TEST_CHARGER_FLEX_ENROLMENT_RESPONSE
      );
    });

    it.each([
      [HttpStatusCode.Unauthorized, new UnauthorizedException()],
      [HttpStatusCode.NotFound, new ChargerNotFoundError()],
    ])(
      'should handle %s errors from upstream API',
      async (upstreamStatus, expectedError) => {
        jest
          .spyOn(enrolmentApi, 'getChargingStationEnrolmentStatus')
          .mockRejectedValueOnce({ status: upstreamStatus });

        await expect(
          service.getFlexEnrolment(TEST_CHARGER_PPID)
        ).rejects.toThrow(expectedError);
      }
    );
  });

  describe('deleteFlexEnrolment', () => {
    it('should succeed on 204 No Content responses from upstream API', async () => {
      const mockUnenrolChargingStation = jest
        .spyOn(enrolmentApi, 'unenrolChargingStation')
        .mockResolvedValueOnce({
          config: null,
          data: null,
          headers: null,
          status: 204,
          statusText: 'No Content',
        });

      await expect(
        service.deleteFlexEnrolment(TEST_CHARGER_PPID)
      ).resolves.not.toThrow();

      expect(mockUnenrolChargingStation).toHaveBeenCalledTimes(1);
      expect(mockUnenrolChargingStation).toHaveBeenCalledWith(
        TEST_CHARGER_PPID
      );
    });

    it.each([
      [HttpStatusCode.Unauthorized, new UnauthorizedException()],
      [HttpStatusCode.NotFound, new ChargerNotFoundError()],
    ])(
      'should handle %s errors from upstream API',
      async (upstreamStatus, expectedError) => {
        jest
          .spyOn(enrolmentApi, 'unenrolChargingStation')
          .mockRejectedValueOnce({ status: upstreamStatus });

        await expect(
          service.deleteFlexEnrolment(TEST_CHARGER_PPID)
        ).rejects.toThrow(expectedError);
      }
    );
  });

  describe('getFlexRequests', () => {
    it('should return empty list for charger with no FlexibilityRequests', async () => {
      const mockGetActiveChargingStationFlexibilityRequests = jest
        .spyOn(
          flexibilityRequestsApi,
          'getActiveChargingStationFlexibilityRequests'
        )
        .mockResolvedValueOnce({
          config: null,
          data: [],
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const result = await service.getFlexRequests(TEST_CHARGER_PPID);

      expect(
        mockGetActiveChargingStationFlexibilityRequests
      ).toHaveBeenCalledTimes(1);
      expect(
        mockGetActiveChargingStationFlexibilityRequests
      ).toHaveBeenCalledWith(TEST_CHARGER_PPID);
      expect(result).toBeInstanceOf(Array<FlexRequest>);
      expect(result).toHaveLength(0);
    });

    it('should return list of FlexRequests', async () => {
      const mockGetActiveChargingStationFlexibilityRequests = jest
        .spyOn(
          flexibilityRequestsApi,
          'getActiveChargingStationFlexibilityRequests'
        )
        .mockResolvedValueOnce({
          config: null,
          data: [
            TEST_FLEX_REQUEST_RESPONSE_INCREASE,
            TEST_FLEX_REQUEST_RESPONSE_REDUCE,
          ],
          headers: null,
          status: 200,
          statusText: 'OK',
        });

      const result = await service.getFlexRequests(TEST_CHARGER_PPID);

      expect(
        mockGetActiveChargingStationFlexibilityRequests
      ).toHaveBeenCalledTimes(1);
      expect(
        mockGetActiveChargingStationFlexibilityRequests
      ).toHaveBeenCalledWith(TEST_CHARGER_PPID);
      expect(result).toBeInstanceOf(Array<FlexRequest>);
      expect(result).toEqual([
        TEST_FLEX_REQUEST_INCREASE,
        TEST_FLEX_REQUEST_REDUCE,
      ]);
    });

    it.each([
      [HttpStatusCode.Unauthorized, new UnauthorizedException()],
      [HttpStatusCode.NotFound, new ChargerNotFoundError()],
    ])(
      'should handle %s errors from upstream API',
      async (upstreamStatus, expectedError) => {
        jest
          .spyOn(
            flexibilityRequestsApi,
            'getActiveChargingStationFlexibilityRequests'
          )
          .mockRejectedValueOnce({ status: upstreamStatus });

        await expect(
          service.getFlexRequests(TEST_CHARGER_PPID)
        ).rejects.toThrow(expectedError);
      }
    );
  });

  describe('deleteFlexRequest', () => {
    const testFlexRequestId = 'd40ff7bc-cd94-4e4f-9d52-34a80e83e64b';

    it('should succeed on 204 No Content responses from upstream API', async () => {
      const mockDeleteFlexibilityRequest = jest
        .spyOn(flexibilityRequestsApi, 'deleteFlexibilityRequest')
        .mockResolvedValueOnce({
          config: null,
          data: null,
          headers: null,
          status: 204,
          statusText: 'No Content',
        });

      await expect(
        service.deleteFlexRequest(testFlexRequestId)
      ).resolves.not.toThrow();

      expect(mockDeleteFlexibilityRequest).toHaveBeenCalledTimes(1);
      expect(mockDeleteFlexibilityRequest).toHaveBeenCalledWith(
        testFlexRequestId
      );
    });

    it.each([
      [HttpStatusCode.BadRequest, new BadRequestException()],
      [HttpStatusCode.Unauthorized, new UnauthorizedException()],
      [HttpStatusCode.NotFound, new ChargerNotFoundError()],
    ])(
      'should handle %s errors from upstream API',
      async (upstreamStatus, expectedError) => {
        jest
          .spyOn(flexibilityRequestsApi, 'deleteFlexibilityRequest')
          .mockRejectedValueOnce({ status: upstreamStatus });

        await expect(
          service.deleteFlexRequest(testFlexRequestId)
        ).rejects.toThrow(expectedError);
      }
    );
  });
});
