import {
  AcquiredCOPResponse,
  AcquiredCOPValidationResponse,
  ActionUpdateDTO,
  SubscriptionConfirmationOfPayeeDTO,
  SubscriptionConfirmationOfPayeeResponse,
  SubscriptionDTO,
  SubscriptionDirectDebitDTO,
  SubscriptionDocumentsDTO,
} from './subscriptions.types';
import {
  ActionDoesNotExistError,
  FailedToGetSubscriptionError,
  FailedToGetSubscriptionsError,
  FailedToUpdateSubscriptionActionError,
  GetSubscriptionValidationError,
  SubscriptionNotFoundError,
  UpdateSubscriptionActionValidationError,
  UserDoesNotOwnSubscriptionError,
} from './subscriptions.errors';
import { AxiosInstance, HttpStatusCode, isAxiosError } from 'axios';
import { AxiosResponse } from 'axios';
import {
  COP_TEST_MODE,
  TEST_ACCOUNT_NUMBER_MATCH,
  TEST_ACCOUNT_NUMBER_NO_MATCH,
  TEST_ACCOUNT_NUMBER_PARTIAL_MATCH,
} from './subscriptions.constants';
import { ConfigService } from '@nestjs/config';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  PersistedSubscriptionDTOActionsInner,
  SubscriptionsApi,
} from '@experience/mobile/subscriptions-api/axios';
import { Readable } from 'stream';

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsApi.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly subscriptionsApi: SubscriptionsApi,
    @Inject('ACQUIRED_CLIENT')
    private readonly acquiredClient: AxiosInstance
  ) {}

  async getSubscriptions(authId: string): Promise<SubscriptionDTO[]> {
    try {
      const { data } =
        await this.subscriptionsApi.subscriptionsControllerSearch(authId);

      return data.subscriptions.map(({ userId, ...sub }) => sub);
    } catch (error) {
      this.logger.error({ authId, error }, 'failed to get subscriptions');

      throw new FailedToGetSubscriptionsError();
    }
  }

  async updateSubscriptionAction(
    authId: string,
    subscriptionId: string,
    actionId: string,
    updatePayload: ActionUpdateDTO
  ) {
    this.logger.log(
      {
        authId,
        subscriptionId,
        actionId,
      },
      'attempting to update subscription action'
    );

    try {
      const { data: subscription } =
        await this.subscriptionsApi.subscriptionsControllerGetBySubscriptionId(
          subscriptionId
        );

      if (subscription.userId !== authId) {
        this.logger.warn(
          {
            authId,
            subscriptionOwner: subscription.userId,
            subscriptionId,
            actionId,
          },
          'user does not own subscription - not updating action'
        );

        throw new UserDoesNotOwnSubscriptionError();
      }

      const action = subscription.actions?.find(({ id }) => id === actionId);

      if (!action) {
        throw new ActionDoesNotExistError();
      }

      this.logger.log(
        {
          authId,
          subscriptionId,
          actionId,
        },
        'user owns subscription. proceeding with action update'
      );

      const { data: actionUpdate } =
        await this.subscriptionsApi.subscriptionsControllerUpdateActionById(
          subscriptionId,
          actionId,
          updatePayload
        );

      this.logger.log(
        {
          authId,
          subscriptionId,
          actionId,
        },
        'successfully updated subscription action'
      );

      return actionUpdate;
    } catch (error) {
      if (
        error instanceof UserDoesNotOwnSubscriptionError ||
        error instanceof ActionDoesNotExistError
      ) {
        throw error;
      }

      this.logger.error(
        {
          authId,
          subscriptionId,
          actionId,
        },
        'failed to update subscription action'
      );

      if (
        isAxiosError<{ message: string[] }>(error) &&
        error.response.status === HttpStatusCode.BadRequest
      ) {
        throw new UpdateSubscriptionActionValidationError(
          error.response.data.message.toString()
        );
      }

      throw new FailedToUpdateSubscriptionActionError();
    }
  }

  async getSubscriptionById(
    authId: string,
    subscriptionId: string
  ): Promise<SubscriptionDTO> {
    try {
      this.logger.log(
        { authId, subscriptionId },
        'fetching subscription from subscription-api'
      );
      const { data: subscription } =
        await this.subscriptionsApi.subscriptionsControllerGetBySubscriptionId(
          subscriptionId
        );

      if (subscription.userId !== authId) {
        this.logger.error(
          { authId, subscriptionId, subscriptionUserId: subscription.userId },
          'subscription in wrong user'
        );

        throw new SubscriptionNotFoundError();
      }

      return subscription;
    } catch (error) {
      this.logger.error(
        { authId, subscriptionId, error },
        'failed to get subscription'
      );

      if (isAxiosError(error)) {
        switch (error.response.status) {
          case HttpStatusCode.NotFound:
            throw new SubscriptionNotFoundError();
          case HttpStatusCode.BadRequest:
            throw new GetSubscriptionValidationError();
        }
      }

      if (error instanceof SubscriptionNotFoundError) {
        throw error;
      }

      throw new FailedToGetSubscriptionError();
    }
  }

  async getSubscriptionDirectDebitDetails(
    authId: string,
    subscriptionId: string
  ): Promise<SubscriptionDirectDebitDTO> {
    this.logger.log({ subscriptionId }, 'retrieving subscription direct debit');

    try {
      const { data: subscription } =
        await this.subscriptionsApi.subscriptionsControllerGetBySubscriptionId(
          subscriptionId
        );

      if (subscription.userId !== authId) {
        this.logger.error(
          { authId, subscriptionId, subscriptionUserId: subscription.userId },
          'subscription for wrong user'
        );

        throw new UserDoesNotOwnSubscriptionError();
      }

      const { data: directDebitData } =
        await this.subscriptionsApi.subscriptionsControllerGetSubscriptionDirectDebit(
          subscriptionId
        );

      return directDebitData;
    } catch (error) {
      this.logger.error(
        { subscriptionId, error },
        'failed to get subscription direct debit details'
      );

      if (isAxiosError(error)) {
        switch (error.response.status) {
          case HttpStatusCode.NotFound:
            throw new SubscriptionNotFoundError();
        }
      }

      if (error instanceof UserDoesNotOwnSubscriptionError) {
        throw error;
      }

      throw new FailedToGetSubscriptionError();
    }
  }

  async getSubscriptionDocuments(
    authId: string,
    subscriptionId: string
  ): Promise<SubscriptionDocumentsDTO> {
    this.logger.log({ subscriptionId }, 'retrieving subscription documents');

    try {
      const { data: subscription } =
        await this.subscriptionsApi.subscriptionsControllerGetBySubscriptionId(
          subscriptionId
        );

      if (subscription.userId !== authId) {
        this.logger.error(
          { authId, subscriptionId, subscriptionUserId: subscription.userId },
          'subscription for wrong user'
        );

        throw new UserDoesNotOwnSubscriptionError();
      }

      const { data: documents } =
        await this.subscriptionsApi.subscriptionsControllerGetSubscriptionDocuments(
          subscriptionId
        );

      return documents;
    } catch (error) {
      this.logger.error(
        { subscriptionId, error },
        'failed to get subscription documents'
      );

      if (isAxiosError(error)) {
        switch (error.response.status) {
          case HttpStatusCode.NotFound:
            throw new SubscriptionNotFoundError();
        }
      }

      if (error instanceof UserDoesNotOwnSubscriptionError) {
        throw error;
      }

      throw new FailedToGetSubscriptionError();
    }
  }

  private isStream(
    response: AxiosResponse<unknown>
  ): response is AxiosResponse<Readable> {
    return response.headers['content-type'] === 'application/pdf';
  }

  async getSubscriptionDocument(
    authId: string,
    subscriptionId: string,
    documentId: string
  ): Promise<Readable> {
    const { data: subscription } =
      await this.subscriptionsApi.subscriptionsControllerGetBySubscriptionId(
        subscriptionId
      );

    if (subscription.userId !== authId) {
      this.logger.error(
        { authId, subscriptionId, subscriptionUserId: subscription.userId },
        'subscription for wrong user'
      );

      throw new UserDoesNotOwnSubscriptionError();
    }

    const response =
      await this.subscriptionsApi.subscriptionsControllerGetSubscriptionDocument(
        subscriptionId,
        documentId,
        {
          responseType: 'stream',
        }
      );

    if (this.isStream(response)) {
      return response.data;
    }

    throw new FailedToGetSubscriptionsError();
  }

  async doConfirmationOfPayeeCheck(
    authId: string,
    payload: SubscriptionConfirmationOfPayeeDTO
  ): Promise<SubscriptionConfirmationOfPayeeResponse> {
    this.logger.log(
      { authId },
      'performing confirmation of payee check for user'
    );

    try {
      const testMode =
        this.configService.get(COP_TEST_MODE, 'false').toLowerCase() === 'true';

      if (testMode) {
        this.logger.log(
          { authId },
          'COP test mode enabled, checking for test data'
        );

        const result = this.getTestConfirmationOfPayeeResponse(authId, payload);

        if (result) {
          return result;
        }
      }

      const accountName = payload.accountName
        .replace(/[^a-zA-Z.\- ]/g, '')
        .slice(0, 50)
        .trim();

      const { data } = await this.acquiredClient.post<
        AcquiredCOPResponse | AcquiredCOPValidationResponse
      >('v1/tools/confirmation-of-payee', {
        scheme: 'faster_payments',
        account_name: accountName,
        sort_code: payload.sortCode,
        account_number: payload.accountNumber,
        account_type: 'personal',
      });

      if (data.status === 'error') {
        const reason = data.invalid_parameters.map((p) => p.reason).join(', ');

        this.logger.log(
          {
            authId,
            reason,
            status: data.status,
          },
          'failed confirmation of payee validation'
        );

        return {
          status: 'not_matched',
          reason,
          accountName,
        };
      }

      this.logger.log(
        {
          authId,
          status: data.status,
          reason: data.reason,
        },
        'successfully got result of confirmation of payee check'
      );

      return {
        status: data.status,
        reason: data.reason,
        accountName: data.account_name,
      };
    } catch (error) {
      let err = error;
      if (isAxiosError(error)) {
        err = {
          // hide sensitive data
          status: error.response.status,
          data: error.response.data,
        };
      }

      this.logger.error(
        { authId, err },
        'failed to perform confirmation of payee check'
      );

      return {
        status: 'matched',
        reason: 'acquired service is not available',
        accountName: '',
      };
    }
  }

  async getSubscriptionActionById(
    authId: string,
    subscriptionId: string,
    actionId: string
  ): Promise<PersistedSubscriptionDTOActionsInner> {
    const subscription = await this.getSubscriptionById(authId, subscriptionId);

    try {
      this.logger.log(
        {
          authId,
          subscriptionId,
          actionId,
        },
        'searching for action in subscription'
      );

      const subscriptionAction = subscription.actions?.find(
        ({ id }) => id === actionId
      );

      if (subscriptionAction) {
        return subscriptionAction;
      }

      this.logger.log(
        {
          authId,
          subscriptionId,
          actionId,
        },
        'fetching subscription action from subscription-api'
      );

      const { data: action } =
        await this.subscriptionsApi.subscriptionsControllerGetByActionId(
          subscriptionId,
          actionId
        );

      return action;
    } catch (error) {
      this.logger.error(
        { authId, subscriptionId, actionId, error },
        'failed to get subscription action'
      );

      if (
        isAxiosError(error) &&
        error.response.status === HttpStatusCode.NotFound
      ) {
        throw new ActionDoesNotExistError();
      }

      throw new FailedToGetSubscriptionError();
    }
  }

  private getTestConfirmationOfPayeeResponse(
    authId: string,
    payload: SubscriptionConfirmationOfPayeeDTO
  ): SubscriptionConfirmationOfPayeeResponse | null {
    switch (payload.accountNumber) {
      case TEST_ACCOUNT_NUMBER_MATCH:
        this.logger.log(
          { authId },
          'Match test data found, returning match response'
        );

        return {
          status: 'matched',
          reason: '',
          accountName: payload.accountName,
        };

      case TEST_ACCOUNT_NUMBER_NO_MATCH:
        this.logger.log(
          { authId },
          'No match test data found, returning no match response'
        );

        return {
          status: 'not_matched',
          reason: 'stubbed not matched response',
          accountName: payload.accountName,
        };

      case TEST_ACCOUNT_NUMBER_PARTIAL_MATCH:
        this.logger.log(
          { authId },
          'Partial match test data found, returning partial match response'
        );

        return {
          status: 'partial_match',
          reason: 'stubbed partial match response',
          accountName: payload.accountName,
        };

      default:
        this.logger.log(
          { authId },
          'no matching test data, real response will be returned'
        );

        return null;
    }
  }
}
