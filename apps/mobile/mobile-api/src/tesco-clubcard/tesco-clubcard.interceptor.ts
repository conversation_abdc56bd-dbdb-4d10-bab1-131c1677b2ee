import { HttpInterceptor } from '@experience/shared/nest/utils';
import { HttpStatus } from '@nestjs/common';
import {
  TescoClubcardError,
  TescoClubcardNotCreatedError,
  TescoClubcardNotFoundError,
} from './tesco-clubcard.errors';

export class Tesco<PERSON><PERSON>cardInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        code: TescoClubcardError.TESCO_CLUBCARD_NOT_CREATED_ERROR,
        name: TescoClubcardNotCreatedError,
        statusCode: HttpStatus.BAD_REQUEST,
      },
      {
        code: TescoClubcardError.TESCO_CLUBCARD_NOT_FOUND_ERROR,
        name: TescoClubcardNotFoundError,
        statusCode: HttpStatus.NOT_FOUND,
      },
    ]);
  }
}
