FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb
ARG APPLICATION_VERSION
ARG SENTRY_RELEASE
ARG STRIPE_GITHUB_TOKEN
ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE
ENV STRIPE_GITHUB_TOKEN=$STRIPE_GITHUB_TOKEN
WORKDIR /app
COPY .npmrc .
COPY dist/apps/mobile/subscriptions-api .
RUN npm install --omit=dev

# Add RDS CA certs to trust store to be able to connect to RDS over TLS
ADD https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem global-bundle.pem
RUN cat global-bundle.pem >> /etc/ssl/certs/ca-certificates.crt && rm global-bundle.pem

# Configure Node to use the OpenSSL CA store to use certs from above
ENV NODE_OPTIONS=--use-openssl-ca

CMD [ "node", "main.js" ]
EXPOSE 5120
