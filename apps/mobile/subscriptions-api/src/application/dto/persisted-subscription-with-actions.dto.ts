import { ActionEntity } from '../../domain/entities/action.entity';
import { StripFunctions } from '../../utility/types/helper.types';
import { SubscriptionEntity } from '../../domain/entities/subscription.entity';

export class PersistedSubscriptionWithActionsDTO {
  constructor(params: StripFunctions<PersistedSubscriptionWithActionsDTO>) {
    Object.assign(this, params);
  }
  subscription: SubscriptionEntity;
  actions: ActionEntity[];
}
