import { ActionStatus } from '../../../domain/entities/action.entity';
import { ClsService } from 'nestjs-cls';
import {
  CouldNotDeleteEntityError,
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  FatalRepositoryError,
} from '@experience/mobile/clean-architecture';
import { EntityManager, JsonContains } from 'typeorm';
import { MOCK_CREATE_PLAN, MOCK_PLAN } from '../__fixtures__/plan.fixtures';
import {
  MOCK_CREATE_SUBSCRIPTION,
  MOCK_SUBSCRIPTION,
} from '../__fixtures__/subscription.fixtures';
import {
  MOCK_CREATE_SUBSCRIPTION_ENTITY,
  MOCK_POD_DRIVE_PLAN_ENTITY,
  MOCK_SUBSCRIPTION_ENTITY,
  MOCK_SURVEY_ACTION_ENTITY,
} from '../../../domain/entities/__fixtures__/entity.fixtures';
import { OrderOrigin } from '../../../domain/entities/order.entity';
import { Subscription } from '@experience/mobile/driver-account/database';
import { SubscriptionStatus } from '../../../domain/entities/subscription.entity';
import { TypeOrmSubscriptionRepository } from './subscription.repository';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { mockEntityManagerCreateOnce } from '@experience/mobile/nest/typeorm-transactions';
import { v4 } from 'uuid';

type Callback = (entityManager: EntityManager) => Promise<void>;

describe(TypeOrmSubscriptionRepository.name, () => {
  let entityManager: jest.Mocked<EntityManager>;
  let clsService: jest.Mocked<ClsService>;
  let repository: TypeOrmSubscriptionRepository;

  beforeEach(() => {
    entityManager = createMock();
    clsService = createMock();

    repository = new TypeOrmSubscriptionRepository(entityManager, clsService);

    jest.resetAllMocks();

    clsService.get.mockReturnValue(entityManager);
  });

  describe(TypeOrmSubscriptionRepository.prototype.read, () => {
    it('calls entityManager.findOne', async () => {
      const status = SubscriptionStatus.PENDING,
        userId = v4(),
        subscription = {
          id: v4(),
          status,
          userId,
          order: {
            id: v4(),
            origin: 'SALESFORCE',
            orderedAt: new Date(),
            address: {
              line1: "222 Gray's Inn Road",
              line2: null,
              line3: null,
              postcode: 'WC1X 8HB',
            },
            email: '<EMAIL>',
            firstName: 'Mobile',
            lastName: 'Tester',
            mpan: 'S 01 801 101 22 6130 5588 165',
            eCommerceId: 'dr3l2e8dhu::2415',
            phoneNumber: '02072474114',
          },
          plans: [MOCK_PLAN],
          actions: [],
          activatedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        };

      entityManager.findOne.mockResolvedValueOnce(subscription);

      await repository.read(subscription.id);

      expect(entityManager.findOne).toBeCalledWith(Subscription, {
        where: { id: subscription.id },
        relations: ['plans'],
      });
    });

    it('throws FatalRepositoryError when db status is invalid', async () => {
      const status = 'Fake news',
        userId = v4(),
        subscription = {
          id: v4(),
          status,
          userId,
          order: {
            id: v4(),
            origin: 'SALESFORCE',
            orderedAt: new Date(),
            address: {
              line1: "222 Gray's Inn Road",
              line2: null,
              line3: null,
              postcode: 'WC1X 8HB',
            },
            email: '<EMAIL>',
            firstName: 'Mobile',
            lastName: 'Tester',
            mpan: 'S 01 801 101 22 6130 5588 165',
            eCommerceId: 'dr3l2e8dhu::2415',
            phoneNumber: '02072474114',
          },
          plans: [MOCK_PLAN],
          actions: [],
          activatedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        };

      entityManager.findOne.mockResolvedValueOnce(subscription);

      await expect(repository.read(subscription.id)).rejects.toThrowError(
        FatalRepositoryError
      );
    });

    it('throws FatalRepositoryError when unknown error occurs', async () => {
      const status = SubscriptionStatus.PENDING,
        userId = v4(),
        subscription = {
          id: v4(),
          status,
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        };

      entityManager.findOne.mockRejectedValueOnce(new Error('Knowledge...'));

      await expect(repository.read(subscription.id)).rejects.toThrowError(
        FatalRepositoryError
      );
    });

    it('throws FatalRepositoryError when db details is invalid', async () => {
      entityManager.findOne.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [
          {
            ...MOCK_PLAN,
            details: {
              ...MOCK_PLAN.details,
              allowancePeriod: 'fake',
            },
          },
        ],
      });

      await expect(
        repository.read('cab7a384-6f74-48bd-ad5c-096d7c0888f0')
      ).rejects.toThrowError(FatalRepositoryError);
    });
  });

  describe(TypeOrmSubscriptionRepository.prototype.create, () => {
    it('calls entityManager.create', async () => {
      entityManager.save.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [MOCK_PLAN],
      });

      await repository.create(MOCK_CREATE_SUBSCRIPTION_ENTITY);

      expect(entityManager.create).toBeCalledWith(Subscription, {
        ...MOCK_CREATE_SUBSCRIPTION,
        plans: [MOCK_CREATE_PLAN],
      });
    });

    it('calls entityManager.save', async () => {
      mockEntityManagerCreateOnce(entityManager, {
        ...MOCK_SUBSCRIPTION,
        plans: [MOCK_PLAN],
      });

      entityManager.save.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [MOCK_PLAN],
      });

      await repository.create(MOCK_CREATE_SUBSCRIPTION_ENTITY);

      expect(entityManager.save).toHaveBeenCalledWith(
        Subscription,
        {
          ...MOCK_SUBSCRIPTION,
          plans: [MOCK_PLAN],
        },
        { reload: true }
      );
    });

    it('throws FatalRepositoryError when db status is invalid', async () => {
      const status = SubscriptionStatus.PENDING,
        userId = v4(),
        protoSubscription = {
          status,
          userId,
          order: {
            id: v4(),
            origin: OrderOrigin.SALESFORCE,
            orderedAt: new Date(),
          },
        };

      mockEntityManagerCreateOnce(
        entityManager,
        protoSubscription as Subscription
      );

      entityManager.save.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: 'invalid',
        plans: [],
        actions: [],
      });

      await expect(
        repository.create({
          ...MOCK_CREATE_SUBSCRIPTION_ENTITY,
          status: SubscriptionStatus.PENDING,
        })
      ).rejects.toThrowError(FatalRepositoryError);
    });

    it('throws CouldNotPersistEntityError when unknown error occurs', async () => {
      const status = SubscriptionStatus.PENDING,
        userId = v4(),
        protoSubscription = {
          status,
          userId,
          order: {
            id: v4(),
            origin: OrderOrigin.SALESFORCE,
            orderedAt: new Date(),
          },
        };

      mockEntityManagerCreateOnce(
        entityManager,
        protoSubscription as Subscription
      );

      entityManager.save.mockRejectedValueOnce(new Error('Knowledge...'));

      await expect(
        repository.create({
          ...MOCK_CREATE_SUBSCRIPTION_ENTITY,
          status: SubscriptionStatus.PENDING,
        })
      ).rejects.toThrowError(CouldNotPersistEntityError);
    });

    it('throws FatalRepositoryError when db details is invalid', async () => {
      mockEntityManagerCreateOnce(entityManager, MOCK_SUBSCRIPTION);

      entityManager.save.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [
          {
            ...MOCK_PLAN,
            details: {
              ...MOCK_PLAN.details,
              allowancePeriod: 'fake',
            },
          },
        ],
      });

      await expect(
        repository.create({
          ...MOCK_CREATE_SUBSCRIPTION_ENTITY,
          status: SubscriptionStatus.PENDING,
        })
      ).rejects.toThrowError(FatalRepositoryError);
    });
  });

  describe(TypeOrmSubscriptionRepository.prototype.delete, () => {
    it('calls entityManager.softDelete', async () => {
      const id = v4();

      await repository.delete(id);

      expect(entityManager.softDelete).toBeCalledWith(Subscription, {
        id,
      });
    });

    it('throws CouldNotDeleteEntityError when unknown error occurs', async () => {
      const id = v4();

      entityManager.softDelete.mockRejectedValueOnce(
        new Error('Look at all dem chickens')
      );

      await expect(repository.delete(id)).rejects.toThrowError(
        CouldNotDeleteEntityError
      );
    });
  });

  describe(
    TypeOrmSubscriptionRepository.prototype.getPendingSubscriptionByOrderId,
    () => {
      it('calls entityManager.findOne', async () => {
        entityManager.findOne.mockResolvedValueOnce({
          ...MOCK_SUBSCRIPTION,
          plans: [MOCK_PLAN],
        });

        const res = await repository.getPendingSubscriptionByOrderId(
          MOCK_SUBSCRIPTION.order.id
        );

        expect(res).toStrictEqual(MOCK_SUBSCRIPTION_ENTITY);

        expect(entityManager.findOne).toHaveBeenCalledTimes(1);
        expect(entityManager.findOne).toHaveBeenCalledWith(Subscription, {
          where: {
            order: JsonContains({
              id: MOCK_SUBSCRIPTION_ENTITY.order.id,
            }),
            status: ActionStatus.PENDING,
          },
          relations: ['plans', 'actions'],
        });
      });

      it('returns null if no matching result', async () => {
        entityManager.findOne.mockResolvedValueOnce(null);

        const res = await repository.getPendingSubscriptionByOrderId(
          MOCK_SUBSCRIPTION_ENTITY.order.id
        );

        expect(res).toBeNull();

        expect(entityManager.findOne).toHaveBeenCalledTimes(1);
        expect(entityManager.findOne).toHaveBeenCalledWith(Subscription, {
          where: {
            order: JsonContains({
              id: MOCK_SUBSCRIPTION_ENTITY.order.id,
            }),
            status: ActionStatus.PENDING,
          },
          relations: ['plans', 'actions'],
        });
      });

      it('throws FatalRepositoryError when unknown error occurs', async () => {
        entityManager.findOne.mockRejectedValueOnce(new Error());

        await expect(
          repository.getPendingSubscriptionByOrderId(v4())
        ).rejects.toThrowError(FatalRepositoryError);
      });
    }
  );

  describe(TypeOrmSubscriptionRepository.prototype.activateSubscription, () => {
    it('activates the subscription', async () => {
      const now = new Date(Date.now());

      jest.spyOn(Date, 'now').mockImplementation(() => now.getTime());

      entityManager.findOne.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [MOCK_PLAN],
      });

      entityManager.save.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        status: SubscriptionStatus.ACTIVE,
        activatedAt: now,
        plans: [MOCK_PLAN],
      });

      const subscription = await repository.activateSubscription(
        MOCK_SUBSCRIPTION.id
      );

      expect(subscription).toEqual({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
        activatedAt: now,
        plan: MOCK_POD_DRIVE_PLAN_ENTITY,
      });

      expect(entityManager.findOne).toHaveBeenCalledTimes(1);
      expect(entityManager.findOne).toHaveBeenCalledWith(Subscription, {
        where: {
          id: MOCK_SUBSCRIPTION.id,
        },
        relations: ['plans', 'actions'],
      });

      expect(entityManager.save).toHaveBeenCalledTimes(1);
      expect(entityManager.save).toHaveBeenCalledWith(Subscription, {
        ...MOCK_SUBSCRIPTION,
        status: SubscriptionStatus.ACTIVE,
        activatedAt: now,
        plans: [MOCK_PLAN],
      });
    });

    it('throws a FatalRepositoryException if update fails', async () => {
      entityManager.update.mockRejectedValueOnce(new Error('oops'));

      await expect(repository.activateSubscription(v4())).rejects.toThrow(
        FatalRepositoryError
      );
    });

    it('throws a FatalRepositoryException if action can not be found', async () => {
      entityManager.findOne.mockResolvedValueOnce(null);

      await expect(repository.activateSubscription(v4())).rejects.toThrow(
        FatalRepositoryError
      );
    });
  });

  describe(
    TypeOrmSubscriptionRepository.prototype.updateSubscriptionStatus,
    () => {
      it('updates the status of an subscription', async () => {
        entityManager.findOne.mockResolvedValueOnce({
          ...MOCK_SUBSCRIPTION,
        });

        entityManager.save.mockResolvedValueOnce({
          ...MOCK_SUBSCRIPTION,
          plans: [MOCK_PLAN],
          status: SubscriptionStatus.ACTIVE,
        });

        const subscription = await repository.updateSubscriptionStatus(
          MOCK_SUBSCRIPTION.id,
          SubscriptionStatus.ACTIVE
        );

        expect(subscription).toEqual({
          ...MOCK_SUBSCRIPTION_ENTITY,
          status: SubscriptionStatus.ACTIVE,
        });

        expect(entityManager.findOne).toHaveBeenCalledTimes(1);
        expect(entityManager.findOne).toHaveBeenCalledWith(Subscription, {
          where: {
            id: MOCK_SUBSCRIPTION.id,
          },
          relations: ['plans', 'actions'],
        });

        expect(entityManager.save).toHaveBeenCalledTimes(1);
        expect(entityManager.save).toHaveBeenCalledWith(Subscription, {
          ...MOCK_SUBSCRIPTION,
          status: SubscriptionStatus.ACTIVE,
        });
      });

      it('throws a FatalRepositoryException if update fails', async () => {
        entityManager.update.mockRejectedValueOnce(new Error('oops'));

        await expect(
          repository.updateSubscriptionStatus(v4(), SubscriptionStatus.ACTIVE)
        ).rejects.toThrow(FatalRepositoryError);
      });

      it('throws a FatalRepositoryException if action can not be found', async () => {
        entityManager.findOne.mockResolvedValueOnce(null);

        await expect(
          repository.updateSubscriptionStatus(v4(), SubscriptionStatus.ACTIVE)
        ).rejects.toThrow(FatalRepositoryError);
      });
    }
  );

  describe(TypeOrmSubscriptionRepository.prototype.getByActionId, () => {
    it('calls entityManager.findOne', async () => {
      entityManager.findOne.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION,
        plans: [MOCK_PLAN],
      });

      await repository.getByActionId('any-id');

      expect(entityManager.findOne).toBeCalledWith(Subscription, {
        where: { actions: { id: 'any-id' } },
        relations: ['actions', 'plans'],
      });
    });

    it('throws FatalRepositoryError when unknown error occurs', async () => {
      entityManager.findOne.mockRejectedValueOnce(new Error('Knowledge...'));

      await expect(
        repository.getByActionId(MOCK_SURVEY_ACTION_ENTITY.id)
      ).rejects.toThrowError(FatalRepositoryError);
    });
  });

  describe(
    TypeOrmSubscriptionRepository.prototype.cancelAndDeleteSubscription,
    () => {
      it('throws a CouldNotFindEntityError if the subscription could not be found', async () => {
        await repository.cancelAndDeleteSubscription('invalid-subscription-id');

        const callback: Callback = entityManager.transaction.mock
          .calls[0][0] as unknown as Callback;

        const mockEntityManager = createMock<EntityManager>();

        (
          mockEntityManager.findOne as unknown as jest.MockedFunction<
            () => Promise<null>
          >
        ).mockResolvedValueOnce(null);

        await expect(() => callback(mockEntityManager)).rejects.toThrow(
          CouldNotFindEntityError
        );

        expect(mockEntityManager.findOne).toHaveBeenCalledTimes(1);
        expect(mockEntityManager.findOne).toHaveBeenCalledWith(Subscription, {
          where: { id: 'invalid-subscription-id' },
          relations: ['plans', 'actions'],
        });

        expect(mockEntityManager.update).not.toHaveBeenCalled();
        expect(mockEntityManager.softRemove).not.toHaveBeenCalled();
      });

      it('updates the subscription status to CANCELLED and soft removes it in a transaction', async () => {
        await repository.cancelAndDeleteSubscription(MOCK_SUBSCRIPTION.id);

        const callback: Callback = entityManager.transaction.mock
          .calls[0][0] as unknown as Callback;

        const mockEntityManager = createMock<EntityManager>();

        (
          mockEntityManager.findOne as unknown as jest.MockedFunction<
            () => Promise<Subscription>
          >
        ).mockResolvedValueOnce(MOCK_SUBSCRIPTION);

        await callback(mockEntityManager);

        expect(mockEntityManager.findOne).toHaveBeenCalledTimes(1);
        expect(mockEntityManager.findOne).toHaveBeenCalledWith(Subscription, {
          where: { id: MOCK_SUBSCRIPTION.id },
          relations: ['plans', 'actions'],
        });

        expect(mockEntityManager.update).toHaveBeenCalledTimes(1);
        expect(mockEntityManager.update).toHaveBeenCalledWith(
          Subscription,
          {
            id: MOCK_SUBSCRIPTION.id,
          },
          {
            status: SubscriptionStatus.CANCELLED,
          }
        );

        expect(mockEntityManager.softRemove).toHaveBeenCalledTimes(1);
        expect(mockEntityManager.softRemove).toHaveBeenCalledWith(
          MOCK_SUBSCRIPTION
        );
      });
    }
  );
});
