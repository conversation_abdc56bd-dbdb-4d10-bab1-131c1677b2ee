// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`mobile api TescoClubcardModule should get a users clubcard 1`] = `
{
  "customerId": "1234567890",
}
`;

exports[`mobile api carbon module carbon controller should return a 30m intensity data set 1`] = `
{
  "data": {
    "from": "2023-03-27T09:00:00Z",
    "intensity": {
      "forecast": 200,
      "index": "moderate",
    },
    "to": "2023-03-27T09:30:00Z",
  },
  "dnoregion": "Scottish Hydro Electric Power Distribution",
  "regionid": 1,
  "shortname": "North Scotland",
}
`;

exports[`mobile api carbon module carbon controller should return a 48hr intensity data set 1`] = `
{
  "data": {
    "dates": [
      {
        "date": "2023-03-27",
        "entries": [
          {
            "from": "09:00",
            "intensity": {
              "forecast": 200,
              "index": "moderate",
            },
            "to": "09:30",
          },
        ],
      },
      {
        "date": "2023-03-28",
        "entries": [],
      },
    ],
    "dnoregion": "Scottish Hydro Electric Power Distribution",
    "regionid": 1,
    "shortname": "North Scotland",
  },
}
`;

exports[`mobile api carbon module carbon controller should return a list of DNO regions 1`] = `
{
  "data": [
    {
      "dnoregion": "Electricity North West",
      "regionid": 3,
      "shortname": "North West England",
    },
  ],
}
`;

exports[`mobile api chargers module chargers controller /chargers should return chargers 1`] = `
[
  {
    "delegatedControl": {
      "status": "ACTIVE",
    },
    "linkedAt": "2024-01-03T:00:00:00.000Z",
    "modelInfo": {
      "architecture": "3.0",
      "colour": "black",
      "ledColourSet": "uk",
      "style": "solo",
    },
    "ppid": "PSL-11111",
    "timezone": "UTC",
    "unitId": 1,
  },
  {
    "delegatedControl": {
      "status": "ACTIVE",
    },
    "linkedAt": "2024-01-02T:00:00:00.000Z",
    "modelInfo": {
      "architecture": "3.0",
      "colour": "black",
      "ledColourSet": "uk",
      "style": "solo",
    },
    "ppid": "PSL-123456",
    "timezone": "UTC",
    "unitId": 2,
  },
  {
    "delegatedControl": {
      "status": "ACTIVE",
    },
    "linkedAt": "2024-01-01T:00:00:00.000Z",
    "modelInfo": {
      "architecture": "5.0",
      "colour": "black",
      "ledColourSet": "uk",
      "style": "solo3s",
    },
    "ppid": "PSL-555555",
    "timezone": "UTC",
    "unitId": 3,
  },
]
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/charge-overrides should create a charger override for a charger 1`] = `
[
  {
    "chargingStation": {
      "addressId": "04befc49-3c2c-4d81-97d2-0b90f99ce053",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "tandem inflammatio ambulo",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "efc0b020-2bb5-4bd2-9156-4c5ce8614e39",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "aliqua aegrotatio coerceo",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "afb519b5-a90e-466d-a77a-38a8b9de1403",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "alius velum aegre",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "74ae0bad-3b10-4a00-907c-f3ccd52c9600",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "aeternus arma tepesco",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
]
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/charge-overrides should delete a charger's charge overrides 1`] = `""`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/charge-overrides should get a charger's charge overrides 1`] = `
[
  {
    "chargingStation": {
      "addressId": "4d4edab2-9573-4e67-bb18-da5beeb45867",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "demonstro vesica cur",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "dc6f4013-83d0-4bf4-a9b5-6b2263421e59",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "amet voluptate succurro",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "e1955493-4405-40db-99be-b91699f3a46f",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "defaeco tamisium conatus",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "699da8a0-f264-4985-8fce-7964833807fd",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "conturbo decerno adhaero",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "5a80d7d3-1870-4010-b297-f1d9e01d980a",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "sollicito caelum angulus",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
  {
    "chargingStation": {
      "addressId": "6336aac3-bc87-4207-aa36-7fedfc3d2e54",
      "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
      "mpan": "vulnus damnatio color",
      "ppid": "PSL-000000",
    },
    "deletedAt": "2021-08-01T00:00:00.000Z",
    "endAt": "2021-08-01T00:00:00.000Z",
    "evse": {
      "door": "A",
      "ocppEvseId": 1,
    },
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "receivedAt": "2021-08-01T00:00:00.000Z",
    "requestedAt": "2021-08-01T00:00:00.000Z",
  },
]
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/connectivity-status should return a connectivity status 1`] = `
{
  "connectedComponents": [
    "evses",
  ],
  "evses": [
    {
      "architecture": "arch3",
      "connectivityState": {
        "connectionQuality": 5,
        "connectionStartedAt": "2023-08-21T14:16:47.622Z",
        "connectivityStatus": "ONLINE",
        "lastMessageAt": "2023-08-21T15:05:47Z",
        "protocol": "POW",
        "signalStrength": -44,
      },
      "connectors": [
        {
          "chargingState": "CHARGING",
          "door": "A",
          "id": 1,
        },
      ],
      "energyOfferStatus": {
        "isOfferingEnergy": true,
        "reason": "FLEX_REQUEST",
        "until": "2023-08-21T14:16:47.622Z",
      },
      "id": 1,
    },
  ],
  "ppid": "PSL-1234567",
}
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/dnoregion should return a DNO region 1`] = `
{
  "regionid": 4,
}
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/firmware should return a 200 and return a user's charger firmware based on the given charger when authenticated 1`] = `
[
  {
    "serialNumber": "181410663",
    "updateStatus": {
      "isUpdateAvailable": false,
    },
    "versionInfo": {
      "architecture": "arch2",
      "details": {
        "dspVersion": "2.35F14C",
        "wifiVersion": "01.14",
      },
      "manifestId": "A22P-2.35.0-00002",
    },
  },
  {
    "serialNumber": "181310113",
    "updateStatus": {
      "isUpdateAvailable": true,
    },
    "versionInfo": {
      "architecture": "arch2",
      "details": {
        "dspVersion": "2.26F",
        "wifiVersion": "01.13",
      },
      "manifestId": "A22P-2.26.0-00001",
    },
  },
]
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/flex-enrolment should return a flex enrolment status 1`] = `
{
  "enrolmentStatus": {
    "isEnrolled": true,
  },
  "ppid": "PSL-1234567",
}
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/flex-requests should return flex requests 1`] = `
[
  {
    "direction": "INCREASE",
    "endAt": "2023-10-04T10:45:00.000Z",
    "id": "d40ff7bc-cd94-4e4f-9d52-34a80e83e64b",
    "limit": {
      "unit": "AMP",
      "value": 0,
    },
    "requestedAt": "2023-10-01T00:00:00.000Z",
    "startAt": "2023-10-04T08:30:00.000Z",
  },
  {
    "direction": "REDUCE",
    "endAt": "2023-10-09T16:00:00.000Z",
    "id": "e12ff7bc-aa20-3e3e-8cc3-44a50e81e80c",
    "limit": {
      "unit": "KW",
      "value": 200,
    },
    "requestedAt": "2023-10-03T00:00:00.000Z",
    "startAt": "2023-10-07T10:15:00.000Z",
  },
]
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/model-info should get a charger's model information 1`] = `
{
  "architecture": "3.0",
  "colour": "black",
  "ledColourSet": "uk",
  "style": "solo",
}
`;

exports[`mobile api chargers module chargers controller /chargers/:ppid/restrictions should return a restriction 1`] = `
{
  "chargeAllowed": true,
  "chargeLimits": [
    {
      "amount": 20,
      "type": "energy",
      "unit": "kWh",
    },
  ],
  "minimumBalance": {
    "amount": 20,
    "currency": "GBP",
  },
  "userBalance": {
    "amount": 30,
    "currency": "GBP",
  },
}
`;

exports[`mobile api chargers module chargers controller DELETE /chargers/:ppid/tariffs should return a 204 1`] = `""`;

exports[`mobile api chargers module chargers controller GET /chargers/:ppid/tariffs should return a 200 and return the saved tariff 1`] = `
{
  "data": [
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "7a17bf0d-fff2-4e25-9545-81592351c466",
      "maxChargePrice": 0.2,
      "ppid": "possimus absens vos",
      "supplierId": "deporto cognomen sono",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "027e6b9f-2b5f-45fd-8902-0c1c04498ea2",
      "maxChargePrice": 0.2,
      "ppid": "antiquus varietas stabilis",
      "supplierId": "cenaculum aliquam anser",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "970fd68e-6f0b-4c32-a979-2411b084d8f8",
      "maxChargePrice": 0.2,
      "ppid": "tempus facilis nihil",
      "supplierId": "cupio officia cultura",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "b473ad30-4a4d-4d55-8082-10220fa600c6",
      "maxChargePrice": 0.2,
      "ppid": "conculco casus curso",
      "supplierId": "calamitas aeneus currus",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "87c28236-287f-4a19-868a-257948495dad",
      "maxChargePrice": 0.2,
      "ppid": "aspernatur sponte curso",
      "supplierId": "alo cruciamentum adinventitias",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "0ac57ca0-4fe5-4e73-9430-6e883023df07",
      "maxChargePrice": 0.2,
      "ppid": "audax illo aduro",
      "supplierId": "fugiat condico communis",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "a18214cb-848e-42bd-aa32-82d84949338d",
      "maxChargePrice": 0.2,
      "ppid": "facilis praesentium crustulum",
      "supplierId": "auctus numquam ademptio",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "362e3bf7-d6dc-4f5c-9ecd-f471e5e35beb",
      "maxChargePrice": 0.2,
      "ppid": "alius appello cribro",
      "supplierId": "comitatus vallum vallum",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "4886eb23-85c6-4f27-be14-3f7ed157ef5c",
      "maxChargePrice": 0.2,
      "ppid": "adhaero conatus demonstro",
      "supplierId": "sustineo varietas absconditus",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "909b5c66-4be7-4fc4-a356-7bd07669f5f3",
      "maxChargePrice": 0.2,
      "ppid": "cicuta avarus sint",
      "supplierId": "magni arma talis",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "20ca41bb-8a6d-48eb-a8be-36d0261bf7f2",
      "maxChargePrice": 0.2,
      "ppid": "summisse utrum terminatio",
      "supplierId": "vociferor ipsa ventosus",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "6607354a-79dd-4976-b176-3cbf66858eb3",
      "maxChargePrice": 0.2,
      "ppid": "nostrum textilis fugiat",
      "supplierId": "volva decerno virga",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "5beafb2b-65cc-4f38-8c83-fe98ded6d385",
      "maxChargePrice": 0.2,
      "ppid": "theatrum thermae comes",
      "supplierId": "placeat solio animi",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "46be6b21-7c3c-4418-b0bb-fa7182c93b5a",
      "maxChargePrice": 0.2,
      "ppid": "arbustum video autem",
      "supplierId": "sol capitulus esse",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "d7c35cd6-e907-42b5-a3dd-5602b9d8c892",
      "maxChargePrice": 0.2,
      "ppid": "aiunt dolorem amet",
      "supplierId": "sodalitas comedo sed",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
    {
      "cheapestUnitPrice": 0.2,
      "effectiveFrom": "2021-01-01",
      "id": "e151e246-a535-49f5-a5b4-5f9d02b8f763",
      "maxChargePrice": 0.2,
      "ppid": "averto iure ara",
      "supplierId": "audax teneo aeneus",
      "tariffInfo": [
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
        {
          "days": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY",
            "SATURDAY",
            "SUNDAY",
          ],
          "end": "00:00:00",
          "price": 0.3,
          "start": "00:00:00",
        },
      ],
      "timezone": "Europe/London",
    },
  ],
  "metadata": {
    "criteria": {
      "effectiveFrom": "2024-08-25T20:05:46.646Z",
      "effectiveTo": "2024-08-25T20:05:46.646Z",
      "ppid": "tener vesper apto",
    },
  },
}
`;

exports[`mobile api chargers module chargers controller POST /chargers/:ppid/tariffs should return a 200 and return the saved tariff 1`] = `
{
  "cheapestUnitPrice": 0.2,
  "effectiveFrom": "2021-01-01",
  "id": "7ab3cedb-d025-4bfb-97e6-a02467c418f6",
  "maxChargePrice": 0.2,
  "ppid": "adulescens venustas creator",
  "supplierId": "texo sulum succurro",
  "tariffInfo": [
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
  ],
  "timezone": "Europe/London",
}
`;

exports[`mobile api chargers module chargers controller PUT /chargers/:ppid/tariffs should return a 200 1`] = `
{
  "cheapestUnitPrice": 0.2,
  "effectiveFrom": "2021-01-01",
  "id": "3c02fefe-7f30-4858-8779-e3eb1d6503bd",
  "maxChargePrice": 0.2,
  "ppid": "absorbeo demulceo auctor",
  "supplierId": "pel quam cunae",
  "tariffInfo": [
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
    {
      "days": [
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
        "SUNDAY",
      ],
      "end": "00:00:00",
      "price": 0.3,
      "start": "00:00:00",
    },
  ],
  "timezone": "Europe/London",
}
`;

exports[`mobile api check for upgrade module check for upgrade controller should proxy the response to the client 1`] = `
{
  "forceUpgrade": false,
  "found": true,
  "message": "A non-mandatory update is available",
  "query": {
    "app_language": "en",
    "app_name": "Installer App",
    "app_version": "1.0.2",
    "environment": "production",
    "platform": "ios",
  },
}
`;

exports[`mobile api check for upgrade module check for upgrade controller should proxy the response to the client 2`] = `
{
  "forceUpgrade": false,
  "found": true,
  "message": "A non-mandatory update is available",
  "query": {
    "app_language": "en",
    "app_name": "Installer App",
    "app_version": "1.0.2",
    "environment": "production",
    "platform": "ios",
  },
}
`;

exports[`mobile api energy module GET /energy/suppliers should return a 200 OK with a list of suppliers on success 1`] = `
[
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "corporis talus torqueo",
    "id": "deficio fugiat benevolentia",
    "name": "Theresa Price",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "timor acies studio",
    "id": "demergo taedium utique",
    "name": "Ms. Rochelle Hane",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "toties vita demitto",
    "id": "desino audeo tero",
    "name": "Ruby Homenick",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "acsi subiungo tredecim",
    "id": "advoco delectus tertius",
    "name": "Terence Hartmann",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "demens thorax volubilis",
    "id": "ait cum ea",
    "name": "Nettie Schroeder",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "somnus ustulo expedita",
    "id": "creator laudantium confero",
    "name": "Rick Lynch",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "tenuis odio cariosus",
    "id": "at acsi sonitus",
    "name": "Adrian Quitzon",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "delectatio adulatio depraedor",
    "id": "aequus desparatus coniecto",
    "name": "Patsy Langworth",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "earum curvo despecto",
    "id": "viridis vaco capitulus",
    "name": "Sherri Sporer",
    "timeZone": "Europe/London",
  },
  {
    "defaultMaxChargePrice": 0.2,
    "defaultTariffInfo": [
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
      {
        "days": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY",
        ],
        "end": "00:00:00",
        "price": 0.3,
        "start": "00:00:00",
      },
    ],
    "icon": "tot demoror cilicium",
    "id": "vitium appositus distinctio",
    "name": "Megan Reichert",
    "timeZone": "Europe/London",
  },
]
`;

exports[`mobile api health module health controller should perform a health check 1`] = `
{
  "details": {},
  "error": {},
  "info": {},
  "status": "ok",
}
`;

exports[`mobile api linky module linky controller GET /:ppid should return linky status for the given charger 1`] = `
{
  "linkyCapable": null,
  "scheduleEnabled": null,
}
`;

exports[`mobile api linky module linky controller POST /:ppid should return the linky status for the given charger 1`] = `
{
  "linkyCapable": true,
  "scheduleEnabled": true,
}
`;

exports[`mobile api location module location controller should find an address from valid id 1`] = `
[
  {
    "AdminAreaCode": "",
    "AdminAreaName": "Islington",
    "Barcode": "(EC1Y8QE4B6)",
    "Block": "",
    "BuildingName": "Discovery House 28-42",
    "BuildingNumber": "",
    "City": "London",
    "Company": "Pod Point Ltd",
    "CountryIso2": "GB",
    "CountryIso3": "GBR",
    "CountryIsoNumber": 826,
    "CountryName": "United Kingdom",
    "DataLevel": "Premise",
    "Department": "",
    "District": "",
    "DomesticId": "8182563",
    "Field1": "",
    "Field10": "",
    "Field11": "",
    "Field12": "",
    "Field13": "",
    "Field14": "",
    "Field15": "",
    "Field16": "",
    "Field17": "",
    "Field18": "",
    "Field19": "",
    "Field2": "",
    "Field20": "",
    "Field3": "",
    "Field4": "",
    "Field5": "",
    "Field6": "",
    "Field7": "",
    "Field8": "",
    "Field9": "",
    "Id": "GB|RM|A|8182563|ENG",
    "Label": "Pod Point Ltd
Discovery House
28-42 Banner Street
LONDON
EC1Y 8QE
UNITED KINGDOM",
    "Language": "ENG",
    "LanguageAlternatives": "ENG",
    "Line1": "Discovery House",
    "Line2": "28-42 Banner Street",
    "Line3": "",
    "Line4": "",
    "Line5": "",
    "Neighbourhood": "",
    "POBoxNumber": "",
    "PostalCode": "EC1Y 8QE",
    "Province": "",
    "ProvinceCode": "",
    "ProvinceName": "",
    "SecondaryStreet": "",
    "SortingNumber1": "74116",
    "SortingNumber2": "",
    "Street": "Banner Street",
    "SubBuilding": "",
    "Type": "Commercial",
  },
]
`;

exports[`mobile api location module location controller should return a list of addresses 1`] = `
[
  {
    "Description": "Banner Street, London - 26 Addresses",
    "Highlight": "0-4,5-8",
    "Id": "GB|RM|ENG|8QE-EC1Y",
    "Text": "EC1Y 8QE",
    "Type": "Postcode",
  },
]
`;

exports[`mobile api marketing module marketing controller gets the marketing opportunities for a given charger 1`] = `
{
  "opportunities": [
    "REWARDS",
  ],
}
`;

exports[`mobile api payment module payment controller create-payment-intent successfully creates a payment intent for payment 1`] = `
{
  "customer": "veritatis stips ver",
  "ephemeralKey": "cogito vinco urbs",
  "paymentIntent": "civitas culpa ad",
}
`;

exports[`mobile api payment module payment controller setup-intent successfully creates a setup intent for payment 1`] = `
{
  "customer": "correptius arcus thymbra",
  "ephemeralKey": "vapulus vacuus quibusdam",
  "setupIntent": "sunt subseco spoliatio",
}
`;

exports[`mobile api payment module payment controller webhook successfully calls billing-api webhook 1`] = `""`;

exports[`mobile api remote lock module remote lock controller GET /:ppid should return an offMode of null for the given charger if it is not arch 5+ 1`] = `
{
  "offMode": null,
}
`;

exports[`mobile api remote lock module remote lock controller GET /:ppid should return the remote lock status for the given charger 1`] = `
{
  "offMode": null,
}
`;

exports[`mobile api remote lock module remote lock controller POST /:ppid should return the remote lock status for the given charger 1`] = `
{
  "offMode": true,
}
`;

exports[`mobile api reward wallet module reward wallet controller GET /reward-wallet returns a 200 1`] = `
{
  "allowance": {
    "annualAllowanceMiles": 7500,
    "balanceMiles": 6532,
  },
  "payments": {
    "thresholdGbp": 10,
  },
  "rewards": {
    "balanceGbp": 23.71,
    "balanceMiles": 1018,
  },
}
`;

exports[`mobile api reward wallet module reward wallet controller POST /reward-wallet/payout returns a 201 1`] = `""`;

exports[`mobile api rewards module GET /rewards/bank-accounts returns a 200 OK on success 1`] = `
[
  {
    "id": "9ba622ae-500a-4ffe-aac6-ecf65e4e0186",
    "last4": "adiuvo reprehenderit super",
    "name": "Miriam Little",
  },
  {
    "id": "b6e7a000-81b5-4122-aec6-e0b4c3d23c86",
    "last4": "perferendis arcus valens",
    "name": "Jenna Monahan",
  },
  {
    "id": "4e5a22a3-c2f4-411b-a81c-e7e7e8b508eb",
    "last4": "tego volubilis bellum",
    "name": "Dr. Judith Strosin Jr.",
  },
  {
    "id": "65a6f79e-1d4f-4e4e-8889-c965e8d9ea28",
    "last4": "viduo incidunt conatus",
    "name": "Jerome Hudson",
  },
  {
    "id": "0200e610-8996-4bf7-b206-f922a9c16da1",
    "last4": "coepi sumo sophismata",
    "name": "Dan Muller",
  },
  {
    "id": "d22490de-b510-4caf-9f1f-81f52c37856c",
    "last4": "calamitas strenuus cui",
    "name": "Ramona Lynch",
  },
  {
    "id": "de64fc2e-e3cc-4986-8e24-3d5b43ff09c2",
    "last4": "adficio ventus repellat",
    "name": "Krystal Bartell",
  },
  {
    "id": "a3e6ef1d-0548-410e-8cbc-29d0cce6cb6f",
    "last4": "vorax apparatus cuppedia",
    "name": "Hazel O'Connell",
  },
  {
    "id": "a637c476-3a79-41dd-8542-cc6c8f9106d4",
    "last4": "consuasor odio tibi",
    "name": "Delbert Bartell",
  },
  {
    "id": "c04d0f05-a9a2-4820-ab68-1d622aa0675b",
    "last4": "provident vester terga",
    "name": "Alvin Rippin III",
  },
  {
    "id": "a2bf0d7e-2a39-47df-ad7d-20a6db289555",
    "last4": "canto careo cinis",
    "name": "Kara Lesch DDS",
  },
  {
    "id": "adaf17a5-62f2-475d-a8ff-6902445297f2",
    "last4": "dolor defero culpa",
    "name": "Eduardo Quitzon",
  },
]
`;

exports[`mobile api rewards module GET /rewards/transactions returns a 200 OK on success 1`] = `
[
  {
    "createdAt": "ascit sursum cito",
    "currency": "repellat animadverto circumvenio",
    "id": "a5a0b95c-8b50-4a66-877a-62040ddb9cd1",
    "status": "PENDING",
    "value": 5269104708040403,
  },
  {
    "createdAt": "autem cattus cogito",
    "currency": "collum venio commemoro",
    "id": "c343669e-1c69-4d14-b675-1034a6cf4eea",
    "status": "POSTED",
    "value": 6102894901909722,
  },
  {
    "createdAt": "absorbeo attollo suppellex",
    "currency": "suasoria minus defendo",
    "id": "631ca9d0-f9ce-44ff-91ff-4fb79d7e452e",
    "status": "RETURNED",
    "value": 2936603505595343,
  },
  {
    "createdAt": "cupio tristis voluptatem",
    "currency": "civis venia ultra",
    "id": "ccc0dd2a-994c-490a-be3f-a304bf6cdbb3",
    "status": "POSTED",
    "value": 5836516362323628,
  },
  {
    "createdAt": "civis tristis earum",
    "currency": "antea iure credo",
    "id": "1c7f9cc4-fac8-4000-bec0-1cf66cad83df",
    "status": "PENDING",
    "value": 7334936887688474,
  },
  {
    "createdAt": "curriculum substantia distinctio",
    "currency": "coniecto sit asper",
    "id": "b098173e-5309-4c7f-83c5-a786cf54776d",
    "status": "PENDING",
    "value": 209849145322906,
  },
  {
    "createdAt": "cogito defendo cimentarius",
    "currency": "decipio alii deficio",
    "id": "7e9ada96-e556-454f-8650-5de93d62636f",
    "status": "RETURNED",
    "value": 5468005505082582,
  },
  {
    "createdAt": "cum vobis laudantium",
    "currency": "tonsor aro adstringo",
    "id": "cb67fb91-6ab8-484f-bbed-bbe448940c35",
    "status": "RETURNED",
    "value": 8597541202416035,
  },
  {
    "createdAt": "atqui sto conturbo",
    "currency": "pax curo suffoco",
    "id": "aff50fab-3f7b-4f49-9c11-b31ecb67186c",
    "status": "PENDING",
    "value": 457789356714184,
  },
  {
    "createdAt": "demonstro sub avaritia",
    "currency": "theologus ventito inflammatio",
    "id": "bc0982ed-51d2-4bed-98b6-aca0eef6900b",
    "status": "RETURNED",
    "value": 4741036804247812,
  },
  {
    "createdAt": "cinis summisse veniam",
    "currency": "demonstro crudelis neque",
    "id": "e12a128b-d7f8-4f6a-96f7-769012e9be8c",
    "status": "CANCELLED",
    "value": 1208880628879464,
  },
  {
    "createdAt": "verecundia vestigium valens",
    "currency": "pax appositus crur",
    "id": "c3ce3fc5-b74a-4db3-9dcc-66917564de75",
    "status": "RETURNED",
    "value": 4102801536641478,
  },
  {
    "createdAt": "totam caecus commodo",
    "currency": "colo auditor aspicio",
    "id": "7a7c0c62-9c1c-4266-ac48-157cc2152ae1",
    "status": "RETURNED",
    "value": 3484296223918362,
  },
  {
    "createdAt": "tibi delicate uredo",
    "currency": "autus vado admoveo",
    "id": "d44698f1-52c3-4528-bee2-769ce3ea573d",
    "status": "PENDING",
    "value": ****************,
  },
  {
    "createdAt": "sui tabella velum",
    "currency": "solus deficio tibi",
    "id": "********-3895-4ec0-97a1-93198e5c5bbb",
    "status": "CANCELLED",
    "value": ****************,
  },
  {
    "createdAt": "demo summa dolor",
    "currency": "vilicus correptius taceo",
    "id": "aa8d2576-41ad-4d32-ad0e-14527df4453d",
    "status": "PENDING",
    "value": ****************,
  },
]
`;

exports[`mobile api rewards module POST /rewards/bank-accounts returns a 201 created on success 1`] = `
{
  "id": "5cf7826e-aa13-4533-8c9f-4c3d564855d1",
  "last4": "vaco aequus uredo",
  "name": "Bob Barton",
}
`;

exports[`mobile api rewards module POST /rewards/webhook successfully calls payments-api webhook 1`] = `""`;

exports[`mobile api rewards module PUT /rewards/bank-accounts/:bankAccountId returns a 201 created on success 1`] = `
{
  "id": "d59f322b-45f2-4f83-8892-5696ee2850ca",
  "last4": "cuius vestrum deserunt",
  "name": "May Thiel",
}
`;

exports[`mobile api smart charging module DELETE /smart-charging/delegated-controls/:ppid should return a 204 status on success 1`] = `""`;

exports[`mobile api smart charging module DELETE /smart-charging/delegated-controls/:ppid/vehicles/:vehicleId should return a 200 OK status on success 1`] = `""`;

exports[`mobile api smart charging module GET /smart-charging/delegated-controls/:ppid should return a 200 on status 1`] = `
{
  "createdAt": "2021-01-01T00:00:00Z",
  "id": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
  "ppid": "PSL-123456",
  "status": "ACTIVE",
  "thirdPartyManagerProviderId": "quod tantillus maiores",
  "vehicleLinks": [
    {
      "id": "demitto vesper similique",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "cinis porro alienus",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "83dda312-b00b-4a19-8c34-255fb6eef841",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "crur acidus desparatus",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "tergo color delectatio",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
          "batteryLevelPercent": 78,
          "chargeLimitPercent": 78,
          "chargeRate": 78,
          "chargeTimeRemaining": 78,
          "isCharging": true,
          "isFullyCharged": true,
          "isPluggedIn": true,
          "lastUpdated": "2021-08-12T09:00:00Z",
          "maxCurrent": 32,
          "powerDeliveryState": "PLUGGED_IN:STOPPED",
          "range": 300,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "e44f9871-cb6a-4c4b-bd77-4807b8b84534",
        "interventions": {
          "all": "creta audio comitatus",
          "chargeState": [
            "culpa contigo accusantium",
            "tardus desidero corrigo",
            "accusator voluntarius contego",
            "derideo provident confugo",
            "vinculum acsi tergo",
            "umbra universe conventus",
            "rem cuppedia pecus",
            "somniculosus tero cado",
            "tamquam attonbitus socius",
            "saepe aer decor",
            "umerus torqueo carmen",
            "spoliatio colligo ademptio",
            "curvo laboriosam coerceo",
            "illum delego tergum",
          ],
          "information": [
            "una decet subvenio",
            "sublime dedico vulariter",
            "delectus abstergo libero",
            "ocer damnatio comburo",
            "cunctatio arguo decumbo",
            "commodi suus creptio",
            "utilis urbanus taceo",
            "volva laboriosam neque",
            "dolore et carmen",
            "itaque iure aequitas",
            "audax alias pectus",
            "denuo articulus certe",
            "aer nostrum et",
            "substantia magnam stips",
            "accusator dolor tolero",
          ],
        },
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "viscus usitas ex",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "uterque derelinquo audio",
      },
      "isPluggedInToThisCharger": true,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
          "batteryLevelPercent": 78,
          "chargeLimitPercent": 78,
          "chargeRate": 78,
          "chargeTimeRemaining": 78,
          "isCharging": true,
          "isFullyCharged": true,
          "isPluggedIn": true,
          "lastUpdated": "2021-08-12T09:00:00Z",
          "maxCurrent": 32,
          "powerDeliveryState": "PLUGGED_IN:INITIALIZING",
          "range": 300,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "75331987-f8a0-414a-b8f5-54afb63cb1d6",
        "interventions": {
          "all": "validus capto absum",
          "chargeState": [
            "sapiente demens deficio",
            "numquam curvo suppono",
            "creber synagoga tametsi",
            "vitae crepusculum denique",
            "vicinus veritatis volutabrum",
            "infit complectus bellicus",
          ],
          "information": [
            "repellat supplanto arbustum",
            "demens pauper accusamus",
            "causa ventito contabesco",
            "terga repudiandae auxilium",
            "id stultus sumo",
          ],
        },
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "dolores terminatio amet",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "capio hic ager",
      },
      "isPluggedInToThisCharger": true,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "705e0e64-9c1e-4dc3-a411-57f59151d7b2",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "acerbitas acidus celebrer",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "cum celer delicate",
      },
      "isPluggedInToThisCharger": true,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "ae2d3cd6-5b5f-42e6-a81b-f9c86516f8d2",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "urbanus auxilium corrigo",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "agnitio adipiscor colligo",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "b826a18c-d84e-4a9e-a11c-a5a700d8ffe7",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "placeat minima ustulo",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "bene depraedor claudeo",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "7033613c-d3df-47df-9518-97c7167ed0bf",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "suasoria tunc summopere",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "acidus voluptatem allatus",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "04cdf7ee-b3aa-4077-9e35-f139ac2a77a9",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "turba abeo delibero",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "in acsi tribuo",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
          "batteryLevelPercent": 78,
          "chargeLimitPercent": 78,
          "chargeRate": 78,
          "chargeTimeRemaining": 78,
          "isCharging": true,
          "isFullyCharged": true,
          "isPluggedIn": true,
          "lastUpdated": "2021-08-12T09:00:00Z",
          "maxCurrent": 32,
          "powerDeliveryState": "UNPLUGGED",
          "range": 300,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "c2b82214-a519-4cb2-9a08-d1462c080082",
        "interventions": {
          "all": "surgo sustineo verus",
          "chargeState": [
            "corrigo commodo amicitia",
          ],
          "information": [
            "sponte agnitio brevis",
            "succurro delego acerbitas",
          ],
        },
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "coerceo defluo astrum",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "illum absum dens",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
          "batteryLevelPercent": 78,
          "chargeLimitPercent": 78,
          "chargeRate": 78,
          "chargeTimeRemaining": 78,
          "isCharging": true,
          "isFullyCharged": true,
          "isPluggedIn": true,
          "lastUpdated": "2021-08-12T09:00:00Z",
          "maxCurrent": 32,
          "powerDeliveryState": "UNPLUGGED",
          "range": 300,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "049e0661-7d2b-4e52-b41b-f9a49146dd17",
        "interventions": {
          "all": "vinitor cubicularis cattus",
          "chargeState": [
            "ulciscor avaritia vomer",
            "temperantia sursum placeat",
            "tutis despecto tunc",
            "ex tepidus pauper",
            "xiphias dolor ubi",
            "vesco vapulus conitor",
            "amplus desino titulus",
            "theologus congregatio repudiandae",
            "patrocinor aequitas studio",
            "quibusdam adopto creptio",
            "damno teneo earum",
          ],
          "information": [
            "trans vinco ullus",
            "solus crux utrum",
            "apto cumque paulatim",
            "solutio volva textilis",
            "aggero ducimus uredo",
            "illo aut conqueror",
            "neque maiores demum",
            "deorsum victoria abscido",
            "trans sol admoneo",
            "statua civitas caecus",
            "canto vulnero infit",
            "vulticulus aiunt qui",
            "casus sursum pecus",
            "cupressus adaugeo a",
            "commemoro vinculum amiculum",
            "delego super vestigium",
          ],
        },
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "adficio corroboro tactus",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "textus cui supellex",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "cd79c6c4-fec9-44b4-96d0-564f6df11f36",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
    {
      "id": "sum solvo defessus",
      "intents": {
        "details": [
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
          {
            "chargeByTime": "07:00:00",
            "chargeKWh": 28,
            "dayOfWeek": "MONDAY",
          },
        ],
        "id": "bis votum bonus",
      },
      "isPluggedInToThisCharger": false,
      "isPrimary": true,
      "vehicle": {
        "chargeState": {
          "batteryCapacity": 78,
        },
        "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
        "id": "a91e8350-592a-4023-af12-c00c8235c2d6",
        "vehicleInformation": {
          "brand": "Polestar",
          "displayName": "My car",
          "evDatabaseId": "1234567890",
          "model": "2",
          "modelVariant": "Long range",
          "vehicleRegistrationPlate": "ABC123",
        },
      },
    },
  ],
}
`;

exports[`mobile api smart charging module PATCH /smart-charging/delegated-controls/:ppid/vehicle/:vehicleId should return a 200 OK status on success 1`] = `
{
  "chargeState": {
    "batteryCapacity": 78,
  },
  "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
  "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
  "id": "a192af63-7aa4-48f9-8633-2a6897940d0d",
  "vehicleInformation": {
    "brand": "Polestar",
    "displayName": "My car",
    "evDatabaseId": "1234567890",
    "model": "2",
    "modelVariant": "Long range",
    "vehicleRegistrationPlate": "ABC123",
  },
}
`;

exports[`mobile api smart charging module POST /smart-charging/delegated-controls/:ppid/vehicle should return a created 201 status on success 1`] = `
{
  "id": "valde a cotidie",
  "intents": {
    "details": [
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
      {
        "chargeByTime": "07:00:00",
        "chargeKWh": 28,
        "dayOfWeek": "MONDAY",
      },
    ],
    "id": "advoco velum saepe",
  },
  "isPluggedInToThisCharger": false,
  "isPrimary": true,
  "vehicle": {
    "chargeState": {
      "batteryCapacity": 78,
      "batteryLevelPercent": 78,
      "chargeLimitPercent": 78,
      "chargeRate": 78,
      "chargeTimeRemaining": 78,
      "isCharging": true,
      "isFullyCharged": true,
      "isPluggedIn": true,
      "lastUpdated": "2021-08-12T09:00:00Z",
      "maxCurrent": 32,
      "powerDeliveryState": "PLUGGED_IN:CHARGING",
      "range": 300,
    },
    "enodeUserId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
    "enodeVehicleId": "efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b",
    "id": "87798eec-5c2d-4cc8-873c-f8e485f4ac0f",
    "interventions": {
      "all": "architecto eligendi aliquid",
      "chargeState": [
        "terebro patrocinor accedo",
        "adhuc attollo voluptas",
        "caterva tantillus cicuta",
        "cattus causa clibanus",
        "absorbeo votum maxime",
        "pauper fugiat adimpleo",
        "demens harum speculum",
        "explicabo adsidue quam",
        "nobis in turpis",
        "dedecor pax anser",
        "casus ullam acceptus",
        "aro trans calculus",
        "caute substantia voco",
        "avarus tametsi aqua",
        "crebro commodi vacuus",
        "sollers concido acer",
      ],
      "information": [
        "voluptas eveniet talis",
        "coniuratio degusto beneficium",
        "tui contigo adfero",
        "strenuus armarium theologus",
        "accendo occaecati derelinquo",
        "vulpes appono spectaculum",
        "alius arx voveo",
        "timidus caries tyrannus",
      ],
    },
    "vehicleInformation": {
      "brand": "Polestar",
      "displayName": "My car",
      "evDatabaseId": "1234567890",
      "model": "2",
      "modelVariant": "Long range",
      "vehicleRegistrationPlate": "ABC123",
    },
  },
}
`;

exports[`mobile api smart charging module PUT /smart-charging/delegated-controls/:ppid should return a 201 on status on success 1`] = `""`;

exports[`mobile api smart charging module PUT /smart-charging/delegated-controls/:ppid/vehicle/:vehicleId/intents should return a 200 OK status on success 1`] = `
{
  "createdAt": "2024-07-31T12:34:56.789Z",
  "delegatedControlChargingStationVehicleId": "adc6e152-1778-4faa-b71c-194af592f23c",
  "id": "93e7e441-8508-4b79-8cf2-b786b4b46304",
  "intentDetails": [
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
    {
      "chargeByTime": "07:00:00",
      "chargeKWh": 28,
      "dayOfWeek": "MONDAY",
    },
  ],
  "updatedAt": "2024-07-31T12:34:56.789Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 1`] = `
{
  "actions": [
    {
      "data": {
        "applicationId": 8471833427301458,
        "loanId": 6290876413928966,
      },
      "dependsOn": [
        "690c885c-fbc1-4d04-ac6a-f08e4a9beeec",
        "863a5896-86de-48e1-b18f-e9676eb0e33b",
        "3d1766af-9389-455f-b5be-8af0eca0599a",
        "22aa288c-8ae9-485a-a355-fc5ba29ce0c3",
        "91b7fc13-27d8-4741-9253-798032e9bc62",
        "401116fc-0cc7-4384-8851-a80117999501",
        "eaea90dc-d4ec-4eb2-8190-1fa9ccede302",
        "919d38a3-f121-42a9-bd5d-8c70d9ec2c59",
        "fb2aaec2-a179-40c1-884b-a2942eb1d06e",
        "a676e60e-c4d9-40a1-befe-ccc05f5ad993",
        "bb985c26-16e6-4f5f-bcf3-52beb9b5f80b",
        "030ecc7a-f730-4252-8f9d-de0abf8ac16a",
        "ea36a01b-5342-4283-a411-57000c8aab2f",
        "d3ce2a54-2b9a-410c-ab3e-25531240c91a",
        "40874bb7-687e-4941-b5fd-14750b5520c5",
        "64babd26-0b54-47a1-9f6d-38e69ec7a218",
        "a60f1e5e-50b6-41e2-ac1b-470fe6d51742",
        "766fa9fa-21df-472b-9bfb-4135a5c9d340",
      ],
      "id": "1305cd8a-89b3-4e6b-80ff-c40c40c8e424",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "73722400-f0cb-4625-a290-7b424c1829de",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "0eb68b14-7a6e-44b4-a0b9-71c4f71818ad",
        "0824db58-7943-46ca-890d-2cab9c3d6870",
        "3f4dd511-7f7c-42b1-aad0-c015920c7192",
        "621791d3-4b88-4ac3-8aca-7a85d99e0d89",
        "9275b3f2-c8e0-4ee7-8250-a5adcc3d21c2",
        "26bff8da-ecbd-4714-957b-de928549ab78",
        "afaeb3f6-2add-4cec-936c-096d1097e3eb",
        "dcac9f81-3e3d-4383-aadd-c095f7bbc483",
        "3650abd4-6f3c-4088-8b1c-ef873e2bbb08",
      ],
      "id": "287497b0-d898-4854-b8d4-95ebf3077d23",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "4960df1e-6ca5-4ca4-aaae-9aa24eb89027",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "7235b45f-4b26-4f6c-81c1-47d9239876d7",
        "4ef1dd39-f5fd-4086-9beb-f59bde0ce3bb",
        "8602241b-2027-4654-bfb1-64c981307c70",
        "34e4a13d-6a19-497f-9c53-d37df9f117a1",
        "056c4d46-ce65-4895-aa93-30e3a9a837a8",
        "9230decc-236b-409f-9945-9b415c0bb3a4",
      ],
      "id": "3ece8ba9-8103-4b8c-83c9-b19e8e696470",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "624aca68-f68f-4e5f-8545-ccf96efbb95f",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://decent-suv.net/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://flashy-disposer.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://lively-flight.net/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://coarse-tomatillo.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://little-jump.info",
          },
        ],
      },
      "dependsOn": [
        "c5cad011-9204-4f7c-a4cd-be4947335f43",
        "0260b73e-47fb-445e-8f5c-256ac29ad983",
        "12882546-0bb2-41de-bd6b-0987400f3da6",
        "5e011373-b5cb-4a58-a19b-94d72cd5a20c",
        "d27d53fa-fec4-4aed-815d-d63ff84ca7c0",
        "3e0e277f-82b0-4399-9ffd-7226e7bff322",
        "bbec3885-56d4-4a27-bf2e-38ab44eb0fd3",
        "079f47f6-7f2c-4db0-a44e-3a7ce51b64ca",
        "d6b4a19a-325f-401f-aba0-ad78b5900f19",
        "003d1041-6fab-48a0-922d-48dca6d37662",
        "ab2cecf4-92d2-4837-9eb2-8906884613e2",
        "ab15057a-cade-4cf9-9a47-638c8fceb4b5",
        "dc3d2994-3d6d-4235-8f5a-50e5af81bef9",
        "8cb0ee40-590d-42b2-8c55-cff829b45207",
        "9c5e8a21-76b3-4586-abdd-43338eeba498",
        "837e206d-44e6-428e-b9e6-34ae00e783b3",
      ],
      "id": "4fb76508-2cf1-489f-a7eb-4b1ebdbc06e6",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "01fe39ed-8c35-40e5-9918-05a3cb667e02",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://livid-napkin.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://mindless-accelerator.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://direct-going.info",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://equatorial-digit.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://grounded-numeric.org/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://secondary-distinction.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://cuddly-hydrocarbon.com/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://fatherly-knickers.net",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://extra-large-cauliflower.name/",
          },
        ],
      },
      "dependsOn": [
        "fe62e341-7b3a-4c2d-8b1f-cc702b837229",
        "9d512fad-9153-4455-8de1-35f0c1e053ea",
        "394fca0e-ad30-40f5-a7bc-885e253a8453",
        "0ff5fbdb-3f84-4a12-991c-cb99670fb3cc",
        "84354696-57ed-4fc3-a14d-c10a6ff775fd",
        "6aa9003b-a1de-43e5-b662-28fb703cb2b0",
      ],
      "id": "89163564-68e4-46a8-a784-7ac7d78a1a31",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "636ba7a9-0885-4a7c-9ec1-9bcf39ae0407",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://near-toothpick.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://frequent-litter.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://menacing-recommendation.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://grandiose-extent.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://lighthearted-injunction.name/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://ample-flame.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://scared-seafood.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://ill-sanity.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://wonderful-affect.org/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://sardonic-topsail.org",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://remorseful-experience.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://tinted-desk.net",
          },
        ],
      },
      "dependsOn": [
        "eda76982-cf95-49a8-a97b-16bdd53a89e8",
        "70b61c8b-6b6f-4707-9386-691b5fc1354f",
        "fd5e6eb9-2e5e-466e-b534-e48c65b1a71a",
        "369dc61c-373f-4933-b409-6df2478bdce1",
      ],
      "id": "dc4bf7a0-9e68-4e71-8217-6e0700cdecb1",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "134a5446-52d8-43b0-bd4f-6b0a9e43c651",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "applicationId": 5498869033097488,
        "loanId": 2921504156615307,
      },
      "dependsOn": [
        "13f3e04e-2570-4e39-a320-05a00009fd83",
        "d4e2e57e-a36c-4889-b8ff-b9839f77a3ad",
        "6b3e9dbb-df78-4d10-bbae-c9eefa2224f1",
        "f3b99b11-12e7-48ad-ada7-00b08e03923e",
        "99db1571-7e2d-4795-949d-e2d5f69660dc",
        "0e80d8a0-28d7-4440-81f5-6007a1ebc958",
        "0ce9db2c-0b3b-444c-abc2-c8221a4f8bbe",
        "04882688-5e03-4b8d-8d24-9a6b9c6b5ae9",
        "eeafd9ee-a476-461b-83a3-4e3655931957",
        "f2e11ed1-d28b-45f5-bfee-50fe5766622b",
        "372ae663-e7b1-46e5-8466-8e775b98e4e9",
        "7211d533-717e-484c-bf9c-57bcf4fb5ce7",
        "489bb0c5-00cc-4c8b-8f01-f757ae2228fa",
        "6ac07cdf-1fc9-4654-8d06-8531fb1c78d5",
        "952900ec-b04c-4513-bbd2-2b13e575f30e",
        "3cfe81b6-9111-4d5b-a806-74811f897ada",
        "c5a5d610-3609-4b28-a4bf-35dce70fe22a",
        "4021ba56-4f15-41b4-aba1-f872c2606552",
      ],
      "id": "7ce23a0f-2944-4ce4-9c97-f449047c3dd0",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "9428a62a-df71-4b63-b1e3-64c07ecca2c9",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a98cc1f2-301d-426b-8660-67b732246f4a",
        "156bd3a5-4183-4617-8edd-a2dee0b7a1bf",
      ],
      "id": "dac5e7ab-0f75-4795-979d-de1c0c3de508",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "e2019da3-9e29-4cd7-8810-3077df3cd616",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "ppid": "e83ff444-fcd9-4ce8-a321-a1ec9aae3093",
      },
      "dependsOn": [
        "044f0178-b408-425b-80bb-94d54ed56e6a",
        "3b39b748-e400-4812-ad13-e46b7f0a4dce",
        "311564e4-987f-4221-abb5-9ed9243e4527",
        "1d299ec9-d560-42ec-9954-ef2c4d054094",
        "154519fb-34f3-45f8-a08f-8469984096ad",
        "c31dd51e-6a78-4aa4-9f71-4c57076210e0",
        "0053cd4c-af3e-448f-83d4-bd93914a5a57",
        "2f9c7b1e-ba16-4602-b3c2-32215f4736b5",
        "d61ca38e-f7f6-47f7-abfe-594f23f1080a",
        "1fdf6d9a-d769-48ff-99ba-7b1297894ba0",
        "90f6fb3d-de7a-4818-b291-24e85ec5c52b",
        "ce08c491-762a-4f55-8646-1a2c3134999b",
        "889feba6-11fb-4f5a-a2cd-46d1f2df840b",
        "dde83c33-f970-4672-9d56-22f17fbe3e9c",
        "32487456-37cd-4577-8549-35c57bf61db3",
        "a952df73-48fb-4b0e-b9f7-1fe5622bfb4c",
        "68b385cb-cedd-4555-849e-a02f056e1f58",
        "a5bda0aa-3f75-4e89-8d63-403842bba28e",
      ],
      "id": "fe57811e-8824-48e0-b95c-186fd820db7c",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "c2660899-41fa-4a92-be43-c50d814cece3",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "applicationId": 350404173427960,
        "loanId": 8837320388183252,
      },
      "dependsOn": [
        "ce6468ca-4c87-4edf-9d05-fac02e29bf0a",
        "19d0610b-43db-46cd-9cf1-7fd24a553bd6",
        "a534ed56-e592-455f-b45a-d6283683abf8",
        "7d417f14-d99b-414e-9706-2693b27fcc1e",
        "87e9a31e-95d4-452d-84de-d4e6fa638419",
        "bbdd5c01-5958-4d8e-8e96-c60a3df4c150",
        "9b0a5d47-5dae-4253-a1e1-424485583964",
        "d5c5f364-b677-4130-a1fc-92c6613a97bc",
        "7a45b2b5-57b2-4f68-ad2d-9979b467bdb5",
        "24153436-8aaf-4042-9530-f3fc473c74eb",
        "28b04b6c-f184-40c7-af53-e7e3b7a3a59a",
      ],
      "id": "b8917a70-a0dd-4d09-b1e9-06154874550f",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "0522a96f-16da-40aa-8bd3-559569b55a02",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "9a1a7da2-3151-4a45-b83b-da3228f21e46",
      },
      "dependsOn": [
        "6759c179-0070-447d-8ddb-4db93c8b9c92",
        "4d8d507c-b033-4dbb-9398-66d852dab4bb",
        "29995777-83a6-440b-9279-06ba08e305df",
        "a45143c4-a1a8-4d36-970a-7e364c544a3f",
        "4a3dad99-f69e-4bad-979e-6700cc00a055",
        "1c530e9b-a478-45ca-93c5-b35ad1a007f5",
        "faec2a37-f04a-4709-97d0-8085e447bbf5",
        "e65d10f8-fe2c-442f-9d52-ac56d2d2dedc",
      ],
      "id": "d5a43eb2-d75a-4801-8b20-b6e052c4c403",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "59866b5a-a382-43b4-8b19-cebd4bebc788",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "aac39c25-ceab-4bbd-a612-c223c4fdb030",
      },
      "dependsOn": [
        "4ffb30c4-d3a6-4c62-80de-f834b9fce0cd",
        "8fecf99e-fb20-4ab4-ac26-7938d43032e9",
        "a64c05a6-d849-442d-a7ce-86af676b8e0b",
        "5b97b611-7417-4062-8a08-b9f89accbd89",
        "14eb6f9f-ea5f-4fec-96a8-81b5f8843f45",
      ],
      "id": "9fe8ed73-b71a-4d9a-b849-b626f70b2817",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "bc5e832b-1b4e-4346-a7e2-b397cb64f22a",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "921c6adc-12c3-4165-9105-e370e60ba97c",
      },
      "dependsOn": [
        "14c8e265-b8a4-422a-a3b0-05c500320981",
        "c8288a9a-619a-4e50-85ac-dbff994a805e",
        "be537c43-0582-4ba9-aa8d-3eb889c97ba8",
        "f2c177a6-f337-44ae-91d0-eebf057da42d",
        "117f8003-e689-4b53-83b1-64e33ec3ce48",
        "b7ad0d49-b31d-4361-98fb-863948c9cf43",
        "75308ce1-889b-4385-b89f-5bc0aa156a70",
      ],
      "id": "d7993132-9a45-4412-a9b6-ff36ba74b0e8",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "7d467b5f-c348-40da-ba13-cff6f031fb3c",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "ad1dbb3e-c593-4a3d-b0f0-3ab6f0cecbaa",
      },
      "dependsOn": [
        "2e2d8817-df0c-4278-8ba0-e62e5b56609d",
        "ea109153-1b8a-454c-bf08-b20148b3263b",
      ],
      "id": "b00510bc-77fd-4981-be8a-d2bbf599e650",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "9fbb8101-f882-426b-993d-f6e7a64a85d3",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "6d3c69b1-4439-45e3-9dcb-9ad05f5d646e",
        "039c1c3e-4a4b-48b9-95d9-9eabfa968846",
        "01dba839-a90e-49ed-bc84-8ec6007b0876",
        "b92b7461-90de-4cf2-8b56-570435e0da6a",
        "329ef172-421d-44e4-afd9-ff0b5255007a",
        "01021378-5d2c-4188-a140-a9c2eff1de36",
        "1fab8290-1652-4ed3-8607-4d1aaea99279",
        "5e17dda5-1511-4841-88ca-b5eb12b64a2b",
        "d3caf4d3-ddab-4360-a59c-5b60d17ada17",
        "1dcde3e5-80c8-4048-a278-76d179d0dfb7",
        "3862ac82-84e3-4e39-a83c-9cc2397bc752",
        "0e7e062e-e3cf-4603-80ac-1746e4f0287c",
        "5c81a092-e969-46a1-ae3e-bd1d319b269e",
        "2991e421-8610-46b2-95a4-598554b60782",
        "e2185972-4af1-4444-b5e6-f8d382102055",
        "7d82ea69-555b-4ea7-a105-1164b9e256c4",
        "45b32a15-4655-4590-8c3c-5570491e9442",
        "564499d5-0dcd-46c2-8eda-87c1719f5941",
        "3cb0daa6-6c76-41ad-84ee-a74df4bb65e1",
      ],
      "id": "3f4c847c-0b83-4652-8dde-d69e502d6cdb",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "6c531c6c-b2d3-4ea2-8d5b-ec5680d6d268",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://orderly-bump.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://advanced-place.com",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://posh-tool.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://general-submitter.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://definite-markup.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://quarterly-majority.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://taut-filter.com",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://misguided-scout.com/",
          },
        ],
      },
      "dependsOn": [
        "17e2ad85-55b3-44e7-82ad-47e78a4d3f1c",
        "6674c598-cf72-4f0e-a9d3-258ccc8a198f",
        "f46ec28a-39dd-4d93-b055-3d50f53e940c",
        "152569ad-ba7e-4191-b965-b95531f98684",
        "2c20b2cb-d832-414f-989b-f74a525ac587",
        "547c4dba-9690-41e9-8901-dd806e706b55",
        "ae5b329d-29d6-425d-aa5a-63a608429562",
        "3a244caa-65a6-43e1-8cf9-b86e3e6c2e2d",
        "1cc404aa-aa74-44fe-80c8-5ed6bccf88ea",
        "8865a7c6-0e2c-4a82-9ae3-5b89cc26b5d2",
        "f75ae2eb-e49c-4556-9a98-7693b0345b4f",
        "ca7fb71a-8a1b-46fd-ac05-88d443ec673b",
        "8667a482-3ad9-4a24-9b1d-845a0f6dec90",
        "1577251f-7968-4da6-859f-02f0e67d7e32",
        "0a7ef9ad-e01a-49b9-bfbc-4720b703d1d8",
        "402b217b-f4a0-46e1-a077-0b359d9e002a",
        "3256c9c2-b69e-4779-87f0-58bd3f4c383c",
      ],
      "id": "2c1877e9-fab1-4222-8eb0-5b6bfde3c1c5",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "496c8821-32ad-4426-818b-5836753f34a5",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "c7917f86-257f-475b-875a-b593e6e293a8",
        "4130d582-432d-437c-a608-d9b0c4b503c2",
        "ec04b184-4116-4ed7-af84-97368cf9b8e7",
        "4ac1a853-ef7b-4cc1-9433-f063fe759505",
        "0825c49c-7373-4e2d-8a44-015edcf51c35",
        "62bd35bf-8224-46da-aa7a-376f50beb612",
      ],
      "id": "6f266399-e4cf-4338-a7b1-9fe1348e5a38",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "c1c3a7d1-8e14-4050-991e-057bf8ce43e9",
      "type": "PAY_UPFRONT_FEE_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "2164d103-6999-4d11-913a-4442d6f13234",
  "order": {
    "address": {
      "line1": "uredo canto aggero",
      "line2": "carus amiculum caritas",
      "line3": "altus sapiente crebro",
      "postcode": "bellicus ducimus avaritia",
    },
    "eCommerceId": "c3bc7d77-9006-4e7b-8db3-d1fdca0eab02",
    "email": "<EMAIL>",
    "firstName": "Joey Conn III",
    "id": "9f2a8178-0f30-47ad-9bab-7225aea9ee22",
    "lastName": "Gary Ratke",
    "mpan": "tenus conculco apto",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "titulus tamisium ad",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "7fd9cc35-24e9-41ce-a4f6-c8d40ed81130",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "ea nulla ventosus",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "REJECTED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 2`] = `
{
  "actions": [
    {
      "data": {
        "surveyUrl": "https://creamy-quit.net",
      },
      "dependsOn": [
        "bccf7256-96f8-4262-a826-05aae356041a",
        "08431146-ac88-4964-9d9a-69c7fc27d799",
        "2feb8cdd-17e4-4de1-8b94-ae319e85f116",
        "57738c2b-397c-4cdb-9f7b-d3d579f61aa7",
        "2d89a60d-6aba-4431-b159-7f8a97d635b6",
        "95760580-dc71-4d8e-b29a-f311902faccd",
        "1c62f4ca-9a1d-4601-827f-642af077016c",
        "d9f5c54c-599f-4f15-ae36-735cbab95898",
        "bb6fac49-0382-4366-8745-b9e36ab1bdce",
        "2898c2d9-953f-4294-86dc-a948a60cdb53",
        "e126cc92-0b64-4ff8-b637-fcefa3bd007b",
        "d086bf23-e63c-4a77-8e3a-84b20e01934e",
        "87ca6759-3772-4779-bff8-ec777035c21b",
        "f60779b6-1260-4e86-8ef9-07a5715e28b4",
        "2c797837-f09d-478d-bc9b-cd5476285b9d",
        "3a6de94b-f4a5-4fd5-a8f7-5b20bbfbf208",
      ],
      "id": "7b5f9943-4155-44f9-8e0a-78587490f2a7",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "c7009ce8-f9ac-4b4c-b1c5-502f29741fd8",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "5d073cf3-3b7a-4af6-b993-9799d039fa2d",
      },
      "dependsOn": [
        "df34b0f7-1100-4af3-a9f5-c43d2f6d2654",
      ],
      "id": "af51f85b-6ad9-49e7-9c52-be57bc198245",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "3fd57ac5-d00e-4333-b286-e04bc23d8ac5",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://pricey-descendant.biz/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://husky-deployment.biz/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://scratchy-deck.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://moral-formation.org/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://sore-siege.name",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://muddy-incandescence.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://haunting-ribbon.com",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://nimble-technologist.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://possible-intellect.info",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://vague-testing.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://livid-coil.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://remorseful-nun.com",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://honorable-metabolite.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://guilty-operating.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://favorite-fowl.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://concerned-sport.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://shallow-relative.name",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://astonishing-yeast.net/",
          },
        ],
      },
      "dependsOn": [
        "ba7c6bd3-4660-4226-aaac-ff5b2f25b382",
        "f6b3d267-84c0-4548-85c3-49cac45f34d0",
        "8688ee07-0d96-4031-863d-6ab1b3995855",
        "a5dab005-038d-43e3-8228-d33f53be8779",
        "75c6fe9e-4d52-435a-8177-ccaa6fb93e9a",
        "50f401e9-2cd1-4c3f-b019-5b8af8f6301c",
        "12894272-54bf-428a-a237-4554d5e5a437",
        "d4968257-1e1f-4b8e-90cb-db0ce98c833b",
        "5aaa64aa-7982-4739-8223-de99a73e2be6",
        "19cff627-d0c8-441f-affc-b0a1854cfa06",
        "1f867fb7-4fcc-448c-973a-3ca8e3df4d41",
        "a67b0c73-a3b9-4291-9c12-a70e2e4c5dfc",
        "67961a97-60e5-47cc-b22c-54609752f32e",
        "fe3de43b-5acf-46b9-b047-dca80a546484",
        "9fc39013-3438-4899-bce1-9b3f1f4369d4",
        "a0488435-a338-4f5b-b31f-21404b5be6af",
        "5daa73fa-c81b-4e4b-a780-54b4b43c2e15",
        "0465d513-4e8e-4018-a80a-2c53fe0a3c3f",
        "f625e14c-b96d-4586-bd57-a83dcfe16d6a",
      ],
      "id": "c267d75d-40fd-4190-a591-f4a68843f63b",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "b83ac8c2-a73a-4d17-b892-c04ac2821196",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "applicationId": 3408364229401079,
        "loanId": 3610433412692520,
      },
      "dependsOn": [
        "1d53d553-330c-47ad-973a-cf909b3866f8",
        "01edd9b2-01aa-4b0b-90ec-51926ae2b62f",
        "466f813a-b983-4b5b-9c27-f64429a10ad4",
        "8b9ecd9d-d208-4cd0-b9db-0eeec46ad580",
        "f48b9fe0-ee0b-4fab-8f39-a92adda6ae47",
        "17e1f0ca-107e-461a-980f-5e8edae0eab9",
      ],
      "id": "55cbc227-08f2-4a4a-af56-c710b6dfff07",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "52531acb-49eb-44d2-8e64-fd1dfe07e936",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "dc6d94aa-1602-4400-af92-f3e835365cce",
      },
      "dependsOn": [
        "037cd55d-3918-4951-a444-9a5c2d681f58",
        "d9381186-f552-46b6-9b26-7f5a9b3f9ab2",
        "57d9e075-f174-4804-bec0-988acd665653",
        "8d9a5d99-5294-4dab-bcfb-df98773167f1",
        "a5f8ad44-7d6b-4f1d-b505-64f9b6409460",
        "fb8bcce3-4e2d-4fa1-b14e-d7e9a80d36f5",
        "965fcd23-99c4-4e3a-a058-a08264da7b92",
        "8d462445-df68-49ed-94ef-1cbbfb11c640",
        "9ce43ae1-2191-4170-8b18-afb0582f7418",
        "9ce1fcaf-2287-49de-ae73-d3936eacaf06",
        "d643cf3a-9813-47e3-8e35-ff87267c0ded",
        "c8109e62-e7b8-4fab-a9ff-47406d1bc522",
        "df14d4f2-3de3-451f-b05e-754f9b954eb8",
        "772b94ed-39a7-49e7-bde1-e983301fc989",
        "0fa9fdc9-be74-4bf7-9977-22e6c1b9fdc1",
        "81354c3e-ef96-489c-9b1d-51f741ad10f1",
        "2601a5c6-c049-4d77-a401-7d2540c588cd",
        "96b32e19-5e33-469b-ba8a-d2ba67ccbb5e",
        "10ca37a5-6885-4611-bba6-6d1dc03d615f",
        "0f6b3c0e-2687-4944-868c-6860661b6672",
      ],
      "id": "9fe9ed9e-ab87-4ee3-bc16-c1d41620eb66",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "71cd72b4-10cd-4e28-be1f-10090bae5efd",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "bb1d9a42-7872-47ab-83c1-e8f0d22ff3b5",
      },
      "dependsOn": [
        "69ee410a-5b3f-421c-a05b-7931838ca0aa",
        "a4e3176b-7200-4859-881a-8679f836170c",
        "d41d4e56-9f91-4558-b755-380c60c24968",
        "d5635e96-c7e5-4fe1-bd78-22dd7f1d8c37",
        "aa58588e-00ca-48f0-81d1-84451b67883c",
        "34f338cc-c72e-41f4-895f-bb2590e511a9",
        "5e98598a-291c-43ba-aca5-0e27887dd26b",
        "1f529a2b-7431-42c0-ba8a-a4e09167959d",
        "817ff8df-1b5b-4984-b67c-f33330900bea",
        "89dfd6bf-a6f8-4038-b86e-0522db585546",
        "95294b11-bb8d-4b7c-b51b-283923bdbe75",
        "07a43c5a-fd97-4729-bfaa-49f6cd9efe24",
        "81d5ce23-5f71-44ee-abc5-6ab41bae0201",
        "9e69be45-526a-402a-b061-c1938369d11a",
        "6afe8d22-8add-490e-a472-84931cc32661",
        "904cfb27-72a7-4920-9ce8-d3240264df30",
        "2754a87e-e8c0-475c-8f22-a91e375ad50a",
        "a121debf-5639-49d4-bfb0-41723f359eba",
        "60277a3b-3e04-4544-82ff-ed2d95590eff",
      ],
      "id": "6cc76803-ed5b-480b-be4f-bd688d3e47e8",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "a9914c78-87c6-4086-b84b-966dd2197da1",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "f242ff41-37bc-40e4-8df4-ecf59aaed681",
        "bb7f8ef4-42ff-4d99-91e4-05b977c104f0",
        "37d71eb6-7874-445e-a881-6b92313efce7",
        "16880479-5910-4519-8a5d-4ee3abcd58d6",
        "34401d7e-15b7-425d-9189-91c6e5df2c2a",
      ],
      "id": "87003819-e1a5-47d9-8047-8778f5e6b6a8",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "dca38a48-b016-4282-b47c-b1376e2944c8",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "70c15704-f0fe-43c0-bdc7-b73b0e155c95",
        "d43a5c3a-890f-464d-88b1-9cf72cb73ef3",
        "dfb2b187-59b1-4c5d-95db-7350b373e010",
        "7b968a52-cae5-4e89-8f10-75a0ad0f8ea0",
        "f314888d-5e3a-4b25-8276-ff49aeaa0ca7",
        "70d0d14a-ca4b-4946-aaa4-6723a27d5bd5",
        "e8d09758-8090-4e40-b4c0-ff3a8c09ee22",
        "a360fe0f-7582-41f1-a819-ea04b644b3f2",
      ],
      "id": "91d669f0-dd91-4cb5-a0c9-d31228d47646",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "a6b9bb18-e653-4bd2-90af-f3c86e1e52d7",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://well-made-responsibility.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://joyous-deduction.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://worse-parsnip.net",
          },
        ],
      },
      "dependsOn": [
        "0b7132f5-08a2-4a2e-9ccd-a0e03f7ab6ad",
        "b64ac76e-63a5-4695-9c44-11958f0658b1",
        "aab019a2-58b6-4a9c-b46c-8bbb36d93ae1",
        "e6337f6a-ec9e-4e65-aa37-edcb0a3659de",
        "e96033c2-244c-44b2-baa7-441834ccd4cf",
        "ef6b55bb-21da-4eff-81f8-d708da1b06ad",
        "065eccf7-3629-4702-a0ce-b5c50f1853e9",
        "b1b06140-3ef5-46ec-aede-fca3201466a1",
        "00c78bf9-96b5-48e0-8381-af88388ffe37",
        "8ef9235e-2772-4546-8f2f-2a1426a8fe72",
        "e3b5ade6-68e3-4620-9420-e6dd92c1e7df",
        "2b4ae603-f30a-415a-92da-0028e800775e",
        "b6908ecd-12f5-4461-89fb-f95988390906",
      ],
      "id": "02acb681-b230-4080-8c5b-c634882a2be8",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "50535e90-9988-4224-b0e5-8935f41c491c",
      "type": "SIGN_DOCUMENTS_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "9c25b565-2e63-4adc-acb1-c2ad0469a4fd",
  "order": {
    "address": {
      "line1": "suus abutor desino",
      "line2": "cerno ambitus tego",
      "line3": "bos velut vester",
      "postcode": "vomica suppellex eius",
    },
    "eCommerceId": "b8554186-c2ae-4969-b2ff-420644283245",
    "email": "<EMAIL>",
    "firstName": "Elizabeth MacGyver",
    "id": "c7b749c7-1df7-4e49-8f7e-1523617cd231",
    "lastName": "Dr. Herman Bayer",
    "mpan": "vindico amitto bonus",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "minima temeritas at",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "a2f36630-3262-4aa8-9971-25c1ea251d53",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "thesaurus numquam magnam",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 3`] = `
{
  "actions": [
    {
      "data": {},
      "dependsOn": [
        "0e9d8bc5-1b3f-474e-acf4-e66a43f08d41",
        "140236d7-99a6-418c-a58a-a8c75998ff66",
      ],
      "id": "e450657c-ea59-416e-a344-8e3bea3052a7",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "18e00317-9e11-48da-aac4-fdd6d8cb9469",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "surveyUrl": "https://other-minion.org",
      },
      "dependsOn": [
        "7f6874b8-09e6-4d5b-850a-6d00eb1159e4",
        "6c68ff55-41b8-43c9-bbd3-78a5f797bda3",
        "1939ec42-acc8-4ba5-941a-eb8f9c658de6",
        "45e63b6e-8f9e-4826-8421-1230a1342071",
        "f66c71f8-ffc8-4491-be4b-6751945cb781",
        "ef97fe65-fb88-4c5d-ab35-fee0b52a2a13",
        "7caa6030-2b7a-4067-aa8d-88de86c872d0",
        "a6d0cd76-cc68-4eb5-95d5-f749dd3b5181",
        "579519f1-279d-42eb-9e36-1fd29adf5e39",
        "1501fa52-9462-49b4-b776-f223c6de965b",
        "307366e4-4c4a-452c-97bb-4a30b1c6e553",
        "d8077d2e-d820-4fc3-aaa8-f2c011f4504c",
      ],
      "id": "9f041db6-0378-405b-be7b-f5fedb416d0b",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "2a99f400-056b-44c9-8be7-59ae105e0820",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "applicationId": 2240565443653431,
        "loanId": 3250415578114200,
      },
      "dependsOn": [
        "5f630385-b57e-469e-b6b5-1e388a5cef21",
        "4ec0993f-2dce-4416-a594-1fe7bb88a3bf",
        "ac568956-20c2-4a18-bb1b-e24513af7f93",
        "f08ef317-26f2-4c1a-a11c-36f6e8bc1c4a",
        "682f583b-11f5-4558-b26c-cfe2cfafcd88",
        "ebe9e1be-8dd2-46be-bf09-c8345addac5d",
        "185b16ab-4c88-45e0-83a2-991f3e83bdd6",
        "e4248256-35e2-4a02-912e-eb8f5950cad4",
        "1206ccf4-b7ea-4fca-9e11-cfa9143e65b6",
        "197533f1-a035-4823-adf4-e230ab1f06ac",
        "2a73f7f8-049b-4195-b80c-f0b5f33d14a5",
        "d89550f0-4c9e-4e37-8672-9000f5f66f21",
        "f688dc7f-f664-49c1-b4a1-520773ebca53",
      ],
      "id": "e43a0c9e-5865-4c06-848d-51ffe64b7986",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "b46cf56e-0935-4f90-8b3a-c0e80042a60e",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a253b0cb-a28d-4f38-98e5-b65171eb2532",
        "d57cf63b-9fd9-4926-830f-a4c84b4b4f25",
        "8d9b0b8e-c937-4127-987e-2c242acdf4a1",
        "41aabd48-7d9b-4a5e-ba01-e1e2b4f43169",
        "2910e2dc-0c1a-4002-b262-d3f53def10dc",
        "d50df83f-8fbc-48a3-a93c-253d592657cd",
        "90a97db9-ea04-48b8-8135-432fdb65d3cb",
        "759ed64e-3993-43c1-ae4a-44c35f04c355",
      ],
      "id": "8a4adece-c105-403c-8383-f4576fec333a",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "4e9804cd-eb4e-4678-8748-eaa595db6bcb",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "4706c350-743a-4c20-8636-a11501157c7f",
        "d4cb8c39-3787-4339-af24-cac742345b02",
        "3382e816-1e65-4db0-8f59-30b47292e07f",
        "b806969c-35b7-48ba-acd9-80546638ae0f",
        "ac259e54-42f4-4a02-9d54-6e74393057a4",
        "1a578960-3ed6-42c5-a86e-a7b9996bdde2",
        "31a9b735-be71-415f-9e36-77656f25f616",
        "5d74c24d-2212-4733-9ac0-bd31617256fa",
        "10121a09-b173-4a89-b6be-dc57332af3e2",
        "59cb78f9-6c1f-4939-a431-2efdcf156857",
        "39a1cdd8-b10e-4fe9-ac67-f55b16113f2f",
        "d9b52732-bdaa-4815-9c3f-b9b7641de0ec",
        "dc26352e-121a-4d03-9620-d09a9b001bf5",
        "841a21c5-cb02-4817-ab73-7181e0b2db4e",
        "e47a2b7d-1478-4f16-aa34-59aa32b18831",
      ],
      "id": "f00374dd-ba94-4a98-9c4c-ee4a80462316",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "279dd3ba-2f97-4048-a11a-4285bd5bd50b",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "ppid": "0aef69c5-9144-43ac-8eb7-d11187f86151",
      },
      "dependsOn": [
        "d38c655b-8049-4a0e-b7ca-5559c2be6f68",
      ],
      "id": "be063196-0966-4a32-8328-b434e4b72ffc",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "f46ea70e-7a08-4374-bbf5-79525cecbf7f",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "2fdb77b5-e294-40e0-be1b-2220dd650b45",
      },
      "dependsOn": [
        "ff435f4d-b84b-4c87-8210-bf0674d9df4f",
        "d647bd8b-d9a0-4469-ac6a-01ab6fb758ef",
        "00eb247f-cac4-40e1-aca0-a07c4e0dcb4b",
        "72654ae3-2066-4c8a-bd81-e0897e5f4786",
        "c964c46c-6f1a-4a68-aa39-e994b1dcb8fb",
      ],
      "id": "3cad9eec-afc6-427c-9f5a-7ffd292f2384",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "5d9e10ab-b028-4ffa-bfc1-03481d11fab3",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "4cbf57b2-3865-402b-926d-f77d5f00c1dd",
        "30e4835a-be3b-404f-90c5-546c47934c68",
        "4979dedd-e865-4ace-9394-ae9cde93cda7",
        "50d9a62b-7361-467b-ad37-fdd866c7efe5",
        "9faa2e6b-6bf9-4ba8-baf9-902ae39d4055",
        "1fa71de3-4dbe-4a14-8c04-2d45ef98d4c6",
        "5ec43ef2-c1b1-4c39-a1e7-c3952d5f32f4",
        "196bd105-eb2e-4610-8a70-a42298d49cfe",
        "38361636-6a3d-4d77-a89d-ab509c5eb1ce",
        "a5e2efa1-ec67-41f6-ace3-aa5110422c71",
        "cdc12020-0994-47bc-8105-5d28beb102bc",
        "6e975302-df91-4d82-8743-4bfd8738e179",
        "e40f524a-6cda-42f2-8c37-7405dc2c4e04",
        "db76d4d6-3945-47e6-9a6d-65e7df3760c2",
      ],
      "id": "67ed40b3-a2fc-455f-afac-b79e506b211e",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "1eb2413c-74ce-4c3d-a35a-7622c0d953ab",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "surveyUrl": "https://insistent-pasta.com/",
      },
      "dependsOn": [
        "d7611027-a1b1-4fda-ae77-8f39aecd8fc1",
        "f42d76f4-7e65-4524-8303-6e2d1b795bff",
        "3e849e1b-b737-4b86-9776-59dc55c8fa6d",
        "e231ed4d-a949-4299-9e85-27012015ad44",
        "6d61416b-35a2-40bf-9554-e17f41a91667",
        "de0e6d18-7eec-463c-b516-890eff45c606",
        "3d576b37-7472-4c5f-90f4-6d722e2c3852",
        "5c51b582-d1a1-47e3-b943-a44fe797a4e4",
      ],
      "id": "e8da40de-d276-494c-89cf-20e43c089939",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "5135864a-a9eb-4df2-80ff-4f4be976ca80",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "914a6b3c-f735-4f56-9633-b70c1c5b09b4",
      },
      "dependsOn": [
        "44b1e55a-f884-4157-9da9-56fc889ac98e",
        "dfb40cce-ca24-44e1-9437-a34852bf3b46",
        "5697eff5-9005-4a2f-9240-99b165520d32",
        "4763f5a6-52c2-4925-a7cc-97bfd7db5990",
        "61f660ab-7991-4665-8ac2-236176366993",
        "29b22688-a0ba-4de1-b8f3-ca05c3216400",
        "af246d3d-812b-423c-b821-9d73fd4fc72e",
        "3c65d295-f591-456a-a278-8de42b2694ec",
        "867d8b6b-8078-4e48-92e2-d67b29f1ddf9",
      ],
      "id": "fdd5dc25-a2fc-4e74-90e7-78d1714e377d",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "7015fddb-2359-4b46-9b6f-1b1fbe7e8e27",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "ede5ee64-447a-4466-82ac-c5d1ea7646b2",
      },
      "dependsOn": [
        "d5672931-9718-489c-9896-be0e541b18bb",
        "b5aca218-005d-4389-8e2f-5f1a734f1446",
        "377f97dc-7763-4e4c-bcb5-a1c02fdebbae",
        "d062aa0b-7cea-4a89-a6f3-74f91cd633c4",
        "fc745eb7-5cf0-4cec-9d67-9bdd672f24c7",
        "2bb5a73c-41b2-4c29-a6e2-70be3aeedff4",
        "7283929d-a705-46f8-95f6-5d1c7d50dc61",
        "926cb2a8-2753-46c2-b11a-2b55042d4e19",
        "6f92a652-d4c4-4c01-919a-08f8181343d4",
        "a8118bfb-481e-4321-b3b8-7b61ab8d08a9",
        "7e017731-64fc-4cd1-b4a5-df4f15976f3d",
        "eb0bdc18-869e-4148-a584-811ff99e1571",
        "4d6e535e-1723-49aa-aee9-8cc7bb428287",
        "a71fe602-e0dd-4c51-baed-840cbbce31a1",
        "0874e773-ac84-4cb1-94b3-cdbda575aa65",
        "3d4e5721-34a7-4146-9312-3e343dfa2012",
        "ffd10c6a-e247-43f0-a309-d76d2e970fed",
        "823110df-79e7-4711-b9e7-f600daf27124",
      ],
      "id": "43248a70-e113-4f28-9ac0-8e749346f776",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "edf9f178-b977-41f7-9063-a3482b3935b1",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "77792625-c8b9-4e60-983c-b699c2789333",
        "a505301f-a132-4946-bc22-908e1edceed6",
        "b55aa2a4-4d22-48be-b3cb-1ce2a0be4870",
      ],
      "id": "c8da76b9-0806-4786-93f7-96d6687b5fac",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "e8e29b5c-b382-42df-a5e4-db1b9f84cbd0",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "applicationId": 6846998193385454,
        "loanId": 7042067786567020,
      },
      "dependsOn": [
        "1728f108-47a1-4372-89fa-0ac6cc905a02",
        "b7343059-c472-4489-bdb2-8a23a6a5a827",
        "841336bd-fdae-48b8-8282-c734494f39a0",
        "76517884-268d-4627-bcc9-f5838815b08f",
        "5e8f6064-c3b3-4b2a-a436-d8c87b8bd13e",
        "47a981f5-481a-41d6-b4ae-4996fa0ed52c",
        "341625c4-d028-42be-9e58-0f84ebebbb0e",
        "6b1909c7-90a5-4931-b98a-3564f03e0030",
        "362f90cf-9b06-4453-9bea-89a48ed712b1",
        "e650f12a-ca2e-49a4-8478-e92ddd3b5e0c",
        "422552c1-9ca4-4196-9b19-5fd2a1a17b09",
        "27f7722f-e353-432e-96d8-73289ecec0a7",
        "b500adae-8a9c-442a-af71-129bbd17061a",
        "c7dc5b34-9ca2-46b5-b9db-72add5dbfd3a",
        "26881863-7cef-4de5-84a7-932e4a5edf5c",
        "bf1684b0-d9c3-426c-b6cc-b0762df30528",
        "473a7575-121c-47b7-bfad-a1b8a0d3ea24",
        "d0b0d1cc-e62f-4a97-9dca-8710ea13e2c5",
        "7d6b8d69-b52f-49dd-a5c9-a25a1cfe7e72",
      ],
      "id": "15f6d8c9-f62e-4451-b732-0775d2154f3a",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "2318ed87-8f7e-4024-b215-a093852e7533",
      "type": "CHECK_AFFORDABILITY_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "49696053-8080-4e9a-8c61-e417dc39452f",
  "order": {
    "address": {
      "line1": "catena benigne vox",
      "line2": "rem tardus arguo",
      "line3": "debitis optio nisi",
      "postcode": "adiuvo alo arbor",
    },
    "eCommerceId": "3854e64e-a154-4bf5-8179-6dae561728de",
    "email": "<EMAIL>",
    "firstName": "Ellis Effertz",
    "id": "cdb82aca-5e3d-476c-9b03-2bf49ba6db26",
    "lastName": "Josephine Muller",
    "mpan": "ascit ater censura",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "bellicus temperantia utilis",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "f552c0e5-66c5-4500-bfbe-11a5cc1f19dc",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "titulus curiositas architecto",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 4`] = `
{
  "actions": [
    {
      "data": {
        "applicationId": 1279604572192780,
        "loanId": 890158641076098,
      },
      "dependsOn": [
        "128b038f-c941-459e-944d-1e2173a227e7",
        "0d76af47-4459-45cc-9be9-40c48d26d3ae",
        "a93b2f5f-797d-4a52-b2ba-dde7d26e4e82",
        "86267e2e-5d72-4e47-83c4-605e3804ea26",
        "86189fe7-9796-4718-a98d-705afb02c79b",
        "a923cd7d-428a-49a0-9825-fc31f3667c5a",
        "9fee10ec-3739-437d-a409-aa86423233e1",
        "52ca7c87-9f18-4696-bfb0-dc116f9e1a76",
        "3e81624f-9967-4a19-9784-9fc56d954468",
        "8dcddb83-3520-4371-adc5-f5ad9d695cf3",
        "bfd0828c-1c85-44e5-991b-156210d8dddc",
        "40dea377-b2d5-4b4c-ab23-d062527bb359",
        "2961fd94-a3c1-4859-b6d4-6ad80b3fefbb",
        "a0f0a721-0899-4e10-bda5-0fa26ac41fac",
        "8c4183c2-368a-4acd-9039-709312bcefdf",
        "15a00783-1fe3-47d2-9a92-1a16d0e1d9c6",
        "4cbf41e3-f40a-4527-af21-1f7a9ca61545",
        "d4a75eda-ada0-489e-91cd-47b7e3b05f94",
        "10d93a47-b4f6-43ed-9b80-233315d1dacc",
        "a3c2d86c-60ff-4bf4-bbbd-ff20a9083b8d",
      ],
      "id": "b488ade7-ebbb-46e0-bb5b-325a5dec9028",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "8ae42cf6-1495-462e-9c84-9d77874b398d",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://enlightened-nun.org",
      },
      "dependsOn": [
        "8bf1387f-1ed0-4ab2-b9f2-24bf32a9957e",
        "f56cae4c-eb13-49a8-8e38-0db4477ddc99",
        "f328ccc9-2ea6-46bf-aa1b-5d6908172ff8",
        "5abae90d-d5ec-4086-994c-266f403e4060",
        "aa529ab6-c8cc-46b8-a4e9-6e9ff084ae3d",
        "c966d210-2786-46e0-a562-1698863ef993",
        "a42111e6-02e7-4136-89a6-e23bfd721687",
        "59f94e27-25da-4881-8688-66fc71c76d65",
        "c46bd364-a3d4-464c-b343-1287bed531cf",
        "5b8087bf-1347-4f9f-9c2a-7a28abd23480",
        "1a3275c7-02da-491d-a4c5-fcefe5b1cb09",
        "ed264e28-fd36-467f-a2dd-85c076e71bae",
        "1ee0aa24-26f3-4b7f-b1b6-46ac90810215",
        "e594ff31-d0e6-4f2d-a6ab-0fc623eaa153",
        "109a4faa-48e0-4fc9-b988-97c4dbfc0af8",
        "8f2bc4f9-1da6-4469-8dd9-c8c0b446d06a",
        "556cf283-272a-4835-93d6-1f61bd844331",
      ],
      "id": "5cb9675b-0dd7-45e7-8515-1dc32dabb5f6",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "ab71d962-bdb8-4e3a-bc68-f85d6eae2ade",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "ddf5990b-aebd-475b-b6cf-42e9fbd93c9e",
      },
      "dependsOn": [
        "db6c5b22-5e0a-4bce-aadf-716f094d05b5",
        "84a3a433-936b-4021-af15-31951ffcff71",
        "70c26dd9-20f9-45f7-adee-08c08daec9b3",
        "f12baf4e-7f5b-4a69-868e-0843905beb2a",
        "fe598b52-2643-4657-b0b6-3eb08d30fa41",
        "b6c78241-5207-47a6-9e77-22d1cf21e2e2",
        "1ae6259a-41ba-44a6-ba56-54bb850e5a2f",
        "97d21ea4-2c38-4404-9873-9d5b43b74997",
        "6ce3f72b-8187-483a-b039-1374e5fa04c5",
        "ca3a7fa5-48fd-4532-94af-1d07f46ec835",
        "e54d25ca-bdc9-40e3-bbc9-5d48f2762157",
        "aea929b7-9051-44fe-8e82-3dafb25ebee8",
        "813d774b-1acb-4d9f-8fe6-eeafc2fab63f",
        "ac9c2346-d5b2-46f8-9ff2-b2760b2323e3",
        "3185c139-b7ef-4d49-8e87-bd74a476620a",
        "4e7416d2-4700-447d-9c8b-ebac998c28b0",
        "7f5ed186-60df-4c21-ac22-71c4079b92d8",
        "6b2d1c69-4ec4-42e0-98d9-edd60c68645f",
        "f30701d4-5ec2-4df6-8139-022583eb98ee",
        "1a2e7cd0-948a-412f-b1c3-51fcfeae6a1c",
      ],
      "id": "19633059-78af-4458-94df-f81a69b6546a",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "db5dcce9-bb62-4fe3-bd89-622ecc847a19",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://petty-swine.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://teeming-unblinking.com",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://hairy-merit.biz",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://variable-object.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://unfortunate-quinoa.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://ample-viability.org",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://icy-subsidy.com/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://muddy-passport.com",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://pleased-vein.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://hoarse-glider.info/",
          },
        ],
      },
      "dependsOn": [
        "529679f6-8a8f-4b63-b9e1-8e39ffbd7255",
        "4726af42-f2f5-475b-b30a-eca57fb085ba",
      ],
      "id": "f5165378-78a9-408f-9dc8-5a175e991481",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "2f44fb2f-21db-46c3-9971-6b63b3e95e15",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "surveyUrl": "https://rusty-gripper.biz",
      },
      "dependsOn": [
        "658aec7c-2673-425c-b72e-35be95e3c571",
        "cf629d41-72a4-4bfa-a1ca-4301524f34f9",
        "22daa85b-df37-435c-8c26-4936a74c5a81",
        "3817ba35-ea83-49aa-8f51-550e3cd6288b",
        "59384392-37ae-4e8d-a07e-00ed1353e2ed",
        "d6a1da9e-0455-4967-b08c-c3f7c59913ae",
        "89ee48c0-c0e9-4a5b-ae7d-daf644518703",
        "5e0cd7e5-8b92-43c0-a177-3c443f6c131a",
        "971d8afd-495b-4d76-9653-9ea1860604f3",
        "04607a26-31be-4f8e-89f2-87c2e8f782d1",
        "5ad939c9-edcb-4630-9808-841433362105",
      ],
      "id": "a3aa0e07-bbd0-49aa-887a-82597f9c111c",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "0645730a-cb7d-469e-b78a-f430d6fcfa06",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "dc0f48f5-f63c-431c-abed-85f3e67e26df",
        "43176ecc-438f-4325-b5f7-1f9269fb20ef",
        "c8e00ce4-13b2-4384-9d24-118820bb2562",
        "948df669-326f-42e3-b6a8-07296607ff52",
        "e9a7228e-f008-46e5-a0a0-bcfbb74db165",
      ],
      "id": "8b2637d4-e45d-48d2-87ca-52bcb18e0c6f",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "180764e5-b831-46ea-a9cd-d16747b1bb74",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "0276e9d4-34b6-468d-be5f-e06dd99b5c0d",
        "dc5c11ed-61d9-4bab-8264-da40878b48b7",
        "4a0df578-44ca-4df3-b8b7-6f9034441dd9",
        "87a547ac-6d4e-4141-ab50-1ea190682d9f",
        "c4cf6c85-dd78-4352-9cbb-bcfa4858b624",
        "f43561c1-dc75-482e-bd1e-ebc6f7355b1e",
        "b268f129-1b98-4ea6-8817-b6e0b03726f7",
        "e83547fe-1e3a-4527-8cfd-28c905cdf45b",
        "b9c6ac39-5263-4573-aab9-6df5dbe77d43",
        "dde44ac7-a8b7-48a5-8515-e8c84c287d9a",
        "69b0322b-8e88-43f4-ba94-54b6651ab3d0",
        "7157457d-8ed2-4adb-b205-3eb926048bb2",
        "a1e851e4-117c-4293-ab80-03dcd5754970",
        "f18683b3-35ca-4149-bdb8-d8dee750e4e1",
        "fe709a98-0a2c-45ce-a683-15ca8632b907",
        "da1862d3-71f1-4de9-a4dd-14850a9f97b8",
        "2bcfdb2d-d9a2-4f4b-ad31-245ba5b3de5d",
        "7c921329-987d-48d9-9f42-923401ab8729",
      ],
      "id": "a86a91df-6ace-4e15-a701-bc4127536a3f",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "5a998f65-9ce5-45a7-b353-35887baf528b",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "65278a84-5cee-4e06-8bae-91a7b0961e9e",
        "4be54fce-c2ad-4015-920e-7e987161ed0e",
        "4b468324-98ab-40c7-bbca-68cfb9459c74",
        "fff3e896-2871-41b3-a788-8f89c3d29ca9",
        "ebf37ce7-b10b-4739-be2a-ba69e168d615",
        "d41c67a4-d013-404e-ab0a-04c1b26968c2",
        "07b26a7b-6b2f-46ad-8729-d2d4b856611e",
        "cadbd7ca-a20e-4fc2-a544-6498c8b172a4",
        "e2521082-877e-4a38-ac12-662bb2482cd9",
        "c7ae5b12-69d7-45ef-9fa7-72db8d4fe295",
        "528bad47-98e4-4d29-bda9-ed8e34bb252b",
        "106d04f8-e151-41c2-b434-d473eb20f7f1",
        "2b936764-21f2-453d-b616-91627150da35",
        "985fe9e6-ea2a-46c6-9041-1b9ef2f595be",
        "7cdb6f57-2485-45cf-ac2e-efb5af0dd1bf",
        "6ca39f9a-8f41-4eee-8661-7ba5204bf35e",
        "ecf3e7e4-cdda-4b45-b132-9f0b3a243a2b",
        "5145687f-c43d-4f1b-a6c1-dbf4ce8a91b4",
        "df7db476-23a4-40f6-959c-e22726b8d91e",
        "34e62285-0f4f-4f1e-b1dd-bace812cb048",
      ],
      "id": "cce30231-c591-4712-9510-9fd7ec0f9c14",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "c3d24575-bbdb-4a30-821e-00f6a5eab9c3",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "2db6abe8-a604-4ffe-ab13-a7f04f1eb055",
        "94a11c70-3b6f-49ae-8c76-09dd11348ca5",
        "a5234ebd-8d33-4cf5-990b-bc7affbada27",
        "80f6f83e-1b8e-4490-8d4b-1e9dcfb515d7",
        "920f27ec-7288-4b78-97cd-90d134c1e7ac",
        "868d8e1a-86b0-49df-b83e-da6f6b530709",
        "69266e7c-cb11-48a0-89e7-013ae37a1813",
      ],
      "id": "91613fce-ade9-47bb-887e-70c0f329ebb0",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "b53c66fe-8e41-47ab-9419-706bc33020df",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "surveyUrl": "https://vast-wear.info",
      },
      "dependsOn": [
        "934c79fb-0c7c-48d4-98bd-c8c74ee29105",
        "b41948c5-7206-41b4-9222-d55994bc5afd",
        "1985a440-91ee-4c0b-8469-b3b45d140ea0",
        "cdd09ae7-48f2-458c-9b6b-e27898aba60b",
        "de05b806-b93e-4f3c-b104-f6017230af01",
        "b2e43d0f-aeb4-4bd1-ba23-73f69a427393",
        "f0f85323-0769-4b82-aace-7f603107fb9a",
        "7c7344b8-333e-4894-8d50-6830416da841",
        "6abe92f7-ec22-4ae2-9555-aa21e3d92efa",
        "ba3d6e4c-f993-43e3-b6b5-223731b73269",
        "5e5d786d-2f6f-4b3b-9be9-06d6d1bb5186",
        "99954842-f336-4028-87d7-ab47394a9f3d",
        "de98f969-924a-438d-ae2f-dfe18cb536cd",
        "03424c98-4a08-4940-863b-792cdece165f",
        "b48ed0b2-5390-4409-86cc-10bedfacfdb8",
        "d000859a-3e40-438b-9ff2-277f21d14492",
        "881d406e-73b6-46f5-9b37-41743dfcc9f0",
        "dceeb966-18dc-41a5-83c7-c3ce37ab797e",
        "0c5dd3df-d94e-4490-9cd6-d58ae18fb8eb",
      ],
      "id": "ef8e1b3b-7c3d-4209-8c8e-bfde22123803",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "09a73a4b-48dc-446e-b702-b1d97f58b999",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://unfit-density.info/",
      },
      "dependsOn": [
        "a3cffc02-689d-4069-866e-29234eff9172",
        "f556a96d-7ef6-4014-a404-f1f3be6a8933",
        "47ca160f-a200-4c8c-99c3-67e4b2aebeb7",
        "d4387ba3-c267-48d3-aa3b-c65044d03e17",
        "9d0b3f28-7107-43a9-864e-42f0f6fe42a4",
        "fabb793d-75e1-4989-a0a1-099ddbfc7191",
        "d17cb730-20ba-44ed-92cf-e54f1180a002",
        "66308432-a164-4429-9d4d-9985e43fb7c3",
      ],
      "id": "536c3185-5dbd-40c3-b73c-58796ab2b0a3",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "564558fe-3612-4e87-bc79-c9b6c87448f2",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://forceful-individual.name",
      },
      "dependsOn": [
        "fed14012-052f-4ac4-8a8f-06185645b58d",
        "17ef5078-a942-489a-b752-2fbf1212e482",
        "67c327eb-fe60-46ac-960e-884f760ac6d8",
        "4d6c2d47-36f9-4581-8f08-31f3d8689068",
        "c260a1e7-d675-41f0-acde-785c4159020b",
        "d02d4eb9-37a6-48eb-9a5d-cb085c00ef08",
        "c5193acc-a4ca-4387-b50d-7e7100757cc9",
        "02a90c24-2cd0-48c2-9044-9072b01e8765",
        "7f4c3ad5-88e9-455c-8ba8-d9c7e2875083",
        "65d58b1d-4ef9-4004-9f88-80144fe56c6b",
        "d7f86f7b-e3d2-4b54-83c5-071caf7db216",
        "6991c31b-bf7a-4b21-8a3f-72827d232ec0",
        "6e85c7d1-6295-4778-b78e-72264fc31982",
        "28a252fa-0a44-484f-9122-94fcd1fc0c8e",
        "b2f4034d-72f3-49c2-90f1-054678146560",
      ],
      "id": "433ee93b-0107-40a2-aabb-03d949b26970",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "daa91d3e-656d-4078-a79e-b0b22b8c0dad",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "applicationId": 3319741026060614,
        "loanId": 5969191178256851,
      },
      "dependsOn": [
        "bdc60ed3-7a2c-4013-8909-5d5216e5e70d",
        "1fb6c96c-82f0-4420-b02d-fb3eb30fdc67",
        "c30d2324-9408-49e3-9ec4-62fdc8650c65",
        "8f93b688-3c38-4c46-816e-311cfc5c143b",
        "b3eef9cd-1bfa-4c54-bbb7-f58f5bc092f4",
        "d0ab760e-41e3-45e4-9db5-c44b44aa0801",
        "051f83e1-2835-4974-aba4-6db424bc85ee",
        "e56cffa0-167e-45ca-82b1-caaf8c2f0325",
        "dbe5feea-7cd3-4e70-ba96-6677a7b614dd",
        "077debf1-4ed9-49b6-b800-2cdbc470d391",
      ],
      "id": "207cf7a8-e0e8-4680-97a9-0acd074e1909",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "51bc371d-d05f-4941-b3a2-55c1a9a2ce63",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://clean-scratch.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://pink-provider.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://imaginary-steeple.com",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://worse-sand.com/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://deficient-feather.com/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://scary-councilman.name",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://round-secrecy.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://cruel-mantua.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://suburban-angle.info",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://flawless-awareness.name/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://musty-guidance.com",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://quarterly-ribbon.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://insistent-programme.com",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://tragic-scenario.com",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://ethical-incandescence.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://drab-marathon.info/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://inconsequential-bathrobe.info",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://vague-forage.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://questionable-heartache.info/",
          },
        ],
      },
      "dependsOn": [
        "b87225c1-46ba-4322-9dfb-aa3086fb0a48",
        "301b76e3-deb1-482c-b77e-abc19a720b3c",
        "70936eee-3006-447a-8c54-08a68b7c32b8",
        "1da84838-2e57-4da8-94f1-c77501069354",
        "3ffc48cf-7484-40de-90db-5e6d4d0df90b",
        "84f38460-a8ed-4929-beea-3e989e810ec8",
        "4cf991fc-578c-4dd0-9d98-4114510abc8a",
        "853db621-0e43-4b0e-b4b4-fc0ca17fc25a",
        "ecb3cf68-1759-46f6-9cda-7f044d586f52",
        "e207e49b-1a19-4121-8efa-5db584af6983",
        "790935ec-18ca-4232-99a0-2ed003106aeb",
        "2c195765-72c5-45d1-b7b2-9b2ef5309e58",
        "7c0b3a7b-e67a-4e87-8868-cdc523b93c5d",
        "50bdb447-a204-4188-b3f9-76d733ddc989",
        "16a95835-c01e-4dd3-9aac-6496eb65ff17",
      ],
      "id": "ed44cc65-52e0-4c3d-8fa7-1d282c1971f0",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "38c3f2eb-1cc7-471e-a8f1-dd07b4d4afd8",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a4cc8e02-6530-46a3-b1ff-faa8bf114d9a",
        "8cc0fdd0-c984-4213-b306-e90a13f901b5",
        "34c14776-1cb3-49e7-b5c7-64a90c12be7b",
        "0c4a6b00-8768-46e1-a0b6-eb554c94f90a",
        "103fc909-d538-43a9-9f79-f6d17285f0e0",
        "f074184d-60d0-48ce-989b-6df3b3155ff9",
        "2e26ff3a-8a6e-4d0c-9ae8-3dd723a017e3",
        "65b1d79a-7d9d-4469-b7be-967d4883b533",
        "a042447b-042d-40ff-9b81-fcc76f559fe9",
        "2079f876-3cb9-452d-b8a2-f21db6f50c82",
        "1e35e16b-dadc-417f-ac00-d16c90d2b51e",
        "11422329-fb99-4338-a267-ef73d2bc902c",
        "ed7821e8-52f5-49d9-80cd-3abc2344299f",
      ],
      "id": "261a2da7-d05b-4228-b2f8-0caab31bcd46",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "db872529-8b3e-4335-bf4a-3c83494cad33",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://lovely-sushi.biz/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://dismal-backburn.org/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://elliptical-elevator.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://acidic-alb.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://unsightly-cauliflower.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://polished-heating.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://sure-footed-cassava.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://agile-dish.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://unripe-volleyball.biz/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://lazy-legend.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://flimsy-corral.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://velvety-cook.biz",
          },
        ],
      },
      "dependsOn": [
        "42a9b734-6d24-4d66-9680-199fc058c80c",
        "c403ed0f-8c22-4ae6-abb3-124dbca16563",
        "36352c5a-61b7-48f8-a679-c478184e93ee",
        "4717a1ea-154a-4966-93e3-e002123aa9b2",
        "a1f9559e-5746-4c66-824f-155b954fc574",
        "3de5bb22-d585-4872-9920-186b562776de",
        "c42f0047-bae3-418e-9397-c36694590f3a",
        "1056eee2-566f-43f2-9d5f-ed7e760fa8a9",
        "132c8ef5-f5e1-4b71-831f-0260837341da",
        "bd1d9330-678b-4746-a438-162a04017cb3",
        "a852b33a-defa-417c-9788-f3a4b084846b",
        "ed18b972-7e97-4033-96bf-3ab9143ac9e3",
        "1072ff1e-e6d1-4267-89cc-c627843a6bd9",
        "0e87b35c-290d-4451-af5c-d8c21fca63b5",
        "0a7a2c98-202c-48d1-b9a8-b6e40337e84d",
        "9d0281c3-73bf-485d-967e-022d077101a7",
      ],
      "id": "b979df16-7e6c-4b0f-ac31-272fc1759387",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "ebccddde-4e22-417b-83f4-9d06ca34db27",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "applicationId": 2859409333270795,
        "loanId": 5779735546566062,
      },
      "dependsOn": [
        "b6b9772a-5a80-4f76-8765-99254283a876",
        "485afde1-e4e3-4613-89cb-ae8794d1a12f",
        "894c008d-6a5e-4aec-88bb-9468471bb709",
        "f8091394-3bac-4210-8e55-5ff8dce8cd1c",
        "87a4b7ec-ddab-4038-bae2-db86eb75d2cb",
        "9160be8b-3f10-4349-9523-b56df7d24439",
        "e629c34f-c14a-4e85-b2f1-4566f8919820",
        "fe5c41ba-bd9e-4887-ad23-fd08db656b25",
        "a5f14949-2bc1-4734-b0ea-d853974244aa",
        "89e594ed-70c8-48dd-add7-b7966e190219",
      ],
      "id": "f4552691-1cd8-48f3-85ca-3328f6060c10",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "84e22139-b7d7-4b68-b60d-8932869d97bc",
      "type": "CHECK_AFFORDABILITY_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "952bad5b-6c00-4718-b5cc-375ffec7c1b0",
  "order": {
    "address": {
      "line1": "vulticulus tendo rem",
      "line2": "debeo depulso tametsi",
      "line3": "desino caecus trans",
      "postcode": "coma voluntarius sui",
    },
    "eCommerceId": "2e002d9f-608c-4a58-8a17-4f23dfc4f20d",
    "email": "<EMAIL>",
    "firstName": "Martin Gorczany",
    "id": "92264bb8-b601-48d7-8c83-fd06ac58f261",
    "lastName": "Theodore Collins",
    "mpan": "vorax ad culpo",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "spoliatio causa aut",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "4c503a26-436d-44a1-92ed-8cb9058fa5b1",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "amo adicio uter",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 5`] = `
{
  "actions": [
    {
      "data": {
        "surveyUrl": "https://concrete-hammock.biz",
      },
      "dependsOn": [
        "b380a842-35db-4c04-b7b1-ff95a7546e9f",
      ],
      "id": "1f76a938-5db9-4c69-95f3-2e8eb02ae694",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "bfdd87c3-3c8d-4d05-8d9a-831ed1a9c15b",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "feebb247-6790-4e44-b741-9c14cb66f119",
        "65793dde-8688-435a-8b82-2be72442e7c8",
        "afaee9ac-95c4-44e6-ab38-bac09ce78730",
        "804d16f4-eb1a-4648-a96e-210751abe604",
        "651ce3ba-d53d-48bf-b6b0-42edd518d78f",
        "2736eb07-4f4b-45ff-81f3-793a47ddcc07",
        "c43f02c0-bcb2-42b3-b8f2-09fc4015f772",
        "273a35b6-97f5-4899-a5db-6ac318e06dbb",
        "8d859517-155a-4343-aa58-c1e21df9991a",
        "e86ccae9-27c7-4bfa-b0da-2801a859416a",
        "5c3d5dfd-af1b-488f-b047-16ab60e7b839",
        "e85f01bd-c97e-414c-9b25-2f30916824ac",
      ],
      "id": "a4acbaa6-a9b2-4490-ba3a-6fe9f622d0b9",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "2b067509-5c1c-4850-ba91-7c7534f96382",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "1f07ba37-9f2a-463e-8a7e-8804feede193",
        "57401962-d25f-45c4-b490-eff46f2340db",
        "35f448cf-c290-45b0-b299-3078864a1c0b",
        "79c91319-db88-48f7-b28a-6211813edff4",
        "d9de4ffa-2bd1-4a00-9323-ffb5a4d21808",
        "c9b74697-5d4d-4074-b966-cce980cd42df",
        "81a9f836-8624-49d9-8732-4b67c359f322",
        "834ae5f7-2bad-4a89-b7d4-95ea0a296101",
        "103783fe-784a-40e2-abf3-fa2ed48fa710",
        "0abd0eed-ca01-4a63-a97c-1e97c9f6b469",
        "547b6a98-1dd6-4e1c-b2a1-a2b002b34e25",
        "83e1b01a-f65c-4d0d-b9a7-cb2d21a663c8",
        "89113acc-477f-4c4b-87ac-67c63eda4741",
        "e728ceff-2703-4f23-a35c-1256d35f6db9",
        "667e9edd-ca84-4e37-8d77-3aac1f294698",
        "40b727d3-0d9d-4d5a-a736-09e356b63fd4",
        "077e5b5b-e64a-42ed-aae6-93bbff04366b",
      ],
      "id": "96426804-e927-4543-85bd-fe559954ad4f",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "91b40bf3-cd56-4756-8078-6d883ba5b64a",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "c96d0e61-ed26-4397-bac9-5c58678cf0dc",
        "10189ea7-908e-4f71-bc09-b97173942e97",
        "b1b7f8d5-e9a9-43cb-82fb-ff449b826404",
        "250d6797-5498-4b90-b01d-7a1401796756",
        "bf9a19f1-c946-4956-83db-22fdda665803",
        "8fa29cd0-f858-4c6b-9255-df37cdf4eae8",
        "894087c8-799e-46a5-91e5-20e40bdc4df4",
        "52b8801b-16ab-4432-be67-583ec8bfc0e6",
        "b43d0157-97f6-48e6-ac3d-7bfae84464f1",
        "9d3e5d50-fd8b-4d3a-92bc-372540796473",
        "10871ddb-d920-4b5c-9c49-709232c1c382",
        "4b20d22f-55d2-41ea-92d4-d435af49543a",
        "536f7eb5-9f5f-4feb-9755-44be63f4529d",
        "6fbd1eeb-0259-4f3f-b545-e3bc5e65cb07",
        "024e6a50-ca02-4f0b-aea5-d9ed4daef3cd",
        "85de7fba-45cb-460a-a5f2-73c6530c40eb",
        "73e083e4-5a96-49e7-b482-8c2ea36c67c4",
        "212455e1-05cd-4b0a-ae60-8a888d4fa899",
      ],
      "id": "8c488992-b2bb-47af-90e0-cb69a3694a87",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "95382a33-defd-49e9-92e5-64dd77e0c9dc",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "ppid": "c205d6e9-f34e-4a55-9465-557d8dfa1dbd",
      },
      "dependsOn": [
        "3ef28981-faa7-4d5a-8c6c-ea7922c920a5",
        "1cb91db3-fab2-4d58-9ff1-e54f047173a0",
        "b7053a6b-3719-4bd2-942c-485dfaaea871",
        "216690c1-55d4-42e8-8395-3c7daf81c826",
        "673598a9-8ace-4e87-a543-79d454ba2cdf",
        "a6074661-b48d-4a32-9c1f-c55b2f98f5e6",
        "cb9d58cf-ad5a-4ed6-84fd-84062bd21fb7",
        "bd9aeae7-f17f-4f7a-bbd9-e4580b898209",
        "d233bca6-f6a9-42cf-b509-2450a4a04281",
        "22b037da-76bd-4493-970c-7e6fb27d42bc",
        "06ccfc92-c560-4c6f-ab47-d25697c478c2",
        "417d04a6-2b8c-406f-9d76-452a64b46be2",
        "f11e1ddb-bb05-4cee-9813-8211efd0b302",
      ],
      "id": "ab257bc6-2f09-4e1a-9d87-e3a9ef96355c",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "4cf702a8-a160-4085-a340-ce122da2e262",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "surveyUrl": "https://key-translation.info",
      },
      "dependsOn": [
        "2ed0229f-ef4a-4804-a71b-62e3aac7a9da",
        "855309d9-2f1c-4fb4-8e16-d8aeade5b54b",
        "5252efdc-6652-4744-b168-9ae1c5fd76da",
        "d571d896-a657-4129-a281-a6b371c4a1b4",
        "dd0ebc59-e18f-447c-a7ff-f1d8a20aa7f7",
        "cbc43026-ec0d-419c-bcc7-8dd37248523f",
      ],
      "id": "3ecb924f-304a-4a62-b5ce-38d30b0f78e6",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "6aa82c7e-ae2d-49a2-9f6b-d2d3dbf1af9f",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "2acf7842-4b05-4e97-9aca-34c5df5a508c",
        "32d0e278-c43e-4d42-9efe-5b1bba208845",
        "eaaac005-c2c5-4afe-a2e0-91b384303212",
        "fb9515ad-3764-4971-9ac9-d3c6c12d0a94",
        "267d3314-5c0c-4ea3-b2e1-9b4498a1b8a2",
        "9d732941-3711-4301-b40a-ec3912738a0f",
        "5e0a397a-c040-4c04-9dbc-e9420b1ed8ee",
        "16ef9163-15a5-4a4a-8921-5e929e644798",
        "88ee65d4-f0d4-407f-b706-36f0b4956f96",
        "d61a4e76-9760-4d95-b9d9-4ef44ec6116a",
        "a4336f53-f1b6-432d-837d-efd44199761d",
        "9b121573-a7d1-455d-b5b4-9108e04f12ea",
        "14a14c7d-d554-43b9-b816-0d6c9c305afe",
        "698da378-e76a-423f-8239-f5204deebf7d",
        "7dbdf1a7-1261-41dc-80c3-cdc8daf2e646",
        "826cfbb8-53af-4027-bbf6-ac09aec18207",
      ],
      "id": "741fcae7-b568-473d-8057-ff8d628a91cd",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "e72ed7fd-8ff3-4a45-a962-2fb95bd75768",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "540ad1c2-9f74-4280-91cb-efb8d9356370",
        "2d1b64fd-29e3-4ce8-aeec-dbc98e590932",
        "c8b0976b-07fb-45bc-a184-311de58d7b57",
        "25a5f4f3-f2ff-495d-8c3f-34c8bf48afac",
        "8a2303ab-5282-40dd-accd-f70385b3ad1b",
        "50fb2dd1-dfd7-404d-9423-c4446138b132",
        "610d2feb-bc5f-44ea-8e28-06e10cf0fe13",
        "c5f72042-142a-47f1-9c92-4825dd734d4c",
        "18546a1a-2285-43ea-9e3e-2b434ec507c4",
        "aaddde35-0e6e-4b07-83bf-d926d8eb0966",
        "49ec13fd-5129-43c7-9498-e917b4dce33c",
        "0c0bffc6-815b-4f57-aeb5-ac6b8cb38084",
        "87eaa673-f224-43aa-980b-5f1b80091f7c",
        "67fb2c3f-c7e2-464e-bba9-3683a162a1e5",
        "1bd21b5c-7f2f-458a-a2a6-07f8695c8d8f",
        "80a4b48c-bf69-4e0e-8df3-9aa006229f75",
        "206395d2-3905-46d0-98c8-fc29ec90439f",
        "acd03ea7-baaf-443e-90ec-c8c809d2b665",
        "3e7a7b51-f6d0-4672-b514-52937db6e0a3",
      ],
      "id": "d6cc3666-dd8c-4d92-9d96-d879de14a5a0",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "4ce6532b-6fbe-4080-97e5-8c75c76d5d27",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 887688877129698,
        "loanId": 8366824450040058,
      },
      "dependsOn": [
        "a44c1fad-ed37-4285-8b08-c0bac867707a",
        "48aa78e3-d294-441f-b888-c5663584096a",
        "4451c8fb-c82f-4811-9386-30ece731d47d",
        "d83f3c47-3edd-44aa-b7de-06aa769806eb",
        "4b5e3168-58c4-4112-a144-720db279b319",
        "c0932717-242d-43de-9141-be3b3fb94e78",
        "d3086a1f-e980-4d2e-8a64-0819342e4900",
        "0151f295-1173-40f7-bfd6-9420a030ec0f",
        "bd1481a8-f071-43b0-8a58-96e9998dc09b",
        "650c18bc-6fb6-4d33-ba13-fbad21e67349",
        "77cd01cf-c0fd-4254-b004-6d0114880f48",
        "5fc9feb8-9f8c-4e97-914a-095b7be6bcec",
        "aef1c65a-fa22-4c1f-9210-0d0ec90d94d2",
        "af7709ad-4698-45ed-9ef7-4354f1f44084",
        "7ebfd577-8f61-468f-a4ad-ba3cd742f69d",
      ],
      "id": "3d4484c2-2333-4ac6-aa3b-e355efda8479",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "4030beab-b35b-4ee1-94c3-58db85e964ad",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "9343d775-71cf-46c3-929e-f6f2a9c9e1cc",
      },
      "dependsOn": [
        "63f6689e-6c2b-4c30-aba8-7f4fa0714438",
        "93844986-533b-446a-942e-7cbb31bb1f06",
        "57cad2e1-91cd-41ca-b5a0-011fb6f6124d",
        "32cc3518-4c30-4cdb-a699-dd9608e4049c",
        "feb03862-fc8e-4f2e-80cc-743a49cd2556",
      ],
      "id": "2dbc28f3-f179-4901-8214-d71aa92d3c3c",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "a8b17aa5-34a2-492f-a88c-c8c5ad18e424",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "af65480e-2da8-4dec-81fe-39101475f1b8",
  "order": {
    "address": {
      "line1": "velut verbum abscido",
      "line2": "cultura templum aptus",
      "line3": "defero ciminatio carbo",
      "postcode": "vesco aggero adsidue",
    },
    "eCommerceId": "f64ba945-3c85-4164-a64b-54bc5ac75054",
    "email": "<EMAIL>",
    "firstName": "Jean Spencer",
    "id": "a630261a-061e-47cb-a342-430a4189349b",
    "lastName": "Latoya Gerlach",
    "mpan": "illo consequuntur cras",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "aliqua comptus ulciscor",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "4eaa877e-7a71-4101-b668-1f27eb87cbcd",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "solus suppono adversus",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ACTIVE",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 6`] = `
{
  "actions": [
    {
      "data": {
        "applicationId": 2097216462399219,
        "loanId": 1039568691613762,
      },
      "dependsOn": [
        "df7dcaf1-2220-4314-83d8-fe6e71163866",
        "6e855353-2e36-432f-a63f-b2c88349c3b3",
        "2d169c7f-706b-455f-8750-a719a44bc019",
        "4375f5f6-921e-4b01-96c9-6439a155893c",
        "a4776dc6-22b0-49c9-bd8b-2681a594eee4",
      ],
      "id": "46a22a85-9aac-4f67-ae0c-64686eecf691",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "714a9fdc-830e-4cc9-9e36-32f8d0f9c454",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://closed-saw.org/",
      },
      "dependsOn": [
        "6414b07e-a6e2-4bc5-84c4-c2de72401595",
        "6f29e500-eb93-4530-bb1d-b3c5378af9c9",
        "39e105b3-c405-48b9-917f-f98dc1904572",
        "de212ac0-60b2-4ba6-841b-f87cef76020c",
        "8d99edff-21aa-4511-9bb0-1536c4a7c910",
        "0d637166-888d-46c8-8618-85baab87c0f7",
        "86974ffe-4de1-41da-8bab-f2ee3438a9d3",
        "31328936-0bf9-4a34-909a-c45c33f4bc29",
        "ed435d96-12aa-4dc2-9668-b97882bb11ce",
        "3bf10219-3998-47c2-89d3-742e7b489f46",
        "240c2c01-85b9-4761-95db-c36a52fbaf7d",
        "f026fed4-c1a3-4516-bb2a-40e247f3b7cd",
        "eab58f4c-b73c-41ab-a8cb-844a9ec00bd3",
        "cd725c55-64db-4b1d-8208-34d8c592b19f",
        "d9b50719-96f1-4e08-bde5-a07590a34ae7",
        "ca2efe08-8308-4370-8960-60286e18c218",
        "b6576362-62e8-41d0-95a4-e653e527fcef",
      ],
      "id": "7f5c7edb-a46f-4874-bd8b-78cfe346316e",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "fd9f2bb0-4ff5-49b6-931e-6f830d4e194a",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "e5650a26-e991-4e32-becb-de61987d3ad5",
        "abca34fe-ec21-48bc-8d2c-c086ca790491",
        "24b8e8c5-d4ee-4c2b-af9b-b53400be9312",
        "115fbc96-8282-4413-8678-cff4b4d8bdbb",
        "5bbfc3e0-aba2-471f-807b-6489311623b8",
        "8426fb36-0568-4084-b12b-9eb264a424f7",
        "fb0e0c1f-b1a9-4562-b47b-c2f9ac513bac",
        "b5b7327b-e0a6-4b2c-838d-6bc568281c54",
        "13edb345-0103-47f8-91e9-b7ce20417f32",
        "8ffd0002-585e-417f-b90b-07c13603b31a",
        "47a26175-feeb-4918-9833-5cf9b862327c",
        "9728131b-683a-4cee-a463-797e72698350",
        "9292df60-709d-48b7-83e4-b1ce9c5d7d0a",
        "3d51a2e4-08c6-4d4e-9bbc-15b4ebcbe500",
        "27f8d0bb-4eec-4965-938a-e04a721b486c",
      ],
      "id": "b8862217-7a87-48d6-a68a-22f37b39493e",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "a3e9a4d2-4528-4afa-ae88-4c3f3707b59c",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://lovely-laughter.name/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://some-saw.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://lonely-restaurant.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://sorrowful-galoshes.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://elementary-zebra.info/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://another-freezing.biz/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://complete-cutlet.net/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://common-icebreaker.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://live-status.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://blue-doing.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://fair-countess.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://known-asset.info",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://variable-lox.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://physical-birth.name/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://minor-mixture.biz",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://orderly-convection.org/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://dirty-typeface.com",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://brave-giggle.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://concrete-galoshes.info/",
          },
        ],
      },
      "dependsOn": [
        "703c92ab-1836-444a-b1e8-c8a5e8f2c199",
        "cc49c1c9-10ca-4088-a4b1-68d377b77d76",
        "6424249e-7a8f-4637-9cd7-6ead0049caa3",
        "cf071344-16e5-421d-b43d-c603ff046b70",
        "dcf455f9-22eb-4cd5-9eed-04c8b9ff84e1",
        "d14e9102-6dae-41d4-b89d-6c561973cbd9",
        "d4d05bb6-a4dc-4af8-8e6e-c7d0c381604d",
        "43b324dd-f003-4be3-9f6d-ab3cafc296f3",
        "a2d3b444-bd33-49ea-852a-b8199bf033c0",
        "b83df9dc-be60-41fc-9cb6-659329311cbc",
        "a68fca5e-426e-4f46-800e-68ef39523dbf",
        "ab2e2266-33c5-4b07-856c-3824a56685a6",
        "b07d7549-9ec0-4833-9b3a-08c0b59ff9e4",
        "c6010e21-9181-475e-bc4b-b60c81d7f2c9",
        "cf488a63-30eb-4ca8-a693-d7c0f2c5258f",
        "6fc2fe11-707a-4315-bf16-13dbd1682b4f",
        "a6c7fd16-81e4-459f-b92d-f4f64a8e32a6",
        "fb131546-3237-490c-b294-52e8461dec8e",
        "a413dcd3-6dbd-44ec-8474-006e0333657f",
        "a3dd5a2a-fc8f-4ee8-96be-0befd6fd5273",
      ],
      "id": "255c7c34-331c-4dbd-ab03-0709423ce77e",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "90a4ea2e-6052-4117-a31e-92d5980bdd81",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "0ee4861e-3700-4f35-8d3f-01d42120d774",
        "f2d24230-16b7-4e76-a15d-7d2eea93603d",
        "9a14bc51-02d3-44f6-9050-f0648de416f7",
        "53a22343-5e42-474b-8031-48cbf3504292",
        "a9d650d9-1e90-4290-b6bf-33a1165235b8",
        "fe91d944-597b-44bd-b42c-31e5296f869a",
        "a01a6cc5-c1d5-4431-855b-ac7cbc11517d",
        "3c2a52f3-8e60-4144-b9c6-71cea2bfe7e7",
        "70b02136-090d-4b62-a61b-24b02de0a33a",
        "f206ec3d-b477-4180-88f0-e4c71578976b",
        "0af6c30e-d823-46f0-9f47-49d93a53c340",
        "06e28d7b-a56a-463e-a164-4ffaa5910064",
      ],
      "id": "b36a59a3-a46f-42e7-abc9-62fd0b2d3ca4",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "0f540ce1-32ff-454c-85d4-555738472098",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://carefree-platypus.com/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://velvety-electronics.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://intent-flight.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://juicy-reboot.info",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://fragrant-pile.info/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://trustworthy-suv.biz",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://acclaimed-contractor.info",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://impartial-certification.info",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://experienced-hydrocarbon.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://weird-numeric.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://boring-recovery.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://exhausted-abacus.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://magnificent-mentor.name",
          },
        ],
      },
      "dependsOn": [
        "5bc1a4e0-735d-46f8-a836-c5009d3b89a2",
        "3a217961-51c8-40dc-9a7f-37b8c758007a",
        "6f679dad-38ea-47a7-a206-b8dee675346e",
        "a6058dc3-a62b-45d8-9123-2b9ba41bdb7e",
      ],
      "id": "d83d476c-4be1-42a8-8197-1773886e4d40",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "4500b62d-d1c0-4953-8398-6a0844929ba1",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "applicationId": 4109539067070210,
        "loanId": 6187044699054369,
      },
      "dependsOn": [
        "fec4a006-6811-4005-b5ec-0bd81b0318f9",
      ],
      "id": "c14c0eae-1575-4421-9cad-1c3d05411143",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "6306c109-b647-482c-9b57-86116c3bbc16",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://lanky-affiliate.net/",
      },
      "dependsOn": [
        "1dc37ed4-9354-426b-a845-94ad00c12ac1",
      ],
      "id": "630fb89f-3f06-4e2e-9423-2513bcca014b",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "4987d812-20a8-4090-b5c4-bb4c50f4735c",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "219001fc-5c95-40b2-9a7c-81d4b5f28017",
      },
      "dependsOn": [
        "de8faf10-33d8-448a-9ccf-f3b095c993fa",
        "d9fcedab-037c-4362-a92a-427d03a8b7c3",
        "07ecc73b-306c-4026-af64-497f45c54069",
        "1d611b14-a95a-4359-a28b-46f962148e01",
        "18a075d0-ee10-4886-89a1-c1896a329c00",
        "2ff36db3-d67d-4777-89b8-fcd8fcf148d7",
      ],
      "id": "4e4fa62c-ec7e-47c1-9893-08c7cff77caf",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "8a98b5e5-64b1-4204-83ce-8ade541be1f3",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "d40d9402-eff1-4304-83f2-997625904ac5",
        "95c3d014-b732-4f4f-85f2-ba32b3145b67",
        "f36dab15-6017-4f2c-8261-e997c9a6cd77",
        "23378c88-8663-437d-9d58-81a68b23e7c7",
        "46269e08-6af3-42ba-be23-8a34b7bb4611",
        "33b7b77b-4c05-4c49-ae7d-f349d586a18a",
        "db72e15c-ac7f-4b36-bc76-f327a9d9e898",
        "80a4ad02-6db4-4861-a4cd-736c9119b7a9",
        "d8641bb9-d89e-4b38-99c8-067759869a9b",
        "5c2337a5-c125-4670-9a9d-f6e184df5fa8",
        "c00bfa4f-3480-4b90-8e3e-893c670ae121",
        "3ec5e78a-01ff-4d72-bb70-5819bcf616a9",
        "f2259932-951c-457c-9f4c-368dd4fedb29",
      ],
      "id": "b9d6d0c9-e1c4-4ddb-b18b-ddeeb26a17f0",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "a90d799f-3076-4edd-851c-d4f9e7a2fc62",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 1801805098013171,
        "loanId": 1641491183989034,
      },
      "dependsOn": [
        "ce257f3e-3d97-4f23-a4ae-ac933dcd3a6c",
        "e46560d2-5668-4b62-85d2-04e2a76bbd01",
        "b9bf2917-4f19-4b18-985f-a8503977c4d6",
        "44ee5e6e-5ee6-4f2c-ab4f-1169eab93d49",
        "6d5b0a61-deec-42f3-8bee-def84915385f",
        "6e2a64c5-6487-413b-bd32-4380a4e21983",
        "ccfad357-28fc-44aa-be19-4db5bfe03eb7",
      ],
      "id": "08caa9d2-5e13-4124-8fb6-f92680531745",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "f06d2cdc-2c79-49b0-ab86-e7de9e60b60f",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "cb7ffdb0-c487-414c-bfc8-dc96ab716016",
        "dc65c0f0-e49c-43d8-aa08-34795ae9a9b3",
        "6039260e-32b4-4da3-93d4-d9bee8594d19",
        "6bac333b-60d0-4afc-bac8-6805c482ccfe",
        "ee87384f-270f-4b1d-a88b-c0abac4aee16",
        "fd730107-1fcf-4e97-8e2b-bd38dde4f84a",
        "2b1cbdce-b744-4cd0-af6a-88ee0decf402",
        "00bc2293-f18e-487d-b7d0-1e7d22deee7d",
        "d0080454-48dd-4a2a-b5a6-711c1e5d881d",
        "7490d0fd-55bf-46d1-85bf-b06baaf2d839",
        "a86381ba-3ec6-400e-968e-092a6f397a22",
        "37e4f583-7728-4c66-8ec3-9255b2473dc8",
        "ddba931b-a78e-499c-96b7-51f6017b2981",
        "aa9a900a-d0b3-4926-bc18-2b63d46266b3",
        "db3820bb-5cb4-4969-825e-c185cf0ece44",
        "52d1378f-d0b8-40cc-9dd6-d281b66b2027",
      ],
      "id": "38d144a3-c075-4543-8df2-e5a375b84ce2",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "8cd100f9-8cc4-48ed-8973-65c7320c805e",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "surveyUrl": "https://first-minion.name/",
      },
      "dependsOn": [
        "b81a5a28-ea95-420d-861a-c7c2a21266e6",
        "86c662ee-6976-44e7-a5b5-8c26296245ff",
        "818bb823-7437-48c2-87c5-559044254628",
        "530924b4-8832-4838-9752-f1439ed21533",
        "53e50476-4222-44b6-bfb6-eb80c3864705",
      ],
      "id": "a580aba0-6616-4de1-b980-86c6c406ce99",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "9f6d6a27-c687-42a3-a59e-763dd8600a1f",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "02933740-f9a9-40f9-a28a-6b7fa39acbf5",
      },
      "dependsOn": [
        "5dc57cbf-9479-40eb-bd8b-9dc40aa29136",
      ],
      "id": "dbdbd228-9d82-49a7-b142-64aad947d34d",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "cc6406d1-8be4-4d94-a511-850e366c78df",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "46f79821-1d0e-4dd4-8f45-e66bf4b78cea",
  "order": {
    "address": {
      "line1": "tabella vesica culpo",
      "line2": "virgo non arcus",
      "line3": "somniculosus abutor thema",
      "postcode": "astrum cur coerceo",
    },
    "eCommerceId": "fabc2570-91b2-43f5-83e5-e635aec138b4",
    "email": "<EMAIL>",
    "firstName": "Stephanie Balistreri",
    "id": "8615eccc-42e6-46fe-b491-2e098afc382c",
    "lastName": "Orlando Kerluke",
    "mpan": "adiuvo conservo nesciunt",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "officia dolore ultra",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "31f7b506-5f04-41c8-a419-0102072a60db",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "verbera vox nisi",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "PENDING",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 7`] = `
{
  "actions": [
    {
      "data": {
        "surveyUrl": "https://friendly-distinction.org/",
      },
      "dependsOn": [
        "69ad533e-2f44-4092-a34f-fa7dd81e7ab2",
        "590f8bc4-b524-40be-9aa2-d75e9ab9ca20",
        "1090ff43-1352-41f5-8330-217d80f64b4c",
        "7bef0c73-6308-4ef2-9e53-a27448ad2967",
        "f44c7bf8-46bd-4687-8d25-d2f3661d4b29",
        "a666d245-d8ab-4bdd-aa91-5fa596602d0f",
        "e4c6e099-c4ec-440a-b7bd-48f43b60245e",
        "9b044ca9-7615-406a-8efd-8bce3ec4ec61",
        "8a6dace4-0fdc-420a-84b9-91bf4824b469",
        "c2330a9c-d63e-471c-aefe-7d53d5712fc6",
        "e9c32a5c-50a4-4d28-be1c-22291c1b55de",
        "4caa33bf-d30b-42c9-b619-c485e7495f74",
        "3e4de9ca-a1b6-4bfb-af42-8f33898a7bf2",
        "c1ffe279-366f-47e4-bcb3-2855c6b45e11",
        "3f079385-973c-4c80-addd-517369439384",
        "72fcaa1c-9a61-4715-ac79-837100e53229",
        "d5a57361-3f5d-430e-9666-632f13b31283",
        "a3fea8d0-f182-47f7-8b50-ae9cbca3d39d",
      ],
      "id": "770ca060-5cf3-46cc-b36b-9c56c05dd7d9",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "9857d301-71c4-4426-85ab-8b493a5066f1",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://unsteady-rim.name/",
      },
      "dependsOn": [
        "5475b8a9-f056-41a3-a8e4-be855a49d8e5",
        "31c0aa54-66bc-4c95-b776-b49665b34306",
        "bc7d51fc-a755-4557-ba30-58a5ddd29d06",
        "9892ac3f-d114-46b7-be58-9d4f3d17b103",
        "789c10ba-6dac-4420-b331-5b4e8063abeb",
        "55d90009-0d05-4b0e-937d-58e850b806d3",
        "99f46ba5-8194-4696-8bd6-5f5a88f06c01",
        "171d9ba9-96fe-4dd0-92ea-c157718b4cba",
        "b2b38498-b187-4d72-8567-56051ccc1e95",
      ],
      "id": "b8da4507-961d-49e5-9d5f-b176e7d4c3da",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "66ff835d-59fe-43de-a906-b2b11a5963cc",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "a3cd5509-4420-491c-97ce-fb017af0784a",
      },
      "dependsOn": [
        "eaa3b00c-6779-44bb-a632-08568f9465d3",
        "aa0bae79-e81c-430b-9522-46b7b479b296",
        "5cf7e698-ea78-4415-b2d0-1ed4283bde5c",
        "a3f62305-3446-4128-9a4c-32fbdead5325",
        "c746a41c-6abf-4a6f-b25e-0ac37812ca88",
        "fafbe4dd-b096-47bc-bfb0-6638b36f003a",
        "c98ed0b2-8c80-40ed-b320-935bbc18ef0b",
        "19852fd3-2117-430b-ab9d-9cc22964359e",
        "d93a1211-a55b-4868-be8d-dd1b4e833f1a",
        "7d303bc0-104f-4afb-9b16-59c07ac24caa",
      ],
      "id": "6d4ca4fa-cc6c-4d80-9954-5b0ec4603cac",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "e6312aa7-4257-4850-b729-b6f69f83c62c",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://educated-lounge.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://lovely-resource.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://velvety-conservative.biz/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://far-volleyball.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://colorless-awareness.name/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://whirlwind-place.com/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://knotty-futon.net/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://trim-angle.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://needy-cook.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://average-tune-up.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://sure-footed-handful.name",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://useless-coin.com",
          },
        ],
      },
      "dependsOn": [
        "b56e3321-58b2-4d1f-8dcb-79d201aa8929",
        "eb6b0d2b-73b4-4eb4-9d4c-fb8d0707b61f",
        "2e8eb5bd-bd33-43ed-8e57-704c37aa1dcf",
        "7ffb3a12-9835-4820-9633-2226da1f21d2",
        "3e0567b5-d29a-420b-aa52-3caae2294acd",
        "20dde620-e92d-497d-ab11-239dd8152115",
        "24c7a5a1-9636-4b60-95ad-9cec2962c0c9",
        "0b619f2b-284a-489c-bf41-7af1c4b66ee1",
        "8d9951ff-3446-4f50-8532-7804bbed921e",
        "49cc6a8f-6d37-4e10-b3c7-498b115aeffc",
        "e070918d-7933-43df-8d6c-7e4529b6a0cf",
        "a34b95a9-5185-4fb1-8fe4-55af1d7460d5",
        "512c268b-aad1-4932-8324-7e06023b4fcc",
        "2db94f4a-91d7-4736-8ceb-f57d3be44394",
        "1aa1d018-2caf-4671-b94a-2d588abe0617",
      ],
      "id": "35fbe485-339e-4542-813d-89c69ea381ac",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "474c48cb-b9d2-45f1-8987-ec9359f36917",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a50ed828-e8f7-4e39-849c-dd8d7fea832a",
        "15e45291-9412-43c0-a2b1-13c74ba27d2f",
        "2e3777ab-483a-4397-b783-2f701f41bc2f",
        "9cb18f9b-7d1f-4830-a3f8-8e28f0183deb",
        "c928b6cc-ef40-4bc3-a6bf-37ea669dd66a",
      ],
      "id": "152f5136-d028-4ee4-9ce1-3782a0a11331",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "c7099f49-5d98-474a-9bd2-7e14e7a9eae6",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://voluminous-governance.biz/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://muddy-behest.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://impossible-commodity.net",
          },
        ],
      },
      "dependsOn": [
        "eda779c3-5d7e-44ff-9e12-efc56a9d9bbf",
        "80566488-189c-42ea-b270-95a4768141fb",
        "20dd3200-7f56-4218-8fc0-95028415691d",
        "e9742e8f-fdc6-4061-823d-654bb8a018c5",
        "59c4715e-2236-41eb-8b28-9346c7736a1c",
        "892ac196-213d-465c-9525-e64ec24697fb",
        "5653ee34-4f35-43e7-a213-b536d056ff77",
        "affb62f5-da3b-4447-a9d6-320cf7aad795",
        "a86605f7-fbf8-465b-8eeb-745051ce543d",
        "533e32a0-bb21-4665-8028-f65ca2c1fdc9",
        "3be5542b-da3c-49d2-ab99-31571e91ba80",
        "bbf12687-7ffa-461c-be9c-dbada6240a5d",
        "a6aa7114-bfc5-4422-8608-d541566974e4",
        "7918ba45-b28b-4bc5-ac40-0098d317d5b7",
        "45f79a36-cc10-4ab0-b556-d505b6577387",
        "3e6dfda5-7af8-4468-a6c5-919803c60989",
        "89e4386b-e270-4bf2-ba6e-8e522fbc164f",
        "f3a680d0-8cc1-4238-a1a0-2fb835a0b999",
      ],
      "id": "6f6b6281-d1cf-4c73-a5c5-bcd46db23cd5",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "6a8f7256-df99-46e0-9edd-3ce36126f14a",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "35b01f17-76fc-46e6-bf08-6cfb1329deb9",
        "6ba103d9-5d96-4574-951d-72742936c43e",
      ],
      "id": "499921cd-08a4-4322-8e18-7ebedeb90595",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "a98b978f-17a9-4108-bb9f-990788bcd2fb",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "surveyUrl": "https://pretty-developing.name",
      },
      "dependsOn": [
        "9895ef98-7d63-4b24-b088-bd7d9ee8c075",
        "a14d4aec-29d6-4387-92d1-98ccdb0ef072",
        "3ee1cf9a-0ebe-4476-a5c7-65e2819efa8f",
        "8c1000f7-2ae3-4a82-a5d6-8503b6cc342b",
        "fe92517f-9aa8-434f-a8b2-171d5402dac3",
        "6dd74fce-284d-41d0-87da-b9be40541476",
        "1aa93365-5f9f-4732-b92d-b076c959c836",
        "0333e119-71a4-4953-b6d9-9e612345ae32",
      ],
      "id": "94d28a31-2fed-46e8-be80-9569a611d048",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "056b5787-0e33-4b8a-b3aa-6f9af295db75",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "b50415b0-e813-4d5d-99ba-a0d8130ad481",
        "8abb8b5e-adac-4d50-9410-fe1074cc758b",
        "7c6305a3-1267-4edb-b3b3-cbb182818fc8",
        "b8d62156-3e44-4882-9d0e-e48c912cc33d",
        "de379c45-73e5-4b35-b810-c2fa9c185ec9",
        "5daefc32-573b-4762-bf8e-7c508f3974ba",
        "c35de48d-7aa1-40d8-b4e9-c40799cb280a",
        "b7d5bf71-c5eb-40e3-899c-60eaa14aa931",
        "56c6d959-0881-4cef-8f15-2899364f2d96",
        "d591ffe2-d35d-412e-ade8-1b4b5b0d9977",
        "48c2fdf9-a788-4c16-a9d2-9f05080d9aa9",
        "b0f8d5fe-5176-4129-8c30-17c6344e8610",
        "83d0f597-2252-4a7c-8451-91bf6db89f11",
        "c2c1de22-0f61-43b3-a73a-0cb87c6aa833",
        "63a2c5d8-9c96-410e-91b6-7739c96237fa",
        "55a700b5-b07d-4bef-9397-0ccb39423f1c",
        "1cbb8bae-44af-4e2d-bbcc-8c3de6b10f8f",
        "3ac8de1d-a5a4-4e85-afd6-b10fa4d612a2",
      ],
      "id": "1528c9f3-85d1-45c0-8233-2990c9902d23",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "2d64e448-2765-46de-882d-edca1d717256",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "2cf0ae4a-2914-43ec-85c7-4ae25de03cd4",
        "1c5ce645-5ca0-4993-94a3-527c2df429d3",
        "68cbe3ad-3942-4fbe-bd4a-ab755166915b",
        "41aef4b5-6d6e-4ec0-8073-bc95b601bf46",
        "0b1365f8-0836-4595-9542-c63879fd4892",
        "c0b62e55-f7f7-4197-8a6d-8bb02e59658c",
        "33720013-b701-47a1-a0fd-70b400b0c8a0",
        "7ae0c46d-44a0-42a2-b1a1-e100c197bce0",
        "15d2bcb0-44d8-44d4-b309-e3e9df43bc9d",
        "5bec72fa-cd21-4e3e-9bac-0cce8a6613c3",
        "858fb5a6-4d67-4f34-8b54-b1a2d45ea108",
        "4310046c-c819-4aed-abb2-19fa369f446b",
        "ff482e05-cdea-44e8-a01d-8f1ba059cd20",
        "bbbb44b9-b6b7-47a4-afba-264170ad023f",
        "360f672e-def3-49af-be79-ea03b2c00563",
      ],
      "id": "60c017dc-5a85-4e62-a254-cd0a1f5a1b51",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "3e40364e-7e2c-41d6-84ac-bda44ef38054",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "b3c2adf1-fe48-433f-bd97-941f58b7332e",
        "f1ba2b44-3086-4117-a08a-65821ecfb7e6",
        "f45eb8da-ce9c-45c8-bfba-bde7c3d546e9",
      ],
      "id": "bb12209d-f397-420b-8b20-e5831b2738ea",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "467ca7f2-e400-463d-a319-700a3cd1ca73",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "acfe0ff6-b85b-41a5-9be8-e740a48c10a7",
        "10c6ec3e-3b00-4fa5-9274-9e8c3f4d1272",
        "0e62ab85-3967-4ae2-803b-0d1ebb1d9690",
        "8df88083-25ef-47c0-a932-8fc034475876",
        "4634fb8f-229c-4ad8-b533-df283b696562",
        "92a3c012-de1f-4289-bcbd-793b59628204",
        "6a1192a5-0ec5-49c1-888b-f3c2a287967c",
        "8ff863ee-b6ba-4d0e-b1c5-be86928c0533",
        "75c4541d-d328-4d68-897e-4493ca77eb1c",
        "12c8a4e7-c91a-4a27-bd62-f7418eba1ef4",
        "db846a5b-dd9d-45a8-8c1a-f8a826441075",
        "65e1a342-909a-43aa-a045-91da1e05634a",
        "89fe2609-9258-41ae-95a5-20f54c7a4739",
        "5e8f0fc5-56bc-4926-b63f-151c58ccabae",
        "d4bf43e4-68a1-4f7c-86fe-a8e58d657450",
        "1a319b7c-aa5c-414b-a3ad-83af6b5a3a65",
        "ed79612a-38dc-4315-955e-e39dc2faab9c",
        "edb35f72-913d-4b34-b43f-045c614e5fd5",
        "34584506-7ff1-481e-bb1a-1a8030502c53",
      ],
      "id": "b1e66259-b47e-480e-a0ed-02f4004d7cfd",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "00c55668-769e-4434-926f-88e2dc19ef02",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "dec21d23-23d1-4943-abd7-e43ddd16b16e",
      ],
      "id": "74bcb087-cb54-4f1a-aad8-42a18aadb3e1",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "4c03cbce-1171-4f91-806b-e5ab6499d79d",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://purple-bonfire.info/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://pleasant-bid.net/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://spirited-reconsideration.biz/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://immaculate-spear.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://limp-maintainer.com",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://deserted-flu.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://compassionate-stitcher.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://comfortable-developing.com",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://deserted-sticker.net",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://scaly-publicity.biz",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://prickly-vol.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://putrid-fedora.com/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://somber-colonialism.com/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://unlawful-sunbeam.info",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://spirited-reboot.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://humble-bandwidth.name/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://remorseful-configuration.net",
          },
        ],
      },
      "dependsOn": [
        "3dbc11b1-1f05-4a6e-922d-1c4d4d0bda54",
        "9eeefd02-de41-42c9-894b-e69784b1b6f1",
      ],
      "id": "a982e4f9-e55a-4408-8fb5-29dfa7237d80",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "e3648800-3b03-4d8b-be48-ac1ef36298ec",
      "type": "SIGN_DOCUMENTS_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "faa95a1a-2e9b-4a23-943e-f881d9f891bc",
  "order": {
    "address": {
      "line1": "sunt voluptates rerum",
      "line2": "speculum cervus calcar",
      "line3": "audacia sperno sto",
      "postcode": "celer cupio earum",
    },
    "eCommerceId": "7e1ec3d1-7939-4b70-8a80-1d4f2459fb72",
    "email": "<EMAIL>",
    "firstName": "Jamie O'Keefe",
    "id": "9f7b6daf-43d0-484d-bb35-4508d0227eca",
    "lastName": "Ms. Janis Rau-Marvin",
    "mpan": "ocer vel adeo",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "cotidie casus tendo",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "0aabc321-0393-4214-b58c-8c5e7a39c445",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "sum ratione concedo",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "REJECTED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 8`] = `
{
  "actions": [
    {
      "data": {
        "surveyUrl": "https://steel-word.com",
      },
      "dependsOn": [
        "bbcc28b0-8815-4826-9495-eda7b6b5d653",
        "da60c674-862b-4928-9b89-0ae0404ba9d5",
        "cb0eb728-8463-4f8e-bd54-7c48801136a8",
        "a8c979bc-cafc-4d3a-9906-2a9cc961015a",
        "a207e8db-a955-4887-9dda-401e46c56979",
      ],
      "id": "122e96e2-de0d-424c-bb6c-6e94cf1176b8",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "43068d5e-695b-4b50-810f-3c32479203b4",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "44b8b866-d6ea-4b4c-83bb-6b2ffdf54264",
        "b0caa5f0-9146-4893-b42d-f8349bef087d",
        "f8e6bda6-be1c-4268-bffa-0428a765d00f",
        "10e0c088-af90-4539-b3a1-78080c5c9412",
        "ac647675-dedf-4297-a94c-fc0ad4b27359",
        "09505ed7-68e2-4526-b49b-73c6ee6572c4",
        "a8b4e36c-425c-4010-a217-fd3bee4b5acc",
        "4dc413d1-88ac-4148-8326-369db9b82126",
      ],
      "id": "68b3b611-6b87-47f3-9ad7-51c7bf3b3797",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "b03f9b81-0b81-41d2-a816-018b16a07994",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "c06359f9-31cf-465e-bad1-c3e2ab4ee1c4",
      ],
      "id": "66ee992e-c7a7-420c-977e-b0f5f926610b",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "cc2be9ff-6636-45d3-ae7e-c68d78ce95fd",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "78de1ed4-e68b-4241-8ea4-a784d099d721",
        "217c66ae-9183-4b68-828b-ff6d90b4a489",
        "8c852102-8be2-412f-a641-bb7d14d45b28",
        "322c6967-2f3c-45a4-8179-8186c5265440",
        "888a174d-723a-47b1-84d1-63f0f30df5be",
        "af58efbb-010e-4db0-b070-c4011f38804f",
        "c680e13b-664e-4b07-80d1-7a5b287a4802",
        "743b1b6d-4b5f-406f-93c0-14253ecb1c12",
        "cf4a8376-4683-4d93-812b-71ac6cc12ad7",
        "5369cbb0-eafe-46e1-81e5-d6790ee19baa",
        "26b01f8c-e44b-404f-839d-c8e36031457b",
        "ccbb2b5d-779b-45eb-8f24-28cbd9be517e",
        "d9b038ee-171a-4d70-b02a-1ea6226d52a5",
        "db19429b-cde1-4f75-ab0e-1043523878db",
        "8f2c7aaa-23c1-4621-96c2-64890023fcb5",
      ],
      "id": "733a7cef-fc43-436b-b873-8360d08cf0c3",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "44d37fbf-140d-44c1-9381-a7f36582e963",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "ppid": "f0ae27e1-72f5-440d-a525-30fe315e8926",
      },
      "dependsOn": [
        "0ff64779-b41e-48aa-b606-5f84ef165b8e",
        "4560621a-8dbf-4558-b328-582a3aebee42",
        "888b1ab6-0e4f-4dda-b941-471456052f41",
        "1b253fcf-4f0b-4c68-a6fe-18ad8f3fe3c5",
        "6f9a7693-8a41-411b-853e-6da7a7630d0e",
        "8a9165ba-c581-407d-9fc1-c0b2d0a76b34",
        "cf9ebf9f-0b8b-4a00-94f7-de986479723c",
        "f4df65c1-59cb-4c9b-8efe-9948a5ab102a",
        "0aaf6b16-6846-4e43-b937-fa3a31992fc0",
        "73fc5cd2-be0c-4db1-8467-5c4e167b4cde",
      ],
      "id": "5e897b6f-480c-4049-8d8f-42d821ccf2bd",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "f2f600f8-3b87-4c8f-9f27-04a8517e31f5",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "1cabdef1-c60d-4a6a-bd6a-db03d10a4591",
        "7192c243-a052-40a5-b832-ec0b5aa1fae3",
        "f62ec9e6-2634-4e5b-99db-dad530824342",
        "88d68243-9641-40ae-8356-fa5c5576229e",
        "a4f7e030-4254-4b4c-90e6-3e4f55e7842d",
        "bebfd320-be67-4e9f-bf99-866bbb6d77d3",
        "3a7aea50-c180-4dd2-89d2-2fc088cdf38e",
        "825cba50-ad54-469c-9537-f953bf67278b",
      ],
      "id": "98fabebe-ddaf-404f-94be-7c8029416990",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "08c7b3b9-8cb1-4d7c-a16a-5dc57fb31a9f",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "ppid": "d7651995-1e02-4111-a2ef-70ec87bc9ecb",
      },
      "dependsOn": [
        "0081c9ef-a6ec-472f-bc3f-44713deffd1b",
        "a32f67b9-3b55-44f3-801f-0133195254d9",
        "225084d7-5aa0-4bb5-afa2-c93d78019f70",
        "0a2e5060-e42f-4033-b997-14880cbb996f",
        "aaeacadf-2726-4a8d-8f50-a89bcffd3595",
        "491f7715-f6a5-482d-8017-103585f223fe",
        "3b5046df-9d88-417a-ae20-8dac69954139",
        "3642eaa6-32bd-4cab-88f3-f5565a3a572d",
        "40cd08f4-d093-468e-bca0-4268f7f2abbf",
        "617e6819-8008-46f0-9e2f-70ba9fc82ef1",
        "be8661be-282d-481e-b3a1-567cc2ab6314",
        "50754d50-744b-41bb-bdb2-cb7c05a0f733",
        "1cfc893f-daf6-47b0-b44d-3f392f25a0aa",
        "8d873a54-d8be-4965-9018-04661b7cfeca",
        "a044a562-3340-4793-8457-006288a58e8e",
        "696a8b93-0255-4e46-92c8-d22c6c0f7be9",
      ],
      "id": "6cd1675f-209a-47e1-9fea-52672e40adaf",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "ef8cb424-cf8c-432f-ae84-8404e2299573",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "249cb0cd-f9d7-4794-affb-da6af9d19e78",
      ],
      "id": "0658bccf-dd7d-4ee2-a855-11c453da31fb",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "3506b0d7-6e81-4209-85f0-a2a29b4a02c3",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "ee6fdf14-c0c5-48ea-a9a0-daac8fbdef73",
        "24b2aea5-7518-42e6-b9cf-65ff2f0a211f",
        "83be99ff-033f-4b87-9df1-6da76f395c1c",
        "1139a4f3-7a78-464c-8ba2-4a64f02627e2",
        "8d2cc03d-5d2c-448e-8630-0f9043975fc4",
      ],
      "id": "cc15e23d-1142-4d81-8486-0db92dae5a64",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "3059cef6-7beb-4735-85da-069e79c26fbf",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "b5a86721-0a9e-4b02-b7a3-c5e20e391815",
        "e5ca0b90-ec4a-48bf-bce1-97d16686c035",
        "d88d2b97-12e5-42a0-8433-655023b5ab63",
        "dde490b2-e10c-4c58-b611-cadf7ad8705f",
        "7a6c0b30-fc52-4565-b2a7-23a160e942f6",
        "3ce9a2d5-991c-4ad5-a6aa-220177897f08",
        "c1f81d7e-d6a8-4d5b-a3e7-97acc8f56eb5",
        "ba986443-ecc2-4843-9fb4-d1e3e1c02b1b",
        "0b666072-9374-483b-b6dd-c2f6cb91040e",
        "cfbf54c1-c6c0-41ab-87b9-d727730a9063",
        "f0ba7e84-975d-40a8-91b1-860fe0813ae9",
        "0be3eb4c-ae72-4ea2-ad2f-e168cabd5ac5",
        "e97f7895-8d64-4f4f-81fe-d7b3952fb1cf",
        "332f6262-686a-49bf-9a11-df7453182f58",
        "d34f546f-c2d4-4884-8ac5-a785b60916aa",
        "ad521b83-49af-4535-aeec-e77584fb0acb",
        "b634e1c4-1856-416f-b2b5-90234be983db",
        "00d2839a-721e-4fcd-97fc-c88bb4f77159",
      ],
      "id": "fb837ce3-1f40-4e2a-b64d-3f631a5ec42e",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "ef594c5a-2724-462a-b231-d1c291342fe3",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "16e0e179-b22e-438e-8e92-f7a8a1eec721",
  "order": {
    "address": {
      "line1": "cubo arca volutabrum",
      "line2": "uberrime utique voluptas",
      "line3": "altus pecco aestus",
      "postcode": "subvenio depraedor debitis",
    },
    "eCommerceId": "2a6509ab-8c15-407b-92d8-cfaa9e18a1e6",
    "email": "<EMAIL>",
    "firstName": "Shelia Batz DDS",
    "id": "241957a7-2b65-4628-9b7a-8cc91b209311",
    "lastName": "Donald Ferry",
    "mpan": "aureus vis conduco",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "tutamen eveniet commemoro",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "34da0d40-14b8-46c0-b68f-1ee7d611fd91",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "accusantium denuncio crastinus",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "SUSPENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 9`] = `
{
  "actions": [
    {
      "data": {},
      "dependsOn": [
        "5a25717f-5245-4f4f-9270-9a9b1cf55cfc",
        "efc8b5f3-fe06-4050-a147-c120f105627d",
        "1ee8956a-2f1a-4f88-8dad-a1c217bf8592",
        "3d0e4092-44a6-44b7-88d0-9c89a680b59b",
        "efb75a7b-7395-4b2c-b9d8-671274045b37",
        "66ac43a8-517f-45eb-8858-8c12ca2e7caf",
      ],
      "id": "69cf229e-6819-4bbc-888d-3400417bb8f9",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "f4c17e5b-0465-49c1-baf7-674ef946e9e1",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://unconscious-heartache.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://portly-publication.info/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://joyous-vibration.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://some-opera.com",
          },
        ],
      },
      "dependsOn": [
        "3559b9ae-bf9e-4f3a-ab43-114aee99b53b",
        "10469921-9a88-42f1-bbf4-e0538367d148",
        "b6f461e9-8056-4fb0-bd41-9b5bd656365d",
        "34592851-b2e5-4524-86c4-3c1d763fcb39",
        "60601041-d3d9-4a10-afb2-a888980575ba",
        "f0546a13-de43-423b-ba0f-85ac596dab37",
        "ad24b1c8-9c49-43e8-8a8a-047cb515479a",
        "7de2c89f-cd6b-4bc4-91a6-b2c9c2c69307",
        "13722d04-75d7-48bb-ac10-2a2a9cf0eb45",
        "c953bc58-b36c-447e-8fbe-eab4852b178b",
        "d19f30d5-0aeb-4a3a-9f82-4c1f4eb17800",
        "70dc3c9a-3d1b-4313-8b5a-6c4a0c313acd",
        "f9a143a3-a6f6-406a-8daf-b443b4661b1e",
        "3904c2f5-1019-459d-872b-9ab98ab4b5f7",
        "5e62c43b-c837-4975-b832-87aa503d4561",
        "74baaa38-50bf-43d8-8b77-90bb0bf4edce",
        "0af6f657-d0c4-4b92-886b-b9162d08282a",
        "04624642-5e64-4f6b-b545-2cddd2c46390",
        "668604cc-8b8c-43fe-8af1-5bee539f7dab",
      ],
      "id": "5739a4bb-3159-4b48-b595-11dbb1c5ed60",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "455c2306-cea1-4a2d-a6e8-40e8161bb07b",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a0d05a6e-e5e6-4030-9d5e-16f07110ee63",
        "d95516f9-5f99-455a-b114-4f5918cd01d7",
      ],
      "id": "67089b64-0d9d-4c12-9f67-6e8041929730",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "5a882694-4673-48a9-8106-7aa0d107811e",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://jaunty-climb.name",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://ill-fellow.org/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://ill-packaging.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://magnificent-stranger.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://dearest-t-shirt.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://athletic-forager.org",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://untimely-swordfish.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://other-graffiti.com",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://gorgeous-deed.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://admired-cutover.com/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://supportive-request.net/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://courteous-flint.biz/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://sugary-antelope.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://mindless-pillow.net/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://only-place.org/",
          },
        ],
      },
      "dependsOn": [
        "eb842e86-a425-427b-ba58-612928ae3f57",
        "b48ca657-3fca-4690-8cf9-de5cf9818987",
        "ce78aa3b-f4fd-4b6c-9995-d249f25b3990",
        "2328fb7f-c59a-4ed0-a46e-df1439920f26",
        "ac9834c5-e6cf-45b6-961a-c847e7d0973b",
        "3d67d329-e1e6-4275-95c3-3e72f005d0d0",
        "74d238f7-5909-4a6f-847f-4a51d5b1dd0d",
        "c96e781d-8d59-4121-9e0f-2d6eb94da969",
        "7da3ce9b-eb8e-4749-9778-afd2445faec0",
        "65561134-8fa6-4270-b0a0-745dbcd6be7d",
        "6f19678a-20f4-47c1-b740-1970462bc7e9",
      ],
      "id": "2fd2852c-2264-4917-a225-3bf25d27cce0",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "26594851-89d0-4da2-a75b-6e7d6e9c4aba",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "30a6511e-2262-4cd7-a887-94ef7adabb81",
        "5a351e39-9d8b-48be-9f24-88feca0a418d",
        "efcf02ca-3380-4c63-b0dc-fd7e6e371009",
        "30b691ae-7b82-4200-ae4f-c0853b0cfe06",
        "335f7bc2-0ce7-416c-b764-fa3e74e5b7c7",
        "60bee6f6-d559-4370-b3c3-6d3c81a3f662",
      ],
      "id": "b278b605-7577-4229-bdd2-057f2569217c",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "db052fac-b751-43db-9587-fd5097c51bd1",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 4549271056942230,
        "loanId": 698585938252214,
      },
      "dependsOn": [
        "a69f33bc-bad6-4f02-8402-4c7266ccb82a",
        "6e2c5661-960c-47a6-b5f4-4fdd06435bbc",
        "e27a1a44-8574-479d-b682-dc00bda0af29",
        "bc36fdcf-498f-44ef-8bf4-ae9d84ee3124",
        "a1b28289-e9ec-4c2f-b3e5-8bb93263f9fe",
        "64c16f0e-60d0-4cd9-b868-81fd48597bda",
        "4226018a-4fed-4edc-9cfe-4e9ec1630669",
        "0cc9d505-9e3c-4571-ac78-c144fb610829",
        "319379a4-d146-4602-85b5-a5013a765158",
        "300568ca-6196-4af0-b7db-e5782b3d563e",
        "11f48d64-a57f-4460-8f7b-5bf5e44ff302",
        "7c29be84-a68b-480f-b6f0-f4a80d52d415",
        "2d60ef85-1bd9-4cf4-afa6-88010ee4e7de",
        "4b5a087d-880d-4b54-b95a-1014f804e76f",
        "6e06863c-2ae1-4e82-8294-afbe821f510a",
      ],
      "id": "c62f8539-cb5c-42f7-9106-c8b237c2353b",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "34a6a56b-94b4-4593-b4e4-5509d017398f",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "a0519c70-acdf-4b8e-bce2-cb6caadd61da",
      },
      "dependsOn": [
        "2cba5907-a71f-4976-9c5b-59da68e1a6ce",
        "a6e60109-59c3-4a9e-b133-4287b554ad38",
        "7cf5975f-900c-4a54-9b79-a297d0146c4a",
      ],
      "id": "e80b6144-8c54-41b9-a421-14ac0f4b890b",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "14b81e7a-742e-404d-bed6-2531b0cb071e",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "surveyUrl": "https://unripe-whack.net/",
      },
      "dependsOn": [
        "a8b08731-bf99-4175-b62b-064e28fb3f75",
        "a40fdcb6-f997-449b-b441-f1434ee264cb",
        "8d37979d-76fd-4430-ad61-c1cc1e67e199",
        "6786970b-92ad-48f2-ad7c-9539ecab689a",
        "1ae5aef5-215e-471a-a540-bae8bbb99255",
        "dacb3421-2cb5-418e-8f8f-1ed6c10100d2",
        "f03968e9-4044-4473-b5da-f80e96560e61",
        "26f24cf2-3e5f-4005-bc2e-1f9c3a40e7ad",
        "29321757-8e0d-4d70-af1b-a55591142d94",
        "717e3511-7039-46cf-8565-2e024fc4fa18",
        "96890801-01cc-4726-813e-f7ebe9205fc3",
        "270fe28e-ab89-4318-8863-3299633f0967",
        "2a4b6eea-6de1-4e6d-84cf-2ddaf9572420",
        "e86f959a-621d-4ab7-8eca-28caa28b5830",
        "481f3037-3c40-47e5-863d-fe4da1f626bd",
        "76a88b08-48cf-47e6-87d6-cbadba9e166f",
        "41dc8809-4b44-4227-916d-48cbbc9e2bbe",
        "72a71a7c-7e10-4266-8e12-4e7a6ad9aff5",
        "5ecea0d3-5661-4741-b918-cd4458aa1335",
        "7f662876-7e48-4187-81fe-60fe020711dd",
      ],
      "id": "c1f0fcb3-e4f1-4a81-be36-6420243baed8",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "c8ff08bb-9272-4661-ae11-c9603fc02837",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "4c7e0a4c-8796-4cad-b9e0-2f415112ba71",
      },
      "dependsOn": [
        "43eec313-56c9-4d38-b0bc-e50479a66fa6",
        "412dd0dc-cddb-459a-b617-f453a2016b6b",
        "091ff02f-5351-45e8-9570-68311742e9f6",
        "d19caf53-5dcc-47bd-b606-a79bc65dfc0e",
      ],
      "id": "c6678e90-3d9e-48c1-bff2-c62f6341bf07",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "d924d66c-2a0c-43f6-991f-f268417fd215",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "surveyUrl": "https://tender-deer.net/",
      },
      "dependsOn": [
        "4a51051f-6347-4f55-b895-48376aa5fa99",
        "f5dafe09-75dc-4b63-988f-7408d89bc4ce",
        "ed82a62a-a957-4cea-9e9b-fabda72accd0",
        "e75acf5c-39c0-4d2f-8f16-30c6dcd58923",
        "277bf278-c26a-4cc7-b4a5-2fc4151ef67b",
        "13ce4dab-fcaf-4fdd-ad21-45b1a9c70893",
        "59152445-112f-461d-a01c-c5fcaae117be",
        "900173df-cc38-494e-b048-18fc03f6e3f8",
        "bf0e0d66-29e2-4be8-882f-bf7c5157dcc9",
        "7ec869f9-9146-4de7-bbfe-b95294b71761",
        "e795607d-15b3-48ac-bdb7-4a73224c29a9",
        "58069f47-9e85-4800-8836-13da0c0876d1",
        "b876c04b-faac-44bd-8b74-7246c8c067ce",
        "68048863-cd45-4ad6-8c7f-06320bd86c03",
        "180a2bc0-07b2-4177-b3bb-0a54ec5ad30c",
        "a2026cea-91a0-4a7f-acf6-1aff4a042004",
        "b7c10f31-0bcc-48a8-9571-3ef868334c12",
        "1c2de262-2c1b-4ec8-a453-02c85e1b37d6",
        "fb123160-0049-454a-b052-2ad6318111ed",
      ],
      "id": "c31ec402-362b-4cbf-8f6b-31e1201ebe48",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "748f54ba-7f34-476a-9713-eec94a6a7f75",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "e46b7b5a-ea61-438b-9e2b-070f8d5ee75c",
      },
      "dependsOn": [
        "f3c185ee-7225-4469-b61d-f9686d4a5655",
        "993f4a7c-b845-4274-8329-3c723cabe40c",
        "7ac0c65b-e39a-4813-a131-c41f6cd1dce8",
        "fc4fa090-bf3d-4768-95b1-91dc75d367fa",
        "8bbef59c-dfd2-42ae-ac19-cbe8ce625e95",
        "77e75387-a3fb-43af-94a1-1776cebc82bf",
        "53b706b5-c9aa-4a0f-9a55-ff9136ce27b8",
        "752cd361-8108-40cb-9fab-cd968f11b0b6",
        "fec6487b-e84e-486e-baf7-db21ced3a79c",
        "6f2184cb-9a8f-4508-91e1-52e05be3c2ac",
        "8a5a256d-4664-460a-abc0-bec915c2f517",
        "f11fd9f8-ff0c-4a35-8933-16e857882dd8",
        "9b521e55-6e28-4072-8691-1de120275fc4",
      ],
      "id": "ff3b1863-5af6-4865-b415-0f2516404c56",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "c99f368a-c78a-46c4-be0b-05e8bf091d86",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "158cd0c6-31ff-4c47-8296-18bfd403f8b5",
      },
      "dependsOn": [
        "3ecc22b7-a730-4b4f-98ee-a5e368ee166e",
        "8ccddf17-9bde-4f38-b07c-efe7387277f8",
        "cf252301-3e45-49ef-8ed0-9860b39dc758",
        "b755097c-96f8-4523-b7e7-c308abfff322",
        "693b5c22-8530-42f1-9eeb-f4efed10483c",
        "5287e530-2c37-452f-a4bc-477dd1381bf9",
        "bbd84f1c-0615-4f93-b9f5-9385f8b93efb",
        "7ad6392f-a895-4a8a-9091-bc839107e05c",
        "ad65a8fd-0bfb-4b1a-a53a-eb84bb993e0f",
      ],
      "id": "6405642c-131e-4870-a810-798509a9ef52",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "5065ab63-2ccb-4337-8cfe-a52923f379ea",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "surveyUrl": "https://beloved-cardboard.biz",
      },
      "dependsOn": [
        "e49d71a3-944c-4ce7-8c99-9184f4a3e747",
        "eea451dd-310f-46d8-b147-70a2918d0810",
        "b311bb46-9afc-45b4-a426-cf59b9e6c1b2",
        "a11baeaa-281b-437d-8ae1-4dd5dea4905a",
        "49dd4099-9f7f-4e2f-bc8d-1fcb1e56f794",
        "b3c8923f-e45a-445a-8176-c56993238eef",
        "5b662468-1422-41d2-9fed-b3479fa6069b",
        "a52eac17-e027-44df-9255-3e164f5c8301",
        "f763cf70-8e88-4d75-9195-2e9b3e1d08b2",
        "0c951868-6de7-4100-8d66-81686a42b89a",
        "b409c1cf-0e9b-4869-9644-858ee36cf33d",
        "61ab19f1-5c50-4132-a149-3aee3cd57e38",
        "6fcb70b8-8494-4d08-af63-ad4a6bec96f1",
        "e57c7d79-4348-4913-b317-4f91fc4ab2b4",
        "31cf928a-b749-43e6-8db6-f7f1c6d65535",
        "7751df2c-528d-4907-8403-a9ed2a30e544",
        "006418c0-a251-4121-bddf-6c3ab97855ef",
        "8ba5a124-26e4-40bb-a02b-8bdb86556439",
      ],
      "id": "1469593b-f1c9-4b1e-bf55-b37dd0c27013",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "4ecf740a-f8ed-4750-9339-989d19568725",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "69432151-99b0-45a5-ac6b-c229ca94a53a",
        "88e6be5a-3e99-4107-b269-3404ef1f3e40",
        "d702d37b-2413-4ae0-88bb-9a7bf632becb",
        "b4d88f40-ab47-404b-a3cd-4e156cfd87c5",
      ],
      "id": "057b1c53-acb1-41ce-9b47-b9b67a6e3741",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "35420489-b104-46c7-b325-2f64b77a9418",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "d3ced681-5156-485c-9c78-509d58d8a04c",
      ],
      "id": "ba15fef6-48dd-4f5e-a3b8-43f5a14375de",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "6f2307fe-cac7-46ec-83db-91622d3552bf",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "fbaece84-6758-4fc2-8e46-83110b2d84b1",
        "4e7ebf6e-8f4f-4766-8e5f-dabecf1208cc",
        "db73cec2-0411-4542-bd18-8a28fcb401a5",
        "8378b565-676e-4a75-9ead-d6d773d74e5b",
        "6dd4aed5-5cef-4819-b633-634b426d02b9",
        "d632a225-e0d8-4a7f-9e75-22bda2274c6b",
        "1e9c79cb-10d6-45c5-8d16-a6521b7e6ba3",
        "583353c4-5c4a-44c5-a46c-fe758f292c88",
        "2e3c44da-a654-4d8e-aab5-72029b2079ed",
        "57e89db3-e597-4f79-8bc3-ef0f60b34519",
        "d386908c-02a8-4547-98b5-fc85e643066b",
        "d4c55643-3113-451e-af3b-ea2be1a71c25",
        "fd7955e4-9ea5-46ac-8f23-f02841127741",
        "878de1fb-efc9-48bf-8657-955f4986f467",
        "af455af3-591d-4dfa-9818-6ad48ba8ebaf",
        "341bb025-ea57-46a1-83f7-ae57d226a543",
        "6de2218d-cc82-4293-8ca0-d87ae1c0cd38",
        "94330651-78c4-451e-ab9f-a580142a7c0a",
      ],
      "id": "ff742e28-e35d-4c3f-be21-3947969e10a4",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "68261c86-0a4e-4c11-bcf5-a03956a33cfd",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "1c159b28-9a13-4b85-8d3f-dc9f849d850d",
  "order": {
    "address": {
      "line1": "desino conservo curatio",
      "line2": "triduana agnitio sum",
      "line3": "adflicto non apto",
      "postcode": "conqueror praesentium aspicio",
    },
    "eCommerceId": "69adf1a2-f432-4933-802e-b00565aa0742",
    "email": "<EMAIL>",
    "firstName": "Phil Raynor",
    "id": "14a52f29-49a6-468f-a4e7-1e8712f07fed",
    "lastName": "Abel Von",
    "mpan": "voluptatum vestigium non",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "suscipio dedecor blanditiis",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "a80e3e01-9054-4f73-8b7b-dd1d4a2f8180",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "tot cursim aequus",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "PENDING",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 10`] = `
{
  "actions": [
    {
      "data": {
        "applicationId": 8418753194951045,
        "loanId": 812471540219827,
      },
      "dependsOn": [
        "f163935a-3db2-4aeb-94cf-8d9a711bb457",
        "860fa4b1-7106-4022-bc18-ed99988f2297",
        "9eecec81-c538-4088-830d-6bac7edccce0",
        "3c83581c-9dd9-4199-a6d9-ad35a8a7b291",
        "a76ffb30-b9ed-4498-a361-030de6a8b968",
        "6d22b369-e521-4689-9c68-36048f4a2572",
        "e51a43c7-f76d-4e23-87e7-5bf983e0996c",
        "04c9d0e6-0787-4726-bfaa-fa4f064e2d56",
        "36d06668-a07e-4d5b-8630-898251eb56fe",
        "a62f34d3-8ef5-41e5-b9b7-c4d15a8d67e9",
        "2b07656b-50bb-4f09-aba3-a1f4543cdd80",
        "dfc876f9-5054-4096-ad8f-f722c8f8d288",
        "c6dae531-fdf3-41df-98a6-4728a5e1845a",
        "17739b5a-d02f-4eca-8c33-5533f382e073",
        "706cda9e-5d83-4a80-85b3-b856e62275b2",
        "76da60c8-cfdb-4c5d-b0e9-a093876e771b",
        "38ab83d0-2126-4928-a285-66b6c7d31660",
        "d89c02d6-510e-4750-8efb-37cad496737c",
        "9737ae75-f008-4aeb-aa5d-75eacc89a5cd",
        "c869dffc-da8e-476b-82f8-6aa92002914d",
      ],
      "id": "71ddef08-4dae-42b6-8607-fd361b60ab46",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "7bdca663-9a52-496e-a680-f84c373f0a7f",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "applicationId": 7695791330526388,
        "loanId": 6130130814996793,
      },
      "dependsOn": [
        "65cd349c-4625-40c1-b37a-13bd1b7e39df",
        "e020259b-3f0e-43b3-9d40-2fd6b8bfb3fb",
      ],
      "id": "f316039a-39c9-45fb-a2a6-c47fb4e230c0",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "971af7fa-b506-4ab1-bd86-e67bd1d8da65",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "b995837b-d8e5-45cc-8e41-75badadc3417",
        "e716efae-b934-4e16-b211-75d7a8c82f27",
        "417b668e-560b-402c-8842-c982b9338067",
        "2e53382d-f5b5-4ed2-bcb3-219d1974d970",
        "745a8647-f772-444b-9e1d-3d434ac56486",
        "4eb11dc5-40db-4d37-91d1-5966d7db7658",
        "6dfc4921-c1bb-4683-957a-3c91548eea72",
        "f17f2423-6c95-476b-9e26-56e2b6798c28",
        "7a9806e2-26f4-4e49-ab53-5aa617916cba",
        "5e403c74-cb3b-43bd-8232-bf034988619a",
      ],
      "id": "977a43a6-7d7c-407b-8a27-b75d00237fb2",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "8348a061-7698-4bf5-ae8e-b1363218e630",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "58dfe583-051f-480a-a3ee-dd10c3da2af1",
        "5c2b25c9-3009-411c-9d57-74681a1642b6",
        "67eeb178-aad5-44c1-9232-8470aabf650b",
        "f5e4f191-aa51-4ae2-9e5f-ea4fa6ee51de",
        "9210b139-520b-465f-88bf-00c27faacea2",
        "4c3c8aae-6a55-4500-8de9-bf614c20b130",
        "573455fc-1019-454b-8373-2bcc8f8ab1a8",
        "35248077-3c69-48e1-9133-76733ce76971",
        "e4e6c485-8e0d-4816-b435-61162eafab26",
        "957f3e53-587d-4d1b-b811-78cf7216a8b0",
        "bef8c04f-9aad-41f9-8e55-3a746711917c",
      ],
      "id": "479aca46-894a-4e15-9825-4367b79ed0b2",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "fddb0534-9664-4226-8c05-8982e77d5f15",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "surveyUrl": "https://favorable-cinder.info",
      },
      "dependsOn": [
        "8581e1cf-cffe-492f-9186-68a07de6e919",
        "3d69efee-c798-4256-8be1-c44f4546af89",
        "503a1a5c-9ff7-4f7f-948b-4099bef31a23",
        "84b099d2-2f69-423c-bbf1-c77f357722e5",
      ],
      "id": "f8b82e1e-f110-4a63-a98c-67750cdf9dac",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "bb8f75cc-6cad-49a6-88e2-ae5406b77952",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "020da8d0-d7b7-49df-9eb8-e3e3205e59a2",
      ],
      "id": "4c00c50b-0f25-425e-963b-7a25babc2167",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "ecf96a80-3961-45a4-be77-3599977e7a85",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "ppid": "2356b181-d5a0-433d-adaf-0360812a1fcc",
      },
      "dependsOn": [
        "d8fe83fa-ef2f-45b7-87d7-8dc9f5784b54",
        "ba674d93-e250-4adb-84cf-a4ffbdd8f9f8",
        "7c66e01c-044d-4c5f-bc85-6b0c8fe3f75f",
        "03a899a7-0503-4456-b895-b1d54f919aef",
        "17c4aaa6-f2d6-4730-9fb3-ecfc4f2cb4be",
        "c873a16a-2ad4-4235-92c9-e386692fc267",
        "24e06992-91f2-4e51-bd16-49fbe6e89ee7",
        "eb7ae4d1-87fa-4393-9173-6b9c999ca0cf",
        "85a3b0ab-3bdb-4b28-ab47-7b3b13f272ca",
        "e2421040-857c-4cb9-8cd6-5339b75c998f",
        "b9c5ba90-a7cf-407d-b896-5e530db094ca",
        "a94e6da0-b9d2-4352-98ac-e3cc5308f67e",
        "557ad5f1-b7a7-4c02-8a70-72ae54d0d871",
      ],
      "id": "614d748f-41df-4f97-92c2-f908ac6212e3",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "85e8022f-4f57-4db8-a578-a2e0017847dd",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "3357b9ba-6461-4d15-bac6-40c537a713d7",
        "3be46827-f06b-422d-9c16-c9d57acc700c",
      ],
      "id": "c007a75d-654d-456e-9523-a903561d2792",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "ed964443-2433-42a7-8dc3-503535229b25",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "applicationId": 6977128973080115,
        "loanId": 6811029397558365,
      },
      "dependsOn": [
        "1f9a87cb-9df9-490d-a388-f1e22a6e1d7a",
        "91cae314-e4b3-4731-aefb-a4cb2aa7ae1f",
      ],
      "id": "bff1afbd-3dc8-41b8-b488-98d43b633395",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "d9158bc7-75c9-4913-9cac-9d757380b46d",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://vibrant-formula.name/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://easy-duffel.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://wealthy-section.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://odd-minority.net/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://coordinated-flint.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://best-stitcher.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://altruistic-railway.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://roasted-hammock.info/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://elliptical-rosemary.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://political-beret.info/",
          },
        ],
      },
      "dependsOn": [
        "2d58d865-8908-45a4-bb9b-56d5e8d4ab8a",
        "ea3faaac-ef57-4a7d-98b8-0eb0a97a8d50",
        "d34f3ea4-2396-4b31-801c-253c57850778",
        "6829a53f-e403-4030-9927-4defca00e47b",
        "56941168-3c0a-45fe-a2f9-792ec745a7b4",
        "19c81688-d566-470c-ad8e-4d42949ade3b",
        "d272338a-7a50-4ea8-8e73-7b5b1d724c22",
        "4d51b431-39b8-4409-b903-45e152331637",
        "69b9994a-b147-49bf-b075-97465ad60afc",
        "e65dd80d-3fa2-4253-8a91-88e4d509bb93",
        "60bfdf4b-2789-4900-908c-f4e8e2f3c64c",
        "a9fb160b-4c25-45bd-a234-71acd70e5880",
        "de352952-d368-4d08-8bd0-f926530c67eb",
        "95a358b5-f92d-4ea6-a4a2-5b8fc91033b9",
        "f6245e76-07c9-4b9d-9468-e00b97408075",
        "726e11f3-ec99-463a-b014-9a8a31fb9676",
        "17a09b6b-e78e-42c0-899b-6ec03d775e31",
      ],
      "id": "0d8ff975-e76e-4091-abb9-38d8692b9403",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "c5187868-0344-4407-a6b9-ebb3e4493f89",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "21c5a282-9d4d-4534-9852-4e8427863498",
        "a551242f-f39d-4743-910f-cb860de255ca",
      ],
      "id": "c21ef255-fa54-4b94-abb5-f99b8c4752bd",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "f34cbb72-f945-4539-98ec-caab369af144",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "4585d751-ab9f-4954-adaa-aa0d6d43120a",
        "a1b4ad46-84dc-4597-ae4c-714380f86a99",
        "ab685818-bd77-47d9-94ea-eda55e20995b",
        "b6981195-eb2c-4339-b300-e64bc5a47b2f",
        "9ca0fb70-1efc-45a6-ac75-22c67e5e32ca",
        "fcd55146-aa1c-4017-9b6a-fd6d2e71cfd9",
        "6706b888-5985-4221-aaa5-cb0fcd94fa78",
        "b7d7b7a0-ecda-40d9-a0be-86dd10410a6d",
        "c57d12a6-2d20-4138-ab60-8e954d328d1f",
        "c1abf9cd-6af6-49bf-9ceb-cd37c6158bd1",
        "2358d0bb-e086-437a-97a5-8a035e762bad",
        "c7c48b66-f72f-4ac2-81bf-ec1ba23834dc",
      ],
      "id": "cf735a3b-e116-4546-99e0-b1a7d07e7bba",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "5b4cbb7f-5bf8-4a6a-8b17-d68708dcfd84",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "surveyUrl": "https://blue-hope.info/",
      },
      "dependsOn": [
        "b819124d-e520-4cb6-8d86-3fd4e732f929",
        "291001fd-553a-46c5-a3dd-70c6cca23330",
      ],
      "id": "814de573-3af7-43e4-b765-79266410636b",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "31934139-86d4-4b22-9f95-dcc7c7b09f2d",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "acc13038-23e7-4d4c-ae43-28ab50b30bf5",
        "332e54b7-ca26-4839-88f4-d629c00b89f6",
        "30ef2dfc-bfbc-4010-9ab1-2acb784a301e",
        "64c04ab5-b9f1-4a7a-81af-9f268702acb3",
      ],
      "id": "50358ea9-9e92-4227-976c-6b8780fb004e",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "775fb51c-3aca-4218-92aa-bc3b819c4859",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "1b11b4b7-a698-4a08-bf45-be2b398d85bf",
  "order": {
    "address": {
      "line1": "annus conicio pecco",
      "line2": "demergo theca vapulus",
      "line3": "abutor iusto colo",
      "postcode": "antea volaticus alias",
    },
    "eCommerceId": "7806b598-47a3-4495-aa5c-971946891e73",
    "email": "<EMAIL>",
    "firstName": "Lionel Quigley III",
    "id": "293ce277-a1a7-4624-b7c6-e43eef015bbf",
    "lastName": "Tanya Casper",
    "mpan": "vicinus auxilium beatae",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "appono illo odio",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "55e307e8-9417-41c8-bcf9-f12714bc1dfc",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "laborum crepusculum benigne",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ACTIVE",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 11`] = `
{
  "actions": [
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://coarse-bungalow.net",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://brisk-resolve.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://lovable-armchair.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://lean-expense.name",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://specific-deer.com",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://unrealistic-possession.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://standard-stool.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://courteous-piglet.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://gummy-lotion.com",
          },
        ],
      },
      "dependsOn": [
        "e3f57a89-a41d-436b-9a70-c16a3f6cf3de",
        "f06cd6f1-e721-4203-a833-5f8d91e5abf9",
        "81135bf0-1c3c-4f31-9520-f2ae7ba3f8d1",
      ],
      "id": "2d9c6186-4d5c-4a5b-9153-ce0fa0e3b304",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "1c7fc347-e3fd-4204-9c57-222ff9484159",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "surveyUrl": "https://biodegradable-makeover.com",
      },
      "dependsOn": [
        "77c129ed-c222-41bb-a0f2-014247aa0361",
        "7e5fd8d2-8364-446d-96d6-d6a7e8b51248",
        "e5215b1c-6768-497f-abff-ad0b461a1def",
        "becc9902-7cef-4506-80db-805e341153a2",
        "e25fbad4-460f-4344-898b-1c4fc0dda698",
        "93e9959a-433c-47b9-9e5f-154ef2c94522",
        "3d6af68e-1c68-4611-84eb-bae72b7be461",
        "17893bef-ba6c-4f29-92fc-2db4861219c7",
        "98d52f70-4ed0-40df-9329-82b7d2b9eeaf",
        "01f74c3d-55fe-4899-a8cb-d6dbfa87f9c9",
        "1ed6e576-70ce-4902-848b-d467d2fbffd2",
        "1b20dcae-055a-4f9d-bccd-afb65ebc9373",
        "4ea746f1-d3dd-4e02-918a-4a8c18c0568f",
        "b5404d89-f89f-4a63-a3aa-b63f34391e59",
      ],
      "id": "406ad26e-c30d-4868-864b-bda222421396",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "d352448e-58c4-456d-aea4-a7ec67b60ea3",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "applicationId": 5391898654165160,
        "loanId": 7991706074608347,
      },
      "dependsOn": [
        "4dc06608-1580-4115-9670-2b24239eb759",
      ],
      "id": "64db2fdd-f9ff-4ca6-a3b5-e7eda1fc95a5",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "6153ada7-6443-4b26-b6e7-7083fe81c7d0",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "7d666834-c260-4f6c-86ac-1b2c8dc9aab4",
        "65538c3e-5ef1-425c-914b-3c073dc5edf2",
        "77002b4a-c32c-44b4-8168-0174b0bd627d",
        "b0d3d777-4e30-4fdb-8150-a59165be5ac1",
        "c154a7f7-69b7-427e-bda3-16c574a52f30",
        "cad88f5c-8a94-4495-af3b-c78d08fbfcc6",
        "4a26a4ff-1e88-4db4-a1f7-dbe6e141bcd9",
        "54e5f6f5-b99b-4860-ae38-3a934826a2e5",
        "5c6250b2-4ba3-4148-bcb1-f1a350e1ee9a",
        "49e72040-abde-4dac-8208-0e59766ef8c9",
        "9a28f0f4-ac4d-450c-809c-8b535c571849",
        "dc632820-1852-4c91-9450-6595eb7b5a60",
        "d333242e-cfa2-44bd-a5cf-73bef27a86d8",
        "49c96335-dc22-4f35-82fd-ac45bd1b0917",
        "9b994235-8373-4bfe-a71a-c19b6edbe904",
        "e06fbed1-cb49-442b-887c-2d2ee78cb006",
        "c44e4ad4-317a-4a2d-803b-98a727eae4e7",
      ],
      "id": "4a657e15-9bf5-43de-b666-82a880613837",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "b82d479c-f5b9-4195-a2e7-88d0621d87ba",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://soulful-bend.com/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://super-heating.name",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://impractical-dividend.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://flowery-premise.org",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://neat-decryption.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://pure-intellect.biz",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://well-to-do-comestible.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://timely-self-confidence.name",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://wavy-ad.info/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://neighboring-puppet.info/",
          },
        ],
      },
      "dependsOn": [
        "58125007-60bf-4ff3-a0f3-8f4fb10a1897",
        "736c6714-19ca-4dc1-94d0-e09466af5652",
      ],
      "id": "2ee37383-a131-4b3b-a5dc-1638161fe3b3",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "c1a92229-0d2f-4c83-aaa1-11d3321f49ba",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "b7f1beb2-0210-41be-b478-2400c1c9c1c1",
        "370d9977-3e2b-416c-8441-86eae1e38eb4",
        "5ffff382-7f69-4732-b4fa-e222352fcae5",
        "d3c954b0-ca47-4730-9b1f-759a2f5dfc79",
        "852ecd81-06e9-4c4e-8df1-839ff53c1d2f",
        "2c58db5f-f6ff-4ff1-9478-be3a003c5f59",
        "b6d9dd4a-cd2f-48fe-8a85-8d8f8dd21d53",
        "8424cd52-3d4c-402b-b7c7-e3fd29ecff4d",
        "b6948e23-9b27-4b43-b3db-d8662598156c",
        "6fdf7957-9dc1-41a4-93b3-26e6c1ff34da",
        "856b4f7d-bac2-49d7-aac1-7c10fc38f41c",
        "a030b148-0ed3-4794-840e-2a8349dbdb84",
        "2ad3d709-0aab-4bb3-8fc4-dc747dd7afcb",
        "becbd68f-0f2b-4a2e-b175-73f8e398bcc7",
        "87f12071-cdd3-4e46-879d-0c47499a2b88",
      ],
      "id": "c95d4d2c-605a-4c9f-945a-afe7377c1418",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "6bac865c-4119-4a54-85b3-1f62a5dd0cfc",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "ppid": "b76382c4-d42e-4e27-835e-8524a7d6a972",
      },
      "dependsOn": [
        "99ab68cc-e66e-40f7-af51-6590429327f8",
        "e3083253-2834-437b-9295-bacd8f48cfc1",
        "741b5530-43d3-4466-9d46-31688c507d81",
        "dd68112c-e5cf-4671-83b1-480427cafb85",
        "262edc82-f0c7-4454-93ac-fc65a052816c",
        "8493a461-1c45-47b8-ba9d-2fbebf0a5bc3",
        "23130f29-ee54-4a24-b181-fbd4563d98d3",
        "25e7573a-7233-4b11-bac1-deb67ba01a2f",
        "9fd7a184-e310-4fde-ba78-d59c30b873d8",
        "2dfb4d7b-340b-4cdf-8c67-86d6ded3a88d",
        "2e366edc-a441-4a84-af53-3b86f3d10943",
        "6f54e53e-2d80-42a1-9d70-3faeb1f2e7dd",
      ],
      "id": "12b126d3-a9e4-4778-92e1-e688ac6f2661",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "57873330-d965-464a-8ccf-d1db712d1b41",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "571018ac-f5ad-4a47-bd12-9d391e168178",
      },
      "dependsOn": [
        "17af0e4c-753d-4fad-a4f4-3428b83cd0fb",
        "dfc836b9-d0ff-4607-b732-4716e443bf40",
        "8a16cdb0-5b9b-48a0-91f2-1b83bf69a061",
        "1fc91041-8d9d-48c4-97c2-93fcb240c07a",
        "f1c044d5-b1fe-4743-a479-be3522d7bf5c",
        "ef2e3a67-65f5-4435-8a8b-a2ec60383328",
        "3a323fee-49a8-417b-a83a-6f8e6d4dfe41",
        "f14176b7-3644-47ed-ada7-0c40b146f678",
        "84c96ae3-2958-4783-a0a4-0b10ebdf793f",
        "043cd585-6485-4f8f-ac88-4b72527fc04e",
        "9451a636-dfbe-448b-854f-f2639e7e29de",
        "cf5ae73a-4c6d-4ed5-99b6-1618a6406010",
        "12837631-9550-4134-b8ec-9d5addad7ed6",
        "1458c9eb-0684-40e0-a890-610a6ca17483",
        "8eaac270-e46e-470b-9945-79c590d606bb",
        "eef1440d-9c3f-495a-8230-a71b1394da1e",
        "bbbab5d9-d649-4509-a871-54fd39300a2d",
        "db8b2184-ba73-4edc-a9ee-4467ec540e2f",
        "9c4330cb-9198-4b02-a350-d788de5c14b2",
      ],
      "id": "39faceb1-034c-49d6-9e96-77fb99988492",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "d7f5904f-b86e-468a-a567-1e82db1db69b",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "91019e48-821f-4441-aad9-38ffc85dcd23",
        "4bd42ae6-ad28-43d2-b154-a22685db3bd2",
        "093080b9-808c-459b-8630-f0dc5dc11491",
        "48676cb3-4ec4-44d1-b002-59c8f5773b3b",
        "7a512234-8b2b-4377-aac6-497973369789",
      ],
      "id": "d4054d53-b393-4f05-ae1a-867de481b300",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "eb2f79b2-ecfc-4499-95d5-35973637937d",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "e20758ac-1c7e-4c5b-bb55-d1d36fcbe041",
        "c8237ef2-7086-4f38-8918-869837fa6640",
        "8dd87bdb-b66d-4200-8324-dc8a9f798c34",
        "e0965f08-bd20-4a3f-a654-4928ceb9b180",
        "6311ace6-2d81-45f3-b0f9-0af9372440eb",
        "580d187b-33b4-45ee-b27f-4cfce95d6602",
        "a01af5f1-2498-407d-8ef8-48f4831b0774",
        "50e50ed1-d876-4f32-874d-1ea7002dd37d",
        "f7672160-4c1b-4033-b4e0-645d4859c9cd",
        "9744e910-f1c5-4fa4-b0bb-01bb537b7e6e",
        "44f56cb0-5b74-4904-9007-089db6f851a6",
        "1391f50b-b7a3-41f6-9e7b-82abb690c013",
        "bc872cc6-93ca-4e41-8777-a47398cd1525",
        "1ec0247a-b44e-48b2-89f8-d5c6b97648c2",
        "deca68a1-8e39-4a1c-8f99-738fca1b66fc",
        "6ce34b68-8008-4a36-9801-f7b5cc1b96bf",
        "2b50a875-bf0a-48a2-a579-e9f4ed7ff0dc",
        "0a2b1d93-406d-4671-9f45-cbb0bea1ed5a",
        "490483ac-a35d-4ddd-93db-f8dd58b82217",
        "35e4c439-0ab7-43a3-af06-efd730f5388d",
      ],
      "id": "b4bbce0a-77d8-48a0-9edd-7ab3a880182b",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "10544e07-47f0-4460-b668-3f1871dfd4cf",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 1191177752310984,
        "loanId": 5450546393164997,
      },
      "dependsOn": [
        "f623bcda-b876-4d7e-af63-b4deea1f4472",
      ],
      "id": "2a9a7a75-7a2b-4e27-a953-ce77d9a75e5f",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "ada638a4-a0e0-45c2-b89b-af0153e4dcd9",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "applicationId": 1939139391797624,
        "loanId": 48296682430032,
      },
      "dependsOn": [
        "aad80c3c-68fb-4e47-80bb-53bb2468248d",
        "ded5fea0-7e8f-4a5d-8b01-b4ac7c31643e",
        "f0f3eff6-bb29-4c8f-b500-1903e11afbcd",
        "08bdbc15-628f-4e57-87fe-aa01a5aa3ddf",
        "fd7ecf5b-3add-4328-823e-6fcf30044edd",
        "6f1555af-dafc-4fcc-ae4d-c20f80a46b5b",
        "86a98fcd-9454-410a-a551-c764622be420",
      ],
      "id": "e1f0e809-6abb-4280-a715-03ed2b5e6c5a",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "f64a09b0-3cc3-4392-ad3f-4d01a281e796",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://expert-celsius.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://ample-markup.net/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://woeful-ocelot.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://yummy-fibre.com/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://thrifty-decryption.com",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://exalted-populist.info/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://glorious-integer.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://doting-individual.com",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://narrow-median.biz",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://edible-bracelet.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://immediate-decision.org",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://unkempt-substitution.com",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://unwieldy-lady.name",
          },
        ],
      },
      "dependsOn": [
        "eacd6d84-72ad-4d08-b560-9cbce61de613",
        "88fbaa8e-d705-4df4-87ba-7c13eb1cc9fc",
        "fbd5abd9-fe25-47a8-bfc7-1bffff72602f",
        "81c95ff1-5b99-48b6-8264-0aa376128a3a",
        "cca282c3-dc3b-4764-ac61-07feb1d52d67",
        "f2d7c9b8-0e43-4c8e-ab51-3b203c997427",
        "5f7e1038-cc20-403b-84a5-eb9be2224c6f",
        "f6e87b0e-c94f-42cf-be69-a380dcf0a9d9",
        "37ca3937-ec83-4438-87b1-b6eb8e533d3c",
        "b03271b1-5094-4564-8024-d97f6c9af4aa",
        "2e5646d6-160f-4231-a8e8-ff8ccd812d7a",
        "2bd4cb58-b3db-4d30-8add-221e250e3861",
        "6d794209-f7e2-49aa-bcca-b80a8d97b855",
        "2ed600b4-040b-4205-9e5b-6bf199a2d8a6",
        "cb3ab993-b1b5-4ff9-bc8f-c3987d34d9aa",
        "27814558-40ba-4d3b-84f9-d0c3fd12a181",
        "5174e7a5-2574-463d-bbf0-e312deb01a7b",
        "cc2de65e-2d47-4e3c-9c73-e4073970a159",
        "71441a3a-fbdf-494b-a3b3-00939a95a446",
        "77de9d40-a499-4c7c-96d0-c6284ce257d7",
      ],
      "id": "3e8a3029-bf46-4efc-bfab-4f72daa111af",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "4e96ba4b-8d13-4a08-a5aa-17ffde4a1272",
      "type": "SIGN_DOCUMENTS_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "f9957932-600b-4f4d-a2bf-f3a5a60c3a77",
  "order": {
    "address": {
      "line1": "ter inflammatio attero",
      "line2": "deleniti adfectus inventore",
      "line3": "caveo sponte eum",
      "postcode": "deinde caveo patria",
    },
    "eCommerceId": "36a3cac9-6f42-48dc-ae1a-67582a56cba4",
    "email": "<EMAIL>",
    "firstName": "Kim Johns III",
    "id": "1b993bf3-d804-490e-ac8c-83a699803c37",
    "lastName": "Jennie Funk",
    "mpan": "delectus amplitudo adsidue",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE_TEST",
    "phoneNumber": "bis armarium alienus",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "0c6fe3e0-f533-4b91-825f-de8095d5fcd1",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "sortitus coniuratio uxor",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "SUSPENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions should return a 200 OK with a list of subscriptions 12`] = `
{
  "actions": [
    {
      "data": {
        "surveyUrl": "https://understated-kielbasa.org/",
      },
      "dependsOn": [
        "61b2fee9-36ab-46e9-9f49-02e7de3419d5",
        "921506b8-07d9-4fe2-afc8-04b4677b9a8c",
        "a8754f5a-b988-4737-9577-f33aad974311",
        "c9f90ef9-b57f-497a-99da-bd25ff09cd83",
        "577eb551-fa08-48bb-a09f-982dc1be0bed",
      ],
      "id": "1725a238-4504-48f8-96a7-43bc9ccc0e1e",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "5cf5ded9-adf9-4cfd-901d-5f90fa78ae92",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "f6e95e42-095f-4c87-98ca-843da653aa5a",
        "055e7f60-bfb5-41dd-9538-7cfd2637d3a6",
        "4d131ceb-84aa-4492-8f76-2e0270e6d23d",
        "648b7b9b-3ddc-4f29-ab0c-2b256c02e20d",
        "455298af-0fed-498a-be80-cb43fc591aff",
        "dc53ae02-d7bd-4b16-86c8-888bf966e6b2",
      ],
      "id": "7c706d6f-7110-4794-9448-0a8bc7cd8cc9",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "636116c8-d2a6-42c1-8dd8-bd84da9fdbef",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "a611fb94-30d3-4baa-8b04-57de03b10cb7",
        "78e80f35-ba13-46fc-95bf-19662bcbce38",
        "e7badcf6-70ea-41eb-8a68-eb3d3d1758ab",
        "af5487a8-20a2-4891-ac75-d646021e02bd",
        "cd033d58-86a9-477d-927b-d076d4403ddc",
        "7fb5c304-f028-4376-90fb-27d6bf575193",
        "7a4af35c-329b-4ec0-8bba-0dd16aaa8507",
        "d49564cf-34c9-4e3d-b04a-520bdd5555c3",
        "150e850a-b6db-480d-a89e-749942b32bbf",
        "830c7085-60d4-44f4-b953-09659830a41f",
        "ff7be193-67ab-4105-9b6f-566ec2eb9f95",
        "6bc75473-eac3-4e63-8791-ec7157ce53eb",
        "964c2d5a-df91-45a4-b3d5-c54331f67dd3",
        "cd0a6e6d-0543-4944-8177-fe18c7d8b795",
      ],
      "id": "ff0703b8-90c7-4dc5-8953-70d1e13e80fb",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "74a443b5-c39f-4760-93c8-934b25d9656d",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "applicationId": 3929372313654164,
        "loanId": 8335525800133065,
      },
      "dependsOn": [
        "36709587-256e-4ca4-bd6c-0bc47e577990",
        "e427e1fe-30c9-41ba-924e-059989629f08",
        "99f5ef3d-9304-4b40-be11-b5afaac1c660",
        "c7170832-2fab-4e0f-994b-c3c6199e9d68",
        "e3d20d31-fdc2-487d-a431-ec8cc2cdf3ce",
        "0e7c4d08-bdd3-42b2-a88f-28a51b9720d7",
        "6f75112a-0dc0-4454-8148-a8bd1259065d",
        "10f5dd42-c8f7-46f8-bc63-8c43295a5eb5",
        "f62a0c7f-d9e2-4fcc-8b12-5f024c3a8b26",
        "81cd15d1-0c15-4b82-b5f8-093c6da973c3",
        "396c06d8-d307-4cfa-af20-039b31f799c2",
        "1cd6003e-3eb7-438f-b06f-b7f3e4184a90",
        "a6fdcd44-d646-4745-af49-7e6a4201b353",
        "972efe8b-812d-48b8-ad3a-288e8dc3b6a4",
        "171050c0-2170-4c65-808c-1e0cd6164513",
        "0ed2c4d3-f1cc-41d0-b9e5-20fe767e0ef1",
        "ceaf94fc-6783-4207-a095-1b6480f4538d",
      ],
      "id": "1e5aa9e9-4a93-44df-a2ec-69b18f29e5e0",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "c4a8ad18-69bd-452f-be84-6f9cc1dce601",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "d75dccfe-9851-4d5a-b968-e37f5b73e572",
      },
      "dependsOn": [
        "292c285b-2f77-4832-a40a-c5df523077ab",
        "216a3831-818e-4da0-8fef-21941e43755a",
        "5a21cd1f-1519-4f1d-b6e8-f055cd95715c",
        "2a628dee-b18d-464c-a773-ab3233696222",
        "de9c14ac-8b5b-4f5b-ae3d-357215e9b689",
        "f41b2f8e-4e1f-4210-be52-8435c071fefc",
        "e85641ff-79a4-4cd1-bddb-b66df20e46d7",
        "3e11ca47-d57c-4542-966f-afda38ca985e",
        "316a4d4f-3808-4691-8d7c-aa91077958ce",
        "80d3a88c-cfac-4004-8745-b0c669b58d3d",
        "ed77be8b-0d5c-408b-8ed7-3cd55a7e124b",
        "a42ea165-293a-4f19-a40e-5e4f465f22c5",
        "be8fcbdc-260e-4cf3-b9cd-1b0d5e25f0d7",
        "7695e195-0d88-4877-88b3-85f07637f014",
        "9c6ae0de-2fa7-4402-8171-ee03466937c4",
        "f02864b3-b559-4949-8a05-b277479a65db",
      ],
      "id": "9dd7db9e-8667-4599-84cd-e567510ef62d",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "b3602149-9146-425f-828a-721743c62f0b",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "6ef7865c-fd86-4d12-971d-caf96e0c0990",
      },
      "dependsOn": [
        "58fe2ab8-3678-48e5-8559-188c397955a7",
        "45625605-869e-4652-a15d-40c1f3e4293b",
        "c1ce4f24-9689-4301-9d00-fa39a5aa1153",
        "c5422e85-c6b6-46ff-bb44-33ee5cfa51c9",
        "e1752bd5-9aa0-43df-b59c-5af844d6d8e1",
        "7927cc4c-e134-4d56-a50e-76bad4a981b2",
        "3f9231e7-0e09-4999-a74c-4daf5272d7e0",
        "09ac385f-1dcd-4ab1-9ebf-7b51dc2d4de3",
      ],
      "id": "18bbba2a-9835-42bb-916b-3e3682add925",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "ed24e112-eba8-450f-9790-cbff3ea71383",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "9f57ad87-c968-4bb7-a154-1e8c19ca0588",
      },
      "dependsOn": [
        "9f23b119-2dd4-4aa7-9908-7552b577ff35",
        "5d9f08c5-7555-4fa8-b22f-0725f80c8e84",
        "5cdc6300-04eb-410f-aacd-b3fbf09ce0ba",
        "ba28da6c-04af-4148-a5bf-eaf572166d9d",
        "d464b832-5b46-4e3d-bce3-24143591809b",
        "087545c5-ac39-4db4-8fa3-08a3e004c5fd",
        "fb9d2b06-162e-4bf8-9db5-e5c624c3738b",
        "3d7eeac1-0375-463c-941c-989a2cd6f931",
        "cc815c18-e5f4-4185-b352-9320a1b2afec",
        "0e8978ed-016b-4b93-b019-95563ae32b0a",
        "5f639898-d234-4d55-b0c2-4b7d326c8826",
        "eb647bc7-a58e-4b23-b6a6-183ae70cd724",
        "18c38388-636b-4556-96f9-0c84f50cd5b5",
        "831096dc-99ae-4825-ae01-98ad7f60018d",
        "e96423fb-6847-45d4-b8dd-33123c9ce930",
        "3f44aba2-0c06-480d-9156-d0fd30aec593",
        "2f98ec69-fad5-4515-b014-b18129e14f21",
        "bceccb49-fdec-4707-bb92-4f92c8507952",
        "c125b0df-11c7-439d-a088-375ab03bf7de",
        "9152e703-f6bd-4a5d-91f3-1a834bdc36c3",
      ],
      "id": "63360f42-bb1c-4819-85ce-6a79c3062a52",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "0161d683-f1af-4e2f-a318-bf46185f9b82",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "applicationId": 537158878498125,
        "loanId": 2946713610822780,
      },
      "dependsOn": [
        "ff3b9cc0-0241-4a3f-b554-7f69117885a9",
        "84274df4-aab3-4a18-8607-4b1f7affb7f0",
        "17dccad0-dff2-4d41-950b-32b4d96dffe3",
        "2ca379f0-2cff-4c6c-8677-97345821bd04",
        "ac34b12f-0065-43e1-a584-1678360d4168",
        "bae6210c-ecd0-411d-aff8-36296c75d521",
        "a7da496d-0a9f-43d8-ad6c-cb51c8a9e472",
        "32e6156f-2bd4-478a-a7cd-577143214136",
        "6ffca623-e5d1-4459-a1a0-4bb99fe66d83",
        "f295cc0e-3e99-4e6e-a8ca-507492fc587b",
        "88bc9ba1-38a9-4bb2-ae75-bcac5888a24d",
        "8bee81ca-6773-43a3-9606-152545dcab24",
        "c763b703-803c-42c0-828c-64afe4ed58ce",
        "660006b8-0a0f-47c3-85ab-492237bf1377",
        "2410e9ba-8f5c-48a9-8980-986718553f4b",
        "89e66d60-9d51-406a-9e1d-ad78328d738a",
      ],
      "id": "c44186d5-e487-40b9-80ef-167d66d42280",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "b8d1e93f-836e-48dc-98d6-5f8f98026b08",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://diligent-advancement.info/",
      },
      "dependsOn": [
        "b7310fe7-2840-4880-b407-9625fb543096",
        "99f88477-18cf-4ad1-93b5-3663b3af2b5b",
        "7a3639a4-c4ca-4114-bed3-ced7ff6333d6",
        "3f26be4a-3fce-4783-bcf2-3a3ef03bc6d7",
        "452b8747-3100-4664-a6a9-fd28774658f3",
        "d4dd9321-eb46-4e91-b635-ae68569e8a4b",
        "1c0a4e8c-b572-4a09-adde-2124a34042d9",
        "d9937203-6270-4759-8bbe-0fec4aae9cc4",
      ],
      "id": "4dd3bd66-fe43-4e9d-8573-67a33b078b40",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "93284393-fc26-4531-85e2-5bab54f9d965",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://fatherly-gallery.org",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://bright-mousse.org/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://memorable-surface.org",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://mature-tail.net/",
          },
        ],
      },
      "dependsOn": [
        "5e8a591f-94a6-4c6d-b9de-e7f8dbb82d07",
        "ac3bdc58-0f93-4602-b667-1375864d8865",
        "726154d2-9e5c-489c-813d-77bf5a2203e8",
        "7a8386b6-7e77-499f-ad87-3787f11860ec",
        "af33a15e-f048-4ded-b411-6d6f4294e223",
        "5661b120-8126-442d-b470-1239080e13b2",
        "693fe22e-4f4c-44af-a6b0-c5bd757ad472",
        "cc2cbd1e-6658-42be-8473-b6f811ff5111",
        "b2a798fe-1273-4f53-9a8b-d48b7c589558",
        "f3c6592e-8139-4873-a2f8-9120f17d14a4",
        "7d5ffa88-9288-4f8c-9966-2c81ad531b17",
        "10df4844-fbfd-442e-92b1-f30d7e86da94",
        "b7ae3ed5-a585-4c90-8e6d-4ef3ae491e2f",
      ],
      "id": "d975044a-1be9-4635-933d-b81f8ca1b2dd",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "70d046dd-5664-4fd8-a8ac-789c153c7024",
      "type": "SIGN_DOCUMENTS_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "079abaac-fe97-4af6-8901-bec134847a0b",
  "order": {
    "address": {
      "line1": "antea peccatus avarus",
      "line2": "natus calco tyrannus",
      "line3": "convoco molestiae illo",
      "postcode": "vinum nemo amissio",
    },
    "eCommerceId": "f55561ae-ef84-47f5-93e2-e12170b82aa6",
    "email": "<EMAIL>",
    "firstName": "Larry Walter",
    "id": "164d416c-335b-458c-8ce5-3887b01577b2",
    "lastName": "Monica Wisozk",
    "mpan": "caveo vulgus vero",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "non vallum summopere",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "8156f966-88e1-4439-930f-37053894b09d",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "communis censura fugit",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "ENDED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
}
`;

exports[`mobile api subscriptions module GET /subscriptions/:subscriptionId should return 200 OK with a subscription 1`] = `
{
  "actions": [
    {
      "data": {
        "ppid": "e032cc6d-fcb2-489a-ae6c-b86250029964",
      },
      "dependsOn": [
        "92b4f3c3-da21-4c6b-ab59-9ef4870711d3",
        "361cda75-11c7-4476-8fae-fbfe9e29f2d8",
        "057baccc-c8d5-44bd-8c77-a1d8f906bdd5",
        "8822990c-6538-4cf7-8eb3-9491cbe9b413",
        "ec29cedf-37ff-4a03-97a2-eede95dcd11c",
        "e1334894-0220-45a4-8ede-95f30fe4ceba",
        "abb8c188-b941-4a1c-996b-005301b81ce6",
      ],
      "id": "46b7235b-d4b7-4a53-84cb-442cd86fcad3",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "8f0e7b82-d031-4afa-bd83-e3d1dc1596c9",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "a4c2f741-fa1e-481f-aa2f-5f6b9bb654bc",
      },
      "dependsOn": [
        "c80e6453-5174-43c0-bd6f-1101a0778734",
        "c6d0dbe9-cd3e-49df-add3-b7fb157121be",
        "0470e29b-df65-4f69-9e2b-def675930d6f",
        "f38c306f-fd5f-4f2a-bbe2-50ecc85f7075",
        "0ae135d1-7549-4238-ba8e-55c1648e8f8a",
        "3b2f888a-8b91-4f82-8441-17fa73c563b5",
        "fb0031f4-f88a-4d86-9328-0698d8e419b6",
        "f73ecaf0-fa2a-4597-8ed4-a89f4b560760",
      ],
      "id": "ce8fe5ea-7fd6-4e2d-871e-19d9eb76cbac",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "ad645c95-bccf-472f-bc02-64bb0d04930e",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "applicationId": 6275612972951451,
        "loanId": 3037433446804554,
      },
      "dependsOn": [
        "d7b7ecb9-e395-4107-9953-20b1dabe16ad",
        "47cfa409-e2c7-41e8-8d72-7e45efa604b0",
        "858c8b66-e7d9-4836-a8a5-3ec0032acb73",
        "e0981ba1-a810-450f-ad78-7858297c42c2",
        "95867a18-7400-4743-994d-a77026385755",
        "a95b3b3a-29d5-48f8-86b3-4095d5efad2a",
        "84617bdf-9642-4dda-981f-885b25c3af4a",
        "c957c364-025f-4e0b-924a-9110cf215a29",
        "d2b2825d-10e7-44db-9cb9-ac6b8e01e9ec",
        "0a630f21-25d9-437d-bb60-8cac4b6d0c93",
        "bba2474a-5038-4158-88f9-439f10aefba5",
        "c1d85c52-f86e-4431-a718-1e272bbb75b8",
        "956a7f77-3916-4270-85cc-fb00b4c85d11",
        "2c147a76-c3dc-49e7-a4bf-459967f96149",
        "054ed724-09ce-40f2-817a-d93d341670cd",
        "134da233-ac15-40c9-a9bf-88f285c0225d",
        "4f5bd9e7-a503-49e7-b2f6-be516c4c4204",
        "53bc42d0-8ea4-4bdc-aab2-8201e3e58fd9",
        "4b926926-fcc8-4ad3-ad15-77dbabc3d333",
        "eb0399fd-de52-4d3b-a2e2-500d19c4609d",
      ],
      "id": "1414eaa5-daef-4546-8c00-7c4d8d357ccf",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "8cb05d3a-9c11-4782-85ee-88941bb31314",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "634db537-05d5-4044-b523-7b7aa60efe92",
        "0bb6c41a-b372-4c27-8d51-829972755545",
        "27850f5e-6d5f-4ae5-bffb-4d067779df34",
        "88b1aee2-5423-4387-a522-894472ff3ea3",
        "5446463d-55de-4c5a-84eb-8021a8a76858",
        "cffcb4a9-d9d2-4c52-abc2-04a48806a4aa",
        "47df60bf-0b12-431a-a0ac-dfacff9adc94",
        "1f95169c-f44f-4701-ada1-b78327d1ae95",
        "71a9069f-5b18-49ff-a86f-d5653857a5a0",
        "684f5b85-da3f-414e-9f2a-9ceb2e1297a1",
        "127d4948-2c4d-4965-b4b0-d507e9d04e29",
        "eaf42676-8873-4733-8dca-1e6d7baa551b",
        "4a50264f-a91f-47d5-92ed-82284cef46eb",
        "d13482a7-1eb9-43da-a070-c6b0b947fb84",
        "66702dae-923b-48ae-b9d1-c45d880a3320",
        "288ebddf-8dff-4ee5-af3a-0d996c82b4bb",
        "ac747cd3-c53f-436a-b505-f34a6bd39ddf",
        "569336f7-fd0c-4f33-bba5-3f6d0d6f2c31",
      ],
      "id": "70cb1719-60f4-4e6f-a89b-d96840dbb7fa",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "b3209656-75af-49af-af36-bd10f61366dd",
      "type": "SETUP_DIRECT_DEBIT_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://pleasant-oil.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://self-assured-parade.net/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://subtle-packaging.com/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://complicated-millet.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://cheap-kettledrum.name",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://smoggy-vision.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://sore-promise.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://dependent-bin.org/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://definite-maintainer.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://posh-scrap.net",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://content-hunger.info/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://exotic-hunt.net",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://handy-armchair.info",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://gleaming-porter.biz/",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://functional-pronoun.org/",
          },
          {
            "code": "rca",
            "signed": false,
            "signingUrl": "https://polished-formula.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://worst-following.com",
          },
        ],
      },
      "dependsOn": [
        "a8623de1-f642-4993-9e6f-5ea630e24ebc",
        "2e4b7c11-8b98-42da-b07a-7a26f2ac045d",
        "ebb69a33-f272-4e0d-887b-e30bddc68298",
        "a921511e-92a1-4ac6-9839-fc49ee455bb9",
        "6fa617ce-ceda-428e-b72f-80e2308f636f",
        "18e9456d-e6d3-4916-b335-352885d0aabd",
        "04b00c35-3a5b-42ca-8fe6-f8805e5eb865",
        "cf419c6b-807e-498c-b473-d5f9fae8764e",
      ],
      "id": "617cbaac-c3c9-4b2c-a43f-dcd0fe955147",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "3a039dd9-082a-4710-aba0-653fb1f65d43",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "documents": [
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://elderly-jump.name",
          },
          {
            "code": "rca",
            "signed": true,
            "signingUrl": "https://well-off-steak.net/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://ethical-birdcage.org/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://striking-import.biz",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://shy-video.name/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://grave-accelerator.biz/",
          },
          {
            "code": "ha",
            "signed": false,
            "signingUrl": "https://stale-contractor.biz",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://shiny-carboxyl.biz/",
          },
          {
            "code": "ha",
            "signed": true,
            "signingUrl": "https://grouchy-entry.biz/",
          },
        ],
      },
      "dependsOn": [
        "8a2bc298-7e61-485b-b0d1-3903c231a221",
        "5a5de41f-2334-4976-80db-d45855b7af22",
        "a3a04adf-4a5e-425b-bdb1-24b1e95b52a7",
        "45263382-8966-47f4-94f7-1f8b4cd3dfe0",
        "fc7ffd73-8d72-403e-8d29-eb55a03b132d",
        "4a8dd104-da88-4b93-83cf-1cc8c2022db9",
        "2f9e2eca-aa64-4579-be54-4fe68f90e33a",
        "eef2bce1-a00d-4ed9-bf95-283d9016120c",
        "65a0f903-7d40-40be-896b-d845ea3a1241",
        "54406e27-3d4b-4c59-9695-921a91711383",
        "a47296a8-e96a-448f-9001-19501f435778",
        "9fd91212-076d-4959-ae72-906c34b1695a",
        "09fd32d2-5d64-47e5-922f-8cd4b2a6b276",
        "e1ed92f4-ff88-4240-abe3-4f426c8b1a01",
        "daa58718-b999-46a5-9189-6c5c247c4762",
        "1b4002d5-f6c8-4fbf-aa81-dda354a73a7c",
      ],
      "id": "de556a8d-773a-4d9c-8614-2b1343bfd9f1",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "f3b1c89b-3efe-4ad3-bf1f-1fb68b94363e",
      "type": "SIGN_DOCUMENTS_V1",
    },
    {
      "data": {
        "surveyUrl": "https://svelte-tomography.biz",
      },
      "dependsOn": [
        "e93a01a1-5bfd-4ecb-a6b1-2e97a5315275",
      ],
      "id": "0497031f-e565-4b7a-98e5-cd22bb80340c",
      "owner": "USER",
      "status": "FAILURE",
      "subscriptionId": "75eb112d-70b8-42c6-9e08-6629585d10c3",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "73daa733-c9e6-4397-8925-c1069ff0048e",
        "3cb1fa20-e8aa-437f-8702-e665435b71e4",
        "f5b295b2-b4d5-478a-8ebd-ca63e2aa3d1b",
        "5c5c1b08-6d99-4a8f-9bbf-c43ea637f9d0",
        "74a7ffd9-7362-45ce-96b4-c947573291a2",
        "098e1c77-76f6-44bd-a9d6-5fd06ff83725",
        "7b08b3c4-b376-44ec-a1b3-ed4483f1cef0",
        "79874990-1194-46f0-833b-11513a9c700f",
        "6d1be335-2fcb-423a-85cc-4c7bfa09c907",
        "8685efd8-f215-4bc4-a303-85e30bbf1f87",
        "61cc6664-ad29-4f13-817c-f47d54178f1d",
        "101043e9-083c-43a6-b390-1f46faa85a6e",
        "4316d7ac-9dea-4bca-924d-4afa5462ba4f",
        "d94ac990-4214-4d5b-a60f-2801aaf5e668",
        "b506e970-546a-47c2-9587-29d4986faed8",
      ],
      "id": "cae39caf-36a7-4e13-a5af-932ea3a1bd8a",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "c9f1c524-a07f-4913-b9c4-885d43696e64",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "ace1c54b-bf0a-4962-bc92-f9c0792248f8",
        "67e03fc0-0ef4-416e-b6b8-669979efa152",
        "91657e36-1a11-4317-81fc-b465a73d4975",
        "74f8e567-4b81-4f80-b996-d52f654d47de",
        "059cf9ed-b593-432b-a11c-3d655bb04b23",
        "f0075511-434a-4f2f-b0ee-bf59aeee8076",
        "1e59768f-a568-4bc3-9562-40c172184ad2",
        "b009b136-9f50-4950-8b14-05945d7c309e",
        "a1339491-7742-4e26-aac1-cf1a65d438b6",
        "66d3f6fe-5c3a-430e-b989-3e8e7f3f4017",
        "9b1d1cde-1088-4bad-a248-519052ca5986",
      ],
      "id": "3edc3117-4dbf-4a42-a9b9-228dd42dbab9",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "f115e637-1831-413d-ae3b-bc8d2d3628f1",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 4366652069285034,
        "loanId": 6183831495044118,
      },
      "dependsOn": [
        "ca09f292-39f6-42b3-bb5d-739934b6c991",
        "6ce12b4d-db5d-4073-9e07-b5dcf908fa52",
        "ec60968d-bbab-4747-aa97-7803e64d6a3a",
        "36173129-2646-4180-96f1-8a90743dc351",
        "00cb2124-11e2-4008-989d-26de247ca062",
        "0d14ffa4-ef26-40c1-8700-18c74be1d343",
      ],
      "id": "8951efb3-cee6-4567-a8e1-b606beb859ce",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "81fc6d36-37c1-4ee2-a39e-fd0bb5b61320",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "eb890eab-9619-4a22-82a0-55dce11e0677",
        "82f336ac-a61f-4a65-a404-76b1be297325",
        "a0d1dbca-5a84-4f85-b1a6-6325b5e2b7b6",
        "f76de9c3-df63-4e70-bdb2-980253c33214",
        "8b225726-4556-4d03-88af-5c9242547924",
        "ad53d4dd-3b70-4f71-8ffb-764545ccfcea",
        "23d989c6-b128-4e06-b26b-1129f9328cfd",
        "74e93192-7b8a-4760-a675-d992ee17e35b",
        "cebb3e6e-1d4c-4131-959c-7314ae1a196e",
        "3a3d82d6-5995-4f92-8fd5-0d04f7bac829",
        "7744c52a-923e-4324-8750-7b2e8cd8fa4d",
        "d69c29b2-c5c9-4ff6-b030-524930a49ce0",
        "b0ee6d0b-2f29-4816-a760-34fc79f67cca",
        "3e78c598-2070-4c72-ab89-a4dcdffe122b",
        "43b2a427-d060-4252-96a6-db34a311ffea",
        "de218102-f931-4034-b591-1ebae88793bc",
        "6d4e1fd2-aa5a-47cb-a7fa-10f56bb5d8be",
        "d053ca0a-fac0-486a-aa23-122b143649e7",
        "bbf82397-ace8-43a7-b8ba-e445184401fb",
        "029ff3d6-4d01-4ba3-9bad-2532977567dc",
      ],
      "id": "c176cccd-cfe9-4012-8feb-8756f83a8aaa",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "7dd8a72a-d83f-496b-b989-6b66dcd93335",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 4123010761677990,
        "loanId": 2700063750384682,
      },
      "dependsOn": [
        "969eda4b-7ef0-4dba-8760-5536f58d868f",
        "8792da48-4c5b-482a-b7c0-dc64a13c0efa",
        "398f8187-97f7-496d-972f-8a54d45c1d10",
        "cd249103-bace-4e36-bc30-8c65acdf6a91",
        "880ba7f1-d4f2-4b2e-80bd-a2295c312229",
        "74433f35-59d1-47ae-8584-4adc4a2dcb6a",
      ],
      "id": "504fe665-ef1c-45a1-b9e6-93d6b110024e",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "f9899cea-4439-439d-9823-338cc381c0a2",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "surveyUrl": "https://glossy-alliance.info/",
      },
      "dependsOn": [
        "24a2ab9e-8455-457c-b9ea-f507cd767d6a",
        "10ce9b0c-d65f-4a9d-b3d4-a5862180d87b",
        "d7294996-2fd3-47b3-b6a8-47c0b0525fa8",
        "acf2731e-5968-448d-aa9a-02b2cea0ea13",
        "2e772dc6-497e-46f7-ab23-8b6bd6fb86d0",
        "20f362c8-4b08-45d6-a211-a6fe7b92d8b1",
        "ea5b4f5f-0439-4728-bea5-3e5bc263cade",
        "42805c57-6db8-46be-9996-d0aaa1932f5d",
        "e6760936-5776-410c-986e-b78ea2d244df",
        "bb9cc99b-fb65-4a3f-ba02-2c5d31cc6f7c",
        "363ff541-c76e-4336-a168-3484706cdb86",
        "ebb91a2e-5ff7-463f-b451-eea856e217ea",
        "92d69ff8-d411-4984-bcaf-c13e087af87d",
        "e48f8b42-a020-4e5b-8fcd-e0fb16aab6e1",
        "bb59a330-0b9d-4d05-870c-091bfa004366",
      ],
      "id": "d27cce7e-365c-445d-a937-03e20e0a2f98",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "6c28a9c4-3039-4fea-b5af-33ee8d5e52d6",
      "type": "COMPLETE_HOME_SURVEY_V1",
    },
    {
      "data": {
        "ppid": "a6fad306-cfac-4766-9f72-48740e7db438",
      },
      "dependsOn": [
        "1cceb4cc-17ab-4005-8feb-57dd93b39f1f",
        "78443234-370d-4f09-90a5-713fa8b349d6",
        "2e727ae7-fc80-4589-9e9f-b7d7b606d003",
        "3ae3771e-4823-4a3f-8211-16aced558ad6",
        "7f539603-a0a9-4f57-b30a-55ca85a30766",
        "6bf303a4-1fb4-4824-a491-048537cb4d09",
        "aa69ea1c-8014-4a8a-978a-d1a5e71a1505",
        "cb536ed9-b9a5-4a7a-8f19-f6be99b8fc89",
        "9d926d42-52e6-4bfa-bf65-ba751fefe9a9",
        "fa1d0585-a5fc-45a5-baef-489e6bdd58ca",
        "7470a559-275f-4445-8fda-880e90e4e81d",
        "ce5ceb25-969a-46f5-8524-689d663a126e",
        "cee1372d-87aa-4544-9609-6e350f58389f",
        "3c025a5e-3747-4fa6-9d21-2278f06e84a7",
        "0bde07c8-7e07-4766-8be5-6696d382cbc7",
        "7d5cbc8d-80bb-4ab9-8154-e9a28eb6d65d",
        "644cae12-70d3-46c6-955b-9ebfd8a8f433",
        "041912b3-86c3-4721-9c04-bf6d6e7303b0",
        "bc3c8b65-b659-4c2f-8bfe-29588bec0b31",
      ],
      "id": "49990f7d-b29b-4b5f-a8e0-27eadd79c135",
      "owner": "USER",
      "status": "SUCCESS",
      "subscriptionId": "fc07eb48-f784-424d-9a22-9bbf0fd3a950",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "applicationId": 8947771095508555,
        "loanId": 2025450691844184,
      },
      "dependsOn": [
        "b6216f1d-4e7c-4fa6-af26-12a45ea1e8d6",
        "27a16f12-a7be-40b3-9adb-c72b80d0e37a",
        "93c48961-aaaf-421d-a84f-30c421b0d57b",
        "6444cbb2-7d66-49ef-8847-f60d19b6b982",
        "f6e080fc-5a6a-4e2c-83db-474a72435f3a",
        "adca400e-563f-4f9d-b728-8bee3683585a",
        "559a8b2d-84d4-469a-9df3-d715b8e67604",
        "8960cbbc-9e83-4cd0-9a89-67de76b50e73",
        "3aa2a4c3-b8d3-46ac-9e21-652c1ff64293",
        "987fbe11-59b9-4727-958f-dd5f553c8745",
        "bbed8fec-3b53-412d-8bc8-1710fd21d03a",
        "cd633a38-8eb0-4433-991f-ccc1e7f621ba",
        "511e3174-9ad6-4de5-beef-092c89f82628",
        "cbe0a927-e505-40c0-961d-d68b0e982af0",
        "1eb3c83d-9938-4f55-9fab-fdc8ac71c8e4",
      ],
      "id": "b0feb25c-f41d-4277-a8ac-0ab544f0cd37",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "7d621c5d-810c-4f38-9c3e-3f2fd85e5693",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {},
      "dependsOn": [
        "1f8ba279-eaa8-4a3e-ac73-946b3158874b",
        "632f6fa1-11e4-46a5-ba41-67bc0d549ed5",
        "ec3b20bc-ec3a-4608-9b83-b919d6ba49d1",
        "46d80ed8-bc27-4b43-b2c4-1739bdfe748a",
        "0ed8fc84-d0a0-496f-82f9-f22235bd2fc0",
        "830c675e-6b6f-4d35-b79e-55e162788157",
        "fe3a8e34-8a86-4a4e-bd6b-67c08f70b20e",
        "ced52c24-b0b5-45aa-9854-4fd9552c0fb4",
      ],
      "id": "7d308dad-4b48-46c0-9630-67d0b2172ab5",
      "owner": "SYSTEM",
      "status": "SUCCESS",
      "subscriptionId": "5d5a7101-6842-44ce-b6b9-17e659ee1e96",
      "type": "PAY_UPFRONT_FEE_V1",
    },
    {
      "data": {
        "applicationId": 960887750698565,
        "loanId": 4997177834543260,
      },
      "dependsOn": [
        "3fc469af-da3d-4b5a-83ca-08dbd5c0d296",
        "dea68de3-700e-4ab0-8797-51e543b79cfc",
      ],
      "id": "fc00b078-9615-4c87-a97c-7d0de27f412e",
      "owner": "SYSTEM",
      "status": "PENDING",
      "subscriptionId": "7b89d3e4-332f-47a7-922f-7605fbf286f1",
      "type": "CHECK_AFFORDABILITY_V1",
    },
    {
      "data": {
        "ppid": "4de8965c-b9ba-4653-9099-594a017f9f38",
      },
      "dependsOn": [
        "2c9e10d4-1c13-4541-a219-933178c5c311",
        "4dee8a8f-6088-4080-832c-27863ac8fc82",
        "bb32c8c9-6d5e-43fb-9eac-dfe3d3b33e49",
        "8d75258c-b338-4485-905b-d3ffced4632a",
        "7a4f5fcc-f2fc-4bfa-87e9-8be799689e2f",
        "9b499337-77c6-422b-b21d-408eecd385f4",
        "4f64bae9-cb33-4523-856c-90e851f07e56",
        "338aca2d-e664-449c-96d4-da3797cf9842",
        "042240e5-2ab1-443a-8e40-0483668feaa9",
        "fc6e2363-65fe-498e-91e0-e7e347e4167f",
        "e1a6e920-02fa-4980-a1d5-1d8195f0dbac",
        "9586cbf2-cda7-406e-b482-a37945f7d48a",
      ],
      "id": "40babaaa-1eed-4204-b5c6-7f61f1f1bd21",
      "owner": "SYSTEM",
      "status": "FAILURE",
      "subscriptionId": "2f62a747-5ec2-4648-9beb-1603322e9340",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
    {
      "data": {
        "ppid": "03a8ebcf-19dd-4c1e-aa0c-40d83812c8da",
      },
      "dependsOn": [
        "cc7700bb-126c-4e4e-a387-5c12b2a8fb1d",
        "ff89017e-0b95-49b0-8e9b-067983c7de1a",
        "c5b6533f-fee8-43d9-a0c9-49f36d094f0a",
        "8a782888-cd8a-4483-ad5c-e699abc69aca",
      ],
      "id": "f0fd1e7b-01a6-4690-aa35-b621f92d7157",
      "owner": "USER",
      "status": "PENDING",
      "subscriptionId": "3ac5bf32-ea1b-4fbc-b0db-d8be8b9e75e2",
      "type": "INSTALL_CHARGING_STATION_V1",
    },
  ],
  "activatedAt": "2025-01-01T00:00:00.000Z",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "deletedAt": "2025-01-01T00:00:00.000Z",
  "id": "39060126-353d-41f4-b037-32c1c25eaa4b",
  "order": {
    "address": {
      "line1": "cariosus curvo cruciamentum",
      "line2": "theologus sulum aufero",
      "line3": "subseco vinitor villa",
      "postcode": "patruus bos curatio",
    },
    "eCommerceId": "8c6fe907-f3e9-4f40-b60f-118c08471d61",
    "email": "<EMAIL>",
    "firstName": "Everett Cole",
    "id": "7a8a85d0-0074-4554-a595-53b279151a17",
    "lastName": "April Nicolas DVM",
    "mpan": "patria tricesimus adipisci",
    "orderedAt": "2025-01-01T00:00:00.000Z",
    "origin": "SALESFORCE",
    "phoneNumber": "defero condico creber",
  },
  "plan": {
    "allowanceMiles": 10000,
    "allowancePeriod": "ANNUAL",
    "contractDurationMonths": 18,
    "id": "71c5bd44-4889-4fd1-a03b-53bf21989bf2",
    "milesRenewalDate": Any<String>,
    "monthlyFeePounds": 35,
    "productCode": "deludo id vitiosus",
    "rateMilesPerKwh": 3.5,
    "ratePencePerMile": 2.28,
    "type": "POD_DRIVE",
    "upfrontFeePounds": 99,
  },
  "status": "REJECTED",
  "updatedAt": "2025-01-01T00:00:00.000Z",
  "userId": "5357be96-1495-4951-8046-c2d59ba76c33",
}
`;

exports[`mobile api subscriptions module GET /subscriptions/:subscriptionId/actions/:actionId should return 200 OK with an action 1`] = `
{
  "data": {
    "ppid": "32bc3615-e363-4a27-a857-bad79d00b8f7",
  },
  "dependsOn": [
    "41b3f669-194c-4b57-8300-14b63dde1d1d",
    "6d9fe1ce-e9a4-40e5-aed1-adc5041471b4",
    "6261197c-4b70-41f9-b2ba-d081c877fcdb",
    "c3ea434a-f5a6-4861-94b9-678859dfcf03",
    "7f87edae-0cd8-4ff3-9c53-f7d69af62772",
    "7aca9172-f712-467e-91d0-a18565579bca",
    "77c240ac-3fbd-4c85-948c-5ee38e14a21d",
    "c1c434ee-8165-481a-970e-86b3b558149f",
    "9147ae1d-b39b-4b56-bae7-66780e0f5de7",
    "9993faeb-818b-4e2a-b915-6265f48daa53",
    "4eaaa8e4-9cc5-47cc-9d4c-55cd0883c3f6",
  ],
  "id": "35ad554f-a19c-44ff-bf83-e80c3d998ac8",
  "owner": "SYSTEM",
  "status": "FAILURE",
  "subscriptionId": "15819c42-f1d3-4d82-a434-49052b676ab0",
  "type": "INSTALL_CHARGING_STATION_V1",
}
`;

exports[`mobile api subscriptions module GET /subscriptions/:subscriptionId/direct-debit should return stubbed direct debit 1`] = `
{
  "accountNumberLastDigits": "1234",
  "monthlyPaymentDay": 13,
  "nameOnAccount": "Mr Tom Wallace",
  "sortCodeLastDigits": "23",
}
`;

exports[`mobile api subscriptions module GET /subscriptions/:subscriptionId/documents should return stubbed documents 1`] = `
{
  "documents": [
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "ha",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
    {
      "active": true,
      "format": "PDF",
      "issued": "2025-05-28T18:28:21.959Z",
      "link": "/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48",
      "type": "rca",
    },
  ],
}
`;

exports[`mobile api subscriptions module GET /subscriptions/actions/CHECK_AFFORDABILITY_V1/form-data should return 200 with form data 1`] = `
{
  "id": "abc-123-456",
  "name": "Form",
  "sections": [
    {
      "fields": [
        {
          "id": "dateOfBirth",
          "label": "Date of birth",
          "type": "dateOfBirth",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "maritalStatus",
          "label": "Marital Status",
          "type": "radio",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "Married",
              "value": "married",
            },
            {
              "label": "Single",
              "value": "single",
            },
            {
              "label": "Cohabiting",
              "value": "cohabiting",
            },
            {
              "label": "Divorced",
              "value": "divorced",
            },
            {
              "label": "Separated",
              "value": "separated",
            },
            {
              "label": "Widowed",
              "value": "widowed",
            },
            {
              "label": "Other",
              "value": "other",
            },
          ],
        },
        {
          "id": "residentialStatus",
          "label": "Residential Status",
          "type": "radio",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "Homeowner",
              "value": "homeowner",
            },
            {
              "label": "Council Tenant",
              "value": "councilTenant",
            },
            {
              "label": "Private Tenant",
              "value": "privateTenant",
            },
            {
              "label": "Other",
              "value": "other",
            },
            {
              "label": "Living with parents or family",
              "value": "livingWithParentsOrFamily",
            },
            {
              "label": "House share",
              "value": "houseShare",
            },
          ],
        },
        {
          "dataType": "int",
          "id": "dependants",
          "label": "Number of dependants (incl. adult dependants)",
          "type": "inlineRadio",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "0",
              "value": 0,
            },
            {
              "label": "1",
              "value": 1,
            },
            {
              "label": "2",
              "value": 2,
            },
            {
              "label": "3+",
              "value": 3,
            },
          ],
        },
      ],
      "title": "Your details",
    },
    {
      "fields": [
        {
          "id": "employmentStatus",
          "label": "Employment Status",
          "type": "select",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "Employed - Full time",
              "value": "fullTime",
            },
            {
              "label": "Employed - Part time",
              "value": "partTime",
            },
            {
              "label": "On benefits",
              "value": "onBenefits",
            },
            {
              "label": "Retired",
              "value": "retired",
            },
            {
              "label": "Self employed",
              "value": "selfEmployed",
            },
            {
              "label": "Temporary employed",
              "value": "temporaryEmployed",
            },
            {
              "label": "Homemaker",
              "value": "homemaker",
            },
          ],
        },
        {
          "id": "salary",
          "label": "Monthly take-home salary",
          "placeholder": "E.g. £1,520",
          "type": "currency",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
      ],
      "title": "Employment & Income",
    },
    {
      "fields": [
        {
          "help": {
            "content": "This is **your contribution** toward:
- Rent and/or mortgage payments, as well as payments for council tax, gas, electricity, water, and other energy bills.",
            "title": "Monthly rent, mortgage & utility bills",
          },
          "id": "monthlyRent",
          "label": "Your monthly rent, mortgage & utility bills",
          "type": "currency",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "help": {
            "content": "This is **your contribution** toward:
- Transport costs (public transport, car/van expenses, fuel, servicing)
- Holidays, leisure and recreation (including streaming services and reading materials)
- Medical expenses
- Education (including school trips and ad hoc costs)
- Clothing, and personal care (toiletries, beauty products, hairdressing, etc.)",
            "title": "Your monthly travel & living expenses",
          },
          "id": "monthlyTravelLivingExpenses",
          "label": "Your monthly travel & living expenses",
          "type": "currency",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "help": {
            "content": "This is **your contribution** toward:
- Regular household maintenance (cleaning and repairs) including your contribution to phone and internet costs",
            "title": "Your monthly household expenses",
          },
          "id": "monthlyHouseholdExpenses",
          "label": "Your monthly household expenses",
          "type": "currency",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "help": {
            "content": "These are **payments for:**
- Payday loads and other loans, credit cards, store cards, home credit, catalogues, retail finance, car finance, insurances etc...",
            "title": "Your monthly credit commitments",
          },
          "id": "monthlyCreditPayments",
          "label": "Your monthly credit payments",
          "type": "currency",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
      ],
      "subtitle": "If you share any expenses, please enter **your contributions**. Take into consideration any increases in the next 6 months (e.g., new car payments).",
      "title": "Monthly expenditure",
    },
  ],
}
`;

exports[`mobile api subscriptions module GET /subscriptions/actions/SETUP_DIRECT_DEBIT_V1/form-data should return 200 with form data 1`] = `
{
  "id": "direct-debit",
  "name": "Direct Debit",
  "sections": [
    {
      "fields": [
        {
          "id": "accountNumber",
          "label": "Account Number",
          "type": "bankAccountNumber",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "sortCode",
          "label": "Sort Code",
          "type": "sortCode",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "nameOnAccount",
          "label": "Name on Account",
          "type": "text",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "emailAddress",
          "label": "Email Address",
          "type": "email",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "phoneNumber",
          "label": "Phone number",
          "type": "phoneNumber",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
      ],
      "title": "Account details",
    },
    {
      "fields": [
        {
          "id": "country",
          "label": "Country",
          "type": "select",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "England",
              "value": "en",
            },
            {
              "label": "Scotland",
              "value": "sl",
            },
          ],
        },
        {
          "id": "addressLine1",
          "label": "Address Line 1",
          "type": "text",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "addressLine2",
          "label": "Address Line 2",
          "type": "text",
        },
        {
          "id": "cityOrTown",
          "label": "City or Town",
          "type": "text",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": "postCode",
          "label": "Post Code",
          "type": "postCode",
          "validators": [
            {
              "type": "required",
            },
          ],
        },
        {
          "id": " confirmationStatementCheck",
          "type": "checkbox",
          "validators": [
            {
              "type": "required",
            },
          ],
          "values": [
            {
              "label": "I confirm I am the account holder and the only person required to authorise debits from this account.",
              "value": true,
            },
          ],
        },
        {
          "id": " directDebitGuarantee",
          "type": "infoMarkdown",
          "values": [
            {
              "value": "By pressing 'sign' I understand that payments are protected by the [Direct Debit Guarantee](launchDirectDebitDepositGuaranteeModal).",
            },
          ],
        },
      ],
      "title": "Billing Address",
    },
  ],
}
`;

exports[`mobile api subscriptions module POST /subscriptions/actions/SETUP_DIRECT_DEBIT_V1/confirmation-of-payee should return a 200 OK with COP response 1`] = `
{
  "accountName": "Mr Pod Point",
  "reason": "account_name_does_not_match",
  "status": "not_matched",
}
`;

exports[`mobile api support module support controller should return a 201 if the email is sent 1`] = `
{
  "message": "Se han enviado comentarios a soporte con éxito.",
}
`;

exports[`mobile api users module users controller GET / should return a 200 if the user is authenticated 1`] = `
{
  "accountCreationTimestamp": "2023-08-24T10:14:52.000Z",
  "balance": {
    "amount": 1000,
    "currency": "GBP",
  },
  "deletedAtTimestamp": null,
  "email": "<EMAIL>",
  "emailVerified": true,
  "first_name": "James",
  "lastSignInTimestamp": "2023-08-24T10:14:52.000Z",
  "last_name": "Bond",
  "locale": "en",
  "paymentProcessorId": null,
  "preferences": {
    "unitOfDistance": "mi",
  },
  "rewards": null,
  "status": "active",
  "uid": "123",
}
`;

exports[`mobile api users module users controller POST /enode/link should return a 200 if the user is authenticated (no vendor) 1`] = `
{
  "linkToken": "U2FtcGxlIFNESyB0b2tlbgpTYW1wbGUgU0RLIHRva2VuClNhbXBsZSBTREsgdG9rZW4KU2FtcGxlIFNESyB0b2tlbg==",
  "linkUrl": "https://link.enode.com/YzIwZThhYjYtMjMzMi00ZTAyLTg0OTYtYzdjOTlhZTY3Zjc3QDI2YzI1MDExLTdhYTctNGE2NS1iNjBmLTZmMzc5NmRhODUyMDowNDViYjFiYmE0M2Y5NDU5YTc5OTgxZmEyYTg1NmI4YzhkOGU4YjgyNmNmMzQzZmFmMGNhZTlmNDBjMmZmOTgy",
}
`;

exports[`mobile api users module users controller POST /enode/link should return a 200 if the user is authenticated (with vendor) 1`] = `
{
  "linkToken": "U2FtcGxlIFNESyB0b2tlbgpTYW1wbGUgU0RLIHRva2VuClNhbXBsZSBTREsgdG9rZW4KU2FtcGxlIFNESyB0b2tlbg==",
  "linkUrl": "https://link.enode.com/YzIwZThhYjYtMjMzMi00ZTAyLTg0OTYtYzdjOTlhZTY3Zjc3QDI2YzI1MDExLTdhYTctNGE2NS1iNjBmLTZmMzc5NmRhODUyMDowNDViYjFiYmE0M2Y5NDU5YTc5OTgxZmEyYTg1NmI4YzhkOGU4YjgyNmNmMzQzZmFmMGNhZTlmNDBjMmZmOTgy",
}
`;

exports[`mobile api users module users controller POST /login/alert should return a 200 if a valid token is passed 1`] = `""`;

exports[`mobile api vehicles module GET /vehicles/:vehicleId/interventions should return a 200 OK status on success 1`] = `
{
  "chargeState": [
    {
      "brand": "aptus arca veritatis",
      "domain": "Device",
      "id": "26a60de5-d692-481e-a912-f5cfa8ff925e",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "aestivus copia atavus",
      "vendorType": "aetas dignissimos tergiversatio",
    },
    {
      "brand": "aequitas cursus inflammatio",
      "domain": "Device",
      "id": "f91f67c8-2745-4f4c-997a-3bc57ae4eb0d",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "arma condico trado",
      "vendorType": "sub cerno tunc",
    },
    {
      "brand": "repudiandae circumvenio odio",
      "domain": "Account",
      "id": "491dd136-a88a-49fd-a49d-d9b8ff8ef122",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "considero utpote sperno",
      "vendorType": "vulgaris comptus ultio",
    },
    {
      "brand": "minima valetudo texo",
      "domain": "Device",
      "id": "46cf8fb3-7781-4c33-8dbd-b0a7b5080f28",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "sunt unde bellum",
      "vendorType": "appositus terreo trepide",
    },
    {
      "brand": "solutio cuius considero",
      "domain": "Account",
      "id": "0a42d52d-a6d2-4b8b-aef4-7ab93e5673e4",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "cerno aliqua cursus",
      "vendorType": "coniecto soluta angelus",
    },
    {
      "brand": "civis balbus expedita",
      "domain": "Account",
      "id": "489eb9d1-92dc-4040-9462-e131cd6c1fbb",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "adeptio contego angustus",
      "vendorType": "aetas arcesso odio",
    },
    {
      "brand": "sonitus decumbo suadeo",
      "domain": "Account",
      "id": "8a2bd038-587d-4998-9096-c9a6f26d000f",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "ulciscor denuncio succedo",
      "vendorType": "certus verto crapula",
    },
    {
      "brand": "despecto adficio benigne",
      "domain": "Account",
      "id": "c9775ea7-e7b8-484d-a154-c39e3b5aaa4e",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "ascisco candidus victus",
      "vendorType": "beatae uterque cruentus",
    },
    {
      "brand": "illo volva cupio",
      "domain": "Account",
      "id": "b815e309-6a5e-41e0-86e6-4d548d83642e",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "ea stabilis comprehendo",
      "vendorType": "paens cubo nesciunt",
    },
    {
      "brand": "terminatio sursum agnitio",
      "domain": "Device",
      "id": "fe3df7a1-16b7-4910-9748-018b1a045343",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "abbas venio adaugeo",
      "vendorType": "cultura caput contigo",
    },
    {
      "brand": "thesis umquam repudiandae",
      "domain": "Device",
      "id": "42b2b1aa-5f34-43b2-b748-2de8ffb127e1",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "viridis sunt animadverto",
      "vendorType": "termes valeo vapulus",
    },
  ],
  "information": [
    {
      "brand": "ustulo amitto tres",
      "domain": "Account",
      "id": "324de16b-7eea-4f3c-bb75-2ab22bca2187",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "curo deorsum amplexus",
      "vendorType": "vitiosus clamo officia",
    },
    {
      "brand": "vulpes nesciunt blanditiis",
      "domain": "Device",
      "id": "9fae4279-0f50-42e6-8b29-cbd32e58d276",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "talis vulnero addo",
      "vendorType": "adduco avaritia tendo",
    },
    {
      "brand": "ancilla corrupti tero",
      "domain": "Account",
      "id": "71b11edf-3b7c-446c-8a20-37e0180f7df3",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "volubilis confugo beatae",
      "vendorType": "at cohibeo cursim",
    },
    {
      "brand": "causa tamdiu sunt",
      "domain": "Device",
      "id": "4beca2b9-47c3-41a6-86e0-421949fbe0d1",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "tabula agnosco dolorum",
      "vendorType": "amita depereo color",
    },
    {
      "brand": "callide vindico audacia",
      "domain": "Account",
      "id": "7edba647-da59-4904-8d70-7a76999ab578",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "cohaero ad facere",
      "vendorType": "voluptatum arbitro supra",
    },
    {
      "brand": "ago concido vis",
      "domain": "Account",
      "id": "5e05f0dc-f9d0-4642-a011-cd4b3accca8a",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "cometes speciosus sublime",
      "vendorType": "cohaero tredecim aegrotatio",
    },
    {
      "brand": "condico aetas assentator",
      "domain": "Account",
      "id": "28d665e4-db9b-417b-9521-2802a37d0e41",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "teneo alioqui collum",
      "vendorType": "vallum varius cerno",
    },
    {
      "brand": "capio libero auctor",
      "domain": "Account",
      "id": "5cf6c3db-e2e2-42fd-aad0-69a9d8d6e242",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "vestigium celo desino",
      "vendorType": "contigo calculus suspendo",
    },
    {
      "brand": "deleniti adinventitias damno",
      "domain": "Device",
      "id": "354d1b6f-f386-42e3-9d15-d5f54e942ddb",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "ratione nam denique",
      "vendorType": "acquiro eligendi calculus",
    },
    {
      "brand": "suus umquam villa",
      "domain": "Account",
      "id": "ac4251f3-8941-46e9-a62c-1f324fdaed96",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "temporibus quaerat absum",
      "vendorType": "adeo amplus tamquam",
    },
    {
      "brand": "sequi ad tergeo",
      "domain": "Device",
      "id": "9567c9e6-12e1-4de9-8ebc-b6ae824d8785",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "abduco adinventitias averto",
      "vendorType": "adaugeo deludo vir",
    },
    {
      "brand": "trans sopor valeo",
      "domain": "Device",
      "id": "c894706a-e24a-47ac-aabf-973eb9e6e80f",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "correptius agnosco annus",
      "vendorType": "ubi suscipio doloremque",
    },
    {
      "brand": "caste calculus talio",
      "domain": "Device",
      "id": "52f57ded-75ad-49be-9d19-5c7c93a8c84d",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "conitor vel sollicito",
      "vendorType": "video provident tantum",
    },
    {
      "brand": "utique sumptus degero",
      "domain": "Account",
      "id": "655ba852-7ce2-45e2-b53a-f508fce74473",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "asper conturbo vel",
      "vendorType": "tergum tutis video",
    },
    {
      "brand": "antiquus cariosus defluo",
      "domain": "Account",
      "id": "e1edf470-40da-4d03-a029-0ad59be1e2e6",
      "introducedAt": "2024-08-25T20:05:46.646Z",
      "resolution": {
        "access": "Physical",
        "agent": "Device",
        "description": "Replace the battery with a new one",
        "title": "Replace battery",
      },
      "vendor": "verbum comes surculus",
      "vendorType": "cupressus annus cruentus",
    },
  ],
}
`;

exports[`mobile api vehicles module GET /vehicles/:vehicleId/interventions/:interventionId should return a 200 OK status on success 1`] = `
{
  "brand": "stultus benigne sum",
  "domain": "Account",
  "id": "2864c6cf-77d2-4c7d-b456-2878955ceb18",
  "introducedAt": "2024-08-25T20:05:46.646Z",
  "resolution": {
    "access": "Physical",
    "agent": "Device",
    "description": "Replace the battery with a new one",
    "title": "Replace battery",
  },
  "vendor": "deripio supra eos",
  "vendorType": "conduco curatio causa",
}
`;

exports[`mobile api warranties module GET /warranties/:ppid should return a 200 OK with warranty 1`] = `
{
  "endDate": "2026-07-01",
  "productCode": "LIFETIME_48_HOURS",
  "slaHours": 48,
  "startDate": "2025-01-01",
}
`;
