import { AccountDetailsDto } from '../dto/account-details.dto';
import {
  AccountEntity,
  AccountEntityType,
} from '../../domain/entities/account.entity';
import { AccountRepositoryInterface } from '../../domain/repositories/account.repository.interface';
import {
  CleanLogger,
  CouldNotFindEntityError,
  InvalidEntityError,
  TransactionProviderInterface,
} from '@experience/mobile/clean-architecture';
import { DependencyInjectionToken } from '../../modules/constants';
import { FatalApplicationError } from '../errors/fatal-application.error';
import { Inject, forwardRef } from '@nestjs/common';
import {
  InvalidTransfer,
  InvalidTransferError,
} from '../../domain/errors/invalid-transfer.error';
import { ProcessWalletActionDTO } from '../dto/process-wallet-action.dto';
import { SubscriptionPlanDTO } from '../dto/subscription-plan.dto';
import {
  TransactionEntity,
  TransactionEntityCurrency,
  TransactionEntityStatus,
  TwoWayTransaction,
} from '../../domain/entities/transaction.entity';
import { TransactionService } from './transaction.service';
import { WalletActionReference } from '../../interfaces/http/dto/wallet-action.dto';
import { WalletEntity } from '../../domain/entities/wallet.entity';
import { WalletService } from './wallet.service';
import { v4 } from 'uuid';

export class AccountService {
  constructor(
    @Inject(DependencyInjectionToken.ACCOUNTS_SERVICE_LOGGER)
    private readonly logger: CleanLogger,
    @Inject(DependencyInjectionToken.ACCOUNTS_REPOSITORY)
    private readonly accountsRepository: AccountRepositoryInterface,
    @Inject(TransactionService)
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => WalletService))
    private readonly walletService: WalletService,
    @Inject(DependencyInjectionToken.TRANSACTION_PROVIDER)
    private readonly transactionProvider: TransactionProviderInterface
  ) {}

  async getAccountDetails(
    userId: string,
    type: AccountEntity['type']
  ): Promise<AccountDetailsDto> {
    try {
      this.logger.log({ userId, type }, 'retrieving account');

      const wallet = await this.walletService.readByUserId(userId);

      if (!wallet) {
        throw new CouldNotFindEntityError(
          {
            userId,
          },
          `could not find wallet for user ${userId}`
        );
      }

      const account =
        type == AccountEntityType.ALLOWANCE
          ? await this.accountsRepository.getAllowanceAccount(wallet.id)
          : await this.accountsRepository.getRewardsAccount(wallet.id);

      const transactions = await this.transactionService.getAccountTransactions(
        account.id
      );

      const balanceTransactions = transactions.filter(
        (tx) => tx.status === TransactionEntityStatus.COMPLETED
      );

      return AccountDetailsDto.from(account, balanceTransactions, transactions);
    } catch (error) {
      this.logger.error({ userId, type, error }, 'failed to retrieve account');

      throw error;
    }
  }

  async createDefaultUserAccounts(
    walletId: WalletEntity['id'],
    subscriptionPlan: SubscriptionPlanDTO
  ): Promise<void> {
    try {
      await this.transactionProvider.withTransaction(async () => {
        this.logger.log({ walletId }, 'creating user allowance account');
        const allowanceAccount = await this.accountsRepository.create({
          rewardWalletId: walletId,
          type: AccountEntityType.ALLOWANCE,
        });

        this.logger.log({ walletId }, 'creating user rewards account');

        await this.accountsRepository.create({
          rewardWalletId: walletId,
          type: AccountEntityType.REWARDS,
        });

        this.logger.log(
          { walletId, allowanceAccountId: allowanceAccount.id },
          'setting initial allowance'
        );

        await this.setInitialAllowance(allowanceAccount, subscriptionPlan);

        this.logger.log(
          { walletId, allowanceAccountId: allowanceAccount.id },
          'user accounts created'
        );
      });
    } catch (error) {
      this.logger.error({ walletId, error }, 'error creating user accounts');
      throw new FatalApplicationError('Error creating user accounts');
    }
  }

  async processRewardWithdrawal(
    userId: string,
    idempotencykey: string
  ): Promise<TwoWayTransaction> {
    const existingTransaction =
      await this.transactionService.getTransactionsPairByIdempotentKey(
        idempotencykey
      );

    if (existingTransaction) {
      return existingTransaction;
    }

    const userWallet = await this.walletService.readByUserIdOrFail(userId);
    const systemWallet = await this.walletService.getSystemWallet();
    const userAccount = await this.accountsRepository.getRewardsAccount(
      userWallet.id
    );
    const systemAccount = await this.accountsRepository.getSystemAccount(
      systemWallet.id
    );
    const balance = await this.transactionService.getAccountBalance(
      userAccount.id
    );

    if (balance <= 0) {
      this.logger.error(
        { userWallet, userAccount },
        'user account balance is empty'
      );
      throw new InvalidEntityError(
        { id: userAccount.id },
        'User account balance is empty'
      );
    }

    this.logger.log(
      {
        wallet: userWallet,
        account: userAccount,
      },
      'emptying rewards account'
    );

    const transactions = await this.transfer(
      userAccount,
      systemAccount,
      balance,
      TransactionEntityCurrency.MILES,
      idempotencykey,
      WalletActionReference.REWARDS_WITHDRAWAL
    );

    this.logger.log(
      {
        wallet: userWallet,
        account: userAccount,
      },
      'successfully emptied rewards account'
    );

    return transactions;
  }

  async transfer(
    sourceAccount: AccountEntity,
    destinationAccount: AccountEntity,
    amount: number,
    currency: TransactionEntityCurrency,
    idempotencyKey: TransactionEntity['idempotencyKey'],
    reference?: string
  ): Promise<TwoWayTransaction> {
    const context = {
      sourceAccountId: sourceAccount.id,
      destinationAccountId: destinationAccount.id,
      amount,
      currency,
    };

    if (amount < 0) {
      this.logger.error(context, 'Amount must be positive');
      throw new InvalidTransferError(InvalidTransfer.NEGATIVE_AMOUNT);
    }

    if (sourceAccount.id === destinationAccount.id) {
      this.logger.error(
        context,
        'Source and destination accounts are the same'
      );
      throw new InvalidTransferError(InvalidTransfer.MATCHING_ACCOUNTS);
    }

    const sourceBalance = await this.transactionService.getAccountBalance(
      sourceAccount.id
    );

    if (
      sourceBalance === 0 &&
      sourceAccount.type !== AccountEntityType.SYSTEM_MILES
    ) {
      this.logger.error(
        { ...context, sourceBalance },
        'Insufficient funds for transfer'
      );

      throw new InvalidTransferError(InvalidTransfer.INSUFFICIENT_FUNDS);
    }

    if (sourceBalance < amount) {
      switch (sourceAccount.type) {
        // System Miles may always transfer
        case AccountEntityType.SYSTEM_MILES:
          break;

        // Allowance accounts can transfer up to their balance
        case AccountEntityType.ALLOWANCE:
          this.logger.log(
            { ...context, sourceBalance },
            'Insufficient balance for transfer, transferring remaining source balance'
          );
          amount = sourceBalance;
          break;

        // Anything else isn't allowed to transfer without sufficient balance
        default:
          this.logger.error(
            { ...context, sourceBalance },
            'Insufficient funds for transfer'
          );

          throw new InvalidTransferError(InvalidTransfer.INSUFFICIENT_FUNDS);
      }
    }

    return await this.transactionService.transfer(
      sourceAccount.id,
      destinationAccount.id,
      amount,
      currency,
      idempotencyKey,
      reference
    );
  }

  private async setInitialAllowance(
    account: AccountEntity,
    { allowanceMiles }: SubscriptionPlanDTO
  ): Promise<void> {
    const systemWallet = await this.walletService.getSystemWallet();
    const systemMilesAccount = await this.accountsRepository.getSystemAccount(
      systemWallet.id
    );
    const idempotencyKey = v4();

    this.logger.log(
      {
        accountId: account.id,
        allowanceMiles,
      },
      'setting initial allowance'
    );

    await this.transfer(
      systemMilesAccount,
      account,
      allowanceMiles,
      TransactionEntityCurrency.MILES,
      idempotencyKey,
      WalletActionReference.INITIAL_ALLOWANCE
    );
  }

  async processRewardAccrual(
    userId: string,
    payload: ProcessWalletActionDTO
  ): Promise<TwoWayTransaction> {
    this.logger.log({ userId, ...payload }, 'processing rewards accrual');

    const existingTransaction =
      await this.transactionService.getTransactionsPairByIdempotentKey(
        payload.idempotencyKey
      );

    if (existingTransaction) {
      return existingTransaction;
    }

    const wallet = await this.walletService.readByUserIdOrFail(userId);
    const allowanceAccount = await this.accountsRepository.getAllowanceAccount(
      wallet.id
    );
    const rewardsAccount = await this.accountsRepository.getRewardsAccount(
      wallet.id
    );

    this.logger.log(
      {
        userId,
        allowanceAccountId: allowanceAccount.id,
        rewardsAccountId: rewardsAccount.id,
        ...payload,
      },
      'got user allowance and rewards accounts'
    );

    const transactionEntities = await this.transfer(
      allowanceAccount,
      rewardsAccount,
      payload.amount,
      payload.currency,
      payload.idempotencyKey,
      WalletActionReference.REWARDS_ACCRUAL
    );

    this.logger.log(
      {
        userId,
        allowanceAccountId: allowanceAccount.id,
        rewardsAccountId: rewardsAccount.id,
        ...payload,
      },
      'successfully processed reward accrual'
    );

    return transactionEntities;
  }
}
