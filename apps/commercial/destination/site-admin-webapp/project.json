{"name": "destination-site-admin-webapp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/commercial/destination/site-admin-webapp", "projectType": "application", "tags": ["commercial", "package", "site-admin"], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/.aws/**/destination-site-admin-webapp/*"]}, "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/commercial/destination/site-admin-webapp", "assets": [{"input": "./assets/site-admin-webapp/", "output": "./", "glob": "**/*.*"}, {"input": "./assets/shared/", "output": "./", "glob": "**/*.*"}], "generateLockfile": true}, "configurations": {"development": {"outputPath": "apps/commercial/destination/site-admin-webapp"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "destination-site-admin-webapp:build", "dev": true, "port": 4201}, "configurations": {"development": {"buildTarget": "destination-site-admin-webapp:build:development", "dev": true}, "production": {"buildTarget": "destination-site-admin-webapp:build:production", "dev": false}}}, "serve:e2e": {"executor": "nx:run-commands", "options": {"command": "nx run destination-site-admin-webapp:build:production && nx run destination-site-admin-webapp:serve:production"}}, "compose": {"executor": "nx:run-commands", "dependsOn": ["build"], "defaultConfiguration": "up", "configurations": {"up": {"commands": ["docker compose --compatibility -f apps/commercial/destination/site-admin-webapp/docker-compose.yml up"]}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "destination-site-admin-webapp:build:production"}}, "smoke-test": {"executor": "nx:run-commands", "options": {"args": "--baseUrl=http://host.docker.internal:4201", "commands": ["docker run --rm -i -e BASE_URL={args.baseUrl} grafana/k6 run - <apps/commercial/destination/site-admin-webapp/smoke-test.js"], "parallel": false}, "configurations": {"ecs": {"args": "--baseUrl=http://destination-site-admin-webapp.destination.cluster.com:4201"}}}}}