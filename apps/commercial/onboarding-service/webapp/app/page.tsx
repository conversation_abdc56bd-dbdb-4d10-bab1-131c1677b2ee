import {
  Anchor,
  Heading,
  HeadingSizes,
  Paragraph,
  PodPointLogo,
} from '@experience/shared/react/design-system';
import Image from 'next/image';

const Index = () => (
  <>
    <div className="min-h-screen flex flex-col md:flex-row">
      <div className="md:hidden p-4 mb-12">
        <PodPointLogo width="172px" height="47px" />
      </div>
      <div className="hidden md:flex items-center justify-center md:w-1/2 lg:w-2/3 p-5 bg-black bg-center bg-cover bg-[url('/hero-background.png')]"></div>
      <div className="flex flex-1 justify-center md:items-center p-5 bg-white">
        <div className="flex flex-col items-center">
          <div className="max-w-full w-80">
            <main className="flex-col space-y-4 text-center">
              <Heading.H1 className="font-semibold">Welcome to Pod</Heading.H1>
              <Heading.H2 fontSize={HeadingSizes.S} className="font-semibold">
                Drivers
              </Heading.H2>
              <Paragraph>Download the app to get started</Paragraph>
              <div className="flex items-center">
                <Anchor
                  href="https://apps.apple.com/gb/app/pod-point/id996063316?uo=4&mt=8"
                  isNativeLink
                  rel="noreferrer"
                  target="_blank"
                >
                  <Image
                    src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-example-preferred.png"
                    alt="Download on the Apple store"
                    width={160}
                    height={54}
                  />
                </Anchor>
                <Anchor
                  href="https://play.google.com/store/apps/details?id=com.podpoint"
                  isNativeLink
                  rel="noreferrer"
                  target="_blank"
                >
                  <Image
                    alt="Get it on Google Play"
                    height={80}
                    src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png"
                    width={206}
                  />
                </Anchor>
              </div>
              <div className="relative flex items-center">
                <div className="flex-grow border-t border-neutral"></div>
              </div>
              <Heading.H2 fontSize={HeadingSizes.S} className="font-semibold">
                Administrators
              </Heading.H2>
              <Paragraph>
                Visit the{' '}
                <Anchor
                  href="https://sites.pod-point.com"
                  isNativeLink={true}
                  target="_blank"
                >
                  Site Management Service
                </Anchor>
              </Paragraph>
              <Image
                alt="Site Management Service"
                className="w-full"
                height={0}
                src="https://cdn-www.pod-point.com/workplace-feature-2-04.svg"
                width={0}
                priority={true}
              />
            </main>
          </div>
        </div>
      </div>
    </div>
  </>
);
export default Index;
