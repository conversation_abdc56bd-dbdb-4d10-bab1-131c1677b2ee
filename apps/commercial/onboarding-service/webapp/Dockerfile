###################
# BUILD
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb AS builder

WORKDIR /usr/src/app

COPY dist/apps/commercial/onboarding-service/webapp/package*.json ./

RUN npm ci && npm cache clean --force

###################
# PRODUCTION
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb

ARG APPLICATION_VERSION
ARG SENTRY_RELEASE

ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE

# Add node modules from build image
COPY --chown=node:node --from=builder /usr/src/app/node_modules ./node_modules

# Add application source code
COPY --chown=node:node dist/apps/commercial/onboarding-service/webapp/ .

USER node

CMD [ "npx", "next", "start", "-p", "8101", "--keepAliveTimeout", "75000" ]

EXPOSE 8101
