###################
# BUILD
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb AS builder

WORKDIR /usr/src/app

COPY dist/apps/commercial/statement-service/api/package*.json ./

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

RUN npm ci && npm cache clean --force

###################
# PRODUCTION
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5340cbfc2df14331ab021555fdd9f83f072ce811488e705b0e736b11adeec4bb

ARG APPLICATION_VERSION
ARG SENTRY_RELEASE

ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE

ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Add OpenSSL for Prisma
RUN apk add --no-cache openssl

# Install Chromium dependencies for Puppeteer
RUN apk --no-cache add \
  chromium \
  nss \
  freetype \
  freetype-dev \
  harfbuzz \
  ca-certificates \
  ttf-freefont

# Add RDS CA certs to trust store to be able to connect to RDS over TLS
ADD https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem global-bundle.pem
RUN cat global-bundle.pem >> /etc/ssl/certs/ca-certificates.crt && rm global-bundle.pem

# Add node modules from build image
COPY --chown=node:node --from=builder /usr/src/app/node_modules ./node_modules

# Add assets required by the application at runtime
COPY --chown=node:node assets/statement-service-api ./assets/statement-service-api
COPY --chown=node:node node_modules/@prisma/clients/statements ./node_modules/@prisma/clients/statements
COPY --chown=node:node libs/commercial/statement-service/prisma/statements/schema/prisma ./libs/commercial/statement-service/prisma/statements/schema/prisma
RUN npx prisma generate --schema libs/commercial/statement-service/prisma/statements/schema/prisma/schema.prisma

# Add application source code
COPY --chown=node:node dist/apps/commercial/statement-service/api .

USER node

CMD [ "node", "main.js" ]

EXPOSE 5102
