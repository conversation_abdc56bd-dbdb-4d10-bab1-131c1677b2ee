// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type Access string

const (
	AccessHome    Access = "home"
	AccessPublic  Access = "public"
	AccessPrivate Access = "private"
)

func (e *Access) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Access(s)
	case string:
		*e = Access(s)
	default:
		return fmt.Errorf("unsupported scan type for Access: %T", src)
	}
	return nil
}

type NullAccess struct {
	Access Access `json:"access"`
	Valid  bool   `json:"valid"` // Valid is true if Access is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullAccess) Scan(value interface{}) error {
	if value == nil {
		ns.Access, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Access.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullAccess) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Access), nil
}

type SubmittedChargeStatus string

const (
	SubmittedChargeStatusNEW       SubmittedChargeStatus = "NEW"
	SubmittedChargeStatusPROCESSED SubmittedChargeStatus = "PROCESSED"
)

func (e *SubmittedChargeStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = SubmittedChargeStatus(s)
	case string:
		*e = SubmittedChargeStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for SubmittedChargeStatus: %T", src)
	}
	return nil
}

type NullSubmittedChargeStatus struct {
	SubmittedChargeStatus SubmittedChargeStatus `json:"submitted_charge_status"`
	Valid                 bool                  `json:"valid"` // Valid is true if SubmittedChargeStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullSubmittedChargeStatus) Scan(value interface{}) error {
	if value == nil {
		ns.SubmittedChargeStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.SubmittedChargeStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullSubmittedChargeStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.SubmittedChargeStatus), nil
}

type Term string

const (
	TermDuration Term = "duration"
	TermEnergy   Term = "energy"
	TermFixed    Term = "fixed"
)

func (e *Term) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Term(s)
	case string:
		*e = Term(s)
	default:
		return fmt.Errorf("unsupported scan type for Term: %T", src)
	}
	return nil
}

type NullTerm struct {
	Term  Term `json:"term"`
	Valid bool `json:"valid"` // Valid is true if Term is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTerm) Scan(value interface{}) error {
	if value == nil {
		ns.Term, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Term.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTerm) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Term), nil
}

type UserType string

const (
	UserTypePublic  UserType = "public"
	UserTypeMembers UserType = "members"
	UserTypeDrivers UserType = "drivers"
)

func (e *UserType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = UserType(s)
	case string:
		*e = UserType(s)
	default:
		return fmt.Errorf("unsupported scan type for UserType: %T", src)
	}
	return nil
}

type NullUserType struct {
	UserType UserType `json:"user_type"`
	Valid    bool     `json:"valid"` // Valid is true if UserType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullUserType) Scan(value interface{}) error {
	if value == nil {
		ns.UserType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.UserType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullUserType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.UserType), nil
}

type AggregateStatisticsChargesLkUp struct {
	ChargeID          int32         `json:"charge_id"`
	GroupUid          string        `json:"group_uid"`
	LocationID        int32         `json:"location_id"`
	AddressID         int32         `json:"address_id"`
	PresentmentAmount sql.NullInt32 `json:"presentment_amount"`
}

type CarbonIntensityDnoRegion struct {
	ID         int16       `json:"id"`
	ShortName  string      `json:"short_name"`
	DnoRegion  string      `json:"dno_region"`
	Boundaries interface{} `json:"boundaries"`
}

type CarbonIntensityRegionalForecastPeriod struct {
	DnoRegionID int16           `json:"dno_region_id"`
	StartTime   time.Time       `json:"start_time"`
	UpdatedAt   time.Time       `json:"updated_at"`
	Data        json.RawMessage `json:"data"`
}

type CommercialSubmittedCharge struct {
	ID             int64                 `json:"id"`
	OrganisationID int64                 `json:"organisation_id"`
	DriverID       int64                 `json:"driver_id"`
	ChargeID       int64                 `json:"charge_id"`
	SubmittedAt    time.Time             `json:"submitted_at"`
	CreatedBy      string                `json:"created_by"`
	CreatedAt      time.Time             `json:"created_at"`
	Status         SubmittedChargeStatus `json:"status"`
	ProcessedBy    sql.NullInt64         `json:"processed_by"`
	ProcessedAt    sql.NullTime          `json:"processed_at"`
}

type EventStoreClaimedHistoricEvent struct {
	ID           int64           `json:"id"`
	Data         json.RawMessage `json:"data"`
	ProcessedAt  sql.NullTime    `json:"processed_at"`
	ChargeEndsAt sql.NullTime    `json:"charge_ends_at"`
}

type EventStoreEnergyCostCurrencyUpdatedEvent struct {
	ID          int64           `json:"id"`
	Data        json.RawMessage `json:"data"`
	ProcessedAt sql.NullTime    `json:"processed_at"`
	UnpluggedAt sql.NullTime    `json:"unplugged_at"`
}

type EventStoreEvent struct {
	ID            int64           `json:"id"`
	TransactionID interface{}     `json:"transaction_id"`
	AggregateID   uuid.UUID       `json:"aggregate_id"`
	Version       int32           `json:"version"`
	Data          json.RawMessage `json:"data"`
}

type EventStoreEventSubscription struct {
	SubscriptionName  string      `json:"subscription_name"`
	LastTransactionID interface{} `json:"last_transaction_id"`
	LastEventID       int64       `json:"last_event_id"`
}

type EventStoreExpensedHistoricEvent struct {
	ID           int64           `json:"id"`
	Data         json.RawMessage `json:"data"`
	ProcessedAt  sql.NullTime    `json:"processed_at"`
	ChargeEndsAt sql.NullTime    `json:"charge_ends_at"`
}

type EventStoreHistoricEvent struct {
	ID          int64           `json:"id"`
	Data        json.RawMessage `json:"data"`
	ProcessedAt sql.NullTime    `json:"processed_at"`
}

type EventStoreLinkedHistoricCommand struct {
	ID          int64           `json:"id"`
	Data        json.RawMessage `json:"data"`
	ProcessedAt sql.NullTime    `json:"processed_at"`
	LinkedAt    sql.NullTime    `json:"linked_at"`
}

type EventStoreSolarEnergyCostRecalculatedHistoric struct {
	ID          int64           `json:"id"`
	Data        json.RawMessage `json:"data"`
	ProcessedAt sql.NullTime    `json:"processed_at"`
	UnpluggedAt sql.NullTime    `json:"unplugged_at"`
}

type PodpointAuthoriser struct {
	ID        int64          `json:"id"`
	Uid       string         `json:"uid"`
	Type      string         `json:"type"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
	GroupUid  sql.NullString `json:"group_uid"`
}

type PodpointBillingAccount struct {
	ID                 int64          `json:"id"`
	UserID             sql.NullInt64  `json:"user_id"`
	Uid                string         `json:"uid"`
	Balance            int32          `json:"balance"`
	Currency           string         `json:"currency"`
	BusinessName       string         `json:"business_name"`
	Line1              string         `json:"line_1"`
	Line2              string         `json:"line_2"`
	PostalTown         string         `json:"postal_town"`
	Postcode           string         `json:"postcode"`
	Country            string         `json:"country"`
	Phone              sql.NullString `json:"phone"`
	Mobile             sql.NullString `json:"mobile"`
	PaymentProcessorID sql.NullString `json:"payment_processor_id"`
	CreatedAt          sql.NullTime   `json:"created_at"`
	UpdatedAt          sql.NullTime   `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
}

type PodpointBillingEvent struct {
	ID                  int64          `json:"id"`
	AccountID           sql.NullInt64  `json:"account_id"`
	PresentmentAmount   int32          `json:"presentment_amount"`
	PresentmentCurrency string         `json:"presentment_currency"`
	ExchangeRate        string         `json:"exchange_rate"`
	SettlementAmount    int32          `json:"settlement_amount"`
	SettlementCurrency  string         `json:"settlement_currency"`
	Amount              sql.NullInt32  `json:"amount"`
	TransactionProvider sql.NullString `json:"transaction_provider"`
	TransactionID       sql.NullString `json:"transaction_id"`
	Description         string         `json:"description"`
	RefundedAmount      sql.NullInt64  `json:"refunded_amount"`
	CreatedAt           sql.NullTime   `json:"created_at"`
	ProcessedAt         sql.NullTime   `json:"processed_at"`
	UpdatedAt           sql.NullTime   `json:"updated_at"`
	DeletedAt           sql.NullTime   `json:"deleted_at"`
	UserID              sql.NullInt64  `json:"user_id"`
}

type PodpointCharge struct {
	ID                    int64          `json:"id"`
	LocationID            sql.NullInt64  `json:"location_id"`
	UnitID                int64          `json:"unit_id"`
	JobID                 sql.NullString `json:"job_id"`
	Door                  int64          `json:"door"`
	EnergyCost            sql.NullInt32  `json:"energy_cost"`
	TagID                 sql.NullInt64  `json:"tag_id"`
	BillingAccountID      sql.NullInt64  `json:"billing_account_id"`
	BillingEventID        sql.NullInt64  `json:"billing_event_id"`
	GroupID               sql.NullInt64  `json:"group_id"`
	ClaimedChargeID       sql.NullInt64  `json:"claimed_charge_id"`
	ChargeCycleID         sql.NullString `json:"charge_cycle_id"`
	StartsAt              sql.NullTime   `json:"starts_at"`
	EndsAt                sql.NullTime   `json:"ends_at"`
	StartEventProcessedAt sql.NullTime   `json:"start_event_processed_at"`
	EndEventProcessedAt   sql.NullTime   `json:"end_event_processed_at"`
	KwhUsed               string         `json:"kwh_used"`
	Duration              sql.NullInt64  `json:"duration"`
	IsClosed              int16          `json:"is_closed"`
	CreatedAt             sql.NullTime   `json:"created_at"`
	UpdatedAt             sql.NullTime   `json:"updated_at"`
	DeletedAt             sql.NullTime   `json:"deleted_at"`
	GenerationKwhUsed     sql.NullString `json:"generation_kwh_used"`
}

type PodpointChargeState struct {
	ID               int64          `json:"id"`
	ChargeID         int64          `json:"charge_id"`
	Pilot            string         `json:"pilot"`
	Energy           sql.NullString `json:"energy"`
	Midenergy        sql.NullString `json:"midenergy"`
	Current1         sql.NullInt64  `json:"current1"`
	V1rms            sql.NullInt64  `json:"v1rms"`
	StartsAt         time.Time      `json:"starts_at"`
	EndsAt           sql.NullTime   `json:"ends_at"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	GenerationEnergy sql.NullString `json:"generation_energy"`
}

type PodpointClaimedCharge struct {
	ID               int64         `json:"id"`
	PodLocationID    int64         `json:"pod_location_id"`
	PodDoorID        int64         `json:"pod_door_id"`
	UserID           sql.NullInt64 `json:"user_id"`
	BillingEventID   sql.NullInt64 `json:"billing_event_id"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        time.Time     `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
	ClaimedBy        sql.NullInt64 `json:"claimed_by"`
	AuthoriserID     int64         `json:"authoriser_id"`
	BillingAccountID sql.NullInt64 `json:"billing_account_id"`
}

type PodpointEvDriver struct {
	ID        int64        `json:"id"`
	Email     string       `json:"email"`
	FirstName string       `json:"first_name"`
	LastName  string       `json:"last_name"`
	GroupID   int64        `json:"group_id"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodpointEvDriverDomain struct {
	ID         int64        `json:"id"`
	DomainName string       `json:"domain_name"`
	GroupID    int64        `json:"group_id"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  sql.NullTime `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type PodpointGroup struct {
	ID            int64         `json:"id"`
	Uid           string        `json:"uid"`
	TypeID        int64         `json:"type_id"`
	OwnerUserID   sql.NullInt64 `json:"owner_user_id"`
	Name          string        `json:"name"`
	ContactName   string        `json:"contact_name"`
	Phone         string        `json:"phone"`
	BusinessName  string        `json:"business_name"`
	Line1         string        `json:"line_1"`
	Line2         string        `json:"line_2"`
	PostalTown    string        `json:"postal_town"`
	Postcode      string        `json:"postcode"`
	Country       string        `json:"country"`
	FeePercentage string        `json:"fee_percentage"`
	CreatedAt     sql.NullTime  `json:"created_at"`
	UpdatedAt     sql.NullTime  `json:"updated_at"`
	DeletedAt     sql.NullTime  `json:"deleted_at"`
}

type PodpointGroupBillingAccount struct {
	ID               int64        `json:"id"`
	GroupID          int64        `json:"group_id"`
	BillingAccountID int64        `json:"billing_account_id"`
	CreatedAt        sql.NullTime `json:"created_at"`
	UpdatedAt        sql.NullTime `json:"updated_at"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

type PodpointMember struct {
	ID        int64        `json:"id"`
	Email     string       `json:"email"`
	FirstName string       `json:"first_name"`
	LastName  string       `json:"last_name"`
	GroupID   int64        `json:"group_id"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodpointPodAddress struct {
	ID           int64          `json:"id"`
	GroupID      sql.NullInt64  `json:"group_id"`
	ContactName  sql.NullString `json:"contact_name"`
	Email        sql.NullString `json:"email"`
	Telephone    sql.NullString `json:"telephone"`
	BusinessName string         `json:"business_name"`
	Line1        string         `json:"line_1"`
	Line2        string         `json:"line_2"`
	PostalTown   string         `json:"postal_town"`
	Postcode     string         `json:"postcode"`
	Country      string         `json:"country"`
	Description  string         `json:"description"`
	TypeID       int64          `json:"type_id"`
	TariffID     sql.NullInt64  `json:"tariff_id"`
	CostPerKwh   sql.NullInt64  `json:"cost_per_kwh"`
	CreatedAt    sql.NullTime   `json:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at"`
	DeletedAt    sql.NullTime   `json:"deleted_at"`
}

type PodpointPodLocation struct {
	ID                 int64          `json:"id"`
	AddressID          int64          `json:"address_id"`
	RevenueProfileID   sql.NullInt64  `json:"revenue_profile_id"`
	AdvertID           sql.NullInt64  `json:"advert_id"`
	Longitude          string         `json:"longitude"`
	Latitude           string         `json:"latitude"`
	Geohash            sql.NullInt64  `json:"geohash"`
	Name               sql.NullString `json:"name"`
	Description        string         `json:"description"`
	PaygEnabled        int16          `json:"payg_enabled"`
	ContactlessEnabled int16          `json:"contactless_enabled"`
	MidmeterEnabled    int16          `json:"midmeter_enabled"`
	IsPublic           int16          `json:"is_public"`
	IsHome             int16          `json:"is_home"`
	IsEvZone           int16          `json:"is_ev_zone"`
	UnitID             sql.NullInt64  `json:"unit_id"`
	CreatedAt          sql.NullTime   `json:"created_at"`
	UpdatedAt          sql.NullTime   `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
	Timezone           sql.NullString `json:"timezone"`
	Uuid               string         `json:"uuid"`
}

type PodpointPodModel struct {
	ID                             int64          `json:"id"`
	VendorID                       int64          `json:"vendor_id"`
	RangeID                        sql.NullInt64  `json:"range_id"`
	PcbConfigurationID             sql.NullInt64  `json:"pcb_configuration_id"`
	Name                           string         `json:"name"`
	SupportsPayg                   int16          `json:"supports_payg"`
	SupportsOcpp                   int16          `json:"supports_ocpp"`
	SupportsContactless            int16          `json:"supports_contactless"`
	SupportsSimultaneousDcCharging int16          `json:"supports_simultaneous_dc_charging"`
	ImageUrl                       sql.NullString `json:"image_url"`
	CreatedAt                      sql.NullTime   `json:"created_at"`
	UpdatedAt                      sql.NullTime   `json:"updated_at"`
	DeletedAt                      sql.NullTime   `json:"deleted_at"`
}

type PodpointPodUnit struct {
	ID               int64          `json:"id"`
	Ppid             string         `json:"ppid"`
	Name             sql.NullString `json:"name"`
	ModelID          int64          `json:"model_id"`
	StatusID         sql.NullInt64  `json:"status_id"`
	ConfigID         sql.NullInt32  `json:"config_id"`
	OcppEndpointID   sql.NullInt64  `json:"ocpp_endpoint_id"`
	InstallationID   sql.NullString `json:"installation_id"`
	DateCommissioned sql.NullTime   `json:"date_commissioned"`
	LastContact      sql.NullTime   `json:"last_contact"`
	RelayWeldFlag    int16          `json:"relay_weld_flag"`
	CreatedAt        sql.NullTime   `json:"created_at"`
	UpdatedAt        sql.NullTime   `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodpointPodUnitUser struct {
	UnitID    int64        `json:"unit_id"`
	UserID    int64        `json:"user_id"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
}

type PodpointRevenueProfile struct {
	ID        int64        `json:"id"`
	GroupID   int64        `json:"group_id"`
	Name      string       `json:"name"`
	Currency  string       `json:"currency"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodpointRevenueProfileTier struct {
	ID               int64          `json:"id"`
	RevenueProfileID int64          `json:"revenue_profile_id"`
	Type             Term           `json:"type"`
	UserType         UserType       `json:"user_type"`
	BeginTime        sql.NullString `json:"begin_time"`
	BeginDay         sql.NullInt64  `json:"begin_day"`
	EndTime          sql.NullString `json:"end_time"`
	EndDay           sql.NullInt64  `json:"end_day"`
	StartSecond      int64          `json:"start_second"`
	Rate             int64          `json:"rate"`
	CreatedAt        sql.NullTime   `json:"created_at"`
	UpdatedAt        sql.NullTime   `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodpointTariff struct {
	ID               int64         `json:"id"`
	Currency         string        `json:"currency"`
	Name             string        `json:"name"`
	UserID           sql.NullInt64 `json:"user_id"`
	EnergySupplierID sql.NullInt64 `json:"energy_supplier_id"`
	CreatedAt        sql.NullTime  `json:"created_at"`
	UpdatedAt        sql.NullTime  `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

type PodpointTariffTier struct {
	ID        int64          `json:"id"`
	Rate      int64          `json:"rate"`
	BeginTime sql.NullString `json:"begin_time"`
	BeginDay  sql.NullInt32  `json:"begin_day"`
	EndTime   sql.NullString `json:"end_time"`
	EndDay    sql.NullInt32  `json:"end_day"`
	TariffID  int64          `json:"tariff_id"`
	CreatedAt sql.NullTime   `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type PodpointUser struct {
	ID                   int64          `json:"id"`
	AuthID               string         `json:"auth_id"`
	GroupID              sql.NullInt64  `json:"group_id"`
	RoleID               int64          `json:"role_id"`
	Email                string         `json:"email"`
	Salutation           sql.NullString `json:"salutation"`
	FirstName            string         `json:"first_name"`
	LastName             string         `json:"last_name"`
	Password             sql.NullString `json:"password"`
	RememberToken        sql.NullString `json:"remember_token"`
	VerifyToken          sql.NullString `json:"verify_token"`
	LastLogin            sql.NullTime   `json:"last_login"`
	ActivatedAt          sql.NullTime   `json:"activated_at"`
	LastNotificationSeen sql.NullTime   `json:"last_notification_seen"`
	CreatedAt            sql.NullTime   `json:"created_at"`
	UpdatedAt            sql.NullTime   `json:"updated_at"`
	DeletedAt            sql.NullTime   `json:"deleted_at"`
	IsEmailedUsageData   int16          `json:"is_emailed_usage_data"`
}

type ProjectionsAuditedCharge struct {
	ID                int32          `json:"id"`
	AggregateID       uuid.UUID      `json:"aggregate_id"`
	LocationID        sql.NullInt32  `json:"location_id"`
	ChargingDuration  sql.NullInt32  `json:"charging_duration"`
	PluggedInDuration sql.NullInt32  `json:"plugged_in_duration"`
	ClaimChargeID     sql.NullInt32  `json:"claim_charge_id"`
	StartedAt         sql.NullTime   `json:"started_at"`
	PluggedInAt       sql.NullTime   `json:"plugged_in_at"`
	Billed            bool           `json:"billed"`
	EnergyKwh         sql.NullString `json:"energy_kwh"`
	GroupName         sql.NullString `json:"group_name"`
	AuthoriserID      sql.NullInt32  `json:"authoriser_id"`
	AuthoriserType    sql.NullString `json:"authoriser_type"`
	Ppid              sql.NullString `json:"ppid"`
	Door              sql.NullString `json:"door"`
	ChargeCycleID     sql.NullString `json:"charge_cycle_id"`
	SupportsOcpp      bool           `json:"supports_ocpp"`
	GroupID           uuid.NullUUID  `json:"group_id"`
}

type ProjectionsCharge struct {
	ID                    int64          `json:"id"`
	ChargeUuid            uuid.UUID      `json:"charge_uuid"`
	StartedAt             sql.NullTime   `json:"started_at"`
	EndedAt               sql.NullTime   `json:"ended_at"`
	EnergyTotal           sql.NullString `json:"energy_total"`
	ChargeDurationTotal   sql.NullInt32  `json:"charge_duration_total"`
	ChargerID             sql.NullString `json:"charger_id"`
	Door                  sql.NullString `json:"door"`
	PluggedInAt           sql.NullTime   `json:"plugged_in_at"`
	UnpluggedAt           sql.NullTime   `json:"unplugged_at"`
	SettlementAmount      sql.NullInt32  `json:"settlement_amount"`
	SettlementCurrency    sql.NullString `json:"settlement_currency"`
	ExpensedToGroup       uuid.NullUUID  `json:"expensed_to_group"`
	ExpensedTo            sql.NullString `json:"expensed_to"`
	ChargerName           sql.NullString `json:"charger_name"`
	ChargerType           NullAccess     `json:"charger_type"`
	SiteName              sql.NullString `json:"site_name"`
	GroupID               uuid.NullUUID  `json:"group_id"`
	SiteID                uuid.NullUUID  `json:"site_id"`
	GroupName             sql.NullString `json:"group_name"`
	EnergyCost            sql.NullInt32  `json:"energy_cost"`
	EnergyCostCurrency    sql.NullString `json:"energy_cost_currency"`
	ChargerTimezone       sql.NullString `json:"charger_timezone"`
	UserIds               []uuid.UUID    `json:"user_ids"`
	GenerationEnergyTotal sql.NullString `json:"generation_energy_total"`
	GridEnergyTotal       sql.NullString `json:"grid_energy_total"`
	Confirmed             sql.NullBool   `json:"confirmed"`
}

type ProjectionsSiteStatsMonthly struct {
	ID               int32       `json:"id"`
	Month            time.Time   `json:"month"`
	SiteID           uuid.UUID   `json:"site_id"`
	SiteName         string      `json:"site_name"`
	EnergyUsageKwh   string      `json:"energy_usage_kwh"`
	EnergyCost       int32       `json:"energy_cost"`
	RevenueGenerated int32       `json:"revenue_generated"`
	NumberOfCharges  int32       `json:"number_of_charges"`
	NumberOfDrivers  int32       `json:"number_of_drivers"`
	TotalDuration    int32       `json:"total_duration"`
	GroupID          uuid.UUID   `json:"group_id"`
	GroupName        string      `json:"group_name"`
	ChargeIds        []uuid.UUID `json:"charge_ids"`
	DriverIds        []uuid.UUID `json:"driver_ids"`
}
