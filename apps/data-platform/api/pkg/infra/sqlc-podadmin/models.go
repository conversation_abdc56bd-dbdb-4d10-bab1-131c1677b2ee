// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlcpodadmin

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"time"
)

type BillingEventsTransactionProvider string

const (
	BillingEventsTransactionProviderNone    BillingEventsTransactionProvider = "none"
	BillingEventsTransactionProviderStripe  BillingEventsTransactionProvider = "stripe"
	BillingEventsTransactionProviderUnknown BillingEventsTransactionProvider = "unknown"
	BillingEventsTransactionProviderPayter  BillingEventsTransactionProvider = "payter"
)

func (e *BillingEventsTransactionProvider) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = BillingEventsTransactionProvider(s)
	case string:
		*e = BillingEventsTransactionProvider(s)
	default:
		return fmt.Errorf("unsupported scan type for BillingEventsTransactionProvider: %T", src)
	}
	return nil
}

type NullBillingEventsTransactionProvider struct {
	BillingEventsTransactionProvider BillingEventsTransactionProvider `json:"billing_events_transaction_provider"`
	Valid                            bool                             `json:"valid"` // Valid is true if BillingEventsTransactionProvider is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullBillingEventsTransactionProvider) Scan(value interface{}) error {
	if value == nil {
		ns.BillingEventsTransactionProvider, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.BillingEventsTransactionProvider.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullBillingEventsTransactionProvider) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.BillingEventsTransactionProvider), nil
}

type DownloadsStatus string

const (
	DownloadsStatusPending DownloadsStatus = "pending"
	DownloadsStatusReady   DownloadsStatus = "ready"
)

func (e *DownloadsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = DownloadsStatus(s)
	case string:
		*e = DownloadsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for DownloadsStatus: %T", src)
	}
	return nil
}

type NullDownloadsStatus struct {
	DownloadsStatus DownloadsStatus `json:"downloads_status"`
	Valid           bool            `json:"valid"` // Valid is true if DownloadsStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullDownloadsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.DownloadsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.DownloadsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullDownloadsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.DownloadsStatus), nil
}

type ParkingOpeningTimesDay string

const (
	ParkingOpeningTimesDayMon ParkingOpeningTimesDay = "mon"
	ParkingOpeningTimesDayTue ParkingOpeningTimesDay = "tue"
	ParkingOpeningTimesDayWed ParkingOpeningTimesDay = "wed"
	ParkingOpeningTimesDayThu ParkingOpeningTimesDay = "thu"
	ParkingOpeningTimesDayFri ParkingOpeningTimesDay = "fri"
	ParkingOpeningTimesDaySat ParkingOpeningTimesDay = "sat"
	ParkingOpeningTimesDaySun ParkingOpeningTimesDay = "sun"
)

func (e *ParkingOpeningTimesDay) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ParkingOpeningTimesDay(s)
	case string:
		*e = ParkingOpeningTimesDay(s)
	default:
		return fmt.Errorf("unsupported scan type for ParkingOpeningTimesDay: %T", src)
	}
	return nil
}

type NullParkingOpeningTimesDay struct {
	ParkingOpeningTimesDay ParkingOpeningTimesDay `json:"parking_opening_times_day"`
	Valid                  bool                   `json:"valid"` // Valid is true if ParkingOpeningTimesDay is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullParkingOpeningTimesDay) Scan(value interface{}) error {
	if value == nil {
		ns.ParkingOpeningTimesDay, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ParkingOpeningTimesDay.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullParkingOpeningTimesDay) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ParkingOpeningTimesDay), nil
}

type RevenueProfileTiersType string

const (
	RevenueProfileTiersTypeDuration RevenueProfileTiersType = "duration"
	RevenueProfileTiersTypeEnergy   RevenueProfileTiersType = "energy"
	RevenueProfileTiersTypeFixed    RevenueProfileTiersType = "fixed"
)

func (e *RevenueProfileTiersType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RevenueProfileTiersType(s)
	case string:
		*e = RevenueProfileTiersType(s)
	default:
		return fmt.Errorf("unsupported scan type for RevenueProfileTiersType: %T", src)
	}
	return nil
}

type NullRevenueProfileTiersType struct {
	RevenueProfileTiersType RevenueProfileTiersType `json:"revenue_profile_tiers_type"`
	Valid                   bool                    `json:"valid"` // Valid is true if RevenueProfileTiersType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRevenueProfileTiersType) Scan(value interface{}) error {
	if value == nil {
		ns.RevenueProfileTiersType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RevenueProfileTiersType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRevenueProfileTiersType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RevenueProfileTiersType), nil
}

type RevenueProfileTiersUserType string

const (
	RevenueProfileTiersUserTypePublic  RevenueProfileTiersUserType = "public"
	RevenueProfileTiersUserTypeMembers RevenueProfileTiersUserType = "members"
	RevenueProfileTiersUserTypeDrivers RevenueProfileTiersUserType = "drivers"
)

func (e *RevenueProfileTiersUserType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RevenueProfileTiersUserType(s)
	case string:
		*e = RevenueProfileTiersUserType(s)
	default:
		return fmt.Errorf("unsupported scan type for RevenueProfileTiersUserType: %T", src)
	}
	return nil
}

type NullRevenueProfileTiersUserType struct {
	RevenueProfileTiersUserType RevenueProfileTiersUserType `json:"revenue_profile_tiers_user_type"`
	Valid                       bool                        `json:"valid"` // Valid is true if RevenueProfileTiersUserType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRevenueProfileTiersUserType) Scan(value interface{}) error {
	if value == nil {
		ns.RevenueProfileTiersUserType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RevenueProfileTiersUserType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRevenueProfileTiersUserType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RevenueProfileTiersUserType), nil
}

type Advert struct {
	ID        uint32    `json:"id"`
	Image     string    `json:"image"`
	Url       string    `json:"url"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ApiKey struct {
	AccessKey string `json:"access_key"`
	SecretKey string `json:"secret_key"`
	Email     string `json:"email"`
	Contact   string `json:"contact"`
	Purpose   string `json:"purpose"`
	Role      string `json:"role"`
}

type ApiKeyResource struct {
	AccessKey     string       `json:"access_key"`
	ApiResourceID uint32       `json:"api_resource_id"`
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`
	DeletedAt     sql.NullTime `json:"deleted_at"`
}

type ApiNonce struct {
	ID        uint64    `json:"id"`
	AccessKey string    `json:"access_key"`
	Nonce     string    `json:"nonce"`
	UsedAt    time.Time `json:"used_at"`
}

type ApiResource struct {
	ID        uint32       `json:"id"`
	Resource  string       `json:"resource"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type ApiSession struct {
	ID         string         `json:"id"`
	UserID     uint32         `json:"user_id"`
	DeviceID   sql.NullString `json:"device_id"`
	DeviceType sql.NullString `json:"device_type"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  sql.NullTime   `json:"updated_at"`
}

type AuditLog struct {
	ID          uint32       `json:"id"`
	UserID      uint32       `json:"user_id"`
	Description string       `json:"description"`
	CreatedAt   sql.NullTime `json:"created_at"`
	UpdatedAt   sql.NullTime `json:"updated_at"`
	DeletedAt   sql.NullTime `json:"deleted_at"`
}

type Authoriser struct {
	ID        uint32         `json:"id"`
	Uid       string         `json:"uid"`
	Type      string         `json:"type"`
	GroupUid  sql.NullString `json:"group_uid"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type AwsdmsLoopbackPrevention struct {
	TaskName  string       `json:"task_name"`
	Timestamp sql.NullTime `json:"timestamp"`
}

type BatchJob struct {
	ID         uint32         `json:"id"`
	UserID     uint32         `json:"user_id"`
	Name       string         `json:"name"`
	Data       sql.NullString `json:"data"`
	Filename   sql.NullString `json:"filename"`
	Errors     sql.NullString `json:"errors"`
	StartedAt  sql.NullTime   `json:"started_at"`
	FinishedAt sql.NullTime   `json:"finished_at"`
	CreatedAt  sql.NullTime   `json:"created_at"`
	UpdatedAt  sql.NullTime   `json:"updated_at"`
	DeletedAt  sql.NullTime   `json:"deleted_at"`
}

type BillingAccount struct {
	ID     uint32        `json:"id"`
	UserID sql.NullInt32 `json:"user_id"`
	// (DC2Type:uuid)
	Uid                string         `json:"uid"`
	Balance            int32          `json:"balance"`
	Currency           string         `json:"currency"`
	BusinessName       string         `json:"business_name"`
	Line1              string         `json:"line_1"`
	Line2              string         `json:"line_2"`
	PostalTown         string         `json:"postal_town"`
	Postcode           string         `json:"postcode"`
	Country            string         `json:"country"`
	Phone              sql.NullString `json:"phone"`
	Mobile             sql.NullString `json:"mobile"`
	PaymentProcessorID sql.NullString `json:"payment_processor_id"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          sql.NullTime   `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
}

type BillingEvent struct {
	ID                  uint32                               `json:"id"`
	AccountID           sql.NullInt32                        `json:"account_id"`
	PresentmentAmount   int32                                `json:"presentment_amount"`
	PresentmentCurrency string                               `json:"presentment_currency"`
	ExchangeRate        string                               `json:"exchange_rate"`
	SettlementAmount    int32                                `json:"settlement_amount"`
	SettlementCurrency  string                               `json:"settlement_currency"`
	Amount              sql.NullInt32                        `json:"amount"`
	TransactionProvider NullBillingEventsTransactionProvider `json:"transaction_provider"`
	TransactionID       sql.NullString                       `json:"transaction_id"`
	Description         string                               `json:"description"`
	RefundedAmount      sql.NullInt32                        `json:"refunded_amount"`
	CreatedAt           time.Time                            `json:"created_at"`
	ProcessedAt         sql.NullTime                         `json:"processed_at"`
	UpdatedAt           sql.NullTime                         `json:"updated_at"`
	DeletedAt           sql.NullTime                         `json:"deleted_at"`
	UserID              sql.NullInt32                        `json:"user_id"`
}

type Charge struct {
	ID                    uint32         `json:"id"`
	LocationID            sql.NullInt32  `json:"location_id"`
	UnitID                uint32         `json:"unit_id"`
	JobID                 sql.NullString `json:"job_id"`
	Door                  uint32         `json:"door"`
	EnergyCost            sql.NullInt32  `json:"energy_cost"`
	TagID                 sql.NullInt32  `json:"tag_id"`
	BillingAccountID      sql.NullInt32  `json:"billing_account_id"`
	BillingEventID        sql.NullInt32  `json:"billing_event_id"`
	GroupID               sql.NullInt32  `json:"group_id"`
	ClaimedChargeID       sql.NullInt32  `json:"claimed_charge_id"`
	ChargeCycleID         sql.NullString `json:"charge_cycle_id"`
	StartsAt              sql.NullTime   `json:"starts_at"`
	EndsAt                sql.NullTime   `json:"ends_at"`
	StartEventProcessedAt sql.NullTime   `json:"start_event_processed_at"`
	EndEventProcessedAt   sql.NullTime   `json:"end_event_processed_at"`
	KwhUsed               string         `json:"kwh_used"`
	GenerationKwhUsed     sql.NullString `json:"generation_kwh_used"`
	Duration              sql.NullInt32  `json:"duration"`
	IsClosed              bool           `json:"is_closed"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             sql.NullTime   `json:"updated_at"`
	DeletedAt             sql.NullTime   `json:"deleted_at"`
}

type ChargeMethod struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type ChargeSchedule struct {
	ID        uint32       `json:"id"`
	Uid       string       `json:"uid"`
	StartDay  int8         `json:"start_day"`
	StartTime time.Time    `json:"start_time"`
	EndDay    int8         `json:"end_day"`
	EndTime   time.Time    `json:"end_time"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type ChargeScheduleStatus struct {
	ID               uint32       `json:"id"`
	ChargeScheduleID uint32       `json:"charge_schedule_id"`
	IsActive         bool         `json:"is_active"`
	CreatedAt        sql.NullTime `json:"created_at"`
	UpdatedAt        sql.NullTime `json:"updated_at"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

type ChargeState struct {
	ID               uint32         `json:"id"`
	ChargeID         uint32         `json:"charge_id"`
	Pilot            string         `json:"pilot"`
	Energy           sql.NullString `json:"energy"`
	Midenergy        sql.NullString `json:"midenergy"`
	GenerationEnergy sql.NullString `json:"generation_energy"`
	Current1         sql.NullInt32  `json:"current1"`
	V1rms            sql.NullInt32  `json:"v1rms"`
	StartsAt         time.Time      `json:"starts_at"`
	EndsAt           sql.NullTime   `json:"ends_at"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
}

type ClaimedCharge struct {
	ID               uint32        `json:"id"`
	PodLocationID    uint32        `json:"pod_location_id"`
	PodDoorID        uint32        `json:"pod_door_id"`
	UserID           sql.NullInt32 `json:"user_id"`
	BillingEventID   sql.NullInt32 `json:"billing_event_id"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        time.Time     `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
	ClaimedBy        sql.NullInt32 `json:"claimed_by"`
	AuthoriserID     int32         `json:"authoriser_id"`
	BillingAccountID sql.NullInt32 `json:"billing_account_id"`
}

type Device struct {
	DeviceID   string       `json:"device_id"`
	DeviceType string       `json:"device_type"`
	UserID     uint32       `json:"user_id"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type DinRail struct {
	ID           uint32       `json:"id"`
	SerialNumber string       `json:"serial_number"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    sql.NullTime `json:"updated_at"`
	DeletedAt    sql.NullTime `json:"deleted_at"`
}

type DinRailPodUnit struct {
	DinRailID  uint32        `json:"din_rail_id"`
	UnitID     uint32        `json:"unit_id"`
	InsertedAt time.Time     `json:"inserted_at"`
	InsertedBy sql.NullInt32 `json:"inserted_by"`
	RemovedAt  sql.NullTime  `json:"removed_at"`
	RemovedBy  sql.NullInt32 `json:"removed_by"`
}

type Download struct {
	ID          uint32          `json:"id"`
	UserID      uint32          `json:"user_id"`
	Filename    string          `json:"filename"`
	StoragePath string          `json:"storage_path"`
	Status      DownloadsStatus `json:"status"`
	DeletedAt   sql.NullTime    `json:"deleted_at"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

type EnergySupplier struct {
	ID        uint32         `json:"id"`
	Name      string         `json:"name"`
	OrderName sql.NullString `json:"order_name"`
	Country   string         `json:"country"`
	CreatedAt sql.NullTime   `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type EvDriver struct {
	ID         uint32       `json:"id"`
	Email      string       `json:"email"`
	FirstName  string       `json:"first_name"`
	LastName   string       `json:"last_name"`
	GroupID    uint32       `json:"group_id"`
	CanExpense bool         `json:"can_expense"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type EvDriverDomain struct {
	ID         uint32       `json:"id"`
	DomainName string       `json:"domain_name"`
	GroupID    uint32       `json:"group_id"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type FailedQueueJob struct {
	ID         uint32         `json:"id"`
	Connection string         `json:"connection"`
	Queue      string         `json:"queue"`
	Payload    string         `json:"payload"`
	Exception  sql.NullString `json:"exception"`
	FailedAt   time.Time      `json:"failed_at"`
}

type Favourite struct {
	ID           uint32       `json:"id"`
	UserID       uint32       `json:"user_id"`
	PodAddressID uint32       `json:"pod_address_id"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
	DeletedAt    sql.NullTime `json:"deleted_at"`
}

type Group struct {
	ID            uint32        `json:"id"`
	Uid           string        `json:"uid"`
	TypeID        uint32        `json:"type_id"`
	OwnerUserID   sql.NullInt32 `json:"owner_user_id"`
	Name          string        `json:"name"`
	ContactName   string        `json:"contact_name"`
	Phone         string        `json:"phone"`
	BusinessName  string        `json:"business_name"`
	Line1         string        `json:"line_1"`
	Line2         string        `json:"line_2"`
	PostalTown    string        `json:"postal_town"`
	Postcode      string        `json:"postcode"`
	Country       string        `json:"country"`
	FeePercentage string        `json:"fee_percentage"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     sql.NullTime  `json:"updated_at"`
	DeletedAt     sql.NullTime  `json:"deleted_at"`
}

type GroupBillingAccount struct {
	ID               uint32       `json:"id"`
	GroupID          uint32       `json:"group_id"`
	BillingAccountID uint32       `json:"billing_account_id"`
	CreatedAt        sql.NullTime `json:"created_at"`
	UpdatedAt        sql.NullTime `json:"updated_at"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

type GroupLocation struct {
	GroupID    uint32       `json:"group_id"`
	LocationID uint32       `json:"location_id"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  sql.NullTime `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type GroupModule struct {
	GroupID   uint32         `json:"group_id"`
	ModuleID  uint32         `json:"module_id"`
	Settings  sql.NullString `json:"settings"`
	ExpiresAt sql.NullTime   `json:"expires_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
}

type GroupPodModel struct {
	GroupID    int32     `json:"group_id"`
	PodModelID int32     `json:"pod_model_id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

type GroupTag struct {
	GroupID   uint32       `json:"group_id"`
	TagID     uint32       `json:"tag_id"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type GroupTransaction struct {
	ID            uint32         `json:"id"`
	GroupID       uint32         `json:"group_id"`
	PaymentNo     sql.NullString `json:"payment_no"`
	Subtotal      uint32         `json:"subtotal"`
	Vat           string         `json:"vat"`
	FeePercentage string         `json:"fee_percentage"`
	Notes         sql.NullString `json:"notes"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     sql.NullTime   `json:"deleted_at"`
}

type GroupType struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type HardwareStatus struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type Label struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type LabelPodLocation struct {
	PodLocationID uint32 `json:"pod_location_id"`
	LabelID       uint32 `json:"label_id"`
}

type LabelPodUnit struct {
	ID        uint32 `json:"id"`
	LabelID   uint32 `json:"label_id"`
	PodUnitID uint32 `json:"pod_unit_id"`
}

type Member struct {
	ID         uint32       `json:"id"`
	Email      string       `json:"email"`
	FirstName  string       `json:"first_name"`
	LastName   string       `json:"last_name"`
	GroupID    uint32       `json:"group_id"`
	CanExpense bool         `json:"can_expense"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type Migration struct {
	Migration string `json:"migration"`
	Batch     int32  `json:"batch"`
}

type Module struct {
	ID          uint32         `json:"id"`
	ClassName   string         `json:"class_name"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Settings    sql.NullString `json:"settings"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at"`
	DeletedAt   sql.NullTime   `json:"deleted_at"`
}

type Notification struct {
	ID        uint32    `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type NotificationUser struct {
	ID             uint32 `json:"id"`
	UserID         uint32 `json:"user_id"`
	NotificationID uint32 `json:"notification_id"`
	Enabled        bool   `json:"enabled"`
}

type OcppEndpoint struct {
	ID        uint32       `json:"id"`
	Url       string       `json:"url"`
	Version   string       `json:"version"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type ParkingCharge struct {
	ID           uint32         `json:"id"`
	PodAddressID sql.NullInt32  `json:"pod_address_id"`
	MinPrice     sql.NullInt32  `json:"min_price"`
	MaxPrice     sql.NullInt32  `json:"max_price"`
	Notes        sql.NullString `json:"notes"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    sql.NullTime   `json:"deleted_at"`
}

type ParkingOpeningTime struct {
	ID           uint32                 `json:"id"`
	PodAddressID uint32                 `json:"pod_address_id"`
	Day          ParkingOpeningTimesDay `json:"day"`
	AllDay       bool                   `json:"all_day"`
	From         time.Time              `json:"from"`
	To           time.Time              `json:"to"`
}

type ParkingOpeningTimeNote struct {
	ID           uint32         `json:"id"`
	PodAddressID uint32         `json:"pod_address_id"`
	Notes        sql.NullString `json:"notes"`
}

type PasswordReset struct {
	Email     string       `json:"email"`
	Token     string       `json:"token"`
	CreatedAt time.Time    `json:"created_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type Pcb struct {
	ID              uint32         `json:"id"`
	SerialNumber    string         `json:"serial_number"`
	TypeID          sql.NullInt32  `json:"type_id"`
	Derated         bool           `json:"derated"`
	RepairVersionID sql.NullInt32  `json:"repair_version_id"`
	MacAddress      sql.NullString `json:"mac_address"`
	EolData         sql.NullString `json:"eol_data"`
	LastContact     sql.NullTime   `json:"last_contact"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       sql.NullTime   `json:"deleted_at"`
}

type PcbCommand struct {
	ID                         uint32       `json:"id"`
	PcbID                      uint32       `json:"pcb_id"`
	Reset                      bool         `json:"reset"`
	OutOfService               bool         `json:"out_of_service"`
	Available                  bool         `json:"available"`
	KeyLock                    bool         `json:"key_lock"`
	UnlockPlug                 bool         `json:"unlock_plug"`
	UpdateAvailableDsp         bool         `json:"update_available_dsp"`
	UpdateAvailableWifi        bool         `json:"update_available_wifi"`
	UpdateAvailableRfid        bool         `json:"update_available_rfid"`
	Pause                      bool         `json:"pause"`
	ShortTermPowerRatingExpiry sql.NullTime `json:"short_term_power_rating_expiry"`
	ShortTermPowerRating       int32        `json:"short_term_power_rating"`
	CreatedAt                  time.Time    `json:"created_at"`
	UpdatedAt                  time.Time    `json:"updated_at"`
}

type PcbCommandSchedule struct {
	ID         uint32       `json:"id"`
	PcbID      uint32       `json:"pcb_id"`
	UserID     uint32       `json:"user_id"`
	Command    string       `json:"command"`
	ExecutesAt time.Time    `json:"executes_at"`
	CreatedAt  sql.NullTime `json:"created_at"`
	UpdatedAt  sql.NullTime `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type PcbConfiguration struct {
	ID                            uint32        `json:"id"`
	PcbID                         sql.NullInt32 `json:"pcb_id"`
	IsRenaultUnit                 bool          `json:"is_renault_unit"`
	ArrayOn                       bool          `json:"array_on"`
	IsSmartUnit                   bool          `json:"is_smart_unit"`
	UseStagingUrl                 bool          `json:"use_staging_url"`
	MaximumSupply                 int32         `json:"maximum_supply"`
	PowerRating                   int32         `json:"power_rating"`
	LogResolution                 int32         `json:"log_resolution"`
	PvSolarSystem                 bool          `json:"pv_solar_system"`
	IsMonitoringEnabled           bool          `json:"is_monitoring_enabled"`
	Dip5                          bool          `json:"dip5"`
	SixMaDetected                 bool          `json:"six_ma_detected"`
	Hphc                          uint16        `json:"hphc"`
	IsUpliftMaxOvercurrentEnabled bool          `json:"is_uplift_max_overcurrent_enabled"`
	IsDiagnosticModeEnabled       bool          `json:"is_diagnostic_mode_enabled"`
	IsSetupModeEnabled            bool          `json:"is_setup_mode_enabled"`
	IsItSystem                    bool          `json:"is_it_system"`
	IsTestModeDisabled            bool          `json:"is_test_mode_disabled"`
	GracePeriod                   uint16        `json:"grace_period"`
	GracePower                    uint16        `json:"grace_power"`
	NoCommsPower                  uint16        `json:"no_comms_power"`
	Cpwl                          bool          `json:"cpwl"`
	CreatedAt                     time.Time     `json:"created_at"`
	UpdatedAt                     time.Time     `json:"updated_at"`
	IsVoltageThresholdReduced     bool          `json:"is_voltage_threshold_reduced"`
	FastMeterPeriod               uint32        `json:"fast_meter_period"`
}

type PcbPodUnit struct {
	PcbID      uint32        `json:"pcb_id"`
	UnitID     uint32        `json:"unit_id"`
	DoorID     sql.NullInt32 `json:"door_id"`
	InsertedAt time.Time     `json:"inserted_at"`
	InsertedBy sql.NullInt32 `json:"inserted_by"`
	RemovedAt  sql.NullTime  `json:"removed_at"`
	RemovedBy  sql.NullInt32 `json:"removed_by"`
}

type PcbSim struct {
	PcbID      uint32        `json:"pcb_id"`
	SimNumber  string        `json:"sim_number"`
	InsertedAt time.Time     `json:"inserted_at"`
	InsertedBy sql.NullInt32 `json:"inserted_by"`
	RemovedAt  sql.NullTime  `json:"removed_at"`
	RemovedBy  sql.NullInt32 `json:"removed_by"`
}

type PcbSoftwareVersion struct {
	ID                uint32       `json:"id"`
	PcbID             uint32       `json:"pcb_id"`
	SoftwareVersionID uint32       `json:"software_version_id"`
	CreatedAt         time.Time    `json:"created_at"`
	UpdatedAt         time.Time    `json:"updated_at"`
	DeletedAt         sql.NullTime `json:"deleted_at"`
}

type PcbStatusHistory struct {
	ID        uint32         `json:"id"`
	PcbID     uint32         `json:"pcb_id"`
	StatusID  uint32         `json:"status_id"`
	Reason    sql.NullString `json:"reason"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type PcbType struct {
	ID                 uint32         `json:"id"`
	HardwareRegisterID sql.NullInt32  `json:"hardware_register_id"`
	Version            string         `json:"version"`
	Type               string         `json:"type"`
	Description        sql.NullString `json:"description"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          sql.NullTime   `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
}

type PodAddress struct {
	ID           uint32         `json:"id"`
	GroupID      sql.NullInt32  `json:"group_id"`
	ContactName  sql.NullString `json:"contact_name"`
	Email        sql.NullString `json:"email"`
	Telephone    sql.NullString `json:"telephone"`
	BusinessName string         `json:"business_name"`
	Line1        string         `json:"line_1"`
	Line2        string         `json:"line_2"`
	PostalTown   string         `json:"postal_town"`
	Postcode     string         `json:"postcode"`
	Country      string         `json:"country"`
	Description  string         `json:"description"`
	TypeID       uint32         `json:"type_id"`
	TariffID     sql.NullInt32  `json:"tariff_id"`
	CostPerKwh   sql.NullInt32  `json:"cost_per_kwh"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at"`
	DeletedAt    sql.NullTime   `json:"deleted_at"`
}

type PodAddressType struct {
	ID              uint32       `json:"id"`
	Name            string       `json:"name"`
	Code            int32        `json:"code"`
	OnRoadside      bool         `json:"on_roadside"`
	HasRestrictions bool         `json:"has_restrictions"`
	CreatedAt       time.Time    `json:"created_at"`
	UpdatedAt       sql.NullTime `json:"updated_at"`
	DeletedAt       sql.NullTime `json:"deleted_at"`
}

type PodCommand struct {
	ID        uint32        `json:"id"`
	Name      string        `json:"name"`
	Code      string        `json:"code"`
	DoorID    sql.NullInt32 `json:"door_id"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at"`
	DeletedAt sql.NullTime  `json:"deleted_at"`
}

type PodCommandHistory struct {
	ID               uint32         `json:"id"`
	UserID           uint32         `json:"user_id"`
	CommandID        uint32         `json:"command_id"`
	UnitID           uint32         `json:"unit_id"`
	PhoneNumber      string         `json:"phone_number"`
	GatewayMessageID string         `json:"gateway_message_id"`
	StatusCode       sql.NullString `json:"status_code"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        sql.NullTime   `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodComponent struct {
	ID                   uint32         `json:"id"`
	Name                 string         `json:"name"`
	RelatesToModel       string         `json:"relates_to_model"`
	ModelIdentifier      string         `json:"model_identifier"`
	RelationshipName     string         `json:"relationship_name"`
	DefaultValue         sql.NullString `json:"default_value"`
	CommissionType       sql.NullString `json:"commission_type"`
	CommissionValidation sql.NullString `json:"commission_validation"`
	CreateType           sql.NullString `json:"create_type"`
	CreateValidation     sql.NullString `json:"create_validation"`
	CreatePivotData      sql.NullString `json:"create_pivot_data"`
	UpdateType           sql.NullString `json:"update_type"`
	UpdateValidation     sql.NullString `json:"update_validation"`
	UpdatePivotData      sql.NullString `json:"update_pivot_data"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            sql.NullTime   `json:"deleted_at"`
}

type PodComponentRange struct {
	PodRangeID     uint32    `json:"pod_range_id"`
	PodComponentID uint32    `json:"pod_component_id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type PodConfiguration struct {
	ID          uint32         `json:"id"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	Config      string         `json:"config"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   sql.NullTime   `json:"deleted_at"`
}

type PodConnector struct {
	ID               uint32        `json:"id"`
	ModelID          uint32        `json:"model_id"`
	DoorID           uint32        `json:"door_id"`
	SocketID         uint32        `json:"socket_id"`
	ChargeMethodID   uint32        `json:"charge_method_id"`
	Power            int32         `json:"power"`
	Current          int32         `json:"current"`
	Voltage          int32         `json:"voltage"`
	VoltageMin       sql.NullInt32 `json:"voltage_min"`
	VoltageMax       sql.NullInt32 `json:"voltage_max"`
	HasTetheredCable bool          `json:"has_tethered_cable"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        sql.NullTime  `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

type PodDoor struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodFault struct {
	ID                 uint32       `json:"id"`
	FaultCode          string       `json:"fault_code"`
	Name               string       `json:"name"`
	PodFaultCategoryID uint32       `json:"pod_fault_category_id"`
	CreatedAt          time.Time    `json:"created_at"`
	UpdatedAt          time.Time    `json:"updated_at"`
	DeletedAt          sql.NullTime `json:"deleted_at"`
}

type PodFaultAction struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodFaultCategory struct {
	ID               uint32        `json:"id"`
	FaultCode        string        `json:"fault_code"`
	Name             string        `json:"name"`
	ParentCategoryID sql.NullInt32 `json:"parent_category_id"`
	CreatedAt        time.Time     `json:"created_at"`
	UpdatedAt        time.Time     `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

type PodFaultRange struct {
	PodRangeID uint32       `json:"pod_range_id"`
	PodFaultID uint32       `json:"pod_fault_id"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	DeletedAt  sql.NullTime `json:"deleted_at"`
}

type PodFaultStatus struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodLocation struct {
	ID                 uint32         `json:"id"`
	Uuid               string         `json:"uuid"`
	AddressID          uint32         `json:"address_id"`
	RevenueProfileID   sql.NullInt32  `json:"revenue_profile_id"`
	AdvertID           sql.NullInt32  `json:"advert_id"`
	Longitude          string         `json:"longitude"`
	Latitude           string         `json:"latitude"`
	Geohash            sql.NullInt64  `json:"geohash"`
	Name               sql.NullString `json:"name"`
	Description        string         `json:"description"`
	PaygEnabled        bool           `json:"payg_enabled"`
	ContactlessEnabled bool           `json:"contactless_enabled"`
	MidmeterEnabled    bool           `json:"midmeter_enabled"`
	IsPublic           bool           `json:"is_public"`
	IsHome             bool           `json:"is_home"`
	IsEvZone           bool           `json:"is_ev_zone"`
	UnitID             sql.NullInt32  `json:"unit_id"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          sql.NullTime   `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
	Timezone           sql.NullString `json:"timezone"`
}

type PodLocationImage struct {
	ID            uint32         `json:"id"`
	PodLocationID uint32         `json:"pod_location_id"`
	Title         sql.NullString `json:"title"`
	Path          string         `json:"path"`
	IsPublic      bool           `json:"is_public"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

type PodLocationUnit struct {
	LocationID uint32        `json:"location_id"`
	UnitID     uint32        `json:"unit_id"`
	InsertedAt time.Time     `json:"inserted_at"`
	InsertedBy sql.NullInt32 `json:"inserted_by"`
	RemovedAt  sql.NullTime  `json:"removed_at"`
	RemovedBy  sql.NullInt32 `json:"removed_by"`
}

type PodMessage struct {
	ID        uint32         `json:"id"`
	UnitID    sql.NullInt32  `json:"unit_id"`
	Text      sql.NullString `json:"text"`
	EventData sql.NullString `json:"event_data"`
	SentAt    sql.NullTime   `json:"sent_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type PodMessagePart struct {
	ID               uint32         `json:"id"`
	MessageID        sql.NullInt32  `json:"message_id"`
	GatewayMessageID string         `json:"gateway_message_id"`
	Gateway          string         `json:"gateway"`
	PhoneNumber      string         `json:"phone_number"`
	ConcatRef        sql.NullString `json:"concat_ref"`
	ConcatTotal      sql.NullInt16  `json:"concat_total"`
	ConcatPart       sql.NullInt16  `json:"concat_part"`
	Text             string         `json:"text"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodModel struct {
	ID                             uint32         `json:"id"`
	VendorID                       uint32         `json:"vendor_id"`
	RangeID                        sql.NullInt32  `json:"range_id"`
	PcbConfigurationID             sql.NullInt32  `json:"pcb_configuration_id"`
	Name                           string         `json:"name"`
	SupportsPayg                   bool           `json:"supports_payg"`
	SupportsOcpp                   bool           `json:"supports_ocpp"`
	SupportsContactless            bool           `json:"supports_contactless"`
	SupportsSimultaneousDcCharging bool           `json:"supports_simultaneous_dc_charging"`
	ImageUrl                       sql.NullString `json:"image_url"`
	CreatedAt                      time.Time      `json:"created_at"`
	UpdatedAt                      sql.NullTime   `json:"updated_at"`
	DeletedAt                      sql.NullTime   `json:"deleted_at"`
}

type PodModelCommand struct {
	ModelID   uint32       `json:"model_id"`
	CommandID uint32       `json:"command_id"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodRange struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodStatus struct {
	ID        uint32       `json:"id"`
	KeyName   string       `json:"key_name"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodUnit struct {
	ID               uint32         `json:"id"`
	Ppid             string         `json:"ppid"`
	Name             sql.NullString `json:"name"`
	ModelID          uint32         `json:"model_id"`
	StatusID         sql.NullInt32  `json:"status_id"`
	ConfigID         sql.NullInt32  `json:"config_id"`
	OcppEndpointID   sql.NullInt32  `json:"ocpp_endpoint_id"`
	InstallationID   sql.NullString `json:"installation_id"`
	DateCommissioned sql.NullTime   `json:"date_commissioned"`
	LastContact      sql.NullTime   `json:"last_contact"`
	RelayWeldFlag    bool           `json:"relay_weld_flag"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        sql.NullTime   `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodUnitChargeSchedule struct {
	ID               uint32       `json:"id"`
	UnitID           uint32       `json:"unit_id"`
	ChargeScheduleID uint32       `json:"charge_schedule_id"`
	CreatedAt        sql.NullTime `json:"created_at"`
	UpdatedAt        sql.NullTime `json:"updated_at"`
	DeletedAt        sql.NullTime `json:"deleted_at"`
}

type PodUnitConnector struct {
	UnitID      uint32        `json:"unit_id"`
	ConnectorID uint32        `json:"connector_id"`
	StatusID    sql.NullInt32 `json:"status_id"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   sql.NullTime  `json:"updated_at"`
	DeletedAt   sql.NullTime  `json:"deleted_at"`
}

type PodUnitFault struct {
	ID               uint32         `json:"id"`
	PodLocationID    sql.NullInt32  `json:"pod_location_id"`
	PodUnitID        uint32         `json:"pod_unit_id"`
	PodFaultActionID uint32         `json:"pod_fault_action_id"`
	PodFaultStatusID uint32         `json:"pod_fault_status_id"`
	PodFaultID       uint32         `json:"pod_fault_id"`
	PcbID            uint32         `json:"pcb_id"`
	RepairProcess    sql.NullString `json:"repair_process"`
	Notes            sql.NullString `json:"notes"`
	ZendeskNumber    sql.NullString `json:"zendesk_number"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type PodUnitName struct {
	ID        uint32       `json:"id"`
	Ppid      string       `json:"ppid"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type PodUnitUser struct {
	UnitID    uint32    `json:"unit_id"`
	UserID    uint32    `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type PodUnitUserToken struct {
	UserID    uint32        `json:"user_id"`
	UnitID    sql.NullInt32 `json:"unit_id"`
	Token     string        `json:"token"`
	CreatedAt time.Time     `json:"created_at"`
}

type PodVendor struct {
	ID        uint32         `json:"id"`
	Name      sql.NullString `json:"name"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type RcdCheck struct {
	ID            uint32    `json:"id"`
	CheckDatetime time.Time `json:"check_datetime"`
}

type RcdCheckTrippedConnector struct {
	ID           uint32 `json:"id"`
	RcdCheckID   uint32 `json:"rcd_check_id"`
	ConnectorRef string `json:"connector_ref"`
}

type RepairVersion struct {
	ID        uint32       `json:"id"`
	Version   string       `json:"version"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type Report struct {
	ID          uint32         `json:"id"`
	UserID      uint32         `json:"user_id"`
	Params      sql.NullString `json:"params"`
	Url         sql.NullString `json:"url"`
	IsGenerated bool           `json:"is_generated"`
	CreatedAt   sql.NullTime   `json:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at"`
}

type RevenueProfile struct {
	ID        uint32       `json:"id"`
	GroupID   uint32       `json:"group_id"`
	Name      string       `json:"name"`
	Currency  string       `json:"currency"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type RevenueProfileTier struct {
	ID               uint32                      `json:"id"`
	RevenueProfileID uint32                      `json:"revenue_profile_id"`
	Type             RevenueProfileTiersType     `json:"type"`
	UserType         RevenueProfileTiersUserType `json:"user_type"`
	BeginTime        sql.NullTime                `json:"begin_time"`
	BeginDay         sql.NullInt32               `json:"begin_day"`
	EndTime          sql.NullTime                `json:"end_time"`
	EndDay           sql.NullInt32               `json:"end_day"`
	StartSecond      uint32                      `json:"start_second"`
	Rate             uint32                      `json:"rate"`
	CreatedAt        time.Time                   `json:"created_at"`
	UpdatedAt        sql.NullTime                `json:"updated_at"`
	DeletedAt        sql.NullTime                `json:"deleted_at"`
}

type Role struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type Router struct {
	ID            uint32        `json:"id"`
	SerialNumber  string        `json:"serial_number"`
	LanMacAddress string        `json:"lan_mac_address"`
	SimNumber     string        `json:"sim_number"`
	Longitude     string        `json:"longitude"`
	Latitude      string        `json:"latitude"`
	ModelID       sql.NullInt32 `json:"model_id"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at"`
	DeletedAt     sql.NullTime  `json:"deleted_at"`
}

type RouterModel struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type RouterPodUnit struct {
	ID        uint32       `json:"id"`
	RouterID  uint32       `json:"router_id"`
	UnitID    uint32       `json:"unit_id"`
	IsMaster  int8         `json:"is_master"`
	CreatedAt time.Time    `json:"created_at"`
	UpdatedAt time.Time    `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type RouterStatusHistory struct {
	ID        uint32         `json:"id"`
	RouterID  uint32         `json:"router_id"`
	StatusID  uint32         `json:"status_id"`
	Reason    sql.NullString `json:"reason"`
	CreatedAt sql.NullTime   `json:"created_at"`
	UpdatedAt sql.NullTime   `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type Sim struct {
	SimNumber    string         `json:"sim_number"`
	PhoneNumber  sql.NullString `json:"phone_number"`
	IsActive     bool           `json:"is_active"`
	Tariff       sql.NullString `json:"tariff"`
	IpAddress    sql.NullString `json:"ip_address"`
	ProviderID   sql.NullInt32  `json:"provider_id"`
	ConnectedAt  sql.NullTime   `json:"connected_at"`
	ExpiresAt    sql.NullTime   `json:"expires_at"`
	TerminatedAt sql.NullTime   `json:"terminated_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at"`
	DeletedAt    sql.NullTime   `json:"deleted_at"`
}

type SimProvider struct {
	ID        uint32       `json:"id"`
	Name      string       `json:"name"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}

type Socket struct {
	ID          uint32         `json:"id"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	OcppCode    sql.NullString `json:"ocpp_code"`
	OcppType    sql.NullInt32  `json:"ocpp_type"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at"`
	DeletedAt   sql.NullTime   `json:"deleted_at"`
}

type SoftwareVersion struct {
	ID                 uint32         `json:"id"`
	ModuleID           sql.NullInt32  `json:"module_id"`
	HardwareRegisterID sql.NullInt32  `json:"hardware_register_id"`
	Version            string         `json:"version"`
	Description        string         `json:"description"`
	Checksum           sql.NullString `json:"checksum"`
	Url                sql.NullString `json:"url"`
	Signature          sql.NullString `json:"signature"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	DeletedAt          sql.NullTime   `json:"deleted_at"`
}

type SoftwareVersionModule struct {
	ID           uint32 `json:"id"`
	Name         string `json:"name"`
	HasSignature bool   `json:"has_signature"`
}

type Tag struct {
	ID               uint32         `json:"id"`
	BillingAccountID sql.NullInt32  `json:"billing_account_id"`
	Rfid             string         `json:"rfid"`
	Type             sql.NullString `json:"type"`
	Label            sql.NullString `json:"label"`
	IsEnabled        bool           `json:"is_enabled"`
	ExpiresAt        sql.NullTime   `json:"expires_at"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        sql.NullTime   `json:"updated_at"`
	DeletedAt        sql.NullTime   `json:"deleted_at"`
}

type Tariff struct {
	ID               uint32        `json:"id"`
	Currency         string        `json:"currency"`
	Name             string        `json:"name"`
	UserID           sql.NullInt32 `json:"user_id"`
	EnergySupplierID sql.NullInt32 `json:"energy_supplier_id"`
	CreatedAt        sql.NullTime  `json:"created_at"`
	UpdatedAt        sql.NullTime  `json:"updated_at"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

type TariffTier struct {
	ID        uint32        `json:"id"`
	Rate      uint32        `json:"rate"`
	BeginTime sql.NullTime  `json:"begin_time"`
	BeginDay  sql.NullInt32 `json:"begin_day"`
	EndTime   sql.NullTime  `json:"end_time"`
	EndDay    sql.NullInt32 `json:"end_day"`
	TariffID  uint32        `json:"tariff_id"`
	CreatedAt sql.NullTime  `json:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at"`
	DeletedAt sql.NullTime  `json:"deleted_at"`
}

type User struct {
	ID                   uint32         `json:"id"`
	AuthID               string         `json:"auth_id"`
	GroupID              sql.NullInt32  `json:"group_id"`
	RoleID               uint32         `json:"role_id"`
	Email                string         `json:"email"`
	Salutation           sql.NullString `json:"salutation"`
	FirstName            string         `json:"first_name"`
	LastName             string         `json:"last_name"`
	Password             sql.NullString `json:"password"`
	RememberToken        sql.NullString `json:"remember_token"`
	VerifyToken          sql.NullString `json:"verify_token"`
	LastLogin            sql.NullTime   `json:"last_login"`
	ActivatedAt          sql.NullTime   `json:"activated_at"`
	LastNotificationSeen sql.NullTime   `json:"last_notification_seen"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            sql.NullTime   `json:"updated_at"`
	DeletedAt            sql.NullTime   `json:"deleted_at"`
	IsEmailedUsageData   bool           `json:"is_emailed_usage_data"`
}

type VehicleMake struct {
	ID        uint32         `json:"id"`
	Name      string         `json:"name"`
	LogoUrl   sql.NullString `json:"logo_url"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

type VehicleModel struct {
	ID              uint32         `json:"id"`
	Uuid            sql.NullString `json:"uuid"`
	Name            string         `json:"name"`
	VehicleMakeID   uint32         `json:"vehicle_make_id"`
	SocketID        uint32         `json:"socket_id"`
	BatteryCapacity sql.NullString `json:"battery_capacity"`
	StartYear       sql.NullInt32  `json:"start_year"`
	EndYear         sql.NullInt32  `json:"end_year"`
	Capacity        string         `json:"capacity"`
	Range           sql.NullInt32  `json:"range"`
	Type            string         `json:"type"`
	ImageUrl        sql.NullString `json:"image_url"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       sql.NullTime   `json:"deleted_at"`
}

type VehicleModelCountry struct {
	ID             uint32         `json:"id"`
	VehicleModelID uint32         `json:"vehicle_model_id"`
	Country        sql.NullString `json:"country"`
	CreatedAt      sql.NullTime   `json:"created_at"`
	UpdatedAt      sql.NullTime   `json:"updated_at"`
	DeletedAt      sql.NullTime   `json:"deleted_at"`
}

type VehicleModelUser struct {
	ID             uint32 `json:"id"`
	VehicleModelID uint32 `json:"vehicle_model_id"`
	UserID         uint32 `json:"user_id"`
}

type VehicleModelVehicleTag struct {
	VehicleModelID uint32 `json:"vehicle_model_id"`
	VehicleTagID   uint32 `json:"vehicle_tag_id"`
}

type VehicleTag struct {
	ID        uint32       `json:"id"`
	Slug      string       `json:"slug"`
	CreatedAt sql.NullTime `json:"created_at"`
	UpdatedAt sql.NullTime `json:"updated_at"`
	DeletedAt sql.NullTime `json:"deleted_at"`
}
