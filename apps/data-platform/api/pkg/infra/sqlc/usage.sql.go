// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: usage.sql

package sqlc

import (
	"context"
	"time"
)

const retrieveBasicUsageByOrganisation = `-- name: RetrieveBasicUsageByOrganisation :many
WITH charges_by_group_between_dates AS
       (SELECT DATE_TRUNC($1, c.ends_at)                          AS interval_start_date,
               COALESCE(ABS(lk_up.presentment_amount), 0)::bigint AS revenue_generated,
               COALESCE(ROUND(c.kwh_used, 2), 0)::numeric(18, 2)  AS total_energy_usage,
               COALESCE(c.energy_cost, 0) ::bigint                AS energy_cost
        FROM podpoint.charges c
               INNER JOIN aggregate_statistics.charges_lk_up lk_up
                          ON c.id = lk_up.charge_id
                            AND c.deleted_at IS NULL AND c.is_closed = 1
                            AND (c.ends_at BETWEEN $2::timestamptz AND $3::timestamptz)
        WHERE lk_up.group_uid = $4),
     date_series AS
       (SELECT GENERATE_SERIES($2::timestamp, $3::timestamp - INTERVAL '1 day',
                               CONCAT('1 ', $1)::interval) AS interval_start_date,
               0                                           AS revenue_generated,
               0                                           AS total_energy_usage,
               0                                           AS energy_cost)
  (SELECT TO_CHAR(interval_start_date, 'YYYY-MM-DD')                     AS interval_start_date,
          COALESCE(ROUND(SUM(total_energy_usage), 2), 0)::numeric(18, 2) AS total_usage,
          COALESCE(SUM(revenue_generated), 0)::integer                   AS revenue_generated,
          COALESCE(SUM(energy_cost), 0) ::integer                        AS cost
   FROM charges_by_group_between_dates
   GROUP BY interval_start_date
   UNION
   SELECT TO_CHAR(DATE_TRUNC($1, interval_start_date), 'YYYY-MM-DD'),
          revenue_generated,
          total_energy_usage,
          energy_cost
   FROM date_series ds
   WHERE NOT EXISTS(SELECT interval_start_date, revenue_generated, total_energy_usage, energy_cost
                    FROM charges_by_group_between_dates b
                    WHERE TO_CHAR(DATE_TRUNC($1, b.interval_start_date), 'YYYY-MM-DD') =
                          TO_CHAR(DATE_TRUNC($1, ds.interval_start_date), 'YYYY-MM-DD')))
    ORDER BY interval_start_date
`

type RetrieveBasicUsageByOrganisationParams struct {
	DateTrunc string    `json:"date_trunc"`
	Column2   time.Time `json:"column_2"`
	Column3   time.Time `json:"column_3"`
	GroupUid  string    `json:"group_uid"`
}

type RetrieveBasicUsageByOrganisationRow struct {
	IntervalStartDate string `json:"interval_start_date"`
	TotalUsage        string `json:"total_usage"`
	RevenueGenerated  int32  `json:"revenue_generated"`
	Cost              int32  `json:"cost"`
}

func (q *Queries) RetrieveBasicUsageByOrganisation(ctx context.Context, arg RetrieveBasicUsageByOrganisationParams) ([]RetrieveBasicUsageByOrganisationRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveBasicUsageByOrganisation,
		arg.DateTrunc,
		arg.Column2,
		arg.Column3,
		arg.GroupUid,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveBasicUsageByOrganisationRow
	for rows.Next() {
		var i RetrieveBasicUsageByOrganisationRow
		if err := rows.Scan(
			&i.IntervalStartDate,
			&i.TotalUsage,
			&i.RevenueGenerated,
			&i.Cost,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveBasicUsageByOrganisationAndLocation = `-- name: RetrieveBasicUsageByOrganisationAndLocation :many
WITH charges_by_pod_between_dates AS
       (SELECT DATE_TRUNC($1, c.ends_at)                          AS interval_start_date,
               COALESCE(ABS(lk_up.presentment_amount), 0)::bigint AS revenue_generated,
               COALESCE(ROUND(c.kwh_used, 2), 0)::numeric(18, 2)  AS total_energy_usage,
               COALESCE(c.energy_cost, 0) ::bigint                AS energy_cost
        FROM podpoint.charges c
               INNER JOIN aggregate_statistics.charges_lk_up lk_up
                          ON c.id = lk_up.charge_id
                            AND c.deleted_at IS NULL AND c.is_closed = 1
                            AND (c.ends_at BETWEEN $2::timestamptz AND $3::timestamptz)
        WHERE lk_up.group_uid = $4
          AND lk_up.location_id = $5),
     date_series AS
       (SELECT GENERATE_SERIES($2::timestamp, $3::timestamp - INTERVAL '1 day',
                               CONCAT('1 ', $1)::interval) AS interval_start_date,
               0                                           AS revenue_generated,
               0                                           AS total_energy_usage,
               0                                           AS energy_cost)
  (SELECT TO_CHAR(interval_start_date, 'YYYY-MM-DD')                     AS interval_start_date,
          COALESCE(ROUND(SUM(total_energy_usage), 2), 0)::numeric(18, 2) AS total_usage,
          COALESCE(SUM(revenue_generated), 0)::integer                   AS revenue_generated,
          COALESCE(SUM(energy_cost), 0) ::integer                        AS cost
   FROM charges_by_pod_between_dates
   GROUP BY interval_start_date
   UNION
   SELECT TO_CHAR(DATE_TRUNC($1, interval_start_date), 'YYYY-MM-DD'),
          revenue_generated,
          total_energy_usage,
          energy_cost
   FROM date_series ds
   WHERE NOT EXISTS(SELECT interval_start_date, revenue_generated, total_energy_usage, energy_cost
                    FROM charges_by_pod_between_dates b
                    WHERE TO_CHAR(DATE_TRUNC($1
                                    , b.interval_start_date)
                            , 'YYYY-MM-DD') =
                          TO_CHAR(DATE_TRUNC($1
                                    , ds.interval_start_date)
                            , 'YYYY-MM-DD')))
    ORDER BY interval_start_date
`

type RetrieveBasicUsageByOrganisationAndLocationParams struct {
	DateTrunc  string    `json:"date_trunc"`
	Column2    time.Time `json:"column_2"`
	Column3    time.Time `json:"column_3"`
	GroupUid   string    `json:"group_uid"`
	LocationID int32     `json:"location_id"`
}

type RetrieveBasicUsageByOrganisationAndLocationRow struct {
	IntervalStartDate string `json:"interval_start_date"`
	TotalUsage        string `json:"total_usage"`
	RevenueGenerated  int32  `json:"revenue_generated"`
	Cost              int32  `json:"cost"`
}

func (q *Queries) RetrieveBasicUsageByOrganisationAndLocation(ctx context.Context, arg RetrieveBasicUsageByOrganisationAndLocationParams) ([]RetrieveBasicUsageByOrganisationAndLocationRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveBasicUsageByOrganisationAndLocation,
		arg.DateTrunc,
		arg.Column2,
		arg.Column3,
		arg.GroupUid,
		arg.LocationID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveBasicUsageByOrganisationAndLocationRow
	for rows.Next() {
		var i RetrieveBasicUsageByOrganisationAndLocationRow
		if err := rows.Scan(
			&i.IntervalStartDate,
			&i.TotalUsage,
			&i.RevenueGenerated,
			&i.Cost,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const retrieveBasicUsageByOrganisationAndSite = `-- name: RetrieveBasicUsageByOrganisationAndSite :many
WITH charges_by_group_and_site_between_dates AS
       (SELECT DATE_TRUNC($1, c.ends_at)                          AS interval_start_date,
               COALESCE(ABS(lk_up.presentment_amount), 0)::bigint AS revenue_generated,
               COALESCE(ROUND(c.kwh_used, 2), 0)::numeric(18, 2)  AS total_energy_usage,
               COALESCE(c.energy_cost, 0) ::bigint                AS energy_cost
        FROM podpoint.charges c
               INNER JOIN aggregate_statistics.charges_lk_up lk_up
                          ON c.id = lk_up.charge_id
                            AND c.deleted_at IS NULL AND c.is_closed = 1
                            AND (c.ends_at BETWEEN $2::timestamptz AND $3::timestamptz)
        WHERE lk_up.group_uid = $4
          AND lk_up.address_id = $5),
     date_series AS
       (SELECT GENERATE_SERIES($2::timestamp, $3::timestamp - INTERVAL '1 day',
                               CONCAT('1 ', $1)::interval) AS interval_start_date,
               0                                           AS revenue_generated,
               0                                           AS total_energy_usage,
               0                                           AS energy_cost)
  (SELECT TO_CHAR(interval_start_date, 'YYYY-MM-DD')                     AS interval_start_date,
          COALESCE(ROUND(SUM(total_energy_usage), 2), 0)::numeric(18, 2) AS total_usage,
          COALESCE(SUM(revenue_generated), 0)::integer                   AS revenue_generated,
          COALESCE(SUM(energy_cost), 0) ::integer                        AS cost
   FROM charges_by_group_and_site_between_dates
   GROUP BY interval_start_date
   UNION
   SELECT TO_CHAR(DATE_TRUNC($1, interval_start_date), 'YYYY-MM-DD'),
          revenue_generated,
          total_energy_usage,
          energy_cost
   FROM date_series ds
   WHERE NOT EXISTS(SELECT interval_start_date, revenue_generated, total_energy_usage, energy_cost
                    FROM charges_by_group_and_site_between_dates b
                    WHERE TO_CHAR(DATE_TRUNC($1, b.interval_start_date), 'YYYY-MM-DD') =
                          TO_CHAR(DATE_TRUNC($1, ds.interval_start_date), 'YYYY-MM-DD')))
    ORDER BY interval_start_date
`

type RetrieveBasicUsageByOrganisationAndSiteParams struct {
	DateTrunc string    `json:"date_trunc"`
	Column2   time.Time `json:"column_2"`
	Column3   time.Time `json:"column_3"`
	GroupUid  string    `json:"group_uid"`
	AddressID int32     `json:"address_id"`
}

type RetrieveBasicUsageByOrganisationAndSiteRow struct {
	IntervalStartDate string `json:"interval_start_date"`
	TotalUsage        string `json:"total_usage"`
	RevenueGenerated  int32  `json:"revenue_generated"`
	Cost              int32  `json:"cost"`
}

func (q *Queries) RetrieveBasicUsageByOrganisationAndSite(ctx context.Context, arg RetrieveBasicUsageByOrganisationAndSiteParams) ([]RetrieveBasicUsageByOrganisationAndSiteRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveBasicUsageByOrganisationAndSite,
		arg.DateTrunc,
		arg.Column2,
		arg.Column3,
		arg.GroupUid,
		arg.AddressID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveBasicUsageByOrganisationAndSiteRow
	for rows.Next() {
		var i RetrieveBasicUsageByOrganisationAndSiteRow
		if err := rows.Scan(
			&i.IntervalStartDate,
			&i.TotalUsage,
			&i.RevenueGenerated,
			&i.Cost,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
