// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LabelledValue should match snapshot with component value 1`] = `
<body>
  <div>
    <div
      class="break-words"
    >
      <dt
        class="text-md font-medium text-neutral "
      >
        Favourite colour
      </dt>
      <dd
        class="text-md font-semibold text-neutral mt-1"
      >
        <span
          class="bg-warning"
        >
          uh oh
        </span>
      </dd>
    </div>
  </div>
</body>
`;

exports[`LabelledValue should match snapshot with string value 1`] = `
<body>
  <div>
    <div
      class="break-words"
    >
      <dt
        class="text-md font-medium text-neutral "
      >
        Favourite colour
      </dt>
      <dd
        class="text-md font-semibold text-neutral mt-1"
      >
        Orange
      </dd>
    </div>
  </div>
</body>
`;
