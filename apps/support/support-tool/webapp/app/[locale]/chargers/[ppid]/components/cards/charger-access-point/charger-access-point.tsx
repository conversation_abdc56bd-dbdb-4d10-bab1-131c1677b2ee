import { Anchor, Card } from '@experience/shared/react/design-system';
import { ChargingStationSummary } from '@experience/shared/axios/assets-api-client';
import { Door } from '@experience/shared/axios/assets-configuration-api-client';
import { GetWifiCredentials200Response } from '@experience/shared/axios/assets-provisioning-api-client';
import { KeyValueTable } from '@experience/shared/react/layouts';
import { useTranslations } from 'next-intl';
import QRCode from 'react-qr-code';

export interface ChargerAccessPointProps {
  summary: ChargingStationSummary;
  socket: Door;
  wifiCredentials: GetWifiCredentials200Response | undefined;
}

export const ChargerAccessPoint = ({
  summary,
  socket,
  wifiCredentials,
}: ChargerAccessPointProps) => {
  const t = useTranslations('Chargers.Components.Cards');

  const appendSocket = summary.evses.length > 1;

  const redactUrl = appendSocket
    ? `/chargers/${
        summary.ppid
      }?redact=${!!wifiCredentials?.password}&socket=${socket}`
    : `/chargers/${summary.ppid}?redact=${!!wifiCredentials?.password}`;

  return (
    <Card>
      <div className="flex justify-between items-baseline">
        <Card.Header>{t('chargerAccessPointTitle')}</Card.Header>
        {wifiCredentials ? (
          <Anchor href={redactUrl} className="text-right ml-2">
            {wifiCredentials?.password
              ? t('chargerAccessPointHidePassword')
              : t('chargerAccessPointShowPassword')}
          </Anchor>
        ) : null}
      </div>

      <KeyValueTable
        caption={t('chargerAccessPointTableTitle')}
        rows={[
          {
            key: t('chargerAccessPointTableSSIDKey'),
            value: wifiCredentials?.ssid,
          },
          {
            key: t('chargerAccessPointTablePasswordKey'),
            value: wifiCredentials?.ssid
              ? wifiCredentials?.password ?? (
                  <span className="text-neutral">• • • • • • • • • •</span>
                )
              : undefined,
          },
        ]}
      />
      {wifiCredentials?.ssid ? (
        <div
          className={'border-2 border-dotted border-neutral/20 mt-2 h-32 p-3'}
        >
          {wifiCredentials?.password ? (
            <QRCode
              className={'w-full h-full'}
              value={`${summary.ppid},${wifiCredentials?.ssid},${wifiCredentials?.password}`}
            />
          ) : undefined}
        </div>
      ) : null}
    </Card>
  );
};
