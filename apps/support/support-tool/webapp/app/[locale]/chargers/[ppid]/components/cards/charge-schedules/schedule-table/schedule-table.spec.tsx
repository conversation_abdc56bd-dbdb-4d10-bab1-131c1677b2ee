import 'whatwg-fetch';
import { faker } from '@faker-js/faker';
import { getDaysOfWeek } from '@experience/shared/typescript/utils';
import { mockChargeSchedules } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../../../test-utils';
import { screen, within } from '@testing-library/react';
import { setIn } from 'immutable';
import ScheduleTable from './schedule-table';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const defaultProps = {
  schedules: mockChargeSchedules.data,
  smartMode: true,
};

describe('ScheduleTable', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(
      <ScheduleTable {...defaultProps} />
    );
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <ScheduleTable {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a table caption', () => {
    renderWithProviders(<ScheduleTable {...defaultProps} />);
    expect(
      screen.getByText('Table of charge schedules and modes')
    ).toBeInTheDocument();
  });

  it('should have rows for each schedule in the table', () => {
    renderWithProviders(<ScheduleTable {...defaultProps} />);

    const rows = document.querySelectorAll('tbody tr');
    expect(rows.length).toBe(7);
  });

  it('should render the start day, time and duration for each row', () => {
    renderWithProviders(<ScheduleTable {...defaultProps} />);

    const [firstRow] = screen.getAllByRole('row', { name: /Monday/i });
    expect(within(firstRow).getByText('10:00:00')).toBeInTheDocument();
    expect(within(firstRow).getByText('4 hours')).toBeInTheDocument();
  });

  it('should render correct duration for end day after start day', () => {
    renderWithProviders(
      <ScheduleTable
        schedules={[
          {
            uid: faker.string.uuid(),
            start_day: 1,
            start_time: '18:00:00',
            end_day: 2,
            end_time: '06:00:00',
            status: { is_active: true },
          },
        ]}
        smartMode={true}
      />
    );

    const [firstRow] = screen.getAllByRole('row', { name: /Monday/i });
    expect(within(firstRow).getByText('18:00:00')).toBeInTheDocument();
    expect(within(firstRow).getByText('12 hours')).toBeInTheDocument();
  });

  it('should render all schedules as inactive when smart mode is off', () => {
    renderWithProviders(<ScheduleTable {...defaultProps} smartMode={false} />);

    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row) => {
      expect(within(row as HTMLElement).getByText('No')).toBeInTheDocument();
    });
  });

  it('should sort schedules in day order', () => {
    const unorderedSchedules = [
      {
        uid: faker.string.uuid(),
        start_day: 5,
        start_time: '18:00:00',
        end_day: 6,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 3,
        start_time: '18:00:00',
        end_day: 4,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 4,
        start_time: '18:00:00',
        end_day: 5,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 6,
        start_time: '18:00:00',
        end_day: 7,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 2,
        start_time: '18:00:00',
        end_day: 3,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 7,
        start_time: '18:00:00',
        end_day: 1,
        end_time: '06:00:00',
        status: { is_active: true },
      },
      {
        uid: faker.string.uuid(),
        start_day: 1,
        start_time: '18:00:00',
        end_day: 2,
        end_time: '06:00:00',
        status: { is_active: true },
      },
    ];

    renderWithProviders(
      <ScheduleTable
        {...setIn(defaultProps, ['schedules'], unorderedSchedules)}
      />
    );
    const [, ...rows] = screen.getAllByRole('row');

    rows.forEach((row, index) => {
      expect(within(row).getByText(getDaysOfWeek()[index])).toBeInTheDocument();
    });
  });
});
