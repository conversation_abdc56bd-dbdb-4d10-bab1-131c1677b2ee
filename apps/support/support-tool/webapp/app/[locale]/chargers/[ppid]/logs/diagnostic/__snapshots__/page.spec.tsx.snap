// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`View diagnostic logs page should match snapshot 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                Diagnostic logs - PSL-12345 (Socket A)
              </h1>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500 text-info border-b-info hover:text-info cursor-default"
                  disabled=""
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  Socket C
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  Socket D
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  Socket E
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  Socket F
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <button
              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
              type="button"
            >
              Open log viewer
            </button>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of diagnostic logs
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Start
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  End
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-01-01 00:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-01-02 00:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                COMPLETED
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                  href="www.test-url.com"
                >
                  Download
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
