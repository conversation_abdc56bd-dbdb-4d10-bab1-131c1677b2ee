// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Flex details page content  should match snapshot 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <h1
            class="text-xxl font-bold"
          >
            Flex details - PSL-12345
          </h1>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Sarah Rogahn
          </h3>
        </div>
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Flex programmes
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Programme status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Active
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Enrollment date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    2021-09-01 00:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Unenrollment date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Enrolled
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Karen Simonis
          </h3>
        </div>
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Flex programmes
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Programme status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Inactive
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Enrollment date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    2021-09-01 00:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Unenrollment date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    2021-09-02 00:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Unenrolled
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Flex events
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Direction
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Requested
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Scheduled start
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Scheduled end
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Skipped
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Flex up
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Flex down
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Flex up
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Flex down
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Flex up
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-08-01 01:00:00
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
