// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Recent charges table should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Grid energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Solar energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Recent charges table should match snapshot for COMMERCIAL unit 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Recent charges table should match snapshot for DOMESTIC unit 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Grid energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Solar energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Recent charges table should match snapshot if the charger is domestic and the architecture is pre arch5 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Grid energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Solar energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                N/A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Recent charges table should match snapshot with charges 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Grid energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Solar energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 18:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2024-06-30 - 20:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20 odd:bg-gray-50"
            >
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4 text-nowrap"
              >
                2018-05-15 - 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                532.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                35.44
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.32
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                06:10:45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="flex justify-center mr-4"
                    >
                      <button
                        aria-label="Edit energy cost 5588483f-7125-414a-887f-6fdc37911182"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        id="edit-energy-cost-5588483f-7125-414a-887f-6fdc37911182"
                        name="edit-energy--cost-5588483f-7125-414a-887f-6fdc37911182"
                        type="button"
                      >
                        <svg
                          class="fill-current h-4 w-4"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g>
                            <path
                              d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                            />
                            <path
                              d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                            />
                          </g>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Recent charges table should match snapshot without charges 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="plugInAt"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="plugInAt"
              id="headlessui-label-:test-id-1"
            >
              Plugged in
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 plugInAt"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="plugInAt"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Last 12 months
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="plugInAt"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="from=&to="
              />
            </span>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of recent completed charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Started at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Ended at
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Unplugged
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Grid energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Solar energy (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Total duration
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Socket
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Actions
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          />
        </table>
      </div>
    </div>
  </div>
</body>
`;
