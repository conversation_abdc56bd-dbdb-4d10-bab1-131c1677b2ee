import {
  Button,
  ButtonTypes,
  Modal,
  ModalProps,
  Paragraph,
  Select,
  TextSize,
  TextWeight,
} from '@experience/shared/react/design-system';
import {
  SetChargeNowOnFormState,
  setChargeNowOn,
} from '../../actions/set-charge-now';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { useState } from 'react';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

export interface ChargeNowModalProps {
  open: boolean;
  ppid: string;
  setIsChargeNow: (value: boolean) => void;
  setOpen: (open: boolean) => void;
}

const hoursOptions = Array.from({ length: 13 }, (_, i) => {
  const value = i.toString();
  return {
    id: value,
    name: value,
  };
});

const minutesOptions = Array.from({ length: 12 }, (_, i) => {
  const value = (i * 5).toString();
  return {
    id: value,
    name: value,
  };
});

export const ChargeNowModal = ({
  open,
  ppid,
  setIsChargeNow,
  setOpen,
}: ChargeNowModalProps) => {
  const t = useTranslations('Chargers.Components.ChargeModes');
  const defaultFormState: SetChargeNowOnFormState = {
    success: null,
    errors: { duration: '' },
  };
  const [formState, setFormState] =
    useState<SetChargeNowOnFormState>(defaultFormState);
  const [hours, setHours] = useState<string>('0');
  const [minutes, setMinutes] = useState<string>('15');

  const formId = 'charge-now-form';

  const handleSubmit = async (data: FormData): Promise<void> => {
    try {
      const state = await setChargeNowOn(data);
      setFormState(state);
      if (state.success === true) {
        toast.success(t('chargeNowSwitchToActive'));
        setIsChargeNow(true);
        setOpen(false);
      }
    } catch (error) {
      toast.error(t('chargeNowToastError'));
    }
  };

  const handleCancel = (): void => {
    setFormState(defaultFormState);
    setOpen(false);
  };

  const form = (
    <form className="-mb-6" name={formId} action={handleSubmit}>
      <input type="hidden" name="ppid" value={ppid} />

      <div className="flex items-center space-x-4">
        <div className="flex-grow">
          <Select
            errorState={!!formState.errors.duration}
            id="hours"
            label={t('chargeNowModalHoursLabel')}
            name="hours"
            options={hoursOptions}
            placeholder=""
            onChange={(value) => {
              setHours(value as string);
            }}
            selected={hoursOptions.find((option) => option.id === hours)}
          />
        </div>
        <div className="flex-grow">
          <Select
            errorState={!!formState.errors.duration}
            id="minutes"
            label={t('chargeNowModalMinutesLabel')}
            name="minutes"
            options={minutesOptions}
            placeholder=""
            onChange={(value) => {
              setMinutes(value as string);
            }}
            selected={minutesOptions.find((option) => option.id === minutes)}
          />
        </div>
      </div>
      {formState.errors.duration ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-error"
        >
          {t('chargeNowModalDurationErrorMessage', {
            errorMessage: formState.errors.duration,
          })}
        </Paragraph>
      ) : null}

      <VerticalSpacer />
      <div className="flex justify-between">
        <Button
          buttonType={ButtonTypes.LINK}
          onClick={handleCancel}
          className="mr-4"
        >
          {t('chargeNowModalCancelButton')}
        </Button>
        <Button type="submit">{t('chargeNowModalSaveChangesButton')}</Button>
      </div>
    </form>
  );

  const modalProps: ModalProps = {
    open,
    title: t('chargeNowModalHeading'),
    content: form,
    handleCancel,
    setOpen,
  };

  return <Modal {...modalProps} />;
};

export default ChargeNowModal;
