// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OtherConfigurationValues should match snapshot 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Other configuration values
      </h3>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of other configuration values
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  ChargeCurrentLimitA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  32
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OffMode
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OfflineSchedulingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PPDevClampFaultThreshold
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  2
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingCurrentLimitImportA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  50
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensor
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  NONE
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorPolarityInverted
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarExportMargin
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  5
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMatchingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMaxGridImport
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  4.1
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStartHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStopHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  64
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarSystemInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  FirmwareVersion
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  1.0.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  LinkyScheduleEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  RcdBreakerSize
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  UnlockConnectorOnEVSideDisconnect
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</body>
`;
