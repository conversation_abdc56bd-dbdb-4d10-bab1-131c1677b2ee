// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ChargeSchedules should match snapshot 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          Edit schedules
        </a>
      </div>
      <div
        class="rounded-md p-3 bg-info/10 border-2 border-info/30"
      >
        <div
          class="flex"
        >
          <div
            class="shrink-0"
          >
            <span
              class="text-info"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                  />
                  <path
                    d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                  />
                  <path
                    d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                  />
                </g>
              </svg>
            </span>
          </div>
          <div
            class="flex flex-col justify-center ml-4"
          >
            <p
              class="text-md font-normal sr-only break-words"
            >
              Alert
            </p>
            <div
              class="text-info"
            >
              <p
                class="text-md font-normal break-words"
              >
                This charger is currently in manual mode. The below schedules will not take effect.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of charge schedules and modes
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left text-center p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Monday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      10:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      4 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Tuesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      20:35:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      3 hours 15 minutes
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Wednesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Thursday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      16:10:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      1 hour 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Friday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Saturday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Sunday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-red-600"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="recharts-responsive-container"
          style="width: 100%; height: 320px; min-width: 0;"
        />
      </div>
    </section>
  </div>
</body>
`;
