import { API3ChargeSchedule } from '@experience/shared/axios/smart-charging-service-client';
import {
  CheckmarkCircleIcon,
  CrossCircleIcon,
} from '@experience/shared/react/design-system-icons';
import { Paragraph, Table } from '@experience/shared/react/design-system';
import {
  getTimeDifferenceInHoursAndMinutesAsString,
  isChargeScheduleActive,
} from '../charge-schedule-utils';
import { nextIntlTimeFormat } from '@experience/shared/typescript/utils';
import { useFormatter, useTranslations } from 'next-intl';
import dayjs from 'dayjs';

interface ScheduleTableProps {
  schedules?: API3ChargeSchedule[];
  smartMode: boolean;
}

export const ScheduleTable = ({ schedules, smartMode }: ScheduleTableProps) => {
  const t = useTranslations('Chargers.Components.ChargeSchedulesTable');
  const format = useFormatter();

  return (
    <Table caption={t('caption')}>
      <Table.Header>
        <Table.Row>
          <Table.Heading>{t('startHeading')}</Table.Heading>
          <Table.Heading>{t('durationHeading')}</Table.Heading>
          <Table.Heading center>{t('activeHeading')}</Table.Heading>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {schedules
          ?.sort((a, b) => a.start_day - b.start_day)
          .map((schedule) => (
            <Table.Row key={schedule.uid}>
              <Table.Data>
                <Paragraph>
                  {t(`scheduleDayOfWeek${schedule.start_day}`)}
                </Paragraph>
                <Paragraph>
                  {format.dateTime(
                    dayjs(`2020-01-01 ${schedule.start_time}`).toDate(),
                    nextIntlTimeFormat
                  )}
                </Paragraph>
              </Table.Data>
              <Table.Data>
                <Paragraph>
                  {getTimeDifferenceInHoursAndMinutesAsString(schedule)}
                </Paragraph>
              </Table.Data>
              <Table.Data>
                {isChargeScheduleActive(schedule.status, smartMode) ? (
                  <CheckmarkCircleIcon.LIGHT
                    className="mx-auto text-primary"
                    height="h-6"
                    title={t('activeScheduleYes')}
                    width="w-6"
                  />
                ) : (
                  <CrossCircleIcon.LIGHT
                    className="mx-auto text-red-600"
                    height="h-6"
                    title={t('activeScheduleNo')}
                    width="w-6"
                  />
                )}
              </Table.Data>
            </Table.Row>
          ))}
      </Table.Body>
    </Table>
  );
};

export default ScheduleTable;
