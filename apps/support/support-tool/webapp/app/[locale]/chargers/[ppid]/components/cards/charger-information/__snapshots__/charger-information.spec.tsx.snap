// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ChargerInformation should match snapshot with operator tag 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white md:col-span-2 lg:col-span-1"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger information
        </h3>
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/logs/diagnostic?socket=A"
        >
          View diagnostic logs
        </a>
      </div>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Charger information table
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Model SKU
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  T7-S-07-AMC-BLK
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Arch version
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  5.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Firmware
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  A50P-X.Y.Z-0000P
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Manufactured
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  1 June 2023
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Operator
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  Test Operator
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  MAC address
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  0cb2b703c778
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PCBA serial number
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  30303030
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="flex flex-row-reverse"
      >
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/pcbs/history?socket=A"
        >
          History
        </a>
      </div>
    </section>
  </div>
</body>
`;

exports[`ChargerInformation should match snapshot without operator tag 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white md:col-span-2 lg:col-span-1"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger information
        </h3>
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/logs/diagnostic?socket=A"
        >
          View diagnostic logs
        </a>
      </div>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Charger information table
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Model SKU
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  T7-S-07-AMC-BLK
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Arch version
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  5.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Firmware
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  A50P-X.Y.Z-0000P
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Manufactured
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  1 June 2023
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  Operator
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  Pod Point
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  MAC address
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  0cb2b703c778
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PCBA serial number
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                >
                  30303030
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="flex flex-row-reverse"
      >
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/pcbs/history?socket=A"
        >
          History
        </a>
      </div>
    </section>
  </div>
</body>
`;
