'use client';

import { AccountDetails } from '../../actions/get-details';
import { AccountStatusBadge } from '../../components/account-status-badge';
import {
  Anchor,
  ArrowLeftIcon,
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { EmailVerificationBadge } from '../../components/email-verification-badge';
import { LabelledValue } from '../../../components/labelled-value/labelled-value';
import { TopCardHeading } from '../../../components/top-card-layout/top-card-layout';
import {
  VerticalSpacer,
  VerticalSpacerSize,
} from '@experience/shared/react/layouts';
import {
  formatPenceAsCurrencyString,
  nextIntlLongDateTimeFormat,
} from '@experience/shared/typescript/utils';
import { useFormatter, useTranslations } from 'next-intl';
import AccountChargers from './chargers/account-chargers';
import AccountFactors from './factors/account-factors';
import ActionMenu from './actions-menu/action-menu';
import AvatarPlaceholder from '../../../components/avatar-placeholder/avatar-placeholder';

interface PageContentProps {
  details: AccountDetails;
}

export const LocaleDisplayNames: { [key: string]: string } = {
  en: '🇬🇧 United Kingdom',
  es: '🇪🇸 Spain',
  fr: '🇫🇷 France',
  ie: '🇮🇪 Republic of Ireland',
  no: '🇳🇴 Norway',
};

export const PageContent = ({ details }: PageContentProps) => {
  const t = useTranslations('Accounts.DetailsPage');
  const format = useFormatter();

  if (!details) {
    return null;
  }

  const {
    accountCreationTimestamp,
    authId,
    balance,
    email,
    firstName,
    isEmailVerified,
    lastName,
    lastSignInTimestamp,
    locale,
    status,
  } = details;

  const fullName = `${firstName} ${lastName}`;

  const createdAtLabel = accountCreationTimestamp
    ? format.dateTime(accountCreationTimestamp, {
        ...nextIntlLongDateTimeFormat,
        second: undefined,
      })
    : '–';

  const lastSignInLabel = lastSignInTimestamp
    ? format.dateTime(lastSignInTimestamp, {
        ...nextIntlLongDateTimeFormat,
        second: undefined,
      })
    : '–';

  const localeDisplayName = locale
    ? LocaleDisplayNames[locale] || locale?.toUpperCase()
    : t('noDisplayName');

  const accountBalance = balance
    ? formatPenceAsCurrencyString({
        amount: balance.amount,
        options: { style: 'currency', currency: balance.currency },
      })
    : null;

  return (
    <>
      <Anchor
        className="flex items-center gap-1 mb-2 font-bold max-w-fit"
        href={`/accounts`}
      >
        <ArrowLeftIcon.LIGHT width="w-4 h-4" />
        {t('backLinkLabel')}
      </Anchor>
      <Card>
        <div className="flex flex-row justify-between">
          <TopCardHeading heading={t('accountDetailsPageHeading')} />
          <ActionMenu
            authId={authId}
            firstName={firstName}
            lastName={lastName}
            email={email}
            active={status === 'active'}
            balance={balance}
            isVerified={isEmailVerified}
            region={locale}
          />
        </div>

        <VerticalSpacer />
        <div className="flex flex-row">
          <AvatarPlaceholder name={fullName} />

          <div className="flex-1 flex flex-col justify-center">
            <div className="flex flex-row">
              <Heading.H3 className="font-bold">{fullName}</Heading.H3>
            </div>

            <div className="flex flex-row items-center">
              <Paragraph className="text-neutral">{email}</Paragraph>
            </div>
          </div>
        </div>

        <div className="flex flex-row pt-3 pb-1 text-neutral">
          <Paragraph className="mr-8">
            {t('accountCreated', { createdAt: createdAtLabel })}
          </Paragraph>
          <Paragraph className="mr-8">
            {t('accountLastSignIn', { signedIn: lastSignInLabel })}
          </Paragraph>
        </div>

        <VerticalSpacer />

        <dl className="flex flex-row flex-wrap gap-x-9 gap-y-5">
          <LabelledValue
            label={t('accountStatusLabel')}
            value={<AccountStatusBadge status={status} />}
          />
          <LabelledValue
            label={t('emailVerificationLabel')}
            value={<EmailVerificationBadge isVerified={isEmailVerified} />}
          />
          <LabelledValue label={t('regionLabel')} value={localeDisplayName} />
          <LabelledValue
            label={t('accountBalanceLabel')}
            value={accountBalance}
          />
        </dl>
      </Card>

      <VerticalSpacer />

      <section>
        <Heading.H2 fontSize={HeadingSizes.M} className="mt-8">
          {t('factorsTabTitle')}
        </Heading.H2>
        <VerticalSpacer size={VerticalSpacerSize.Small} />
        <AccountFactors authId={authId} />
      </section>

      <VerticalSpacer />

      <section>
        <Heading.H2 fontSize={HeadingSizes.M} className="mt-8">
          {t('chargersTabTitle')}
        </Heading.H2>
        <VerticalSpacer size={VerticalSpacerSize.Small} />
        <AccountChargers authId={authId} />
      </section>
    </>
  );
};

export default PageContent;
