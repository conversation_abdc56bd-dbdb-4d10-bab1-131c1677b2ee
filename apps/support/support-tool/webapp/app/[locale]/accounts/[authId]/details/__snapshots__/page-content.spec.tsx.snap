// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Accounts page content should match snapshot 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/accounts"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="flex flex-row justify-between"
      >
        <h1
          class="text-xxl font-bold"
        >
          Account details
        </h1>
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed ml-4"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Actions
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <div
        class="flex flex-row"
      >
        <div
          class="bg-neutral/20 rounded-full w-20 h-20 mr-4 flex items-center justify-center"
        >
          <span
            class="font-semibold text-xxl"
          >
            JD
          </span>
        </div>
        <div
          class="flex-1 flex flex-col justify-center"
        >
          <div
            class="flex flex-row"
          >
            <h3
              class="text-xxl font-bold"
            >
              Jane Doe
            </h3>
          </div>
          <div
            class="flex flex-row items-center"
          >
            <p
              class="text-md font-normal text-neutral break-words"
            />
          </div>
        </div>
      </div>
      <div
        class="flex flex-row pt-3 pb-1 text-neutral"
      >
        <p
          class="text-md font-normal mr-8 break-words"
        >
          Created: 30 April 2024 at 10:51
        </p>
        <p
          class="text-md font-normal mr-8 break-words"
        >
          Last sign in: 30 April 2024 at 10:51
        </p>
      </div>
      <div
        class="pb-4"
      />
      <dl
        class="flex flex-row flex-wrap gap-x-9 gap-y-5"
      >
        <div
          class="break-words"
        >
          <dt
            class="text-md font-medium text-neutral "
          >
            Account status
          </dt>
          <dd
            class="text-md font-semibold text-neutral mt-1"
          >
            <span
              aria-label="Active"
              class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary text-sm uppercase"
              role="status"
            >
              <span
                class="h-4 w-4 mr-1"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    Checkmark
                  </title>
                  <path
                    d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
                    id="prod"
                  />
                </svg>
              </span>
              Active
            </span>
          </dd>
        </div>
        <div
          class="break-words"
        >
          <dt
            class="text-md font-medium text-neutral "
          >
            Email verification
          </dt>
          <dd
            class="text-md font-semibold text-neutral mt-1"
          >
            <span
              aria-label="Not verified"
              class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 text-sm uppercase"
              role="status"
            >
              <span
                class="h-4 w-4 mr-1"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="16"
                    cy="16"
                    r="12"
                  />
                </svg>
              </span>
              Not verified
            </span>
          </dd>
        </div>
        <div
          class="break-words"
        >
          <dt
            class="text-md font-medium text-neutral "
          >
            Region
          </dt>
          <dd
            class="text-md font-semibold text-neutral mt-1"
          >
            🇬🇧 United Kingdom
          </dd>
        </div>
        <div
          class="break-words"
        >
          <dt
            class="text-md font-medium text-neutral "
          >
            Account balance
          </dt>
          <dd
            class="text-md font-semibold text-neutral mt-1"
          >
            £10.99
          </dd>
        </div>
      </dl>
    </section>
    <div
      class="pb-4"
    />
    <section>
      <h2
        class="text-xxl mt-8"
      >
        Authentication (2FA)
      </h2>
      <div
        class="pb-2"
      />
      <div
        role="status"
        title="Loading..."
      >
        <svg
          aria-hidden="true"
          class="h-16 w-16 animate-spin fill-primary text-neutral/30"
          viewBox="0 0 100 101"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </g>
        </svg>
        <span
          class="sr-only"
        >
          Loading...
        </span>
      </div>
    </section>
    <div
      class="pb-4"
    />
    <section>
      <h2
        class="text-xxl mt-8"
      >
        Chargers
      </h2>
      <div
        class="pb-2"
      />
      <div
        role="status"
        title="Loading..."
      >
        <svg
          aria-hidden="true"
          class="h-16 w-16 animate-spin fill-primary text-neutral/30"
          viewBox="0 0 100 101"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </g>
        </svg>
        <span
          class="sr-only"
        >
          Loading...
        </span>
      </div>
    </section>
  </div>
</body>
`;
