import 'whatwg-fetch';
import { PersistedSubscriptionDTOActionsInner } from '@experience/mobile/subscriptions-api/axios';
import {
  SubscriptionActions,
  SubscriptionActionsProps,
} from './subscription-actions';
import { mockSubscription } from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../test-utils';
import { screen } from '@testing-library/react';

const defaultProps: SubscriptionActionsProps = {
  actions: mockSubscription.actions,
};

describe('subscription actions', () => {
  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <SubscriptionActions {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <SubscriptionActions {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render all actions and their status', () => {
    const actions = [
      { type: 'PAY_UPFRONT_FEE_V1', status: 'PENDING' },
      { type: 'COMPLETE_HOME_SURVEY_V1', status: 'SUCCESS' },
      { type: 'CHECK_AFFORDABILITY_V1', status: 'FAILURE' },
      { type: 'SETUP_DIRECT_DEBIT_V1', status: 'PENDING' },
      { type: 'SIGN_DOCUMENTS_V1', status: 'PENDING' },
      { type: 'INSTALL_CHARGING_STATION_V1', status: 'PENDING' },
    ] as PersistedSubscriptionDTOActionsInner[];

    renderWithProviders(<SubscriptionActions actions={actions} />);

    const [payUpfrontFee] = screen.getAllByText('Pay upfront fee');
    const [completeHomeSurvey] = screen.getAllByText('Survey completed');
    const [checkAffordability] = screen.getAllByText('Affordability passed');
    const [setupDirectDebit] = screen.getAllByText('Direct debit set up');
    const [signDocuments] = screen.getAllByText('Legal documents signed');
    const [installChargingStation] = screen.getAllByText(
      'Charging station installed'
    );

    expect(payUpfrontFee.parentNode).toHaveClass('text-yellow-500');
    expect(completeHomeSurvey.parentNode).toHaveClass('text-primary');
    expect(checkAffordability.parentNode).toHaveClass('text-red-600');
    expect(setupDirectDebit.parentNode).toHaveClass('text-yellow-500');
    expect(signDocuments.parentNode).toHaveClass('text-yellow-500');
    expect(installChargingStation.parentNode).toHaveClass('text-yellow-500');
  });

  it('should map a new action', () => {
    const actions = [
      { type: 'NEW_ACTION_V1', status: 'PENDING' },
    ] as unknown as PersistedSubscriptionDTOActionsInner[];

    renderWithProviders(<SubscriptionActions actions={actions} />);

    const [newAction] = screen.getAllByText('NEW_ACTION_V1');
    expect(newAction.parentNode).toHaveClass('text-yellow-500');
  });
});
