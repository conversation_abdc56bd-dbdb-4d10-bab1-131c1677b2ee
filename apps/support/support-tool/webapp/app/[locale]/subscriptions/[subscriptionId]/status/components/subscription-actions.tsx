import {
  ActionStatus,
  PersistedSubscriptionDTOActionsInner,
} from '@experience/mobile/subscriptions-api/axios';
import { Card } from '@experience/shared/react/design-system';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { useTranslations } from 'next-intl';
import IconWithText from '../../../../chargers/[ppid]/components/icon-with-text/icon-with-text';

export interface SubscriptionActionsProps {
  actions: PersistedSubscriptionDTOActionsInner[];
}

type ActionsMap = Record<string, PersistedSubscriptionDTOActionsInner | null>;
type BadgeIcons = 'CheckmarkCircleIcon' | 'CrossCircleIcon' | 'WarningIcon';

const mapActions = (
  actions: PersistedSubscriptionDTOActionsInner[]
): ActionsMap =>
  actions.reduce(
    (result, action) => {
      result[action.type] = action;
      return result;
    },
    {
      PAY_UPFRONT_FEE_V1: null,
      COMPLETE_HOME_SURVEY_V1: null,
      CHECK_AFFORDABILITY_V1: null,
      SETUP_DIRECT_DEBIT_V1: null,
      SIGN_DOCUMENTS_V1: null,
      INSTALL_CHARGING_STATION_V1: null,
    } as ActionsMap
  );

const getActionStatusIcon = (status?: ActionStatus): BadgeIcons => {
  switch (status) {
    case 'SUCCESS':
      return 'CheckmarkCircleIcon';
    case 'FAILURE':
      return 'CrossCircleIcon';
    case 'PENDING':
      return 'WarningIcon';
    default:
      return 'CrossCircleIcon';
  }
};

const getActionStatusClassName = (status?: ActionStatus): string => {
  switch (status) {
    case 'SUCCESS':
      return 'text-primary';
    case 'FAILURE':
      return 'text-red-600';
    case 'PENDING':
      return 'text-yellow-500';
    default:
      return 'text-red-600';
  }
};

export const SubscriptionActions = ({ actions }: SubscriptionActionsProps) => {
  const t = useTranslations('Subscriptions.SubscriptionActions');

  const actionMap: Record<string, string> = {
    PAY_UPFRONT_FEE_V1: 'payUpfrontFee',
    COMPLETE_HOME_SURVEY_V1: 'completeHomeSurvey',
    CHECK_AFFORDABILITY_V1: 'checkAffordability',
    SETUP_DIRECT_DEBIT_V1: 'setupDirectDebit',
    SIGN_DOCUMENTS_V1: 'signDocuments',
    INSTALL_CHARGING_STATION_V1: 'chargingStationInstalled',
  };

  return (
    <Card>
      {Object.keys(mapActions(actions)).map((action, index, target) => {
        const mappedActions = mapActions(actions);
        const actionData = mappedActions[action];

        if (!actionData) return null;

        const translationKey = actionMap[actionData.type];

        return (
          <>
            <IconWithText
              key={actionData.type}
              icon={getActionStatusIcon(actionData.status)}
              iconClassName={getActionStatusClassName(actionData.status)}
              title={translationKey ? t(translationKey) : actionData.type}
              text={translationKey ? t(translationKey) : actionData.type}
            />
            {index < target.length - 1 ? <VerticalSpacer /> : null}
          </>
        );
      })}
    </Card>
  );
};
