{"Layout": {"navigationHomeLink": "<PERSON><PERSON>o", "navigationAccountsLink": "Cuentas", "navigationChargersLink": "Cargadores", "navigationSubscriptionsLink": "Suscripciones"}, "Home": {"heading": "Soporte", "accountsCardHeading": "Cuentas", "accountsCardDescription": "Ver y administrar la configuración de la cuenta", "accountsCardLink": "Buscar cuenta", "chargersCardHeading": "Cargadores", "chargersCardDescription": "Ver actividad y administrar la configuración del cargador", "chargersCardLink": "Buscar cargadores", "subscriptionsCardHeading": "Suscripciones", "subscriptionsCardDescription": "Ver y administrar las suscripciones de Pod Drive", "subscriptionsCardLink": "Buscar suscripciones", "title": "Soporte"}, "Accounts": {"AccountStatusBadgeComponent": {"activeLabel": "Activo", "disabledLabel": "Desactivado", "defaultLabel": "Desconocido"}, "EmailVerificationBadgeComponent": {"verifiedLabel": "Verificado", "unverifiedLabel": "No verificado"}, "SearchPage": {"heading": "Cuentas", "searchCardHeading": "Buscar una cuenta:", "searchCardEmailLabel": "E-mail", "searchCardSubmitButton": "<PERSON>r", "noSearchResults": "Ninguna cuenta coincide con la búsqueda actual.", "searchResultsLink": "Abrir", "title": "Herramienta de soporte - cuentas", "emailPlaceholder": "<EMAIL>"}, "DetailsPage": {"accountDetailsPageHeading": "Detalles de la cuenta", "actionMenuEditName": "Editar nombre", "actionMenuEditEmailAddress": "Editar dirección de correo", "actionMenuEditRegion": "Editar regi<PERSON>", "actionMenuSendPasswordRecovery": "Enviar recuperación de contraseña", "actionMenuDeactivateAccount": "Desactivar cuenta", "actionMenuReactivateAccount": "Reactivar cuenta", "actionMenuResendEmailVerification": "Reenviar email de verificación", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Guardar", "saveChangesButton": "Guardar cambios", "noDisplayName": "No establecido", "backLinkLabel": "Atrás", "accountCreated": "Creado: {createdAt}", "accountLastSignIn": "Última sesión en: {signedIn}", "accountStatusLabel": "Estado de la cuenta", "emailVerificationLabel": "Verificación de email", "regionLabel": "Región", "accountBalanceLabel": "Saldo de la cuenta", "chargersTabTitle": "Cargadores", "factorsTabTitle": "Autenticación (2FA)", "activityTableTitle": "Actividad", "activityTableDateHeading": "<PERSON><PERSON>", "activityTableTimeHeading": "<PERSON><PERSON>", "activityTableEventHeading": "Evento", "activityTableDetailsHeading": "Detalles", "activityTableStatusHeading": "Estado", "success": "Éxito", "failed": "Fallo", "factorsTableTitle": "Factores", "factorsTableCountryCode": "Código de <PERSON>ís", "factorsTableLastThreeDigits": "Últimos 3 dígitos del número de teléfono", "factorsTableEnrollmentTime": "Inscrito", "chargersTableTitle": "Cargadores", "chargersTablePpidHeading": "PPID", "chargersTableActionsHeading": "Acciones", "unlinkChargerButton": "Desvincular cargador", "accountPaymentsTableTitle": "<PERSON><PERSON> por {authId}", "deactivatedAccountModalTitle": "Desactivar usuario", "deactivatedAccountModalParagraph": "La sesión de cliente de todos los servicios será cerrada y no podrá volver a iniciar sesión.", "deactivatedAccountModalConfirmButton": "Desactivar cuenta", "deactivatedAccountSuccessMessage": "Cuenta de cliente desactivada - puede tardar unos segundos para que se aplique este cambio", "deactivatedAccountFailedMessage": "Error al desactivar la cuenta", "deactivatedAccountRefundAlertTitle": "Reembolso requer<PERSON>", "deactivatedAccountRefundAlertParagraph": "Por favor, asegúrese de reembolsar al cliente ya que su cuenta está actualmente {balanceString} en crédito.", "deactivatedAccountPaymentAlertTitle": "Pago re<PERSON>", "deactivatedAccountPaymentAlertParagraph": "El cliente debe recargar para limpiar el saldo de su cuenta de {balanceString} antes de cerrar la cuenta.", "editNameSuccessMessage": "Detalles de usuario actualizados esto puede tardar unos segundos en actualizar", "editNameFailedMessage": "Error al actualizar los detalles del usuario", "editNameModalTitle": "Editar nombre", "editNameModalFirstNameLabel": "Nombre", "editNameModalLastNameLabel": "Apellido", "editRegionSuccessMessage": "Detalles del usuario actualizados correctamente", "editRegionFailedMessage": "Error al actualizar los detalles del usuario", "editRegionFormRegionLabel": "Región", "editRegionFormRegionSelectPlaceholder": "Seleccione una región", "editRegionModalTitle": "Editar regi<PERSON>", "reactivateAccountSuccessMessage": "Cuenta de cliente reactivada: este cambio puede tardar unos segundos en ser aplicado", "reactivateAccountFailedMessage": "Error al reactivar la cuenta", "reactivateAccountModalTitle": "Reactivar usuario", "reactivateAccountModalParagraph": "La cuenta de cliente será reactivada, permitiéndole iniciar sesión.", "reactivateAccountModalConfirmButton": "Reactivar cuenta", "resendVerificationEmailSuccessMessage": "Email de verificación reenviado", "resendVerificationEmailErrorMessage": "Error al reenviar la verificación de correo electrónico", "resendVerificationEmailModalTitle": "Enviar verificación por email", "resendVerificationEmailModalConfirmButton": "Enviar verificación por email", "sendPasswordResetEmailSuccessMessage": "Correo de restablecimiento de contraseña enviado", "sendPasswordResetEmailErrorMessage": "No se pudo enviar el correo electrónico de restablecimiento de contraseña", "sendPasswordResetEmailModalTitle": "Enviar contraseña restablecida", "sendPasswordResetEmailModalParagraph": "Se enviará un correo electrónico para restablecer la contraseña a {email}", "sendPasswordResetEmailModalConfirmButton": "Enviar contraseña restablecida", "sendUpdateEmailSuccessMessage": "Actualizar solicitud de dirección de correo electrónico enviada", "sendUpdateEmailDuplicateErrorMessage": "Ya existe una cuenta con esta dirección de email", "sendUpdateEmailErrorMessage": "Error al enviar la solicitud de actualización de correo", "sendUpdateEmailErrorModalTitle": "Editar dirección de correo", "sendUpdateEmailErrorModalParagraph": "Se enviará un correo electrónico a la nueva dirección de correo electrónico para que el cliente confirme el cambio antes de que se actualice la cuenta.", "sendUpdateEmailErrorFormEmailLabel": "Dirección de email", "title": "Herramienta de soporte - Detalles de la cuenta", "accountActionTitle": "Acciones"}}, "Chargers": {"ChargeModeHistoryPage": {"chargeNow": "<PERSON><PERSON> ahora", "expired": "<PERSON><PERSON><PERSON><PERSON>", "heading": "Historial de modo de carga - {ppid}", "manualMode": "Modo manual", "manualStop": "Cancelado por el usuario o sustituido", "overrideType": "Tipo de sobrescribir", "requested": "Solicitado", "start": "Comenzar", "stop": "<PERSON><PERSON>", "stopReason": "Detener razón", "tableCaption": "Historial del modo de tabla de carga", "title": "Soporte Tool - Historial de modo de carga"}, "Components": {"AdminsTable": {"caption": "Tabla de administradores", "emailHeader": "E-mail", "expandLabel": "Expandir", "header": "Administradores", "noAdmins": "Este grupo al que pertenece este cargador no tiene configurados administradores.", "lastLoginHeader": "Último acceso"}, "Alerts": {"chargeScheduleBannerMessage": "Este cargador está actualmente en modo manual. Los siguientes programas no surtirán efecto.", "delegatedControlBannerMessage": "Este cargador está actualmente bajo control delegado. Los siguientes horarios son meramente indicativos y están sujetos a cambios.", "grafanaLinkMessage": " o ver este cargador en Grafana ", "externalOperatorBannerMessage": "Este cargador no está operado por Pod Point. Los clientes deben ponerse en contacto con el operador para obtener soporte. Puede buscar este cargador en MIS ", "here": "aquí", "supportedArchitectureBannerMessage": "Puede buscar este cargador en MIS ", "unSupportedArchitectureBannerMessage": "Actualmente no es posible hacer cambios o enviar comandos a este cargador. Puede buscar este cargador en MIS "}, "Badges": {"defaultFault": "Fallo - {firstOccurrence}", "faultVendorCode": "Fallo {vendorErrorCode} - {firstOccurrence}", "faultDescription": "Fault {podPointFault}: {description} - {firstOccurrence}", "faultErrorCode": "Fallo {errorCode} - {firstOccurrence}", "outOfRangeFault": "Proximidad fuera de rango", "reversePolarityFault": "Polaridad inversa, neutro alto, tierra suelta", "fuseSaverFault": "Error del protector del fusible / CLS Diferencia significativa entre la corriente del coche y de la pinza CT (no solar)", "renaultSettingFault": "Fallo de ajuste Renault (Noruega)", "arrayFault": "<PERSON><PERSON> de la matriz", "highVoltageFault": "Alto voltaje", "lowVoltageFault": "Bajo vol<PERSON>je", "pilotLowFault": "<PERSON><PERSON> bajo", "a12VFault": "Defecto de nivel A-12V", "cableOverTempStopFault": "CableOverTempStop", "overtemperatureFault": "Sobretemperatura", "overcurrentFault": "Sobrecorriente", "hardwareDamageFault": "Hardware Daños Relé soldadura", "mennekesFault": "<PERSON><PERSON>", "rCDACFault": "CA RCD", "groundFailureFault": "Fallo de tierra", "6mAFault": "Doblador de detección 6mA (RCD DC)", "unlockSocketFault": "Desbloquear fallo del conector", "upstreamPowerFault": "Corriente ascendente / Fallo de caída de tensión", "linuxUnavailableFault": "Error interno entre STM y Linux (linux no disponible)", "queueOverflowFault": "Error interno entre STM y Linux (desbordamiento de cola)", "invalidModeFault": "El vehículo está en un modo no válido para cargar (Reportado por pila IEC)", "exceededImportLimitUnder125Fault": "Límite de importación superado (entre 100-125%) durante 60 segundos", "exceededImportLimitOver125Fault": "Límite de importación excedido en un 125%", "threePhaseEarthPENFault": "Defecto PEN de 3 fases (tierra)", "threePhaseNeutralPENFault": "Defecto PEN 3-fase (neutral)", "threePhaseMissingPhaseFault": "Fase de Fase en la unidad de 3-fase", "linkyConnectionFault": "Conexión perdida de enlace", "linkyDaughterBoardFault": " Falta o defectuoso tablero ligero", "voltagePolarityFault": "Intercambiado en vivo y neutral", "6mASelfTestFault": "Defecto de autoprueba RCD", "lockNotDetectedFault": "Bloqueo no detectado", "midFault": "Medidor MID dañado o ausente", "rfidNotDetectedFault": "RFID no detectado", "rfidErrorFault": "RFID presente pero defectuoso", "veryHighVoltageFault": "Tensión muy alta, >270V, probablemente fallo PEN", "firmwareAvailable": "Actualización de firmware disponible", "firmwareUpdateNotRequested": "Actualización de firmware no solicitada", "firmwareUpdateNotAccepted": "Actualización de firmware no aceptada", "firmwareUpdateInProgress": "Actualización del firmware en progreso", "firmwareUpdateFailed": "Error al actualizar el firmware"}, "Cards": {"warrantyDetailsTitle": "Detalles de garantía", "warrantyDetailsTableTitle": "Tabla de detalles de garantía", "warrantyDetailsTableStartDateKey": "Fecha de inicio de garantía", "warrantyDetailsTableEndDateKey": "Fecha de fin de garantía", "warrantyDetailsTableWarrantyStatusKey": "Estado de garantía", "warrantyDetailsTableWarrantyStatusActive": "Activo", "warrantyDetailsTableWarrantyStatusInactive": "Inactivo", "chargerAccessPointTitle": "Punto de acceso de cargador (AP)", "chargerAccessPointHidePassword": "Ocultar contraseña", "chargerAccessPointShowPassword": "Mostrar contraseña", "chargerAccessPointTableTitle": "Punto de acceso del cargador", "chargerAccessPointTableSSIDKey": "SSID", "chargerAccessPointTablePasswordKey": "Contraseña", "chargerConnectivityQuality0": "0", "chargerConnectivityQuality1": "1 (<PERSON><PERSON>, RSSI {signalStrength})", "chargerConnectivityQuality2": "2 (Regular, RSSI {signalStrength})", "chargerConnectivityQuality3": "3 (<PERSON><PERSON><PERSON>, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality4": "4 (<PERSON><PERSON>, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality5": "5 (<PERSON><PERSON><PERSON>, RSSI {signalStrength})", "chargerConnectivityTableChargingStatusKey": "Estado de carga", "chargerConnectivityTableConnectionQuality": "Calidad de la conexión", "chargerConnectivityTableConnectionQualityScale": "(escala 1-5)", "chargerConnectivityTableLastMessageKey": "<PERSON>lt<PERSON>", "chargerConnectivityTableLastSeenKey": "Última conexión iniciada", "chargerConnectivityTableMacAddress": "Dirección MAC de ruta", "chargerConnectivityTableModel": "<PERSON><PERSON> de ruta", "chargerConnectivityTableSerialNumber": "Número de serie de ruta", "chargerConnectivityTableSimNumber": "Número de SIM de ruta", "chargerConnectivityTableStatusKey": "Estado", "chargerConnectivityTableTitle": "Tabla de conectividad del cargador", "chargerConnectivityTitle": "Conectividad de carga", "chargerConnectivityWifiStatusUnknown": "desconocido", "chargerInformationTitle": "Información del cargador", "chargerInformationViewLogsLink": "Ver registros de diagnóstico", "chargerInformationHistoryLink": "Historial", "chargerInformationTableTitle": "Tabla de información del cargador", "chargerInformationTableSKUKey": "Modelo SKU", "chargerInformationTableArchitectureKey": "Versión Arquitectura", "chargerInformationTableFirmwareKey": "Firmware", "chargerInformationTableManufacturedKey": "Fabricado", "chargerInformationTableOperatorKey": "Operador", "chargerInformationTableOperatorEditLabel": "E<PERSON>cer operador", "chargerInformationTableMACKey": "Dirección MAC", "chargerInformationTablePCBAKey": "Número de serie PCBA", "installationDetailsTitle": "Detalles de instalación", "installationDetailsViewDetailsLink": "Ver detalles de instalación", "installationDetailsTableTitle": "Tabla de detalles de instalación", "installationDetailsTableInstallDateKey": "Fecha de instalación", "installationDetailsTableInstalledByKey": "Instalado por", "installationDetailsTableCompanyKey": "Empresa", "lastCompleteChargeTitle": "Última carga completa", "lastCompleteChargeRecentCharges": "Cargas recientes", "lastCompleteChargeTableTitle": "Última tabla de cargas completas", "lastCompleteChargeTableStartedAtKey": "Comenzado el", "lastCompleteChargeTableEndedAtKey": "Terminado el", "lastCompleteChargeTableKWhKey": "<PERSON><PERSON> en<PERSON><PERSON>", "setOperatorTooltip": "E<PERSON>cer operador", "tariffAllDay": "Todo el día", "tariffsDayOfWeek1": "<PERSON><PERSON>", "tariffsDayOfWeek2": "<PERSON><PERSON>", "tariffsDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tariffsDayOfWeek4": "<PERSON><PERSON>", "tariffsDayOfWeek5": "Viernes", "tariffsDayOfWeek6": "Sábado", "tariffsDayOfWeek7": "Domingo", "tariffEnergyProvider": "Proveedor de energía", "tariffEffectiveFrom": "Efectivo de", "tariffsTitle": "Tariffs", "tariffRate": "<PERSON><PERSON>", "tariffRateOffPeak": "Tasa sin pico", "tariffRatePeak": "Velocidad m<PERSON>xi<PERSON>", "vehicleBatteryCapacity": "Capacidad de la batería", "vehicleBatteryPercentage": "<PERSON><PERSON> de bater<PERSON> actual", "vehicleChargeLimit": "Límite de carga", "vehicleEnodeConnected": "eNode conectado", "vehicleMakeAndModel": "Crear y modelar", "vehiclePluggedIn": "Conectado", "vehicleTableCaption": "Carga por horas", "vehicleTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicleViewAllVehicles": "<PERSON><PERSON> <PERSON>", "yes": "Sí", "no": "No"}, "ChargeModes": {"3rdPartyOperator": "Terceros", "active": "Activo", "candidate": "Candi<PERSON><PERSON>", "chargeModeHeading": "Modos", "smartModeLabel": "Modo inteligente", "smartModeSwitchToSmart": "<PERSON><PERSON> a<PERSON>o a inteligente.", "smartModeConfirmActivateModalTitle": "Activar modo inteligente", "smartModeConfirmDeactivateModalTitle": "Desactivar modo inteligente", "smartModeConfirmActivateModalMessage": "¿Estás seguro de que quieres activar el modo inteligente?", "smartModeConfirmDeactivateModalMessage": "¿Estás seguro de que quieres desactivar el modo inteligente?", "smartModeModalConfirmButton": "Confirmar", "smartModeModalCancelButton": "<PERSON><PERSON><PERSON>", "smartModeDelegatedControlModalTitle": "Control delegado", "smartModeDelegatedControlModalContent": "El modo inteligente no se puede cambiar mientras el cargador está bajo control delegado", "smartModeDelegatedControlModalConfirmButton": "Ok", "smartModeToastError": "Error al establecer el modo inteligente", "chargeNowLabel": "<PERSON><PERSON> ahora", "chargeNowModalCancelButton": "<PERSON><PERSON><PERSON>", "chargeNowModalDurationErrorMessage": "{errorMessage}", "chargeNowModalHeading": "Establecer la duración de carga ahora", "chargeNowModalHoursLabel": "<PERSON><PERSON>", "chargeNowModalMinutesLabel": "<PERSON><PERSON><PERSON>", "chargeNowModalSaveChangesButton": "Guardar cambios", "chargeNowSwitchToActive": "Carga ahora activa.", "chargeNowSwitchToInactive": "Carga ahora desactivada.", "chargeNowToastError": "Error al configurar la carga ahora", "delegatedControlLabel": "Control delegado", "delegatedControlModalConfirmButton": "Confirmar", "delegatedControlModalCancelButton": "<PERSON><PERSON><PERSON>", "delegatedControlModalContent": "¿Está seguro de que desea eliminar el cargador {ppid} del control delegado?", "delegatedControlModalTitle": "Eliminar del control delegado", "delegatedControlToastSuccess": "El cargador ya no está bajo control delegado", "delegatedControlToastError": "Error al eliminar del control delegado", "flexLabel": "Flex", "flexValueEnrolled": "Inscrito", "flexValueUnenrolled": "Baja", "inactive": "Inactivo", "offModeLabel": "<PERSON><PERSON>", "offModeModalActivateHeading": "Activar modo apagado", "offModeModalDeactivateHeading": "Desactivar modo apagado", "offModeModalActivateContent": "¿Estás seguro de que quieres activar el modo?", "offModeModalDeactivateContent": "¿Estás seguro de que quieres desactivar el modo?", "offModeModalConfirmButton": "Confirmar", "offModeModalCancelButton": "<PERSON><PERSON><PERSON>", "offModeToastActivatedSuccess": "El modo apagado ha sido activado", "offModeToastDeactivatedSuccess": "El modo apagado ha sido desactivado", "offModeToastError": "Error al desactivar el modo", "pending": "Pendiente", "since": "desde", "smartModeSwitchToManual": "Modo establecido en manual.", "unknown": "Desconocido"}, "ChargeSchedules": {"chargeSchedulesHeader": "<PERSON><PERSON><PERSON>", "addSchedulesLink": "<PERSON><PERSON><PERSON>", "editSchedulesLink": "Editar programas", "viewSchedulesLink": "Ver programas", "noSchedulesFoundText": "Este cargador puede ser utilizado en todo momento, no hay ningún programa de cargas."}, "ChargeSchedulesTable": {"startHeading": "Comenzar", "caption": "Tabla de tarifas y modos", "durationHeading": "Duración", "activeHeading": "Activo", "activeScheduleYes": "Sí", "activeScheduleNo": "No", "scheduleDayOfWeek1": "<PERSON><PERSON>", "scheduleDayOfWeek2": "<PERSON><PERSON>", "scheduleDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduleDayOfWeek4": "<PERSON><PERSON>", "scheduleDayOfWeek5": "Viernes", "scheduleDayOfWeek6": "Sábado", "scheduleDayOfWeek7": "Domingo"}, "ChargeSchedulesChart": {"scheduleDayOfWeek1": "<PERSON><PERSON>", "scheduleDayOfWeek2": "<PERSON><PERSON>", "scheduleDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduleDayOfWeek4": "<PERSON><PERSON>", "scheduleDayOfWeek5": "Viernes", "scheduleDayOfWeek6": "Sábado", "scheduleDayOfWeek7": "Domingo"}, "ChargerConfiguration": {"configurationHeading": "Configuración del cargador", "tableCaption": "Tabla de configuración del cargador", "editButtonAriaLabel": "Editar configuración del cargador", "powerBalancingEnabledLabel": "Equilibrado de energía activado", "powerBalancingEnabledValueYes": "Sí", "powerBalancingEnabledValueNo": "No", "powerBalancingSensorLabel": "Sensor de equilibrio de energía", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Limpiar CT", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ninguna", "ctMaxAmpsLabel": "Suministro máximo en la pinza CT (amperios)", "chargerRatingAmpsLabel": "Potencia del cargador (amperios)", "lastRetrievedLabel": "Última configuración"}, "ChargerHeader": {"copyButtonAriaLabel": "<PERSON><PERSON><PERSON> {heading}", "copySuccessMessage": "Copiado con éxito \"{heading}\" al portapapeles"}, "ChargerIcons": {"locationIconTextCommercial": "Comercial", "locationIconTitleCommercial": "Ubicación comercial", "locationIconTextDomestic": "<PERSON><PERSON><PERSON><PERSON>", "locationIconTitleDomestic": "Ubicación doméstica", "isPublicIconText": "Público:", "isPublicIconValueYes": "Sí", "isPublicIconValueNo": "No", "confirmIconText": "Confirmar:", "confirmIconValueYes": "Sí", "confirmIconValueNo": "No", "hasMidmeterEnabledIconText": "MID meter:", "hasMidmeterEnabledIconValueYes": "Sí", "hasMidmeterEnabledIconValueNo": "No", "hasTariffIconText": "<PERSON><PERSON><PERSON> as<PERSON>:", "hasTariffIconValueYes": "Sí", "hasTariffIconValueNo": "No", "hasRfidReaderIconText": "Lector RFID:", "hasRfidReaderIconValueYes": "Sí", "hasRfidReaderIconValueNo": "No"}, "ChargersTable": {"caption": "Tabla de otros cargadores ubicados en el mismo sitio", "expandLabel": "Expandir", "header": "Cargadores (sitio)", "modelHeader": "<PERSON><PERSON>", "nameHeader": "Nombre", "noChargers": "Este cargador no tiene otros cargadores ubicados en el mismo sitio.", "ppidHeader": "PPID"}, "ChargingIntentsTable": {"caption": "Tabla de cargo por horas", "noChargingIntents": "Este cargador no tiene intenciones de carga.", "shortFormDayOfWeek1": "<PERSON><PERSON>", "shortFormDayOfWeek2": "<PERSON><PERSON>", "shortFormDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFormDayOfWeek4": "<PERSON><PERSON>", "shortFormDayOfWeek5": "Viernes", "shortFormDayOfWeek6": "Sábado", "shortFormDayOfWeek7": "Domingo", "vehicleChargeBy": "Cargo por", "batteryToAdd": "<PERSON><PERSON><PERSON>", "enodeConnectedWarning": "* Los vehículos conectados a un nodo cargan al límite de carga, y sólo retroceden al programa de abajo si no pueden conectarse."}, "DomainsTable": {"activatedOnHeader": "Fecha de activación", "caption": "Tabla de dominios", "expandLabel": "Expandir", "domainNameHeader": "Nombre de dominio", "header": "<PERSON><PERSON><PERSON>", "noDomains": "Este grupo al que pertenece este cargador no tiene dominios configurados."}, "DriversTable": {"caption": "Tabla de controladores", "emailHeader": "E-mail", "expandLabel": "Expandir", "header": "Controladores", "noDrivers": "Este grupo al que pertenece este cargador no tiene controladores configurados.", "statusHeader": "Estado", "tierHeader": "<PERSON><PERSON>"}, "LinkedUsers": {"unlinkUserAriaLabel": "Desvincular usuario {user}"}, "RfidCardsTable": {"activeHeader": "Activo", "caption": "Tabla de tarjetas RFID", "expandLabel": "Expandir", "header": "Tarjetas RFID", "noCards": "Este cargador no tiene tarjetas RFID.", "referenceHeader": "Referencia"}, "OtherConfigurationValues": {"header": "Otros valores de configuración", "issueFetching": "Hubo un problema al recuperar la configuración.", "tableCaption": "Tabla de otros valores de configuración"}, "Modals": {"cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "Enviar", "saveChangesButton": "Guardar cambios", "commandModalParagraph": "¿Estás seguro de que quieres enviar el comando {command} al cargador {ppid} (Enchufe {socket})?", "commandModal": "Enchufe {socket}", "commandModalSuccess": "Comando {command} enviado correctamente", "commandModalSoftResetCommand": "Reini<PERSON>", "commandModalHardResetCommand": "<PERSON><PERSON><PERSON> completo", "commandModalSetInServiceCommand": "Poner en servicio", "commandModalSetOutOfServiceCommand": "Puesta fuera de servicio", "commandModalUnlockConnectorCommand": "Desbloquear conector", "commandModalError": "Error al enviar el comando {command}", "requestDiagnosticLogsErrorMessage": "Error al solicitar registros de diagnóstico", "requestDiagnosticLogsParagraph": "Solicitud de 24 horas de registros de diagnóstico del cargador {ppid} {socket}", "requestDiagnosticLogsSocket": "Enchufe {socket}", "requestDiagnosticLogsStartDateLabel": "Fecha de inicio", "requestDiagnosticLogsStartTimeLabel": "Hora de inicio", "requestDiagnosticLogsTitle": "Solicitud de registros de diagnóstico", "requestFirmwareUpdateParagraph": "¿Estás seguro de que quieres enviar una solicitud de actualización de firmware al cargador {ppid} {socket}?", "requestFirmwareUpdateSocket": "Enchufe {socket}", "requestFirmwareUpdateSuccess": "Actualización de firmware solicitada con éxito", "requestFirmwareUpdateError": "Error al solicitar actualización de firmware", "requestFirmwareUpdateTitle": "Solicitar actualización de firmware", "retrieveLatestChargerConfigurationParagraph": "¿Está seguro de que desea recuperar la última configuración para el cargador {ppid}?", "retrieveLatestChargerConfigurationSuccess": "Se ha solicitado con éxito la última configuración. Puede tardar unos segundos en actualizarse. Puede que necesite actualizar su navegador.", "retrieveLatestChargerConfigurationError": "Error al solicitar la configuración más reciente", "retrieveLatestChargerConfigurationTitle": "Recuperar la última configuración del cargador", "setOperatorSuccess": "Se ha establecido correctamente el operador. Puede tardar unos segundos en actualizarse. Puede que necesite actualizar su navegador.", "setOperatorError": "Error al establecer el operador", "setOperatorLabel": "Operador", "setOperatorTitle": "E<PERSON>cer operador"}, "CommandsMenu": {"title": "<PERSON><PERSON><PERSON>", "softResetCommand": "Reini<PERSON>", "hardResetCommand": "<PERSON><PERSON><PERSON> completo", "setInServiceCommand": "Poner en servicio", "setOutOfServiceCommand": "Poner fuera de servicio", "unlockConnectorCommand": "Desbloquear conector", "requestDiagnosticLogsCommand": "Solicitar registros de diagnóstico", "requestFirmwareUpdateCommand": "Solicitar actualización de firmware", "retrieveLatestConfigurationCommand": "Recuperar la última configuración"}, "SocketSwitcher": {"socketTitle": "Enchufe {socket}", "optionAll": "Todos"}, "SolarConfiguration": {"heading": "Configuración solar", "tableCaption": "Tabla de configuración solar", "editButtonAriaLabel": "Editar configuración solar", "pvSolarSystemInstalledLabel": "Sistema solar PV instalado", "pvSolarSystemInstalledValueYes": "Sí", "pvSolarSystemInstalledValueNo": "No", "solarMatchingEnabledLabel": "Ajuste solar activado", "solarMatchingEnabledValueYes": "Sí", "solarMatchingEnabledValueNo": "No", "maxGridImportLabel": "Importación de red máxima (amperios)"}, "Tags": {"header": "Etiquetas", "noTags": "Este cargador no tiene etiquetas.", "tableCaption": "Tabla de etiquetas"}, "TopCardLayout": {"addressPrefix": "Dirección:", "backLinkLabel": "Atrás", "groupPrefix": "Grupo:", "siteContactPrefix": "Contacto del sitio:", "sitePrefix": "Sitio:"}}, "ConfigurationPage": {"backButton": "Cargadores", "chargeCurrentLimit": "Potencia del cargador (amperios)", "chargerConfiguration": "Configuración del cargador", "confirmCharge": "Confirmar carga", "heading": "Configuración - {ppid} (Socket {socket})", "linkySchedulesEnabled": "Programas de enlace habilitados", "offlineSchedulingEnabled": "Programación sin conexión habilitada", "penFaultMode": "Modo de fallo del puntal", "powerBalancingCurrentLimit": "Suministro máximo en la pinza CT (amperios)", "powerBalancingEnabled": "Equilibrado de energía activado", "powerBalancingSensor": "Sensor de equilibrio de energía", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Limpiar CT", "powerBalancingSensorInstalled": "Equilibrado de energía instalado", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ninguna", "powerBalancingSensorPlaceholder": "Seleccionar sensor de equilibrio de energía", "powerBalancingSensorPolarityInverted": "Polaridad de equilibrado de potencia (la pinza CT) invertida", "ppDevClampFaultThreshold": "Umbral de falla de brillo de la pinza CT (amperios)", "rcdBreakerSize": "Valoración RCBO del cargador EV (amperios)", "saveChangesButton": "Guardar cambios", "selectMode": "Se<PERSON>ccionar modo", "selectThreshold": "Seleccionar umbral", "socketTitle": "(Enchufe {socket})", "solarConfiguration": "Configuración solar", "solarExportMargin": "Margen de exportación solar (amperios)", "solarExportMarginTooltip": "Compensación a la exportación para determinar la energía disponible a partir de la generación solar. Esto se utiliza para asegurar que los sistemas que incorporan una batería no proporcionan energía de batería para cargar el EV. Debe ser 0 para instalaciones sin un sistema de batería instalado.", "solarMatchingEnabled": "Ajuste solar activado", "solarMatchingEnabledTooltip": "Si está activada la función de carga solar excedente, se indica como «Modo de carga solar» en la aplicación móvil.", "solarMaxGridImport": "Importación máxima de red (amperios)", "solarMaxGridImportConverted": "Importación máxima de red (kW)", "solarMaxGridImportConvertedTooltip": "Una conversión de la importación máxima de red de amplificadores a kW como se muestra en la aplicación móvil.", "solarMaxGridImportTooltip": "Máxima cantidad (en amperios) de electricidad que se extraerá de la red para complementar la generación solar y permitir que se produzca una carga. Cuando se ajusta a 6,0, el cargador cargará a 1,4 kW cuando no se genera energía solar.", "solarStartDelay": "Retraso de inicio solar (segundos)", "solarStartDelayTooltip": "La duración mínima en segundos de la corriente generada consistentemente superior requerida para aumentar la tasa de carga. Utilizado para evitar la alternancia sucesiva entre las tasas de carga.", "solarStopDelay": "Retraso de parada solar (segundos)", "solarStopDelayTooltip": "La duración mínima en segundos de la corriente generada consistentemente menor requerida para reducir la tasa de carga. Utilizado para evitar la alternancia sucesiva entre las tasas de carga.", "solarSystemInstalled": "Sistema solar PV instalado", "solarSystemInstalledTooltip": "Si la propiedad tiene un sistema PV Solar instalado (o cualquier otro sistema de generación de electricidad que pueda exportar a la red). Esto está etiquetado como \"Tengo paneles solares\" en la aplicación móvil.", "title": "Herramienta de soporte - Configuración del cargador", "toastError": "Error al actualizar la configuración", "toastSuccess": "Configuración actualizada correctamente", "unlockConnectorOnEVSideDisconnect": "¿Debe desbloquear el cable del cargador cuando se desconecta del vehículo?"}, "DetailsPage": {"backButton": "Cargadores", "cancelButton": "<PERSON><PERSON><PERSON>", "noDisplayName": "No establecido", "saveButton": "Guardar", "saveChangesButton": "Guardar cambios", "socketTitle": "(Enchufe {socket})", "title": "Herramienta de soporte - Cargador"}, "DiagnosticsLogsViewerPage": {"actions": "Acciones", "download": "<PERSON><PERSON><PERSON>", "end": "Fin", "heading": "Registros de diagnóstico - {ppid} (Socket {socket})", "openButton": "<PERSON><PERSON>r visor de registro", "socketTitle": "(Enchufe {socket})", "start": "Comenzar", "status": "Estado", "tableCaption": "Tabla de registros de diagnóstico", "title": "Visor de registro de diagnóstico", "topPageTitle": "Herramienta de soporte - Registros de diagnóstico", "viewerPageTitle": "Herramienta de soporte - Visor de registros de diagnóstico"}, "FlexDetailsPage": {"flexDeletedAt": "Omitido", "flexDetailsHeading": "Detalles de Flex - {ppid}", "flexDirection": "Dirección", "flexProgrammeEnrollmentDate": "Fecha de inscripción", "flexProgrammeStatus": "Estado del programa", "flexProgrammes": "Programas Flex", "flexProgrammesUnEnrollmentDate": "<PERSON><PERSON>", "flexRequestedAt": "Solicitado", "flexScheduledEnd": "Fin programado", "flexScheduledStart": "Inicio programado", "flexStatus": "Estado", "socketTitle": "(Enchufe {socket})", "title": "Herramienta de soporte - Detalles de Flex"}, "InstallationPage": {"heading": "Detalles de la instalación - {ppid}", "headingWithSocket": "Detalles de la instalación - {ppid} (Socket {socket})", "installationImages": "Imágenes de instalación", "installationValues": "Estos son los valores registrados en el punto en que el cargador fue instalado. Pueden diferir de los valores actuales y no pueden ser actualizados.", "installationDate": "Fecha de instalación", "installedBy": "Instalado por", "company": "Empresa", "outOfService": "Fuera de servicio", "yes": "Sí", "no": "No", "householdMaxSupply": "Suministro máximo en la pinza CT (amperios)", "breakerSize": "Valoración RCBO del cargador EV (amperios)", "powerRatingPerPhase": "Potencia del cargador (amperios)", "powerBalancingSensorInstalled": "Equilibrado de energía instalado", "powerBalancingEnabled": "Equilibrado de energía activado", "powerBalancingSensor": "Sensor de equilibrio de energía", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Limpiar CT", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ninguna", "linkySchedulesEnabled": "Programas de enlace habilitados", "powerGenerationSystemInstalled": "Sistema solar PV instalado", "socketTitle": "(Enchufe {socket})", "title": "Herramienta de soporte - Detalles de instalación"}, "PcbHistoryPage": {"all": "Todos", "datetime": "<PERSON><PERSON>/hora", "errorCode": "<PERSON><PERSON><PERSON>", "failed": "Fallo", "heading": "Historial de PCB - {ppid} (Enchufe {socket})", "inProgress": "En progreso", "serialNumber": "Número de serie", "status": "Estado", "success": "Éxito", "socketTitle": "(Enchufe {socket})", "tableCaption": "Tabla de historial de pcb", "title": "Herramienta de soporte - historial de PCB"}, "RecentChargesPage": {"heading": "Cargas completados recientes - {ppid}", "socketHeading": " (Enchufe {socket})", "downloadChargesCsvButton": "Descargar CSV", "pluggedInAtHeader": "Conectado", "startedAtHeader": "Comenzado el", "endedAtHeader": "Terminado el", "unpluggedAtHeader": "Desconectado", "energyTotalHeader": "Energía suministrada (kWh)", "gridEnergyTotalHeader": "Energía de red (kWh)", "generationEnergyTotalHeader": "Energía solar (kWh)", "energyCostHeader": "Coste de energía", "revenueGeneratedHeader": "Ingresos", "chargeDurationTotalHeader": "Duración de la carga", "totalDurationHeader": "Duración total", "doorHeader": "<PERSON><PERSON><PERSON>", "confirmedHeader": "<PERSON><PERSON>rma<PERSON>", "actionsHeader": "Acciones", "actionsEditEnergyTooltip": "Editar coste de energía", "actionsEditEnergyAriaLabel": "Editar coste de energía {chargeId}", "actionsEditRevenueCostTooltip": "Editar coste de ingresos", "actionsEditRevenueCostAriaLabel": "Editar coste de ingresos {chargeId}", "tableCaption": "Tabla de cargas recientes completadas", "tableFilterPlaceholder": "Todos", "noDataFound": "No se han encontrado cargas recientes", "filtersLabel": "Filtros: ", "clearFilters": "<PERSON><PERSON><PERSON>", "pluggedInFilterYear": "Últimos 12 meses", "pluggedInFilterWeek": "Últimos 7 días", "pluggedInFilter30days": "Últimos 30 días", "pluggedInFilter90days": "Últimos 90 días", "pluggedInFilterMonth": "<PERSON>ste mes", "pluggedInFilterPeviousMonth": "Mes anterior", "socketTitle": "(Enchufe {socket})", "title": "Herramienta de soporte - Cargas recientes", "editRevenueCostTitle": "<PERSON><PERSON> ing<PERSON>", "editRevenueCostDescription": "Recomendar los ingresos por este cargo, esta es la cantidad que el conductor paga al sitio por el cargo. El saldo del monedero del conductor no será modificado como parte de este cambio.", "editRevenueCostEnergyTotalText": "Energía entregada", "editRevenueCostInputUpdatedCost": "Ingresos actualizados (£)", "editRevenueCostInputCost": "Precio por kWh (en penca)", "editRevenueCostInputCostPlaceholder": "Introduzca pence por kWh", "editEnergyCostTitle": "<PERSON><PERSON> coste", "editEnergyCostDescription": "Actualice el coste de energía de esta carga.", "editEnergyCostEnergyTotalText": "Energía suministrada (kWh)", "editGridEnergyCostEnergyTotalText": "Energía de red (kWh)", "editEnergyCostInputUpdatedCost": "Coste de energía actualizado (£)", "editEnergyCostInputCost": "Pence per kWh", "editEnergyCostInputCostPlaceholder": "Introduzca pence por kWh", "updateCostFormSaveButton": "Guardar cambios", "updateCostFormCancelButton": "<PERSON><PERSON><PERSON>", "chargeAlreadyExpensedError": "Este cargo ya ha sido costado y no puede ser actualizado", "commonInternalServerError": "Algo salió mal. <PERSON><PERSON> favor, inténtalo de nuevo"}, "SchedulesEditPage": {"active": "Activo", "activeScheduleYes": "Sí", "activeScheduleNo": "No", "chargeScheduleDurationError": "La duración mínima del horario es de 15 minutos", "chargeScheduleOverlapError": "Uno o más programas se superponen. Asegúrese de que haya al menos 15 minutos entre cualquier programa", "day": "Día", "duration": "Duración", "errorToast": "Error al actualizar la programación", "heading": "Editar programas - {ppid}", "headingReadOnly": "Ver programas - {ppid}", "hours": "horas", "minutes": "minutos", "saveChangesButton": "Guardar cambios", "start": "Comenzar", "successToast": "Horario actualizado correctamente", "title": "Herramienta de soporte - Editar programas de carga", "titleReadOnly": "Herramienta de soporte - Ver programas de carga", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "Viernes", "Saturday": "Sábado", "Sunday": "Domingo"}, "SearchPage": {"heading": "Cargadores", "searchCardHeading": "Navegar al cargador:", "searchFormPpidLabel": "PPID o Nombre", "searchFormSubmitButton": "<PERSON>r", "title": "Herramienta de soporte - Cargadores"}, "VehiclesPage": {"batteryCapacity": "Capacidad de la batería", "vehicleBatteryPercentage": "<PERSON><PERSON> de bater<PERSON> actual", "chargeLimit": "Límite de carga", "enodeConnected": "eNode conectado", "heading": "Veh<PERSON><PERSON>los - {ppid}", "lastSeen": "Vehículo visto por última vez en", "lastUpdated": "Estado de carga actualizado por última vez el", "no": "No", "powerDeliveryState": "Estado de entrega de energía", "title": "Herramienta de soporte - Vehículo", "yes": "Sí"}}, "Shared": {"UnlinkChargerModal": {"successToast": "Cargador desvinculado correctamente de la cuenta", "errorToast": "Error al desvincular el cargador", "chargerPageConfirmMessage": "¿Estás seguro de que quieres desvincular esta cuenta de este cargador?", "confirmMessage": "¿Estás seguro de que quieres desvincular este cargador de esta cuenta?", "title": "Desvincular cargador", "confirmMessageParagraph": "El titular de la cuenta ya no podrá ver o administrar el cargador en la aplicación del conductor. Tendrán acceso a cargar datos desde antes de que el cargador fuera desconectado.", "confirmButtonText": "Desvincular cargador", "cancelButtonText": "<PERSON><PERSON><PERSON>"}}, "Subscriptions": {"SearchPage": {"heading": "Suscripciones", "searchCardHeading": "Buscar suscripciones:", "searchResultsOrderedAt": "Ordenado", "searchResultsLink": "Abrir", "searchResultsStatus": "Estado", "searchCardInputLabel": "Email o PPID", "searchCardInputPlaceholder": "<EMAIL>", "searchCardSubmitButton": "<PERSON>r", "noSearchResults": "No se encontraron suscripciones", "title": "Herramienta de soporte - Suscripciones"}, "StatusPage": {"title": "Herramienta de soporte - Estado de suscripción", "heading": "Estado del Pod Drive", "noPpidAllocated": "ID de cargador: N/A"}, "SubscriptionActions": {"payUpfrontFee": "<PERSON><PERSON> por adelantado", "completeHomeSurvey": "Encuesta completada", "checkAffordability": "Afordabilidad pasada", "setupDirectDebit": "Configuración de débito directo", "signDocuments": "Documentos legales firmados", "chargingStationInstalled": "Estación de carga instalada"}, "StatusBadges": {"subscriptionStatusActive": "Activo", "subscriptionStatusCancelled": "Cancelado", "subscriptionStatusPending": "Pendiente", "subscriptionStatusSuspended": "Suspendido", "subscriptionStatusRejected": "<PERSON><PERSON><PERSON><PERSON>", "subscriptionStatusEnded": "Terminado"}}, "WorkInProgress": {"bannerParagraph": "Esta página todavía está siendo trabajada. Algunas características pueden no funcionar o no existen todavía."}, "Errors": {"ctClampThresholdMinValue": "El umbral de falla de la pinza CT debe ser mayor o igual a 1", "ctClampThresholdMaxValue": "El umbral de falla de la pinza CT debe ser menor o igual a 5", "powerBalancingMaxValue": "La calificación de brillo de equilibrio de poder debe ser menor o igual a 100", "powerBalancingMinValue": "La calificación de brillo de equilibrio de poder debe ser mayor o igual a 0", "powerBalancingSensorValue": "Debe ser uno de los siguientes: <PERSON><PERSON><PERSON>, <PERSON> clamp, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "breakerSizeMinValue": "La calificación RCBO del cargador EV debe ser mayor que 0", "maxCurrentRatingMinValue": "La calificación actual máxima debe ser mayor que 0", "solarMinGridImportValue": "La importación de cuadrícula (amperios) debe ser mayor o igual a 0.0", "solarMaxGridImportValue": "La importación de cuadrícula (amperios) debe ser menor o igual a 6.0", "solarStartHysteresisMinValue": "La histéresis de inicio solar debe ser mayor que 0", "solarStopHysteresisMinValue": "La histéresis de parada solar debe ser mayor que 0", "solarExportMarginMinValue": "El margen de exportación solar (amperios) debe ser mayor o igual a 0.0", "solarExportMarginMaxValue": "El margen de exportación solar (amperios) debe ser menor o igual a 6.0", "penFaultModeValue": "El modo PEN debe ser de alto rango o bajo rango", "currencyPenceError": "'La entrada debe ser un valor de moneda válido en pence'", "priceMinValueError": "El precio debe ser mayor que £{price}", "priceMaxValueError": "El precio debe ser inferior a £{price}", "priceDecimalPlacesError": "El precio debe estar dentro de tres decimales", "tagValueWhitespace": "El valor de la etiqueta no debe tener espacios", "tagValueLowercase": "El valor de la etiqueta debe ser minúsculas", "tagInvalidKey": "La clave de etiqueta no es válida", "tagInvalidValue": "El valor de etiqueta para esta clave no es válido"}}