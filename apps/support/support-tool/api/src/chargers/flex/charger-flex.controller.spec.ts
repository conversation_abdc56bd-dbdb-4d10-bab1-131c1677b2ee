import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import { ChargerFlexController } from './charger-flex.controller';
import { ChargerFlexService } from './charger-flex.service';
import { INestApplication } from '@nestjs/common';

import {
  ChargingStationProgramme,
  ProgrammeResponse,
} from '@experience/shared/axios/competitions-service-client';
import { FlexRequestResponse } from '@experience/shared/axios/smart-charging-service-client';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import {
  mockFlexibilityRequests,
  mockGetProgrammesData,
} from '@experience/support/support-tool/shared/specs';
import request from 'supertest';

jest.mock('./charger-flex.service');

describe('ChargerFlexController', () => {
  let app: INestApplication;
  let controller: ChargerFlexController;
  let service: ChargerFlexService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerFlexController],
      providers: [ChargerFlexService],
    }).compile();

    controller = module.get<ChargerFlexController>(ChargerFlexController);
    service = module.get<ChargerFlexService>(ChargerFlexService);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get programmes for a ppid controller', async () => {
    const mockGetProgrammes = jest
      .spyOn(service, 'getProgrammes')
      .mockResolvedValueOnce(mockGetProgrammesData());

    await request(app.getHttpServer())
      .get('/chargers/PSL-12345/flex/programmes')
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(
        convertAllDatesToISOString(
          mockGetProgrammesData
        ) as (ChargingStationProgramme & ProgrammeResponse)[]
      );

    expect(mockGetProgrammes).toHaveBeenCalledWith('PSL-12345', TEST_OIDC_USER);
  });

  it('should get events for a ppid', async () => {
    const mockGetEvents = jest
      .spyOn(service, 'getFlexibilityRequests')
      .mockResolvedValueOnce(mockFlexibilityRequests);

    await request(app.getHttpServer())
      .get('/chargers/PSL-12345/flex/events')
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(
        convertAllDatesToISOString(
          mockFlexibilityRequests
        ) as FlexRequestResponse[]
      );

    expect(mockGetEvents).toHaveBeenCalledWith('PSL-12345', TEST_OIDC_USER);
  });
});
