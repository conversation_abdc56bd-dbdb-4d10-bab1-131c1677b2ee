# 🚀 Experience Monorepo

Welcome to the **Experience Monorepo**! This repository serves multiple development squads working on various customer facing Pod Point systems.

## 🏗 Squads

This monorepo supports the following squads:

- **Commercial**
- **Data Platform**
- **Mobile**
- **Assets**
- **Grid**

## 🖥️ Applications

This monorepo supports the following applications, services and libraries:

- **The Site Management Service**
- **The Statement Service**
- **The OCPI Service**
- **The Support Tool**
- **The Charge Data Platform**
- **Various mobile application backend services**
- **The React Design System**
- **The Onboarding Service**
- **The Loyalty Card Service**

## ⚙️ Tech Stack

- **Monorepo Management:** [Nx](https://nx.dev/) 🏗
- **Frontend:** [Next.js](https://nextjs.org/) ⚡
- **Backend Services:** [NestJS](https://nestjs.com/) & [Go](https://go.dev/) 🚀
- **CI/CD:** GitHub Actions 🛠
- **Cloud Deployment:** AWS ☁️
- **Dependency Management:** [Renovate](https://github.com/renovatebot/renovate) 🤖
- **Localisation Management:** [Crowdin](https://crowdin.com/) 🇫🇷 🇪🇸 🇳🇴
- **Developer Portal:** [Backstage](https://backstage.io/) 📖

## 🚀 Getting Started

### 📦 Prerequisites

Ensure you have the following installed:

- [Node.js](https://nodejs.org/) (see [.nvmrc](.nvmrc) for version)
- [npm](https://www.npmjs.com/)

The Ansible Playbooks [here](https://github.com/Pod-Point/ansible-macos-setup) will set up most of the other things you will need. It is not actively maintained and may be missing some dependencies.

Alternatively run `brew install awscli go golangci-lint pkg-config cairo pango libpng jpeg giflib librsvg pixman`.

### 🛠 Installation

Export an environment variable named `STRIPE_GITHUB_TOKEN` containing the value from `arn:aws:secretsmanager:eu-west-1:146549662676:secret:billing-api-ao223y:stripe_github_token::`.
This is required in order to install the `@stripe/stripe` package. This step can be removed once Stripe move this library out of private beta.

Clone the repository and install dependencies:

```sh
<NAME_EMAIL>:Pod-Point/experience.git
cd experience
npm install
```

Optionally install pre-commit hooks using Husky:

```sh
npx husky init
```

### ▶ Running the Applications

To run a specific application:

```sh
npx nx serve [app-name]
```

For example, to start the Support Tool Next.js webapp:

```sh
npx nx serve support-tool-webapp
```

To start the Support Tool NestJS API:

```sh
npx nx serve support-tool-api
```

_The majority of the applications require that you copy their .env.local.sample file to .env.local and fill in any missing configurations._

### 🧪 Running Tests

Run unit tests:

```sh
npx nx test [app-name]
```

Run e2e tests:

```sh
npx nx e2e [e2e-project-name]
```

## 🚀 CI/CD & Deployment

This repository uses **GitHub Actions** for CI/CD, automating testing and deployment workflows. Applications are deployed to AWS environments.

[![Pipeline](https://github.com/Pod-Point/destination/actions/workflows/pipeline.yml/badge.svg)](https://github.com/Pod-Point/destination/actions/workflows/pipeline.yml)
[![Pull Request](https://github.com/Pod-Point/destination/actions/workflows/pull-request.yml/badge.svg)](https://github.com/Pod-Point/destination/actions/workflows/pull-request.yml)
[![Release](https://github.com/Pod-Point/destination/actions/workflows/release.yml/badge.svg)](https://github.com/Pod-Point/destination/actions/workflows/release.yml)

Further documentation can be found [here](docs/domain/docs/guides/releases).

## 🔄 Dependency Management

We use [Renovate](https://github.com/renovatebot/renovate) to keep dependencies up-to-date automatically. View the [Dependency Dashboard](https://github.com/Pod-Point/experience/issues/6574) to see updates.

## 🏗 Project Structure

```
experience/
│── .github/workflows/  # CI/CD configurations
│── apps/               # Application-specific code (Next.js, NestJS)
│── docs/               # Team-specific spaces for documentation
│── libs/               # Shared libraries and utilities
│── tools/              # Custom scripts and utilities
│── nx.json             # Nx configuration
│── package.json        # Dependency management
│── README.md           # You're here! 📖
```

## 🔧 Troubleshooting

Try the following first if you encounter issues:

> Expect this to take approximately 1 minute to run, perhaps longer if you have a poor internet connection.

```sh
npm run fix-repo
```

_If the above npm script command does not work, you might be having npm issues, try invoking the script directly_

```sh
./init/fix-repo.sh
```

This script will fix common issues with the monorepo setup.

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Branching:** Use feature branches (`feature/[issue]-description`)
2. **Commits:** Follow [Conventional Commits](https://www.conventionalcommits.org/)
3. **Pull Requests:** Open PRs against `main` with clear descriptions
4. **Code Style:** Ensure all code passes linting and tests before submitting

---

Happy coding! 🚀✨
