version: '2'
run:
  issues-exit-code: 1
linters:
  default: none
  enable:
    - bodyclose
    - dogsled
    - errcheck
    - exhaustive
    - goconst
    - gocritic
    - gocyclo
    - gosec
    - govet
    - ineffassign
    - nakedret
    - nolintlint
    - prealloc
    - predeclared
    - revive
    - staticcheck
    - thelper
    - tparallel
    - unconvert
    - unparam
    - whitespace
  settings:
    errcheck:
      check-type-assertions: true
    goconst:
      min-len: 2
      min-occurrences: 3
    gocritic:
      enabled-tags:
        - diagnostic
        - performance
        - style
    govet:
      enable:
        - shadow
    nolintlint:
      require-explanation: true
      require-specific: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - errcheck
        path: .*_test\.go
      - linters:
          - gosec
        path: libs/data-platform/tasks
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/test/seeding/podadmin_helper.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/event-sourcing/projection/xdp_internal/charges/random/charge.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/test/seeding/projections.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/event-sourcing/domain/events/charges/service_integration_test.go
        text: G115
      - linters:
          - gosec
        path: apps/data-platform/api/pkg/infra/podadminpayments/repository_test.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/test/fixtures/podadmin.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/test/seeding/chargers.go
        text: G115
      - linters:
          - gosec
        path: apps/data-platform/api-e2e/pkg/random/drivers.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/event-sourcing/commands/pkg/infra/podadmin/repository_test.go
        text: G115
      - linters:
          - gosec
        path: libs/shared/go/event-store/subscriptions_repository_test.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/event-sourcing/projection/drivers/random/charge.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/test/seeding/podadmin_charges.go
        text: G115
      - linters:
          - gosec
        path: apps/data-platform/api-e2e/pkg/random/charge_completed_event.go
        text: G115
      - linters:
          - gosec
        path: libs/data-platform/event-sourcing/projection/xdp_internal/charges/random/util.go
        text: G115
      - linters:
          - staticcheck
        path: libs/shared/go/aws/test/containers.go
        text: SA1019
      - linters:
          - staticcheck
        path: libs/data-platform/api/contract/design/*
        text: ST1001
      - linters:
          - revive
        path: libs/data-platform/api/contract/design/*
        text: dot-imports
    paths:
      - experience/libs/data-platform/api/contract/gen
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - goimports
  exclusions:
    generated: lax
    paths:
      - experience/libs/data-platform/api/contract/gen
      - third_party$
      - builtin$
      - examples$
