name: API Client Generation

on:
  workflow_dispatch:

jobs:
  maintain:
    name: API Client Generation
    runs-on: ubuntu-24.04
    steps:
      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ steps.fetch-auth-token.outputs.token }}

      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}

      - name: Get actions user id
        id: get-actions-user-id
        run: |
          actions_user_id=`id -u $USER`
          echo "actions_user_id=$actions_user_id" >> $GITHUB_OUTPUT

      - name: Regenerate the API clients
        run: |
          npx nx run-many --targets regenerate-client
          npx nx affected --targets generate-mocks
        env:
          GH_TOKEN: ${{ steps.fetch-auth-token.outputs.token }}

      # @see https://github.com/actions/runner/issues/1282
      - name: Reset file permissions
        run: |
          find . ! -user ${{ steps.get-actions-user-id.outputs.actions_user_id }} -exec sudo chown -cvf "${{ steps.get-actions-user-id.outputs.actions_user_id }}" "{}" \;

      - name: Format the changed files
        run: |
          npx nx format:write --uncommitted

      - name: Submit
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          body: '🔧 This pull request regenerates api clients based on latest openapi yaml files.'
          branch: 'regenerate-api-clients'
          commit-message: 'chore(shared): regenerate api clients'
          title: 'chore(shared): regenerate api clients'
          token: ${{ steps.fetch-auth-token.outputs.token }}
