name: Package
run-name: Package ${{ inputs.projects }} version (${{ github.sha }})

on:
  workflow_dispatch:
    inputs:
      projects:
        description: 'Project(s)'
        required: true
        type: string

permissions:
  actions: read
  contents: read
  id-token: write

jobs:
  prepare:
    name: 🧭 Prepare
    runs-on: ubuntu-24.04
    outputs:
      matrix: ${{ steps.prepare-matrix.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Prepare matrix
        id: prepare-matrix
        run: |
          echo "matrix=$(npx nx show projects --projects ${{ inputs.projects }},tag:${{ inputs.projects }} --exclude '!tag:package' --json | jq -r tostring)" >> $GITHUB_OUTPUT

  package:
    name: 🏗️ Package
    runs-on: ubuntu-24.04
    needs: [prepare]
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: package-${{ matrix.service }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Package
        uses: ./.github/actions/package
        with:
          sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
          service: ${{ matrix.service }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
          version: ${{ github.sha }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.prepare.outputs.matrix) }}
    timeout-minutes: 10
