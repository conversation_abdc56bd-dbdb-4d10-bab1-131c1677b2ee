name: Pull Request

# Keep paths in sync with paths on pull-request-ignored.yml to ensure required status check runs.
# https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/managing-protected-branches/troubleshooting-required-status-checks#handling-skipped-but-required-checks
on:
  merge_group:
    paths-ignore:
      - '**/.env.local.sample'
      - '**/.gitignore'
      - '**/README.md'
      - '**/backstage.yaml'
      - '.aws/**'
      - '.codebuild'
      - '.editorconfig'
      - '.github/CODEOWNERS'
      - '.github/actions/deploy/*'
      - '.github/actions/e2e-test/*'
      - '.github/actions/package/*'
      - '.github/actions/post-deploy/*'
      - '.github/actions/cdn-upload/*'
      - '.github/pull_request_template.md'
      - '.github/workflows/analyse.yml'
      - '.github/workflows/api-client-generation.yml'
      - '.github/workflows/deploy.yml'
      - '.github/workflows/e2e-test.yml'
      - '.github/workflows/lock-file-maintenance.yml'
      - '.github/workflows/nx-migrate.yml'
      - '.github/workflows/nx-run.yml'
      - '.github/workflows/package-and-deploy.yml'
      - '.github/workflows/package.yml'
      - '.github/workflows/pipeline.yml'
      - '.github/workflows/pull-request-closed.yml'
      - '.github/workflows/pull-request-commit-lint.yml'
      - '.github/workflows/pull-request-format-check.yml'
      - '.github/workflows/pull-request-package.yml'
      - '.github/workflows/pull-request-security-checks.yml'
      - '.github/workflows/release.yml'
      - '.github/workflows/renovate.yml'
      - '.github/workflows/repo-visualiser.yml'
      - '.github/workflows/translate.yml'
      - '.github/workflows/upload-vehicles.yml'
      - '.github/workflows/zap.yml'
      - '.gitignore'
      - '.husky'
      - '.run/**'
      - 'README.md'
      - 'catalog-info.yaml'
      - 'commitlint.config.js'
      - 'crowdin.update.sh'
      - 'docs/**'
      - 'init/**'
      - 'renovate.json'
  pull_request:
    paths-ignore:
      - '**/.env.local.sample'
      - '**/.gitignore'
      - '**/README.md'
      - '**/backstage.yaml'
      - '.aws/**'
      - '.codebuild'
      - '.editorconfig'
      - '.github/CODEOWNERS'
      - '.github/actions/deploy/*'
      - '.github/actions/e2e-test/*'
      - '.github/actions/package/*'
      - '.github/actions/post-deploy/*'
      - '.github/actions/cdn-upload/*'
      - '.github/pull_request_template.md'
      - '.github/workflows/analyse.yml'
      - '.github/workflows/api-client-generation.yml'
      - '.github/workflows/deploy.yml'
      - '.github/workflows/e2e-test.yml'
      - '.github/workflows/lock-file-maintenance.yml'
      - '.github/workflows/nx-migrate.yml'
      - '.github/workflows/nx-run.yml'
      - '.github/workflows/package-and-deploy.yml'
      - '.github/workflows/package.yml'
      - '.github/workflows/pipeline.yml'
      - '.github/workflows/pull-request-closed.yml'
      - '.github/workflows/pull-request-commit-lint.yml'
      - '.github/workflows/pull-request-format-check.yml'
      - '.github/workflows/pull-request-package.yml'
      - '.github/workflows/pull-request-security-checks.yml'
      - '.github/workflows/release.yml'
      - '.github/workflows/renovate.yml'
      - '.github/workflows/repo-visualiser.yml'
      - '.github/workflows/translate.yml'
      - '.github/workflows/upload-vehicles.yml'
      - '.github/workflows/zap.yml'
      - '.gitignore'
      - '.husky'
      - '.run/**'
      - 'README.md'
      - 'catalog-info.yaml'
      - 'commitlint.config.js'
      - 'crowdin.update.sh'
      - 'docs/**'
      - 'init/**'
      - 'renovate.json'

permissions:
  actions: read
  pull-requests: read
  contents: read
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  CYPRESS_VERIFY_TIMEOUT: 60000

jobs:
  prepare:
    if: ${{ !contains(github.event.pull_request.title, '[no ci]') }}
    name: Prepare
    outputs:
      affected_apps_with_e2e_target: ${{ steps.prepare.outputs.affected_apps_with_e2e_target }}
      affected_projects_with_commercial_tag: ${{ steps.prepare.outputs.affected_projects_with_commercial_tag }}
      affected_projects_with_data_platform_tag: ${{ steps.prepare.outputs.affected_projects_with_data_platform_tag }}
      affected_projects_with_mobile_tag: ${{ steps.prepare.outputs.affected_projects_with_mobile_tag }}
      affected_projects_with_shared_tag: ${{ steps.prepare.outputs.affected_projects_with_shared_tag }}
      version: ${{ steps.prepare.outputs.version }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Prepare
        id: prepare
        uses: ./.github/actions/prepare
    timeout-minutes: 10

  lint-go:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_data_platform_tag)[0] != null }}
    name: Lint (Go)
    needs: [prepare]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: '1.24.4'
      - name: Tidy
        run: |
          go mod tidy
          git diff --exit-code || (echo "go.mod and go.sum files have been modified. Please run \"go mod tidy\" and commit the changes." && exit 1)
      - name: Lint
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9 # v8.0.0
        with:
          version: v2.1.0
          args: ./apps/data-platform/... ./libs/data-platform/... ./libs/shared/go/... --allow-parallel-runners --timeout=15m --verbose
          only-new-issues: true
    timeout-minutes: 15

  main-commercial:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_commercial_tag)[0] != null }}
    name: Main (Commercial)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: commercial-${{ matrix.targets }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Run
        run: npx nx affected --exclude '!tag:commercial' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-commercial-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: lint
            runs-on: ubuntu-22.04
          - targets: test --runInBand --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build --parallel=4
            runs-on: ubuntu-22.04-4xCPU
    timeout-minutes: 30

  main-data-platform:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_data_platform_tag)[0] != null }}
    name: Main (Data Platform)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: data-platform-${{ matrix.targets }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Run
        run: npx nx affected --exclude '!tag:data-platform' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-data-platform-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: test --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  main-mobile:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_mobile_tag)[0] != null }}
    name: Main (Mobile)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: mobile-${{ matrix.targets }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Run
        run: npx nx affected --exclude '!tag:mobile' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-mobile-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: lint
            runs-on: ubuntu-22.04
          - targets: test --runInBand
            runs-on: ubuntu-22.04
          - targets: build
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  main-shared:
    if: ${{ fromJSON(needs.prepare.outputs.affected_projects_with_shared_tag)[0] != null }}
    name: Main (Shared)
    needs: [prepare]
    runs-on: ${{ matrix.runs-on }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: shared-${{ matrix.targets }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Run
        run: npx nx affected --exclude '!tag:shared,!tag:support' --targets ${{ matrix.targets }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-shared-${{ matrix.targets }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        include:
          - targets: lint
            runs-on: ubuntu-22.04
          - targets: test --runInBand --parallel=4
            runs-on: ubuntu-22.04-4xCPU
          - targets: build build-storybook
            runs-on: ubuntu-22.04
    timeout-minutes: 30

  e2e:
    if: ${{ always() && !(contains(needs.*.result, 'cancelled')) && !(contains(needs.*.result, 'failure')) && fromJSON(needs.prepare.outputs.affected_apps_with_e2e_target)[0] != null }}
    name: E2E
    needs:
      [
        prepare,
        lint-go,
        main-commercial,
        main-data-platform,
        main-mobile,
        main-shared,
      ]
    runs-on: ${{ (contains(matrix.affected, 'data-platform') || contains(matrix.affected, 'design-system') || contains(matrix.affected, 'webapp')) && 'ubuntu-22.04-4xCPU' || 'ubuntu-22.04' }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: e2e-${{ matrix.affected }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Login ECR
        uses: ./.github/actions/ecr-login
        with:
          build-account-id: ${{ vars.AWS_BUILD_ACCOUNT_ID }}
          iam-role: github-ecr-data-platform-api
      - name: E2E
        run: npx nx e2e ${{ matrix.affected }}
      - name: Upload artifact
        if: ${{ failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dist-${{ matrix.affected }}
          path: dist
          retention-days: 1
    strategy:
      fail-fast: false
      matrix:
        affected: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_e2e_target) }}
    timeout-minutes: 20

  verify:
    if: ${{ always() && !contains(github.event.pull_request.title, '[no ci]') }}
    name: Verify
    needs:
      [
        prepare,
        lint-go,
        main-commercial,
        main-data-platform,
        main-mobile,
        main-shared,
        e2e,
      ]
    runs-on: ubuntu-24.04
    steps:
      - name: Success
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Failure
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
    timeout-minutes: 5
