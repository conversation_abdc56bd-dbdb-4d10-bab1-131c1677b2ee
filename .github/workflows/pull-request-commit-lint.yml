name: Pull Request (Commit Lint)

on:
  pull_request:
    types: ['opened', 'edited', 'reopened', 'synchronize']

jobs:
  main:
    if: ${{ !contains(github.event.pull_request.title, '[no ci]') }}
    name: Main (Commit <PERSON>t)
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Run
        run: echo $PULL_REQUEST_TITLE | npx commitlint
        env:
          PULL_REQUEST_TITLE: ${{ github.event.pull_request.title }}
    timeout-minutes: 10

  verify:
    if: always()
    name: Verify (Commit <PERSON>)
    needs: [main]
    runs-on: ubuntu-24.04
    steps:
      - name: Success
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Failure
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
    timeout-minutes: 5
