name: Pull Request (Package)
run-name: Pull Request (Package) - ${{ github.ref_name }}

on:
  workflow_dispatch:

permissions:
  actions: read
  contents: read
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  prepare:
    name: Prepare
    outputs:
      affected_apps_with_package_tag: ${{ steps.prepare.outputs.affected_apps_with_package_tag }}
      version: ${{ steps.prepare.outputs.version }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Prepare
        id: prepare
        uses: ./.github/actions/prepare
    timeout-minutes: 10

  package:
    if: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_package_tag)[0] != null }}
    name: Package
    needs: [prepare]
    outputs:
      packaged: ${{ needs.prepare.outputs.affected_apps_with_package_tag }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Package
        uses: ./.github/actions/package
        with:
          sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
          service: ${{ matrix.affected }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
          version: ${{ needs.prepare.outputs.version }}
    strategy:
      fail-fast: false
      matrix:
        affected: ${{ fromJSON(needs.prepare.outputs.affected_apps_with_package_tag) }}
    timeout-minutes: 20
