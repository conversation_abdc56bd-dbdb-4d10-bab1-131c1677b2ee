name: Analyse

on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  config-cat:
    name: ConfigCat
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: <PERSON><PERSON>
        uses: configcat/scan-repository@53b19276964b06c9c8bd3ad8606d91bdbc185003 # v2.7.0
        with:
          api-user: ${{ secrets.CONFIGCAT_API_USER }}
          api-pass: ${{ secrets.CONFIGCAT_API_PASS }}
          config-id: 08dcb7a4-2585-4f97-8b92-dd8b0138477c
    timeout-minutes: 5

  crowd-in:
    name: CrowdIn
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Upload
        uses: crowdin/github-action@297234bae049541aa48ed268e5de00dee4efa4b4 # v2.8.0
        with:
          config: crowdin.yml
          upload_sources: true
          upload_translations: false
          download_translations: false
        env:
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
    timeout-minutes: 5
