name: Package & deploy
run-name: Package & deploy ${{ inputs.projects }} version (${{ github.sha }})

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        type: environment
      projects:
        description: 'Project(s)'
        required: true
        type: string
      smoke-test:
        description: 'Run smoke tests'
        required: false
        type: boolean

permissions:
  actions: read
  contents: read
  id-token: write

jobs:
  prepare:
    name: 🧭 Prepare
    runs-on: ubuntu-24.04
    outputs:
      matrix: ${{ steps.prepare-matrix.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Prepare matrix
        id: prepare-matrix
        run: |
          echo "matrix=$(npx nx show projects --projects ${{ inputs.projects }},tag:${{ inputs.projects }} --exclude '!tag:package' --json | jq -r tostring)" >> $GITHUB_OUTPUT

  package:
    name: 🏗️ Package
    runs-on: ubuntu-24.04
    needs: [prepare]
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup
        uses: ./.github/actions/setup
        with:
          nx_cache_key: package-${{ matrix.service }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
      - name: Package
        uses: ./.github/actions/package
        with:
          sentry_auth_token: ${{ secrets.SENTRY_AUTH_TOKEN }}
          service: ${{ matrix.service }}
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
          version: ${{ github.sha }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.prepare.outputs.matrix) }}
    timeout-minutes: 10

  deploy:
    environment: ${{ inputs.environment }}
    name: 🚀 Deploy
    runs-on: ubuntu-24.04
    needs: [prepare, package]
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Sleep
        run: sleep $(( ${{ strategy.job-index }} * 1 ))s
      - name: Deploy
        uses: ./.github/actions/deploy
        with:
          environment: ${{ vars.ENVIRONMENT_NAME }}
          service: ${{ matrix.service }}
          version: ${{ github.sha }}
          wait-for-service-stability: true
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.prepare.outputs.matrix) }}
    timeout-minutes: 10

  smoke-tests:
    if: ${{ github.event.inputs.smoke-test == 'true' }}
    environment: ${{ inputs.environment }} (Smoke Test)
    name: 💨 Smoke Test
    needs: [deploy]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Smoke Test
        uses: ./.github/actions/post-deploy
        with:
          account-id: ${{ vars.AWS_ACCOUNT_ID }}
          stripe-github-token: ${{ secrets.STRIPE_GITHUB_TOKEN }}
    timeout-minutes: 20
