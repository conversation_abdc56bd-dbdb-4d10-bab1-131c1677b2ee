name: Nx Run

on:
  workflow_dispatch:
    inputs:
      project:
        description: 'Project'
        required: true
        type: string
      target:
        description: 'Target'
        required: true
        type: string

jobs:
  run:
    name: Run
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}

      - name: Run
        run: |
          npx nx run ${{ inputs.project }}:${{ inputs.target }}
