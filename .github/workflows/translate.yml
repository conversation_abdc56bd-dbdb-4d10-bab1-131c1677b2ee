name: Translate

# GitHub actions schedule is in UTC time.
# Schedule twice but only sync translations when UK time is 0730 (see prepare step).
# Schedule at the bottom of the hour to ensure one run will be between 0700 and 0759 UK time.
on:
  schedule:
    - cron: '30 6,7 * * 1-5'
  workflow_dispatch:

permissions:
  actions: read
  contents: write
  pull-requests: write
  repository-projects: read

jobs:
  prepare:
    name: Prepare
    outputs:
      translate: ${{ steps.prepare.outputs.translate }}
    runs-on: ubuntu-24.04
    steps:
      - name: Prepare
        id: prepare
        env:
          EVENT_NAME: ${{ github.event_name }}
          TZ: Europe/London
        run: |
          if [[ ${EVENT_NAME} == 'schedule' ]]; then
            if [ $(date +%H) -eq '07' ]; then
              echo "translate=true" >> "$GITHUB_OUTPUT"
            else
              echo "translate=false" >> "$GITHUB_OUTPUT"
            fi
          else
            echo "translate=true" >> "$GITHUB_OUTPUT"
          fi

  sync-with-crowdin:
    if: needs.prepare.outputs.translate == 'true'
    name: Sync With <PERSON><PERSON>
    needs: [prepare]
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Authenticate
        id: fetch-auth-token
        uses: getsentry/action-github-app-token@d4b5da6c5e37703f8c3b3e43abb5705b46e159cc # v3.0.0
        with:
          app_id: ${{ secrets.RELEASE_APP_ID }}
          private_key: ${{ secrets.RELEASE_APP_PRIVATE_KEY }}

      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}

      - name: Get actions user id
        id: get-actions-user-id
        run: |
          actions_user_id=`id -u $USER`
          echo "actions_user_id=$actions_user_id" >> $GITHUB_OUTPUT

      - name: Sync Translations
        uses: crowdin/github-action@297234bae049541aa48ed268e5de00dee4efa4b4 # v2.8.0
        with:
          upload_sources: true
          upload_translations: false
          download_translations: true
          export_only_approved: false
          download_translations_args: -v
          crowdin_branch_name: main
          localization_branch_name: translations
          commit_message: 'chore(translations): update translations from crowdin'
          pull_request_title: 'chore(translations): update translations from crowdin'
          create_pull_request: false
          config: crowdin.yml
        env:
          GITHUB_TOKEN: ${{ steps.fetch-auth-token.outputs.token }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}

      # @see https://github.com/actions/runner/issues/1282
      - name: Reset file permissions
        run: |
          find . ! -user ${{ steps.get-actions-user-id.outputs.actions_user_id }} -exec sudo chown -cvf "${{ steps.get-actions-user-id.outputs.actions_user_id }}" "{}" \;

      - name: Fetch Main
        if: github.ref != 'refs/heads/main'
        run: git rev-parse --verify main || git remote set-branches origin main && git fetch --depth 100 origin main && git branch main origin/main

      - name: Update Generated Translated Files
        run: |
          (git push origin translations --no-verify || true)
          npx nx affected --target=build --base=main --head=translations

      - name: Verify
        uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
        id: verify
        with:
          skip_initial_fetch: true
          base_sha: main
          sha: translations

      - name: Push pending changes
        if: steps.verify.outputs.any_changed == 'true'
        run: |
          echo "One or more test file(s) has changed."
          for file in ${TEST_ALL_CHANGED_FILES}; do
            echo "$file was changed"
          done
          git config --global user.name 'actions-bot'
          git config --global user.email '<EMAIL>'
          (git commit -am 'chore(translations): update generated files' || true)
          (git push origin translations --no-verify || true)
        env:
          GITHUB_TOKEN: ${{ steps.fetch-auth-token.outputs.token }}
          TEST_ALL_CHANGED_FILES: ${{ steps.verify.outputs.all_changed_files }}

      - name: Submit
        if: steps.verify.outputs.any_changed == 'true'
        run: |
          gh pr create -B main -H ${{ env.BRANCH_NAME }} --title '${{ env.PR_TITLE }}' --body '${{ env.PR_BODY }}' || gh pr edit -B main --title '${{ env.PR_TITLE }}' --body '${{ env.PR_BODY }}'
        env:
          BRANCH_NAME: translations
          PR_TITLE: 'chore(translations): update translations from crowdin'
          PR_BODY: 'If this is a PR that linting is not running, then make sure you close the PR, delete the branch and re-run the crowdin workflow.'
          GITHUB_TOKEN: ${{ steps.fetch-auth-token.outputs.token }}
