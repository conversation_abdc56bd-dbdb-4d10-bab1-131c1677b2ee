name: Upload Vehicles JSON

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        type: choice
        options:
          - Development
          - Production
      bev-source:
        description: 'The URL of the BEV source'
        required: true
        type: string
      phev-source:
        description: 'The URL of the PHEV source'
        required: true
        type: string

permissions:
  actions: read
  pull-requests: read
  contents: read
  id-token: write

jobs:
  deploy-dev:
    name: Upload Vehicles JSON (dev)
    if: ${{ inputs.environment == 'Development' }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}

      - name: Generate Vehicles JSON
        env:
          BEV_SOURCE: ${{ inputs.bev-source }}
          PHEV_SOURCE: ${{ inputs.phev-source }}
          ENODE_OAUTH_BASE_URL: https://oauth.production.enode.io
          ENODE_API_BASE_URL: https://enode-api.production.enode.io
          ENODE_CLIENT_ID: ${{ secrets.ENODE_CLIENT_ID }}
          ENODE_CLIENT_SECRET: ${{ secrets.ENODE_CLIENT_SECRET }}
          ENODE_CSV_URL: https://developers.enode.com/resources/capabilities/vehicle.csv
        run: |
          npx nx convert vehicles-conversion convertVehicles

      - name: Upload to CDN
        uses: ./.github/actions/cdn-upload
        with:
          origin: apps/mobile/vehicles-conversion/src/assets/vehicles.json
          destination: home-app/dev/vehicles.json

  deploy-prod:
    name: Upload Vehicles JSON (prod)
    if: ${{ inputs.environment == 'Production' }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup
        uses: ./.github/actions/setup
        with:
          stripe_github_token: ${{ secrets.STRIPE_GITHUB_TOKEN }}

      - name: Generate Vehicles JSON
        env:
          BEV_SOURCE: ${{ inputs.bev-source }}
          PHEV_SOURCE: ${{ inputs.phev-source }}
          ENODE_OAUTH_BASE_URL: https://oauth.production.enode.io
          ENODE_API_BASE_URL: https://enode-api.production.enode.io
          ENODE_CLIENT_ID: ${{ secrets.ENODE_CLIENT_ID }}
          ENODE_CLIENT_SECRET: ${{ secrets.ENODE_CLIENT_SECRET }}
          ENODE_CSV_URL: https://developers.enode.com/resources/capabilities/vehicle.csv
        run: |
          npx nx convert vehicles-conversion convertVehicles

      - name: Upload to CDN
        uses: ./.github/actions/cdn-upload
        with:
          origin: apps/mobile/vehicles-conversion/src/assets/vehicles.json
          destination: home-app/vehicles.json
