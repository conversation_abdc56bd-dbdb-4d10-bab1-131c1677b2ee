name: 'Configure and install tooling'
description: 'Configure and install tooling on ubuntu image.'

inputs:
  nx_cache_key:
    description: 'Key to use when saving and restoring the Nx cache.'
    required: false
  stripe_github_token:
    description: 'Stripe github token'
    required: true

runs:
  using: 'composite'

  steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        fetch-depth: 0
        filter: tree:0
        ref: ${{ github.event.pull_request.head.sha }}

    - name: Derive appropriate SHAs for base and head for `nx affected` commands
      uses: nrwl/nx-set-shas@dbe0650947e5f2c81f59190a38512cf49126fe6b # v4.3.0
      with:
        last-successful-event: ${{ github.event_name == 'release' && 'release' || 'push' }}

    - name: Setup Node
      id: setup-node
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
      with:
        node-version-file: '.nvmrc'
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Use cache (node_modules)
      id: cache-node-modules
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: |
          node_modules
          ~/.cache/Cypress
          ~/.cache/ms-playwright
          ~/.cache/prisma
          ~/.cache/puppeteer
        key: ${{ runner.os }}-node-modules-${{ steps.setup-node.outputs.node-version }}-${{ hashFiles('package.json', 'package-lock.json') }}

    - name: Use cache (Nx)
      if: ${{ inputs.nx_cache_key != '' }}
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: .nx/cache
        key: ${{ runner.os }}-nx-cache-${{ steps.setup-node.outputs.node-version }}-${{ hashFiles('package.json', 'package-lock.json') }}-${{ inputs.nx_cache_key }}

    - name: Use cache (Next.js)
      if: ${{ inputs.nx_cache_key != '' }}
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: dist/apps/**/.next/cache
        key: ${{ runner.os }}-next-cache-${{ steps.setup-node.outputs.node-version }}-${{ hashFiles('package.json', 'package-lock.json') }}-${{ inputs.nx_cache_key }}-${{ hashFiles('apps/**/*.js', 'apps/**/*.jsx', 'apps/**/*.ts', 'apps/**/*.tsx') }}

    - name: Install dependencies
      if: steps.cache-node-modules.outputs.cache-hit != 'true'
      run: |
        npm ci
        npx playwright install
      env:
        STRIPE_GITHUB_TOKEN: ${{ inputs.stripe_github_token }}
      shell: bash

    - name: Setup Go
      id: setup-go
      uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5
      with:
        go-version: '1.24.4'

    - name: Create localstack profile
      run: |
        mkdir ~/.aws
        echo "[profile localstack]" > ~/.aws/config
        echo "region = eu-west-1" >> ~/.aws/config
        echo "output = json" >> ~/.aws/config
      shell: bash
