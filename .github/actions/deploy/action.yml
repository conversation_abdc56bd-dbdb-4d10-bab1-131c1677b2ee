name: 'Deploy'
description: 'Deploy application into a cluster.'

inputs:
  environment:
    description: 'Name of the environment into which to perform the deployment.'
    required: true
  service:
    description: 'Name of service to deploy.'
    required: true
  version:
    description: 'Version of service to deploy.'
    required: true
  wait-for-service-stability:
    description: 'Whether to wait for the ECS service to reach stable state after deploying the new task definition.'
    required: false

runs:
  using: 'composite'

  steps:
    - name: Check deploy properties file exists
      id: check-deploy-properties-exists
      uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
      with:
        files: '.aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties'

    - name: Check task definition file exists
      id: check-task-definition-exists
      uses: andstor/file-existence-action@076e0072799f4942c8bc574a82233e1e4d13e9d6 # v3.0.0
      with:
        files: '.aws/${{ inputs.environment }}/${{ inputs.service }}/task-definition.json'

    - name: Task definition exists
      if: steps.check-task-definition-exists.outputs.files_exists == 'true'
      run: echo "Task definition file exists."
      shell: bash

    - name: Read properties from build properties file
      id: read-build-properties
      run: |
        echo "build_account_id=$(cat .aws/build/${{ inputs.service }}/build.properties | grep "account.id=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
      shell: bash

    - name: Read properties from deploy properties file
      if: steps.check-deploy-properties-exists.outputs.files_exists == 'true'
      id: read-deploy-properties
      run: |
        echo "account_id=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "account.id=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_cluster=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.cluster=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_service_deploy=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.service.deploy" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_1=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.1=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_2=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.2=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_3=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.3=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_4=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.4=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_5=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.5=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_6=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.6=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_7=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.7=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_8=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.8=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_9=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.9=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
        echo "ecs_scheduled_tasks_10=$(cat .aws/${{ inputs.environment }}/${{ inputs.service }}/deploy.properties | grep "ecs.scheduled-tasks.10=" | cut -d'=' -f2)" >> $GITHUB_OUTPUT
      shell: bash

    - name: Assume role for environment
      if: steps.check-task-definition-exists.outputs.files_exists == 'true'
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        aws-region: eu-west-1
        role-to-assume: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        role-duration-seconds: 900
        role-skip-session-tagging: true

    - name: Render new revision of task definition
      if: steps.check-task-definition-exists.outputs.files_exists == 'true'
      id: render-new-task-definition
      uses: aws-actions/amazon-ecs-render-task-definition@acd72bc11233ac43bac45ddb7b8e54ada737e6cd # v1.7.4
      with:
        task-definition: .aws/${{ inputs.environment }}/${{ inputs.service }}/task-definition.json
        container-name: ${{ inputs.service }}
        image: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}

    - name: Deploy new revision of task definition and start service
      if: ${{ steps.check-task-definition-exists.outputs.files_exists == 'true' && steps.read-deploy-properties.outputs.ecs_service_deploy == 'true' }}
      id: amazon-ecs-deploy-task-definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@f7b2b96b7fe1b20b452641919c0559bcc19ab8f9 # v2.3.3
      with:
        task-definition: ${{ steps.render-new-task-definition.outputs.task-definition }}
        service: ${{ inputs.service }}
        cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        wait-for-service-stability: ${{ inputs.wait-for-service-stability }}

    # https://github.com/aws-actions/amazon-ecs-deploy-task-definition/issues/191#issuecomment-**********
    - name: Check if service is running new revision of task definition
      if: ${{ steps.amazon-ecs-deploy-task-definition.outcome == 'success' && inputs.wait-for-service-stability == 'true' }}
      run: |
        CURRENT_TASK_DEF_ARN=$(aws ecs describe-services --cluster ${{ steps.read-deploy-properties.outputs.ecs_cluster }} --services ${{ inputs.service }} --query services[0].deployments[0].taskDefinition | jq -r ".")
        NEW_TASK_DEF_ARN=${{ steps.amazon-ecs-deploy-task-definition.outputs.task-definition-arn }}
        echo "Current task arn: $CURRENT_TASK_DEF_ARN"
        echo "New task arn: $NEW_TASK_DEF_ARN"
        if [ "$CURRENT_TASK_DEF_ARN" != "$NEW_TASK_DEF_ARN" ]; then
          echo "Deployment failed."
          exit 1
        fi
      shell: bash

    - name: Deploy new revision of task definition and don't start the service
      if: ${{ steps.check-task-definition-exists.outputs.files_exists == 'true' && steps.read-deploy-properties.outputs.ecs_service_deploy == 'false' }}
      uses: aws-actions/amazon-ecs-deploy-task-definition@f7b2b96b7fe1b20b452641919c0559bcc19ab8f9 # v2.3.3
      with:
        task-definition: ${{ steps.render-new-task-definition.outputs.task-definition }}
        cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        wait-for-service-stability: ${{ inputs.wait-for-service-stability }}

    - name: Task definition does not exist
      if: steps.check-task-definition-exists.outputs.files_exists == 'false'
      run: echo "Task definition file does not exist."
      shell: bash

    - name: Deploy new revision of task definition and start service
      if: ${{ steps.check-task-definition-exists.outputs.files_exists == 'false' && steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_service_deploy == 'true' }}
      id: deploy-image-uri-to-ecs
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        ecs_service: ${{ inputs.service }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}
        wait_for_service_stability: ${{ inputs.wait-for-service-stability }}

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_1 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_1 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_1 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_1 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_2 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_2 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_2 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_2 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_3 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_3 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_3 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_3 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_4 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_4 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_4 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_4 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_5 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_5 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_5 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_5 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_6 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_6 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_6 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_6 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_7 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_7 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_7 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_7 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_8 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_8 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_8 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_8 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_9 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_9 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_9 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_9 }}
        wait_for_service_stability: 'false'

    - name: Deploy new revision of scheduled task (${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_10 }})
      if: ${{ steps.read-deploy-properties.outcome == 'success' && steps.read-deploy-properties.outputs.ecs_scheduled_tasks_10 != '' }}
      uses: Pod-Point-Platform/deploy-image-uri-to-ecs@v3.3.2
      with:
        aws_build_iam_role_arn: arn:aws:iam::${{ steps.read-build-properties.outputs.build_account_id }}:role/github-ecr-${{ inputs.service }}
        aws_iam_role_arn: arn:aws:iam::${{ steps.read-deploy-properties.outputs.account_id }}:role/github-ecs-${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        container_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_10 }}
        docker_image_uri: ${{ steps.read-build-properties.outputs.build_account_id }}.dkr.ecr.eu-west-1.amazonaws.com/${{ inputs.service }}:${{ inputs.version }}
        ecs_cluster: ${{ steps.read-deploy-properties.outputs.ecs_cluster }}
        environment_name: ${{ inputs.environment }}
        task_definition_name: ${{ inputs.service }}-${{ steps.read-deploy-properties.outputs.ecs_scheduled_tasks_10 }}
        wait_for_service_stability: 'false'
