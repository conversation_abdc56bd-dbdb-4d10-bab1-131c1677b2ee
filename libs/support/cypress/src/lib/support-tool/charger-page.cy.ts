export const describeChargerPage = (): void => {
  describe('Charger page', () => {
    it('should have the correct page titles', () => {
      cy.visit('/chargers');
      cy.title().should('eq', 'Support Tool - Chargers');
      cy.visit('/chargers/PSL-12345?socket=A');
      cy.title().should('eq', 'Support Tool - Charger - PSL-12345 (Socket A)');
      cy.visit('/chargers/PSL-12345/logs/diagnostic?socket=A');
      cy.title().should(
        'eq',
        'Support Tool - Diagnostic logs - PSL-12345 (Socket A)'
      );
    });

    it('should render correctly', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');

      cy.shouldHaveText('<PERSON> - <EMAIL>');
      cy.shouldHaveText('Charger information');
      cy.shouldHaveText('Charger connectivity');
      cy.shouldHaveText('Last complete charge');
      cy.shouldHaveText('Charger access point (AP)');
      cy.shouldHaveText('Charger configuration');
      cy.shouldHaveText('Solar configuration');
      cy.shouldHaveText('Installation details');
      cy.shouldHaveText('Warranty details');
      cy.shouldHaveText('Last complete charge');
      cy.shouldHaveText('Modes');
      cy.shouldHaveText('Schedules');
      cy.shouldHaveText('Smart mode');
      cy.shouldHaveText('Off mode');
      cy.shouldHaveText('Charge now');
      cy.shouldHaveText('Flex');
    });

    it('should send a soft reset command', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Soft reset');
      cy.shouldHaveText(
        'Are you sure you want to send command soft reset to charger PSL-12345 (Socket A)?'
      );
      cy.clickButton('Send');
      cy.shouldHaveText('Successfully sent soft reset command');
    });

    it('should send a hard reset command', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Hard reset');
      cy.shouldHaveText(
        'Are you sure you want to send command hard reset to charger PSL-12345 (Socket A)?'
      );
      cy.clickButton('Send');
      cy.shouldHaveText('Successfully sent hard reset command');
    });

    it('should send an in service command', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Set in service');
      cy.shouldHaveText(
        'Are you sure you want to send command set in service to charger PSL-12345 (Socket A)?'
      );
      cy.clickButton('Send');
      cy.shouldHaveText('Successfully sent set in service command');
    });

    it('should send an out of service command', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Set out of service');
      cy.shouldHaveText(
        'Are you sure you want to send command set out of service to charger PSL-12345 (Socket A)?'
      );
      cy.clickButton('Send');
      cy.shouldHaveText('Successfully sent set out of service command');
    });

    it('should send an unlock connector command', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Unlock connector');
      cy.shouldHaveText(
        'Are you sure you want to send command unlock connector to charger PSL-12345 (Socket A)?'
      );
      cy.clickButton('Send');
      cy.shouldHaveText('Successfully sent unlock connector command');
    });

    it('should request diagnostic logs and redirect to view diagnostic logs page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Commands');
      cy.clickMenuItem('Request diagnostic logs');
      cy.shouldHaveHeading(2, 'Request diagnostic logs');
      cy.clickButton('Send');
      cy.shouldHaveText('Diagnostic logs - PSL-12345 (Socket A)');
    });

    it('should navigate to the charger configuration page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Edit charger configuration');
      cy.shouldHaveHeading(1, 'Configuration - PSL-12345 (Socket A)');
      cy.shouldHaveHeading(2, 'Charger configuration');
    });

    it('should navigate to the solar configuration page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Edit solar configuration');
      cy.shouldHaveHeading(1, 'Configuration - PSL-12345 (Socket A)');
      cy.shouldHaveHeading(2, 'Solar configuration');
    });

    it('should navigate to the installation page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickLink('View installation details');
      cy.shouldHaveText('Installation details - PSL-12345 (Socket A)');
    });

    it('should navigate to the recent charges page', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickLink('Recent charges');
      cy.shouldHaveText('Recent completed charges - PSL-12345 (Socket A)');
    });

    it('should set the operator', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Set operator');
      cy.shouldHaveHeading(2, 'Set operator');
      cy.selectOption('Operator', 'Lidl', 0, true);
      cy.clickButton('Save changes');
      cy.shouldHaveText(
        'Successfully set the operator. It may take a few seconds to update. You may need to refresh your browser.'
      );
    });

    it('should unlink the user from the charger', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Unlink user John Doe');
      cy.shouldHaveHeading(2, 'Unlink charger');
      cy.clickButton('Unlink charger');
      cy.shouldHaveText('Charger successfully unlinked from account');
    });

    it('should toggle smart mode for the charger', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-56789');
      cy.clickButton('Go');
      cy.clickCheckbox('Smart mode');
      cy.clickButton('Confirm');
      cy.shouldHaveText('Mode set to manual.');
    });

    it('should show modal if charger is under delegated control when toggling smart mode', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickCheckbox('Smart mode');
      cy.shouldHaveText(
        'Smart mode cannot be changed whilst charger is under delegated control'
      );
    });

    it('should be able to remove delegated control from the charger', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickButton('Remove delegated control');
      cy.shouldHaveHeading(2, 'Remove from delegated control');
      cy.clickButton('Confirm');
      cy.shouldHaveText('Charger no longer under delegated control');
    });

    it('should toggle charge now for the charger', () => {
      cy.clickLink('Chargers');
      cy.enterText('PPID or Name', 'PSL-12345');
      cy.clickButton('Go');
      cy.clickCheckbox('Charge now');
      cy.shouldHaveHeading(2, 'Set duration of charge now');
      cy.selectOption('Minutes', '30', 0, true);
      cy.selectOption('Hours', '2', 0, true);
      cy.clickButton('Save changes');
      cy.shouldHaveText('Charge now set to active.');
      cy.clickCheckbox('Charge now');
      cy.shouldHaveText('Charge now set to inactive.');
    });
  });
};
