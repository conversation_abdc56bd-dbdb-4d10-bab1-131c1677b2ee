networks:
  pod-point:
    name: '${NETWORK_NAME:-pod-point}'
services:
  experience-cluster:
    container_name: '${CONTAINER_NAME:-experience-cluster}'
    image: postgis/postgis:15-3.4-alpine@sha256:d96be3a9c16d44e22d34d5c7b6e85be8875addf672672c6cd38171ea6055a94a
    restart: always
    environment:
      POSTGRES_PASSWORD: password
      POSTGRES_USER: postgres
    ports:
      - '${PORT:-5432}:5432'
    networks:
      - pod-point
    volumes:
      - ./docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready']
