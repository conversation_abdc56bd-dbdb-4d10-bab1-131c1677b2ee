# Shared Test DB Podadmin

This library provides a Docker Compose runner to launch a replica of the podadmin database containing test data also
curated within this library. It can be used during local development and also to support automated end-to-end testing.

## Running the database

Run `npx nx compose shared-test-db-podadmin -c up` to start the database container.

Run `npx nx compose shared-test-db-podadmin -c down` to stop the database container.

Run `npx nx compose shared-test-db-podadmin -c remove` to remove the database container (but not volumes).

Run `npx nx compose shared-test-db-podadmin -c recreate` to recreate the database container and volumes.

## Adding more test data

The intention is to use realistic data only, extracted from a production or production like database. There is one file
in the migrations directory for each table within podadmin. The docker-compose.yml file ensures that these are executed
in the correct order.
