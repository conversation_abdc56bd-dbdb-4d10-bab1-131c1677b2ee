-- select * from pod_models
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(1, 1, 2, NULL, 'T7-S', 1, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(2, 1, 3, NULL, 'T13', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(3, 1, 3, NULL, 'T32', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(4, 1, 3, NULL, 'P32', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(5, 2, NULL, NULL, 'SGTE', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:01:57');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(6, 3, NULL, NULL, 'Elektrobay', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:02:26');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(7, 4, NULL, NULL, 'Terra 51', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:02:46');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(9, 5, NULL, NULL, 'Dual32', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:04:19');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(10, 6, NULL, NULL, 'GE DuraStation', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:04:13');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(11, 1, NULL, NULL, 'Solo', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:04:22');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(12, 6, NULL, NULL, 'CPS-DBT Rapid Charge Unit', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:06:22');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(13, 7, NULL, NULL, 'EVolt', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:06:25');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(14, 8, NULL, NULL, 'NQC', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:11:23');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(15, 9, NULL, NULL, 'CCL-PM1', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:12:08');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(16, 10, NULL, NULL, 'VirtualCP', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:18:07');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(17, 11, NULL, NULL, 'Rolec', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:19:21');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(18, 12, NULL, NULL, 'Keba', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:20:18');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(19, 3, NULL, NULL, 'EB305Twin', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:21:08');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(20, 1, 3, NULL, 'T7', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(21, 5, NULL, NULL, 'Combi32', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:23:49');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(22, 8, NULL, NULL, 'DCRapid', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:24:35');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(23, 1, 3, NULL, 'T22', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(24, 13, 3, NULL, 'S3 Three Phase', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:25:35');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(29, 1, NULL, NULL, 'S7u', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:26:25');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(31, 13, 3, NULL, '7kw/7kw', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:28:21');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(33, 13, 3, NULL, 'T22 wired up as T7', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:28:36');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(35, 1, NULL, NULL, 'S22u', 0, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-04-07 09:23:03', '2017-08-07 15:29:18');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(37, 13, 3, NULL, 'T7 wired up as T22', 0, 0, 0, 0, NULL, '2015-05-27 11:03:32', NULL, '2017-08-07 15:30:11');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(38, 1, 1, NULL, 'S3-1N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(39, 1, 1, NULL, 'S3-2N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(40, 1, 1, NULL, 'S7-1N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(41, 1, 1, NULL, 'S7-2N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(42, 1, 1, NULL, 'S7-UN', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(43, 1, 1, NULL, 'S11-2N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(44, 1, 1, NULL, 'S11-UN', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(45, 1, 1, NULL, 'S22-2N', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(46, 1, 1, NULL, 'S22-UN', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(47, 1, 1, NULL, 'S3-1C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(48, 1, 1, NULL, 'S3-2C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(49, 1, 1, NULL, 'S7-1C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(50, 1, 1, NULL, 'S7-2C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(51, 1, 1, NULL, 'S7-UC', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(52, 1, 1, NULL, 'S7-UP', 1, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(53, 1, 1, NULL, 'S11-2C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(54, 1, 1, NULL, 'S11-UC', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(55, 1, 1, NULL, 'S22-2C', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(56, 1, 1, NULL, 'S22-UC', 0, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(57, 1, 1, NULL, 'S22-UP', 1, 0, 0, 0, NULL, '2015-06-12 13:17:46', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(58, 1, 2, NULL, 'T22-S', 1, 0, 0, 0, NULL, '2015-06-25 11:02:42', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(59, 1, 2, NULL, 'T3-S', 1, 0, 0, 0, NULL, '2015-07-14 12:52:41', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(60, 1, 1, NULL, 'S3-UP', 1, 0, 0, 0, NULL, '2015-08-24 10:52:11', '2015-10-14 15:01:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(61, 1, 5, 100302, 'S3-1C-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2016-09-12 10:58:01', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(62, 1, 5, 100302, 'S7-1C-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2016-09-12 10:58:26', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(63, 1, 5, 245210, 'S7-UC-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2016-09-12 10:58:50', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(64, 1, 5, 100298, 'S7-2C-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2016-09-12 10:59:18', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(65, 1, 5, 245210, 'S7-UP-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2016-09-12 10:59:33', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(66, 1, 5, 100302, 'S22-UC-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2016-09-12 10:59:53', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(67, 1, 5, 100302, 'S22-2C-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2016-09-12 11:00:16', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(68, 1, 5, 100302, 'S22-UP-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2016-09-12 11:01:11', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(69, 1, 6, 245210, 'T7-S-2', 1, 0, 0, 0, NULL, '2016-09-12 11:01:42', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(70, 1, 6, 100302, 'T22-S-2', 1, 0, 0, 0, NULL, '2016-09-12 11:02:00', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(71, 4, 4, NULL, 'Terra 53 CJG', 1, 1, 0, 0, NULL, '2016-11-18 13:20:13', '2016-11-18 13:20:13', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(72, 1, 5, 100302, 'S3-2C-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2016-12-08 17:27:31', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(73, 1, 5, 100300, 'S7-UC-2-NO', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2016-12-09 17:28:16', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(74, 1, 5, 100302, 'S22-UC-2-NO', 0, 0, 0, 0, NULL, '2016-12-09 17:29:17', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(75, 4, 4, NULL, 'Terra 53 CGT', 1, 1, 0, 0, NULL, '2017-04-06 08:59:37', '2017-04-06 08:59:37', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(76, 1, 2, NULL, 'T7/3-S', 1, 0, 0, 0, NULL, '2017-05-03 13:09:05', NULL, NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(77, 1, 6, 245210, 'T3-S-2', 1, 0, 0, 0, NULL, '2017-05-18 16:14:03', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(78, 1, 5, 245210, 'S7-UP-2-3G', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2017-06-15 16:12:50', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(79, 1, 5, 100302, 'S22-UP-2-3G', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2017-06-15 16:12:50', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(80, 1, 6, 245210, 'T7-S-2-RO', 1, 0, 0, 0, NULL, '2017-06-15 16:12:50', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(81, 1, 6, 100302, 'T22-S-2-3G', 1, 0, 0, 0, NULL, '2017-06-15 16:12:50', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(82, 1, 1, NULL, 'S11-UP', 1, 0, 0, 0, NULL, '2017-07-17 16:07:19', '2017-07-17 16:07:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(83, 1, 7, 100299, 'S11-UC-2-REN', 0, 0, 0, 0, NULL, '2017-08-01 10:19:46', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(84, 1, 7, NULL, 'S11-2C-2-REN', 0, 0, 0, 0, NULL, '2017-08-01 10:19:46', '2017-08-01 10:19:46', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(85, 1, 7, 100299, 'S3-UC-2-REN', 0, 0, 0, 0, NULL, '2017-08-01 10:19:46', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(86, 1, 7, NULL, 'S3-2C-2-REN', 0, 0, 0, 0, NULL, '2017-08-01 10:19:46', NULL, NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(87, 1, 12, 245210, 'M7-Proto', 1, 0, 0, 0, NULL, '2017-08-23 12:26:51', '2017-08-23 12:26:53', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(88, 1, 5, NULL, 'S3-UP-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2017-11-01 15:26:59', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(89, 1, 5, NULL, 'S11-UP-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2018-01-10 14:44:57', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(90, 1, 6, NULL, 'T11-S-2', 1, 0, 0, 0, NULL, '2018-01-22 17:12:57', '2018-01-22 17:13:01', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(91, 1, 6, NULL, 'T11-S-2-3G', 1, 0, 0, 0, NULL, '2018-01-22 17:28:58', '2018-01-22 17:29:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(92, 4, 4, NULL, 'Terra 53 CJT', 1, 1, 0, 0, NULL, '2018-02-14 12:48:17', '2018-02-14 12:48:17', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(93, 1, 6, NULL, 'T7/3-S-2', 1, 0, 0, 0, NULL, '2018-02-23 11:51:21', '2018-02-23 11:51:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(94, 1, 5, NULL, 'S11-UC-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2018-03-21 17:33:07', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(95, 1, 5, NULL, 'S3-UP-2-3G', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2018-04-23 14:06:13', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(96, 14, 9, NULL, 'EVDE25E4DUM', 1, 1, 0, 0, NULL, '2018-05-29 13:24:24', '2018-05-29 13:24:24', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(97, 4, 8, NULL, 'Terra 53 CJ', 1, 1, 0, 0, NULL, '2018-08-14 10:38:37', '2018-08-14 10:38:37', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(98, 1, 7, NULL, 'S22-UC-2-REN', 0, 0, 0, 0, NULL, '2018-12-07 16:08:04', '2018-12-07 16:08:04', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(99, 1, 5, 100298, 'S3-2C-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-01-02 10:28:29', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(100, 1, 5, 245210, 'S7-UC-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:30:22', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(101, 1, 5, 100298, 'S7-2C-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-01-02 10:32:27', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(102, 1, 5, 245210, 'S7-UP-6mA-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:33:52', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(103, 1, 5, 245210, 'S7-UC-6mA-2-H', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:39:39', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(104, 1, 5, 245210, 'S7-UP-6mA-2-RO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:42:45', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(105, 1, 5, 245210, 'S22-UC-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:44:46', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(106, 1, 5, 100298, 'S22-2C-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-01-02 10:46:30', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(107, 1, 5, 245210, 'S22-UP-6mA-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:48:24', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(108, 1, 5, 245210, 'S22-UP-6mA-2-RO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-02 10:49:56', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(109, 1, 6, 245210, 'T3-S-6mA-2', 1, 0, 0, 0, NULL, '2019-01-02 11:09:23', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(110, 1, 6, 245210, 'T7-S-6mA-2', 1, 0, 0, 0, NULL, '2019-01-02 11:11:22', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(111, 1, 6, 245210, 'T22-S-6mA-2', 1, 0, 0, 0, NULL, '2019-01-02 11:13:40', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(112, 1, 6, 245210, 'T3-S-6mA-2-RO', 1, 0, 0, 0, NULL, '2019-01-02 11:15:51', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(113, 1, 6, 245210, 'T7-S-6mA-2-RO', 1, 0, 0, 0, NULL, '2019-01-02 11:19:04', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(114, 1, 6, 245210, 'T22-S-6mA-2-RO', 1, 0, 0, 0, NULL, '2019-01-02 11:22:00', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(115, 1, 5, 100300, 'S7-UP-2-NO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-25 13:15:56', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(116, 1, 5, 100302, 'S22-UP-2-NO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-01-25 13:17:40', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(117, 1, 6, 100302, 'T3-S-2-NO', 1, 0, 0, 0, NULL, '2019-01-31 10:57:03', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(118, 1, 6, 245210, 'T3-S-6mA-2-NO', 1, 0, 0, 0, NULL, '2019-01-31 10:57:03', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(119, 4, 8, NULL, 'Terra 53 CT', 1, 1, 0, 0, NULL, '2019-02-19 12:09:54', '2019-02-19 12:09:54', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(120, 4, 4, NULL, 'Terra 53 CJT (2)', 1, 1, 0, 0, NULL, '2019-02-21 14:47:49', '2019-02-21 14:47:49', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(121, 1, 6, 245210, 'T11-S-6mA-2-NO', 1, 0, 0, 0, NULL, '2019-02-21 17:13:22', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(122, 1, 5, 245210, 'S7-UP-6mA-2-NO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-02-21 17:59:21', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(123, 1, 5, 245210, 'S22-UP-6mA-2-NO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-02-21 18:00:48', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(124, 1, 5, 132159, 'S7-UC-6mA-2-NO', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-02-21 18:03:00', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(125, 1, 5, 132159, 'S22-2C-6mA-2-NO', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-02-21 18:04:37', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(126, 1, 5, 132159, 'S22-UC-6mA-2-NO', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-02-28 11:16:09', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(127, 1, 5, NULL, 'S3-UC-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-02-28 17:06:38', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(128, 1, 6, 245210, 'T11-S-6mA-2', 1, 0, 0, 0, NULL, '2019-03-20 11:02:59', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(129, 1, 6, 245210, 'T11-S-6mA-2-RO', 1, 0, 0, 0, NULL, '2019-04-11 10:19:23', '2019-04-11 10:19:23', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(130, 4, 4, NULL, 'Terra 54 CJG', 1, 1, 0, 0, NULL, '2019-07-08 12:46:08', '2019-07-08 12:46:08', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(131, 1, 5, 132159, 'S7-2C-6mA-2-NO', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-07-18 13:56:33', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(132, 1, 12, 245210, 'MT7-S-6mA-2-RO', 1, 0, 0, 0, NULL, '2019-07-24 13:00:35', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(133, 1, 5, 100298, 'S7-2C-6mA-2-VW', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-07-31 10:23:54', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(134, 4, 4, NULL, 'Terra 54 CJT', 1, 1, 0, 0, NULL, '2019-08-09 19:07:23', '2019-08-09 19:07:23', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(135, 1, 5, 245210, 'S3-UP-6mA-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-09-17 10:41:50', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(136, 1, 6, 245210, 'T3-S-2-RO', 1, 0, 0, 0, NULL, '2019-09-26 14:30:55', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(137, 15, 11, NULL, 'Veefil-RT TRI93-50-01', 1, 1, 0, 0, NULL, '2019-10-31 16:20:13', '2019-10-31 16:20:13', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(138, 1, 5, 245210, 'S11-UP-6mA-2', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-11-01 12:15:21', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(139, 1, 5, 245210, 'S11-UP-6mA-2-RO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2019-11-01 12:15:37', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(140, 1, 5, 100298, 'S7-2C-6mA-2-S', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2019-11-20 10:44:06', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(141, 1, 10, 245210, 'T7-S-03-BK-RFID-6mA', 1, 0, 0, 0, NULL, '2020-01-20 10:09:18', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(142, 1, 10, 245210, 'T7-S-03-BK-RFID-RO-6mA', 1, 0, 0, 0, NULL, '2020-01-20 10:09:18', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(143, 1, 6, NULL, 'T3/7-S-2', 1, 0, 0, 0, NULL, '2020-02-03 16:15:59', '2020-02-03 16:15:59', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(144, 1, 6, 245210, 'T7-S-6mA-2-LIDL', 1, 0, 0, 0, NULL, '2020-02-12 17:13:26', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(145, 1, 6, 245210, 'T7-S-03-BK-RO-6mA', 1, 0, 0, 0, NULL, '2020-02-17 12:51:32', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(146, 1, 6, 245210, 'T7-S-03-BK-6mA', 1, 0, 0, 0, NULL, '2020-02-17 12:52:40', '2020-02-17 12:52:40', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(147, 1, 6, 245210, 'T7-S-03-WH-6mA', 1, 0, 0, 0, NULL, '2020-02-19 10:31:25', '2020-02-19 10:31:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(148, 1, 6, 245210, 'T7-S-03-WH-RO-6mA', 1, 0, 0, 0, NULL, '2020-02-19 10:35:58', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(149, 1, 6, 245210, 'T22-S-03-BK-6mA', 1, 0, 0, 0, NULL, '2020-02-19 10:41:17', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(150, 1, 6, 245210, 'T22-S-03-BK-RO-6mA', 1, 0, 0, 0, NULL, '2020-02-19 10:45:23', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(151, 1, 6, 245210, 'T22-S-03-WH-TES-6mA', 1, 0, 0, 0, NULL, '2020-02-19 10:46:05', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(152, 1, 6, 245210, 'T22-S-GY-6mA-2-RO', 1, 0, 0, 0, NULL, '2020-03-04 11:41:33', '2020-03-04 11:41:33', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(153, 1, 6, 245210, 'T7-S-03-WH-TES-6mA', 1, 0, 0, 0, NULL, '2020-04-07 14:01:03', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(154, 1, 5, 100301, 'S7-UC-2-AR-6mA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2020-04-15 17:27:15', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(155, 1, 5, 100301, 'S22-UC-2-AR-6mA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2020-04-15 17:35:50', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(156, 15, 11, NULL, 'Veefil-RT TRI93-50-01-C', 1, 1, 1, 0, NULL, '2020-05-14 12:26:08', '2020-05-14 12:26:08', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(157, 1, 6, 245210, 'T3-S-03-BK-6mA', 1, 0, 0, 0, NULL, '2020-07-06 12:19:47', '2020-07-06 12:19:47', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(158, 1, 6, 245210, 'T3-S-03-BK-RO-6mA', 1, 0, 0, 0, NULL, '2020-07-06 12:22:35', '2020-07-06 12:22:35', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(159, 4, 4, NULL, 'Terra 54 CJG (22)', 1, 1, 0, 0, NULL, '2020-07-24 13:11:15', '2020-07-24 13:11:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(160, 1, 10, 245210, 'T7-S-03-WH-RFID-6mA', 1, 0, 0, 0, NULL, '2020-08-20 11:18:54', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(161, 1, 10, 245210, 'T7-S-03-WH-RFID-RO-6mA', 1, 0, 0, 0, NULL, '2020-08-20 11:19:23', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(162, 1, 6, 245210, 'T22-S-03-WH-RO-6mA', 1, 0, 0, 0, NULL, '2020-08-26 09:25:41', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(163, 1, 5, 245210, 'S3-UC-6mA-2', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2020-09-25 08:56:02', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(164, 1, 6, 245210, 'T7-S-03-WH-LIDL-6mA', 1, 0, 0, 0, NULL, '2020-10-01 09:26:17', '2021-01-06 16:01:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(165, 1, 5, 245210, 'S3-UP-6mA-2-RO', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2020-10-13 14:58:45', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(166, 1, 5, 245210, 'S7-UC-02-AAE', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(167, 1, 5, 245210, 'S7-UP-02-AAE', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(168, 1, 5, 245210, 'S7-UC-02-AAE-HYN', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(169, 1, 5, 245210, 'S7-UP-02-AAM', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(170, 1, 5, 245210, 'S22-UC-02-AAA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(171, 1, 5, 100298, 'S22-2C-02-AAA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(172, 1, 5, 245210, 'S22-UP-02-AAA', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(173, 1, 5, 245210, 'S22-UP-02-AAM', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(174, 1, 6, 245210, 'T3-S-04-AAA-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(175, 1, 6, 245210, 'T3-S-04-AAB-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(176, 1, 7, 100300, 'S7-UP-02-AAI', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(177, 1, 7, 100300, 'S22-UP-02-AAC', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(178, 1, 7, 100300, 'S7-UC-02-AAI', 0, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(179, 1, 7, 100300, 'S22-2C-02-AAC', 0, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(180, 1, 7, 100300, 'S22-UC-02-AAC', 0, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(181, 1, 6, 245210, 'T11-S-04-AAA-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(182, 1, 12, 245210, 'MT7-S-2-AAAA', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(183, 1, 5, 100298, 'S7-2C-02-AAE-VW', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(184, 1, 10, 245210, 'T7-S-04-AAC-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(185, 1, 10, 245210, 'T7-S-04-AAD-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(186, 1, 6, 245210, 'T7-S-04-AAB-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(187, 1, 6, 245210, 'T7-S-04-AAB-WHT', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(188, 1, 6, 245210, 'T22-S-04-AAA-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(189, 1, 6, 245210, 'T22-S-04-AAB-BLK', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(190, 1, 6, 245210, 'T22-S-04-AAA-TES', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(191, 1, 6, 245210, 'T7-S-04-AAA-TES', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(192, 1, 5, 100301, 'S7-UC-02-AAG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(193, 1, 5, 100301, 'S22-UC-02-AAG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-01-06 15:33:41', '2021-10-26 11:43:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(194, 1, 10, 245210, 'T7-S-04-AAC-WHT', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(195, 1, 10, 245210, 'T7-S-04-AAD-WHT', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(196, 1, 6, 245210, 'T22-S-04-AAB-WHT', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(197, 1, 6, 245210, 'T7-S-04-AAA-LIDL', 1, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(198, 1, 5, 100302, 'S7-1C-2-AAA', 0, 0, 0, 0, NULL, '2021-01-06 15:33:41', '2021-01-06 15:33:41', '2021-02-08 10:32:55');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(199, 1, 5, 245210, 'S7-UC-02-ABA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-02-08 10:37:58', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(200, 1, 5, 245210, 'S7-UC-02-ABB', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-02-08 10:41:24', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(201, 1, 5, 245210, 'S7-UP-02-ABB', 1, 0, 0, 0, NULL, '2021-02-08 10:41:24', '2021-02-08 10:41:24', '2021-05-05 09:03:09');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(202, 1, 5, 245210, 'S7-UP-02-ABF', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-02-08 10:41:24', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(203, 1, 5, 100298, 'S7-1C-02-ABB', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2021-02-08 10:41:24', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(204, 1, 5, 100298, 'S7-2C-02-ABA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2021-02-08 10:41:24', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(205, 1, 5, 100298, 'S7-2C-02-ABB', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered.svg', '2021-02-08 10:41:24', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(206, 1, 5, 245210, 'S7-UP-02-ABG', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-05-05 09:00:43', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(207, 1, 6, 245210, 'T11-S-03-BK-RO-6mA', 1, 0, 0, 0, NULL, '2021-06-25 12:55:11', '2021-06-25 12:55:11', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(208, 15, 11, NULL, 'Veefil-RT TRI93-50-01-C (2)', 1, 1, 1, 0, NULL, '2021-07-01 10:48:10', '2021-07-01 10:48:10', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(209, 1, 17, 245210, 'S22-UC-03-ACA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge.svg', '2021-07-08 14:21:06', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(210, 15, 24, NULL, 'RTM-75-C-CDCS', 1, 1, 1, 1, NULL, '2021-10-01 15:31:45', '2021-10-01 15:31:45', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(211, 1, 13, 100298, 'S7-1C-03-ABG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-13 16:13:00', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(212, 1, 13, 100298, 'S7-2C-03-ABG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(213, 1, 13, 245210, 'S7-UC-03-ABG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(214, 1, 13, 245210, 'S7-UP-03-ABG', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', '2022-07-04 11:10:07');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(215, 1, 13, 245210, 'S7-UP-03-ABF', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', '2022-07-04 11:04:49');
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(216, 1, 13, 245210, 'S7-UCB-03-ABG', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(217, 1, 13, 245210, 'S7-UPB-03-ABG', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(218, 1, 13, 245210, 'S7-UPB-03-ABF', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(219, 1, 13, 245210, 'S7-1C-03-ADA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(220, 1, 13, 245210, 'S7-2C-03-ADA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(221, 1, 13, 245210, 'S7-UC-03-ADA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(222, 1, 13, 245210, 'S7-UP-03-ADA', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(223, 1, 13, 245210, 'S7-UP-03-ADC', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(224, 1, 13, 245210, 'S7-UCB-03-ADA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(225, 1, 13, 245210, 'S7-UPB-03-ADA', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(226, 1, 13, 245210, 'S7-UPB-03-ADC', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(227, 1, 13, 100298, 'S22-2C-03-AAA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(228, 1, 13, 245210, 'S22-UC-03-AAA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(229, 1, 13, 245210, 'S22-UP-03-AAA', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(230, 1, 13, 245210, 'S22-UP-03-AAB', 1, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-v3.svg', '2021-10-13 15:35:07', '2021-10-26 11:43:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(231, 1, 17, 172160, 'S7-2C-03-ACA', 0, 0, 0, 0, 'https://d34odvqm45w0od.cloudfront.net/pod_model_svgs/home-charge-tethered-v3.svg', '2021-10-28 17:05:22', '2021-10-28 17:05:22', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(234, 4, 14, NULL, 'Terra 25 WB', 1, 1, 0, 0, NULL, '2021-10-29 14:08:25', NULL, NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(235, 1, 15, 245210, 'T7-S-06-ADA-BLK', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(236, 1, 15, 245210, 'T7-S-06-ADA-WHT', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(237, 1, 15, 245210, 'T7-S-06-ADA-TES', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(238, 1, 15, 245210, 'T7-S-06-ADA-LDL', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(239, 1, 15, 245210, 'T7-S-06-ADB-BLK', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(240, 1, 15, 245210, 'T7-S-06-ADB-WHT', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(241, 1, 15, 245210, 'T7-S-06-ADB-TES', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(242, 1, 15, 245210, 'T7-S-06-ADB-LDL', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(243, 1, 10, 245210, 'T7-S-06-ADC-BLK', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(244, 1, 10, 245210, 'T7-S-06-ADC-WHT', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(245, 1, 10, 245210, 'T7-S-06-ADD-BLK', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(246, 1, 10, 245210, 'T7-S-06-ADD-WHT', 1, 0, 0, 0, NULL, '2021-11-02 11:00:57', '2021-11-02 11:00:57', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(247, 1, 15, 245210, 'T22-S-06-AAA-BLK', 1, 0, 0, 0, NULL, '2021-11-16 16:58:43', '2021-11-16 16:58:43', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(248, 1, 15, 245210, 'T22-S-06-AAA-WHT', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(249, 1, 15, 245210, 'T22-S-06-AAA-TES', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(250, 1, 15, 245210, 'T22-S-06-AAA-LDL', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(251, 1, 15, 245210, 'T22-S-06-AAB-BLK', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(252, 1, 15, 245210, 'T22-S-06-AAB-WHT', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(253, 1, 15, 245210, 'T22-S-06-AAB-TES', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(254, 1, 15, 245210, 'T22-S-06-AAB-LDL', 1, 0, 0, 0, NULL, '2021-11-17 09:24:27', '2021-11-17 09:24:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(255, 1, 17, 172160, 'S7-1C-03-ACA', 0, 0, 0, 0, NULL, '2021-12-07 16:15:19', '2021-12-07 16:15:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(256, 1, 17, 245210, 'S7-UC-03-ACA', 0, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(257, 1, 17, 245210, 'S7-UP-03-ACA', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(258, 1, 17, 245210, 'S7-UP-03-ACC', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(259, 1, 17, 245210, 'S7-UCB-03-ACA', 0, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(260, 1, 17, 245210, 'S7-UPB-03-ACA', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(261, 1, 17, 245210, 'S7-UPB-03-ACC', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(262, 1, 17, 172160, 'S22-2C-03-ACA', 0, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(263, 1, 17, 245210, 'S22-UP-03-ACA', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(264, 1, 17, 245210, 'S22-UP-03-ACC', 1, 0, 0, 0, NULL, '2021-12-07 16:23:36', '2021-12-07 16:23:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(265, 1, 18, 245210, 'T7-S-06-ACA-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(266, 1, 18, 245210, 'T7-S-06-ACA-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(267, 1, 18, 245210, 'T7-S-06-ACA-TES', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(268, 1, 18, 245210, 'T7-S-06-ACA-LDL', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(269, 1, 18, 245210, 'T7-S-06-ACB-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(270, 1, 18, 245210, 'T7-S-06-ACB-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(271, 1, 18, 245210, 'T7-S-06-ACB-TES', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(272, 1, 18, 245210, 'T7-S-06-ACB-LDL', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(273, 1, 18, 245210, 'T22-S-06-ACA-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(274, 1, 18, 245210, 'T22-S-06-ACA-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(275, 1, 18, 245210, 'T22-S-06-ACA-TES', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(276, 1, 18, 245210, 'T22-S-06-ACA-LDL', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(277, 1, 18, 245210, 'T22-S-06-ACB-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(278, 1, 18, 245210, 'T22-S-06-ACB-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(279, 1, 18, 245210, 'T22-S-06-ACB-TES', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(280, 1, 18, 245210, 'T22-S-06-ACB-LDL', 1, 0, 0, 0, NULL, '2021-12-07 17:12:18', '2021-12-07 17:12:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(281, 1, 20, 245210, 'T7-S-06-ACC-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:13:28', '2021-12-07 17:13:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(282, 1, 20, 245210, 'T7-S-06-ACC-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:13:28', '2021-12-07 17:13:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(283, 1, 20, 245210, 'T7-S-06-ACD-BLK', 1, 0, 0, 0, NULL, '2021-12-07 17:13:28', '2021-12-07 17:13:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(284, 1, 20, 245210, 'T7-S-06-ACD-WHT', 1, 0, 0, 0, NULL, '2021-12-07 17:13:28', '2021-12-07 17:13:28', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(285, 1, 13, 245210, 'S11-UP-03-AAB', 1, 0, 0, 0, NULL, '2022-03-07 11:05:04', '2022-03-07 11:05:04', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(286, 1, 13, 245210, 'S11-UP-03-AAA', 1, 0, 0, 0, NULL, '2022-03-07 11:07:54', '2022-03-07 11:07:54', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(287, 1, 17, 172160, 'S7-1C-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(288, 1, 17, 172160, 'S7-2C-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(289, 1, 17, 245210, 'S7-UC-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(290, 1, 17, 245210, 'S7-UP-03-AEA', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(291, 1, 17, 245210, 'S7-UP-03-AEC', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(292, 1, 17, 245210, 'S7-UCB-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(293, 1, 17, 245210, 'S7-UPB-03-AEA', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(294, 1, 17, 245210, 'S7-UPB-03-AEC', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(295, 1, 17, 172160, 'S22-2C-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(296, 1, 17, 245210, 'S22-UC-03-AEA', 0, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(297, 1, 17, 245210, 'S22-UP-03-AEA', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(298, 1, 17, 245210, 'S22-UP-03-AEC', 1, 0, 0, 0, NULL, '2022-04-08 14:57:26', '2022-04-08 14:57:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(301, 1, 18, 245210, 'T7-S-06-AFA-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(302, 1, 18, 245210, 'T7-S-06-AFA-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(303, 1, 18, 245210, 'T7-S-06-AFA-TES', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(304, 1, 18, 245210, 'T7-S-06-AFA-LDL', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(305, 1, 20, 245210, 'T7-S-06-AFC-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(306, 1, 20, 245210, 'T7-S-06-AFC-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(307, 1, 18, 245210, 'T7-S-06-AFB-TES', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(308, 1, 18, 245210, 'T7-S-06-AFB-LDL', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(309, 1, 20, 245210, 'T7-S-06-AFD-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(310, 1, 20, 245210, 'T7-S-06-AFD-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(311, 1, 18, 245210, 'T7-S-06-AFB-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(312, 1, 18, 245210, 'T7-S-06-AFB-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(313, 1, 18, 245210, 'T22-S-06-AFA-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(314, 1, 18, 245210, 'T22-S-06-AFA-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(315, 1, 18, 245210, 'T22-S-06-AFA-TES', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(316, 1, 18, 245210, 'T22-S-06-AFA-LDL', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(317, 1, 18, 245210, 'T22-S-06-AFB-BLK', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(318, 1, 18, 245210, 'T22-S-06-AFB-WHT', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(319, 1, 18, 245210, 'T22-S-06-AFB-TES', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(320, 1, 18, 245210, 'T22-S-06-AFB-LDL', 1, 0, 0, 0, NULL, '2022-04-28 15:40:15', '2022-04-28 15:40:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(321, 1, 17, 172160, 'S7-1C-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(322, 1, 17, 172160, 'S7-2C-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(323, 1, 17, 245210, 'S7-UC-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(324, 1, 17, 245210, 'S7-UP-03-AFA', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(325, 1, 17, 245210, 'S7-UP-03-AFC', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(326, 1, 17, 245210, 'S7-UCB-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(327, 1, 17, 245210, 'S7-UPB-03-AFA', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(328, 1, 17, 245210, 'S7-UPB-03-AFC', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(329, 1, 17, 172160, 'S22-2C-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(330, 1, 17, 245210, 'S22-UC-03-AFA', 0, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(331, 1, 17, 245210, 'S22-UP-03-AFA', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(332, 1, 17, 245210, 'S22-UP-03-AFC', 1, 0, 0, 0, NULL, '2022-05-09 13:04:36', '2022-05-09 13:04:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(333, 4, 22, NULL, 'Terra 124 CC', 1, 1, 0, 1, NULL, '2022-05-19 13:35:06', '2022-05-19 13:35:06', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(334, 15, 24, NULL, 'RTM-75-C-CSCS', 1, 1, 1, 1, NULL, '2022-05-27 14:27:35', '2022-05-27 14:27:35', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(335, 15, 25, NULL, 'RTM-50-C-CDCS', 1, 1, 1, 1, NULL, '2022-05-27 14:34:43', '2022-05-27 14:34:43', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(336, 15, 25, NULL, 'RTM-50-C-CSCS', 1, 1, 1, 1, NULL, '2022-05-27 14:39:41', '2022-05-27 14:39:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(337, 15, 26, NULL, 'RTM-50-CDCS', 1, 1, 0, 0, NULL, '2022-05-27 14:47:18', '2022-05-27 14:47:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(338, 15, 26, NULL, 'RTM-50-CSCS', 1, 1, 0, 0, NULL, '2022-05-27 14:54:56', '2022-05-27 14:54:56', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(339, 1, 5, 245210, 'S3-UP-02-ABF', 1, 0, 0, 0, NULL, '2022-06-09 15:15:33', '2022-06-09 15:15:33', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(340, 1, 17, 245210, 'S3-UP-03-ACA', 1, 0, 0, 0, NULL, '2022-06-17 13:41:53', '2022-06-17 13:41:53', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(341, 1, 17, 245210, 'S3-UP-03-ACC', 1, 0, 0, 0, NULL, '2022-06-17 13:42:20', '2022-06-17 13:42:20', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(342, 1, 13, 245210, 'S3-UP-03-ADC', 1, 0, 0, 0, NULL, '2022-07-06 13:27:15', '2022-07-06 13:27:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(343, 1, 15, 245210, 'T3-S-06-ADA-BLK', 1, 0, 0, 0, NULL, '2022-07-07 09:16:09', '2022-07-07 09:16:09', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(344, 1, 15, 245210, 'T3-S-06-ADB-BLK', 1, 0, 0, 0, NULL, '2022-07-07 08:16:27', '2022-07-07 08:16:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(345, 1, 18, 245210, 'T3-S-06-ACA-BLK', 1, 0, 0, 0, NULL, '2022-07-19 12:57:21', '2022-07-19 12:57:21', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(346, 1, 18, 245210, 'T3-S-06-ACB-BLK', 1, 0, 0, 0, NULL, '2022-07-19 13:01:17', '2022-07-19 13:01:17', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(347, 1, 17, 172160, 'S7-1C-03-AGA', 0, 0, 0, 0, NULL, '2022-09-07 09:46:38', '2022-09-07 09:46:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(348, 1, 17, 172160, 'S7-2C-03-AGA', 0, 0, 0, 0, NULL, '2022-09-07 08:47:50', '2022-09-07 08:47:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(349, 1, 17, 245210, 'S7-UC-03-AGA', 0, 0, 0, 0, NULL, '2022-09-07 08:49:26', '2022-09-07 08:49:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(350, 1, 17, 245210, 'S7-UP-03-AGA', 1, 0, 0, 0, NULL, '2022-09-07 08:50:59', '2022-09-07 08:50:59', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(351, 1, 17, 245210, 'S7-UP-03-AGC', 1, 0, 0, 0, NULL, '2022-09-07 08:51:15', '2022-09-07 08:51:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(352, 1, 17, 245210, 'S7-UCB-03-AGA', 0, 0, 0, 0, NULL, '2022-09-07 08:52:53', '2022-09-07 08:52:53', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(353, 1, 17, 245210, 'S7-UPB-03-AGA', 1, 0, 0, 0, NULL, '2022-09-07 08:54:04', '2022-09-07 08:54:04', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(354, 1, 17, 245210, 'S7-UPB-03-AGC', 1, 0, 0, 0, NULL, '2022-09-07 08:54:04', '2022-09-07 08:54:04', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(355, 1, 17, 172160, 'S7-1C-03-AHA', 0, 0, 0, 0, NULL, '2022-09-26 15:12:42', '2022-09-26 15:12:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(356, 1, 17, 172160, 'S7-2C-03-AHA', 0, 0, 0, 0, NULL, '2022-09-26 15:15:04', '2022-09-26 15:15:04', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(357, 1, 17, 245210, 'S7-UC-03-AHA', 0, 0, 0, 0, NULL, '2022-09-26 14:20:37', '2022-09-26 14:20:37', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(358, 1, 17, 245210, 'S7-UCB-03-AHA', 0, 0, 0, 0, NULL, '2022-09-26 14:21:13', '2022-09-26 14:21:13', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(359, 1, 17, 245210, 'S7-UP-03-AHA', 1, 0, 0, 0, NULL, '2022-09-26 14:23:38', '2022-09-26 14:23:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(360, 1, 17, 245210, 'S7-UPB-03-AHA', 1, 0, 0, 0, NULL, '2022-09-26 14:24:06', '2022-09-26 14:24:06', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(361, 1, 18, 245210, 'T7-S-06-AIA-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(362, 1, 18, 245210, 'T7-S-06-AIA-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(363, 1, 18, 245210, 'T7-S-06-AIA-TES', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(364, 1, 18, 245210, 'T7-S-06-AIA-LDL', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(365, 1, 18, 245210, 'T7-S-06-AIB-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(366, 1, 18, 245210, 'T7-S-06-AIB-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(367, 1, 18, 245210, 'T7-S-06-AIB-TES', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(368, 1, 18, 245210, 'T7-S-06-AIB-LDL', 1, 0, 0, 0, NULL, '2022-09-26 15:02:34', '2022-09-26 15:02:34', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(369, 1, 20, 245210, 'T7-S-06-AIC-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:11:38', '2022-09-26 15:11:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(370, 1, 20, 245210, 'T7-S-06-AIC-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:11:38', '2022-09-26 15:11:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(371, 1, 20, 245210, 'T7-S-06-AID-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:11:38', '2022-09-26 15:11:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(372, 1, 20, 245210, 'T7-S-06-AID-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:11:38', '2022-09-26 15:11:38', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(373, 1, 18, 245210, 'T22-S-06-AIA-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(374, 1, 18, 245210, 'T22-S-06-AIA-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(375, 1, 18, 245210, 'T22-S-06-AIA-TES', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(376, 1, 18, 245210, 'T22-S-06-AIA-LDL', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(377, 1, 18, 245210, 'T22-S-06-AIB-BLK', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(378, 1, 18, 245210, 'T22-S-06-AIB-WHT', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(379, 1, 18, 245210, 'T22-S-06-AIB-TES', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(380, 1, 18, 245210, 'T22-S-06-AIB-LDL', 1, 0, 0, 0, NULL, '2022-09-26 15:13:31', '2022-09-26 15:13:31', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(382, 1, 17, 245210, 'S11-UP-03-ACA', 1, 0, 0, 0, NULL, '2022-09-29 16:05:45', '2022-09-29 16:05:45', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(383, 1, 17, 245210, 'S11-UP-03-ACC', 1, 0, 0, 0, NULL, '2022-09-29 16:06:11', '2022-09-29 16:06:11', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(384, 4, 23, NULL, 'Terra 60L CE', 1, 1, 0, 1, NULL, '2022-11-01 13:11:12', '2022-11-01 13:11:12', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(386, 1, 17, 172160, 'S22-2C-03-AGA', 0, 0, 0, 0, NULL, '2022-11-08 16:25:53', '2022-11-08 16:25:53', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(387, 1, 17, 245210, 'S22-UC-03-AGA', 0, 0, 0, 0, NULL, '2022-11-08 16:29:52', '2022-11-08 16:29:52', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(388, 1, 17, 245210, 'S22-UP-03-AGA', 1, 0, 0, 0, NULL, '2022-11-08 16:32:00', '2022-11-08 16:32:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(389, 1, 17, 245210, 'S22-UP-03-AGC', 1, 0, 0, 0, NULL, '2022-11-08 16:32:21', '2022-11-08 16:32:21', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(390, 1, 18, 245210, 'T11-S-06-ACA-BLK', 1, 0, 0, 0, NULL, '2023-01-31 09:38:43', '2023-01-31 09:38:43', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(391, 1, 18, 245210, 'T11-S-06-ACB-BLK', 1, 0, 0, 0, NULL, '2023-01-31 09:39:47', '2023-01-31 09:39:47', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(392, 1, 18, 245210, 'T7-S-06-AIA-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:41:35', '2023-02-17 15:41:35', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(393, 1, 18, 245210, 'T7-S-06-AIB-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:44:50', '2023-02-17 15:44:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(394, 1, 18, 245210, 'T7-S-06-AIC-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:44:50', '2023-02-17 15:44:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(395, 1, 18, 245210, 'T7-S-06-AID-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:44:50', '2023-02-17 15:44:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(396, 1, 18, 245210, 'T22-S-06-AIA-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:44:50', '2023-02-17 15:44:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(397, 1, 18, 245210, 'T22-S-06-AIB-BMW', 1, 0, 0, 0, NULL, '2023-02-17 15:44:50', '2023-02-17 15:44:50', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(398, 15, 28, NULL, 'PKM-300-R', 1, 1, 1, 1, NULL, '2023-03-02 09:45:56', '2023-03-02 09:45:56', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(399, 15, 27, NULL, 'PKM-150-C-CSCS', 1, 1, 1, 1, NULL, '2023-03-02 09:46:44', '2023-03-02 09:46:44', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(400, 15, 27, NULL, 'PKM-100-C-CSCS', 1, 1, 1, 1, NULL, '2023-03-02 09:47:19', '2023-03-02 09:47:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(401, 1, 18, 245210, 'T11-S-06-AIA-BLK', 1, 0, 0, 0, NULL, '2023-05-10 13:43:29', '2023-05-10 13:43:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(402, 1, 18, 245210, 'T11-S-06-AIB-BLK', 1, 0, 0, 0, NULL, '2023-05-10 13:46:26', '2023-05-10 13:46:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(403, 1, 17, 245210, 'S11-UP-03-AGC', 1, 0, 0, 0, NULL, '2023-05-12 14:51:06', '2023-05-12 14:51:06', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(404, 1, 17, 172160, 'S7-1C-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:20:13', '2023-06-08 14:20:13', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(405, 1, 17, 172160, 'S7-2C-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:26:58', '2023-06-08 14:26:58', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(406, 1, 17, 245210, 'S7-UC-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:31:26', '2023-06-08 14:31:26', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(407, 1, 17, 245210, 'S7-UP-03-AJA', 1, 0, 0, 0, NULL, '2023-06-08 14:35:03', '2023-06-08 14:35:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(408, 1, 17, 245210, 'S7-UP-03-AJC', 1, 0, 0, 0, NULL, '2023-06-08 14:35:17', '2023-06-08 14:35:17', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(409, 1, 17, 245210, 'S7-UCB-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:39:10', '2023-06-08 14:39:10', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(410, 1, 17, 245210, 'S7-UPB-03-AJA', 1, 0, 0, 0, NULL, '2023-06-08 14:41:15', '2023-06-08 14:41:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(411, 1, 17, 245210, 'S7-UPB-03-AJC', 1, 0, 0, 0, NULL, '2023-06-08 14:41:27', '2023-06-08 14:41:27', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(412, 1, 17, 172160, 'S22-2C-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:46:47', '2023-06-08 14:46:47', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(413, 1, 17, 245210, 'S22-UC-03-AJA', 0, 0, 0, 0, NULL, '2023-06-08 14:52:55', '2023-06-08 14:52:55', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(414, 1, 17, 245210, 'S22-UP-03-AJA', 1, 0, 0, 0, NULL, '2023-06-08 14:58:05', '2023-06-08 14:58:05', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(415, 1, 17, 245210, 'S22-UP-03-AJC', 1, 0, 0, 0, NULL, '2023-06-08 14:58:18', '2023-06-08 14:58:18', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(416, 1, 18, 245210, 'T7-S-06-AJA-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(417, 1, 18, 245210, 'T7-S-06-AJA-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(418, 1, 18, 245210, 'T7-S-06-AJA-LDL', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(419, 1, 18, 245210, 'T7-S-06-AJA-TES', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(420, 1, 18, 245210, 'T7-S-06-AJA-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(421, 1, 18, 245210, 'T7-S-06-AJB-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(422, 1, 18, 245210, 'T7-S-06-AJB-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(423, 1, 18, 245210, 'T7-S-06-AJB-LDL', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(424, 1, 18, 245210, 'T7-S-06-AJB-TES', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(425, 1, 18, 245210, 'T7-S-06-AJB-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(426, 1, 20, 245210, 'T7-S-06-AJC-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(427, 1, 20, 245210, 'T7-S-06-AJC-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(428, 1, 20, 245210, 'T7-S-06-AJC-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(429, 1, 20, 245210, 'T7-S-06-AJD-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(430, 1, 20, 245210, 'T7-S-06-AJD-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(431, 1, 20, 245210, 'T7-S-06-AJD-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:23:29', '2023-06-08 15:23:29', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(432, 1, 18, 245210, 'T22-S-06-AJA-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(433, 1, 18, 245210, 'T22-S-06-AJA-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(434, 1, 18, 245210, 'T22-S-06-AJA-LDL', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(435, 1, 18, 245210, 'T22-S-06-AJA-TES', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(436, 1, 18, 245210, 'T22-S-06-AJA-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(437, 1, 18, 245210, 'T22-S-06-AJB-BLK', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(438, 1, 18, 245210, 'T22-S-06-AJB-BMW', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(439, 1, 18, 245210, 'T22-S-06-AJB-LDL', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(440, 1, 18, 245210, 'T22-S-06-AJB-TES', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(441, 1, 18, 245210, 'T22-S-06-AJB-WHT', 1, 0, 0, 0, NULL, '2023-06-08 15:39:30', '2023-06-08 15:39:30', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(442, 1, 17, 245210, 'S11-UP-03-AGA', 1, 0, 0, 0, NULL, '2023-06-13 09:54:53', '2023-06-13 09:54:53', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(443, 1, 5, 100298, 'S7-2C-6mA-2-MTD', 1, 0, 0, 0, NULL, '2023-09-19 14:47:03', '2023-09-19 14:47:03', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(444, 1, 5, 100298, 'S7-2C-03-AFA-MTD', 1, 0, 0, 0, NULL, '2023-10-24 13:14:11', '2023-10-24 13:14:11', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(445, 1, 6, 245210, 'T7/3-S-03-BK-RO-6mA', 1, 0, 0, 0, NULL, '2023-10-31 10:22:21', '2023-10-31 10:22:21', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(446, 1, 29, NULL, 'S7-2C-05-AKA-UK-0001', 0, 1, 0, 0, NULL, '2023-11-10 11:08:42', '2023-11-10 11:08:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(447, 1, 29, NULL, 'S7-2C-05-AKB-UK-0001', 0, 1, 0, 0, NULL, '2023-11-10 11:08:42', '2023-11-10 11:08:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(448, 1, 29, NULL, 'S7-UC-05-AKA-UK-0001', 0, 1, 0, 0, NULL, '2023-11-10 11:08:42', '2023-11-10 11:08:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(449, 1, 29, NULL, 'S7-UCB-05-AKA-UK-0001', 0, 1, 0, 0, NULL, '2023-11-10 11:08:42', '2023-11-10 11:08:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(450, 1, 17, 245210, 'S22-UC-05-TEST', 0, 0, 0, 0, NULL, '2023-12-05 12:38:40', '2023-12-05 12:38:40', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(451, 1, 18, 245210, 'T7-S-06-AJA-CUS', 1, 0, 0, 0, NULL, '2023-12-13 15:04:00', '2023-12-13 15:04:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(452, 1, 18, 245210, 'T7-S-06-AJB-CUS', 1, 0, 0, 0, NULL, '2023-12-13 15:04:00', '2023-12-13 15:04:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(453, 1, 18, 245210, 'T7-S-06-AJA-CUSW', 1, 0, 0, 0, NULL, '2023-12-13 15:04:00', '2023-12-13 15:04:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(454, 1, 18, 245210, 'T7-S-06-AJB-CUSW', 1, 0, 0, 0, NULL, '2023-12-13 15:04:00', '2023-12-13 15:04:00', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(455, 1, 17, 245210, 'S3-UPB-03-ACA', 1, 0, 0, 0, NULL, '2024-01-10 18:41:49', '2024-01-10 18:41:49', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(456, 1, 18, 245210, 'T3-S-06-AIB-BMW', 1, 0, 0, 0, NULL, '2024-04-09 12:51:22', '2024-04-09 12:51:22', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(457, 1, 18, 245210, 'T22-S-06-AJA-CUS', 1, 0, 0, 0, NULL, '2024-04-10 14:56:25', '2024-04-10 14:56:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(458, 1, 18, 245210, 'T22-S-06-AJB-CUS', 1, 0, 0, 0, NULL, '2024-04-10 14:56:25', '2024-04-10 14:56:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(459, 1, 18, 245210, 'T22-S-06-AJA-CUSW', 1, 0, 0, 0, NULL, '2024-04-10 14:56:25', '2024-04-10 14:56:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(460, 1, 18, 245210, 'T22-S-06-AJB-CUSW', 1, 0, 0, 0, NULL, '2024-04-10 14:56:25', '2024-04-10 14:56:25', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(461, 1, 17, 245210, 'S3-UP-03-AGC', 1, 0, 0, 0, NULL, '2024-04-16 12:12:14', '2024-04-16 12:12:14', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(462, 1, 18, 245210, 'T22-S-07-ALA-BLK', 1, 1, 0, 0, NULL, '2024-05-31 10:10:19', '2024-05-31 10:10:22', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(463, 1, 18, 245210, 'T22-S-07-ALB-BLK', 1, 1, 0, 0, NULL, '2024-05-31 10:10:19', '2024-05-31 10:10:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(464, 1, 18, 245210, 'T7-S-07-ALA-BLK', 1, 1, 0, 0, NULL, '2024-05-31 10:10:19', '2024-05-31 10:10:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(465, 1, 18, 245210, 'T7-S-07-ALB-BLK', 1, 1, 0, 0, NULL, '2024-05-31 10:10:19', '2024-05-31 10:10:19', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(466, 1, 31, NULL, 'T7-S-07-AMB-BLK', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(467, 1, 31, NULL, 'T7-S-07-AMB-LDL', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(468, 1, 31, NULL, 'T7-S-07-AMB-TES', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(469, 1, 31, NULL, 'T7-S-07-AMB-CUS', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(470, 1, 31, NULL, 'T7-S-07-AMC-BLK', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(471, 1, 31, NULL, 'T7-S-07-AMC-LDL', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(472, 1, 31, NULL, 'T7-S-07-AMC-TES', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(473, 1, 31, NULL, 'T7-S-07-AMC-CUS', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(474, 1, 31, NULL, 'T22-S-07-AMB-BLK', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(475, 1, 31, NULL, 'T22-S-07-AMB-TES', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(476, 1, 31, NULL, 'T22-S-07-AMB-CUS', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(477, 1, 31, NULL, 'T22-S-07-AMC-BLK', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(478, 1, 31, NULL, 'T22-S-07-AMC-TES', 1, 1, 0, 1, NULL, '2024-06-11 15:14:42', '2024-06-11 15:14:42', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(479, 1, 29, NULL, 'S7-UC-05-AMF-FR-0002', 0, 1, 0, 0, NULL, '2024-06-18 11:11:36', '2024-06-18 11:11:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(480, 1, 29, NULL, 'S22-UC-05-AMF-FR-0002', 0, 1, 0, 0, NULL, '2024-06-18 11:11:36', '2024-06-18 11:11:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(481, 1, 29, NULL, 'S7-UC-05-AMF-ES-0002', 0, 1, 0, 0, NULL, '2024-06-18 11:11:36', '2024-06-18 11:11:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(482, 1, 29, NULL, 'S22-UC-05-AMF-ES-0002', 0, 1, 0, 0, NULL, '2024-06-18 11:11:36', '2024-06-18 11:11:36', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(483, 1, 29, NULL, 'S7-UC-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(484, 1, 29, NULL, 'S22-UC-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(485, 1, 29, NULL, 'S7-UCB-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(486, 1, 29, NULL, 'S22-UCB-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(487, 1, 29, NULL, 'S7-UP-05-AMB-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(488, 1, 29, NULL, 'S22-UP-05-AMB-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(489, 1, 29, NULL, 'S7-UP-05-AMC-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(490, 1, 29, NULL, 'S22-UP-05-AMC-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(491, 1, 29, NULL, 'S7-UP-05-AMD-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(492, 1, 29, NULL, 'S22-UP-05-AMD-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(493, 1, 29, NULL, 'S7-UP-05-AME-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(494, 1, 29, NULL, 'S22-UP-05-AME-UK-0001', 1, 1, 0, 0, NULL, '2024-06-24 16:06:02', '2024-06-24 16:06:02', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(495, 1, 29, NULL, 'S7-UC-05-AMF-FR-0003', 0, 1, 0, 0, NULL, '2024-10-22 10:57:37', '2024-10-22 10:57:37', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(496, 1, 29, NULL, 'S7-2C-05-AMF-FR-0002', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(497, 1, 29, NULL, 'S22-2C-05-AMF-FR-0002', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(498, 1, 29, NULL, 'S7-2C-05-AMF-ES-0002', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(499, 1, 29, NULL, 'S22-2C-05-AMF-ES-0002', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(500, 1, 29, NULL, 'S7-2C-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(501, 1, 29, NULL, 'S22-2C-05-AMA-UK-0001', 0, 1, 0, 0, NULL, '2024-10-29 15:42:15', '2024-10-29 15:42:15', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(502, 1, 29, NULL, 'S7-UP-05-AMG-UK-0001', 0, 1, 0, 0, NULL, '2024-11-12 16:29:41', '2024-11-12 16:29:41', NULL);
INSERT INTO podpoint.pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(503, 1, 29, NULL, 'S22-UP-05-AMG-UK-0001', 0, 1, 0, 0, NULL, '2024-11-12 16:29:41', '2024-11-12 16:29:41', NULL);
