-- select * from pcb_configurations where id in (select pcb_configuration_id from pod_models)
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(100298, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, '2021-01-06 15:33:39', '2021-01-06 15:33:39', 1);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(100299, NULL, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2021-01-06 15:33:39', '2021-01-06 15:33:39', 0);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(100300, NULL, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, '2021-01-06 15:33:39', '2021-01-06 15:33:39', 0);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(100301, NULL, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, '2021-01-06 15:33:39', '2021-01-06 15:33:39', 1);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(100302, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2021-01-06 15:33:39', '2021-01-06 15:33:39', 1);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(132159, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, '2021-07-15 14:26:13', '2021-07-15 14:26:13', 0);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(172160, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, '2021-12-16 11:41:11', '2021-12-16 11:41:11', 0);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, created_at, updated_at, is_voltage_threshold_reduced)
VALUES(462956, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, '2021-12-16 11:41:11', '2021-12-16 11:41:11', 0);
INSERT INTO podpoint.pcb_configurations(id, pcb_id, is_renault_unit, array_on, is_smart_unit, use_staging_url, maximum_supply, power_rating, log_resolution, pv_solar_system, is_monitoring_enabled, dip5, six_ma_detected, hphc, is_uplift_max_overcurrent_enabled, is_diagnostic_mode_enabled, is_setup_mode_enabled, is_it_system, is_test_mode_disabled, grace_period, grace_power, no_comms_power, cpwl, created_at, updated_at, is_voltage_threshold_reduced, fast_meter_period)
VALUES(245210, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 5, 32, 32, 0, '2022-10-31 17:57:41', '2022-10-31 17:57:41', 1, 0);
