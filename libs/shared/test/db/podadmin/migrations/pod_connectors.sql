INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, `current`, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(56, 69, 1, 2, 1, 7, 32, 230, NULL, NULL, 0, '2016-12-08 18:04:36', '2016-12-08 18:04:36', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, `current`, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(57, 69, 2, 2, 1, 7, 32, 230, NULL, NULL, 0, '2016-12-08 18:05:02', '2016-12-08 18:05:02', NULL);
INSERT INTO podpoint.pod_connectors (id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(62, 75, 1, 2, 2, 22, 32, 400, NULL, NULL, 0, '2017-04-06 09:07:15', NULL, NULL);
INSERT INTO podpoint.pod_connectors (id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(63, 75, 2, 5, 3, 50, 125, 0, 50, 500, 1, '2017-04-06 09:09:24', NULL, NULL);
INSERT INTO podpoint.pod_connectors (id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(64, 75, 3, 3, 3, 50, 125, 0, 50, 500, 1, '2017-04-06 09:13:55', NULL, NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(67, 77, 1, 2, 1, 3, 16, 230, NULL, NULL, 0, '2017-05-18 16:16:37', '2017-05-18 16:16:37', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(68, 77, 2, 2, 1, 3, 16, 230, NULL, NULL, 0, '2017-05-18 16:17:09', '2017-05-18 16:17:09', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(150, 132, 1, 2, 1, 7, 32, 230, NULL, NULL, 0, '2019-07-24 13:00:35', '2019-07-24 13:00:35', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(151, 132, 2, 2, 1, 7, 32, 230, NULL, NULL, 0, '2019-07-24 13:00:35', '2019-07-24 13:00:35', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(188, 153, 1, 2, 1, 7, 32, 230, NULL, NULL, 0, '2020-04-07 13:02:18', '2020-04-07 13:02:18', NULL);
INSERT INTO podpoint.pod_connectors(id, model_id, door_id, socket_id, charge_method_id, power, current, voltage, voltage_min, voltage_max, has_tethered_cable, created_at, updated_at, deleted_at)
VALUES(189, 153, 2, 2, 1, 7, 32, 230, NULL, NULL, 0, '2020-04-07 13:02:18', '2020-04-07 13:02:18', NULL);
