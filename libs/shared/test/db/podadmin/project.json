{"name": "shared-test-db-podadmin", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/test/db/podadmin", "projectType": "library", "tags": ["shared"], "targets": {"compose": {"executor": "nx:run-commands", "options": {"cwd": "libs/shared/test/db/podadmin"}, "configurations": {"up": {"command": "docker compose up --detach --wait"}, "down": {"command": "docker compose down"}, "remove": {"command": "docker compose rm --force --stop"}, "recreate": {"command": "docker compose up --detach --force-recreate --renew-anon-volumes  --wait"}}}}}