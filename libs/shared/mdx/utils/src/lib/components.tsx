import {
  Anchor,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { MDXComponents } from 'mdx/types';
import React from 'react';

const convertHeadingToId = (children: React.ReactNode): string | undefined =>
  typeof children === 'string'
    ? children.replace(' ', '-').toLowerCase()
    : undefined;

const a = ({
  children,
  href,
}: {
  children?: React.ReactNode;
  href?: string;
}) => <Anchor href={href as string}>{children}</Anchor>;

const h1 = ({ children }: { children?: React.ReactNode }) => (
  <>
    <div id={convertHeadingToId(children)}></div>
    <Heading.H1>{children}</Heading.H1>
  </>
);

const h2 = ({ children }: { children?: React.ReactNode }) => (
  <>
    <div id={convertHeadingToId(children)}></div>
    <Heading.H2 fontSize={HeadingSizes.S} className="font-semibold">
      {children}
    </Heading.H2>
  </>
);

const h3 = ({ children }: { children?: React.ReactNode }) => (
  <>
    <div id={convertHeadingToId(children)}></div>
    <Heading.H3>{children}</Heading.H3>
  </>
);

const h4 = ({ children }: { children?: React.ReactNode }) => (
  <>
    <div id={convertHeadingToId(children)}></div>
    <Heading.H4>{children}</Heading.H4>
  </>
);

const h5 = ({ children }: { children?: React.ReactNode }) => (
  <>
    <div id={convertHeadingToId(children)}></div>
    <Heading.H5>{children}</Heading.H5>
  </>
);

const ol = ({ children }: { children?: React.ReactNode }) => (
  <ol className="list-decimal list-inside">{children}</ol>
);

const ul = ({ children }: { children?: React.ReactNode }) => (
  <ul className="list-disc list-inside">{children}</ul>
);

export const components: MDXComponents = {
  a: a,
  h1: h1,
  h2: h2,
  h3: h3,
  h4: h4,
  h5: h5,
  ol,
  p: Paragraph,
  ul,
};
