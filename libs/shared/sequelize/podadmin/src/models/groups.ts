import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Charges, ChargesId } from './charges';
import type { EvDriverDomains, EvDriverDomainsId } from './ev-driver-domains';
import type { EvDrivers, EvDriversId } from './ev-drivers';
import type { GroupLocation, GroupLocationId } from './group-location';
import type { GroupTypes, GroupTypesId } from './group-types';
import type { Members, MembersId } from './members';
import type { PodAddresses, PodAddressesId } from './pod-addresses';
import type { PodLocations, PodLocationsId } from './pod-locations';
import type { RevenueProfiles, RevenueProfilesId } from './revenue-profiles';
import type { Users, UsersId } from './users';

export interface GroupsAttributes {
  id: number;
  uid: string;
  typeId: number;
  ownerUserId?: number;
  name: string;
  contactName: string;
  phone: string;
  businessName: string;
  line1: string;
  line2: string;
  postalTown: string;
  postcode: string;
  country: string;
  feePercentage: number;
  createdAt: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export type GroupsPk = 'id';
export type GroupsId = Groups[GroupsPk];
export type GroupsOptionalAttributes =
  | 'id'
  | 'ownerUserId'
  | 'feePercentage'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type GroupsCreationAttributes = Optional<
  GroupsAttributes,
  GroupsOptionalAttributes
>;

export class Groups
  extends Model<GroupsAttributes, GroupsCreationAttributes>
  implements GroupsAttributes
{
  id!: number;
  uid!: string;
  typeId!: number;
  ownerUserId?: number;
  name!: string;
  contactName!: string;
  phone!: string;
  businessName!: string;
  line1!: string;
  line2!: string;
  postalTown!: string;
  postcode!: string;
  country!: string;
  feePercentage!: number;
  createdAt!: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  // Groups belongsTo GroupTypes via typeId
  type!: GroupTypes;
  getType!: Sequelize.BelongsToGetAssociationMixin<GroupTypes>;
  setType!: Sequelize.BelongsToSetAssociationMixin<GroupTypes, GroupTypesId>;
  createType!: Sequelize.BelongsToCreateAssociationMixin<GroupTypes>;
  // Groups hasMany Charges via groupId
  charges!: Charges[];
  getCharges!: Sequelize.HasManyGetAssociationsMixin<Charges>;
  setCharges!: Sequelize.HasManySetAssociationsMixin<Charges, ChargesId>;
  addCharge!: Sequelize.HasManyAddAssociationMixin<Charges, ChargesId>;
  addCharges!: Sequelize.HasManyAddAssociationsMixin<Charges, ChargesId>;
  createCharge!: Sequelize.HasManyCreateAssociationMixin<Charges>;
  removeCharge!: Sequelize.HasManyRemoveAssociationMixin<Charges, ChargesId>;
  removeCharges!: Sequelize.HasManyRemoveAssociationsMixin<Charges, ChargesId>;
  hasCharge!: Sequelize.HasManyHasAssociationMixin<Charges, ChargesId>;
  hasCharges!: Sequelize.HasManyHasAssociationsMixin<Charges, ChargesId>;
  countCharges!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany EvDriverDomains via groupId
  evDriverDomains!: EvDriverDomains[];
  getEvDriverDomains!: Sequelize.HasManyGetAssociationsMixin<EvDriverDomains>;
  setEvDriverDomains!: Sequelize.HasManySetAssociationsMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  addEvDriverDomain!: Sequelize.HasManyAddAssociationMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  addEvDriverDomains!: Sequelize.HasManyAddAssociationsMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  createEvDriverDomain!: Sequelize.HasManyCreateAssociationMixin<EvDriverDomains>;
  removeEvDriverDomain!: Sequelize.HasManyRemoveAssociationMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  removeEvDriverDomains!: Sequelize.HasManyRemoveAssociationsMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  hasEvDriverDomain!: Sequelize.HasManyHasAssociationMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  hasEvDriverDomains!: Sequelize.HasManyHasAssociationsMixin<
    EvDriverDomains,
    EvDriverDomainsId
  >;
  countEvDriverDomains!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany EvDrivers via groupId
  evDrivers!: EvDrivers[];
  getEvDrivers!: Sequelize.HasManyGetAssociationsMixin<EvDrivers>;
  setEvDrivers!: Sequelize.HasManySetAssociationsMixin<EvDrivers, EvDriversId>;
  addEvDriver!: Sequelize.HasManyAddAssociationMixin<EvDrivers, EvDriversId>;
  addEvDrivers!: Sequelize.HasManyAddAssociationsMixin<EvDrivers, EvDriversId>;
  createEvDriver!: Sequelize.HasManyCreateAssociationMixin<EvDrivers>;
  removeEvDriver!: Sequelize.HasManyRemoveAssociationMixin<
    EvDrivers,
    EvDriversId
  >;
  removeEvDrivers!: Sequelize.HasManyRemoveAssociationsMixin<
    EvDrivers,
    EvDriversId
  >;
  hasEvDriver!: Sequelize.HasManyHasAssociationMixin<EvDrivers, EvDriversId>;
  hasEvDrivers!: Sequelize.HasManyHasAssociationsMixin<EvDrivers, EvDriversId>;
  countEvDrivers!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany GroupLocation via groupId
  groupLocations!: GroupLocation[];
  getGroupLocations!: Sequelize.HasManyGetAssociationsMixin<GroupLocation>;
  setGroupLocations!: Sequelize.HasManySetAssociationsMixin<
    GroupLocation,
    GroupLocationId
  >;
  addGroupLocation!: Sequelize.HasManyAddAssociationMixin<
    GroupLocation,
    GroupLocationId
  >;
  addGroupLocations!: Sequelize.HasManyAddAssociationsMixin<
    GroupLocation,
    GroupLocationId
  >;
  createGroupLocation!: Sequelize.HasManyCreateAssociationMixin<GroupLocation>;
  removeGroupLocation!: Sequelize.HasManyRemoveAssociationMixin<
    GroupLocation,
    GroupLocationId
  >;
  removeGroupLocations!: Sequelize.HasManyRemoveAssociationsMixin<
    GroupLocation,
    GroupLocationId
  >;
  hasGroupLocation!: Sequelize.HasManyHasAssociationMixin<
    GroupLocation,
    GroupLocationId
  >;
  hasGroupLocations!: Sequelize.HasManyHasAssociationsMixin<
    GroupLocation,
    GroupLocationId
  >;
  countGroupLocations!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany Members via groupId
  members!: Members[];
  getMembers!: Sequelize.HasManyGetAssociationsMixin<Members>;
  setMembers!: Sequelize.HasManySetAssociationsMixin<Members, MembersId>;
  addMember!: Sequelize.HasManyAddAssociationMixin<Members, MembersId>;
  addMembers!: Sequelize.HasManyAddAssociationsMixin<Members, MembersId>;
  createMember!: Sequelize.HasManyCreateAssociationMixin<Members>;
  removeMember!: Sequelize.HasManyRemoveAssociationMixin<Members, MembersId>;
  removeMembers!: Sequelize.HasManyRemoveAssociationsMixin<Members, MembersId>;
  hasMember!: Sequelize.HasManyHasAssociationMixin<Members, MembersId>;
  hasMembers!: Sequelize.HasManyHasAssociationsMixin<Members, MembersId>;
  countMembers!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany PodAddresses via groupId
  podAddresses!: PodAddresses[];
  getPodAddresses!: Sequelize.HasManyGetAssociationsMixin<PodAddresses>;
  setPodAddresses!: Sequelize.HasManySetAssociationsMixin<
    PodAddresses,
    PodAddressesId
  >;
  addPodAddress!: Sequelize.HasManyAddAssociationMixin<
    PodAddresses,
    PodAddressesId
  >;
  addPodAddresses!: Sequelize.HasManyAddAssociationsMixin<
    PodAddresses,
    PodAddressesId
  >;
  createPodAddress!: Sequelize.HasManyCreateAssociationMixin<PodAddresses>;
  removePodAddress!: Sequelize.HasManyRemoveAssociationMixin<
    PodAddresses,
    PodAddressesId
  >;
  removePodAddresses!: Sequelize.HasManyRemoveAssociationsMixin<
    PodAddresses,
    PodAddressesId
  >;
  hasPodAddress!: Sequelize.HasManyHasAssociationMixin<
    PodAddresses,
    PodAddressesId
  >;
  hasPodAddresses!: Sequelize.HasManyHasAssociationsMixin<
    PodAddresses,
    PodAddressesId
  >;
  countPodAddresses!: Sequelize.HasManyCountAssociationsMixin;
  // Groups belongsToMany PodLocations via groupId and locationId
  locationIdPodLocations!: PodLocations[];
  getLocationIdPodLocations!: Sequelize.BelongsToManyGetAssociationsMixin<PodLocations>;
  setLocationIdPodLocations!: Sequelize.BelongsToManySetAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  addLocationIdPodLocation!: Sequelize.BelongsToManyAddAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  addLocationIdPodLocations!: Sequelize.BelongsToManyAddAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  createLocationIdPodLocation!: Sequelize.BelongsToManyCreateAssociationMixin<PodLocations>;
  removeLocationIdPodLocation!: Sequelize.BelongsToManyRemoveAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  removeLocationIdPodLocations!: Sequelize.BelongsToManyRemoveAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  hasLocationIdPodLocation!: Sequelize.BelongsToManyHasAssociationMixin<
    PodLocations,
    PodLocationsId
  >;
  hasLocationIdPodLocations!: Sequelize.BelongsToManyHasAssociationsMixin<
    PodLocations,
    PodLocationsId
  >;
  countLocationIdPodLocations!: Sequelize.BelongsToManyCountAssociationsMixin;
  // Groups hasMany RevenueProfiles via groupId
  revenueProfiles!: RevenueProfiles[];
  getRevenueProfiles!: Sequelize.HasManyGetAssociationsMixin<RevenueProfiles>;
  setRevenueProfiles!: Sequelize.HasManySetAssociationsMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  addRevenueProfile!: Sequelize.HasManyAddAssociationMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  addRevenueProfiles!: Sequelize.HasManyAddAssociationsMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  createRevenueProfile!: Sequelize.HasManyCreateAssociationMixin<RevenueProfiles>;
  removeRevenueProfile!: Sequelize.HasManyRemoveAssociationMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  removeRevenueProfiles!: Sequelize.HasManyRemoveAssociationsMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  hasRevenueProfile!: Sequelize.HasManyHasAssociationMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  hasRevenueProfiles!: Sequelize.HasManyHasAssociationsMixin<
    RevenueProfiles,
    RevenueProfilesId
  >;
  countRevenueProfiles!: Sequelize.HasManyCountAssociationsMixin;
  // Groups hasMany Users via groupId
  users!: Users[];
  getUsers!: Sequelize.HasManyGetAssociationsMixin<Users>;
  setUsers!: Sequelize.HasManySetAssociationsMixin<Users, UsersId>;
  addUser!: Sequelize.HasManyAddAssociationMixin<Users, UsersId>;
  addUsers!: Sequelize.HasManyAddAssociationsMixin<Users, UsersId>;
  createUser!: Sequelize.HasManyCreateAssociationMixin<Users>;
  removeUser!: Sequelize.HasManyRemoveAssociationMixin<Users, UsersId>;
  removeUsers!: Sequelize.HasManyRemoveAssociationsMixin<Users, UsersId>;
  hasUser!: Sequelize.HasManyHasAssociationMixin<Users, UsersId>;
  hasUsers!: Sequelize.HasManyHasAssociationsMixin<Users, UsersId>;
  countUsers!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof Groups {
    return Groups.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        uid: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        typeId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'group_types',
            key: 'id',
          },
          field: 'type_id',
        },
        ownerUserId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          field: 'owner_user_id',
        },
        name: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        contactName: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'contact_name',
        },
        phone: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        businessName: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'business_name',
        },
        line1: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'line_1',
        },
        line2: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'line_2',
        },
        postalTown: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'postal_town',
        },
        postcode: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        country: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        feePercentage: {
          type: DataTypes.DECIMAL(5, 2),
          allowNull: false,
          field: 'fee_percentage',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'groups',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'type_id',
            using: 'BTREE',
            fields: [{ name: 'type_id' }],
          },
        ],
      }
    );
  }
}
