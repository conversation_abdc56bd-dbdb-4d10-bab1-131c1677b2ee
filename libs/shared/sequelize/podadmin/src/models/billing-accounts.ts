import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { BillingEvents, BillingEventsId } from './billing-events';
import type { Charges, ChargesId } from './charges';
import type { ClaimedCharges, ClaimedChargesId } from './claimed-charges';
import type { Tags, TagsId } from './tags';
import type { Users, UsersId } from './users';

export interface BillingAccountsAttributes {
  id: number;
  userId?: number;
  uid: string;
  balance: number;
  currency: string;
  businessName: string;
  line1: string;
  line2: string;
  postalTown: string;
  postcode: string;
  country: string;
  phone?: string;
  mobile?: string;
  paymentProcessorId?: string;
  createdAt: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export type BillingAccountsPk = 'id';
export type BillingAccountsId = BillingAccounts[BillingAccountsPk];
export type BillingAccountsOptionalAttributes =
  | 'id'
  | 'userId'
  | 'balance'
  | 'phone'
  | 'mobile'
  | 'paymentProcessorId'
  | 'createdAt'
  | 'updatedAt'
  | 'deletedAt';
export type BillingAccountsCreationAttributes = Optional<
  BillingAccountsAttributes,
  BillingAccountsOptionalAttributes
>;

export class BillingAccounts
  extends Model<BillingAccountsAttributes, BillingAccountsCreationAttributes>
  implements BillingAccountsAttributes
{
  id!: number;
  userId?: number;
  uid!: string;
  balance!: number;
  currency!: string;
  businessName!: string;
  line1!: string;
  line2!: string;
  postalTown!: string;
  postcode!: string;
  country!: string;
  phone?: string;
  mobile?: string;
  paymentProcessorId?: string;
  createdAt!: Date;
  updatedAt?: Date;
  deletedAt?: Date;

  // BillingAccounts hasMany BillingEvents via accountId
  billingEvents!: BillingEvents[];
  getBillingEvents!: Sequelize.HasManyGetAssociationsMixin<BillingEvents>;
  setBillingEvents!: Sequelize.HasManySetAssociationsMixin<
    BillingEvents,
    BillingEventsId
  >;
  addBillingEvent!: Sequelize.HasManyAddAssociationMixin<
    BillingEvents,
    BillingEventsId
  >;
  addBillingEvents!: Sequelize.HasManyAddAssociationsMixin<
    BillingEvents,
    BillingEventsId
  >;
  createBillingEvent!: Sequelize.HasManyCreateAssociationMixin<BillingEvents>;
  removeBillingEvent!: Sequelize.HasManyRemoveAssociationMixin<
    BillingEvents,
    BillingEventsId
  >;
  removeBillingEvents!: Sequelize.HasManyRemoveAssociationsMixin<
    BillingEvents,
    BillingEventsId
  >;
  hasBillingEvent!: Sequelize.HasManyHasAssociationMixin<
    BillingEvents,
    BillingEventsId
  >;
  hasBillingEvents!: Sequelize.HasManyHasAssociationsMixin<
    BillingEvents,
    BillingEventsId
  >;
  countBillingEvents!: Sequelize.HasManyCountAssociationsMixin;
  // BillingAccounts hasMany Charges via billingAccountId
  charges!: Charges[];
  getCharges!: Sequelize.HasManyGetAssociationsMixin<Charges>;
  setCharges!: Sequelize.HasManySetAssociationsMixin<Charges, ChargesId>;
  addCharge!: Sequelize.HasManyAddAssociationMixin<Charges, ChargesId>;
  addCharges!: Sequelize.HasManyAddAssociationsMixin<Charges, ChargesId>;
  createCharge!: Sequelize.HasManyCreateAssociationMixin<Charges>;
  removeCharge!: Sequelize.HasManyRemoveAssociationMixin<Charges, ChargesId>;
  removeCharges!: Sequelize.HasManyRemoveAssociationsMixin<Charges, ChargesId>;
  hasCharge!: Sequelize.HasManyHasAssociationMixin<Charges, ChargesId>;
  hasCharges!: Sequelize.HasManyHasAssociationsMixin<Charges, ChargesId>;
  countCharges!: Sequelize.HasManyCountAssociationsMixin;
  // BillingAccounts hasMany ClaimedCharges via billingAccountId
  claimedCharges!: ClaimedCharges[];
  getClaimedCharges!: Sequelize.HasManyGetAssociationsMixin<ClaimedCharges>;
  setClaimedCharges!: Sequelize.HasManySetAssociationsMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  addClaimedCharge!: Sequelize.HasManyAddAssociationMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  addClaimedCharges!: Sequelize.HasManyAddAssociationsMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  createClaimedCharge!: Sequelize.HasManyCreateAssociationMixin<ClaimedCharges>;
  removeClaimedCharge!: Sequelize.HasManyRemoveAssociationMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  removeClaimedCharges!: Sequelize.HasManyRemoveAssociationsMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  hasClaimedCharge!: Sequelize.HasManyHasAssociationMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  hasClaimedCharges!: Sequelize.HasManyHasAssociationsMixin<
    ClaimedCharges,
    ClaimedChargesId
  >;
  countClaimedCharges!: Sequelize.HasManyCountAssociationsMixin;
  // BillingAccounts hasMany Tags via billingAccountId
  tags!: Tags[];
  getTags!: Sequelize.HasManyGetAssociationsMixin<Tags>;
  setTags!: Sequelize.HasManySetAssociationsMixin<Tags, TagsId>;
  addTag!: Sequelize.HasManyAddAssociationMixin<Tags, TagsId>;
  addTags!: Sequelize.HasManyAddAssociationsMixin<Tags, TagsId>;
  createTag!: Sequelize.HasManyCreateAssociationMixin<Tags>;
  removeTag!: Sequelize.HasManyRemoveAssociationMixin<Tags, TagsId>;
  removeTags!: Sequelize.HasManyRemoveAssociationsMixin<Tags, TagsId>;
  hasTag!: Sequelize.HasManyHasAssociationMixin<Tags, TagsId>;
  hasTags!: Sequelize.HasManyHasAssociationsMixin<Tags, TagsId>;
  countTags!: Sequelize.HasManyCountAssociationsMixin;
  // BillingAccounts belongsTo Users via userId
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof BillingAccounts {
    return BillingAccounts.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          field: 'user_id',
        },
        uid: {
          type: DataTypes.CHAR(36),
          allowNull: false,
          comment: '(DC2Type:uuid)',
          unique: 'billing_accounts_uid_unique',
        },
        balance: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        currency: {
          type: DataTypes.STRING(3),
          allowNull: false,
        },
        businessName: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'business_name',
        },
        line1: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'line_1',
        },
        line2: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'line_2',
        },
        postalTown: {
          type: DataTypes.STRING(255),
          allowNull: false,
          field: 'postal_town',
        },
        postcode: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        country: {
          type: DataTypes.STRING(3),
          allowNull: false,
        },
        phone: {
          type: DataTypes.STRING(255),
          allowNull: true,
        },
        mobile: {
          type: DataTypes.STRING(255),
          allowNull: true,
        },
        paymentProcessorId: {
          type: DataTypes.STRING(255),
          allowNull: true,
          field: 'payment_processor_id',
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'updated_at',
        },
        deletedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'deleted_at',
        },
      },
      {
        sequelize,
        tableName: 'billing_accounts',
        timestamps: true,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'billing_accounts_uid_unique',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'uid' }],
          },
          {
            name: 'user_id',
            using: 'BTREE',
            fields: [{ name: 'user_id' }],
          },
        ],
      }
    );
  }
}
