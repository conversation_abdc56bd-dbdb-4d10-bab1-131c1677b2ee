package mysql

import (
	"context"
	"experience/libs/shared/go/db"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

const (
	user     = "user"
	password = "password"
	schema   = "podpoint"
	port     = "3306"
)

type Database struct {
	Container testcontainers.Container
}

func NewPodadmin(t *testing.T) *Database {
	t.Helper()

	req := testcontainers.ContainerRequest{
		Image:         "mysql:8.0.36@sha256:a532724022429812ec797c285c1b540a644c15e248579c6bfdf12a8fbaab4964",
		ImagePlatform: "linux/x86_64",
		ExposedPorts:  []string{"3306/tcp"},
		Env: map[string]string{
			"MYSQL_ROOT_PASSWORD": "secret",
			"MYSQL_PASSWORD":      password,
			"MYSQL_USER":          user,
			"MYSQL_DATABASE":      schema,
		},
		// execution order is first added, first executed
		WaitingFor: wait.ForAll(
			NewWaitForLogStrategy(),
			NewWaitForListeningPort(),
			NewWaitForSQLStrategy()).
			WithStartupTimeoutDefault(3 * time.Minute),
	}

	mysqlContainer, err := testcontainers.GenericContainer(t.Context(), testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	require.NoError(t, err)

	return &Database{
		Container: mysqlContainer,
	}
}

func (c *Database) GetPasswordConfig(ctx context.Context) db.PasswordConfig {
	return db.PasswordConfig{
		Host:     "localhost",
		Name:     "podpoint",
		Password: "password",
		Port:     c.GetPort(ctx),
		Username: "user",
	}
}

func (c *Database) GetRootPasswordConfig(ctx context.Context) db.PasswordConfig {
	dbconfig := c.GetPasswordConfig(ctx)
	dbconfig.Username = "root"
	dbconfig.Password = "secret"

	return dbconfig
}

func (c *Database) GetPort(ctx context.Context) int {
	port, err := c.Container.MappedPort(ctx, port)
	if err != nil {
		log.Fatal(err)
	}
	return port.Int()
}
