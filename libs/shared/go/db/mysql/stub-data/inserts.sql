INSERT INTO pod_vendors(id, name, created_at, updated_at, deleted_at)
VALUES(1, 'Pod Point', '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO pod_ranges(id, name, created_at, updated_at, deleted_at)
VALUES(1, 'solo', '2015-10-14 13:10:50', '2015-10-14 13:10:50', NULL);
INSERT INTO pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(1, 1, 1, NULL, 'T7-S', 1, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-10-14 15:01:09', NULL);
INSERT INTO pod_statuses(id, key_name, name, created_at, updated_at, deleted_at)
VALUES(1, 'available', 'Available', '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);
INSERT INTO group_types(id, name, created_at, updated_at, deleted_at)
VALUES(1, 'podPoint', '2015-04-07 09:23:02', '2015-04-07 09:23:02', NULL);
INSERT INTO group_types(id, name, created_at, updated_at, deleted_at)
VALUES(2, 'host', '2015-04-07 09:23:02', '2015-04-07 09:23:02', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(1, '45c9ef22-b44b-4edd-bb66-392e923950af', 1, 6846, 'Pod Point - Software Team', 'Alfie Barry', '+44(0)3188 203136', 'Pod Point - Software Team', 'Floor 4, Cityside House', '40 Adler Street, Aldgate East', 'London', 'E1 1EE', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(2, 'c9c5d39c-23ba-4a21-ac45-75968fdc055f', 2, 15552, 'Tesco Stores Ltd', 'Alfie O’Connell', '***********', 'Tesco Stores Ltd', 'Tesco House', 'Shire Park, Kestrel Way', 'Welwyn Garden City', 'AL7 1GA', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(3, 'c9c5d39c-23ba-4a21-ac45-75968fdc055f', 2, 15552, 'Tesco Stores Ltd', 'Alfie O’Connell', '***********', 'Tesco Stores Ltd', 'Tesco House', 'Shire Park, Kestrel Way', 'Welwyn Garden City', 'AL7 1GA', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(4, 'c9c5d39c-23ba-4a21-ac45-75968fdc055f', 2, 15552, 'Tesco Stores Ltd', 'Alfie O’Connell', '***********', 'Tesco Stores Ltd', 'Tesco House', 'Shire Park, Kestrel Way', 'Welwyn Garden City', 'AL7 1GA', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
