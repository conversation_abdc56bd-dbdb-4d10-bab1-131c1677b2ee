// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: events.sql

package sqlc

import (
	"context"
	"encoding/json"

	"github.com/google/uuid"
)

const insertEventSubscription = `-- name: InsertEventSubscription :exec
INSERT INTO event_store.event_subscriptions (subscription_name, last_transaction_id, last_event_id)
VALUES ($1, $2, $3)
  ON CONFLICT DO NOTHING
`

type InsertEventSubscriptionParams struct {
	SubscriptionName  string `json:"subscription_name"`
	LastTransactionID uint64 `json:"last_transaction_id"`
	LastEventID       int64  `json:"last_event_id"`
}

func (q *Queries) InsertEventSubscription(ctx context.Context, arg InsertEventSubscriptionParams) error {
	_, err := q.db.ExecContext(ctx, insertEventSubscription, arg.SubscriptionName, arg.LastTransactionID, arg.LastEventID)
	return err
}

const loadEventsByAggregateID = `-- name: LoadEventsByAggregateID :many
SELECT id, transaction_id, aggregate_id, version, data FROM event_store.events WHERE aggregate_id = $1 ORDER BY transaction_id
`

func (q *Queries) LoadEventsByAggregateID(ctx context.Context, aggregateID uuid.UUID) ([]EventStoreEvent, error) {
	rows, err := q.db.QueryContext(ctx, loadEventsByAggregateID, aggregateID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []EventStoreEvent
	for rows.Next() {
		var i EventStoreEvent
		if err := rows.Scan(
			&i.ID,
			&i.TransactionID,
			&i.AggregateID,
			&i.Version,
			&i.Data,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const loadEventsByAggregateIDUpToTxID = `-- name: LoadEventsByAggregateIDUpToTxID :many
SELECT id, transaction_id, aggregate_id, version, data FROM event_store.events WHERE aggregate_id = $1 AND transaction_id <= $2 ORDER BY transaction_id
`

type LoadEventsByAggregateIDUpToTxIDParams struct {
	AggregateID   uuid.UUID `json:"aggregate_id"`
	TransactionID uint64    `json:"transaction_id"`
}

func (q *Queries) LoadEventsByAggregateIDUpToTxID(ctx context.Context, arg LoadEventsByAggregateIDUpToTxIDParams) ([]EventStoreEvent, error) {
	rows, err := q.db.QueryContext(ctx, loadEventsByAggregateIDUpToTxID, arg.AggregateID, arg.TransactionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []EventStoreEvent
	for rows.Next() {
		var i EventStoreEvent
		if err := rows.Scan(
			&i.ID,
			&i.TransactionID,
			&i.AggregateID,
			&i.Version,
			&i.Data,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const loadLastEventVersionForAggregateID = `-- name: LoadLastEventVersionForAggregateID :one
SELECT coalesce(max(version), 0)::INTEGER AS last_version FROM event_store.events
WHERE aggregate_id = $1
`

func (q *Queries) LoadLastEventVersionForAggregateID(ctx context.Context, aggregateID uuid.UUID) (int32, error) {
	row := q.db.QueryRowContext(ctx, loadLastEventVersionForAggregateID, aggregateID)
	var last_version int32
	err := row.Scan(&last_version)
	return last_version, err
}

const readCheckpointAndLockSubscription = `-- name: ReadCheckpointAndLockSubscription :one
SELECT last_event_id,
       last_transaction_id
FROM event_store.event_subscriptions
WHERE subscription_name = $1
FOR UPDATE SKIP LOCKED
`

type ReadCheckpointAndLockSubscriptionRow struct {
	LastEventID       int64  `json:"last_event_id"`
	LastTransactionID uint64 `json:"last_transaction_id"`
}

func (q *Queries) ReadCheckpointAndLockSubscription(ctx context.Context, subscriptionName string) (ReadCheckpointAndLockSubscriptionRow, error) {
	row := q.db.QueryRowContext(ctx, readCheckpointAndLockSubscription, subscriptionName)
	var i ReadCheckpointAndLockSubscriptionRow
	err := row.Scan(&i.LastEventID, &i.LastTransactionID)
	return i, err
}

const readEventsAfterCheckpoint = `-- name: ReadEventsAfterCheckpoint :many
SELECT e.id,
       e.transaction_id,
       e.version,
       e.data
FROM event_store.events e
WHERE (e.transaction_id, e.id) > ($1, $2::int8)
AND e.transaction_id < pg_snapshot_xmin(pg_current_snapshot())
ORDER BY e.transaction_id, e.id
limit 2000
`

type ReadEventsAfterCheckpointParams struct {
	TransactionID uint64 `json:"transaction_id"`
	ID            int64  `json:"id"`
}

type ReadEventsAfterCheckpointRow struct {
	ID            int64           `json:"id"`
	TransactionID uint64          `json:"transaction_id"`
	Version       int32           `json:"version"`
	Data          json.RawMessage `json:"data"`
}

func (q *Queries) ReadEventsAfterCheckpoint(ctx context.Context, arg ReadEventsAfterCheckpointParams) ([]ReadEventsAfterCheckpointRow, error) {
	rows, err := q.db.QueryContext(ctx, readEventsAfterCheckpoint, arg.TransactionID, arg.ID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ReadEventsAfterCheckpointRow
	for rows.Next() {
		var i ReadEventsAfterCheckpointRow
		if err := rows.Scan(
			&i.ID,
			&i.TransactionID,
			&i.Version,
			&i.Data,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const saveEvent = `-- name: SaveEvent :one
INSERT INTO event_store.events (transaction_id, aggregate_id, version, data)
VALUES (pg_current_xact_id(), $1, $2, $3)
RETURNING id, transaction_id, aggregate_id, version, data
`

type SaveEventParams struct {
	AggregateID uuid.UUID       `json:"aggregate_id"`
	Version     int32           `json:"version"`
	Data        json.RawMessage `json:"data"`
}

func (q *Queries) SaveEvent(ctx context.Context, arg SaveEventParams) (EventStoreEvent, error) {
	row := q.db.QueryRowContext(ctx, saveEvent, arg.AggregateID, arg.Version, arg.Data)
	var i EventStoreEvent
	err := row.Scan(
		&i.ID,
		&i.TransactionID,
		&i.AggregateID,
		&i.Version,
		&i.Data,
	)
	return i, err
}

const updateEventSubscription = `-- name: UpdateEventSubscription :one
UPDATE event_store.event_subscriptions
SET last_transaction_id = $1,
       last_event_id = $2
WHERE subscription_name = $3
RETURNING subscription_name, last_transaction_id, last_event_id
`

type UpdateEventSubscriptionParams struct {
	LastTransactionID uint64 `json:"last_transaction_id"`
	LastEventID       int64  `json:"last_event_id"`
	SubscriptionName  string `json:"subscription_name"`
}

func (q *Queries) UpdateEventSubscription(ctx context.Context, arg UpdateEventSubscriptionParams) (EventStoreEventSubscription, error) {
	row := q.db.QueryRowContext(ctx, updateEventSubscription, arg.LastTransactionID, arg.LastEventID, arg.SubscriptionName)
	var i EventStoreEventSubscription
	err := row.Scan(&i.SubscriptionName, &i.LastTransactionID, &i.LastEventID)
	return i, err
}
