{"name": "shared-go-event-store", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared/go/event-store", "implicitDependencies": ["shared-go-db-migrate"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}, "generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["go run libs/shared/go/sqlc/generate.go -config libs/shared/go/event-store/sqlc.yaml", "mockgen -destination libs/shared/go/event-store/test/mock/event.go -source libs/shared/go/event-store/event.go", "mockgen -destination libs/shared/go/event-store/test/mock/store.go -source libs/shared/go/event-store/store.go", "mockgen -destination libs/shared/go/event-store/test/mock/event_transformer.go -source libs/shared/go/event-store/event_transformer.go", "mockgen -destination libs/shared/go/event-store/test/mock/command_transformer.go -source libs/shared/go/event-store/command_transformer.go", "mockgen -destination libs/shared/go/event-store/handlers/mock/ports.go -source libs/shared/go/event-store/handlers/ports.go"], "parallel": false}}}, "tags": ["shared"]}