// Code generated by MockGen. DO NOT EDIT.
// Source: libs/shared/go/event-store/handlers/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/shared/go/event-store/handlers/mock/ports.go -source libs/shared/go/event-store/handlers/ports.go
//
// Package mock_handlers is a generated GoMock package.
package mock_handlers

import (
	context "context"
	sql "database/sql"
	json "encoding/json"
	eventstore "experience/libs/shared/go/event-store"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockSyncEventHandler is a mock of SyncEventHandler interface.
type MockSyncEventHandler struct {
	ctrl     *gomock.Controller
	recorder *MockSyncEventHandlerMockRecorder
}

// MockSyncEventHandlerMockRecorder is the mock recorder for MockSyncEventHandler.
type MockSyncEventHandlerMockRecorder struct {
	mock *MockSyncEventHandler
}

// NewMockSyncEventHandler creates a new mock instance.
func NewMockSyncEventHandler(ctrl *gomock.Controller) *MockSyncEventHandler {
	mock := &MockSyncEventHandler{ctrl: ctrl}
	mock.recorder = &MockSyncEventHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSyncEventHandler) EXPECT() *MockSyncEventHandlerMockRecorder {
	return m.recorder
}

// ErrorAllowed mocks base method.
func (m *MockSyncEventHandler) ErrorAllowed(err error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ErrorAllowed", err)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ErrorAllowed indicates an expected call of ErrorAllowed.
func (mr *MockSyncEventHandlerMockRecorder) ErrorAllowed(err any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ErrorAllowed", reflect.TypeOf((*MockSyncEventHandler)(nil).ErrorAllowed), err)
}

// Execute mocks base method.
func (m *MockSyncEventHandler) Execute(ctx context.Context, event eventstore.Event, eventBody json.RawMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, event, eventBody)
	ret0, _ := ret[0].(error)
	return ret0
}

// Execute indicates an expected call of Execute.
func (mr *MockSyncEventHandlerMockRecorder) Execute(ctx, event, eventBody any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockSyncEventHandler)(nil).Execute), ctx, event, eventBody)
}

// MockAsyncEventHandler is a mock of AsyncEventHandler interface.
type MockAsyncEventHandler struct {
	ctrl     *gomock.Controller
	recorder *MockAsyncEventHandlerMockRecorder
}

// MockAsyncEventHandlerMockRecorder is the mock recorder for MockAsyncEventHandler.
type MockAsyncEventHandlerMockRecorder struct {
	mock *MockAsyncEventHandler
}

// NewMockAsyncEventHandler creates a new mock instance.
func NewMockAsyncEventHandler(ctrl *gomock.Controller) *MockAsyncEventHandler {
	mock := &MockAsyncEventHandler{ctrl: ctrl}
	mock.recorder = &MockAsyncEventHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAsyncEventHandler) EXPECT() *MockAsyncEventHandlerMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockAsyncEventHandler) Execute(ctx context.Context, tx *sql.Tx, event eventstore.Event, currentTransactionID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, tx, event, currentTransactionID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Execute indicates an expected call of Execute.
func (mr *MockAsyncEventHandlerMockRecorder) Execute(ctx, tx, event, currentTransactionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockAsyncEventHandler)(nil).Execute), ctx, tx, event, currentTransactionID)
}

// MockCommandHandler is a mock of CommandHandler interface.
type MockCommandHandler struct {
	ctrl     *gomock.Controller
	recorder *MockCommandHandlerMockRecorder
}

// MockCommandHandlerMockRecorder is the mock recorder for MockCommandHandler.
type MockCommandHandlerMockRecorder struct {
	mock *MockCommandHandler
}

// NewMockCommandHandler creates a new mock instance.
func NewMockCommandHandler(ctrl *gomock.Controller) *MockCommandHandler {
	mock := &MockCommandHandler{ctrl: ctrl}
	mock.recorder = &MockCommandHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommandHandler) EXPECT() *MockCommandHandlerMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockCommandHandler) Execute(ctx context.Context, command eventstore.Command) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx, command)
	ret0, _ := ret[0].(error)
	return ret0
}

// Execute indicates an expected call of Execute.
func (mr *MockCommandHandlerMockRecorder) Execute(ctx, command any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockCommandHandler)(nil).Execute), ctx, command)
}

// MockErrorArbiter is a mock of ErrorArbiter interface.
type MockErrorArbiter struct {
	ctrl     *gomock.Controller
	recorder *MockErrorArbiterMockRecorder
}

// MockErrorArbiterMockRecorder is the mock recorder for MockErrorArbiter.
type MockErrorArbiterMockRecorder struct {
	mock *MockErrorArbiter
}

// NewMockErrorArbiter creates a new mock instance.
func NewMockErrorArbiter(ctrl *gomock.Controller) *MockErrorArbiter {
	mock := &MockErrorArbiter{ctrl: ctrl}
	mock.recorder = &MockErrorArbiterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockErrorArbiter) EXPECT() *MockErrorArbiterMockRecorder {
	return m.recorder
}

// ErrorAllowed mocks base method.
func (m *MockErrorArbiter) ErrorAllowed(err error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ErrorAllowed", err)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ErrorAllowed indicates an expected call of ErrorAllowed.
func (mr *MockErrorArbiterMockRecorder) ErrorAllowed(err any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ErrorAllowed", reflect.TypeOf((*MockErrorArbiter)(nil).ErrorAllowed), err)
}
