// Code generated by MockGen. DO NOT EDIT.
// Source: libs/shared/go/event-store/command_transformer.go
//
// Generated by this command:
//
//	mockgen -destination libs/shared/go/event-store/test/mock/command_transformer.go -source libs/shared/go/event-store/command_transformer.go
//
// Package mock_eventstore is a generated GoMock package.
package mock_eventstore

import (
	eventstore "experience/libs/shared/go/event-store"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockCommandTransformer is a mock of CommandTransformer interface.
type MockCommandTransformer struct {
	ctrl     *gomock.Controller
	recorder *MockCommandTransformerMockRecorder
}

// MockCommandTransformerMockRecorder is the mock recorder for MockCommandTransformer.
type MockCommandTransformerMockRecorder struct {
	mock *MockCommandTransformer
}

// NewMockCommandTransformer creates a new mock instance.
func NewMockCommandTransformer(ctrl *gomock.Controller) *MockCommandTransformer {
	mock := &MockCommandTransformer{ctrl: ctrl}
	mock.recorder = &MockCommandTransformerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommandTransformer) EXPECT() *MockCommandTransformerMockRecorder {
	return m.recorder
}

// Transform mocks base method.
func (m *MockCommandTransformer) Transform(messageBody *string) (eventstore.Command, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transform", messageBody)
	ret0, _ := ret[0].(eventstore.Command)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Transform indicates an expected call of Transform.
func (mr *MockCommandTransformerMockRecorder) Transform(messageBody any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transform", reflect.TypeOf((*MockCommandTransformer)(nil).Transform), messageBody)
}
