/*
Charge Sessions Service Transaction API

An API for creating charging stations transactions.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package transactionsapiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the HandlersStopTransactionRequestBody type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HandlersStopTransactionRequestBody{}

// HandlersStopTransactionRequestBody struct for HandlersStopTransactionRequestBody
type HandlersStopTransactionRequestBody struct {
	ChargingStationId string `json:"chargingStationId"`
	ClientRef         string `json:"clientRef"`
	Door              string `json:"door"`
}

type _HandlersStopTransactionRequestBody HandlersStopTransactionRequestBody

// NewHandlersStopTransactionRequestBody instantiates a new HandlersStopTransactionRequestBody object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHandlersStopTransactionRequestBody(chargingStationId string, clientRef string, door string) *HandlersStopTransactionRequestBody {
	this := HandlersStopTransactionRequestBody{}
	this.ChargingStationId = chargingStationId
	this.ClientRef = clientRef
	this.Door = door
	return &this
}

// NewHandlersStopTransactionRequestBodyWithDefaults instantiates a new HandlersStopTransactionRequestBody object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHandlersStopTransactionRequestBodyWithDefaults() *HandlersStopTransactionRequestBody {
	this := HandlersStopTransactionRequestBody{}
	return &this
}

// GetChargingStationId returns the ChargingStationId field value
func (o *HandlersStopTransactionRequestBody) GetChargingStationId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.ChargingStationId
}

// GetChargingStationIdOk returns a tuple with the ChargingStationId field value
// and a boolean to check if the value has been set.
func (o *HandlersStopTransactionRequestBody) GetChargingStationIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingStationId, true
}

// SetChargingStationId sets field value
func (o *HandlersStopTransactionRequestBody) SetChargingStationId(v string) {
	o.ChargingStationId = v
}

// GetClientRef returns the ClientRef field value
func (o *HandlersStopTransactionRequestBody) GetClientRef() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.ClientRef
}

// GetClientRefOk returns a tuple with the ClientRef field value
// and a boolean to check if the value has been set.
func (o *HandlersStopTransactionRequestBody) GetClientRefOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ClientRef, true
}

// SetClientRef sets field value
func (o *HandlersStopTransactionRequestBody) SetClientRef(v string) {
	o.ClientRef = v
}

// GetDoor returns the Door field value
func (o *HandlersStopTransactionRequestBody) GetDoor() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Door
}

// GetDoorOk returns a tuple with the Door field value
// and a boolean to check if the value has been set.
func (o *HandlersStopTransactionRequestBody) GetDoorOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Door, true
}

// SetDoor sets field value
func (o *HandlersStopTransactionRequestBody) SetDoor(v string) {
	o.Door = v
}

func (o HandlersStopTransactionRequestBody) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HandlersStopTransactionRequestBody) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["chargingStationId"] = o.ChargingStationId
	toSerialize["clientRef"] = o.ClientRef
	toSerialize["door"] = o.Door
	return toSerialize, nil
}

func (o *HandlersStopTransactionRequestBody) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"chargingStationId",
		"clientRef",
		"door",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varHandlersStopTransactionRequestBody := _HandlersStopTransactionRequestBody{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varHandlersStopTransactionRequestBody)

	if err != nil {
		return err
	}

	*o = HandlersStopTransactionRequestBody(varHandlersStopTransactionRequestBody)

	return err
}

type NullableHandlersStopTransactionRequestBody struct {
	value *HandlersStopTransactionRequestBody
	isSet bool
}

func (v NullableHandlersStopTransactionRequestBody) Get() *HandlersStopTransactionRequestBody {
	return v.value
}

func (v *NullableHandlersStopTransactionRequestBody) Set(val *HandlersStopTransactionRequestBody) {
	v.value = val
	v.isSet = true
}

func (v NullableHandlersStopTransactionRequestBody) IsSet() bool {
	return v.isSet
}

func (v *NullableHandlersStopTransactionRequestBody) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHandlersStopTransactionRequestBody(val *HandlersStopTransactionRequestBody) *NullableHandlersStopTransactionRequestBody {
	return &NullableHandlersStopTransactionRequestBody{value: val, isSet: true}
}

func (v NullableHandlersStopTransactionRequestBody) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHandlersStopTransactionRequestBody) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
