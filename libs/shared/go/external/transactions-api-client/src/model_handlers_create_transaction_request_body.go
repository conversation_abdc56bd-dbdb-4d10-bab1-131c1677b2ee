/*
Charge Sessions Service Transaction API

An API for creating charging stations transactions.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package transactionsapiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the HandlersCreateTransactionRequestBody type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HandlersCreateTransactionRequestBody{}

// HandlersCreateTransactionRequestBody struct for HandlersCreateTransactionRequestBody
type HandlersCreateTransactionRequestBody struct {
	// Charging Station ID for which to create the transaction
	ChargingStationId string `json:"chargingStationId"`
	// The connector ID for which to create the transaction
	ConnectorId int32 `json:"connectorId"`
	// The door name
	Door string `json:"door"`
	// The authorisation id tag for which to create the transaction
	IdTag string `json:"idTag"`
	// Additional metadata for the ppcp translation
	Metadata *HandlersCreateTransactionRequestBodyMetadata `json:"metadata,omitempty"`
	// The meter value in Wh for the transaction
	MeterStart int32 `json:"meterStart"`
	// The UTC timestamp of the transaction in the format RFC3339
	Timestamp string `json:"timestamp"`
}

type _HandlersCreateTransactionRequestBody HandlersCreateTransactionRequestBody

// NewHandlersCreateTransactionRequestBody instantiates a new HandlersCreateTransactionRequestBody object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHandlersCreateTransactionRequestBody(chargingStationId string, connectorId int32, door string, idTag string, meterStart int32, timestamp string) *HandlersCreateTransactionRequestBody {
	this := HandlersCreateTransactionRequestBody{}
	this.ChargingStationId = chargingStationId
	this.ConnectorId = connectorId
	this.Door = door
	this.IdTag = idTag
	this.MeterStart = meterStart
	this.Timestamp = timestamp
	return &this
}

// NewHandlersCreateTransactionRequestBodyWithDefaults instantiates a new HandlersCreateTransactionRequestBody object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHandlersCreateTransactionRequestBodyWithDefaults() *HandlersCreateTransactionRequestBody {
	this := HandlersCreateTransactionRequestBody{}
	return &this
}

// GetChargingStationId returns the ChargingStationId field value
func (o *HandlersCreateTransactionRequestBody) GetChargingStationId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.ChargingStationId
}

// GetChargingStationIdOk returns a tuple with the ChargingStationId field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetChargingStationIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingStationId, true
}

// SetChargingStationId sets field value
func (o *HandlersCreateTransactionRequestBody) SetChargingStationId(v string) {
	o.ChargingStationId = v
}

// GetConnectorId returns the ConnectorId field value
func (o *HandlersCreateTransactionRequestBody) GetConnectorId() int32 {
	if o == nil {
		var ret int32
		return ret
	}

	return o.ConnectorId
}

// GetConnectorIdOk returns a tuple with the ConnectorId field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetConnectorIdOk() (*int32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ConnectorId, true
}

// SetConnectorId sets field value
func (o *HandlersCreateTransactionRequestBody) SetConnectorId(v int32) {
	o.ConnectorId = v
}

// GetDoor returns the Door field value
func (o *HandlersCreateTransactionRequestBody) GetDoor() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Door
}

// GetDoorOk returns a tuple with the Door field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetDoorOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Door, true
}

// SetDoor sets field value
func (o *HandlersCreateTransactionRequestBody) SetDoor(v string) {
	o.Door = v
}

// GetIdTag returns the IdTag field value
func (o *HandlersCreateTransactionRequestBody) GetIdTag() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.IdTag
}

// GetIdTagOk returns a tuple with the IdTag field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetIdTagOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.IdTag, true
}

// SetIdTag sets field value
func (o *HandlersCreateTransactionRequestBody) SetIdTag(v string) {
	o.IdTag = v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBody) GetMetadata() HandlersCreateTransactionRequestBodyMetadata {
	if o == nil || IsNil(o.Metadata) {
		var ret HandlersCreateTransactionRequestBodyMetadata
		return ret
	}
	return *o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetMetadataOk() (*HandlersCreateTransactionRequestBodyMetadata, bool) {
	if o == nil || IsNil(o.Metadata) {
		return nil, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBody) HasMetadata() bool {
	if o != nil && !IsNil(o.Metadata) {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given HandlersCreateTransactionRequestBodyMetadata and assigns it to the Metadata field.
func (o *HandlersCreateTransactionRequestBody) SetMetadata(v HandlersCreateTransactionRequestBodyMetadata) {
	o.Metadata = &v
}

// GetMeterStart returns the MeterStart field value
func (o *HandlersCreateTransactionRequestBody) GetMeterStart() int32 {
	if o == nil {
		var ret int32
		return ret
	}

	return o.MeterStart
}

// GetMeterStartOk returns a tuple with the MeterStart field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetMeterStartOk() (*int32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.MeterStart, true
}

// SetMeterStart sets field value
func (o *HandlersCreateTransactionRequestBody) SetMeterStart(v int32) {
	o.MeterStart = v
}

// GetTimestamp returns the Timestamp field value
func (o *HandlersCreateTransactionRequestBody) GetTimestamp() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Timestamp
}

// GetTimestampOk returns a tuple with the Timestamp field value
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBody) GetTimestampOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Timestamp, true
}

// SetTimestamp sets field value
func (o *HandlersCreateTransactionRequestBody) SetTimestamp(v string) {
	o.Timestamp = v
}

func (o HandlersCreateTransactionRequestBody) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HandlersCreateTransactionRequestBody) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["chargingStationId"] = o.ChargingStationId
	toSerialize["connectorId"] = o.ConnectorId
	toSerialize["door"] = o.Door
	toSerialize["idTag"] = o.IdTag
	if !IsNil(o.Metadata) {
		toSerialize["metadata"] = o.Metadata
	}
	toSerialize["meterStart"] = o.MeterStart
	toSerialize["timestamp"] = o.Timestamp
	return toSerialize, nil
}

func (o *HandlersCreateTransactionRequestBody) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"chargingStationId",
		"connectorId",
		"door",
		"idTag",
		"meterStart",
		"timestamp",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varHandlersCreateTransactionRequestBody := _HandlersCreateTransactionRequestBody{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varHandlersCreateTransactionRequestBody)

	if err != nil {
		return err
	}

	*o = HandlersCreateTransactionRequestBody(varHandlersCreateTransactionRequestBody)

	return err
}

type NullableHandlersCreateTransactionRequestBody struct {
	value *HandlersCreateTransactionRequestBody
	isSet bool
}

func (v NullableHandlersCreateTransactionRequestBody) Get() *HandlersCreateTransactionRequestBody {
	return v.value
}

func (v *NullableHandlersCreateTransactionRequestBody) Set(val *HandlersCreateTransactionRequestBody) {
	v.value = val
	v.isSet = true
}

func (v NullableHandlersCreateTransactionRequestBody) IsSet() bool {
	return v.isSet
}

func (v *NullableHandlersCreateTransactionRequestBody) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHandlersCreateTransactionRequestBody(val *HandlersCreateTransactionRequestBody) *NullableHandlersCreateTransactionRequestBody {
	return &NullableHandlersCreateTransactionRequestBody{value: val, isSet: true}
}

func (v NullableHandlersCreateTransactionRequestBody) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHandlersCreateTransactionRequestBody) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
