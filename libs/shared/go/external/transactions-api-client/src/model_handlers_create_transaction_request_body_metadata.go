/*
Charge Sessions Service Transaction API

An API for creating charging stations transactions.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package transactionsapiclient

import (
	"encoding/json"
)

// checks if the HandlersCreateTransactionRequestBodyMetadata type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HandlersCreateTransactionRequestBodyMetadata{}

// HandlersCreateTransactionRequestBodyMetadata struct for HandlersCreateTransactionRequestBodyMetadata
type HandlersCreateTransactionRequestBodyMetadata struct {
	// The ID of the EVSE
	EvseId *int32 `json:"evseId,omitempty"`
	// The version of the firmware
	FirmwareVersion *string `json:"firmwareVersion,omitempty"`
	// The MAC address of the PCB
	PcbMacAddress *string `json:"pcbMacAddress,omitempty"`
	// The serial number of the PCB
	PcbSerialNumber *string `json:"pcbSerialNumber,omitempty"`
	// The type of the PCB
	PcbType *string `json:"pcbType,omitempty"`
}

// NewHandlersCreateTransactionRequestBodyMetadata instantiates a new HandlersCreateTransactionRequestBodyMetadata object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHandlersCreateTransactionRequestBodyMetadata() *HandlersCreateTransactionRequestBodyMetadata {
	this := HandlersCreateTransactionRequestBodyMetadata{}
	return &this
}

// NewHandlersCreateTransactionRequestBodyMetadataWithDefaults instantiates a new HandlersCreateTransactionRequestBodyMetadata object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHandlersCreateTransactionRequestBodyMetadataWithDefaults() *HandlersCreateTransactionRequestBodyMetadata {
	this := HandlersCreateTransactionRequestBodyMetadata{}
	return &this
}

// GetEvseId returns the EvseId field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetEvseId() int32 {
	if o == nil || IsNil(o.EvseId) {
		var ret int32
		return ret
	}
	return *o.EvseId
}

// GetEvseIdOk returns a tuple with the EvseId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetEvseIdOk() (*int32, bool) {
	if o == nil || IsNil(o.EvseId) {
		return nil, false
	}
	return o.EvseId, true
}

// HasEvseId returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) HasEvseId() bool {
	if o != nil && !IsNil(o.EvseId) {
		return true
	}

	return false
}

// SetEvseId gets a reference to the given int32 and assigns it to the EvseId field.
func (o *HandlersCreateTransactionRequestBodyMetadata) SetEvseId(v int32) {
	o.EvseId = &v
}

// GetFirmwareVersion returns the FirmwareVersion field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetFirmwareVersion() string {
	if o == nil || IsNil(o.FirmwareVersion) {
		var ret string
		return ret
	}
	return *o.FirmwareVersion
}

// GetFirmwareVersionOk returns a tuple with the FirmwareVersion field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetFirmwareVersionOk() (*string, bool) {
	if o == nil || IsNil(o.FirmwareVersion) {
		return nil, false
	}
	return o.FirmwareVersion, true
}

// HasFirmwareVersion returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) HasFirmwareVersion() bool {
	if o != nil && !IsNil(o.FirmwareVersion) {
		return true
	}

	return false
}

// SetFirmwareVersion gets a reference to the given string and assigns it to the FirmwareVersion field.
func (o *HandlersCreateTransactionRequestBodyMetadata) SetFirmwareVersion(v string) {
	o.FirmwareVersion = &v
}

// GetPcbMacAddress returns the PcbMacAddress field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbMacAddress() string {
	if o == nil || IsNil(o.PcbMacAddress) {
		var ret string
		return ret
	}
	return *o.PcbMacAddress
}

// GetPcbMacAddressOk returns a tuple with the PcbMacAddress field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbMacAddressOk() (*string, bool) {
	if o == nil || IsNil(o.PcbMacAddress) {
		return nil, false
	}
	return o.PcbMacAddress, true
}

// HasPcbMacAddress returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) HasPcbMacAddress() bool {
	if o != nil && !IsNil(o.PcbMacAddress) {
		return true
	}

	return false
}

// SetPcbMacAddress gets a reference to the given string and assigns it to the PcbMacAddress field.
func (o *HandlersCreateTransactionRequestBodyMetadata) SetPcbMacAddress(v string) {
	o.PcbMacAddress = &v
}

// GetPcbSerialNumber returns the PcbSerialNumber field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbSerialNumber() string {
	if o == nil || IsNil(o.PcbSerialNumber) {
		var ret string
		return ret
	}
	return *o.PcbSerialNumber
}

// GetPcbSerialNumberOk returns a tuple with the PcbSerialNumber field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbSerialNumberOk() (*string, bool) {
	if o == nil || IsNil(o.PcbSerialNumber) {
		return nil, false
	}
	return o.PcbSerialNumber, true
}

// HasPcbSerialNumber returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) HasPcbSerialNumber() bool {
	if o != nil && !IsNil(o.PcbSerialNumber) {
		return true
	}

	return false
}

// SetPcbSerialNumber gets a reference to the given string and assigns it to the PcbSerialNumber field.
func (o *HandlersCreateTransactionRequestBodyMetadata) SetPcbSerialNumber(v string) {
	o.PcbSerialNumber = &v
}

// GetPcbType returns the PcbType field value if set, zero value otherwise.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbType() string {
	if o == nil || IsNil(o.PcbType) {
		var ret string
		return ret
	}
	return *o.PcbType
}

// GetPcbTypeOk returns a tuple with the PcbType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) GetPcbTypeOk() (*string, bool) {
	if o == nil || IsNil(o.PcbType) {
		return nil, false
	}
	return o.PcbType, true
}

// HasPcbType returns a boolean if a field has been set.
func (o *HandlersCreateTransactionRequestBodyMetadata) HasPcbType() bool {
	if o != nil && !IsNil(o.PcbType) {
		return true
	}

	return false
}

// SetPcbType gets a reference to the given string and assigns it to the PcbType field.
func (o *HandlersCreateTransactionRequestBodyMetadata) SetPcbType(v string) {
	o.PcbType = &v
}

func (o HandlersCreateTransactionRequestBodyMetadata) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HandlersCreateTransactionRequestBodyMetadata) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.EvseId) {
		toSerialize["evseId"] = o.EvseId
	}
	if !IsNil(o.FirmwareVersion) {
		toSerialize["firmwareVersion"] = o.FirmwareVersion
	}
	if !IsNil(o.PcbMacAddress) {
		toSerialize["pcbMacAddress"] = o.PcbMacAddress
	}
	if !IsNil(o.PcbSerialNumber) {
		toSerialize["pcbSerialNumber"] = o.PcbSerialNumber
	}
	if !IsNil(o.PcbType) {
		toSerialize["pcbType"] = o.PcbType
	}
	return toSerialize, nil
}

type NullableHandlersCreateTransactionRequestBodyMetadata struct {
	value *HandlersCreateTransactionRequestBodyMetadata
	isSet bool
}

func (v NullableHandlersCreateTransactionRequestBodyMetadata) Get() *HandlersCreateTransactionRequestBodyMetadata {
	return v.value
}

func (v *NullableHandlersCreateTransactionRequestBodyMetadata) Set(val *HandlersCreateTransactionRequestBodyMetadata) {
	v.value = val
	v.isSet = true
}

func (v NullableHandlersCreateTransactionRequestBodyMetadata) IsSet() bool {
	return v.isSet
}

func (v *NullableHandlersCreateTransactionRequestBodyMetadata) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHandlersCreateTransactionRequestBodyMetadata(val *HandlersCreateTransactionRequestBodyMetadata) *NullableHandlersCreateTransactionRequestBodyMetadata {
	return &NullableHandlersCreateTransactionRequestBodyMetadata{value: val, isSet: true}
}

func (v NullableHandlersCreateTransactionRequestBodyMetadata) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHandlersCreateTransactionRequestBodyMetadata) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
