package transactionsapiclient

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		host    string
		want    *APIClient
		wantErr bool
	}{
		{
			name:    "success",
			host:    "http://localhost:8080",
			wantErr: false,
		},
		{
			name:    "host is required",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewTransactionsAPIClient(true, tt.host)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewTransactionsAPIClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCanCallTransactionsAPI(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		require.Equal(t, "/transactions/start", req.RequestURI)

		byteBody, _ := io.ReadAll(req.Body)
		requestBody := string(byteBody)
		require.NotNil(t, requestBody)

		res.Header().Set("Content-Type", "application/json")
		res.WriteHeader(http.StatusCreated)

		_, _ = res.Write([]byte(`{
			"status": "started",
			"message": "Transaction started successfully"
		}`))
	}))
	defer testServer.Close()

	url := testServer.URL
	client, err := NewTransactionsAPIClient(true, url)
	require.NoError(t, err)

	request := client.TransactionsStartPost(context.Background())
	request = request.HandlersStartTransactionRequestBody(newHandlersStartTransactionRequestBody())

	createTransactionResponse, response, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)

	require.Equal(t, 201, response.StatusCode)
	require.Equal(t, "started", *createTransactionResponse.Status)
	require.Equal(t, "Transaction started successfully", *createTransactionResponse.Message)
}

func TestCanCallStubAPI(t *testing.T) {
	client, err := NewTransactionsAPIClient(false, "http://localhost:8080")
	require.NoError(t, err)

	request := client.TransactionsStartPost(context.Background())
	request = request.HandlersStartTransactionRequestBody(newHandlersStartTransactionRequestBody())

	createTransactionResponse, response, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)

	require.Equal(t, 201, response.StatusCode)
	require.Equal(t, "started", *createTransactionResponse.Status)
	require.Equal(t, "Transaction started successfully", *createTransactionResponse.Message)
}

func newHandlersStartTransactionRequestBody() HandlersStartTransactionRequestBody {
	return HandlersStartTransactionRequestBody{
		AuthoriserId:      ptr.To("123"),
		ChargingStationId: ptr.To("456"),
		ClientRef:         ptr.To("CLIENT_REF"),
		Door:              ptr.To("A"),
	}
}
