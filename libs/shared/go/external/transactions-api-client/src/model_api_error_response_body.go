/*
Charge Sessions Service Transaction API

An API for creating charging stations transactions.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package transactionsapiclient

import (
	"encoding/json"
)

// checks if the ApiErrorResponseBody type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ApiErrorResponseBody{}

// ApiErrorResponseBody struct for ApiErrorResponseBody
type ApiErrorResponseBody struct {
	// Verbose error message
	Error *string `json:"error,omitempty"`
}

// NewApiErrorResponseBody instantiates a new ApiErrorResponseBody object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewApiErrorResponseBody() *ApiErrorResponseBody {
	this := ApiErrorResponseBody{}
	return &this
}

// NewApiErrorResponseBodyWithDefaults instantiates a new ApiErrorResponseBody object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewApiErrorResponseBodyWithDefaults() *ApiErrorResponseBody {
	this := ApiErrorResponseBody{}
	return &this
}

// GetError returns the Error field value if set, zero value otherwise.
func (o *ApiErrorResponseBody) GetError() string {
	if o == nil || IsNil(o.Error) {
		var ret string
		return ret
	}
	return *o.Error
}

// GetErrorOk returns a tuple with the Error field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ApiErrorResponseBody) GetErrorOk() (*string, bool) {
	if o == nil || IsNil(o.Error) {
		return nil, false
	}
	return o.Error, true
}

// HasError returns a boolean if a field has been set.
func (o *ApiErrorResponseBody) HasError() bool {
	if o != nil && !IsNil(o.Error) {
		return true
	}

	return false
}

// SetError gets a reference to the given string and assigns it to the Error field.
func (o *ApiErrorResponseBody) SetError(v string) {
	o.Error = &v
}

func (o ApiErrorResponseBody) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ApiErrorResponseBody) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Error) {
		toSerialize["error"] = o.Error
	}
	return toSerialize, nil
}

type NullableApiErrorResponseBody struct {
	value *ApiErrorResponseBody
	isSet bool
}

func (v NullableApiErrorResponseBody) Get() *ApiErrorResponseBody {
	return v.value
}

func (v *NullableApiErrorResponseBody) Set(val *ApiErrorResponseBody) {
	v.value = val
	v.isSet = true
}

func (v NullableApiErrorResponseBody) IsSet() bool {
	return v.isSet
}

func (v *NullableApiErrorResponseBody) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableApiErrorResponseBody(val *ApiErrorResponseBody) *NullableApiErrorResponseBody {
	return &NullableApiErrorResponseBody{value: val, isSet: true}
}

func (v NullableApiErrorResponseBody) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableApiErrorResponseBody) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
