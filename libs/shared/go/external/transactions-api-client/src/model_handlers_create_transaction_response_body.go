/*
Charge Sessions Service Transaction API

An API for creating charging stations transactions.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package transactionsapiclient

import (
	"encoding/json"
)

// checks if the HandlersCreateTransactionResponseBody type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HandlersCreateTransactionResponseBody{}

// HandlersCreateTransactionResponseBody struct for HandlersCreateTransactionResponseBody
type HandlersCreateTransactionResponseBody struct {
	// The ID of the created transaction
	Id *int32 `json:"id,omitempty"`
}

// NewHandlersCreateTransactionResponseBody instantiates a new HandlersCreateTransactionResponseBody object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHandlersCreateTransactionResponseBody() *HandlersCreateTransactionResponseBody {
	this := HandlersCreateTransactionResponseBody{}
	return &this
}

// NewHandlersCreateTransactionResponseBodyWithDefaults instantiates a new HandlersCreateTransactionResponseBody object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHandlersCreateTransactionResponseBodyWithDefaults() *HandlersCreateTransactionResponseBody {
	this := HandlersCreateTransactionResponseBody{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *HandlersCreateTransactionResponseBody) GetId() int32 {
	if o == nil || IsNil(o.Id) {
		var ret int32
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HandlersCreateTransactionResponseBody) GetIdOk() (*int32, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *HandlersCreateTransactionResponseBody) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given int32 and assigns it to the Id field.
func (o *HandlersCreateTransactionResponseBody) SetId(v int32) {
	o.Id = &v
}

func (o HandlersCreateTransactionResponseBody) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HandlersCreateTransactionResponseBody) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	return toSerialize, nil
}

type NullableHandlersCreateTransactionResponseBody struct {
	value *HandlersCreateTransactionResponseBody
	isSet bool
}

func (v NullableHandlersCreateTransactionResponseBody) Get() *HandlersCreateTransactionResponseBody {
	return v.value
}

func (v *NullableHandlersCreateTransactionResponseBody) Set(val *HandlersCreateTransactionResponseBody) {
	v.value = val
	v.isSet = true
}

func (v NullableHandlersCreateTransactionResponseBody) IsSet() bool {
	return v.isSet
}

func (v *NullableHandlersCreateTransactionResponseBody) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHandlersCreateTransactionResponseBody(val *HandlersCreateTransactionResponseBody) *NullableHandlersCreateTransactionResponseBody {
	return &NullableHandlersCreateTransactionResponseBody{value: val, isSet: true}
}

func (v NullableHandlersCreateTransactionResponseBody) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHandlersCreateTransactionResponseBody) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
