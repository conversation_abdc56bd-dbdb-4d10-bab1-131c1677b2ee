.gitignore
.travis.yml
README.md
api/openapi.yaml
api_charge_overrides.go
api_charge_schedules.go
api_charge_schedules_api3.go
api_charging_profiles.go
api_charging_station_cache.go
api_commands.go
api_delegated_control_charging_profiles.go
api_delegated_control_charging_stations.go
api_delegated_control_intents.go
api_delegated_control_notifications.go
api_delegated_control_schedules.go
api_delegated_control_sessions.go
api_delegated_control_vehicles.go
api_energy_offer_status.go
api_flexibility_requests.go
api_health.go
api_rewards.go
client.go
configuration.go
git_push.sh
model_api3_charge_schedule.go
model_api3_patch_charge_schedule_body.go
model_api3_put_charge_schedule_body.go
model_cannot_meet_session_intent.go
model_charge_detail_dto.go
model_charge_override_request.go
model_charge_override_response.go
model_charge_schedule_dto.go
model_charge_schedule_status.go
model_charge_statistics_dto.go
model_charging_profile_delete_range_dto.go
model_charging_profile_dto.go
model_charging_profiles_request_dto.go
model_charging_schedule_dto.go
model_charging_schedule_period_dto.go
model_charging_station_response_dto.go
model_command_event.go
model_command_metadata.go
model_command_response.go
model_composite_schedule_dto.go
model_connected_charge_state.go
model_connected_stateful_vehicle_dto.go
model_create_flex_request_dto.go
model_create_flexibility_request_for_programme_200_response.go
model_create_vehicle_link_request_dto.go
model_create_vehicle_request_dto.go
model_data.go
model_delegated_control_charging_station_response_dto.go
model_delegated_control_charging_station_search_criteria_dto.go
model_delegated_control_charging_station_search_metadata_dto.go
model_delegated_control_charging_station_search_metadata_dto_pagination.go
model_delegated_control_charging_station_search_response_dto.go
model_effective_charge_schedules_dto.go
model_effective_charge_schedules_response_dto.go
model_effective_schedule_dto.go
model_energy_offer_status.go
model_energy_offer_status_response.go
model_enrolment_request_dto.go
model_evse_response.go
model_extended_vehicle_information.go
model_extended_vehicle_link_response_dto.go
model_extended_vehicle_links_response_dto.go
model_flex_request_limit_dto.go
model_flex_request_provider_dto.go
model_flex_request_response.go
model_flex_request_response_limit.go
model_flex_request_search_response.go
model_generic_charge_state.go
model_generic_stateful_vehicle_dto.go
model_get_api3_charge_schedules.go
model_get_charge_schedules_200_response_value_inner.go
model_health_controller_check_200_response.go
model_health_controller_check_200_response_info_value.go
model_health_controller_check_503_response.go
model_intervention_dto.go
model_meta.go
model_paginated_schedules_dto.go
model_reward_info_dto.go
model_reward_points_dto.go
model_set_delegated_control_intents_response_dto.go
model_trigger_command_request.go
model_tx_profile_info_dto.go
model_update_flex_request_dto.go
model_update_vehicle_by_id_200_response.go
model_update_vehicle_link_request_dto.go
model_update_vehicle_request_dto.go
model_validate_charging_profile_request_dto.go
model_validate_charging_profile_response_dto.go
model_vehicle_charge_info_dto.go
model_vehicle_charge_info_dto_charging_station.go
model_vehicle_charging_stations_response_dto.go
model_vehicle_information.go
model_vehicle_intent_entry_dto.go
model_vehicle_intents_request_dto.go
model_vehicle_intents_response_dto.go
model_vehicle_link_response_dto.go
model_vehicle_link_response_dto_vehicle.go
response.go
utils.go
