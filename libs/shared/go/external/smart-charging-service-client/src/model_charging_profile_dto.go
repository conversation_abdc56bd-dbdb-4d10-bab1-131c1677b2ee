/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ChargingProfileDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingProfileDto{}

// ChargingProfileDto struct for ChargingProfileDto
type ChargingProfileDto struct {
	// OCPP 1.6 charging profile, note that chargingProfileId is not required, and if it is provided it will be overwritten by this api
	ChargingProfile map[string]interface{} `json:"chargingProfile"`
}

type _ChargingProfileDto ChargingProfileDto

// NewChargingProfileDto instantiates a new ChargingProfileDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingProfileDto(chargingProfile map[string]interface{}) *ChargingProfileDto {
	this := ChargingProfileDto{}
	this.ChargingProfile = chargingProfile
	return &this
}

// NewChargingProfileDtoWithDefaults instantiates a new ChargingProfileDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingProfileDtoWithDefaults() *ChargingProfileDto {
	this := ChargingProfileDto{}
	return &this
}

// GetChargingProfile returns the ChargingProfile field value
func (o *ChargingProfileDto) GetChargingProfile() map[string]interface{} {
	if o == nil {
		var ret map[string]interface{}
		return ret
	}

	return o.ChargingProfile
}

// GetChargingProfileOk returns a tuple with the ChargingProfile field value
// and a boolean to check if the value has been set.
func (o *ChargingProfileDto) GetChargingProfileOk() (map[string]interface{}, bool) {
	if o == nil {
		return map[string]interface{}{}, false
	}
	return o.ChargingProfile, true
}

// SetChargingProfile sets field value
func (o *ChargingProfileDto) SetChargingProfile(v map[string]interface{}) {
	o.ChargingProfile = v
}

func (o ChargingProfileDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingProfileDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["chargingProfile"] = o.ChargingProfile
	return toSerialize, nil
}

func (o *ChargingProfileDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"chargingProfile",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingProfileDto := _ChargingProfileDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargingProfileDto)

	if err != nil {
		return err
	}

	*o = ChargingProfileDto(varChargingProfileDto)

	return err
}

type NullableChargingProfileDto struct {
	value *ChargingProfileDto
	isSet bool
}

func (v NullableChargingProfileDto) Get() *ChargingProfileDto {
	return v.value
}

func (v *NullableChargingProfileDto) Set(val *ChargingProfileDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingProfileDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingProfileDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingProfileDto(val *ChargingProfileDto) *NullableChargingProfileDto {
	return &NullableChargingProfileDto{value: val, isSet: true}
}

func (v NullableChargingProfileDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingProfileDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
