/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type EnergyOfferStatusAPI interface {

	/*
		GetChargingStationEnergyOfferStatus Returns if a charger should offer energy

		Returns if a charger should offer energy

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param evseId The evseId of the door
		@return ApiGetChargingStationEnergyOfferStatusRequest
	*/
	GetChargingStationEnergyOfferStatus(ctx context.Context, ppid string, evseId int32) ApiGetChargingStationEnergyOfferStatusRequest

	// GetChargingStationEnergyOfferStatusExecute executes the request
	//  @return EnergyOfferStatusResponse
	GetChargingStationEnergyOfferStatusExecute(r ApiGetChargingStationEnergyOfferStatusRequest) (*EnergyOfferStatusResponse, *http.Response, error)

	/*
		GetChargingStationEnergyOfferStatus_0 Returns if a charger should offer energy

		Returns if a charger should offer energy

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param evseId The evseId of the door
		@return ApiGetChargingStationEnergyOfferStatus_0Request
	*/
	GetChargingStationEnergyOfferStatus_1(ctx context.Context, ppid string, evseId int32) ApiGetChargingStationEnergyOfferStatus_0Request

	// GetChargingStationEnergyOfferStatus_1Execute executes the request
	//  @return EnergyOfferStatusResponse
	GetChargingStationEnergyOfferStatus_1Execute(r ApiGetChargingStationEnergyOfferStatus_0Request) (*EnergyOfferStatusResponse, *http.Response, error)
}

// EnergyOfferStatusAPIService EnergyOfferStatusAPI service
type EnergyOfferStatusAPIService service

type ApiGetChargingStationEnergyOfferStatusRequest struct {
	ctx        context.Context
	ApiService EnergyOfferStatusAPI
	ppid       string
	evseId       int32
	cacheControl *string
}

// Set to \&quot;refresh\&quot; to force a refresh
func (r ApiGetChargingStationEnergyOfferStatusRequest) CacheControl(cacheControl string) ApiGetChargingStationEnergyOfferStatusRequest {
	r.cacheControl = &cacheControl
	return r
}

func (r ApiGetChargingStationEnergyOfferStatusRequest) Execute() (*EnergyOfferStatusResponse, *http.Response, error) {
	return r.ApiService.GetChargingStationEnergyOfferStatusExecute(r)
}

/*
GetChargingStationEnergyOfferStatus Returns if a charger should offer energy

Returns if a charger should offer energy

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param evseId The evseId of the door
	@return ApiGetChargingStationEnergyOfferStatusRequest
*/
func (a *EnergyOfferStatusAPIService) GetChargingStationEnergyOfferStatus(ctx context.Context, ppid string, evseId int32) ApiGetChargingStationEnergyOfferStatusRequest {
	return ApiGetChargingStationEnergyOfferStatusRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		evseId:     evseId,
	}
}

// Execute executes the request
//
//	@return EnergyOfferStatusResponse
func (a *EnergyOfferStatusAPIService) GetChargingStationEnergyOfferStatusExecute(r ApiGetChargingStationEnergyOfferStatusRequest) (*EnergyOfferStatusResponse, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *EnergyOfferStatusResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "EnergyOfferStatusAPIService.GetChargingStationEnergyOfferStatus")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/evses/{evseId}/energy-offer-status"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"evseId"+"}", url.PathEscape(parameterValueToString(r.evseId, "evseId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.evseId < 1 {
		return localVarReturnValue, nil, reportError("evseId must be greater than 1")
	}
	if r.evseId > 32 {
		return localVarReturnValue, nil, reportError("evseId must be less than 32")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	if r.cacheControl != nil {
		parameterAddToHeaderOrQuery(localVarHeaderParams, "Cache-Control", r.cacheControl, "simple", "")
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetChargingStationEnergyOfferStatus_0Request struct {
	ctx        context.Context
	ApiService EnergyOfferStatusAPI
	ppid       string
	evseId       int32
	cacheControl *string
}

// Set to \&quot;refresh\&quot; to force a refresh
func (r ApiGetChargingStationEnergyOfferStatus_0Request) CacheControl(cacheControl string) ApiGetChargingStationEnergyOfferStatus_0Request {
	r.cacheControl = &cacheControl
	return r
}

func (r ApiGetChargingStationEnergyOfferStatus_0Request) Execute() (*EnergyOfferStatusResponse, *http.Response, error) {
	return r.ApiService.GetChargingStationEnergyOfferStatus_1Execute(r)
}

/*
GetChargingStationEnergyOfferStatus_0 Returns if a charger should offer energy

Returns if a charger should offer energy

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param evseId The evseId of the door
	@return ApiGetChargingStationEnergyOfferStatus_0Request
*/
func (a *EnergyOfferStatusAPIService) GetChargingStationEnergyOfferStatus_1(ctx context.Context, ppid string, evseId int32) ApiGetChargingStationEnergyOfferStatus_0Request {
	return ApiGetChargingStationEnergyOfferStatus_0Request{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		evseId:     evseId,
	}
}

// Execute executes the request
//
//	@return EnergyOfferStatusResponse
func (a *EnergyOfferStatusAPIService) GetChargingStationEnergyOfferStatus_1Execute(r ApiGetChargingStationEnergyOfferStatus_0Request) (*EnergyOfferStatusResponse, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *EnergyOfferStatusResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "EnergyOfferStatusAPIService.GetChargingStationEnergyOfferStatus_1")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/evses/{evseId}/energy-offer-status"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"evseId"+"}", url.PathEscape(parameterValueToString(r.evseId, "evseId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.evseId < 1 {
		return localVarReturnValue, nil, reportError("evseId must be greater than 1")
	}
	if r.evseId > 32 {
		return localVarReturnValue, nil, reportError("evseId must be less than 32")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	if r.cacheControl != nil {
		parameterAddToHeaderOrQuery(localVarHeaderParams, "Cache-Control", r.cacheControl, "simple", "")
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
