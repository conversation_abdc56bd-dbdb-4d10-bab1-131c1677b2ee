openapi: 3.0.0
info:
  contact: {}
  description: API for managing smart charging service api
  title: Smart Charging Service Api
  version: '1.0'
servers:
  - url: /
tags:
  - description: ''
    name: smart-charging-service-api
paths:
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_200_response'
          description: The Health Check is successful
        '503':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_503_response'
          description: The Health Check is not successful
      tags:
        - Health
  /charge-overrides:
    get:
      description:
        "Search for any overrides based on a PPID. If search criteria is\
        \ valid but no schedules are in effect then a 200 response is returned with\
        \ an empty array body `[]`. Also if the services does not find an EVSE for\
        \ the given criteria then return `[]`"
      operationId: search-active-overrides
      parameters:
        - description: The PPID of the charging station
          explode: true
          in: query
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChargeOverrideResponse'
                type: array
          description: The list of active overrides
        '400':
          description: The search criteria was invalid
        '502':
          description: Unsupported charging station
      summary: Search for any active overrides in place
      tags:
        - Charge Overrides
  /charging-stations/{ppid}/charge-overrides:
    delete:
      description:
        Removes any existing overrides that are in place for a given charging
        station (aka unit)
      operationId: clear-charging-station-charge-overrides
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: ''
        '404':
          description: Unit not found
        '502':
          description: Unsupported charging station
      summary: Removes an override for a given charging station (All EVSEs)
      tags:
        - Charge Overrides
    get:
      description: Gets all overrides for a given charging station (All EVSEs)
      operationId: get-charging-station-charge-overrides
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: Include past overrides
          explode: true
          in: query
          name: includePast
          required: false
          schema:
            example: true
            type: boolean
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChargeOverrideResponse'
                type: array
          description: ''
        '404':
          description: Unit not found
        '502':
          description: Unsupported charging station
      summary: Get all overrides for a given charging station (All EVSEs)
      tags:
        - Charge Overrides
    post:
      description:
        Override and schedules on the device (all evses) either indefinitely
        or until the specified end time
      operationId: create-charging-station-charge-override
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeOverrideRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChargeOverrideResponse'
                type: array
          description: ''
        '400':
          description: Request body invalid
        '404':
          description: Unit not found
        '502':
          description: Unsupported charging station
      summary:
        "Create an override for a given charger, that should be applied to\
        \ all EVSEs"
      tags:
        - Charge Overrides
  /charging-profile/{ppid}:
    post:
      description: Submit charging profiles for the given ppid
      operationId: submit-charging-profiles
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargingProfilesRequestDto'
        description: Body with list of charging profiles to submit
        required: true
      responses:
        '202':
          description: Charging profiles accepted
        '400':
          description: Charging profile not valid
        '404':
          description: Charging station not found
        '422':
          description: Charging station is not an arch5
      summary: Submit charging profiles
      tags:
        - Charging Profiles
  /charging-profile/{ppid}/stack-level-range:
    delete:
      description: Delete profiles in stack level range
      operationId: delete-profiles-in-stack-level-range
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargingProfileDeleteRangeDto'
        required: true
      responses:
        '204':
          description: Stack level range deleted
        '400':
          description: Stack level range not valid
        '404':
          description: Charging station not found
        '422':
          description: Charging station is not an arch5
      summary: Delete profiles in stack level range
      tags:
        - Charging Profiles
  /delegated-controls:
    get:
      description:
        Returns a delegated control charging stations object with the intent.
        The ppid is used as they key.
      operationId: get-delegated-control-charging-stations
      parameters:
        - description: The PPIDs of the charging stations
          explode: true
          in: query
          name: ppid
          required: true
          schema:
            example: 'PSL-000000,PSL-000001'
            items:
              type: string
            type: array
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                additionalProperties:
                  $ref: '#/components/schemas/DelegatedControlChargingStationResponseDto'
                type: object
          description:
            "Delegated control charging stations with linked vehicles and\
            \ intents, including either generic or connected stateful vehicle data"
      summary: Returns delegated control charging stations
      tags:
        - Delegated Control Charging Stations
  /delegated-controls/search:
    get:
      description:
        "Search criteria can be used to filter charging stations returned,\
        \ or omitted to return all charging stations"
      operationId: searchDelegatedControlChargingStations
      parameters:
        - description: Does the charging station have a active charging session
          explode: true
          in: query
          name: hasActiveChargingSession
          required: false
          schema:
            example: 'false'
            type: string
          style: form
        - description: The delegated control provider id
          explode: true
          in: query
          name: providerId
          required: false
          schema:
            example: 451c6a82-aba9-4596-a33c-248cdd7b1af2
            type: string
          style: form
        - description: 'Pagination option: items per page. Defaults to 10'
          explode: true
          in: query
          name: itemsPerPage
          required: false
          schema:
            example: 10
            type: number
          style: form
        - description: 'Pagination option: page number. Defaults to 1'
          explode: true
          in: query
          name: page
          required: false
          schema:
            example: 1
            type: number
          style: form
        - description: Does the charging station have a vehicle plugged in
          explode: true
          in: query
          name: hasVehiclePluggedIn
          required: false
          schema:
            example: 'true'
            type: string
          style: form
        - description: The delegated control status
          explode: true
          in: query
          name: status
          required: false
          schema:
            example: ACTIVE
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DelegatedControlChargingStationSearchResponseDto'
          description: ''
      summary: Search for delegated control charging stations that match given criteria
      tags:
        - Delegated Control Charging Stations
  /delegated-controls/{ppid}:
    delete:
      description: Delete a delegated control charging station by its PPID
      operationId: delete-delegated-control-charging-station-by-ppid
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: Delegated control charging station deleted successfully
        '404':
          description: Unit not found
      summary: Delete a delegated control charging station
      tags:
        - Delegated Control Charging Stations
    get:
      operationId: getDelegatedControlChargingStationByPpid
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DelegatedControlChargingStationResponseDto'
          description: ''
        '404':
          description: Delegated control charging station not found
      summary: Gets a delegated control charging stations details by its PPID
      tags:
        - Delegated Control Charging Stations
    put:
      description:
        Create a delegated control charging station and set the intent
        via providing a charge time
      operationId: create-delegated-control-charging-station
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnrolmentRequestDto'
        required: true
      responses:
        '404':
          description: Charging station not found
        '422':
          description:
            "Charging station is unsupported or is missing one of evse,\
            \ serial number, mac address"
      summary: Create a delegated control charging station and set the intent
      tags:
        - Delegated Control Charging Stations
  /delegated-controls/{ppid}/schedules:
    delete:
      description:
        This endpoint will clear the target schedule(s) for a delegated
        control charging station and set the default schedule (0030 - 0430 daily)
      operationId: clearTargetScheduleForChargingStation
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: No-charge schedules set successfully
        '404':
          description: Unit not found
        '422':
          description:
            Charging Station architecture info is not available or not
            supported
      summary: Clear target schedule(s) for a delegated control charging station
      tags:
        - Delegated Control Schedules
    put:
      description:
        Request target schedules be set for the likely connected or default
        vehicle of a delegated control charging station
      operationId: set-target-schedules-for-delegated-control-charging-station
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description:
            "What triggered this request (if not provided, this is assumed\
            \ to be PLUGIN)"
          explode: true
          in: query
          name: trigger
          required: false
          schema:
            enum:
              - PLUGIN
              - INTENT_UPDATE
            type: string
          style: form
      responses:
        '202':
          description: Request accepted
        '404':
          description: Unit not found
        '422':
          description: Charging Station is not a delegated control charging station
      summary:
        Request target schedules be set for connected vehicle connected to
        a delegated control charging station
      tags:
        - Delegated Control Schedules
  /delegated-controls/{ppid}/plug-in/notify:
    post:
      description: Notify plug-in
      operationId: delegate-control-notify-charger-plug-in
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: Plug-in notified successfully
      summary: Notify plug-in
      tags:
        - Delegated Control Notifications
  /delegated-controls/{ppid}/un-plug/notify:
    post:
      description: Notify un-plug
      operationId: delegate-control-notify-charger-un-plug
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: Un-Plug notified successfully
      summary: Notify un-plug
      tags:
        - Delegated Control Notifications
  /api3/charging-stations/{ppid}/charge-schedules/resend:
    patch:
      description: Resend charge schedules to the charging station
      operationId: resend-charge-schedules
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '204':
          description: The charge schedules were resent
      summary: Resend charge schedules
      tags:
        - Charge Schedules (API3)
  /api3/charging-stations/{ppid}/charge-schedules/{scheduleId}:
    patch:
      description: Update a charge schedule
      operationId: update-charging-station-charge-schedule
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The UID of the charge schedule
          explode: false
          in: path
          name: scheduleId
          required: true
          schema:
            example: 7261db3a-e1e2-4b27-a780-94d3644ae98f
            format: uuid
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/API3PatchChargeScheduleBody'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API3ChargeSchedule'
          description: The updated charge schedule
        '404':
          description: The charging station or charge schedule was not found
        '422':
          description: The charge schedule overlaps with another charge schedule
      summary: Update a charge schedule
      tags:
        - Charge Schedules (API3)
  /api3/charging-stations/{ppid}/charge-schedules:
    get:
      description: Get charging station charge schedules
      operationId: get-charging-station-charge-schedules
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetApi3ChargeSchedules'
          description: The charge schedules
        '404':
          description: The charging station was not found
      summary: Get charge schedules
      tags:
        - Charge Schedules (API3)
    put:
      description: Set charging station charge schedules
      operationId: set-charging-station-charge-schedules
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/API3PutChargeScheduleBody'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/API3ChargeSchedule'
                type: array
          description: The updated charge schedule
        '404':
          description: The charging station or charge schedule was not found
        '422':
          description: The charge schedule overlaps with another charge schedule
      summary: Set charge schedules
      tags:
        - Charge Schedules (API3)
  /charging-stations/{ppid}/flexibility-requests:
    delete:
      description: Delete all flexibility requests for a given charging station
      operationId: delete-flexibility-requests-for-charging-station
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Delete flexibility requests for charging station
      tags:
        - Flexibility Requests
    get:
      description: |-
        Get the active flexibility requests for a given charger
        Results are limited to the first 1000, either ordered by increasing end times
        Or by decreasing start times if the includePast query param is set to true
      operationId: get-active-charging-station-flexibility-requests
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
        - description: Include past requests (defaults to false) if "true"
          explode: true
          in: query
          name: includePast
          required: false
          schema:
            example: true
            type: boolean
          style: form
        - description: Include deleted (canalled) requests (defaults to false) if "true"
          explode: true
          in: query
          name: includeDeleted
          required: false
          schema:
            example: false
            type: boolean
          style: form
        - description: Return only requests starting after this date
          explode: true
          in: query
          name: from
          required: false
          schema:
            type: string
          style: form
        - description: Return only requests starting before this date
          explode: true
          in: query
          name: to
          required: false
          schema:
            type: string
          style: form
        - description: Set to "refresh" to force a refresh
          explode: false
          in: header
          name: Cache-Control
          required: false
          schema:
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlexRequestResponse'
                type: array
          description: All active flexibility requests for the given charger
        '404':
          description: Unit not found
      summary: Get active flexibility requests
      tags:
        - Flexibility Requests
    post:
      description: Create a flexibility request for a given charger
      operationId: create-charging-station-flexibility-request
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFlexRequestDto'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlexRequestResponse'
          description: ''
        '400':
          description: Invalid PPID
        '404':
          description: Unit not found
        '409':
          description: Conflict found with existing data
        '422':
          description: Ends before it starts
        '502':
          description: Unsupported charging station
      summary: Create flexibility request
      tags:
        - Flexibility Requests
  /flexibility-requests:
    get:
      description: Get a flexibility request given it's external provider info
      operationId: get-flexibility-request-by-provider-info
      parameters:
        - description: The name of the competition provider
          explode: true
          in: query
          name: providerName
          required: true
          schema:
            example: axle
            type: string
          style: form
        - description: The provider's id for this flex request
          explode: true
          in: query
          name: providerFlexRequestId
          required: true
          schema:
            example: d40ff7bccd944e4f
            type: string
          style: form
        - explode: true
          in: query
          name: ppid
          required: true
          schema:
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlexRequestResponse'
          description: The flexibility request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompetitionProviderNotFoundResponse'
          description: Competition provider not found
      summary: Get flexibility request by provider info
      tags:
        - Flexibility Requests
  /flexibility-requests/search:
    get:
      description: Search for flexibility requests given criteria
      operationId: searchFlexibilityRequests
      parameters:
        - description: The flex request start date/time
          explode: true
          in: query
          name: startAt
          required: true
          schema:
            example: 2024-08-01T00:00:00Z
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlexRequestSearchResponse'
          description: Search response
      summary: Search for flexibility requests given criteria
      tags:
        - Flexibility Requests
  /flexibility-requests/{id}:
    delete:
      description: Delete a flexibility request given its id
      operationId: delete-flexibility-request
      parameters:
        - explode: false
          in: path
          name: id
          required: true
          schema:
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Delete flexibility request
      tags:
        - Flexibility Requests
    put:
      description: Update a flexibility request given its id
      operationId: update-flexibility-request
      parameters:
        - description: The id of the flexibility request
          explode: false
          in: path
          name: id
          required: true
          schema:
            example: 571aefe2-a406-4cec-8b35-5466226e598e
            format: uuid
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFlexRequestDto'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlexRequestResponse'
          description: The updated flexibility request
        '404':
          description: Flexibility request not found
      summary: Update flexibility request
      tags:
        - Flexibility Requests
  /programmes/{programmeId}/flexibility-requests:
    post:
      description:
        Create a flexibility request for all charging stations in a given
        programme
      operationId: create-flexibility-request-for-programme
      parameters:
        - explode: false
          in: path
          name: programmeId
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFlexRequestDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/create_flexibility_request_for_programme_200_response'
          description: OK
        '403':
          description: Programme is not active
        '404':
          description: ''
      summary: Create flexibility request for all charging stations in a given programme
      tags:
        - Flexibility Requests
  /programmes/{programmeId}/flexibility-requests/submit-async:
    post:
      description:
        Create a flexibility request for all charging stations in a given
        programme asynchronously
      operationId: async-create-flexibility-request-for-programme
      parameters:
        - explode: false
          in: path
          name: programmeId
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFlexRequestDto'
        required: true
      responses:
        '202':
          description: Accepted
        '403':
          description: Programme is not active
        '404':
          description: ''
      summary:
        Create flexibility request for all charging stations in a given programme
        asynchronously
      tags:
        - Flexibility Requests
  /delegated-controls/charging-sessions/search:
    get:
      description: Search for delegated control sessions
      operationId: searchDelegatedControlSessions
      parameters:
        - description:
            The date and time the vehicle was plugged in. It must at least
            one hour ago
          explode: true
          in: query
          name: plugInFrom
          required: true
          schema:
            format: date-time
            type: string
          style: form
        - description:
            The date and time the vehicle was plugged out. It must be after
            plugInFrom
          explode: true
          in: query
          name: plugInTo
          required: false
          schema:
            example: 2021-06-01T00:00:00Z
            format: date-time
            nullable: false
            type: string
          style: form
        - description: A comma separated list of PPIDs
          explode: true
          in: query
          name: ppid
          required: false
          schema:
            example: 'PSL-00001,PSL-00002'
            items:
              type: string
            nullable: false
            type: array
          style: form
        - description: The current page number
          explode: true
          in: query
          name: page
          required: false
          schema:
            default: 1
            example: 1
            nullable: false
            type: number
          style: form
        - description: The number of items per page
          explode: true
          in: query
          name: itemsPerPage
          required: false
          schema:
            default: 10
            example: 10
            nullable: false
            type: number
          style: form
      responses:
        '200':
          description: ''
      summary: Search for delegated control sessions
      tags:
        - Delegated Control Sessions
  /delegated-controls/{ppid}/vehicles/{vehicleId}/intents:
    get:
      description: Get vehicle intents
      operationId: get-vehicle-intents
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The vehicleId associated to the charging station
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleIntentsResponseDto'
          description: ''
        '400':
          description: Invalid ppid or vehicleId
        '404':
          description: Unit not found
      summary: Get vehicle intents
      tags:
        - Delegated Control Intents
    put:
      description:
        Sets delegated control intents. The ppid and vehicleId are required
        as parameters.
      operationId: set-delegated-control-intents
      parameters:
        - description: The ppid of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
        - description: The vehicleId associated to the charging station
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleIntentsRequestDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetDelegatedControlIntentsResponseDto'
          description: ''
        '404':
          description:
            "Possible not found scenarios: charging station not found,\
            \ delegated control charging station not found, vehicle not found, or\
            \ delegated control charging station vehicles record not found."
        '422':
          description: Charging station is missing required details
      summary: Sets delegated control intents
      tags:
        - Delegated Control Intents
  /delegated-controls/{ppid}/vehicles/{vehicleId}/intents/current:
    get:
      operationId: DelegatedControlIntentController_getCurrentIntentDetail
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
        - explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            type: string
          style: simple
      responses:
        '200':
          description: ''
      tags:
        - Delegated Control Intents
  /delegated-controls/{ppid}/notify/intent-cannot-be-met:
    post:
      description: Notify intent cannot be met
      operationId: notify-intent-cannot-be-met
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CannotMeetSessionIntent'
        required: true
      responses:
        '200':
          description: Accepted
        '404':
          description: Unit not found
      summary: Notify that intent cannot be met
      tags:
        - Delegated Control Intents
  /delegated-controls/{ppid}/intent/calculate-charge-info-from-profile:
    post:
      description: Calculate charge info from profile
      operationId: calculate-charge-info-from-profile
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TxProfileInfoDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleChargeInfoDto'
          description: ''
        '404':
          description: Unit not found
      summary: Calculate charge info from ocpp1.6 profile
      tags:
        - Delegated Control Intents
  /delegated-controls/{ppid}/vehicles:
    get:
      description: Get all vehicles linked to a delegated control charging station
      operationId: get-delegated-control-charging-station-vehicles
      parameters:
        - description: The PPID of the delegated charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedVehicleLinksResponseDto'
          description: The vehicles linked to the delegated control charging station
        '400':
          description: Invalid ppid
      summary: Get delegated control charging station vehicles
      tags:
        - Delegated Control Vehicles
    post:
      description: Add vehicle to delegated control charging station
      operationId: add-vehicle-to-delegated-control-charging-station
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVehicleLinkRequestDto'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleLinkResponseDto'
          description: The data of a stateful vehicle
        '400':
          description: Invalid body
        '404':
          description: Unit not found
        '409':
          description: Vehicle already linked to the charging station
        '500':
          description: Internal server error
      summary: Add vehicle to delegated control charging station
      tags:
        - Delegated Control Vehicles
  /delegated-controls/{ppid}/vehicles/{vehicleId}:
    delete:
      description: Unlink a vehicle from a delegated control charging station
      operationId: unlink-vehicle-from-delegated-control-charging-station
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The vehicleId associated to the charging station
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      responses:
        '204':
          description: Vehicle unlinked from delegated control charging station
        '400':
          description: Invalid ppid or vehicleId
        '404':
          description: Unit not found
      summary: Unlink a vehicle from a delegated control charging station
      tags:
        - Delegated Control Vehicles
    get:
      description: Get vehicle
      operationId: get-vehicle
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The vehicleId associated to the charging station
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedVehicleLinkResponseDto'
          description: The data of a stateful vehicle
        '404':
          description: Unit or vehicle not found
      summary: Get vehicle
      tags:
        - Delegated Control Vehicles
    patch:
      description: 'Given a vehicle ID and new data, updates the vehicle'
      operationId: update-vehicle-by-id
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The vehicle ID
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 8bca9272-30c9-4075-a035-bf35876eb9ba
            format: uuid
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateVehicleLinkRequestDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/update_vehicle_by_id_200_response'
          description: The data of a stateful vehicle
        '400':
          description: The vehicle ID or body is invalid
        '404':
          description: Vehicle with the given ID not found
      summary: Update vehicle data by ID
      tags:
        - Delegated Control Vehicles
  /delegated-controls/vehicles/{vehicleId}/charging-stations:
    delete:
      description: Unlink a vehicle from all linked delegated control charging stations
      operationId: unlink-vehicle-from-delegated-control-charging-stations
      parameters:
        - description: The vehicleId associated to the charging station
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      responses:
        '204':
          description: Vehicle unlinked from all linked charging stations
        '400':
          description: Invalid vehicleId
      summary: Unlink a vehicle from all linked delegated control charging stations
      tags:
        - Delegated Control Vehicles
    get:
      description: Get all charging stations linked to a vehicle
      operationId: get-charging-stations-linked-to-vehicle
      parameters:
        - description: The vehicleId linked to the charging stations
          explode: false
          in: path
          name: vehicleId
          required: true
          schema:
            example: 43f7d305-da87-4f80-9f18-b485e5ea3b94
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleChargingStationsResponseDto'
          description: PPIDS of Charging stations linked to a vehicle
        '400':
          description: Invalid vehicleId
      summary: Get all charging stations linked to a vehicle
      tags:
        - Delegated Control Vehicles
  /commands/responses:
    post:
      operationId: postCommandResponse
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandResponse'
        required: true
      responses:
        '404':
          description: The command could not be found
      summary: Send a response from the connectivity commands API
      tags:
        - Commands
  /commands/trigger:
    post:
      operationId: triggerCommand
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TriggerCommandRequest'
        required: true
      responses:
        '201':
          description: ''
      summary: Trigger a command on the charging stations
      tags:
        - Commands
  /charging-stations/{ppid}/evses/{evseId}/energy-offer-status:
    get:
      description: Returns if a charger should offer energy
      operationId: get-charging-station-energy-offer-status
      parameters:
        - description: The PPID of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-000000
            type: string
          style: simple
        - description: The evseId of the door
          explode: false
          in: path
          name: evseId
          required: true
          schema:
            maximum: 32
            minimum: 1
            type: integer
          style: simple
        - description: Set to "refresh" to force a refresh
          explode: false
          in: header
          name: Cache-Control
          required: false
          schema:
            type: string
          style: simple
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnergyOfferStatusResponse'
          description: The energy offer status of the charging station
        '400':
          description: Invalid PPID
        '404':
          description: Unit not found
        '502':
          description: Unsupported charging station
      summary: Returns if a charger should offer energy
      tags:
        - Energy Offer Status
        - energy-offer-status
  /charge-schedules:
    get:
      description: Get all active charge schedules for the given ppids
      operationId: get-charge-schedules
      parameters:
        - description: Accepts comma separated ppid(s) to batch fetch their charge schedules
          explode: true
          in: query
          name: ppid
          required: true
          schema:
            items:
              type: string
            type: array
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                additionalProperties:
                  items:
                    $ref: '#/components/schemas/get_charge_schedules_200_response_value_inner'
                  type: array
                type: object
          description: The charge schedules for the given ppids
      summary: Get all active charge schedules
      tags:
        - Charge Schedules
  /charge-schedules/{ppid}/history:
    delete:
      description: Delete old charge schedules for the given ppid
      operationId: delete-charge-schedule-history
      parameters:
        - description: Accepts a single ppid to delete old charge schedules
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Delete old charge schedules
      tags:
        - Charge Schedules
    get:
      description: Get historical charge schedules for the given ppid
      operationId: get-charger-charge-schedule-history
      parameters:
        - description: Accepts a single ppid to fetch old charge schedules
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
        - description: Number of items to return
          explode: true
          in: query
          name: items
          required: false
          schema:
            default: 100
            example: 10
            type: number
          style: form
        - description: Page number
          explode: true
          in: query
          name: page
          required: false
          schema:
            default: 1
            example: 1
            type: number
          style: form
        - description: Start date of the charge schedule
          explode: true
          in: query
          name: from
          required: true
          schema:
            example: 2021-01-01
            type: string
          style: form
        - description: End date of the charge schedule
          explode: true
          in: query
          name: to
          required: true
          schema:
            example: 2021-01-01
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSchedulesDto'
          description: The historical charge schedules for the given ppid
      summary: Get historical charge schedules
      tags:
        - Charge Schedules
  /charge-schedules/effective:
    post:
      description:
        Get effective charge schedules for the given ppids in the time
        range
      operationId: get-effective-charge-schedules-in-time-range
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EffectiveChargeSchedulesDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/EffectiveChargeSchedulesResponseDto'
                type: array
          description: The effective charge schedules for the given ppids
        '400':
          description: ''
      summary: Get effective charge schedules in time range
      tags:
        - Charge Schedules
  /charge-schedules/charging-stations:
    get:
      operationId: ChargeScheduleController_getChargingStationsWithChargeSchedules
      parameters:
        - description: Number of items to return
          explode: true
          in: query
          name: items
          required: false
          schema:
            default: 100
            example: 10
            type: number
          style: form
        - description: Page number
          explode: true
          in: query
          name: page
          required: false
          schema:
            default: 1
            example: 1
            type: number
          style: form
        - description: Start date of the charge schedule
          explode: true
          in: query
          name: from
          required: true
          schema:
            example: 2021-01-01
            type: string
          style: form
      responses:
        '200':
          description: ''
      tags:
        - Charge Schedules
  /charge-schedules/{ppid}/composite:
    get:
      description:
        Retrieve the composite charge schedule from the charging by executing
        GetCompositeSchedule.conf
      operationId: get-composite-charge-schedule
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
        - description: The duration in seconds for which the composite schedule is requested
          explode: true
          in: query
          name: duration
          required: false
          schema:
            default: 7200
            example: 3600
            type: number
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompositeScheduleDto'
          description: The composite charge schedule for the given ppid
        '404':
          description: The charging station with the given ppid was not found
        '422':
          description: 'Invalid charging station type, only ARCH5 is supported'
        '500':
          description: 'Failed to get schedules, an internal error occurred'
      summary: Get composite charge schedule
      tags:
        - Charge Schedules
  /charge-schedules/{ppid}/default:
    put:
      operationId: setDefaultChargeSchedule
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Set the default charge schedule for a charging station
      tags:
        - Charge Schedules
  /charging-stations/{ppid}/cache:
    delete:
      description: This endpoint will clear the cache for a charging station
      operationId: clearCacheForChargingStation
      parameters:
        - description: The charging station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-12345
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Clear the cache for a charging station
      tags:
        - Charging Station Cache
  /delegated-controls/{ppid}/charging-profiles/validate:
    post:
      description:
        Returns a validated charging profile object with a valid flag.
        The flag indicates if the provided charging profile is valid or not.
      operationId: validateChargingProfile
      parameters:
        - description: The ppid of the charging station
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateChargingProfileRequestDto'
        description: The charging profile to validate
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateChargingProfileResponseDto'
          description: The validated charging profile
      summary: Returns the validated charging profile
      tags:
        - Delegated Control Charging Profiles
  /reward-points/{ppid}:
    post:
      description:
        "Get reward points from charge statistics. Note: this endpoint\
        \ is to be replaced by POST /reward-info/:ppid and should be considered deprecated."
      operationId: convert-charge-to-reward-points
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeStatisticsDto'
        description: Charge statistics
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RewardPointsDto'
          description: Reward information
        '400':
          description: Invalid charge statistics
        '404':
          description: Charging station not found
      summary: Convert a charge into reward points
      tags:
        - Rewards
  /reward-info/{ppid}:
    post:
      operationId: getChargingSessionRewardInfo
      parameters:
        - explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeStatisticsDto'
        description: Charge statistics
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RewardInfoDto'
          description: Reward information
      summary: Get reward information for a charging session
      tags:
        - Rewards
components:
  schemas:
    EvseResponse:
      properties:
        door:
          description: The door of the evse
          example: A
          type: string
        ocppEvseId:
          description: The ocppEvseId of the evse
          example: 1
          maximum: 32
          minimum: 1
          type: number
      required:
        - door
        - ocppEvseId
      type: object
    ChargingStationResponseDto:
      example:
        mpan: mpan
        id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
        ppid: PSL-000000
        addressId: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          description: The ID of the charging station
          example: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
          format: uuid
          nullable: true
          type: string
        ppid:
          example: PSL-000000
          type: string
        mpan:
          nullable: true
          type: string
        addressId:
          format: uuid
          nullable: true
          type: string
      required:
        - ppid
      type: object
    ChargeOverrideResponse:
      example:
        deletedAt: 2021-08-01T00:00:00Z
        requestedAt: 2021-08-01T00:00:00Z
        id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
        receivedAt: 2021-08-01T00:00:00Z
        endAt: 2021-08-01T00:00:00Z
        evse: ''
        chargingStation: ''
      properties:
        id:
          description: The ID of the charge override
          example: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
          format: uuid
          type: string
        requestedAt:
          description: The date and time the charge override was requested
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        receivedAt:
          description: The date and time the charge override was received by the api
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        endAt:
          description: The date and time the charge override should end
          example: 2021-08-01T00:00:00Z
          format: date-time
          nullable: true
          type: string
        evse:
          allOf:
            - $ref: '#/components/schemas/EvseResponse'
          description: The evse associated with the charge override
        chargingStation:
          allOf:
            - $ref: '#/components/schemas/ChargingStationResponseDto'
          description: The charging station associated with the charge override
        deletedAt:
          description: The date and time the charge override was deleted
          example: 2021-08-01T00:00:00Z
          format: date-time
          nullable: true
          type: string
      required:
        - chargingStation
        - deletedAt
        - endAt
        - evse
        - id
        - receivedAt
        - requestedAt
      type: object
    ChargeOverrideRequest:
      example:
        requestedAt: 2021-08-01T00:00:00Z
        endAt: 2021-08-01T00:00:00Z
      properties:
        requestedAt:
          description: The date and time the charge override was requested
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        endAt:
          description: The date and time the charge override should end
          example: 2021-08-01T00:00:00Z
          format: date-time
          nullable: true
          type: string
      required:
        - requestedAt
      type: object
    ChargingProfileDto:
      example:
        chargingProfile: '{}'
      properties:
        chargingProfile:
          description:
            "OCPP 1.6 charging profile, note that chargingProfileId is\
            \ not required, and if it is provided it will be overwritten by this api"
          type: object
      required:
        - chargingProfile
      type: object
    ChargingProfilesRequestDto:
      example:
        profiles:
          - chargingProfile: '{}'
          - chargingProfile: '{}'
      properties:
        profiles:
          description: Charging profiles
          items:
            $ref: '#/components/schemas/ChargingProfileDto'
          type: array
      required:
        - profiles
      type: object
    ChargingProfileDeleteRangeDto:
      example:
        start: 0.8008281904610115
        end: 6.027456183070403
      properties:
        start:
          description: Start of stack level range to delete (inclusive)
          type: number
        end:
          description: End of stack level range to delete (inclusive)
          type: number
      required:
        - end
        - start
      type: object
    VehicleInformation:
      properties:
        brand:
          description: The vehicle make
          example: Polestar
          nullable: true
          type: string
        model:
          description: The vehicle model
          example: '2'
          nullable: true
          type: string
        modelVariant:
          description: The vehicle model variant
          example: Long range
          nullable: true
          type: string
        vehicleRegistrationPlate:
          description: The vehicle registration plate
          example: ABC123
          nullable: true
          type: string
        displayName:
          description: The vehicle display name
          example: My car
          nullable: true
          type: string
        evDatabaseId:
          description: The vehicle EV database ID
          example: '1234567890'
          nullable: true
          type: string
      type: object
    ConnectedChargeState:
      properties:
        batteryCapacity:
          description: The vehicle battery capacity in kWh
          example: 78
          type: number
        batteryLevelPercent:
          description: The vehicle battery level in percent
          example: 78
          nullable: true
          type: number
        chargeLimitPercent:
          description: The vehicle charge limit in percent
          example: 78
          nullable: true
          type: number
        chargeRate:
          description: The vehicle charge rate in kW
          example: 78
          nullable: true
          type: number
        chargeTimeRemaining:
          description: The vehicle charge time remaining in minutes
          example: 78
          nullable: true
          type: number
        isCharging:
          description: The vehicle charging status
          example: true
          nullable: true
          type: boolean
        isFullyCharged:
          description: The vehicle fully charged status
          example: true
          nullable: true
          type: boolean
        isPluggedIn:
          description: The vehicle plugged in status
          example: true
          nullable: true
          type: boolean
        lastUpdated:
          description: The vehicle last updated timestamp
          example: 2021-08-12T09:00:00Z
          nullable: true
          type: string
        maxCurrent:
          description: The vehicle max current in A
          example: 32
          nullable: true
          type: number
        powerDeliveryState:
          description: The vehicle power delivery state
          enum:
            - UNPLUGGED
            - PLUGGED_IN:NO_POWER
            - PLUGGED_IN:STOPPED
            - PLUGGED_IN:COMPLETE
            - PLUGGED_IN:CHARGING
            - UNKNOWN
            - PLUGGED_IN:INITIALIZING
            - PLUGGED_IN:FAULT
          nullable: true
          type: string
        range:
          description: The vehicle range in km
          example: 300
          nullable: true
          type: number
      required:
        - batteryCapacity
        - batteryLevelPercent
        - chargeLimitPercent
        - chargeRate
        - chargeTimeRemaining
        - isCharging
        - isFullyCharged
        - isPluggedIn
        - lastUpdated
        - maxCurrent
        - powerDeliveryState
        - range
      type: object
    InterventionDto:
      properties:
        all:
          description: The endpoint to extract all interventions
          type: string
        chargeState:
          description: The individual interventions for charge state
          items:
            type: string
          type: array
        information:
          description: The individual interventions for vehicle information
          items:
            type: string
          type: array
      required:
        - all
        - chargeState
        - information
      type: object
    ConnectedStatefulVehicleDto:
      example:
        enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        chargeState: ''
        interventions: ''
        vehicleInformation: ''
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        id:
          description: The vehicle ID
          format: uuid
          type: string
        enodeUserId:
          description: The enode user ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        enodeVehicleId:
          description: The enode vehicle ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        vehicleInformation:
          allOf:
            - $ref: '#/components/schemas/VehicleInformation'
          description: The vehicle information
        chargeState:
          allOf:
            - $ref: '#/components/schemas/ConnectedChargeState'
          description: The vehicle charge state
        interventions:
          allOf:
            - $ref: '#/components/schemas/InterventionDto'
          description: The vehicle interventions
      required:
        - chargeState
        - id
        - interventions
        - vehicleInformation
      type: object
    GenericChargeState:
      properties:
        batteryCapacity:
          description: The vehicle battery capacity in kWh
          example: 78
          type: number
      required:
        - batteryCapacity
      type: object
    GenericStatefulVehicleDto:
      example:
        enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        chargeState: ''
        vehicleInformation: ''
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        id:
          description: The vehicle ID
          format: uuid
          type: string
        enodeUserId:
          description: The enode user ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        enodeVehicleId:
          description: The vehicle enode ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        vehicleInformation:
          allOf:
            - $ref: '#/components/schemas/VehicleInformation'
          description: The vehicle information
        chargeState:
          allOf:
            - $ref: '#/components/schemas/GenericChargeState'
          description: The vehicle charge state
      required:
        - chargeState
        - id
        - vehicleInformation
      type: object
    VehicleIntentEntryDto:
      example:
        chargeByTime: 07:00:00
        dayOfWeek: MONDAY
        chargeKWh: 28
      properties:
        chargeByTime:
          description: Time by which the charging should be completed (HH:MM:SS format)
          example: 07:00:00
          type: string
        chargeKWh:
          description: Number of kWh to charge
          example: 28
          type: number
        dayOfWeek:
          description:
            "The day of the week for the charging intent. Valid values:\
            \ MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY."
          enum:
            - MONDAY
            - TUESDAY
            - WEDNESDAY
            - THURSDAY
            - FRIDAY
            - SATURDAY
            - SUNDAY
          example: MONDAY
          type: string
      required:
        - chargeByTime
        - chargeKWh
        - dayOfWeek
      type: object
    VehicleIntentsResponseDto:
      example:
        details:
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
        id: id
        maxPrice: 0.5
      properties:
        id:
          description: The vehicle ID
          type: string
        maxPrice:
          description: The maximum price.
          example: 0.5
          nullable: true
          type: number
        details:
          description: The vehicle intents
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDto'
          type: array
      required:
        - details
        - id
        - maxPrice
      type: object
    VehicleLinkResponseDto:
      example:
        intents:
          details:
            - chargeByTime: 07:00:00
              dayOfWeek: MONDAY
              chargeKWh: 28
            - chargeByTime: 07:00:00
              dayOfWeek: MONDAY
              chargeKWh: 28
          id: id
          maxPrice: 0.5
        isPluggedInToThisCharger: true
        id: id
        vehicle:
          enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          chargeState: ''
          interventions: ''
          vehicleInformation: ''
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        id:
          type: string
        isPluggedInToThisCharger:
          type: boolean
        vehicle:
          $ref: '#/components/schemas/VehicleLinkResponseDto_vehicle'
        intents:
          $ref: '#/components/schemas/VehicleIntentsResponseDto'
      required:
        - id
        - intents
        - isPluggedInToThisCharger
        - vehicle
      type: object
    DelegatedControlChargingStationResponseDto:
      example:
        statusEffectiveFrom: 2000-01-23T04:56:07.000+00:00
        createdAt: 2021-01-01T00:00:00Z
        thirdPartyManagerProviderId: thirdPartyManagerProviderId
        id: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        vehicleLinks:
          - intents:
              details:
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
              id: id
              maxPrice: 0.5
            isPluggedInToThisCharger: true
            id: id
            vehicle:
              enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              chargeState: ''
              interventions: ''
              vehicleInformation: ''
              id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
              enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          - intents:
              details:
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
              id: id
              maxPrice: 0.5
            isPluggedInToThisCharger: true
            id: id
            vehicle:
              enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              chargeState: ''
              interventions: ''
              vehicleInformation: ''
              id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
              enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        ppid: PSL-123456
        status: ACTIVE
      properties:
        id:
          description: The charging station ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          type: string
        ppid:
          description: The charging station PPID
          example: PSL-123456
          type: string
        status:
          description: The status of the charging station
          enum:
            - CANDIDATE
            - PENDING
            - ACTIVE
            - INACTIVE
            - UNKNOWN
          example: ACTIVE
          type: string
        statusEffectiveFrom:
          description: When the current status became effective
          format: date-time
          type: string
        createdAt:
          description: The charging station creation date
          example: 2021-01-01T00:00:00Z
          format: date-time
          type: string
        vehicleLinks:
          items:
            $ref: '#/components/schemas/VehicleLinkResponseDto'
          type: array
        thirdPartyManagerProviderId:
          description: The third party manager provider ID
          nullable: true
          type: string
      required:
        - createdAt
        - id
        - ppid
      type: object
    DelegatedControlChargingStationSearchCriteriaDto:
      example:
        providerId: providerId
        hasVehiclePluggedIn: true
        status: CANDIDATE
      properties:
        status:
          enum:
            - CANDIDATE
            - PENDING
            - ACTIVE
            - INACTIVE
            - UNKNOWN
          type: string
        hasVehiclePluggedIn:
          type: boolean
        providerId:
          type: string
      type: object
    DelegatedControlChargingStationSearchMetadataDto:
      example:
        pagination:
          total: 1
          pages: 1
          itemsPerPage: 1
          page: 1
        criteria:
          providerId: providerId
          hasVehiclePluggedIn: true
          status: CANDIDATE
      properties:
        pagination:
          $ref: '#/components/schemas/DelegatedControlChargingStationSearchMetadataDto_pagination'
        criteria:
          $ref: '#/components/schemas/DelegatedControlChargingStationSearchCriteriaDto'
      required:
        - criteria
        - pagination
      type: object
    DelegatedControlChargingStationSearchResponseDto:
      example:
        metadata:
          pagination:
            total: 1
            pages: 1
            itemsPerPage: 1
            page: 1
          criteria:
            providerId: providerId
            hasVehiclePluggedIn: true
            status: CANDIDATE
        data:
          - statusEffectiveFrom: 2000-01-23T04:56:07.000+00:00
            createdAt: 2021-01-01T00:00:00Z
            thirdPartyManagerProviderId: thirdPartyManagerProviderId
            id: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
            vehicleLinks:
              - intents:
                  details:
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                  id: id
                  maxPrice: 0.5
                isPluggedInToThisCharger: true
                id: id
                vehicle:
                  enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
                  chargeState: ''
                  interventions: ''
                  vehicleInformation: ''
                  id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
                  enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              - intents:
                  details:
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                  id: id
                  maxPrice: 0.5
                isPluggedInToThisCharger: true
                id: id
                vehicle:
                  enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
                  chargeState: ''
                  interventions: ''
                  vehicleInformation: ''
                  id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
                  enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
            ppid: PSL-123456
            status: ACTIVE
          - statusEffectiveFrom: 2000-01-23T04:56:07.000+00:00
            createdAt: 2021-01-01T00:00:00Z
            thirdPartyManagerProviderId: thirdPartyManagerProviderId
            id: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
            vehicleLinks:
              - intents:
                  details:
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                  id: id
                  maxPrice: 0.5
                isPluggedInToThisCharger: true
                id: id
                vehicle:
                  enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
                  chargeState: ''
                  interventions: ''
                  vehicleInformation: ''
                  id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
                  enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              - intents:
                  details:
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                    - chargeByTime: 07:00:00
                      dayOfWeek: MONDAY
                      chargeKWh: 28
                  id: id
                  maxPrice: 0.5
                isPluggedInToThisCharger: true
                id: id
                vehicle:
                  enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
                  chargeState: ''
                  interventions: ''
                  vehicleInformation: ''
                  id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
                  enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
            ppid: PSL-123456
            status: ACTIVE
      properties:
        data:
          items:
            $ref: '#/components/schemas/DelegatedControlChargingStationResponseDto'
          type: array
        metadata:
          $ref: '#/components/schemas/DelegatedControlChargingStationSearchMetadataDto'
      required:
        - data
        - metadata
      type: object
    EnrolmentRequestDto:
      example:
        mpan: '1100034567890'
        postcode: SW1A 1AA
        providerName: axle
        status: CANDIDATE
      properties:
        providerName:
          description: The provider name
          example: axle
          nullable: true
          type: string
        status:
          description: The initial status
          enum:
            - CANDIDATE
            - PENDING
            - ACTIVE
            - INACTIVE
            - UNKNOWN
          example: CANDIDATE
          type: string
        mpan:
          description: The charging station's 13 digit mpan
          example: '1100034567890'
          type: string
        postcode:
          description: The charging station's postcode
          example: SW1A 1AA
          type: string
      type: object
    ChargeScheduleStatus:
      properties:
        is_active:
          title: Charge schedule status
          type: boolean
      required:
        - is_active
      type: object
    API3PatchChargeScheduleBody:
      example:
        end_day: 6.027456183070403
        start_time: 18:00:00
        start_day: 0.8008281904610115
        end_time: 06:00:00
        status: ''
      properties:
        start_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: Start day of the week
          type: number
        start_time:
          example: 18:00:00
          title: Start time of the day
          type: object
        end_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: End day of the week
          type: number
        end_time:
          example: 06:00:00
          title: End time of the day
          type: object
        status:
          allOf:
            - $ref: '#/components/schemas/ChargeScheduleStatus'
          title: Charge schedule status
      type: object
    API3ChargeSchedule:
      example:
        end_day: 6.027456183070403
        uid: uid
        start_time: 18:00:00
        start_day: 0.8008281904610115
        end_time: 06:00:00
        status: ''
      properties:
        uid:
          title: Charge schedule uid
          type: string
        start_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: Start day of the week
          type: number
        start_time:
          example: 18:00:00
          title: Start time of the day
          type: string
        end_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: End day of the week
          type: number
        end_time:
          example: 06:00:00
          title: End time of the day
          type: string
        status:
          allOf:
            - $ref: '#/components/schemas/ChargeScheduleStatus'
          title: Charge schedule status
      required:
        - end_day
        - end_time
        - start_day
        - start_time
        - status
        - uid
      type: object
    Data:
      example:
        end_day: 6.027456183070403
        uid: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        start_time: 18:00:00
        start_day: 0.8008281904610115
        end_time: 06:00:00
        status: ''
      properties:
        uid:
          format: uuid
          title:
            "The ID of the charge schedule, for when updating an existing charge\
            \ schedule"
          type: string
        start_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: Start day of the week
          type: number
        start_time:
          example: 18:00:00
          title: Start time of the day
          type: string
        end_day:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          title: End day of the week
          type: number
        end_time:
          example: 06:00:00
          title: End time of the day
          type: string
        status:
          allOf:
            - $ref: '#/components/schemas/ChargeScheduleStatus'
          title: Charge schedule status
      required:
        - end_day
        - end_time
        - start_day
        - start_time
        - status
      type: object
    API3PutChargeScheduleBody:
      example:
        data:
          - end_day: 6.027456183070403
            uid: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            start_time: 18:00:00
            start_day: 0.8008281904610115
            end_time: 06:00:00
            status: ''
          - end_day: 6.027456183070403
            uid: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            start_time: 18:00:00
            start_day: 0.8008281904610115
            end_time: 06:00:00
            status: ''
      properties:
        data:
          items:
            $ref: '#/components/schemas/Data'
          title: Charge schedule data
          type: array
      required:
        - data
      type: object
    Pagination:
      properties: {}
      type: object
    Meta:
      properties:
        pagination:
          allOf:
            - $ref: '#/components/schemas/Pagination'
          title: Pagination
      required:
        - pagination
      type: object
    GetApi3ChargeSchedules:
      example:
        data:
          - end_day: 6.027456183070403
            uid: uid
            start_time: 18:00:00
            start_day: 0.8008281904610115
            end_time: 06:00:00
            status: ''
          - end_day: 6.027456183070403
            uid: uid
            start_time: 18:00:00
            start_day: 0.8008281904610115
            end_time: 06:00:00
            status: ''
        meta: ''
      properties:
        data:
          items:
            $ref: '#/components/schemas/API3ChargeSchedule'
          title: Charge schedules
          type: array
        meta:
          allOf:
            - $ref: '#/components/schemas/Meta'
          title: Meta
      required:
        - data
        - meta
      type: object
    FlexRequestLimitDto:
      properties:
        unit:
          description: The unit of the limit
          enum:
            - AMP
            - KW
          type: string
        value:
          description: The value of the limit
          type: number
      required:
        - unit
        - value
      type: object
    FlexRequestProviderDto:
      properties:
        name:
          description: The name of the provider
          type: string
        externalFlexRequestId:
          description: The provider's id for this flex request
          type: string
      required:
        - externalFlexRequestId
        - name
      type: object
    CreateFlexRequestDto:
      example:
        requestedAt: 2021-08-01T00:00:00Z
        provider: ''
        limit: ''
        triggerMessageId: triggerMessageId
        endAt: 2021-08-01T00:00:00Z
        startAt: 2021-08-01T00:00:00Z
        direction: INCREASE
      properties:
        requestedAt:
          description: The date and time the flexibility request was requested
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        startAt:
          description: The date and time the flexibility request will start
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        endAt:
          description: The date and time the flexibility request will end
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        direction:
          description: The direction of the flexibility request
          enum:
            - INCREASE
            - REDUCE
          type: string
        limit:
          allOf:
            - $ref: '#/components/schemas/FlexRequestLimitDto'
          description: The limit of the flexibility request
        provider:
          allOf:
            - $ref: '#/components/schemas/FlexRequestProviderDto'
          description: Details of the provider which originated this flex request
        triggerMessageId:
          description:
            "For a Programme-based flex request, the id of the message\
            \ which triggered the request"
          type: string
      required:
        - direction
        - endAt
        - requestedAt
        - startAt
      type: object
    FlexRequestResponse:
      example:
        deletedAt: 2021-08-01T00:00:00Z
        requestedAt: 2021-08-01T00:00:00Z
        limit:
          unit: AMP
          value: 0.8008281904610115
        id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
        endAt: 2021-08-01T00:00:00Z
        chargingStation:
          mpan: mpan
          id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
          ppid: PSL-000000
          addressId: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        startAt: 2021-08-01T00:00:00Z
        direction: INCREASE
      properties:
        id:
          description: The ID of the flexibility request
          example: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
          format: uuid
          type: string
        chargingStation:
          $ref: '#/components/schemas/ChargingStationResponseDto'
        requestedAt:
          description: The date and time the flexibility request was requested
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        startAt:
          description: The date and time the flexibility request will start
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        endAt:
          description: The date and time the flexibility request will end
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        direction:
          description: The direction of the flexibility request
          enum:
            - INCREASE
            - REDUCE
          type: string
        limit:
          $ref: '#/components/schemas/FlexRequestResponse_limit'
        deletedAt:
          description:
            The date and time the flexibility request was deleted (cancelled)
            if applicable
          example: 2021-08-01T00:00:00Z
          format: date-time
          nullable: true
          type: string
      required:
        - chargingStation
        - direction
        - id
        - limit
        - requestedAt
        - startAt
      type: object
    CompetitionProviderNotFoundResponse:
      properties: {}
      type: object
    FlexRequestSearchResponse:
      example:
        data:
          - deletedAt: 2021-08-01T00:00:00Z
            requestedAt: 2021-08-01T00:00:00Z
            limit:
              unit: AMP
              value: 0.8008281904610115
            id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
            endAt: 2021-08-01T00:00:00Z
            chargingStation:
              mpan: mpan
              id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
              ppid: PSL-000000
              addressId: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            startAt: 2021-08-01T00:00:00Z
            direction: INCREASE
          - deletedAt: 2021-08-01T00:00:00Z
            requestedAt: 2021-08-01T00:00:00Z
            limit:
              unit: AMP
              value: 0.8008281904610115
            id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
            endAt: 2021-08-01T00:00:00Z
            chargingStation:
              mpan: mpan
              id: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
              ppid: PSL-000000
              addressId: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            startAt: 2021-08-01T00:00:00Z
            direction: INCREASE
      properties:
        data:
          description: The list of matching flex requests
          items:
            $ref: '#/components/schemas/FlexRequestResponse'
          type: array
      required:
        - data
      type: object
    UpdateFlexRequestDto:
      example:
        endAt: 2000-01-23T04:56:07.000+00:00
        startAt: 2000-01-23T04:56:07.000+00:00
      properties:
        startAt:
          description: The date and time the flexibility request will start
          format: date-time
          type: string
        endAt:
          description: The date and time the flexibility request will end
          format: date-time
          type: string
      required:
        - endAt
        - startAt
      type: object
    VehicleIntentsRequestDto:
      example:
        intentDetails:
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
        maxPrice: 0.5
      properties:
        intentDetails:
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDto'
          type: array
        maxPrice:
          description: The maximum price.
          example: 0.5
          type: number
      required:
        - intentDetails
      type: object
    SetDelegatedControlIntentsResponseDto:
      example:
        createdAt: 2024-07-31T12:34:56.789Z
        intentDetails:
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
        id: 93e7e441-8508-4b79-8cf2-b786b4b46304
        maxPrice: 0.5
        delegatedControlChargingStationVehicleId: adc6e152-1778-4faa-b71c-194af592f23c
        updatedAt: 2024-07-31T12:34:56.789Z
      properties:
        id:
          example: 93e7e441-8508-4b79-8cf2-b786b4b46304
          type: string
        delegatedControlChargingStationVehicleId:
          example: adc6e152-1778-4faa-b71c-194af592f23c
          type: string
        intentDetails:
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDto'
          type: array
        maxPrice:
          example: 0.5
          nullable: true
          type: number
        createdAt:
          example: 2024-07-31T12:34:56.789Z
          format: date-time
          type: string
        updatedAt:
          example: 2024-07-31T12:34:56.789Z
          format: date-time
          type: string
      required:
        - createdAt
        - delegatedControlChargingStationVehicleId
        - id
        - intentDetails
        - maxPrice
        - updatedAt
      type: object
    CannotMeetSessionIntent:
      example:
        expectedChargeByTarget_kWh: 0.8008281904610115
        cannotMeetTargetReason: PRICE
        fullChargeByTime: 2000-01-23T04:56:07.000+00:00
        expectedChargeByTargetPercent: 6.027456183070403
      properties:
        cannotMeetTargetReason:
          enum:
            - PRICE
            - TIME
          type: string
        expectedChargeByTarget_kWh:
          nullable: true
          type: number
        expectedChargeByTargetPercent:
          nullable: true
          type: number
        fullChargeByTime:
          format: date-time
          nullable: true
          type: string
      required:
        - cannotMeetTargetReason
        - expectedChargeByTargetPercent
        - expectedChargeByTarget_kWh
        - fullChargeByTime
      type: object
    TxProfileInfoDto:
      example:
        profile: '{}'
      properties:
        profile:
          description: ocpp TxProfile charging profile
          type: object
      required:
        - profile
      type: object
    ChargeDetailDto:
      example:
        expectedChargeByTarget_kWh: 0.8008281904610115
        fullChargeByTime: 2000-01-23T04:56:07.000+00:00
        expectedChargeByTargetPercent: 6.027456183070403
      properties:
        expectedChargeByTarget_kWh:
          nullable: true
          type: number
        expectedChargeByTargetPercent:
          nullable: true
          type: number
        fullChargeByTime:
          format: date-time
          nullable: true
          type: string
      required:
        - expectedChargeByTargetPercent
        - expectedChargeByTarget_kWh
        - fullChargeByTime
      type: object
    VehicleChargeInfoDto:
      example:
        chargeDetail:
          expectedChargeByTarget_kWh: 0.8008281904610115
          fullChargeByTime: 2000-01-23T04:56:07.000+00:00
          expectedChargeByTargetPercent: 6.027456183070403
        cannotMeetTargetReason: PRICE
        canMeetTarget: true
        chargingStation:
          ppid: ppid
      properties:
        canMeetTarget:
          type: boolean
        cannotMeetTargetReason:
          enum:
            - PRICE
            - TIME
          nullable: true
          type: string
        chargeDetail:
          $ref: '#/components/schemas/ChargeDetailDto'
        chargingStation:
          $ref: '#/components/schemas/VehicleChargeInfoDto_chargingStation'
      required:
        - canMeetTarget
        - cannotMeetTargetReason
        - chargeDetail
        - chargingStation
      type: object
    ExtendedVehicleLinkResponseDto:
      example:
        intents:
          details:
            - chargeByTime: 07:00:00
              dayOfWeek: MONDAY
              chargeKWh: 28
            - chargeByTime: 07:00:00
              dayOfWeek: MONDAY
              chargeKWh: 28
          id: id
          maxPrice: 0.5
        isPluggedInToThisCharger: true
        currentIntent: ''
        id: id
        vehicle:
          enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          chargeState: ''
          interventions: ''
          vehicleInformation: ''
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        id:
          type: string
        isPluggedInToThisCharger:
          type: boolean
        vehicle:
          $ref: '#/components/schemas/VehicleLinkResponseDto_vehicle'
        intents:
          $ref: '#/components/schemas/VehicleIntentsResponseDto'
        currentIntent:
          allOf:
            - $ref: '#/components/schemas/VehicleChargeInfoDto'
          nullable: true
      required:
        - currentIntent
        - id
        - intents
        - isPluggedInToThisCharger
        - vehicle
      type: object
    ExtendedVehicleLinksResponseDto:
      example:
        data:
          - intents:
              details:
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
              id: id
              maxPrice: 0.5
            isPluggedInToThisCharger: true
            currentIntent: ''
            id: id
            vehicle:
              enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              chargeState: ''
              interventions: ''
              vehicleInformation: ''
              id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
              enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          - intents:
              details:
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
                - chargeByTime: 07:00:00
                  dayOfWeek: MONDAY
                  chargeKWh: 28
              id: id
              maxPrice: 0.5
            isPluggedInToThisCharger: true
            currentIntent: ''
            id: id
            vehicle:
              enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
              chargeState: ''
              interventions: ''
              vehicleInformation: ''
              id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
              enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        data:
          items:
            $ref: '#/components/schemas/ExtendedVehicleLinkResponseDto'
          type: array
      required:
        - data
      type: object
    ExtendedVehicleInformation:
      properties:
        brand:
          description: The vehicle make
          example: Polestar
          nullable: true
          type: string
        model:
          description: The vehicle model
          example: '2'
          nullable: true
          type: string
        modelVariant:
          description: The vehicle model variant
          example: Long range
          nullable: true
          type: string
        vehicleRegistrationPlate:
          description: The vehicle registration plate
          example: ABC123
          nullable: true
          type: string
        displayName:
          description: The vehicle display name
          example: My car
          nullable: true
          type: string
        evDatabaseId:
          description: The vehicle EV database ID
          example: '1234567890'
          nullable: true
          type: string
        vin:
          description: The vehicle VIN
          example: '1234567890'
          nullable: true
          type: string
      type: object
    UpdateVehicleRequestDto:
      example:
        enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        chargeState: ''
        vehicleInformation: ''
        enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        vehicleInformation:
          allOf:
            - $ref: '#/components/schemas/ExtendedVehicleInformation'
          description: Vehicle information
        chargeState:
          allOf:
            - $ref: '#/components/schemas/GenericChargeState'
          description: The vehicle charge state
        enodeUserId:
          description: The vehicle user ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        enodeVehicleId:
          description: The vehicle enode ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
      type: object
    UpdateVehicleLinkRequestDto:
      example:
        isPluggedInToThisCharger: true
        vehicle:
          enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          chargeState: ''
          vehicleInformation: ''
          enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        isPluggedInToThisCharger:
          type: boolean
        vehicle:
          $ref: '#/components/schemas/UpdateVehicleRequestDto'
      type: object
    CreateVehicleRequestDto:
      example:
        enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        chargeState: ''
        vehicleInformation: ''
        enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        vehicleInformation:
          allOf:
            - $ref: '#/components/schemas/ExtendedVehicleInformation'
          description: Vehicle information
        chargeState:
          allOf:
            - $ref: '#/components/schemas/GenericChargeState'
          description: The vehicle charge state
        enodeUserId:
          description: The vehicle user ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
        enodeVehicleId:
          description: The vehicle enode ID
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
          type: string
      required:
        - chargeState
      type: object
    CreateVehicleLinkRequestDto:
      example:
        intents:
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
          - chargeByTime: 07:00:00
            dayOfWeek: MONDAY
            chargeKWh: 28
        vehicle:
          enodeVehicleId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          chargeState: ''
          vehicleInformation: ''
          enodeUserId: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
      properties:
        vehicle:
          $ref: '#/components/schemas/CreateVehicleRequestDto'
        intents:
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDto'
          type: array
      required:
        - intents
        - vehicle
      type: object
    VehicleChargingStationsResponseDto:
      example:
        data:
          - PSL-123456
          - PSL-654321
      properties:
        data:
          description:
            An array of charging station ppids that the vehicle is linked
            to
          example:
            - PSL-123456
            - PSL-654321
          items:
            type: string
          type: array
      required:
        - data
      type: object
    CommandMetadata:
      properties:
        ppid:
          description: The charging station PPID
          type: string
      required:
        - ppid
      type: object
    CommandEvent:
      properties:
        messageId:
          description: The command message ID
          type: string
        payload:
          description: The command payload
          type: string
        errorCode:
          description: The command error code
          type: string
        errorDescription:
          description: The command error description
          type: string
        errorDetails:
          description: The command error details
          type: string
      required:
        - messageId
      type: object
    CommandResponse:
      example:
        responseType: RES
        metadata: ''
        protocol: protocol
        clientRef: clientRef
        id: id
        type: ClearChargingProfile
        event: ''
        receivedAt: 2021-08-01T00:00:00Z
      properties:
        id:
          description: The command ID
          type: string
        type:
          description: The command type
          enum:
            - ClearChargingProfile
            - SetChargingProfile
          type: string
        responseType:
          description: The command response type
          enum:
            - RES
            - ERR
          type: string
        clientRef:
          description: The client reference
          type: string
        metadata:
          allOf:
            - $ref: '#/components/schemas/CommandMetadata'
          description: The metadata
        event:
          allOf:
            - $ref: '#/components/schemas/CommandEvent'
          description: The event
        protocol:
          description: The protocol
          type: string
        receivedAt:
          description: The date and time the command response was received
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
      required:
        - clientRef
        - event
        - id
        - metadata
        - protocol
        - receivedAt
        - responseType
        - type
      type: object
    TriggerCommandRequest:
      example:
        ppids:
          - ppids
          - ppids
        command: Heartbeat
      properties:
        ppids:
          description: A list of PPIDs to send the command to
          items:
            type: string
          type: array
        command:
          description: The command to send to the charging stations
          enum:
            - Heartbeat
          type: string
      required:
        - command
        - ppids
      type: object
    EnergyOfferStatus:
      properties:
        isOfferingEnergy:
          description: Whether the charging station is offering energy
          example: false
          type: boolean
        until:
          description:
            The date and time the reason for the offering energy status
            will change
          example: 2021-08-01T00:00:00Z
          format: date-time
          nullable: true
          type: string
        reason:
          description: The reason the for the offering energy
          enum:
            - CHARGE_SCHEDULE
            - FLEX_REQUEST
            - CHARGE_NOW
            - UNKNOWN
          example: CHARGE_NOW
          type: string
        randomDelay:
          nullable: true
          type: boolean
      required:
        - isOfferingEnergy
        - reason
        - until
      type: object
    EnergyOfferStatusResponse:
      example:
        evseId: 1
        energyOfferStatus: ''
        ppid: PSL-000000
      properties:
        ppid:
          description: The PPID of the charging station
          example: PSL-000000
          type: string
        evseId:
          description: The ID of the evse
          example: 1
          type: number
        energyOfferStatus:
          allOf:
            - $ref: '#/components/schemas/EnergyOfferStatus'
          description: The status of the energy offer
      required:
        - energyOfferStatus
        - evseId
        - ppid
      type: object
    ChargeScheduleDto:
      example:
        createdAt: 2021-01-01T00:00:00Z
        deletedAt: 2021-01-01T00:00:00Z
        startDay: 1
        endDay: 7
        startTime: 00:00:00
        endTime: 23:59:59
      properties:
        startDay:
          description: The weekday the charge schedule starts on
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          example: 1
          type: number
        endDay:
          description: The weekday the charge schedule ends on
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          example: 7
          type: number
        startTime:
          description: The time the charge schedule starts
          example: 00:00:00
          type: string
        endTime:
          description: The time the charge schedule ends
          example: 23:59:59
          type: string
        createdAt:
          description: The date the charge schedule was created
          example: 2021-01-01T00:00:00Z
          format: date-time
          type: string
        deletedAt:
          description: The date the charge schedule was last updated
          example: 2021-01-01T00:00:00Z
          format: date-time
          type: string
      required:
        - createdAt
        - deletedAt
        - endDay
        - endTime
        - startDay
        - startTime
      type: object
    PaginatedSchedulesDto:
      example:
        pagination:
          total: 1
          pages: 1
          itemsPerPage: 1
          page: 1
        schedules:
          - createdAt: 2021-01-01T00:00:00Z
            deletedAt: 2021-01-01T00:00:00Z
            startDay: 1
            endDay: 7
            startTime: 00:00:00
            endTime: 23:59:59
          - createdAt: 2021-01-01T00:00:00Z
            deletedAt: 2021-01-01T00:00:00Z
            startDay: 1
            endDay: 7
            startTime: 00:00:00
            endTime: 23:59:59
      properties:
        pagination:
          $ref: '#/components/schemas/DelegatedControlChargingStationSearchMetadataDto_pagination'
        schedules:
          description: The charge schedules for the given ppids
          items:
            $ref: '#/components/schemas/ChargeScheduleDto'
          type: array
      required:
        - pagination
        - schedules
      type: object
    EffectiveChargeSchedulesDto:
      example:
        ppids:
          - pp-1
          - pp-2
        from: 2021-08-01T00:00:00Z
        to: 2021-08-01T00:00:00Z
      properties:
        from:
          description: The start date and time of the effective charge schedules
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        to:
          description: The end date and time of the effective charge schedules
          example: 2021-08-01T00:00:00Z
          format: date-time
          type: string
        ppids:
          description:
            The ppids for which the effective charge schedules should be
            retrieved
          example:
            - pp-1
            - pp-2
          items:
            type: string
          type: array
      required:
        - from
        - ppids
        - to
      type: object
    EffectiveScheduleDto:
      example:
        period: 2021-08-01T00:00:00.000Z/2021-08-01T01:00:00.000Z
      properties:
        period:
          description: the period ISO string of the effective charge schedule
          example: 2021-08-01T00:00:00.000Z/2021-08-01T01:00:00.000Z
          type: string
      required:
        - period
      type: object
    EffectiveChargeSchedulesResponseDto:
      example:
        effectiveSchedules:
          - period: 2021-08-01T00:00:00.000Z/2021-08-01T01:00:00.000Z
          - period: 2021-08-01T00:00:00.000Z/2021-08-01T01:00:00.000Z
        ppid: PSL-112334
      properties:
        ppid:
          description: the ppid that the effective charge schedules are for
          example: PSL-112334
          type: string
        effectiveSchedules:
          description: the effective charge schedules for the given ppid
          items:
            $ref: '#/components/schemas/EffectiveScheduleDto'
          type: array
      required:
        - effectiveSchedules
        - ppid
      type: object
    CompositeScheduleDto:
      example:
        scheduleStart: 2022-01-01T00:00:00Z
        chargingSchedule: ''
        connectorId: 1
        status: Accepted
      properties:
        status:
          description:
            Status of the request. The Charge Point will indicate if it
            was able to process the request
          enum:
            - Accepted
            - Rejected
          example: Accepted
          nullable: false
          type: string
        connectorId:
          description:
            The charging schedule contained in this notification applies
            to a Connector.
          example: 1
          nullable: false
          type: number
        scheduleStart:
          description:
            "Time. Periods contained in the charging profile are relative\
            \ to this point in time. If status is \"Rejected\", this field may be\
            \ absent."
          example: 2022-01-01T00:00:00Z
          nullable: false
          type: string
        chargingSchedule:
          allOf:
            - $ref: '#/components/schemas/ChargeScheduleDto'
          description:
            "Planned Composite Charging Schedule, the energy consumption\
            \ over time. Always relative to ScheduleStart. If status is \"Rejected\"\
            , this field may be absent."
          nullable: false
      required:
        - connectorId
        - status
      type: object
    ChargingSchedulePeriodDto:
      properties:
        startPeriod:
          description: Start period
          example: 0
          type: number
        limit:
          description: Limit
          example: 0
          type: number
        numberPhases:
          description: Number of phases
          example: 0
          nullable: false
          type: number
      required:
        - limit
        - startPeriod
      type: object
    ChargingScheduleDto:
      properties:
        duration:
          description: Duration
          example: 604800
          nullable: false
          type: number
        startSchedule:
          description: Start schedule
          example: 2024-11-13T08:32:36.37Z
          format: date-time
          type: string
        chargingRateUnit:
          description: Charging rate unit
          enum:
            - W
            - A
          example: W
          type: string
        chargingSchedulePeriod:
          description: Charging schedule period
          items:
            $ref: '#/components/schemas/ChargingSchedulePeriodDto'
          type: array
        minChargingRate:
          description: Minimum charging rate
          example: 0
          nullable: false
          type: number
      required:
        - chargingRateUnit
        - chargingSchedulePeriod
      type: object
    ValidateChargingProfileRequestDto:
      example:
        chargingProfileKind: Recurring
        chargingProfilePurpose: TxDefaultProfile
        recurrencyKind: Weekly
        chargingSchedule: ''
        validFrom: 2024-11-13T08:32:36.37Z
        stackLevel: 10000
        transactionId: PPO-NjM0Mzg3OQ==
        validTo: 2024-11-13T08:32:36.37Z
      properties:
        transactionId:
          description: Transaction id
          example: PPO-NjM0Mzg3OQ==
          type: string
        stackLevel:
          description: The stack level
          example: 10000
          type: number
        chargingProfilePurpose:
          description: The charging profile purpose
          enum:
            - ChargePointMaxProfile
            - TxDefaultProfile
            - TxProfile
          example: TxDefaultProfile
          type: string
        chargingProfileKind:
          description: The charging profile kind
          enum:
            - Absolute
            - Recurring
            - Relative
          example: Recurring
          type: string
        recurrencyKind:
          description: The recurrency kind
          enum:
            - Daily
            - Weekly
          example: Weekly
          nullable: false
          type: string
        validFrom:
          description: The valid from date
          example: 2024-11-13T08:32:36.37Z
          format: date-time
          nullable: false
          type: string
        validTo:
          description: The valid to date
          example: 2024-11-13T08:32:36.37Z
          format: date-time
          nullable: false
          type: string
        chargingSchedule:
          allOf:
            - $ref: '#/components/schemas/ChargingScheduleDto'
          description: The charging schedule
      required:
        - chargingProfileKind
        - chargingProfilePurpose
        - chargingSchedule
        - stackLevel
      type: object
    ValidateChargingProfileResponseDto:
      example:
        valid: true
        reason: exceeds current chargeByTime
      properties:
        valid:
          description:
            The validity of the charging profile that was presented for
            validation
          example: true
          type: boolean
        reason:
          description: The reason the charging profile is considered invalid
          example: exceeds current chargeByTime
          type: string
      required:
        - valid
      type: object
    ChargeStatisticsDto:
      example:
        chargeStartTime: 2000-01-23T04:56:07.000+00:00
        kWhFromGrid: 30.5
        chargeEndTime: 2000-01-23T04:56:07.000+00:00
        kWhFromGeneration: 10.5
      properties:
        chargeStartTime:
          description: The start time of the charge (ISO 8601)
          format: date-time
          type: string
        chargeEndTime:
          description: The end time of the charge (ISO 8601)
          format: date-time
          type: string
        kWhFromGrid:
          description: The energy (kWh) from the grid used by the charge
          example: 30.5
          type: number
        kWhFromGeneration:
          description:
            The energy (kWh) from local-generated sources (e.g. by solar)
            used by the charge
          example: 10.5
          type: number
      required:
        - chargeEndTime
        - chargeStartTime
        - kWhFromGeneration
        - kWhFromGrid
      type: object
    RewardPointsDto:
      example:
        reason: ELIGIBLE
        points: 10.5
      properties:
        points:
          description: The number of points the charge is worth
          example: 10.5
          type: number
        reason:
          description: ''
          enum:
            - ELIGIBLE
            - INELIGIBLE_CHARGE_OVERRIDE
            - INELIGIBLE_INSUFFICIENT_GRID_IMPORT
            - INELIGIBLE_NOT_ENROLLED
            - INELIGIBLE_SOLAR_ONLY
          example: ELIGIBLE
          type: string
      required:
        - points
        - reason
      type: object
    RewardInfoDto:
      example:
        rewardableEnergyKwh: 20.45
        vehicleId: 2f727f8b-7c0e-43b3-b455-a1d7f07ea036
      properties:
        rewardableEnergyKwh:
          description:
            "The energy delivered in the session that is rewardable. If\
            \ 0 is returned, the session is not eligible for rewards"
          example: 20.45
          type: number
        vehicleId:
          description: The vehicle charged during the session
          example: 2f727f8b-7c0e-43b3-b455-a1d7f07ea036
          nullable: true
          type: string
      required:
        - rewardableEnergyKwh
        - vehicleId
      type: object
    HealthController_check_200_response_info_value:
      additionalProperties: true
      properties:
        status:
          type: string
      required:
        - status
      type: object
    HealthController_check_200_response:
      example:
        details:
          database:
            status: up
        error: {}
        status: ok
        info:
          database:
            status: up
      properties:
        status:
          example: ok
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example: {}
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          type: object
      type: object
    HealthController_check_503_response:
      example:
        details:
          database:
            status: up
          redis:
            status: down
            message: Could not connect
        error:
          redis:
            status: down
            message: Could not connect
        status: error
        info:
          database:
            status: up
      properties:
        status:
          example: error
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            redis:
              status: down
              message: Could not connect
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
            redis:
              status: down
              message: Could not connect
          type: object
      type: object
    create_flexibility_request_for_programme_200_response:
      example:
        failedPpids:
          - PSL-00000
          - PSL-00000
        successPpids:
          - PSL-00000
          - PSL-00000
      properties:
        successPpids:
          items:
            example: PSL-00000
            type: string
          type: array
        failedPpids:
          items:
            example: PSL-00000
            type: string
          type: array
      type: object
    update_vehicle_by_id_200_response:
      oneOf:
        - $ref: '#/components/schemas/GenericStatefulVehicleDto'
        - $ref: '#/components/schemas/ConnectedStatefulVehicleDto'
    get_charge_schedules_200_response_value_inner:
      properties:
        startDay:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          type: integer
        endDay:
          enum:
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
            - 7
          type: integer
        startTime:
          pattern: '^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'
          type: string
        endTime:
          pattern: '^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'
          type: string
      required:
        - endDay
        - endTime
        - startDay
        - startTime
      type: object
    VehicleLinkResponseDto_vehicle:
      oneOf:
        - $ref: '#/components/schemas/ConnectedStatefulVehicleDto'
        - $ref: '#/components/schemas/GenericStatefulVehicleDto'
    DelegatedControlChargingStationSearchMetadataDto_pagination:
      description: Pagination information
      example:
        total: 1
        pages: 1
        itemsPerPage: 1
        page: 1
      properties:
        pages:
          example: 1
          type: integer
        total:
          example: 1
          type: integer
        page:
          example: 1
          type: integer
        itemsPerPage:
          example: 1
          type: integer
      type: object
    FlexRequestResponse_limit:
      description: The limit of the flexibility request
      example:
        unit: AMP
        value: 0.8008281904610115
      properties:
        unit:
          enum:
            - AMP
            - KW
          type: string
        value:
          type: number
      type: object
    VehicleChargeInfoDto_chargingStation:
      example:
        ppid: ppid
      properties:
        ppid:
          type: string
      type: object
