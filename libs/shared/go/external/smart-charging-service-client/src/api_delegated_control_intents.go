/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type DelegatedControlIntentsAPI interface {

	/*
		CalculateChargeInfoFromProfile Calculate charge info from ocpp1.6 profile

		Calculate charge info from profile

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiCalculateChargeInfoFromProfileRequest
	*/
	CalculateChargeInfoFromProfile(ctx context.Context, ppid string) ApiCalculateChargeInfoFromProfileRequest

	// CalculateChargeInfoFromProfileExecute executes the request
	//  @return VehicleChargeInfoDto
	CalculateChargeInfoFromProfileExecute(r ApiCalculateChargeInfoFromProfileRequest) (*VehicleChargeInfoDto, *http.Response, error)

	/*
		DelegatedControlIntentControllerGetCurrentIntentDetail Method for DelegatedControlIntentControllerGetCurrentIntentDetail

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@param vehicleId
		@return ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest
	*/
	DelegatedControlIntentControllerGetCurrentIntentDetail(ctx context.Context, ppid string, vehicleId string) ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest

	// DelegatedControlIntentControllerGetCurrentIntentDetailExecute executes the request
	DelegatedControlIntentControllerGetCurrentIntentDetailExecute(r ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest) (*http.Response, error)

	/*
		GetVehicleIntents Get vehicle intents

		Get vehicle intents

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param vehicleId The vehicleId associated to the charging station
		@return ApiGetVehicleIntentsRequest
	*/
	GetVehicleIntents(ctx context.Context, ppid string, vehicleId string) ApiGetVehicleIntentsRequest

	// GetVehicleIntentsExecute executes the request
	//  @return VehicleIntentsResponseDto
	GetVehicleIntentsExecute(r ApiGetVehicleIntentsRequest) (*VehicleIntentsResponseDto, *http.Response, error)

	/*
		NotifyIntentCannotBeMet Notify that intent cannot be met

		Notify intent cannot be met

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiNotifyIntentCannotBeMetRequest
	*/
	NotifyIntentCannotBeMet(ctx context.Context, ppid string) ApiNotifyIntentCannotBeMetRequest

	// NotifyIntentCannotBeMetExecute executes the request
	NotifyIntentCannotBeMetExecute(r ApiNotifyIntentCannotBeMetRequest) (*http.Response, error)

	/*
		SetDelegatedControlIntents Sets delegated control intents

		Sets delegated control intents. The ppid and vehicleId are required as parameters.

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The ppid of the charging station
		@param vehicleId The vehicleId associated to the charging station
		@return ApiSetDelegatedControlIntentsRequest
	*/
	SetDelegatedControlIntents(ctx context.Context, ppid string, vehicleId string) ApiSetDelegatedControlIntentsRequest

	// SetDelegatedControlIntentsExecute executes the request
	//  @return SetDelegatedControlIntentsResponseDto
	SetDelegatedControlIntentsExecute(r ApiSetDelegatedControlIntentsRequest) (*SetDelegatedControlIntentsResponseDto, *http.Response, error)
}

// DelegatedControlIntentsAPIService DelegatedControlIntentsAPI service
type DelegatedControlIntentsAPIService service

type ApiCalculateChargeInfoFromProfileRequest struct {
	ctx              context.Context
	ApiService       DelegatedControlIntentsAPI
	ppid             string
	txProfileInfoDto *TxProfileInfoDto
}

func (r ApiCalculateChargeInfoFromProfileRequest) TxProfileInfoDto(txProfileInfoDto TxProfileInfoDto) ApiCalculateChargeInfoFromProfileRequest {
	r.txProfileInfoDto = &txProfileInfoDto
	return r
}

func (r ApiCalculateChargeInfoFromProfileRequest) Execute() (*VehicleChargeInfoDto, *http.Response, error) {
	return r.ApiService.CalculateChargeInfoFromProfileExecute(r)
}

/*
CalculateChargeInfoFromProfile Calculate charge info from ocpp1.6 profile

Calculate charge info from profile

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiCalculateChargeInfoFromProfileRequest
*/
func (a *DelegatedControlIntentsAPIService) CalculateChargeInfoFromProfile(ctx context.Context, ppid string) ApiCalculateChargeInfoFromProfileRequest {
	return ApiCalculateChargeInfoFromProfileRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return VehicleChargeInfoDto
func (a *DelegatedControlIntentsAPIService) CalculateChargeInfoFromProfileExecute(r ApiCalculateChargeInfoFromProfileRequest) (*VehicleChargeInfoDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *VehicleChargeInfoDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlIntentsAPIService.CalculateChargeInfoFromProfile")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/intent/calculate-charge-info-from-profile"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.txProfileInfoDto == nil {
		return localVarReturnValue, nil, reportError("txProfileInfoDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.txProfileInfoDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest struct {
	ctx        context.Context
	ApiService DelegatedControlIntentsAPI
	ppid       string
	vehicleId  string
}

func (r ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest) Execute() (*http.Response, error) {
	return r.ApiService.DelegatedControlIntentControllerGetCurrentIntentDetailExecute(r)
}

/*
DelegatedControlIntentControllerGetCurrentIntentDetail Method for DelegatedControlIntentControllerGetCurrentIntentDetail

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@param vehicleId
	@return ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest
*/
func (a *DelegatedControlIntentsAPIService) DelegatedControlIntentControllerGetCurrentIntentDetail(ctx context.Context, ppid string, vehicleId string) ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest {
	return ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
func (a *DelegatedControlIntentsAPIService) DelegatedControlIntentControllerGetCurrentIntentDetailExecute(r ApiDelegatedControlIntentControllerGetCurrentIntentDetailRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodGet
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlIntentsAPIService.DelegatedControlIntentControllerGetCurrentIntentDetail")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}/intents/current"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiGetVehicleIntentsRequest struct {
	ctx        context.Context
	ApiService DelegatedControlIntentsAPI
	ppid       string
	vehicleId  string
}

func (r ApiGetVehicleIntentsRequest) Execute() (*VehicleIntentsResponseDto, *http.Response, error) {
	return r.ApiService.GetVehicleIntentsExecute(r)
}

/*
GetVehicleIntents Get vehicle intents

Get vehicle intents

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param vehicleId The vehicleId associated to the charging station
	@return ApiGetVehicleIntentsRequest
*/
func (a *DelegatedControlIntentsAPIService) GetVehicleIntents(ctx context.Context, ppid string, vehicleId string) ApiGetVehicleIntentsRequest {
	return ApiGetVehicleIntentsRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
//
//	@return VehicleIntentsResponseDto
func (a *DelegatedControlIntentsAPIService) GetVehicleIntentsExecute(r ApiGetVehicleIntentsRequest) (*VehicleIntentsResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *VehicleIntentsResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlIntentsAPIService.GetVehicleIntents")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}/intents"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiNotifyIntentCannotBeMetRequest struct {
	ctx                     context.Context
	ApiService              DelegatedControlIntentsAPI
	ppid                    string
	cannotMeetSessionIntent *CannotMeetSessionIntent
}

func (r ApiNotifyIntentCannotBeMetRequest) CannotMeetSessionIntent(cannotMeetSessionIntent CannotMeetSessionIntent) ApiNotifyIntentCannotBeMetRequest {
	r.cannotMeetSessionIntent = &cannotMeetSessionIntent
	return r
}

func (r ApiNotifyIntentCannotBeMetRequest) Execute() (*http.Response, error) {
	return r.ApiService.NotifyIntentCannotBeMetExecute(r)
}

/*
NotifyIntentCannotBeMet Notify that intent cannot be met

Notify intent cannot be met

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiNotifyIntentCannotBeMetRequest
*/
func (a *DelegatedControlIntentsAPIService) NotifyIntentCannotBeMet(ctx context.Context, ppid string) ApiNotifyIntentCannotBeMetRequest {
	return ApiNotifyIntentCannotBeMetRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *DelegatedControlIntentsAPIService) NotifyIntentCannotBeMetExecute(r ApiNotifyIntentCannotBeMetRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPost
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlIntentsAPIService.NotifyIntentCannotBeMet")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/notify/intent-cannot-be-met"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.cannotMeetSessionIntent == nil {
		return nil, reportError("cannotMeetSessionIntent is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.cannotMeetSessionIntent
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiSetDelegatedControlIntentsRequest struct {
	ctx                      context.Context
	ApiService               DelegatedControlIntentsAPI
	ppid                     string
	vehicleId                string
	vehicleIntentsRequestDto *VehicleIntentsRequestDto
}

func (r ApiSetDelegatedControlIntentsRequest) VehicleIntentsRequestDto(vehicleIntentsRequestDto VehicleIntentsRequestDto) ApiSetDelegatedControlIntentsRequest {
	r.vehicleIntentsRequestDto = &vehicleIntentsRequestDto
	return r
}

func (r ApiSetDelegatedControlIntentsRequest) Execute() (*SetDelegatedControlIntentsResponseDto, *http.Response, error) {
	return r.ApiService.SetDelegatedControlIntentsExecute(r)
}

/*
SetDelegatedControlIntents Sets delegated control intents

Sets delegated control intents. The ppid and vehicleId are required as parameters.

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The ppid of the charging station
	@param vehicleId The vehicleId associated to the charging station
	@return ApiSetDelegatedControlIntentsRequest
*/
func (a *DelegatedControlIntentsAPIService) SetDelegatedControlIntents(ctx context.Context, ppid string, vehicleId string) ApiSetDelegatedControlIntentsRequest {
	return ApiSetDelegatedControlIntentsRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
//
//	@return SetDelegatedControlIntentsResponseDto
func (a *DelegatedControlIntentsAPIService) SetDelegatedControlIntentsExecute(r ApiSetDelegatedControlIntentsRequest) (*SetDelegatedControlIntentsResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPut
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *SetDelegatedControlIntentsResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlIntentsAPIService.SetDelegatedControlIntents")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}/intents"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.vehicleIntentsRequestDto == nil {
		return localVarReturnValue, nil, reportError("vehicleIntentsRequestDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.vehicleIntentsRequestDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
