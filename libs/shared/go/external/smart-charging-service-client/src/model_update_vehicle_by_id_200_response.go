/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
	"fmt"

	"gopkg.in/validator.v2"
)

// UpdateVehicleById200Response - struct for UpdateVehicleById200Response
type UpdateVehicleById200Response struct {
	ConnectedStatefulVehicleDto *ConnectedStatefulVehicleDto
	GenericStatefulVehicleDto   *GenericStatefulVehicleDto
}

// ConnectedStatefulVehicleDtoAsUpdateVehicleById200Response is a convenience function that returns ConnectedStatefulVehicleDto wrapped in UpdateVehicleById200Response
func ConnectedStatefulVehicleDtoAsUpdateVehicleById200Response(v *ConnectedStatefulVehicleDto) UpdateVehicleById200Response {
	return UpdateVehicleById200Response{
		ConnectedStatefulVehicleDto: v,
	}
}

// GenericStatefulVehicleDtoAsUpdateVehicleById200Response is a convenience function that returns GenericStatefulVehicleDto wrapped in UpdateVehicleById200Response
func GenericStatefulVehicleDtoAsUpdateVehicleById200Response(v *GenericStatefulVehicleDto) UpdateVehicleById200Response {
	return UpdateVehicleById200Response{
		GenericStatefulVehicleDto: v,
	}
}

// Unmarshal JSON data into one of the pointers in the struct
func (dst *UpdateVehicleById200Response) UnmarshalJSON(data []byte) error {
	var err error
	match := 0
	// try to unmarshal data into ConnectedStatefulVehicleDto
	err = newStrictDecoder(data).Decode(&dst.ConnectedStatefulVehicleDto)
	if err == nil {
		jsonConnectedStatefulVehicleDto, _ := json.Marshal(dst.ConnectedStatefulVehicleDto)
		if string(jsonConnectedStatefulVehicleDto) == "{}" { // empty struct
			dst.ConnectedStatefulVehicleDto = nil
		} else {
			if err = validator.Validate(dst.ConnectedStatefulVehicleDto); err != nil {
				dst.ConnectedStatefulVehicleDto = nil
			} else {
				match++
			}
		}
	} else {
		dst.ConnectedStatefulVehicleDto = nil
	}

	// try to unmarshal data into GenericStatefulVehicleDto
	err = newStrictDecoder(data).Decode(&dst.GenericStatefulVehicleDto)
	if err == nil {
		jsonGenericStatefulVehicleDto, _ := json.Marshal(dst.GenericStatefulVehicleDto)
		if string(jsonGenericStatefulVehicleDto) == "{}" { // empty struct
			dst.GenericStatefulVehicleDto = nil
		} else {
			if err = validator.Validate(dst.GenericStatefulVehicleDto); err != nil {
				dst.GenericStatefulVehicleDto = nil
			} else {
				match++
			}
		}
	} else {
		dst.GenericStatefulVehicleDto = nil
	}

	if match > 1 { // more than 1 match
		// reset to nil
		dst.ConnectedStatefulVehicleDto = nil
		dst.GenericStatefulVehicleDto = nil

		return fmt.Errorf("data matches more than one schema in oneOf(UpdateVehicleById200Response)")
	} else if match == 1 {
		return nil // exactly one match
	} else { // no match
		return fmt.Errorf("data failed to match schemas in oneOf(UpdateVehicleById200Response)")
	}
}

// Marshal data from the first non-nil pointers in the struct to JSON
func (src UpdateVehicleById200Response) MarshalJSON() ([]byte, error) {
	if src.ConnectedStatefulVehicleDto != nil {
		return json.Marshal(&src.ConnectedStatefulVehicleDto)
	}

	if src.GenericStatefulVehicleDto != nil {
		return json.Marshal(&src.GenericStatefulVehicleDto)
	}

	return nil, nil // no data in oneOf schemas
}

// Get the actual instance
func (obj *UpdateVehicleById200Response) GetActualInstance() interface{} {
	if obj == nil {
		return nil
	}
	if obj.ConnectedStatefulVehicleDto != nil {
		return obj.ConnectedStatefulVehicleDto
	}

	if obj.GenericStatefulVehicleDto != nil {
		return obj.GenericStatefulVehicleDto
	}

	// all schemas are nil
	return nil
}

// Get the actual instance value
func (obj UpdateVehicleById200Response) GetActualInstanceValue() interface{} {
	if obj.ConnectedStatefulVehicleDto != nil {
		return *obj.ConnectedStatefulVehicleDto
	}

	if obj.GenericStatefulVehicleDto != nil {
		return *obj.GenericStatefulVehicleDto
	}

	// all schemas are nil
	return nil
}

type NullableUpdateVehicleById200Response struct {
	value *UpdateVehicleById200Response
	isSet bool
}

func (v NullableUpdateVehicleById200Response) Get() *UpdateVehicleById200Response {
	return v.value
}

func (v *NullableUpdateVehicleById200Response) Set(val *UpdateVehicleById200Response) {
	v.value = val
	v.isSet = true
}

func (v NullableUpdateVehicleById200Response) IsSet() bool {
	return v.isSet
}

func (v *NullableUpdateVehicleById200Response) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUpdateVehicleById200Response(val *UpdateVehicleById200Response) *NullableUpdateVehicleById200Response {
	return &NullableUpdateVehicleById200Response{value: val, isSet: true}
}

func (v NullableUpdateVehicleById200Response) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUpdateVehicleById200Response) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
