/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the DelegatedControlChargingStationSearchMetadataDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DelegatedControlChargingStationSearchMetadataDto{}

// DelegatedControlChargingStationSearchMetadataDto struct for DelegatedControlChargingStationSearchMetadataDto
type DelegatedControlChargingStationSearchMetadataDto struct {
	Pagination DelegatedControlChargingStationSearchMetadataDtoPagination `json:"pagination"`
	Criteria   DelegatedControlChargingStationSearchCriteriaDto           `json:"criteria"`
}

type _DelegatedControlChargingStationSearchMetadataDto DelegatedControlChargingStationSearchMetadataDto

// NewDelegatedControlChargingStationSearchMetadataDto instantiates a new DelegatedControlChargingStationSearchMetadataDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDelegatedControlChargingStationSearchMetadataDto(pagination DelegatedControlChargingStationSearchMetadataDtoPagination, criteria DelegatedControlChargingStationSearchCriteriaDto) *DelegatedControlChargingStationSearchMetadataDto {
	this := DelegatedControlChargingStationSearchMetadataDto{}
	this.Pagination = pagination
	this.Criteria = criteria
	return &this
}

// NewDelegatedControlChargingStationSearchMetadataDtoWithDefaults instantiates a new DelegatedControlChargingStationSearchMetadataDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDelegatedControlChargingStationSearchMetadataDtoWithDefaults() *DelegatedControlChargingStationSearchMetadataDto {
	this := DelegatedControlChargingStationSearchMetadataDto{}
	return &this
}

// GetPagination returns the Pagination field value
func (o *DelegatedControlChargingStationSearchMetadataDto) GetPagination() DelegatedControlChargingStationSearchMetadataDtoPagination {
	if o == nil {
		var ret DelegatedControlChargingStationSearchMetadataDtoPagination
		return ret
	}

	return o.Pagination
}

// GetPaginationOk returns a tuple with the Pagination field value
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDto) GetPaginationOk() (*DelegatedControlChargingStationSearchMetadataDtoPagination, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Pagination, true
}

// SetPagination sets field value
func (o *DelegatedControlChargingStationSearchMetadataDto) SetPagination(v DelegatedControlChargingStationSearchMetadataDtoPagination) {
	o.Pagination = v
}

// GetCriteria returns the Criteria field value
func (o *DelegatedControlChargingStationSearchMetadataDto) GetCriteria() DelegatedControlChargingStationSearchCriteriaDto {
	if o == nil {
		var ret DelegatedControlChargingStationSearchCriteriaDto
		return ret
	}

	return o.Criteria
}

// GetCriteriaOk returns a tuple with the Criteria field value
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDto) GetCriteriaOk() (*DelegatedControlChargingStationSearchCriteriaDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Criteria, true
}

// SetCriteria sets field value
func (o *DelegatedControlChargingStationSearchMetadataDto) SetCriteria(v DelegatedControlChargingStationSearchCriteriaDto) {
	o.Criteria = v
}

func (o DelegatedControlChargingStationSearchMetadataDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DelegatedControlChargingStationSearchMetadataDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["pagination"] = o.Pagination
	toSerialize["criteria"] = o.Criteria
	return toSerialize, nil
}

func (o *DelegatedControlChargingStationSearchMetadataDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"pagination",
		"criteria",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varDelegatedControlChargingStationSearchMetadataDto := _DelegatedControlChargingStationSearchMetadataDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varDelegatedControlChargingStationSearchMetadataDto)

	if err != nil {
		return err
	}

	*o = DelegatedControlChargingStationSearchMetadataDto(varDelegatedControlChargingStationSearchMetadataDto)

	return err
}

type NullableDelegatedControlChargingStationSearchMetadataDto struct {
	value *DelegatedControlChargingStationSearchMetadataDto
	isSet bool
}

func (v NullableDelegatedControlChargingStationSearchMetadataDto) Get() *DelegatedControlChargingStationSearchMetadataDto {
	return v.value
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDto) Set(val *DelegatedControlChargingStationSearchMetadataDto) {
	v.value = val
	v.isSet = true
}

func (v NullableDelegatedControlChargingStationSearchMetadataDto) IsSet() bool {
	return v.isSet
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDelegatedControlChargingStationSearchMetadataDto(val *DelegatedControlChargingStationSearchMetadataDto) *NullableDelegatedControlChargingStationSearchMetadataDto {
	return &NullableDelegatedControlChargingStationSearchMetadataDto{value: val, isSet: true}
}

func (v NullableDelegatedControlChargingStationSearchMetadataDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
