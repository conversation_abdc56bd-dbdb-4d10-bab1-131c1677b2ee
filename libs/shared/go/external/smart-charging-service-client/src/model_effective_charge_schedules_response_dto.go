/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the EffectiveChargeSchedulesResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &EffectiveChargeSchedulesResponseDto{}

// EffectiveChargeSchedulesResponseDto struct for EffectiveChargeSchedulesResponseDto
type EffectiveChargeSchedulesResponseDto struct {
	// the ppid that the effective charge schedules are for
	Ppid string `json:"ppid"`
	// the effective charge schedules for the given ppid
	EffectiveSchedules []EffectiveScheduleDto `json:"effectiveSchedules"`
}

type _EffectiveChargeSchedulesResponseDto EffectiveChargeSchedulesResponseDto

// NewEffectiveChargeSchedulesResponseDto instantiates a new EffectiveChargeSchedulesResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewEffectiveChargeSchedulesResponseDto(ppid string, effectiveSchedules []EffectiveScheduleDto) *EffectiveChargeSchedulesResponseDto {
	this := EffectiveChargeSchedulesResponseDto{}
	this.Ppid = ppid
	this.EffectiveSchedules = effectiveSchedules
	return &this
}

// NewEffectiveChargeSchedulesResponseDtoWithDefaults instantiates a new EffectiveChargeSchedulesResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewEffectiveChargeSchedulesResponseDtoWithDefaults() *EffectiveChargeSchedulesResponseDto {
	this := EffectiveChargeSchedulesResponseDto{}
	return &this
}

// GetPpid returns the Ppid field value
func (o *EffectiveChargeSchedulesResponseDto) GetPpid() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Ppid
}

// GetPpidOk returns a tuple with the Ppid field value
// and a boolean to check if the value has been set.
func (o *EffectiveChargeSchedulesResponseDto) GetPpidOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Ppid, true
}

// SetPpid sets field value
func (o *EffectiveChargeSchedulesResponseDto) SetPpid(v string) {
	o.Ppid = v
}

// GetEffectiveSchedules returns the EffectiveSchedules field value
func (o *EffectiveChargeSchedulesResponseDto) GetEffectiveSchedules() []EffectiveScheduleDto {
	if o == nil {
		var ret []EffectiveScheduleDto
		return ret
	}

	return o.EffectiveSchedules
}

// GetEffectiveSchedulesOk returns a tuple with the EffectiveSchedules field value
// and a boolean to check if the value has been set.
func (o *EffectiveChargeSchedulesResponseDto) GetEffectiveSchedulesOk() ([]EffectiveScheduleDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.EffectiveSchedules, true
}

// SetEffectiveSchedules sets field value
func (o *EffectiveChargeSchedulesResponseDto) SetEffectiveSchedules(v []EffectiveScheduleDto) {
	o.EffectiveSchedules = v
}

func (o EffectiveChargeSchedulesResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o EffectiveChargeSchedulesResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["ppid"] = o.Ppid
	toSerialize["effectiveSchedules"] = o.EffectiveSchedules
	return toSerialize, nil
}

func (o *EffectiveChargeSchedulesResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"ppid",
		"effectiveSchedules",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varEffectiveChargeSchedulesResponseDto := _EffectiveChargeSchedulesResponseDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varEffectiveChargeSchedulesResponseDto)

	if err != nil {
		return err
	}

	*o = EffectiveChargeSchedulesResponseDto(varEffectiveChargeSchedulesResponseDto)

	return err
}

type NullableEffectiveChargeSchedulesResponseDto struct {
	value *EffectiveChargeSchedulesResponseDto
	isSet bool
}

func (v NullableEffectiveChargeSchedulesResponseDto) Get() *EffectiveChargeSchedulesResponseDto {
	return v.value
}

func (v *NullableEffectiveChargeSchedulesResponseDto) Set(val *EffectiveChargeSchedulesResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableEffectiveChargeSchedulesResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableEffectiveChargeSchedulesResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableEffectiveChargeSchedulesResponseDto(val *EffectiveChargeSchedulesResponseDto) *NullableEffectiveChargeSchedulesResponseDto {
	return &NullableEffectiveChargeSchedulesResponseDto{value: val, isSet: true}
}

func (v NullableEffectiveChargeSchedulesResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableEffectiveChargeSchedulesResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
