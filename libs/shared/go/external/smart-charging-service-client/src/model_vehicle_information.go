/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the VehicleInformation type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VehicleInformation{}

// VehicleInformation struct for VehicleInformation
type VehicleInformation struct {
	// The vehicle make
	Brand NullableString `json:"brand,omitempty"`
	// The vehicle model
	Model NullableString `json:"model,omitempty"`
	// The vehicle model variant
	ModelVariant NullableString `json:"modelVariant,omitempty"`
	// The vehicle registration plate
	VehicleRegistrationPlate NullableString `json:"vehicleRegistrationPlate,omitempty"`
	// The vehicle display name
	DisplayName NullableString `json:"displayName,omitempty"`
	// The vehicle EV database ID
	EvDatabaseId NullableString `json:"evDatabaseId,omitempty"`
}

// NewVehicleInformation instantiates a new VehicleInformation object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVehicleInformation() *VehicleInformation {
	this := VehicleInformation{}
	return &this
}

// NewVehicleInformationWithDefaults instantiates a new VehicleInformation object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVehicleInformationWithDefaults() *VehicleInformation {
	this := VehicleInformation{}
	return &this
}

// GetBrand returns the Brand field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetBrand() string {
	if o == nil || IsNil(o.Brand.Get()) {
		var ret string
		return ret
	}
	return *o.Brand.Get()
}

// GetBrandOk returns a tuple with the Brand field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetBrandOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Brand.Get(), o.Brand.IsSet()
}

// HasBrand returns a boolean if a field has been set.
func (o *VehicleInformation) HasBrand() bool {
	if o != nil && o.Brand.IsSet() {
		return true
	}

	return false
}

// SetBrand gets a reference to the given NullableString and assigns it to the Brand field.
func (o *VehicleInformation) SetBrand(v string) {
	o.Brand.Set(&v)
}

// SetBrandNil sets the value for Brand to be an explicit nil
func (o *VehicleInformation) SetBrandNil() {
	o.Brand.Set(nil)
}

// UnsetBrand ensures that no value is present for Brand, not even an explicit nil
func (o *VehicleInformation) UnsetBrand() {
	o.Brand.Unset()
}

// GetModel returns the Model field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetModel() string {
	if o == nil || IsNil(o.Model.Get()) {
		var ret string
		return ret
	}
	return *o.Model.Get()
}

// GetModelOk returns a tuple with the Model field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetModelOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Model.Get(), o.Model.IsSet()
}

// HasModel returns a boolean if a field has been set.
func (o *VehicleInformation) HasModel() bool {
	if o != nil && o.Model.IsSet() {
		return true
	}

	return false
}

// SetModel gets a reference to the given NullableString and assigns it to the Model field.
func (o *VehicleInformation) SetModel(v string) {
	o.Model.Set(&v)
}

// SetModelNil sets the value for Model to be an explicit nil
func (o *VehicleInformation) SetModelNil() {
	o.Model.Set(nil)
}

// UnsetModel ensures that no value is present for Model, not even an explicit nil
func (o *VehicleInformation) UnsetModel() {
	o.Model.Unset()
}

// GetModelVariant returns the ModelVariant field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetModelVariant() string {
	if o == nil || IsNil(o.ModelVariant.Get()) {
		var ret string
		return ret
	}
	return *o.ModelVariant.Get()
}

// GetModelVariantOk returns a tuple with the ModelVariant field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetModelVariantOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ModelVariant.Get(), o.ModelVariant.IsSet()
}

// HasModelVariant returns a boolean if a field has been set.
func (o *VehicleInformation) HasModelVariant() bool {
	if o != nil && o.ModelVariant.IsSet() {
		return true
	}

	return false
}

// SetModelVariant gets a reference to the given NullableString and assigns it to the ModelVariant field.
func (o *VehicleInformation) SetModelVariant(v string) {
	o.ModelVariant.Set(&v)
}

// SetModelVariantNil sets the value for ModelVariant to be an explicit nil
func (o *VehicleInformation) SetModelVariantNil() {
	o.ModelVariant.Set(nil)
}

// UnsetModelVariant ensures that no value is present for ModelVariant, not even an explicit nil
func (o *VehicleInformation) UnsetModelVariant() {
	o.ModelVariant.Unset()
}

// GetVehicleRegistrationPlate returns the VehicleRegistrationPlate field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetVehicleRegistrationPlate() string {
	if o == nil || IsNil(o.VehicleRegistrationPlate.Get()) {
		var ret string
		return ret
	}
	return *o.VehicleRegistrationPlate.Get()
}

// GetVehicleRegistrationPlateOk returns a tuple with the VehicleRegistrationPlate field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetVehicleRegistrationPlateOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.VehicleRegistrationPlate.Get(), o.VehicleRegistrationPlate.IsSet()
}

// HasVehicleRegistrationPlate returns a boolean if a field has been set.
func (o *VehicleInformation) HasVehicleRegistrationPlate() bool {
	if o != nil && o.VehicleRegistrationPlate.IsSet() {
		return true
	}

	return false
}

// SetVehicleRegistrationPlate gets a reference to the given NullableString and assigns it to the VehicleRegistrationPlate field.
func (o *VehicleInformation) SetVehicleRegistrationPlate(v string) {
	o.VehicleRegistrationPlate.Set(&v)
}

// SetVehicleRegistrationPlateNil sets the value for VehicleRegistrationPlate to be an explicit nil
func (o *VehicleInformation) SetVehicleRegistrationPlateNil() {
	o.VehicleRegistrationPlate.Set(nil)
}

// UnsetVehicleRegistrationPlate ensures that no value is present for VehicleRegistrationPlate, not even an explicit nil
func (o *VehicleInformation) UnsetVehicleRegistrationPlate() {
	o.VehicleRegistrationPlate.Unset()
}

// GetDisplayName returns the DisplayName field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetDisplayName() string {
	if o == nil || IsNil(o.DisplayName.Get()) {
		var ret string
		return ret
	}
	return *o.DisplayName.Get()
}

// GetDisplayNameOk returns a tuple with the DisplayName field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetDisplayNameOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.DisplayName.Get(), o.DisplayName.IsSet()
}

// HasDisplayName returns a boolean if a field has been set.
func (o *VehicleInformation) HasDisplayName() bool {
	if o != nil && o.DisplayName.IsSet() {
		return true
	}

	return false
}

// SetDisplayName gets a reference to the given NullableString and assigns it to the DisplayName field.
func (o *VehicleInformation) SetDisplayName(v string) {
	o.DisplayName.Set(&v)
}

// SetDisplayNameNil sets the value for DisplayName to be an explicit nil
func (o *VehicleInformation) SetDisplayNameNil() {
	o.DisplayName.Set(nil)
}

// UnsetDisplayName ensures that no value is present for DisplayName, not even an explicit nil
func (o *VehicleInformation) UnsetDisplayName() {
	o.DisplayName.Unset()
}

// GetEvDatabaseId returns the EvDatabaseId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VehicleInformation) GetEvDatabaseId() string {
	if o == nil || IsNil(o.EvDatabaseId.Get()) {
		var ret string
		return ret
	}
	return *o.EvDatabaseId.Get()
}

// GetEvDatabaseIdOk returns a tuple with the EvDatabaseId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleInformation) GetEvDatabaseIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.EvDatabaseId.Get(), o.EvDatabaseId.IsSet()
}

// HasEvDatabaseId returns a boolean if a field has been set.
func (o *VehicleInformation) HasEvDatabaseId() bool {
	if o != nil && o.EvDatabaseId.IsSet() {
		return true
	}

	return false
}

// SetEvDatabaseId gets a reference to the given NullableString and assigns it to the EvDatabaseId field.
func (o *VehicleInformation) SetEvDatabaseId(v string) {
	o.EvDatabaseId.Set(&v)
}

// SetEvDatabaseIdNil sets the value for EvDatabaseId to be an explicit nil
func (o *VehicleInformation) SetEvDatabaseIdNil() {
	o.EvDatabaseId.Set(nil)
}

// UnsetEvDatabaseId ensures that no value is present for EvDatabaseId, not even an explicit nil
func (o *VehicleInformation) UnsetEvDatabaseId() {
	o.EvDatabaseId.Unset()
}

func (o VehicleInformation) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VehicleInformation) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if o.Brand.IsSet() {
		toSerialize["brand"] = o.Brand.Get()
	}
	if o.Model.IsSet() {
		toSerialize["model"] = o.Model.Get()
	}
	if o.ModelVariant.IsSet() {
		toSerialize["modelVariant"] = o.ModelVariant.Get()
	}
	if o.VehicleRegistrationPlate.IsSet() {
		toSerialize["vehicleRegistrationPlate"] = o.VehicleRegistrationPlate.Get()
	}
	if o.DisplayName.IsSet() {
		toSerialize["displayName"] = o.DisplayName.Get()
	}
	if o.EvDatabaseId.IsSet() {
		toSerialize["evDatabaseId"] = o.EvDatabaseId.Get()
	}
	return toSerialize, nil
}

type NullableVehicleInformation struct {
	value *VehicleInformation
	isSet bool
}

func (v NullableVehicleInformation) Get() *VehicleInformation {
	return v.value
}

func (v *NullableVehicleInformation) Set(val *VehicleInformation) {
	v.value = val
	v.isSet = true
}

func (v NullableVehicleInformation) IsSet() bool {
	return v.isSet
}

func (v *NullableVehicleInformation) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVehicleInformation(val *VehicleInformation) *NullableVehicleInformation {
	return &NullableVehicleInformation{value: val, isSet: true}
}

func (v NullableVehicleInformation) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVehicleInformation) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
