/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the CreateVehicleLinkRequestDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CreateVehicleLinkRequestDto{}

// CreateVehicleLinkRequestDto struct for CreateVehicleLinkRequestDto
type CreateVehicleLinkRequestDto struct {
	Vehicle CreateVehicleRequestDto `json:"vehicle"`
	Intents []VehicleIntentEntryDto `json:"intents"`
}

type _CreateVehicleLinkRequestDto CreateVehicleLinkRequestDto

// NewCreateVehicleLinkRequestDto instantiates a new CreateVehicleLinkRequestDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCreateVehicleLinkRequestDto(vehicle CreateVehicleRequestDto, intents []VehicleIntentEntryDto) *CreateVehicleLinkRequestDto {
	this := CreateVehicleLinkRequestDto{}
	this.Vehicle = vehicle
	this.Intents = intents
	return &this
}

// NewCreateVehicleLinkRequestDtoWithDefaults instantiates a new CreateVehicleLinkRequestDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCreateVehicleLinkRequestDtoWithDefaults() *CreateVehicleLinkRequestDto {
	this := CreateVehicleLinkRequestDto{}
	return &this
}

// GetVehicle returns the Vehicle field value
func (o *CreateVehicleLinkRequestDto) GetVehicle() CreateVehicleRequestDto {
	if o == nil {
		var ret CreateVehicleRequestDto
		return ret
	}

	return o.Vehicle
}

// GetVehicleOk returns a tuple with the Vehicle field value
// and a boolean to check if the value has been set.
func (o *CreateVehicleLinkRequestDto) GetVehicleOk() (*CreateVehicleRequestDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Vehicle, true
}

// SetVehicle sets field value
func (o *CreateVehicleLinkRequestDto) SetVehicle(v CreateVehicleRequestDto) {
	o.Vehicle = v
}

// GetIntents returns the Intents field value
func (o *CreateVehicleLinkRequestDto) GetIntents() []VehicleIntentEntryDto {
	if o == nil {
		var ret []VehicleIntentEntryDto
		return ret
	}

	return o.Intents
}

// GetIntentsOk returns a tuple with the Intents field value
// and a boolean to check if the value has been set.
func (o *CreateVehicleLinkRequestDto) GetIntentsOk() ([]VehicleIntentEntryDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.Intents, true
}

// SetIntents sets field value
func (o *CreateVehicleLinkRequestDto) SetIntents(v []VehicleIntentEntryDto) {
	o.Intents = v
}

func (o CreateVehicleLinkRequestDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CreateVehicleLinkRequestDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["vehicle"] = o.Vehicle
	toSerialize["intents"] = o.Intents
	return toSerialize, nil
}

func (o *CreateVehicleLinkRequestDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"vehicle",
		"intents",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCreateVehicleLinkRequestDto := _CreateVehicleLinkRequestDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCreateVehicleLinkRequestDto)

	if err != nil {
		return err
	}

	*o = CreateVehicleLinkRequestDto(varCreateVehicleLinkRequestDto)

	return err
}

type NullableCreateVehicleLinkRequestDto struct {
	value *CreateVehicleLinkRequestDto
	isSet bool
}

func (v NullableCreateVehicleLinkRequestDto) Get() *CreateVehicleLinkRequestDto {
	return v.value
}

func (v *NullableCreateVehicleLinkRequestDto) Set(val *CreateVehicleLinkRequestDto) {
	v.value = val
	v.isSet = true
}

func (v NullableCreateVehicleLinkRequestDto) IsSet() bool {
	return v.isSet
}

func (v *NullableCreateVehicleLinkRequestDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCreateVehicleLinkRequestDto(val *CreateVehicleLinkRequestDto) *NullableCreateVehicleLinkRequestDto {
	return &NullableCreateVehicleLinkRequestDto{value: val, isSet: true}
}

func (v NullableCreateVehicleLinkRequestDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCreateVehicleLinkRequestDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
