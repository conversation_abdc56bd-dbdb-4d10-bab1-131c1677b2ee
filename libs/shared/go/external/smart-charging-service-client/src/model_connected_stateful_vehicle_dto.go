/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ConnectedStatefulVehicleDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ConnectedStatefulVehicleDto{}

// ConnectedStatefulVehicleDto struct for ConnectedStatefulVehicleDto
type ConnectedStatefulVehicleDto struct {
	// The vehicle ID
	Id string `json:"id"`
	// The enode user ID
	EnodeUserId NullableString `json:"enodeUserId,omitempty"`
	// The enode vehicle ID
	EnodeVehicleId NullableString `json:"enodeVehicleId,omitempty"`
	// The vehicle information
	VehicleInformation VehicleInformation `json:"vehicleInformation"`
	// The vehicle charge state
	ChargeState ConnectedChargeState `json:"chargeState"`
	// The vehicle interventions
	Interventions InterventionDto `json:"interventions"`
}

type _ConnectedStatefulVehicleDto ConnectedStatefulVehicleDto

// NewConnectedStatefulVehicleDto instantiates a new ConnectedStatefulVehicleDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewConnectedStatefulVehicleDto(id string, vehicleInformation VehicleInformation, chargeState ConnectedChargeState, interventions InterventionDto) *ConnectedStatefulVehicleDto {
	this := ConnectedStatefulVehicleDto{}
	this.Id = id
	this.VehicleInformation = vehicleInformation
	this.ChargeState = chargeState
	this.Interventions = interventions
	return &this
}

// NewConnectedStatefulVehicleDtoWithDefaults instantiates a new ConnectedStatefulVehicleDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewConnectedStatefulVehicleDtoWithDefaults() *ConnectedStatefulVehicleDto {
	this := ConnectedStatefulVehicleDto{}
	return &this
}

// GetId returns the Id field value
func (o *ConnectedStatefulVehicleDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *ConnectedStatefulVehicleDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *ConnectedStatefulVehicleDto) SetId(v string) {
	o.Id = v
}

// GetEnodeUserId returns the EnodeUserId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *ConnectedStatefulVehicleDto) GetEnodeUserId() string {
	if o == nil || IsNil(o.EnodeUserId.Get()) {
		var ret string
		return ret
	}
	return *o.EnodeUserId.Get()
}

// GetEnodeUserIdOk returns a tuple with the EnodeUserId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ConnectedStatefulVehicleDto) GetEnodeUserIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.EnodeUserId.Get(), o.EnodeUserId.IsSet()
}

// HasEnodeUserId returns a boolean if a field has been set.
func (o *ConnectedStatefulVehicleDto) HasEnodeUserId() bool {
	if o != nil && o.EnodeUserId.IsSet() {
		return true
	}

	return false
}

// SetEnodeUserId gets a reference to the given NullableString and assigns it to the EnodeUserId field.
func (o *ConnectedStatefulVehicleDto) SetEnodeUserId(v string) {
	o.EnodeUserId.Set(&v)
}

// SetEnodeUserIdNil sets the value for EnodeUserId to be an explicit nil
func (o *ConnectedStatefulVehicleDto) SetEnodeUserIdNil() {
	o.EnodeUserId.Set(nil)
}

// UnsetEnodeUserId ensures that no value is present for EnodeUserId, not even an explicit nil
func (o *ConnectedStatefulVehicleDto) UnsetEnodeUserId() {
	o.EnodeUserId.Unset()
}

// GetEnodeVehicleId returns the EnodeVehicleId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *ConnectedStatefulVehicleDto) GetEnodeVehicleId() string {
	if o == nil || IsNil(o.EnodeVehicleId.Get()) {
		var ret string
		return ret
	}
	return *o.EnodeVehicleId.Get()
}

// GetEnodeVehicleIdOk returns a tuple with the EnodeVehicleId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *ConnectedStatefulVehicleDto) GetEnodeVehicleIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.EnodeVehicleId.Get(), o.EnodeVehicleId.IsSet()
}

// HasEnodeVehicleId returns a boolean if a field has been set.
func (o *ConnectedStatefulVehicleDto) HasEnodeVehicleId() bool {
	if o != nil && o.EnodeVehicleId.IsSet() {
		return true
	}

	return false
}

// SetEnodeVehicleId gets a reference to the given NullableString and assigns it to the EnodeVehicleId field.
func (o *ConnectedStatefulVehicleDto) SetEnodeVehicleId(v string) {
	o.EnodeVehicleId.Set(&v)
}

// SetEnodeVehicleIdNil sets the value for EnodeVehicleId to be an explicit nil
func (o *ConnectedStatefulVehicleDto) SetEnodeVehicleIdNil() {
	o.EnodeVehicleId.Set(nil)
}

// UnsetEnodeVehicleId ensures that no value is present for EnodeVehicleId, not even an explicit nil
func (o *ConnectedStatefulVehicleDto) UnsetEnodeVehicleId() {
	o.EnodeVehicleId.Unset()
}

// GetVehicleInformation returns the VehicleInformation field value
func (o *ConnectedStatefulVehicleDto) GetVehicleInformation() VehicleInformation {
	if o == nil {
		var ret VehicleInformation
		return ret
	}

	return o.VehicleInformation
}

// GetVehicleInformationOk returns a tuple with the VehicleInformation field value
// and a boolean to check if the value has been set.
func (o *ConnectedStatefulVehicleDto) GetVehicleInformationOk() (*VehicleInformation, bool) {
	if o == nil {
		return nil, false
	}
	return &o.VehicleInformation, true
}

// SetVehicleInformation sets field value
func (o *ConnectedStatefulVehicleDto) SetVehicleInformation(v VehicleInformation) {
	o.VehicleInformation = v
}

// GetChargeState returns the ChargeState field value
func (o *ConnectedStatefulVehicleDto) GetChargeState() ConnectedChargeState {
	if o == nil {
		var ret ConnectedChargeState
		return ret
	}

	return o.ChargeState
}

// GetChargeStateOk returns a tuple with the ChargeState field value
// and a boolean to check if the value has been set.
func (o *ConnectedStatefulVehicleDto) GetChargeStateOk() (*ConnectedChargeState, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargeState, true
}

// SetChargeState sets field value
func (o *ConnectedStatefulVehicleDto) SetChargeState(v ConnectedChargeState) {
	o.ChargeState = v
}

// GetInterventions returns the Interventions field value
func (o *ConnectedStatefulVehicleDto) GetInterventions() InterventionDto {
	if o == nil {
		var ret InterventionDto
		return ret
	}

	return o.Interventions
}

// GetInterventionsOk returns a tuple with the Interventions field value
// and a boolean to check if the value has been set.
func (o *ConnectedStatefulVehicleDto) GetInterventionsOk() (*InterventionDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Interventions, true
}

// SetInterventions sets field value
func (o *ConnectedStatefulVehicleDto) SetInterventions(v InterventionDto) {
	o.Interventions = v
}

func (o ConnectedStatefulVehicleDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ConnectedStatefulVehicleDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	if o.EnodeUserId.IsSet() {
		toSerialize["enodeUserId"] = o.EnodeUserId.Get()
	}
	if o.EnodeVehicleId.IsSet() {
		toSerialize["enodeVehicleId"] = o.EnodeVehicleId.Get()
	}
	toSerialize["vehicleInformation"] = o.VehicleInformation
	toSerialize["chargeState"] = o.ChargeState
	toSerialize["interventions"] = o.Interventions
	return toSerialize, nil
}

func (o *ConnectedStatefulVehicleDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"vehicleInformation",
		"chargeState",
		"interventions",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varConnectedStatefulVehicleDto := _ConnectedStatefulVehicleDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varConnectedStatefulVehicleDto)

	if err != nil {
		return err
	}

	*o = ConnectedStatefulVehicleDto(varConnectedStatefulVehicleDto)

	return err
}

type NullableConnectedStatefulVehicleDto struct {
	value *ConnectedStatefulVehicleDto
	isSet bool
}

func (v NullableConnectedStatefulVehicleDto) Get() *ConnectedStatefulVehicleDto {
	return v.value
}

func (v *NullableConnectedStatefulVehicleDto) Set(val *ConnectedStatefulVehicleDto) {
	v.value = val
	v.isSet = true
}

func (v NullableConnectedStatefulVehicleDto) IsSet() bool {
	return v.isSet
}

func (v *NullableConnectedStatefulVehicleDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableConnectedStatefulVehicleDto(val *ConnectedStatefulVehicleDto) *NullableConnectedStatefulVehicleDto {
	return &NullableConnectedStatefulVehicleDto{value: val, isSet: true}
}

func (v NullableConnectedStatefulVehicleDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableConnectedStatefulVehicleDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
