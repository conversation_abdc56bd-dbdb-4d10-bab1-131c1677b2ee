/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ExtendedVehicleLinksResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ExtendedVehicleLinksResponseDto{}

// ExtendedVehicleLinksResponseDto struct for ExtendedVehicleLinksResponseDto
type ExtendedVehicleLinksResponseDto struct {
	Data []ExtendedVehicleLinkResponseDto `json:"data"`
}

type _ExtendedVehicleLinksResponseDto ExtendedVehicleLinksResponseDto

// NewExtendedVehicleLinksResponseDto instantiates a new ExtendedVehicleLinksResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewExtendedVehicleLinksResponseDto(data []ExtendedVehicleLinkResponseDto) *ExtendedVehicleLinksResponseDto {
	this := ExtendedVehicleLinksResponseDto{}
	this.Data = data
	return &this
}

// NewExtendedVehicleLinksResponseDtoWithDefaults instantiates a new ExtendedVehicleLinksResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewExtendedVehicleLinksResponseDtoWithDefaults() *ExtendedVehicleLinksResponseDto {
	this := ExtendedVehicleLinksResponseDto{}
	return &this
}

// GetData returns the Data field value
func (o *ExtendedVehicleLinksResponseDto) GetData() []ExtendedVehicleLinkResponseDto {
	if o == nil {
		var ret []ExtendedVehicleLinkResponseDto
		return ret
	}

	return o.Data
}

// GetDataOk returns a tuple with the Data field value
// and a boolean to check if the value has been set.
func (o *ExtendedVehicleLinksResponseDto) GetDataOk() ([]ExtendedVehicleLinkResponseDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data, true
}

// SetData sets field value
func (o *ExtendedVehicleLinksResponseDto) SetData(v []ExtendedVehicleLinkResponseDto) {
	o.Data = v
}

func (o ExtendedVehicleLinksResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ExtendedVehicleLinksResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["data"] = o.Data
	return toSerialize, nil
}

func (o *ExtendedVehicleLinksResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"data",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varExtendedVehicleLinksResponseDto := _ExtendedVehicleLinksResponseDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varExtendedVehicleLinksResponseDto)

	if err != nil {
		return err
	}

	*o = ExtendedVehicleLinksResponseDto(varExtendedVehicleLinksResponseDto)

	return err
}

type NullableExtendedVehicleLinksResponseDto struct {
	value *ExtendedVehicleLinksResponseDto
	isSet bool
}

func (v NullableExtendedVehicleLinksResponseDto) Get() *ExtendedVehicleLinksResponseDto {
	return v.value
}

func (v *NullableExtendedVehicleLinksResponseDto) Set(val *ExtendedVehicleLinksResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableExtendedVehicleLinksResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableExtendedVehicleLinksResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableExtendedVehicleLinksResponseDto(val *ExtendedVehicleLinksResponseDto) *NullableExtendedVehicleLinksResponseDto {
	return &NullableExtendedVehicleLinksResponseDto{value: val, isSet: true}
}

func (v NullableExtendedVehicleLinksResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableExtendedVehicleLinksResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
