/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ChargingProfilesRequestDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingProfilesRequestDto{}

// ChargingProfilesRequestDto struct for ChargingProfilesRequestDto
type ChargingProfilesRequestDto struct {
	// Charging profiles
	Profiles []ChargingProfileDto `json:"profiles"`
}

type _ChargingProfilesRequestDto ChargingProfilesRequestDto

// NewChargingProfilesRequestDto instantiates a new ChargingProfilesRequestDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingProfilesRequestDto(profiles []ChargingProfileDto) *ChargingProfilesRequestDto {
	this := ChargingProfilesRequestDto{}
	this.Profiles = profiles
	return &this
}

// NewChargingProfilesRequestDtoWithDefaults instantiates a new ChargingProfilesRequestDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingProfilesRequestDtoWithDefaults() *ChargingProfilesRequestDto {
	this := ChargingProfilesRequestDto{}
	return &this
}

// GetProfiles returns the Profiles field value
func (o *ChargingProfilesRequestDto) GetProfiles() []ChargingProfileDto {
	if o == nil {
		var ret []ChargingProfileDto
		return ret
	}

	return o.Profiles
}

// GetProfilesOk returns a tuple with the Profiles field value
// and a boolean to check if the value has been set.
func (o *ChargingProfilesRequestDto) GetProfilesOk() ([]ChargingProfileDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.Profiles, true
}

// SetProfiles sets field value
func (o *ChargingProfilesRequestDto) SetProfiles(v []ChargingProfileDto) {
	o.Profiles = v
}

func (o ChargingProfilesRequestDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingProfilesRequestDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["profiles"] = o.Profiles
	return toSerialize, nil
}

func (o *ChargingProfilesRequestDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"profiles",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingProfilesRequestDto := _ChargingProfilesRequestDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargingProfilesRequestDto)

	if err != nil {
		return err
	}

	*o = ChargingProfilesRequestDto(varChargingProfilesRequestDto)

	return err
}

type NullableChargingProfilesRequestDto struct {
	value *ChargingProfilesRequestDto
	isSet bool
}

func (v NullableChargingProfilesRequestDto) Get() *ChargingProfilesRequestDto {
	return v.value
}

func (v *NullableChargingProfilesRequestDto) Set(val *ChargingProfilesRequestDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingProfilesRequestDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingProfilesRequestDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingProfilesRequestDto(val *ChargingProfilesRequestDto) *NullableChargingProfilesRequestDto {
	return &NullableChargingProfilesRequestDto{value: val, isSet: true}
}

func (v NullableChargingProfilesRequestDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingProfilesRequestDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
