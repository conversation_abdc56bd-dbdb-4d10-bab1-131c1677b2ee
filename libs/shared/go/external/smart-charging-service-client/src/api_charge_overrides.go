/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type ChargeOverridesAPI interface {

	/*
		ClearChargingStationChargeOverrides Removes an override for a given charging station (All EVSEs)

		Removes any existing overrides that are in place for a given charging station (aka unit)

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiClearChargingStationChargeOverridesRequest
	*/
	ClearChargingStationChargeOverrides(ctx context.Context, ppid string) ApiClearChargingStationChargeOverridesRequest

	// ClearChargingStationChargeOverridesExecute executes the request
	ClearChargingStationChargeOverridesExecute(r ApiClearChargingStationChargeOverridesRequest) (*http.Response, error)

	/*
		CreateChargingStationChargeOverride Create an override for a given charger, that should be applied to all EVSEs

		Override and schedules on the device (all evses) either indefinitely or until the specified end time

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiCreateChargingStationChargeOverrideRequest
	*/
	CreateChargingStationChargeOverride(ctx context.Context, ppid string) ApiCreateChargingStationChargeOverrideRequest

	// CreateChargingStationChargeOverrideExecute executes the request
	//  @return []ChargeOverrideResponse
	CreateChargingStationChargeOverrideExecute(r ApiCreateChargingStationChargeOverrideRequest) ([]ChargeOverrideResponse, *http.Response, error)

	/*
		GetChargingStationChargeOverrides Get all overrides for a given charging station (All EVSEs)

		Gets all overrides for a given charging station (All EVSEs)

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiGetChargingStationChargeOverridesRequest
	*/
	GetChargingStationChargeOverrides(ctx context.Context, ppid string) ApiGetChargingStationChargeOverridesRequest

	// GetChargingStationChargeOverridesExecute executes the request
	//  @return []ChargeOverrideResponse
	GetChargingStationChargeOverridesExecute(r ApiGetChargingStationChargeOverridesRequest) ([]ChargeOverrideResponse, *http.Response, error)

	/*
		SearchActiveOverrides Search for any active overrides in place

		Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ApiSearchActiveOverridesRequest
	*/
	SearchActiveOverrides(ctx context.Context) ApiSearchActiveOverridesRequest

	// SearchActiveOverridesExecute executes the request
	//  @return []ChargeOverrideResponse
	SearchActiveOverridesExecute(r ApiSearchActiveOverridesRequest) ([]ChargeOverrideResponse, *http.Response, error)
}

// ChargeOverridesAPIService ChargeOverridesAPI service
type ChargeOverridesAPIService service

type ApiClearChargingStationChargeOverridesRequest struct {
	ctx        context.Context
	ApiService ChargeOverridesAPI
	ppid       string
}

func (r ApiClearChargingStationChargeOverridesRequest) Execute() (*http.Response, error) {
	return r.ApiService.ClearChargingStationChargeOverridesExecute(r)
}

/*
ClearChargingStationChargeOverrides Removes an override for a given charging station (All EVSEs)

Removes any existing overrides that are in place for a given charging station (aka unit)

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiClearChargingStationChargeOverridesRequest
*/
func (a *ChargeOverridesAPIService) ClearChargingStationChargeOverrides(ctx context.Context, ppid string) ApiClearChargingStationChargeOverridesRequest {
	return ApiClearChargingStationChargeOverridesRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *ChargeOverridesAPIService) ClearChargingStationChargeOverridesExecute(r ApiClearChargingStationChargeOverridesRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodDelete
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeOverridesAPIService.ClearChargingStationChargeOverrides")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/charge-overrides"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiCreateChargingStationChargeOverrideRequest struct {
	ctx                   context.Context
	ApiService            ChargeOverridesAPI
	ppid                  string
	chargeOverrideRequest *ChargeOverrideRequest
}

func (r ApiCreateChargingStationChargeOverrideRequest) ChargeOverrideRequest(chargeOverrideRequest ChargeOverrideRequest) ApiCreateChargingStationChargeOverrideRequest {
	r.chargeOverrideRequest = &chargeOverrideRequest
	return r
}

func (r ApiCreateChargingStationChargeOverrideRequest) Execute() ([]ChargeOverrideResponse, *http.Response, error) {
	return r.ApiService.CreateChargingStationChargeOverrideExecute(r)
}

/*
CreateChargingStationChargeOverride Create an override for a given charger, that should be applied to all EVSEs

Override and schedules on the device (all evses) either indefinitely or until the specified end time

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiCreateChargingStationChargeOverrideRequest
*/
func (a *ChargeOverridesAPIService) CreateChargingStationChargeOverride(ctx context.Context, ppid string) ApiCreateChargingStationChargeOverrideRequest {
	return ApiCreateChargingStationChargeOverrideRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return []ChargeOverrideResponse
func (a *ChargeOverridesAPIService) CreateChargingStationChargeOverrideExecute(r ApiCreateChargingStationChargeOverrideRequest) ([]ChargeOverrideResponse, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue []ChargeOverrideResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeOverridesAPIService.CreateChargingStationChargeOverride")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/charge-overrides"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.chargeOverrideRequest == nil {
		return localVarReturnValue, nil, reportError("chargeOverrideRequest is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.chargeOverrideRequest
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetChargingStationChargeOverridesRequest struct {
	ctx        context.Context
	ApiService ChargeOverridesAPI
	ppid       string
	includePast *bool
}

// Include past overrides
func (r ApiGetChargingStationChargeOverridesRequest) IncludePast(includePast bool) ApiGetChargingStationChargeOverridesRequest {
	r.includePast = &includePast
	return r
}

func (r ApiGetChargingStationChargeOverridesRequest) Execute() ([]ChargeOverrideResponse, *http.Response, error) {
	return r.ApiService.GetChargingStationChargeOverridesExecute(r)
}

/*
GetChargingStationChargeOverrides Get all overrides for a given charging station (All EVSEs)

Gets all overrides for a given charging station (All EVSEs)

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiGetChargingStationChargeOverridesRequest
*/
func (a *ChargeOverridesAPIService) GetChargingStationChargeOverrides(ctx context.Context, ppid string) ApiGetChargingStationChargeOverridesRequest {
	return ApiGetChargingStationChargeOverridesRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return []ChargeOverrideResponse
func (a *ChargeOverridesAPIService) GetChargingStationChargeOverridesExecute(r ApiGetChargingStationChargeOverridesRequest) ([]ChargeOverrideResponse, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue []ChargeOverrideResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeOverridesAPIService.GetChargingStationChargeOverrides")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/charge-overrides"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if r.includePast != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "includePast", r.includePast, "form", "")
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiSearchActiveOverridesRequest struct {
	ctx        context.Context
	ApiService ChargeOverridesAPI
	ppid       *string
}

// The PPID of the charging station
func (r ApiSearchActiveOverridesRequest) Ppid(ppid string) ApiSearchActiveOverridesRequest {
	r.ppid = &ppid
	return r
}

func (r ApiSearchActiveOverridesRequest) Execute() ([]ChargeOverrideResponse, *http.Response, error) {
	return r.ApiService.SearchActiveOverridesExecute(r)
}

/*
SearchActiveOverrides Search for any active overrides in place

Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiSearchActiveOverridesRequest
*/
func (a *ChargeOverridesAPIService) SearchActiveOverrides(ctx context.Context) ApiSearchActiveOverridesRequest {
	return ApiSearchActiveOverridesRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return []ChargeOverrideResponse
func (a *ChargeOverridesAPIService) SearchActiveOverridesExecute(r ApiSearchActiveOverridesRequest) ([]ChargeOverrideResponse, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue []ChargeOverrideResponse
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeOverridesAPIService.SearchActiveOverrides")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-overrides"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.ppid == nil {
		return localVarReturnValue, nil, reportError("ppid is required and must be specified")
	}

	parameterAddToHeaderOrQuery(localVarQueryParams, "ppid", r.ppid, "form", "")
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
