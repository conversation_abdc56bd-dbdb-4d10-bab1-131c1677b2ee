/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the VehicleChargeInfoDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VehicleChargeInfoDto{}

// VehicleChargeInfoDto struct for VehicleChargeInfoDto
type VehicleChargeInfoDto struct {
	CanMeetTarget          bool                                `json:"canMeetTarget"`
	CannotMeetTargetReason NullableString                      `json:"cannotMeetTargetReason"`
	ChargeDetail           ChargeDetailDto                     `json:"chargeDetail"`
	ChargingStation        VehicleChargeInfoDtoChargingStation `json:"chargingStation"`
}

type _VehicleChargeInfoDto VehicleChargeInfoDto

// NewVehicleChargeInfoDto instantiates a new VehicleChargeInfoDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVehicleChargeInfoDto(canMeetTarget bool, cannotMeetTargetReason NullableString, chargeDetail ChargeDetailDto, chargingStation VehicleChargeInfoDtoChargingStation) *VehicleChargeInfoDto {
	this := VehicleChargeInfoDto{}
	this.CanMeetTarget = canMeetTarget
	this.CannotMeetTargetReason = cannotMeetTargetReason
	this.ChargeDetail = chargeDetail
	this.ChargingStation = chargingStation
	return &this
}

// NewVehicleChargeInfoDtoWithDefaults instantiates a new VehicleChargeInfoDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVehicleChargeInfoDtoWithDefaults() *VehicleChargeInfoDto {
	this := VehicleChargeInfoDto{}
	return &this
}

// GetCanMeetTarget returns the CanMeetTarget field value
func (o *VehicleChargeInfoDto) GetCanMeetTarget() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.CanMeetTarget
}

// GetCanMeetTargetOk returns a tuple with the CanMeetTarget field value
// and a boolean to check if the value has been set.
func (o *VehicleChargeInfoDto) GetCanMeetTargetOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CanMeetTarget, true
}

// SetCanMeetTarget sets field value
func (o *VehicleChargeInfoDto) SetCanMeetTarget(v bool) {
	o.CanMeetTarget = v
}

// GetCannotMeetTargetReason returns the CannotMeetTargetReason field value
// If the value is explicit nil, the zero value for string will be returned
func (o *VehicleChargeInfoDto) GetCannotMeetTargetReason() string {
	if o == nil || o.CannotMeetTargetReason.Get() == nil {
		var ret string
		return ret
	}

	return *o.CannotMeetTargetReason.Get()
}

// GetCannotMeetTargetReasonOk returns a tuple with the CannotMeetTargetReason field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VehicleChargeInfoDto) GetCannotMeetTargetReasonOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CannotMeetTargetReason.Get(), o.CannotMeetTargetReason.IsSet()
}

// SetCannotMeetTargetReason sets field value
func (o *VehicleChargeInfoDto) SetCannotMeetTargetReason(v string) {
	o.CannotMeetTargetReason.Set(&v)
}

// GetChargeDetail returns the ChargeDetail field value
func (o *VehicleChargeInfoDto) GetChargeDetail() ChargeDetailDto {
	if o == nil {
		var ret ChargeDetailDto
		return ret
	}

	return o.ChargeDetail
}

// GetChargeDetailOk returns a tuple with the ChargeDetail field value
// and a boolean to check if the value has been set.
func (o *VehicleChargeInfoDto) GetChargeDetailOk() (*ChargeDetailDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargeDetail, true
}

// SetChargeDetail sets field value
func (o *VehicleChargeInfoDto) SetChargeDetail(v ChargeDetailDto) {
	o.ChargeDetail = v
}

// GetChargingStation returns the ChargingStation field value
func (o *VehicleChargeInfoDto) GetChargingStation() VehicleChargeInfoDtoChargingStation {
	if o == nil {
		var ret VehicleChargeInfoDtoChargingStation
		return ret
	}

	return o.ChargingStation
}

// GetChargingStationOk returns a tuple with the ChargingStation field value
// and a boolean to check if the value has been set.
func (o *VehicleChargeInfoDto) GetChargingStationOk() (*VehicleChargeInfoDtoChargingStation, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargingStation, true
}

// SetChargingStation sets field value
func (o *VehicleChargeInfoDto) SetChargingStation(v VehicleChargeInfoDtoChargingStation) {
	o.ChargingStation = v
}

func (o VehicleChargeInfoDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VehicleChargeInfoDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["canMeetTarget"] = o.CanMeetTarget
	toSerialize["cannotMeetTargetReason"] = o.CannotMeetTargetReason.Get()
	toSerialize["chargeDetail"] = o.ChargeDetail
	toSerialize["chargingStation"] = o.ChargingStation
	return toSerialize, nil
}

func (o *VehicleChargeInfoDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"canMeetTarget",
		"cannotMeetTargetReason",
		"chargeDetail",
		"chargingStation",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVehicleChargeInfoDto := _VehicleChargeInfoDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varVehicleChargeInfoDto)

	if err != nil {
		return err
	}

	*o = VehicleChargeInfoDto(varVehicleChargeInfoDto)

	return err
}

type NullableVehicleChargeInfoDto struct {
	value *VehicleChargeInfoDto
	isSet bool
}

func (v NullableVehicleChargeInfoDto) Get() *VehicleChargeInfoDto {
	return v.value
}

func (v *NullableVehicleChargeInfoDto) Set(val *VehicleChargeInfoDto) {
	v.value = val
	v.isSet = true
}

func (v NullableVehicleChargeInfoDto) IsSet() bool {
	return v.isSet
}

func (v *NullableVehicleChargeInfoDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVehicleChargeInfoDto(val *VehicleChargeInfoDto) *NullableVehicleChargeInfoDto {
	return &NullableVehicleChargeInfoDto{value: val, isSet: true}
}

func (v NullableVehicleChargeInfoDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVehicleChargeInfoDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
