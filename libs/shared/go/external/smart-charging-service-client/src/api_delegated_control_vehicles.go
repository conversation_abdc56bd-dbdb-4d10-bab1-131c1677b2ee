/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type DelegatedControlVehiclesAPI interface {

	/*
		AddVehicleToDelegatedControlChargingStation Add vehicle to delegated control charging station

		Add vehicle to delegated control charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@return ApiAddVehicleToDelegatedControlChargingStationRequest
	*/
	AddVehicleToDelegatedControlChargingStation(ctx context.Context, ppid string) ApiAddVehicleToDelegatedControlChargingStationRequest

	// AddVehicleToDelegatedControlChargingStationExecute executes the request
	//  @return VehicleLinkResponseDto
	AddVehicleToDelegatedControlChargingStationExecute(r ApiAddVehicleToDelegatedControlChargingStationRequest) (*VehicleLinkResponseDto, *http.Response, error)

	/*
		GetChargingStationsLinkedToVehicle Get all charging stations linked to a vehicle

		Get all charging stations linked to a vehicle

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param vehicleId The vehicleId linked to the charging stations
		@return ApiGetChargingStationsLinkedToVehicleRequest
	*/
	GetChargingStationsLinkedToVehicle(ctx context.Context, vehicleId string) ApiGetChargingStationsLinkedToVehicleRequest

	// GetChargingStationsLinkedToVehicleExecute executes the request
	//  @return VehicleChargingStationsResponseDto
	GetChargingStationsLinkedToVehicleExecute(r ApiGetChargingStationsLinkedToVehicleRequest) (*VehicleChargingStationsResponseDto, *http.Response, error)

	/*
		GetDelegatedControlChargingStationVehicles Get delegated control charging station vehicles

		Get all vehicles linked to a delegated control charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the delegated charging station
		@return ApiGetDelegatedControlChargingStationVehiclesRequest
	*/
	GetDelegatedControlChargingStationVehicles(ctx context.Context, ppid string) ApiGetDelegatedControlChargingStationVehiclesRequest

	// GetDelegatedControlChargingStationVehiclesExecute executes the request
	//  @return ExtendedVehicleLinksResponseDto
	GetDelegatedControlChargingStationVehiclesExecute(r ApiGetDelegatedControlChargingStationVehiclesRequest) (*ExtendedVehicleLinksResponseDto, *http.Response, error)

	/*
		GetVehicle Get vehicle

		Get vehicle

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param vehicleId The vehicleId associated to the charging station
		@return ApiGetVehicleRequest
	*/
	GetVehicle(ctx context.Context, ppid string, vehicleId string) ApiGetVehicleRequest

	// GetVehicleExecute executes the request
	//  @return ExtendedVehicleLinkResponseDto
	GetVehicleExecute(r ApiGetVehicleRequest) (*ExtendedVehicleLinkResponseDto, *http.Response, error)

	/*
		UnlinkVehicleFromDelegatedControlChargingStation Unlink a vehicle from a delegated control charging station

		Unlink a vehicle from a delegated control charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param vehicleId The vehicleId associated to the charging station
		@return ApiUnlinkVehicleFromDelegatedControlChargingStationRequest
	*/
	UnlinkVehicleFromDelegatedControlChargingStation(ctx context.Context, ppid string, vehicleId string) ApiUnlinkVehicleFromDelegatedControlChargingStationRequest

	// UnlinkVehicleFromDelegatedControlChargingStationExecute executes the request
	UnlinkVehicleFromDelegatedControlChargingStationExecute(r ApiUnlinkVehicleFromDelegatedControlChargingStationRequest) (*http.Response, error)

	/*
		UnlinkVehicleFromDelegatedControlChargingStations Unlink a vehicle from all linked delegated control charging stations

		Unlink a vehicle from all linked delegated control charging stations

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param vehicleId The vehicleId associated to the charging station
		@return ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest
	*/
	UnlinkVehicleFromDelegatedControlChargingStations(ctx context.Context, vehicleId string) ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest

	// UnlinkVehicleFromDelegatedControlChargingStationsExecute executes the request
	UnlinkVehicleFromDelegatedControlChargingStationsExecute(r ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest) (*http.Response, error)

	/*
		UpdateVehicleById Update vehicle data by ID

		Given a vehicle ID and new data, updates the vehicle

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@param vehicleId The vehicle ID
		@return ApiUpdateVehicleByIdRequest
	*/
	UpdateVehicleById(ctx context.Context, ppid string, vehicleId string) ApiUpdateVehicleByIdRequest

	// UpdateVehicleByIdExecute executes the request
	//  @return UpdateVehicleById200Response
	UpdateVehicleByIdExecute(r ApiUpdateVehicleByIdRequest) (*UpdateVehicleById200Response, *http.Response, error)
}

// DelegatedControlVehiclesAPIService DelegatedControlVehiclesAPI service
type DelegatedControlVehiclesAPIService service

type ApiAddVehicleToDelegatedControlChargingStationRequest struct {
	ctx                         context.Context
	ApiService                  DelegatedControlVehiclesAPI
	ppid                        string
	createVehicleLinkRequestDto *CreateVehicleLinkRequestDto
}

func (r ApiAddVehicleToDelegatedControlChargingStationRequest) CreateVehicleLinkRequestDto(createVehicleLinkRequestDto CreateVehicleLinkRequestDto) ApiAddVehicleToDelegatedControlChargingStationRequest {
	r.createVehicleLinkRequestDto = &createVehicleLinkRequestDto
	return r
}

func (r ApiAddVehicleToDelegatedControlChargingStationRequest) Execute() (*VehicleLinkResponseDto, *http.Response, error) {
	return r.ApiService.AddVehicleToDelegatedControlChargingStationExecute(r)
}

/*
AddVehicleToDelegatedControlChargingStation Add vehicle to delegated control charging station

Add vehicle to delegated control charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@return ApiAddVehicleToDelegatedControlChargingStationRequest
*/
func (a *DelegatedControlVehiclesAPIService) AddVehicleToDelegatedControlChargingStation(ctx context.Context, ppid string) ApiAddVehicleToDelegatedControlChargingStationRequest {
	return ApiAddVehicleToDelegatedControlChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return VehicleLinkResponseDto
func (a *DelegatedControlVehiclesAPIService) AddVehicleToDelegatedControlChargingStationExecute(r ApiAddVehicleToDelegatedControlChargingStationRequest) (*VehicleLinkResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *VehicleLinkResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.AddVehicleToDelegatedControlChargingStation")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.createVehicleLinkRequestDto == nil {
		return localVarReturnValue, nil, reportError("createVehicleLinkRequestDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.createVehicleLinkRequestDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetChargingStationsLinkedToVehicleRequest struct {
	ctx        context.Context
	ApiService DelegatedControlVehiclesAPI
	vehicleId  string
}

func (r ApiGetChargingStationsLinkedToVehicleRequest) Execute() (*VehicleChargingStationsResponseDto, *http.Response, error) {
	return r.ApiService.GetChargingStationsLinkedToVehicleExecute(r)
}

/*
GetChargingStationsLinkedToVehicle Get all charging stations linked to a vehicle

Get all charging stations linked to a vehicle

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param vehicleId The vehicleId linked to the charging stations
	@return ApiGetChargingStationsLinkedToVehicleRequest
*/
func (a *DelegatedControlVehiclesAPIService) GetChargingStationsLinkedToVehicle(ctx context.Context, vehicleId string) ApiGetChargingStationsLinkedToVehicleRequest {
	return ApiGetChargingStationsLinkedToVehicleRequest{
		ApiService: a,
		ctx:        ctx,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
//
//	@return VehicleChargingStationsResponseDto
func (a *DelegatedControlVehiclesAPIService) GetChargingStationsLinkedToVehicleExecute(r ApiGetChargingStationsLinkedToVehicleRequest) (*VehicleChargingStationsResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *VehicleChargingStationsResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.GetChargingStationsLinkedToVehicle")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/vehicles/{vehicleId}/charging-stations"
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetDelegatedControlChargingStationVehiclesRequest struct {
	ctx        context.Context
	ApiService DelegatedControlVehiclesAPI
	ppid       string
}

func (r ApiGetDelegatedControlChargingStationVehiclesRequest) Execute() (*ExtendedVehicleLinksResponseDto, *http.Response, error) {
	return r.ApiService.GetDelegatedControlChargingStationVehiclesExecute(r)
}

/*
GetDelegatedControlChargingStationVehicles Get delegated control charging station vehicles

Get all vehicles linked to a delegated control charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the delegated charging station
	@return ApiGetDelegatedControlChargingStationVehiclesRequest
*/
func (a *DelegatedControlVehiclesAPIService) GetDelegatedControlChargingStationVehicles(ctx context.Context, ppid string) ApiGetDelegatedControlChargingStationVehiclesRequest {
	return ApiGetDelegatedControlChargingStationVehiclesRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return ExtendedVehicleLinksResponseDto
func (a *DelegatedControlVehiclesAPIService) GetDelegatedControlChargingStationVehiclesExecute(r ApiGetDelegatedControlChargingStationVehiclesRequest) (*ExtendedVehicleLinksResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ExtendedVehicleLinksResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.GetDelegatedControlChargingStationVehicles")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetVehicleRequest struct {
	ctx        context.Context
	ApiService DelegatedControlVehiclesAPI
	ppid       string
	vehicleId  string
}

func (r ApiGetVehicleRequest) Execute() (*ExtendedVehicleLinkResponseDto, *http.Response, error) {
	return r.ApiService.GetVehicleExecute(r)
}

/*
GetVehicle Get vehicle

Get vehicle

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param vehicleId The vehicleId associated to the charging station
	@return ApiGetVehicleRequest
*/
func (a *DelegatedControlVehiclesAPIService) GetVehicle(ctx context.Context, ppid string, vehicleId string) ApiGetVehicleRequest {
	return ApiGetVehicleRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
//
//	@return ExtendedVehicleLinkResponseDto
func (a *DelegatedControlVehiclesAPIService) GetVehicleExecute(r ApiGetVehicleRequest) (*ExtendedVehicleLinkResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ExtendedVehicleLinkResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.GetVehicle")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiUnlinkVehicleFromDelegatedControlChargingStationRequest struct {
	ctx        context.Context
	ApiService DelegatedControlVehiclesAPI
	ppid       string
	vehicleId  string
}

func (r ApiUnlinkVehicleFromDelegatedControlChargingStationRequest) Execute() (*http.Response, error) {
	return r.ApiService.UnlinkVehicleFromDelegatedControlChargingStationExecute(r)
}

/*
UnlinkVehicleFromDelegatedControlChargingStation Unlink a vehicle from a delegated control charging station

Unlink a vehicle from a delegated control charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param vehicleId The vehicleId associated to the charging station
	@return ApiUnlinkVehicleFromDelegatedControlChargingStationRequest
*/
func (a *DelegatedControlVehiclesAPIService) UnlinkVehicleFromDelegatedControlChargingStation(ctx context.Context, ppid string, vehicleId string) ApiUnlinkVehicleFromDelegatedControlChargingStationRequest {
	return ApiUnlinkVehicleFromDelegatedControlChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
func (a *DelegatedControlVehiclesAPIService) UnlinkVehicleFromDelegatedControlChargingStationExecute(r ApiUnlinkVehicleFromDelegatedControlChargingStationRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodDelete
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.UnlinkVehicleFromDelegatedControlChargingStation")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest struct {
	ctx        context.Context
	ApiService DelegatedControlVehiclesAPI
	vehicleId  string
}

func (r ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest) Execute() (*http.Response, error) {
	return r.ApiService.UnlinkVehicleFromDelegatedControlChargingStationsExecute(r)
}

/*
UnlinkVehicleFromDelegatedControlChargingStations Unlink a vehicle from all linked delegated control charging stations

Unlink a vehicle from all linked delegated control charging stations

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param vehicleId The vehicleId associated to the charging station
	@return ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest
*/
func (a *DelegatedControlVehiclesAPIService) UnlinkVehicleFromDelegatedControlChargingStations(ctx context.Context, vehicleId string) ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest {
	return ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest{
		ApiService: a,
		ctx:        ctx,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
func (a *DelegatedControlVehiclesAPIService) UnlinkVehicleFromDelegatedControlChargingStationsExecute(r ApiUnlinkVehicleFromDelegatedControlChargingStationsRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodDelete
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.UnlinkVehicleFromDelegatedControlChargingStations")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/vehicles/{vehicleId}/charging-stations"
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiUpdateVehicleByIdRequest struct {
	ctx                         context.Context
	ApiService                  DelegatedControlVehiclesAPI
	ppid                        string
	vehicleId                   string
	updateVehicleLinkRequestDto *UpdateVehicleLinkRequestDto
}

func (r ApiUpdateVehicleByIdRequest) UpdateVehicleLinkRequestDto(updateVehicleLinkRequestDto UpdateVehicleLinkRequestDto) ApiUpdateVehicleByIdRequest {
	r.updateVehicleLinkRequestDto = &updateVehicleLinkRequestDto
	return r
}

func (r ApiUpdateVehicleByIdRequest) Execute() (*UpdateVehicleById200Response, *http.Response, error) {
	return r.ApiService.UpdateVehicleByIdExecute(r)
}

/*
UpdateVehicleById Update vehicle data by ID

Given a vehicle ID and new data, updates the vehicle

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@param vehicleId The vehicle ID
	@return ApiUpdateVehicleByIdRequest
*/
func (a *DelegatedControlVehiclesAPIService) UpdateVehicleById(ctx context.Context, ppid string, vehicleId string) ApiUpdateVehicleByIdRequest {
	return ApiUpdateVehicleByIdRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
		vehicleId:  vehicleId,
	}
}

// Execute executes the request
//
//	@return UpdateVehicleById200Response
func (a *DelegatedControlVehiclesAPIService) UpdateVehicleByIdExecute(r ApiUpdateVehicleByIdRequest) (*UpdateVehicleById200Response, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPatch
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *UpdateVehicleById200Response
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlVehiclesAPIService.UpdateVehicleById")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/vehicles/{vehicleId}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)
	localVarPath = strings.Replace(localVarPath, "{"+"vehicleId"+"}", url.PathEscape(parameterValueToString(r.vehicleId, "vehicleId")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.updateVehicleLinkRequestDto == nil {
		return localVarReturnValue, nil, reportError("updateVehicleLinkRequestDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.updateVehicleLinkRequestDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
