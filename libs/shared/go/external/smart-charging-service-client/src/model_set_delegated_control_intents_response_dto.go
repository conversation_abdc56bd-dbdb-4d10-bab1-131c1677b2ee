/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the SetDelegatedControlIntentsResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &SetDelegatedControlIntentsResponseDto{}

// SetDelegatedControlIntentsResponseDto struct for SetDelegatedControlIntentsResponseDto
type SetDelegatedControlIntentsResponseDto struct {
	Id                                       string                  `json:"id"`
	DelegatedControlChargingStationVehicleId string                  `json:"delegatedControlChargingStationVehicleId"`
	IntentDetails                            []VehicleIntentEntryDto `json:"intentDetails"`
	MaxPrice                                 NullableFloat32         `json:"maxPrice"`
	CreatedAt                                time.Time               `json:"createdAt"`
	UpdatedAt                                time.Time               `json:"updatedAt"`
}

type _SetDelegatedControlIntentsResponseDto SetDelegatedControlIntentsResponseDto

// NewSetDelegatedControlIntentsResponseDto instantiates a new SetDelegatedControlIntentsResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSetDelegatedControlIntentsResponseDto(id string, delegatedControlChargingStationVehicleId string, intentDetails []VehicleIntentEntryDto, maxPrice NullableFloat32, createdAt time.Time, updatedAt time.Time) *SetDelegatedControlIntentsResponseDto {
	this := SetDelegatedControlIntentsResponseDto{}
	this.Id = id
	this.DelegatedControlChargingStationVehicleId = delegatedControlChargingStationVehicleId
	this.IntentDetails = intentDetails
	this.MaxPrice = maxPrice
	this.CreatedAt = createdAt
	this.UpdatedAt = updatedAt
	return &this
}

// NewSetDelegatedControlIntentsResponseDtoWithDefaults instantiates a new SetDelegatedControlIntentsResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSetDelegatedControlIntentsResponseDtoWithDefaults() *SetDelegatedControlIntentsResponseDto {
	this := SetDelegatedControlIntentsResponseDto{}
	return &this
}

// GetId returns the Id field value
func (o *SetDelegatedControlIntentsResponseDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *SetDelegatedControlIntentsResponseDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetId(v string) {
	o.Id = v
}

// GetDelegatedControlChargingStationVehicleId returns the DelegatedControlChargingStationVehicleId field value
func (o *SetDelegatedControlIntentsResponseDto) GetDelegatedControlChargingStationVehicleId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.DelegatedControlChargingStationVehicleId
}

// GetDelegatedControlChargingStationVehicleIdOk returns a tuple with the DelegatedControlChargingStationVehicleId field value
// and a boolean to check if the value has been set.
func (o *SetDelegatedControlIntentsResponseDto) GetDelegatedControlChargingStationVehicleIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.DelegatedControlChargingStationVehicleId, true
}

// SetDelegatedControlChargingStationVehicleId sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetDelegatedControlChargingStationVehicleId(v string) {
	o.DelegatedControlChargingStationVehicleId = v
}

// GetIntentDetails returns the IntentDetails field value
func (o *SetDelegatedControlIntentsResponseDto) GetIntentDetails() []VehicleIntentEntryDto {
	if o == nil {
		var ret []VehicleIntentEntryDto
		return ret
	}

	return o.IntentDetails
}

// GetIntentDetailsOk returns a tuple with the IntentDetails field value
// and a boolean to check if the value has been set.
func (o *SetDelegatedControlIntentsResponseDto) GetIntentDetailsOk() ([]VehicleIntentEntryDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.IntentDetails, true
}

// SetIntentDetails sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetIntentDetails(v []VehicleIntentEntryDto) {
	o.IntentDetails = v
}

// GetMaxPrice returns the MaxPrice field value
// If the value is explicit nil, the zero value for float32 will be returned
func (o *SetDelegatedControlIntentsResponseDto) GetMaxPrice() float32 {
	if o == nil || o.MaxPrice.Get() == nil {
		var ret float32
		return ret
	}

	return *o.MaxPrice.Get()
}

// GetMaxPriceOk returns a tuple with the MaxPrice field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *SetDelegatedControlIntentsResponseDto) GetMaxPriceOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return o.MaxPrice.Get(), o.MaxPrice.IsSet()
}

// SetMaxPrice sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetMaxPrice(v float32) {
	o.MaxPrice.Set(&v)
}

// GetCreatedAt returns the CreatedAt field value
func (o *SetDelegatedControlIntentsResponseDto) GetCreatedAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value
// and a boolean to check if the value has been set.
func (o *SetDelegatedControlIntentsResponseDto) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CreatedAt, true
}

// SetCreatedAt sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetCreatedAt(v time.Time) {
	o.CreatedAt = v
}

// GetUpdatedAt returns the UpdatedAt field value
func (o *SetDelegatedControlIntentsResponseDto) GetUpdatedAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value
// and a boolean to check if the value has been set.
func (o *SetDelegatedControlIntentsResponseDto) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.UpdatedAt, true
}

// SetUpdatedAt sets field value
func (o *SetDelegatedControlIntentsResponseDto) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = v
}

func (o SetDelegatedControlIntentsResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o SetDelegatedControlIntentsResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	toSerialize["delegatedControlChargingStationVehicleId"] = o.DelegatedControlChargingStationVehicleId
	toSerialize["intentDetails"] = o.IntentDetails
	toSerialize["maxPrice"] = o.MaxPrice.Get()
	toSerialize["createdAt"] = o.CreatedAt
	toSerialize["updatedAt"] = o.UpdatedAt
	return toSerialize, nil
}

func (o *SetDelegatedControlIntentsResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"delegatedControlChargingStationVehicleId",
		"intentDetails",
		"maxPrice",
		"createdAt",
		"updatedAt",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varSetDelegatedControlIntentsResponseDto := _SetDelegatedControlIntentsResponseDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varSetDelegatedControlIntentsResponseDto)

	if err != nil {
		return err
	}

	*o = SetDelegatedControlIntentsResponseDto(varSetDelegatedControlIntentsResponseDto)

	return err
}

type NullableSetDelegatedControlIntentsResponseDto struct {
	value *SetDelegatedControlIntentsResponseDto
	isSet bool
}

func (v NullableSetDelegatedControlIntentsResponseDto) Get() *SetDelegatedControlIntentsResponseDto {
	return v.value
}

func (v *NullableSetDelegatedControlIntentsResponseDto) Set(val *SetDelegatedControlIntentsResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableSetDelegatedControlIntentsResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableSetDelegatedControlIntentsResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSetDelegatedControlIntentsResponseDto(val *SetDelegatedControlIntentsResponseDto) *NullableSetDelegatedControlIntentsResponseDto {
	return &NullableSetDelegatedControlIntentsResponseDto{value: val, isSet: true}
}

func (v NullableSetDelegatedControlIntentsResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSetDelegatedControlIntentsResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
