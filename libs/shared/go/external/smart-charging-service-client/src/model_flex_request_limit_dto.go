/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the FlexRequestLimitDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &FlexRequestLimitDto{}

// FlexRequestLimitDto struct for FlexRequestLimitDto
type FlexRequestLimitDto struct {
	// The unit of the limit
	Unit string `json:"unit"`
	// The value of the limit
	Value float32 `json:"value"`
}

type _FlexRequestLimitDto FlexRequestLimitDto

// NewFlexRequestLimitDto instantiates a new FlexRequestLimitDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFlexRequestLimitDto(unit string, value float32) *FlexRequestLimitDto {
	this := FlexRequestLimitDto{}
	this.Unit = unit
	this.Value = value
	return &this
}

// NewFlexRequestLimitDtoWithDefaults instantiates a new FlexRequestLimitDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFlexRequestLimitDtoWithDefaults() *FlexRequestLimitDto {
	this := FlexRequestLimitDto{}
	return &this
}

// GetUnit returns the Unit field value
func (o *FlexRequestLimitDto) GetUnit() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Unit
}

// GetUnitOk returns a tuple with the Unit field value
// and a boolean to check if the value has been set.
func (o *FlexRequestLimitDto) GetUnitOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Unit, true
}

// SetUnit sets field value
func (o *FlexRequestLimitDto) SetUnit(v string) {
	o.Unit = v
}

// GetValue returns the Value field value
func (o *FlexRequestLimitDto) GetValue() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.Value
}

// GetValueOk returns a tuple with the Value field value
// and a boolean to check if the value has been set.
func (o *FlexRequestLimitDto) GetValueOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Value, true
}

// SetValue sets field value
func (o *FlexRequestLimitDto) SetValue(v float32) {
	o.Value = v
}

func (o FlexRequestLimitDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o FlexRequestLimitDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["unit"] = o.Unit
	toSerialize["value"] = o.Value
	return toSerialize, nil
}

func (o *FlexRequestLimitDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"unit",
		"value",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varFlexRequestLimitDto := _FlexRequestLimitDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varFlexRequestLimitDto)

	if err != nil {
		return err
	}

	*o = FlexRequestLimitDto(varFlexRequestLimitDto)

	return err
}

type NullableFlexRequestLimitDto struct {
	value *FlexRequestLimitDto
	isSet bool
}

func (v NullableFlexRequestLimitDto) Get() *FlexRequestLimitDto {
	return v.value
}

func (v *NullableFlexRequestLimitDto) Set(val *FlexRequestLimitDto) {
	v.value = val
	v.isSet = true
}

func (v NullableFlexRequestLimitDto) IsSet() bool {
	return v.isSet
}

func (v *NullableFlexRequestLimitDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFlexRequestLimitDto(val *FlexRequestLimitDto) *NullableFlexRequestLimitDto {
	return &NullableFlexRequestLimitDto{value: val, isSet: true}
}

func (v NullableFlexRequestLimitDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFlexRequestLimitDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
