/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strings"
)

type ChargeSchedulesAPI interface {

	/*
		ChargeScheduleControllerGetChargingStationsWithChargeSchedules Method for ChargeScheduleControllerGetChargingStationsWithChargeSchedules

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest
	*/
	ChargeScheduleControllerGetChargingStationsWithChargeSchedules(ctx context.Context) ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest

	// ChargeScheduleControllerGetChargingStationsWithChargeSchedulesExecute executes the request
	ChargeScheduleControllerGetChargingStationsWithChargeSchedulesExecute(r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) (*http.Response, error)

	/*
		DeleteChargeScheduleHistory Delete old charge schedules

		Delete old charge schedules for the given ppid

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid Accepts a single ppid to delete old charge schedules
		@return ApiDeleteChargeScheduleHistoryRequest
	*/
	DeleteChargeScheduleHistory(ctx context.Context, ppid string) ApiDeleteChargeScheduleHistoryRequest

	// DeleteChargeScheduleHistoryExecute executes the request
	DeleteChargeScheduleHistoryExecute(r ApiDeleteChargeScheduleHistoryRequest) (*http.Response, error)

	/*
		GetChargeSchedules Get all active charge schedules

		Get all active charge schedules for the given ppids

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ApiGetChargeSchedulesRequest
	*/
	GetChargeSchedules(ctx context.Context) ApiGetChargeSchedulesRequest

	// GetChargeSchedulesExecute executes the request
	//  @return map[string][]GetChargeSchedules200ResponseValueInner
	GetChargeSchedulesExecute(r ApiGetChargeSchedulesRequest) (*map[string][]GetChargeSchedules200ResponseValueInner, *http.Response, error)

	/*
		GetChargerChargeScheduleHistory Get historical charge schedules

		Get historical charge schedules for the given ppid

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid Accepts a single ppid to fetch old charge schedules
		@return ApiGetChargerChargeScheduleHistoryRequest
	*/
	GetChargerChargeScheduleHistory(ctx context.Context, ppid string) ApiGetChargerChargeScheduleHistoryRequest

	// GetChargerChargeScheduleHistoryExecute executes the request
	//  @return PaginatedSchedulesDto
	GetChargerChargeScheduleHistoryExecute(r ApiGetChargerChargeScheduleHistoryRequest) (*PaginatedSchedulesDto, *http.Response, error)

	/*
		GetCompositeChargeSchedule Get composite charge schedule

		Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@return ApiGetCompositeChargeScheduleRequest
	*/
	GetCompositeChargeSchedule(ctx context.Context, ppid string) ApiGetCompositeChargeScheduleRequest

	// GetCompositeChargeScheduleExecute executes the request
	//  @return CompositeScheduleDto
	GetCompositeChargeScheduleExecute(r ApiGetCompositeChargeScheduleRequest) (*CompositeScheduleDto, *http.Response, error)

	/*
		GetEffectiveChargeSchedulesInTimeRange Get effective charge schedules in time range

		Get effective charge schedules for the given ppids in the time range

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ApiGetEffectiveChargeSchedulesInTimeRangeRequest
	*/
	GetEffectiveChargeSchedulesInTimeRange(ctx context.Context) ApiGetEffectiveChargeSchedulesInTimeRangeRequest

	// GetEffectiveChargeSchedulesInTimeRangeExecute executes the request
	//  @return []EffectiveChargeSchedulesResponseDto
	GetEffectiveChargeSchedulesInTimeRangeExecute(r ApiGetEffectiveChargeSchedulesInTimeRangeRequest) ([]EffectiveChargeSchedulesResponseDto, *http.Response, error)

	/*
		SetDefaultChargeSchedule Set the default charge schedule for a charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid
		@return ApiSetDefaultChargeScheduleRequest
	*/
	SetDefaultChargeSchedule(ctx context.Context, ppid string) ApiSetDefaultChargeScheduleRequest

	// SetDefaultChargeScheduleExecute executes the request
	SetDefaultChargeScheduleExecute(r ApiSetDefaultChargeScheduleRequest) (*http.Response, error)
}

// ChargeSchedulesAPIService ChargeSchedulesAPI service
type ChargeSchedulesAPIService service

type ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	from       *string
	items      *float32
	page       *float32
}

// Start date of the charge schedule
func (r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) From(from string) ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest {
	r.from = &from
	return r
}

// Number of items to return
func (r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) Items(items float32) ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest {
	r.items = &items
	return r
}

// Page number
func (r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) Page(page float32) ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest {
	r.page = &page
	return r
}

func (r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) Execute() (*http.Response, error) {
	return r.ApiService.ChargeScheduleControllerGetChargingStationsWithChargeSchedulesExecute(r)
}

/*
ChargeScheduleControllerGetChargingStationsWithChargeSchedules Method for ChargeScheduleControllerGetChargingStationsWithChargeSchedules

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest
*/
func (a *ChargeSchedulesAPIService) ChargeScheduleControllerGetChargingStationsWithChargeSchedules(ctx context.Context) ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest {
	return ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
func (a *ChargeSchedulesAPIService) ChargeScheduleControllerGetChargingStationsWithChargeSchedulesExecute(r ApiChargeScheduleControllerGetChargingStationsWithChargeSchedulesRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodGet
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.ChargeScheduleControllerGetChargingStationsWithChargeSchedules")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/charging-stations"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.from == nil {
		return nil, reportError("from is required and must be specified")
	}

	if r.items != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "items", r.items, "form", "")
	} else {
		var defaultValue float32 = 100
		r.items = &defaultValue
	}
	if r.page != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page, "form", "")
	} else {
		var defaultValue float32 = 1
		r.page = &defaultValue
	}
	parameterAddToHeaderOrQuery(localVarQueryParams, "from", r.from, "form", "")
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiDeleteChargeScheduleHistoryRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	ppid       string
}

func (r ApiDeleteChargeScheduleHistoryRequest) Execute() (*http.Response, error) {
	return r.ApiService.DeleteChargeScheduleHistoryExecute(r)
}

/*
DeleteChargeScheduleHistory Delete old charge schedules

Delete old charge schedules for the given ppid

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid Accepts a single ppid to delete old charge schedules
	@return ApiDeleteChargeScheduleHistoryRequest
*/
func (a *ChargeSchedulesAPIService) DeleteChargeScheduleHistory(ctx context.Context, ppid string) ApiDeleteChargeScheduleHistoryRequest {
	return ApiDeleteChargeScheduleHistoryRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *ChargeSchedulesAPIService) DeleteChargeScheduleHistoryExecute(r ApiDeleteChargeScheduleHistoryRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodDelete
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.DeleteChargeScheduleHistory")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/{ppid}/history"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiGetChargeSchedulesRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	ppid       *[]string
}

// Accepts comma separated ppid(s) to batch fetch their charge schedules
func (r ApiGetChargeSchedulesRequest) Ppid(ppid []string) ApiGetChargeSchedulesRequest {
	r.ppid = &ppid
	return r
}

func (r ApiGetChargeSchedulesRequest) Execute() (*map[string][]GetChargeSchedules200ResponseValueInner, *http.Response, error) {
	return r.ApiService.GetChargeSchedulesExecute(r)
}

/*
GetChargeSchedules Get all active charge schedules

Get all active charge schedules for the given ppids

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiGetChargeSchedulesRequest
*/
func (a *ChargeSchedulesAPIService) GetChargeSchedules(ctx context.Context) ApiGetChargeSchedulesRequest {
	return ApiGetChargeSchedulesRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return map[string][]GetChargeSchedules200ResponseValueInner
func (a *ChargeSchedulesAPIService) GetChargeSchedulesExecute(r ApiGetChargeSchedulesRequest) (*map[string][]GetChargeSchedules200ResponseValueInner, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *map[string][]GetChargeSchedules200ResponseValueInner
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.GetChargeSchedules")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.ppid == nil {
		return localVarReturnValue, nil, reportError("ppid is required and must be specified")
	}

	{
		t := *r.ppid
		if reflect.TypeOf(t).Kind() == reflect.Slice {
			s := reflect.ValueOf(t)
			for i := 0; i < s.Len(); i++ {
				parameterAddToHeaderOrQuery(localVarQueryParams, "ppid", s.Index(i).Interface(), "form", "multi")
			}
		} else {
			parameterAddToHeaderOrQuery(localVarQueryParams, "ppid", t, "form", "multi")
		}
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetChargerChargeScheduleHistoryRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	ppid       string
	from       *string
	to         *string
	items      *float32
	page       *float32
}

// Start date of the charge schedule
func (r ApiGetChargerChargeScheduleHistoryRequest) From(from string) ApiGetChargerChargeScheduleHistoryRequest {
	r.from = &from
	return r
}

// End date of the charge schedule
func (r ApiGetChargerChargeScheduleHistoryRequest) To(to string) ApiGetChargerChargeScheduleHistoryRequest {
	r.to = &to
	return r
}

// Number of items to return
func (r ApiGetChargerChargeScheduleHistoryRequest) Items(items float32) ApiGetChargerChargeScheduleHistoryRequest {
	r.items = &items
	return r
}

// Page number
func (r ApiGetChargerChargeScheduleHistoryRequest) Page(page float32) ApiGetChargerChargeScheduleHistoryRequest {
	r.page = &page
	return r
}

func (r ApiGetChargerChargeScheduleHistoryRequest) Execute() (*PaginatedSchedulesDto, *http.Response, error) {
	return r.ApiService.GetChargerChargeScheduleHistoryExecute(r)
}

/*
GetChargerChargeScheduleHistory Get historical charge schedules

Get historical charge schedules for the given ppid

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid Accepts a single ppid to fetch old charge schedules
	@return ApiGetChargerChargeScheduleHistoryRequest
*/
func (a *ChargeSchedulesAPIService) GetChargerChargeScheduleHistory(ctx context.Context, ppid string) ApiGetChargerChargeScheduleHistoryRequest {
	return ApiGetChargerChargeScheduleHistoryRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return PaginatedSchedulesDto
func (a *ChargeSchedulesAPIService) GetChargerChargeScheduleHistoryExecute(r ApiGetChargerChargeScheduleHistoryRequest) (*PaginatedSchedulesDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *PaginatedSchedulesDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.GetChargerChargeScheduleHistory")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/{ppid}/history"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.from == nil {
		return localVarReturnValue, nil, reportError("from is required and must be specified")
	}
	if r.to == nil {
		return localVarReturnValue, nil, reportError("to is required and must be specified")
	}

	if r.items != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "items", r.items, "form", "")
	} else {
		var defaultValue float32 = 100
		r.items = &defaultValue
	}
	if r.page != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page, "form", "")
	} else {
		var defaultValue float32 = 1
		r.page = &defaultValue
	}
	parameterAddToHeaderOrQuery(localVarQueryParams, "from", r.from, "form", "")
	parameterAddToHeaderOrQuery(localVarQueryParams, "to", r.to, "form", "")
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetCompositeChargeScheduleRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	ppid       string
	duration   *float32
}

// The duration in seconds for which the composite schedule is requested
func (r ApiGetCompositeChargeScheduleRequest) Duration(duration float32) ApiGetCompositeChargeScheduleRequest {
	r.duration = &duration
	return r
}

func (r ApiGetCompositeChargeScheduleRequest) Execute() (*CompositeScheduleDto, *http.Response, error) {
	return r.ApiService.GetCompositeChargeScheduleExecute(r)
}

/*
GetCompositeChargeSchedule Get composite charge schedule

Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@return ApiGetCompositeChargeScheduleRequest
*/
func (a *ChargeSchedulesAPIService) GetCompositeChargeSchedule(ctx context.Context, ppid string) ApiGetCompositeChargeScheduleRequest {
	return ApiGetCompositeChargeScheduleRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return CompositeScheduleDto
func (a *ChargeSchedulesAPIService) GetCompositeChargeScheduleExecute(r ApiGetCompositeChargeScheduleRequest) (*CompositeScheduleDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *CompositeScheduleDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.GetCompositeChargeSchedule")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/{ppid}/composite"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if r.duration != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "duration", r.duration, "form", "")
	} else {
		var defaultValue float32 = 7200
		r.duration = &defaultValue
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiGetEffectiveChargeSchedulesInTimeRangeRequest struct {
	ctx                         context.Context
	ApiService                  ChargeSchedulesAPI
	effectiveChargeSchedulesDto *EffectiveChargeSchedulesDto
}

func (r ApiGetEffectiveChargeSchedulesInTimeRangeRequest) EffectiveChargeSchedulesDto(effectiveChargeSchedulesDto EffectiveChargeSchedulesDto) ApiGetEffectiveChargeSchedulesInTimeRangeRequest {
	r.effectiveChargeSchedulesDto = &effectiveChargeSchedulesDto
	return r
}

func (r ApiGetEffectiveChargeSchedulesInTimeRangeRequest) Execute() ([]EffectiveChargeSchedulesResponseDto, *http.Response, error) {
	return r.ApiService.GetEffectiveChargeSchedulesInTimeRangeExecute(r)
}

/*
GetEffectiveChargeSchedulesInTimeRange Get effective charge schedules in time range

Get effective charge schedules for the given ppids in the time range

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiGetEffectiveChargeSchedulesInTimeRangeRequest
*/
func (a *ChargeSchedulesAPIService) GetEffectiveChargeSchedulesInTimeRange(ctx context.Context) ApiGetEffectiveChargeSchedulesInTimeRangeRequest {
	return ApiGetEffectiveChargeSchedulesInTimeRangeRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return []EffectiveChargeSchedulesResponseDto
func (a *ChargeSchedulesAPIService) GetEffectiveChargeSchedulesInTimeRangeExecute(r ApiGetEffectiveChargeSchedulesInTimeRangeRequest) ([]EffectiveChargeSchedulesResponseDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodPost
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue []EffectiveChargeSchedulesResponseDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.GetEffectiveChargeSchedulesInTimeRange")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/effective"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}
	if r.effectiveChargeSchedulesDto == nil {
		return localVarReturnValue, nil, reportError("effectiveChargeSchedulesDto is required and must be specified")
	}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.effectiveChargeSchedulesDto
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ApiSetDefaultChargeScheduleRequest struct {
	ctx        context.Context
	ApiService ChargeSchedulesAPI
	ppid       string
}

func (r ApiSetDefaultChargeScheduleRequest) Execute() (*http.Response, error) {
	return r.ApiService.SetDefaultChargeScheduleExecute(r)
}

/*
SetDefaultChargeSchedule Set the default charge schedule for a charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid
	@return ApiSetDefaultChargeScheduleRequest
*/
func (a *ChargeSchedulesAPIService) SetDefaultChargeSchedule(ctx context.Context, ppid string) ApiSetDefaultChargeScheduleRequest {
	return ApiSetDefaultChargeScheduleRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *ChargeSchedulesAPIService) SetDefaultChargeScheduleExecute(r ApiSetDefaultChargeScheduleRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPut
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargeSchedulesAPIService.SetDefaultChargeSchedule")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charge-schedules/{ppid}/default"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}
