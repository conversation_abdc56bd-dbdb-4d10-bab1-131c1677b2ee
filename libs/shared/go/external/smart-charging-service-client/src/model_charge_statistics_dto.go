/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the ChargeStatisticsDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargeStatisticsDto{}

// ChargeStatisticsDto struct for ChargeStatisticsDto
type ChargeStatisticsDto struct {
	// The start time of the charge (ISO 8601)
	ChargeStartTime time.Time `json:"chargeStartTime"`
	// The end time of the charge (ISO 8601)
	ChargeEndTime time.Time `json:"chargeEndTime"`
	// The energy (kWh) from the grid used by the charge
	KWhFromGrid float32 `json:"kWhFromGrid"`
	// The energy (kWh) from local-generated sources (e.g. by solar) used by the charge
	KWhFromGeneration float32 `json:"kWhFromGeneration"`
}

type _ChargeStatisticsDto ChargeStatisticsDto

// NewChargeStatisticsDto instantiates a new ChargeStatisticsDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargeStatisticsDto(chargeStartTime time.Time, chargeEndTime time.Time, kWhFromGrid float32, kWhFromGeneration float32) *ChargeStatisticsDto {
	this := ChargeStatisticsDto{}
	this.ChargeStartTime = chargeStartTime
	this.ChargeEndTime = chargeEndTime
	this.KWhFromGrid = kWhFromGrid
	this.KWhFromGeneration = kWhFromGeneration
	return &this
}

// NewChargeStatisticsDtoWithDefaults instantiates a new ChargeStatisticsDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargeStatisticsDtoWithDefaults() *ChargeStatisticsDto {
	this := ChargeStatisticsDto{}
	return &this
}

// GetChargeStartTime returns the ChargeStartTime field value
func (o *ChargeStatisticsDto) GetChargeStartTime() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.ChargeStartTime
}

// GetChargeStartTimeOk returns a tuple with the ChargeStartTime field value
// and a boolean to check if the value has been set.
func (o *ChargeStatisticsDto) GetChargeStartTimeOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargeStartTime, true
}

// SetChargeStartTime sets field value
func (o *ChargeStatisticsDto) SetChargeStartTime(v time.Time) {
	o.ChargeStartTime = v
}

// GetChargeEndTime returns the ChargeEndTime field value
func (o *ChargeStatisticsDto) GetChargeEndTime() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.ChargeEndTime
}

// GetChargeEndTimeOk returns a tuple with the ChargeEndTime field value
// and a boolean to check if the value has been set.
func (o *ChargeStatisticsDto) GetChargeEndTimeOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargeEndTime, true
}

// SetChargeEndTime sets field value
func (o *ChargeStatisticsDto) SetChargeEndTime(v time.Time) {
	o.ChargeEndTime = v
}

// GetKWhFromGrid returns the KWhFromGrid field value
func (o *ChargeStatisticsDto) GetKWhFromGrid() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.KWhFromGrid
}

// GetKWhFromGridOk returns a tuple with the KWhFromGrid field value
// and a boolean to check if the value has been set.
func (o *ChargeStatisticsDto) GetKWhFromGridOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.KWhFromGrid, true
}

// SetKWhFromGrid sets field value
func (o *ChargeStatisticsDto) SetKWhFromGrid(v float32) {
	o.KWhFromGrid = v
}

// GetKWhFromGeneration returns the KWhFromGeneration field value
func (o *ChargeStatisticsDto) GetKWhFromGeneration() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.KWhFromGeneration
}

// GetKWhFromGenerationOk returns a tuple with the KWhFromGeneration field value
// and a boolean to check if the value has been set.
func (o *ChargeStatisticsDto) GetKWhFromGenerationOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.KWhFromGeneration, true
}

// SetKWhFromGeneration sets field value
func (o *ChargeStatisticsDto) SetKWhFromGeneration(v float32) {
	o.KWhFromGeneration = v
}

func (o ChargeStatisticsDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargeStatisticsDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["chargeStartTime"] = o.ChargeStartTime
	toSerialize["chargeEndTime"] = o.ChargeEndTime
	toSerialize["kWhFromGrid"] = o.KWhFromGrid
	toSerialize["kWhFromGeneration"] = o.KWhFromGeneration
	return toSerialize, nil
}

func (o *ChargeStatisticsDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"chargeStartTime",
		"chargeEndTime",
		"kWhFromGrid",
		"kWhFromGeneration",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargeStatisticsDto := _ChargeStatisticsDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargeStatisticsDto)

	if err != nil {
		return err
	}

	*o = ChargeStatisticsDto(varChargeStatisticsDto)

	return err
}

type NullableChargeStatisticsDto struct {
	value *ChargeStatisticsDto
	isSet bool
}

func (v NullableChargeStatisticsDto) Get() *ChargeStatisticsDto {
	return v.value
}

func (v *NullableChargeStatisticsDto) Set(val *ChargeStatisticsDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargeStatisticsDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargeStatisticsDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargeStatisticsDto(val *ChargeStatisticsDto) *NullableChargeStatisticsDto {
	return &NullableChargeStatisticsDto{value: val, isSet: true}
}

func (v NullableChargeStatisticsDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargeStatisticsDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
