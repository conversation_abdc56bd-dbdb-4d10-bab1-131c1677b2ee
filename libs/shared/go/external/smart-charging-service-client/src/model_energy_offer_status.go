/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the EnergyOfferStatus type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &EnergyOfferStatus{}

// EnergyOfferStatus struct for EnergyOfferStatus
type EnergyOfferStatus struct {
	// Whether the charging station is offering energy
	IsOfferingEnergy bool `json:"isOfferingEnergy"`
	// The date and time the reason for the offering energy status will change
	Until NullableTime `json:"until"`
	// The reason the for the offering energy
	Reason      string       `json:"reason"`
	RandomDelay NullableBool `json:"randomDelay,omitempty"`
}

type _EnergyOfferStatus EnergyOfferStatus

// NewEnergyOfferStatus instantiates a new EnergyOfferStatus object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewEnergyOfferStatus(isOfferingEnergy bool, until NullableTime, reason string) *EnergyOfferStatus {
	this := EnergyOfferStatus{}
	this.IsOfferingEnergy = isOfferingEnergy
	this.Until = until
	this.Reason = reason
	return &this
}

// NewEnergyOfferStatusWithDefaults instantiates a new EnergyOfferStatus object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewEnergyOfferStatusWithDefaults() *EnergyOfferStatus {
	this := EnergyOfferStatus{}
	return &this
}

// GetIsOfferingEnergy returns the IsOfferingEnergy field value
func (o *EnergyOfferStatus) GetIsOfferingEnergy() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.IsOfferingEnergy
}

// GetIsOfferingEnergyOk returns a tuple with the IsOfferingEnergy field value
// and a boolean to check if the value has been set.
func (o *EnergyOfferStatus) GetIsOfferingEnergyOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.IsOfferingEnergy, true
}

// SetIsOfferingEnergy sets field value
func (o *EnergyOfferStatus) SetIsOfferingEnergy(v bool) {
	o.IsOfferingEnergy = v
}

// GetUntil returns the Until field value
// If the value is explicit nil, the zero value for time.Time will be returned
func (o *EnergyOfferStatus) GetUntil() time.Time {
	if o == nil || o.Until.Get() == nil {
		var ret time.Time
		return ret
	}

	return *o.Until.Get()
}

// GetUntilOk returns a tuple with the Until field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *EnergyOfferStatus) GetUntilOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return o.Until.Get(), o.Until.IsSet()
}

// SetUntil sets field value
func (o *EnergyOfferStatus) SetUntil(v time.Time) {
	o.Until.Set(&v)
}

// GetReason returns the Reason field value
func (o *EnergyOfferStatus) GetReason() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Reason
}

// GetReasonOk returns a tuple with the Reason field value
// and a boolean to check if the value has been set.
func (o *EnergyOfferStatus) GetReasonOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Reason, true
}

// SetReason sets field value
func (o *EnergyOfferStatus) SetReason(v string) {
	o.Reason = v
}

// GetRandomDelay returns the RandomDelay field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *EnergyOfferStatus) GetRandomDelay() bool {
	if o == nil || IsNil(o.RandomDelay.Get()) {
		var ret bool
		return ret
	}
	return *o.RandomDelay.Get()
}

// GetRandomDelayOk returns a tuple with the RandomDelay field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *EnergyOfferStatus) GetRandomDelayOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return o.RandomDelay.Get(), o.RandomDelay.IsSet()
}

// HasRandomDelay returns a boolean if a field has been set.
func (o *EnergyOfferStatus) HasRandomDelay() bool {
	if o != nil && o.RandomDelay.IsSet() {
		return true
	}

	return false
}

// SetRandomDelay gets a reference to the given NullableBool and assigns it to the RandomDelay field.
func (o *EnergyOfferStatus) SetRandomDelay(v bool) {
	o.RandomDelay.Set(&v)
}

// SetRandomDelayNil sets the value for RandomDelay to be an explicit nil
func (o *EnergyOfferStatus) SetRandomDelayNil() {
	o.RandomDelay.Set(nil)
}

// UnsetRandomDelay ensures that no value is present for RandomDelay, not even an explicit nil
func (o *EnergyOfferStatus) UnsetRandomDelay() {
	o.RandomDelay.Unset()
}

func (o EnergyOfferStatus) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o EnergyOfferStatus) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["isOfferingEnergy"] = o.IsOfferingEnergy
	toSerialize["until"] = o.Until.Get()
	toSerialize["reason"] = o.Reason
	if o.RandomDelay.IsSet() {
		toSerialize["randomDelay"] = o.RandomDelay.Get()
	}
	return toSerialize, nil
}

func (o *EnergyOfferStatus) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"isOfferingEnergy",
		"until",
		"reason",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varEnergyOfferStatus := _EnergyOfferStatus{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varEnergyOfferStatus)

	if err != nil {
		return err
	}

	*o = EnergyOfferStatus(varEnergyOfferStatus)

	return err
}

type NullableEnergyOfferStatus struct {
	value *EnergyOfferStatus
	isSet bool
}

func (v NullableEnergyOfferStatus) Get() *EnergyOfferStatus {
	return v.value
}

func (v *NullableEnergyOfferStatus) Set(val *EnergyOfferStatus) {
	v.value = val
	v.isSet = true
}

func (v NullableEnergyOfferStatus) IsSet() bool {
	return v.isSet
}

func (v *NullableEnergyOfferStatus) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableEnergyOfferStatus(val *EnergyOfferStatus) *NullableEnergyOfferStatus {
	return &NullableEnergyOfferStatus{value: val, isSet: true}
}

func (v NullableEnergyOfferStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableEnergyOfferStatus) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
