/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the TriggerCommandRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &TriggerCommandRequest{}

// TriggerCommandRequest struct for TriggerCommandRequest
type TriggerCommandRequest struct {
	// A list of PPIDs to send the command to
	Ppids []string `json:"ppids"`
	// The command to send to the charging stations
	Command string `json:"command"`
}

type _TriggerCommandRequest TriggerCommandRequest

// NewTriggerCommandRequest instantiates a new TriggerCommandRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTriggerCommandRequest(ppids []string, command string) *TriggerCommandRequest {
	this := TriggerCommandRequest{}
	this.Ppids = ppids
	this.Command = command
	return &this
}

// NewTriggerCommandRequestWithDefaults instantiates a new TriggerCommandRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTriggerCommandRequestWithDefaults() *TriggerCommandRequest {
	this := TriggerCommandRequest{}
	return &this
}

// GetPpids returns the Ppids field value
func (o *TriggerCommandRequest) GetPpids() []string {
	if o == nil {
		var ret []string
		return ret
	}

	return o.Ppids
}

// GetPpidsOk returns a tuple with the Ppids field value
// and a boolean to check if the value has been set.
func (o *TriggerCommandRequest) GetPpidsOk() ([]string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Ppids, true
}

// SetPpids sets field value
func (o *TriggerCommandRequest) SetPpids(v []string) {
	o.Ppids = v
}

// GetCommand returns the Command field value
func (o *TriggerCommandRequest) GetCommand() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Command
}

// GetCommandOk returns a tuple with the Command field value
// and a boolean to check if the value has been set.
func (o *TriggerCommandRequest) GetCommandOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Command, true
}

// SetCommand sets field value
func (o *TriggerCommandRequest) SetCommand(v string) {
	o.Command = v
}

func (o TriggerCommandRequest) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o TriggerCommandRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["ppids"] = o.Ppids
	toSerialize["command"] = o.Command
	return toSerialize, nil
}

func (o *TriggerCommandRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"ppids",
		"command",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varTriggerCommandRequest := _TriggerCommandRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varTriggerCommandRequest)

	if err != nil {
		return err
	}

	*o = TriggerCommandRequest(varTriggerCommandRequest)

	return err
}

type NullableTriggerCommandRequest struct {
	value *TriggerCommandRequest
	isSet bool
}

func (v NullableTriggerCommandRequest) Get() *TriggerCommandRequest {
	return v.value
}

func (v *NullableTriggerCommandRequest) Set(val *TriggerCommandRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableTriggerCommandRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableTriggerCommandRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTriggerCommandRequest(val *TriggerCommandRequest) *NullableTriggerCommandRequest {
	return &NullableTriggerCommandRequest{value: val, isSet: true}
}

func (v NullableTriggerCommandRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTriggerCommandRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
