package smartchargingserviceclient

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"

	"k8s.io/utils/ptr"
)

var userAgent = "xdp-smart-charging-service-client"

type RewardsAPIStub struct {
}

// NewRewardsAPIClient creates a new RewardPointsAPI
func NewRewardsAPIClient(isProd bool, host string) (RewardsAPI, error) {
	if host == "" {
		return nil, errors.New("NewRewardsAPIClient: host is required")
	}

	if isProd {
		scsCfg := NewConfiguration()
		scsCfg.UserAgent = userAgent
		scsCfg.Servers = ServerConfigurations{{
			URL: host,
		},
		}
		client := NewAPIClient(scsCfg)
		return client.RewardsAPI, nil
	}

	return &RewardsAPIStub{}, nil
}

func (a RewardsAPIStub) ConvertChargeToRewardPoints(ctx context.Context, ppid string) ApiConvertChargeToRewardPointsRequest {
	return ApiConvertChargeToRewardPointsRequest{
		ctx:                 ctx,
		ApiService:          &RewardsAPIStub{},
		ppid:                ppid,
		chargeStatisticsDto: NewChargeStatisticsDtoWithDefaults(),
	}
}

func (a RewardsAPIStub) ConvertChargeToRewardPointsExecute(_ ApiConvertChargeToRewardPointsRequest) (*RewardPointsDto, *http.Response, error) {
	stubResponse := http.Response{
		Body:       io.NopCloser(bytes.NewBufferString("")),
		StatusCode: http.StatusOK,
	}
	return &RewardPointsDto{
		Points: 10.5,
		Reason: "ELIGIBLE",
	}, &stubResponse, nil
}

func (a RewardsAPIStub) GetChargingSessionRewardInfo(ctx context.Context, ppid string) ApiGetChargingSessionRewardInfoRequest {
	return ApiGetChargingSessionRewardInfoRequest{
		ctx:                 ctx,
		ApiService:          &RewardsAPIStub{},
		ppid:                ppid,
		chargeStatisticsDto: NewChargeStatisticsDtoWithDefaults(),
	}
}

func (a RewardsAPIStub) GetChargingSessionRewardInfoExecute(_ ApiGetChargingSessionRewardInfoRequest) (*RewardInfoDto, *http.Response, error) {
	stubResponse := http.Response{
		Body:       io.NopCloser(bytes.NewBufferString("")),
		StatusCode: http.StatusOK,
	}

	return &RewardInfoDto{
		RewardableEnergyKwh: 20.45,
		VehicleId: NullableString{
			value: ptr.To("2f727f8b-7c0e-43b3-b455-a1d7f07ea036"),
			isSet: true,
		},
	}, &stubResponse, nil
}
