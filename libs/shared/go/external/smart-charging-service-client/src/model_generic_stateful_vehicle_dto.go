/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the GenericStatefulVehicleDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &GenericStatefulVehicleDto{}

// GenericStatefulVehicleDto struct for GenericStatefulVehicleDto
type GenericStatefulVehicleDto struct {
	// The vehicle ID
	Id string `json:"id"`
	// The enode user ID
	EnodeUserId NullableString `json:"enodeUserId,omitempty"`
	// The vehicle enode ID
	EnodeVehicleId NullableString `json:"enodeVehicleId,omitempty"`
	// The vehicle information
	VehicleInformation VehicleInformation `json:"vehicleInformation"`
	// The vehicle charge state
	ChargeState GenericChargeState `json:"chargeState"`
}

type _GenericStatefulVehicleDto GenericStatefulVehicleDto

// NewGenericStatefulVehicleDto instantiates a new GenericStatefulVehicleDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewGenericStatefulVehicleDto(id string, vehicleInformation VehicleInformation, chargeState GenericChargeState) *GenericStatefulVehicleDto {
	this := GenericStatefulVehicleDto{}
	this.Id = id
	this.VehicleInformation = vehicleInformation
	this.ChargeState = chargeState
	return &this
}

// NewGenericStatefulVehicleDtoWithDefaults instantiates a new GenericStatefulVehicleDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewGenericStatefulVehicleDtoWithDefaults() *GenericStatefulVehicleDto {
	this := GenericStatefulVehicleDto{}
	return &this
}

// GetId returns the Id field value
func (o *GenericStatefulVehicleDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *GenericStatefulVehicleDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *GenericStatefulVehicleDto) SetId(v string) {
	o.Id = v
}

// GetEnodeUserId returns the EnodeUserId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *GenericStatefulVehicleDto) GetEnodeUserId() string {
	if o == nil || IsNil(o.EnodeUserId.Get()) {
		var ret string
		return ret
	}
	return *o.EnodeUserId.Get()
}

// GetEnodeUserIdOk returns a tuple with the EnodeUserId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *GenericStatefulVehicleDto) GetEnodeUserIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.EnodeUserId.Get(), o.EnodeUserId.IsSet()
}

// HasEnodeUserId returns a boolean if a field has been set.
func (o *GenericStatefulVehicleDto) HasEnodeUserId() bool {
	if o != nil && o.EnodeUserId.IsSet() {
		return true
	}

	return false
}

// SetEnodeUserId gets a reference to the given NullableString and assigns it to the EnodeUserId field.
func (o *GenericStatefulVehicleDto) SetEnodeUserId(v string) {
	o.EnodeUserId.Set(&v)
}

// SetEnodeUserIdNil sets the value for EnodeUserId to be an explicit nil
func (o *GenericStatefulVehicleDto) SetEnodeUserIdNil() {
	o.EnodeUserId.Set(nil)
}

// UnsetEnodeUserId ensures that no value is present for EnodeUserId, not even an explicit nil
func (o *GenericStatefulVehicleDto) UnsetEnodeUserId() {
	o.EnodeUserId.Unset()
}

// GetEnodeVehicleId returns the EnodeVehicleId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *GenericStatefulVehicleDto) GetEnodeVehicleId() string {
	if o == nil || IsNil(o.EnodeVehicleId.Get()) {
		var ret string
		return ret
	}
	return *o.EnodeVehicleId.Get()
}

// GetEnodeVehicleIdOk returns a tuple with the EnodeVehicleId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *GenericStatefulVehicleDto) GetEnodeVehicleIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.EnodeVehicleId.Get(), o.EnodeVehicleId.IsSet()
}

// HasEnodeVehicleId returns a boolean if a field has been set.
func (o *GenericStatefulVehicleDto) HasEnodeVehicleId() bool {
	if o != nil && o.EnodeVehicleId.IsSet() {
		return true
	}

	return false
}

// SetEnodeVehicleId gets a reference to the given NullableString and assigns it to the EnodeVehicleId field.
func (o *GenericStatefulVehicleDto) SetEnodeVehicleId(v string) {
	o.EnodeVehicleId.Set(&v)
}

// SetEnodeVehicleIdNil sets the value for EnodeVehicleId to be an explicit nil
func (o *GenericStatefulVehicleDto) SetEnodeVehicleIdNil() {
	o.EnodeVehicleId.Set(nil)
}

// UnsetEnodeVehicleId ensures that no value is present for EnodeVehicleId, not even an explicit nil
func (o *GenericStatefulVehicleDto) UnsetEnodeVehicleId() {
	o.EnodeVehicleId.Unset()
}

// GetVehicleInformation returns the VehicleInformation field value
func (o *GenericStatefulVehicleDto) GetVehicleInformation() VehicleInformation {
	if o == nil {
		var ret VehicleInformation
		return ret
	}

	return o.VehicleInformation
}

// GetVehicleInformationOk returns a tuple with the VehicleInformation field value
// and a boolean to check if the value has been set.
func (o *GenericStatefulVehicleDto) GetVehicleInformationOk() (*VehicleInformation, bool) {
	if o == nil {
		return nil, false
	}
	return &o.VehicleInformation, true
}

// SetVehicleInformation sets field value
func (o *GenericStatefulVehicleDto) SetVehicleInformation(v VehicleInformation) {
	o.VehicleInformation = v
}

// GetChargeState returns the ChargeState field value
func (o *GenericStatefulVehicleDto) GetChargeState() GenericChargeState {
	if o == nil {
		var ret GenericChargeState
		return ret
	}

	return o.ChargeState
}

// GetChargeStateOk returns a tuple with the ChargeState field value
// and a boolean to check if the value has been set.
func (o *GenericStatefulVehicleDto) GetChargeStateOk() (*GenericChargeState, bool) {
	if o == nil {
		return nil, false
	}
	return &o.ChargeState, true
}

// SetChargeState sets field value
func (o *GenericStatefulVehicleDto) SetChargeState(v GenericChargeState) {
	o.ChargeState = v
}

func (o GenericStatefulVehicleDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o GenericStatefulVehicleDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["id"] = o.Id
	if o.EnodeUserId.IsSet() {
		toSerialize["enodeUserId"] = o.EnodeUserId.Get()
	}
	if o.EnodeVehicleId.IsSet() {
		toSerialize["enodeVehicleId"] = o.EnodeVehicleId.Get()
	}
	toSerialize["vehicleInformation"] = o.VehicleInformation
	toSerialize["chargeState"] = o.ChargeState
	return toSerialize, nil
}

func (o *GenericStatefulVehicleDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"id",
		"vehicleInformation",
		"chargeState",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varGenericStatefulVehicleDto := _GenericStatefulVehicleDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varGenericStatefulVehicleDto)

	if err != nil {
		return err
	}

	*o = GenericStatefulVehicleDto(varGenericStatefulVehicleDto)

	return err
}

type NullableGenericStatefulVehicleDto struct {
	value *GenericStatefulVehicleDto
	isSet bool
}

func (v NullableGenericStatefulVehicleDto) Get() *GenericStatefulVehicleDto {
	return v.value
}

func (v *NullableGenericStatefulVehicleDto) Set(val *GenericStatefulVehicleDto) {
	v.value = val
	v.isSet = true
}

func (v NullableGenericStatefulVehicleDto) IsSet() bool {
	return v.isSet
}

func (v *NullableGenericStatefulVehicleDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableGenericStatefulVehicleDto(val *GenericStatefulVehicleDto) *NullableGenericStatefulVehicleDto {
	return &NullableGenericStatefulVehicleDto{value: val, isSet: true}
}

func (v NullableGenericStatefulVehicleDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableGenericStatefulVehicleDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
