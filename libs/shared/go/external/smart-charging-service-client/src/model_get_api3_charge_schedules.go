/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the GetApi3ChargeSchedules type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &GetApi3ChargeSchedules{}

// GetApi3ChargeSchedules struct for GetApi3ChargeSchedules
type GetApi3ChargeSchedules struct {
	Data []API3ChargeSchedule `json:"data"`
	Meta Meta                 `json:"meta"`
}

type _GetApi3ChargeSchedules GetApi3ChargeSchedules

// NewGetApi3ChargeSchedules instantiates a new GetApi3ChargeSchedules object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewGetApi3ChargeSchedules(data []API3ChargeSchedule, meta Meta) *GetApi3ChargeSchedules {
	this := GetApi3ChargeSchedules{}
	this.Data = data
	this.Meta = meta
	return &this
}

// NewGetApi3ChargeSchedulesWithDefaults instantiates a new GetApi3ChargeSchedules object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewGetApi3ChargeSchedulesWithDefaults() *GetApi3ChargeSchedules {
	this := GetApi3ChargeSchedules{}
	return &this
}

// GetData returns the Data field value
func (o *GetApi3ChargeSchedules) GetData() []API3ChargeSchedule {
	if o == nil {
		var ret []API3ChargeSchedule
		return ret
	}

	return o.Data
}

// GetDataOk returns a tuple with the Data field value
// and a boolean to check if the value has been set.
func (o *GetApi3ChargeSchedules) GetDataOk() ([]API3ChargeSchedule, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data, true
}

// SetData sets field value
func (o *GetApi3ChargeSchedules) SetData(v []API3ChargeSchedule) {
	o.Data = v
}

// GetMeta returns the Meta field value
func (o *GetApi3ChargeSchedules) GetMeta() Meta {
	if o == nil {
		var ret Meta
		return ret
	}

	return o.Meta
}

// GetMetaOk returns a tuple with the Meta field value
// and a boolean to check if the value has been set.
func (o *GetApi3ChargeSchedules) GetMetaOk() (*Meta, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Meta, true
}

// SetMeta sets field value
func (o *GetApi3ChargeSchedules) SetMeta(v Meta) {
	o.Meta = v
}

func (o GetApi3ChargeSchedules) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o GetApi3ChargeSchedules) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["data"] = o.Data
	toSerialize["meta"] = o.Meta
	return toSerialize, nil
}

func (o *GetApi3ChargeSchedules) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"data",
		"meta",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varGetApi3ChargeSchedules := _GetApi3ChargeSchedules{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varGetApi3ChargeSchedules)

	if err != nil {
		return err
	}

	*o = GetApi3ChargeSchedules(varGetApi3ChargeSchedules)

	return err
}

type NullableGetApi3ChargeSchedules struct {
	value *GetApi3ChargeSchedules
	isSet bool
}

func (v NullableGetApi3ChargeSchedules) Get() *GetApi3ChargeSchedules {
	return v.value
}

func (v *NullableGetApi3ChargeSchedules) Set(val *GetApi3ChargeSchedules) {
	v.value = val
	v.isSet = true
}

func (v NullableGetApi3ChargeSchedules) IsSet() bool {
	return v.isSet
}

func (v *NullableGetApi3ChargeSchedules) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableGetApi3ChargeSchedules(val *GetApi3ChargeSchedules) *NullableGetApi3ChargeSchedules {
	return &NullableGetApi3ChargeSchedules{value: val, isSet: true}
}

func (v NullableGetApi3ChargeSchedules) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableGetApi3ChargeSchedules) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
