/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the GetChargeSchedules200ResponseValueInner type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &GetChargeSchedules200ResponseValueInner{}

// GetChargeSchedules200ResponseValueInner struct for GetChargeSchedules200ResponseValueInner
type GetChargeSchedules200ResponseValueInner struct {
	StartDay  int32  `json:"startDay"`
	EndDay    int32  `json:"endDay"`
	StartTime string `json:"startTime" validate:"regexp=^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$"`
	EndTime   string `json:"endTime" validate:"regexp=^([01]{1}[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$"`
}

type _GetChargeSchedules200ResponseValueInner GetChargeSchedules200ResponseValueInner

// NewGetChargeSchedules200ResponseValueInner instantiates a new GetChargeSchedules200ResponseValueInner object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewGetChargeSchedules200ResponseValueInner(startDay int32, endDay int32, startTime string, endTime string) *GetChargeSchedules200ResponseValueInner {
	this := GetChargeSchedules200ResponseValueInner{}
	this.StartDay = startDay
	this.EndDay = endDay
	this.StartTime = startTime
	this.EndTime = endTime
	return &this
}

// NewGetChargeSchedules200ResponseValueInnerWithDefaults instantiates a new GetChargeSchedules200ResponseValueInner object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewGetChargeSchedules200ResponseValueInnerWithDefaults() *GetChargeSchedules200ResponseValueInner {
	this := GetChargeSchedules200ResponseValueInner{}
	return &this
}

// GetStartDay returns the StartDay field value
func (o *GetChargeSchedules200ResponseValueInner) GetStartDay() int32 {
	if o == nil {
		var ret int32
		return ret
	}

	return o.StartDay
}

// GetStartDayOk returns a tuple with the StartDay field value
// and a boolean to check if the value has been set.
func (o *GetChargeSchedules200ResponseValueInner) GetStartDayOk() (*int32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StartDay, true
}

// SetStartDay sets field value
func (o *GetChargeSchedules200ResponseValueInner) SetStartDay(v int32) {
	o.StartDay = v
}

// GetEndDay returns the EndDay field value
func (o *GetChargeSchedules200ResponseValueInner) GetEndDay() int32 {
	if o == nil {
		var ret int32
		return ret
	}

	return o.EndDay
}

// GetEndDayOk returns a tuple with the EndDay field value
// and a boolean to check if the value has been set.
func (o *GetChargeSchedules200ResponseValueInner) GetEndDayOk() (*int32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EndDay, true
}

// SetEndDay sets field value
func (o *GetChargeSchedules200ResponseValueInner) SetEndDay(v int32) {
	o.EndDay = v
}

// GetStartTime returns the StartTime field value
func (o *GetChargeSchedules200ResponseValueInner) GetStartTime() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.StartTime
}

// GetStartTimeOk returns a tuple with the StartTime field value
// and a boolean to check if the value has been set.
func (o *GetChargeSchedules200ResponseValueInner) GetStartTimeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StartTime, true
}

// SetStartTime sets field value
func (o *GetChargeSchedules200ResponseValueInner) SetStartTime(v string) {
	o.StartTime = v
}

// GetEndTime returns the EndTime field value
func (o *GetChargeSchedules200ResponseValueInner) GetEndTime() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value
// and a boolean to check if the value has been set.
func (o *GetChargeSchedules200ResponseValueInner) GetEndTimeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EndTime, true
}

// SetEndTime sets field value
func (o *GetChargeSchedules200ResponseValueInner) SetEndTime(v string) {
	o.EndTime = v
}

func (o GetChargeSchedules200ResponseValueInner) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o GetChargeSchedules200ResponseValueInner) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["startDay"] = o.StartDay
	toSerialize["endDay"] = o.EndDay
	toSerialize["startTime"] = o.StartTime
	toSerialize["endTime"] = o.EndTime
	return toSerialize, nil
}

func (o *GetChargeSchedules200ResponseValueInner) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"startDay",
		"endDay",
		"startTime",
		"endTime",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varGetChargeSchedules200ResponseValueInner := _GetChargeSchedules200ResponseValueInner{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varGetChargeSchedules200ResponseValueInner)

	if err != nil {
		return err
	}

	*o = GetChargeSchedules200ResponseValueInner(varGetChargeSchedules200ResponseValueInner)

	return err
}

type NullableGetChargeSchedules200ResponseValueInner struct {
	value *GetChargeSchedules200ResponseValueInner
	isSet bool
}

func (v NullableGetChargeSchedules200ResponseValueInner) Get() *GetChargeSchedules200ResponseValueInner {
	return v.value
}

func (v *NullableGetChargeSchedules200ResponseValueInner) Set(val *GetChargeSchedules200ResponseValueInner) {
	v.value = val
	v.isSet = true
}

func (v NullableGetChargeSchedules200ResponseValueInner) IsSet() bool {
	return v.isSet
}

func (v *NullableGetChargeSchedules200ResponseValueInner) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableGetChargeSchedules200ResponseValueInner(val *GetChargeSchedules200ResponseValueInner) *NullableGetChargeSchedules200ResponseValueInner {
	return &NullableGetChargeSchedules200ResponseValueInner{value: val, isSet: true}
}

func (v NullableGetChargeSchedules200ResponseValueInner) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableGetChargeSchedules200ResponseValueInner) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
