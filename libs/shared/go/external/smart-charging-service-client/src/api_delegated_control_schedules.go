/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type DelegatedControlSchedulesAPI interface {

	/*
		ClearTargetScheduleForChargingStation Clear target schedule(s) for a delegated control charging station

		This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiClearTargetScheduleForChargingStationRequest
	*/
	ClearTargetScheduleForChargingStation(ctx context.Context, ppid string) ApiClearTargetScheduleForChargingStationRequest

	// ClearTargetScheduleForChargingStationExecute executes the request
	ClearTargetScheduleForChargingStationExecute(r ApiClearTargetScheduleForChargingStationRequest) (*http.Response, error)

	/*
		SetTargetSchedulesForDelegatedControlChargingStation Request target schedules be set for connected vehicle connected to a delegated control charging station

		Request target schedules be set for the likely connected or default vehicle of a delegated control charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid The PPID of the charging station
		@return ApiSetTargetSchedulesForDelegatedControlChargingStationRequest
	*/
	SetTargetSchedulesForDelegatedControlChargingStation(ctx context.Context, ppid string) ApiSetTargetSchedulesForDelegatedControlChargingStationRequest

	// SetTargetSchedulesForDelegatedControlChargingStationExecute executes the request
	SetTargetSchedulesForDelegatedControlChargingStationExecute(r ApiSetTargetSchedulesForDelegatedControlChargingStationRequest) (*http.Response, error)
}

// DelegatedControlSchedulesAPIService DelegatedControlSchedulesAPI service
type DelegatedControlSchedulesAPIService service

type ApiClearTargetScheduleForChargingStationRequest struct {
	ctx        context.Context
	ApiService DelegatedControlSchedulesAPI
	ppid       string
}

func (r ApiClearTargetScheduleForChargingStationRequest) Execute() (*http.Response, error) {
	return r.ApiService.ClearTargetScheduleForChargingStationExecute(r)
}

/*
ClearTargetScheduleForChargingStation Clear target schedule(s) for a delegated control charging station

This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiClearTargetScheduleForChargingStationRequest
*/
func (a *DelegatedControlSchedulesAPIService) ClearTargetScheduleForChargingStation(ctx context.Context, ppid string) ApiClearTargetScheduleForChargingStationRequest {
	return ApiClearTargetScheduleForChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *DelegatedControlSchedulesAPIService) ClearTargetScheduleForChargingStationExecute(r ApiClearTargetScheduleForChargingStationRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodDelete
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlSchedulesAPIService.ClearTargetScheduleForChargingStation")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/schedules"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ApiSetTargetSchedulesForDelegatedControlChargingStationRequest struct {
	ctx        context.Context
	ApiService DelegatedControlSchedulesAPI
	ppid       string
	trigger    *string
}

// What triggered this request (if not provided, this is assumed to be PLUGIN)
func (r ApiSetTargetSchedulesForDelegatedControlChargingStationRequest) Trigger(trigger string) ApiSetTargetSchedulesForDelegatedControlChargingStationRequest {
	r.trigger = &trigger
	return r
}

func (r ApiSetTargetSchedulesForDelegatedControlChargingStationRequest) Execute() (*http.Response, error) {
	return r.ApiService.SetTargetSchedulesForDelegatedControlChargingStationExecute(r)
}

/*
SetTargetSchedulesForDelegatedControlChargingStation Request target schedules be set for connected vehicle connected to a delegated control charging station

Request target schedules be set for the likely connected or default vehicle of a delegated control charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid The PPID of the charging station
	@return ApiSetTargetSchedulesForDelegatedControlChargingStationRequest
*/
func (a *DelegatedControlSchedulesAPIService) SetTargetSchedulesForDelegatedControlChargingStation(ctx context.Context, ppid string) ApiSetTargetSchedulesForDelegatedControlChargingStationRequest {
	return ApiSetTargetSchedulesForDelegatedControlChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *DelegatedControlSchedulesAPIService) SetTargetSchedulesForDelegatedControlChargingStationExecute(r ApiSetTargetSchedulesForDelegatedControlChargingStationRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPut
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "DelegatedControlSchedulesAPIService.SetTargetSchedulesForDelegatedControlChargingStation")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/delegated-controls/{ppid}/schedules"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if r.trigger != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "trigger", r.trigger, "form", "")
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}
