/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the PaginatedSchedulesDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &PaginatedSchedulesDto{}

// PaginatedSchedulesDto struct for PaginatedSchedulesDto
type PaginatedSchedulesDto struct {
	Pagination DelegatedControlChargingStationSearchMetadataDtoPagination `json:"pagination"`
	// The charge schedules for the given ppids
	Schedules []ChargeScheduleDto `json:"schedules"`
}

type _PaginatedSchedulesDto PaginatedSchedulesDto

// NewPaginatedSchedulesDto instantiates a new PaginatedSchedulesDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewPaginatedSchedulesDto(pagination DelegatedControlChargingStationSearchMetadataDtoPagination, schedules []ChargeScheduleDto) *PaginatedSchedulesDto {
	this := PaginatedSchedulesDto{}
	this.Pagination = pagination
	this.Schedules = schedules
	return &this
}

// NewPaginatedSchedulesDtoWithDefaults instantiates a new PaginatedSchedulesDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewPaginatedSchedulesDtoWithDefaults() *PaginatedSchedulesDto {
	this := PaginatedSchedulesDto{}
	return &this
}

// GetPagination returns the Pagination field value
func (o *PaginatedSchedulesDto) GetPagination() DelegatedControlChargingStationSearchMetadataDtoPagination {
	if o == nil {
		var ret DelegatedControlChargingStationSearchMetadataDtoPagination
		return ret
	}

	return o.Pagination
}

// GetPaginationOk returns a tuple with the Pagination field value
// and a boolean to check if the value has been set.
func (o *PaginatedSchedulesDto) GetPaginationOk() (*DelegatedControlChargingStationSearchMetadataDtoPagination, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Pagination, true
}

// SetPagination sets field value
func (o *PaginatedSchedulesDto) SetPagination(v DelegatedControlChargingStationSearchMetadataDtoPagination) {
	o.Pagination = v
}

// GetSchedules returns the Schedules field value
func (o *PaginatedSchedulesDto) GetSchedules() []ChargeScheduleDto {
	if o == nil {
		var ret []ChargeScheduleDto
		return ret
	}

	return o.Schedules
}

// GetSchedulesOk returns a tuple with the Schedules field value
// and a boolean to check if the value has been set.
func (o *PaginatedSchedulesDto) GetSchedulesOk() ([]ChargeScheduleDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.Schedules, true
}

// SetSchedules sets field value
func (o *PaginatedSchedulesDto) SetSchedules(v []ChargeScheduleDto) {
	o.Schedules = v
}

func (o PaginatedSchedulesDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o PaginatedSchedulesDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["pagination"] = o.Pagination
	toSerialize["schedules"] = o.Schedules
	return toSerialize, nil
}

func (o *PaginatedSchedulesDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"pagination",
		"schedules",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varPaginatedSchedulesDto := _PaginatedSchedulesDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varPaginatedSchedulesDto)

	if err != nil {
		return err
	}

	*o = PaginatedSchedulesDto(varPaginatedSchedulesDto)

	return err
}

type NullablePaginatedSchedulesDto struct {
	value *PaginatedSchedulesDto
	isSet bool
}

func (v NullablePaginatedSchedulesDto) Get() *PaginatedSchedulesDto {
	return v.value
}

func (v *NullablePaginatedSchedulesDto) Set(val *PaginatedSchedulesDto) {
	v.value = val
	v.isSet = true
}

func (v NullablePaginatedSchedulesDto) IsSet() bool {
	return v.isSet
}

func (v *NullablePaginatedSchedulesDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullablePaginatedSchedulesDto(val *PaginatedSchedulesDto) *NullablePaginatedSchedulesDto {
	return &NullablePaginatedSchedulesDto{value: val, isSet: true}
}

func (v NullablePaginatedSchedulesDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullablePaginatedSchedulesDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
