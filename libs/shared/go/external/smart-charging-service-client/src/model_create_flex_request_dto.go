/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"
)

// checks if the CreateFlexRequestDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CreateFlexRequestDto{}

// CreateFlexRequestDto struct for CreateFlexRequestDto
type CreateFlexRequestDto struct {
	// The date and time the flexibility request was requested
	RequestedAt time.Time `json:"requestedAt"`
	// The date and time the flexibility request will start
	StartAt time.Time `json:"startAt"`
	// The date and time the flexibility request will end
	EndAt time.Time `json:"endAt"`
	// The direction of the flexibility request
	Direction string `json:"direction"`
	// The limit of the flexibility request
	Limit *FlexRequestLimitDto `json:"limit,omitempty"`
	// Details of the provider which originated this flex request
	Provider *FlexRequestProviderDto `json:"provider,omitempty"`
	// For a Programme-based flex request, the id of the message which triggered the request
	TriggerMessageId *string `json:"triggerMessageId,omitempty"`
}

type _CreateFlexRequestDto CreateFlexRequestDto

// NewCreateFlexRequestDto instantiates a new CreateFlexRequestDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCreateFlexRequestDto(requestedAt time.Time, startAt time.Time, endAt time.Time, direction string) *CreateFlexRequestDto {
	this := CreateFlexRequestDto{}
	this.RequestedAt = requestedAt
	this.StartAt = startAt
	this.EndAt = endAt
	this.Direction = direction
	return &this
}

// NewCreateFlexRequestDtoWithDefaults instantiates a new CreateFlexRequestDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCreateFlexRequestDtoWithDefaults() *CreateFlexRequestDto {
	this := CreateFlexRequestDto{}
	return &this
}

// GetRequestedAt returns the RequestedAt field value
func (o *CreateFlexRequestDto) GetRequestedAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.RequestedAt
}

// GetRequestedAtOk returns a tuple with the RequestedAt field value
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetRequestedAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.RequestedAt, true
}

// SetRequestedAt sets field value
func (o *CreateFlexRequestDto) SetRequestedAt(v time.Time) {
	o.RequestedAt = v
}

// GetStartAt returns the StartAt field value
func (o *CreateFlexRequestDto) GetStartAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.StartAt
}

// GetStartAtOk returns a tuple with the StartAt field value
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetStartAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StartAt, true
}

// SetStartAt sets field value
func (o *CreateFlexRequestDto) SetStartAt(v time.Time) {
	o.StartAt = v
}

// GetEndAt returns the EndAt field value
func (o *CreateFlexRequestDto) GetEndAt() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.EndAt
}

// GetEndAtOk returns a tuple with the EndAt field value
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetEndAtOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EndAt, true
}

// SetEndAt sets field value
func (o *CreateFlexRequestDto) SetEndAt(v time.Time) {
	o.EndAt = v
}

// GetDirection returns the Direction field value
func (o *CreateFlexRequestDto) GetDirection() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Direction
}

// GetDirectionOk returns a tuple with the Direction field value
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetDirectionOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Direction, true
}

// SetDirection sets field value
func (o *CreateFlexRequestDto) SetDirection(v string) {
	o.Direction = v
}

// GetLimit returns the Limit field value if set, zero value otherwise.
func (o *CreateFlexRequestDto) GetLimit() FlexRequestLimitDto {
	if o == nil || IsNil(o.Limit) {
		var ret FlexRequestLimitDto
		return ret
	}
	return *o.Limit
}

// GetLimitOk returns a tuple with the Limit field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetLimitOk() (*FlexRequestLimitDto, bool) {
	if o == nil || IsNil(o.Limit) {
		return nil, false
	}
	return o.Limit, true
}

// HasLimit returns a boolean if a field has been set.
func (o *CreateFlexRequestDto) HasLimit() bool {
	if o != nil && !IsNil(o.Limit) {
		return true
	}

	return false
}

// SetLimit gets a reference to the given FlexRequestLimitDto and assigns it to the Limit field.
func (o *CreateFlexRequestDto) SetLimit(v FlexRequestLimitDto) {
	o.Limit = &v
}

// GetProvider returns the Provider field value if set, zero value otherwise.
func (o *CreateFlexRequestDto) GetProvider() FlexRequestProviderDto {
	if o == nil || IsNil(o.Provider) {
		var ret FlexRequestProviderDto
		return ret
	}
	return *o.Provider
}

// GetProviderOk returns a tuple with the Provider field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetProviderOk() (*FlexRequestProviderDto, bool) {
	if o == nil || IsNil(o.Provider) {
		return nil, false
	}
	return o.Provider, true
}

// HasProvider returns a boolean if a field has been set.
func (o *CreateFlexRequestDto) HasProvider() bool {
	if o != nil && !IsNil(o.Provider) {
		return true
	}

	return false
}

// SetProvider gets a reference to the given FlexRequestProviderDto and assigns it to the Provider field.
func (o *CreateFlexRequestDto) SetProvider(v FlexRequestProviderDto) {
	o.Provider = &v
}

// GetTriggerMessageId returns the TriggerMessageId field value if set, zero value otherwise.
func (o *CreateFlexRequestDto) GetTriggerMessageId() string {
	if o == nil || IsNil(o.TriggerMessageId) {
		var ret string
		return ret
	}
	return *o.TriggerMessageId
}

// GetTriggerMessageIdOk returns a tuple with the TriggerMessageId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateFlexRequestDto) GetTriggerMessageIdOk() (*string, bool) {
	if o == nil || IsNil(o.TriggerMessageId) {
		return nil, false
	}
	return o.TriggerMessageId, true
}

// HasTriggerMessageId returns a boolean if a field has been set.
func (o *CreateFlexRequestDto) HasTriggerMessageId() bool {
	if o != nil && !IsNil(o.TriggerMessageId) {
		return true
	}

	return false
}

// SetTriggerMessageId gets a reference to the given string and assigns it to the TriggerMessageId field.
func (o *CreateFlexRequestDto) SetTriggerMessageId(v string) {
	o.TriggerMessageId = &v
}

func (o CreateFlexRequestDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CreateFlexRequestDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["requestedAt"] = o.RequestedAt
	toSerialize["startAt"] = o.StartAt
	toSerialize["endAt"] = o.EndAt
	toSerialize["direction"] = o.Direction
	if !IsNil(o.Limit) {
		toSerialize["limit"] = o.Limit
	}
	if !IsNil(o.Provider) {
		toSerialize["provider"] = o.Provider
	}
	if !IsNil(o.TriggerMessageId) {
		toSerialize["triggerMessageId"] = o.TriggerMessageId
	}
	return toSerialize, nil
}

func (o *CreateFlexRequestDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"requestedAt",
		"startAt",
		"endAt",
		"direction",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCreateFlexRequestDto := _CreateFlexRequestDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCreateFlexRequestDto)

	if err != nil {
		return err
	}

	*o = CreateFlexRequestDto(varCreateFlexRequestDto)

	return err
}

type NullableCreateFlexRequestDto struct {
	value *CreateFlexRequestDto
	isSet bool
}

func (v NullableCreateFlexRequestDto) Get() *CreateFlexRequestDto {
	return v.value
}

func (v *NullableCreateFlexRequestDto) Set(val *CreateFlexRequestDto) {
	v.value = val
	v.isSet = true
}

func (v NullableCreateFlexRequestDto) IsSet() bool {
	return v.isSet
}

func (v *NullableCreateFlexRequestDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCreateFlexRequestDto(val *CreateFlexRequestDto) *NullableCreateFlexRequestDto {
	return &NullableCreateFlexRequestDto{value: val, isSet: true}
}

func (v NullableCreateFlexRequestDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCreateFlexRequestDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
