/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the RewardInfoDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &RewardInfoDto{}

// RewardInfoDto struct for RewardInfoDto
type RewardInfoDto struct {
	// The energy delivered in the session that is rewardable. If 0 is returned, the session is not eligible for rewards
	RewardableEnergyKwh float32 `json:"rewardableEnergyKwh"`
	// The vehicle charged during the session
	VehicleId NullableString `json:"vehicleId"`
}

type _RewardInfoDto RewardInfoDto

// NewRewardInfoDto instantiates a new RewardInfoDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRewardInfoDto(rewardableEnergyKwh float32, vehicleId NullableString) *RewardInfoDto {
	this := RewardInfoDto{}
	this.RewardableEnergyKwh = rewardableEnergyKwh
	this.VehicleId = vehicleId
	return &this
}

// NewRewardInfoDtoWithDefaults instantiates a new RewardInfoDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRewardInfoDtoWithDefaults() *RewardInfoDto {
	this := RewardInfoDto{}
	return &this
}

// GetRewardableEnergyKwh returns the RewardableEnergyKwh field value
func (o *RewardInfoDto) GetRewardableEnergyKwh() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.RewardableEnergyKwh
}

// GetRewardableEnergyKwhOk returns a tuple with the RewardableEnergyKwh field value
// and a boolean to check if the value has been set.
func (o *RewardInfoDto) GetRewardableEnergyKwhOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.RewardableEnergyKwh, true
}

// SetRewardableEnergyKwh sets field value
func (o *RewardInfoDto) SetRewardableEnergyKwh(v float32) {
	o.RewardableEnergyKwh = v
}

// GetVehicleId returns the VehicleId field value
// If the value is explicit nil, the zero value for string will be returned
func (o *RewardInfoDto) GetVehicleId() string {
	if o == nil || o.VehicleId.Get() == nil {
		var ret string
		return ret
	}

	return *o.VehicleId.Get()
}

// GetVehicleIdOk returns a tuple with the VehicleId field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *RewardInfoDto) GetVehicleIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.VehicleId.Get(), o.VehicleId.IsSet()
}

// SetVehicleId sets field value
func (o *RewardInfoDto) SetVehicleId(v string) {
	o.VehicleId.Set(&v)
}

func (o RewardInfoDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o RewardInfoDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["rewardableEnergyKwh"] = o.RewardableEnergyKwh
	toSerialize["vehicleId"] = o.VehicleId.Get()
	return toSerialize, nil
}

func (o *RewardInfoDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"rewardableEnergyKwh",
		"vehicleId",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varRewardInfoDto := _RewardInfoDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varRewardInfoDto)

	if err != nil {
		return err
	}

	*o = RewardInfoDto(varRewardInfoDto)

	return err
}

type NullableRewardInfoDto struct {
	value *RewardInfoDto
	isSet bool
}

func (v NullableRewardInfoDto) Get() *RewardInfoDto {
	return v.value
}

func (v *NullableRewardInfoDto) Set(val *RewardInfoDto) {
	v.value = val
	v.isSet = true
}

func (v NullableRewardInfoDto) IsSet() bool {
	return v.isSet
}

func (v *NullableRewardInfoDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRewardInfoDto(val *RewardInfoDto) *NullableRewardInfoDto {
	return &NullableRewardInfoDto{value: val, isSet: true}
}

func (v NullableRewardInfoDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRewardInfoDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
