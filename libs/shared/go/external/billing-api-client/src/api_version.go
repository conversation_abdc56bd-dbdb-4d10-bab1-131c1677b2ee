/*
Billing API

Billing API service

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package billingapiclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
)


type VersionAPI interface {

	/*
	VersionControllerGetVersion get application version

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiVersionControllerGetVersionRequest
	*/
	VersionControllerGetVersion(ctx context.Context) ApiVersionControllerGetVersionRequest

	// VersionControllerGetVersionExecute executes the request
	VersionControllerGetVersionExecute(r ApiVersionControllerGetVersionRequest) (*http.Response, error)
}

// VersionAPIService VersionAPI service
type VersionAPIService service

type ApiVersionControllerGetVersionRequest struct {
	ctx        context.Context
	ApiService VersionAPI
}

func (r ApiVersionControllerGetVersionRequest) Execute() (*http.Response, error) {
	return r.ApiService.VersionControllerGetVersionExecute(r)
}

/*
VersionControllerGetVersion get application version

 @param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 @return ApiVersionControllerGetVersionRequest
*/
func (a *VersionAPIService) VersionControllerGetVersion(ctx context.Context) ApiVersionControllerGetVersionRequest {
	return ApiVersionControllerGetVersionRequest{
		ApiService: a,
		ctx: ctx,
	}
}

// Execute executes the request
func (a *VersionAPIService) VersionControllerGetVersionExecute(r ApiVersionControllerGetVersionRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod   = http.MethodGet
		localVarPostBody     interface{}
		formFiles            []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "VersionAPIService.VersionControllerGetVersion")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/version"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}
