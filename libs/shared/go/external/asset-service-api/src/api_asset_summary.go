/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type AssetSummaryAPI interface {

	/*
		GetChargingStation Return asset details for a given charging station

		Lookup an asset summary based on the ppid (serial number) of the charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid ppid from an existing unit
		@return AssetSummaryAPIGetChargingStationRequest
	*/
	GetChargingStation(ctx context.Context, ppid string) AssetSummaryAPIGetChargingStationRequest

	// GetChargingStationExecute executes the request
	//  @return ChargingStationSummary
	GetChargingStationExecute(r AssetSummaryAPIGetChargingStationRequest) (*ChargingStationSummary, *http.Response, error)

	/*
		SearchChargingStations Search for the charging station

		Search for a charging station by EVSE mac address or serial number, results are keyed by the values of query parameter

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return AssetSummaryAPISearchChargingStationsRequest
	*/
	SearchChargingStations(ctx context.Context) AssetSummaryAPISearchChargingStationsRequest

	// SearchChargingStationsExecute executes the request
	//  @return ChargingStationsSummary
	SearchChargingStationsExecute(r AssetSummaryAPISearchChargingStationsRequest) (*ChargingStationsSummary, *http.Response, error)
}

// AssetSummaryAPIService AssetSummaryAPI service
type AssetSummaryAPIService service

type AssetSummaryAPIGetChargingStationRequest struct {
	ctx        context.Context
	ApiService AssetSummaryAPI
	ppid       string
}

func (r AssetSummaryAPIGetChargingStationRequest) Execute() (*ChargingStationSummary, *http.Response, error) {
	return r.ApiService.GetChargingStationExecute(r)
}

/*
GetChargingStation Return asset details for a given charging station

Lookup an asset summary based on the ppid (serial number) of the charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid ppid from an existing unit
	@return AssetSummaryAPIGetChargingStationRequest
*/
func (a *AssetSummaryAPIService) GetChargingStation(ctx context.Context, ppid string) AssetSummaryAPIGetChargingStationRequest {
	return AssetSummaryAPIGetChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return ChargingStationSummary
func (a *AssetSummaryAPIService) GetChargingStationExecute(r AssetSummaryAPIGetChargingStationRequest) (*ChargingStationSummary, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ChargingStationSummary
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "AssetSummaryAPIService.GetChargingStation")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type AssetSummaryAPISearchChargingStationsRequest struct {
	ctx          context.Context
	ApiService   AssetSummaryAPI
	macAddress   *[]string
	serialNumber *[]string
}

// mac address(es) from an existing EVSE(s) (aka PCB)
func (r AssetSummaryAPISearchChargingStationsRequest) MacAddress(macAddress []string) AssetSummaryAPISearchChargingStationsRequest {
	r.macAddress = &macAddress
	return r
}

// serial number(s) from an existing EVSE(s) (aka PCB)
func (r AssetSummaryAPISearchChargingStationsRequest) SerialNumber(serialNumber []string) AssetSummaryAPISearchChargingStationsRequest {
	r.serialNumber = &serialNumber
	return r
}

func (r AssetSummaryAPISearchChargingStationsRequest) Execute() (*ChargingStationsSummary, *http.Response, error) {
	return r.ApiService.SearchChargingStationsExecute(r)
}

/*
SearchChargingStations Search for the charging station

Search for a charging station by EVSE mac address or serial number, results are keyed by the values of query parameter

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return AssetSummaryAPISearchChargingStationsRequest
*/
func (a *AssetSummaryAPIService) SearchChargingStations(ctx context.Context) AssetSummaryAPISearchChargingStationsRequest {
	return AssetSummaryAPISearchChargingStationsRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return ChargingStationsSummary
func (a *AssetSummaryAPIService) SearchChargingStationsExecute(r AssetSummaryAPISearchChargingStationsRequest) (*ChargingStationsSummary, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ChargingStationsSummary
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "AssetSummaryAPIService.SearchChargingStations")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if r.macAddress != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "macAddress", r.macAddress, "form", "csv")
	}
	if r.serialNumber != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "serialNumber", r.serialNumber, "form", "csv")
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
