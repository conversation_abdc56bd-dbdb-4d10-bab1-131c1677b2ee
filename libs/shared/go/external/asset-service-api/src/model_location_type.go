/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
	"fmt"
)

// LocationType The type of the location: domestic, commercial. A replacement for the isHome flag, to allow a more nuanced distinction in future
type LocationType string

// List of LocationType
const (
	LOCATIONTYPE_DOMESTIC   LocationType = "DOMESTIC"
	LOCATIONTYPE_COMMERCIAL LocationType = "COMMERCIAL"
)

// All allowed values of LocationType enum
var AllowedLocationTypeEnumValues = []LocationType{
	"DOMESTIC",
	"COMMERCIAL",
}

func (v *LocationType) UnmarshalJSON(src []byte) error {
	var value string
	err := json.Unmarshal(src, &value)
	if err != nil {
		return err
	}
	enumTypeValue := LocationType(value)
	for _, existing := range AllowedLocationTypeEnumValues {
		if existing == enumTypeValue {
			*v = enumTypeValue
			return nil
		}
	}

	return fmt.Errorf("%+v is not a valid LocationType", value)
}

// NewLocationTypeFromValue returns a pointer to a valid LocationType
// for the value passed as argument, or an error if the value passed is not allowed by the enum
func NewLocationTypeFromValue(v string) (*LocationType, error) {
	ev := LocationType(v)
	if ev.IsValid() {
		return &ev, nil
	} else {
		return nil, fmt.Errorf("invalid value '%v' for LocationType: valid values are %v", v, AllowedLocationTypeEnumValues)
	}
}

// IsValid return true if the value is valid for the enum, false otherwise
func (v LocationType) IsValid() bool {
	for _, existing := range AllowedLocationTypeEnumValues {
		if existing == v {
			return true
		}
	}
	return false
}

// Ptr returns reference to LocationType value
func (v LocationType) Ptr() *LocationType {
	return &v
}

type NullableLocationType struct {
	value *LocationType
	isSet bool
}

func (v NullableLocationType) Get() *LocationType {
	return v.value
}

func (v *NullableLocationType) Set(val *LocationType) {
	v.value = val
	v.isSet = true
}

func (v NullableLocationType) IsSet() bool {
	return v.isSet
}

func (v *NullableLocationType) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableLocationType(val *LocationType) *NullableLocationType {
	return &NullableLocationType{value: val, isSet: true}
}

func (v NullableLocationType) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableLocationType) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
