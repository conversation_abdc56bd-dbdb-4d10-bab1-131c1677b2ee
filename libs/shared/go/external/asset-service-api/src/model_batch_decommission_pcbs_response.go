/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
)

// checks if the BatchDecommissionPcbsResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &BatchDecommissionPcbsResponse{}

// BatchDecommissionPcbsResponse struct for BatchDecommissionPcbsResponse
type BatchDecommissionPcbsResponse struct {
	Decommissioned []string                `json:"decommissioned,omitempty"`
	Failed         []SerialNumberWithError `json:"failed,omitempty"`
}

// NewBatchDecommissionPcbsResponse instantiates a new BatchDecommissionPcbsResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewBatchDecommissionPcbsResponse() *BatchDecommissionPcbsResponse {
	this := BatchDecommissionPcbsResponse{}
	return &this
}

// NewBatchDecommissionPcbsResponseWithDefaults instantiates a new BatchDecommissionPcbsResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewBatchDecommissionPcbsResponseWithDefaults() *BatchDecommissionPcbsResponse {
	this := BatchDecommissionPcbsResponse{}
	return &this
}

// GetDecommissioned returns the Decommissioned field value if set, zero value otherwise.
func (o *BatchDecommissionPcbsResponse) GetDecommissioned() []string {
	if o == nil || IsNil(o.Decommissioned) {
		var ret []string
		return ret
	}
	return o.Decommissioned
}

// GetDecommissionedOk returns a tuple with the Decommissioned field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *BatchDecommissionPcbsResponse) GetDecommissionedOk() ([]string, bool) {
	if o == nil || IsNil(o.Decommissioned) {
		return nil, false
	}
	return o.Decommissioned, true
}

// HasDecommissioned returns a boolean if a field has been set.
func (o *BatchDecommissionPcbsResponse) HasDecommissioned() bool {
	if o != nil && !IsNil(o.Decommissioned) {
		return true
	}

	return false
}

// SetDecommissioned gets a reference to the given []string and assigns it to the Decommissioned field.
func (o *BatchDecommissionPcbsResponse) SetDecommissioned(v []string) {
	o.Decommissioned = v
}

// GetFailed returns the Failed field value if set, zero value otherwise.
func (o *BatchDecommissionPcbsResponse) GetFailed() []SerialNumberWithError {
	if o == nil || IsNil(o.Failed) {
		var ret []SerialNumberWithError
		return ret
	}
	return o.Failed
}

// GetFailedOk returns a tuple with the Failed field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *BatchDecommissionPcbsResponse) GetFailedOk() ([]SerialNumberWithError, bool) {
	if o == nil || IsNil(o.Failed) {
		return nil, false
	}
	return o.Failed, true
}

// HasFailed returns a boolean if a field has been set.
func (o *BatchDecommissionPcbsResponse) HasFailed() bool {
	if o != nil && !IsNil(o.Failed) {
		return true
	}

	return false
}

// SetFailed gets a reference to the given []SerialNumberWithError and assigns it to the Failed field.
func (o *BatchDecommissionPcbsResponse) SetFailed(v []SerialNumberWithError) {
	o.Failed = v
}

func (o BatchDecommissionPcbsResponse) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o BatchDecommissionPcbsResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Decommissioned) {
		toSerialize["decommissioned"] = o.Decommissioned
	}
	if !IsNil(o.Failed) {
		toSerialize["failed"] = o.Failed
	}
	return toSerialize, nil
}

type NullableBatchDecommissionPcbsResponse struct {
	value *BatchDecommissionPcbsResponse
	isSet bool
}

func (v NullableBatchDecommissionPcbsResponse) Get() *BatchDecommissionPcbsResponse {
	return v.value
}

func (v *NullableBatchDecommissionPcbsResponse) Set(val *BatchDecommissionPcbsResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableBatchDecommissionPcbsResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableBatchDecommissionPcbsResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableBatchDecommissionPcbsResponse(val *BatchDecommissionPcbsResponse) *NullableBatchDecommissionPcbsResponse {
	return &NullableBatchDecommissionPcbsResponse{value: val, isSet: true}
}

func (v NullableBatchDecommissionPcbsResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableBatchDecommissionPcbsResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
