/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"encoding/json"
	"fmt"
)

// PcbArchitecture The hardware architecture of the PCB (only applicable to PodPoint units)
type PcbArchitecture string

// List of PcbArchitecture
const (
	PCBARCHITECTURE__1_0 PcbArchitecture = "1.0"
	PCBARCHITECTURE__2_0 PcbArchitecture = "2.0"
	PCBARCHITECTURE__2_4 PcbArchitecture = "2.4"
	PCBARCHITECTURE__3_0 PcbArchitecture = "3.0"
	PCBARCHITECTURE__5_0 PcbArchitecture = "5.0"
)

// All allowed values of PcbArchitecture enum
var AllowedPcbArchitectureEnumValues = []PcbArchitecture{
	"1.0",
	"2.0",
	"2.4",
	"3.0",
	"5.0",
}

func (v *PcbArchitecture) UnmarshalJSON(src []byte) error {
	var value string
	err := json.Unmarshal(src, &value)
	if err != nil {
		return err
	}
	enumTypeValue := PcbArchitecture(value)
	for _, existing := range AllowedPcbArchitectureEnumValues {
		if existing == enumTypeValue {
			*v = enumTypeValue
			return nil
		}
	}

	return fmt.Errorf("%+v is not a valid PcbArchitecture", value)
}

// NewPcbArchitectureFromValue returns a pointer to a valid PcbArchitecture
// for the value passed as argument, or an error if the value passed is not allowed by the enum
func NewPcbArchitectureFromValue(v string) (*PcbArchitecture, error) {
	ev := PcbArchitecture(v)
	if ev.IsValid() {
		return &ev, nil
	} else {
		return nil, fmt.Errorf("invalid value '%v' for PcbArchitecture: valid values are %v", v, AllowedPcbArchitectureEnumValues)
	}
}

// IsValid return true if the value is valid for the enum, false otherwise
func (v PcbArchitecture) IsValid() bool {
	for _, existing := range AllowedPcbArchitectureEnumValues {
		if existing == v {
			return true
		}
	}
	return false
}

// Ptr returns reference to PcbArchitecture value
func (v PcbArchitecture) Ptr() *PcbArchitecture {
	return &v
}

type NullablePcbArchitecture struct {
	value *PcbArchitecture
	isSet bool
}

func (v NullablePcbArchitecture) Get() *PcbArchitecture {
	return v.value
}

func (v *NullablePcbArchitecture) Set(val *PcbArchitecture) {
	v.value = val
	v.isSet = true
}

func (v NullablePcbArchitecture) IsSet() bool {
	return v.isSet
}

func (v *NullablePcbArchitecture) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullablePcbArchitecture(val *PcbArchitecture) *NullablePcbArchitecture {
	return &NullablePcbArchitecture{value: val, isSet: true}
}

func (v NullablePcbArchitecture) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullablePcbArchitecture) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
