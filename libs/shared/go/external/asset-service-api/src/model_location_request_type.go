/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the LocationRequestType type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &LocationRequestType{}

// LocationRequestType struct for LocationRequestType
type LocationRequestType struct {
	AddressId    float32                     `json:"addressId"`
	Latitude     float32                     `json:"latitude"`
	Longitude    float32                     `json:"longitude"`
	LocationType string                      `json:"locationType"`
	Options      *LocationRequestTypeOptions `json:"options,omitempty"`
}

type _LocationRequestType LocationRequestType

// NewLocationRequestType instantiates a new LocationRequestType object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewLocationRequestType(addressId float32, latitude float32, longitude float32, locationType string) *LocationRequestType {
	this := LocationRequestType{}
	this.AddressId = addressId
	this.Latitude = latitude
	this.Longitude = longitude
	this.LocationType = locationType
	return &this
}

// NewLocationRequestTypeWithDefaults instantiates a new LocationRequestType object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewLocationRequestTypeWithDefaults() *LocationRequestType {
	this := LocationRequestType{}
	return &this
}

// GetAddressId returns the AddressId field value
func (o *LocationRequestType) GetAddressId() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.AddressId
}

// GetAddressIdOk returns a tuple with the AddressId field value
// and a boolean to check if the value has been set.
func (o *LocationRequestType) GetAddressIdOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.AddressId, true
}

// SetAddressId sets field value
func (o *LocationRequestType) SetAddressId(v float32) {
	o.AddressId = v
}

// GetLatitude returns the Latitude field value
func (o *LocationRequestType) GetLatitude() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.Latitude
}

// GetLatitudeOk returns a tuple with the Latitude field value
// and a boolean to check if the value has been set.
func (o *LocationRequestType) GetLatitudeOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Latitude, true
}

// SetLatitude sets field value
func (o *LocationRequestType) SetLatitude(v float32) {
	o.Latitude = v
}

// GetLongitude returns the Longitude field value
func (o *LocationRequestType) GetLongitude() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.Longitude
}

// GetLongitudeOk returns a tuple with the Longitude field value
// and a boolean to check if the value has been set.
func (o *LocationRequestType) GetLongitudeOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Longitude, true
}

// SetLongitude sets field value
func (o *LocationRequestType) SetLongitude(v float32) {
	o.Longitude = v
}

// GetLocationType returns the LocationType field value
func (o *LocationRequestType) GetLocationType() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.LocationType
}

// GetLocationTypeOk returns a tuple with the LocationType field value
// and a boolean to check if the value has been set.
func (o *LocationRequestType) GetLocationTypeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.LocationType, true
}

// SetLocationType sets field value
func (o *LocationRequestType) SetLocationType(v string) {
	o.LocationType = v
}

// GetOptions returns the Options field value if set, zero value otherwise.
func (o *LocationRequestType) GetOptions() LocationRequestTypeOptions {
	if o == nil || IsNil(o.Options) {
		var ret LocationRequestTypeOptions
		return ret
	}
	return *o.Options
}

// GetOptionsOk returns a tuple with the Options field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *LocationRequestType) GetOptionsOk() (*LocationRequestTypeOptions, bool) {
	if o == nil || IsNil(o.Options) {
		return nil, false
	}
	return o.Options, true
}

// HasOptions returns a boolean if a field has been set.
func (o *LocationRequestType) HasOptions() bool {
	if o != nil && !IsNil(o.Options) {
		return true
	}

	return false
}

// SetOptions gets a reference to the given LocationRequestTypeOptions and assigns it to the Options field.
func (o *LocationRequestType) SetOptions(v LocationRequestTypeOptions) {
	o.Options = &v
}

func (o LocationRequestType) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o LocationRequestType) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["addressId"] = o.AddressId
	toSerialize["latitude"] = o.Latitude
	toSerialize["longitude"] = o.Longitude
	toSerialize["locationType"] = o.LocationType
	if !IsNil(o.Options) {
		toSerialize["options"] = o.Options
	}
	return toSerialize, nil
}

func (o *LocationRequestType) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"addressId",
		"latitude",
		"longitude",
		"locationType",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varLocationRequestType := _LocationRequestType{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varLocationRequestType)

	if err != nil {
		return err
	}

	*o = LocationRequestType(varLocationRequestType)

	return err
}

type NullableLocationRequestType struct {
	value *LocationRequestType
	isSet bool
}

func (v NullableLocationRequestType) Get() *LocationRequestType {
	return v.value
}

func (v *NullableLocationRequestType) Set(val *LocationRequestType) {
	v.value = val
	v.isSet = true
}

func (v NullableLocationRequestType) IsSet() bool {
	return v.isSet
}

func (v *NullableLocationRequestType) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableLocationRequestType(val *LocationRequestType) *NullableLocationRequestType {
	return &NullableLocationRequestType{value: val, isSet: true}
}

func (v NullableLocationRequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableLocationRequestType) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
