/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"encoding/json"
	"fmt"
)

// checks if the HealthControllerCheck200ResponseInfoValue type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &HealthControllerCheck200ResponseInfoValue{}

// HealthControllerCheck200ResponseInfoValue struct for HealthControllerCheck200ResponseInfoValue
type HealthControllerCheck200ResponseInfoValue struct {
	Status               string `json:"status"`
	AdditionalProperties map[string]interface{}
}

type _HealthControllerCheck200ResponseInfoValue HealthControllerCheck200ResponseInfoValue

// NewHealthControllerCheck200ResponseInfoValue instantiates a new HealthControllerCheck200ResponseInfoValue object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHealthControllerCheck200ResponseInfoValue(status string) *HealthControllerCheck200ResponseInfoValue {
	this := HealthControllerCheck200ResponseInfoValue{}
	this.Status = status
	return &this
}

// NewHealthControllerCheck200ResponseInfoValueWithDefaults instantiates a new HealthControllerCheck200ResponseInfoValue object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHealthControllerCheck200ResponseInfoValueWithDefaults() *HealthControllerCheck200ResponseInfoValue {
	this := HealthControllerCheck200ResponseInfoValue{}
	return &this
}

// GetStatus returns the Status field value
func (o *HealthControllerCheck200ResponseInfoValue) GetStatus() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Status
}

// GetStatusOk returns a tuple with the Status field value
// and a boolean to check if the value has been set.
func (o *HealthControllerCheck200ResponseInfoValue) GetStatusOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Status, true
}

// SetStatus sets field value
func (o *HealthControllerCheck200ResponseInfoValue) SetStatus(v string) {
	o.Status = v
}

func (o HealthControllerCheck200ResponseInfoValue) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HealthControllerCheck200ResponseInfoValue) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["status"] = o.Status

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *HealthControllerCheck200ResponseInfoValue) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"status",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varHealthControllerCheck200ResponseInfoValue := _HealthControllerCheck200ResponseInfoValue{}

	err = json.Unmarshal(data, &varHealthControllerCheck200ResponseInfoValue)

	if err != nil {
		return err
	}

	*o = HealthControllerCheck200ResponseInfoValue(varHealthControllerCheck200ResponseInfoValue)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "status")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableHealthControllerCheck200ResponseInfoValue struct {
	value *HealthControllerCheck200ResponseInfoValue
	isSet bool
}

func (v NullableHealthControllerCheck200ResponseInfoValue) Get() *HealthControllerCheck200ResponseInfoValue {
	return v.value
}

func (v *NullableHealthControllerCheck200ResponseInfoValue) Set(val *HealthControllerCheck200ResponseInfoValue) {
	v.value = val
	v.isSet = true
}

func (v NullableHealthControllerCheck200ResponseInfoValue) IsSet() bool {
	return v.isSet
}

func (v *NullableHealthControllerCheck200ResponseInfoValue) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHealthControllerCheck200ResponseInfoValue(val *HealthControllerCheck200ResponseInfoValue) *NullableHealthControllerCheck200ResponseInfoValue {
	return &NullableHealthControllerCheck200ResponseInfoValue{value: val, isSet: true}
}

func (v NullableHealthControllerCheck200ResponseInfoValue) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHealthControllerCheck200ResponseInfoValue) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
