/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
)

type SuppliersAPI interface {

	/*
		GetSuppliers Get suppliers

		Get a list of suppliers

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ApiGetSuppliersRequest
	*/
	GetSuppliers(ctx context.Context) ApiGetSuppliersRequest

	// GetSuppliersExecute executes the request
	//  @return []SupplierDto
	GetSuppliersExecute(r ApiGetSuppliersRequest) ([]SupplierDto, *http.Response, error)
}

// SuppliersAPIService SuppliersAPI service
type SuppliersAPIService service

type ApiGetSuppliersRequest struct {
	ctx        context.Context
	ApiService SuppliersAPI
}

func (r ApiGetSuppliersRequest) Execute() ([]SupplierDto, *http.Response, error) {
	return r.ApiService.GetSuppliersExecute(r)
}

/*
GetSuppliers Get suppliers

Get a list of suppliers

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ApiGetSuppliersRequest
*/
func (a *SuppliersAPIService) GetSuppliers(ctx context.Context) ApiGetSuppliersRequest {
	return ApiGetSuppliersRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return []SupplierDto
func (a *SuppliersAPIService) GetSuppliersExecute(r ApiGetSuppliersRequest) ([]SupplierDto, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue []SupplierDto
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "SuppliersAPIService.GetSuppliers")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/suppliers"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}
