/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the PersistedTariffRowDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &PersistedTariffRowDto{}

// PersistedTariffRowDto struct for PersistedTariffRowDto
type PersistedTariffRowDto struct {
	// The charging station PPID
	Ppid string `json:"ppid"`
	// Reference to the supplier. Null if the supplier is unknown.
	SupplierId NullableString `json:"supplierId"`
	// Array of tariff information applicable to specific days and times.
	TariffInfo []TariffInfoDto `json:"tariffInfo"`
	// Timezone the tariff information applies to.
	Timezone string `json:"timezone"`
	// The date from which the tariff is effective in the format YYYY-MM-DD
	EffectiveFrom string `json:"effectiveFrom"`
	// Unique ID for the tariff
	Id string `json:"id"`
	// The cheapest unit price (e.g., £0.10).
	CheapestUnitPrice float32 `json:"cheapestUnitPrice"`
}

type _PersistedTariffRowDto PersistedTariffRowDto

// NewPersistedTariffRowDto instantiates a new PersistedTariffRowDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewPersistedTariffRowDto(ppid string, supplierId NullableString, tariffInfo []TariffInfoDto, timezone string, effectiveFrom string, id string, cheapestUnitPrice float32) *PersistedTariffRowDto {
	this := PersistedTariffRowDto{}
	this.Ppid = ppid
	this.SupplierId = supplierId
	this.TariffInfo = tariffInfo
	this.Timezone = timezone
	this.EffectiveFrom = effectiveFrom
	this.Id = id
	this.CheapestUnitPrice = cheapestUnitPrice
	return &this
}

// NewPersistedTariffRowDtoWithDefaults instantiates a new PersistedTariffRowDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewPersistedTariffRowDtoWithDefaults() *PersistedTariffRowDto {
	this := PersistedTariffRowDto{}
	return &this
}

// GetPpid returns the Ppid field value
func (o *PersistedTariffRowDto) GetPpid() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Ppid
}

// GetPpidOk returns a tuple with the Ppid field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetPpidOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Ppid, true
}

// SetPpid sets field value
func (o *PersistedTariffRowDto) SetPpid(v string) {
	o.Ppid = v
}

// GetSupplierId returns the SupplierId field value
// If the value is explicit nil, the zero value for string will be returned
func (o *PersistedTariffRowDto) GetSupplierId() string {
	if o == nil || o.SupplierId.Get() == nil {
		var ret string
		return ret
	}

	return *o.SupplierId.Get()
}

// GetSupplierIdOk returns a tuple with the SupplierId field value
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *PersistedTariffRowDto) GetSupplierIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.SupplierId.Get(), o.SupplierId.IsSet()
}

// SetSupplierId sets field value
func (o *PersistedTariffRowDto) SetSupplierId(v string) {
	o.SupplierId.Set(&v)
}

// GetTariffInfo returns the TariffInfo field value
func (o *PersistedTariffRowDto) GetTariffInfo() []TariffInfoDto {
	if o == nil {
		var ret []TariffInfoDto
		return ret
	}

	return o.TariffInfo
}

// GetTariffInfoOk returns a tuple with the TariffInfo field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetTariffInfoOk() ([]TariffInfoDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.TariffInfo, true
}

// SetTariffInfo sets field value
func (o *PersistedTariffRowDto) SetTariffInfo(v []TariffInfoDto) {
	o.TariffInfo = v
}

// GetTimezone returns the Timezone field value
func (o *PersistedTariffRowDto) GetTimezone() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Timezone
}

// GetTimezoneOk returns a tuple with the Timezone field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetTimezoneOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Timezone, true
}

// SetTimezone sets field value
func (o *PersistedTariffRowDto) SetTimezone(v string) {
	o.Timezone = v
}

// GetEffectiveFrom returns the EffectiveFrom field value
func (o *PersistedTariffRowDto) GetEffectiveFrom() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.EffectiveFrom
}

// GetEffectiveFromOk returns a tuple with the EffectiveFrom field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetEffectiveFromOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.EffectiveFrom, true
}

// SetEffectiveFrom sets field value
func (o *PersistedTariffRowDto) SetEffectiveFrom(v string) {
	o.EffectiveFrom = v
}

// GetId returns the Id field value
func (o *PersistedTariffRowDto) GetId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Id
}

// GetIdOk returns a tuple with the Id field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Id, true
}

// SetId sets field value
func (o *PersistedTariffRowDto) SetId(v string) {
	o.Id = v
}

// GetCheapestUnitPrice returns the CheapestUnitPrice field value
func (o *PersistedTariffRowDto) GetCheapestUnitPrice() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.CheapestUnitPrice
}

// GetCheapestUnitPriceOk returns a tuple with the CheapestUnitPrice field value
// and a boolean to check if the value has been set.
func (o *PersistedTariffRowDto) GetCheapestUnitPriceOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CheapestUnitPrice, true
}

// SetCheapestUnitPrice sets field value
func (o *PersistedTariffRowDto) SetCheapestUnitPrice(v float32) {
	o.CheapestUnitPrice = v
}

func (o PersistedTariffRowDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o PersistedTariffRowDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["ppid"] = o.Ppid
	toSerialize["supplierId"] = o.SupplierId.Get()
	toSerialize["tariffInfo"] = o.TariffInfo
	toSerialize["timezone"] = o.Timezone
	toSerialize["effectiveFrom"] = o.EffectiveFrom
	toSerialize["id"] = o.Id
	toSerialize["cheapestUnitPrice"] = o.CheapestUnitPrice
	return toSerialize, nil
}

func (o *PersistedTariffRowDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"ppid",
		"supplierId",
		"tariffInfo",
		"timezone",
		"effectiveFrom",
		"id",
		"cheapestUnitPrice",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varPersistedTariffRowDto := _PersistedTariffRowDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varPersistedTariffRowDto)

	if err != nil {
		return err
	}

	*o = PersistedTariffRowDto(varPersistedTariffRowDto)

	return err
}

type NullablePersistedTariffRowDto struct {
	value *PersistedTariffRowDto
	isSet bool
}

func (v NullablePersistedTariffRowDto) Get() *PersistedTariffRowDto {
	return v.value
}

func (v *NullablePersistedTariffRowDto) Set(val *PersistedTariffRowDto) {
	v.value = val
	v.isSet = true
}

func (v NullablePersistedTariffRowDto) IsSet() bool {
	return v.isSet
}

func (v *NullablePersistedTariffRowDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullablePersistedTariffRowDto(val *PersistedTariffRowDto) *NullablePersistedTariffRowDto {
	return &NullablePersistedTariffRowDto{value: val, isSet: true}
}

func (v NullablePersistedTariffRowDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullablePersistedTariffRowDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
