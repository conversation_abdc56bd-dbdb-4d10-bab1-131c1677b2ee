/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ChargingStationTariffSearchMetadataDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingStationTariffSearchMetadataDto{}

// ChargingStationTariffSearchMetadataDto struct for ChargingStationTariffSearchMetadataDto
type ChargingStationTariffSearchMetadataDto struct {
	// The search criteria used
	Criteria ChargingStationTariffSearchCriteriaDto `json:"criteria"`
}

type _ChargingStationTariffSearchMetadataDto ChargingStationTariffSearchMetadataDto

// NewChargingStationTariffSearchMetadataDto instantiates a new ChargingStationTariffSearchMetadataDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingStationTariffSearchMetadataDto(criteria ChargingStationTariffSearchCriteriaDto) *ChargingStationTariffSearchMetadataDto {
	this := ChargingStationTariffSearchMetadataDto{}
	this.Criteria = criteria
	return &this
}

// NewChargingStationTariffSearchMetadataDtoWithDefaults instantiates a new ChargingStationTariffSearchMetadataDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingStationTariffSearchMetadataDtoWithDefaults() *ChargingStationTariffSearchMetadataDto {
	this := ChargingStationTariffSearchMetadataDto{}
	return &this
}

// GetCriteria returns the Criteria field value
func (o *ChargingStationTariffSearchMetadataDto) GetCriteria() ChargingStationTariffSearchCriteriaDto {
	if o == nil {
		var ret ChargingStationTariffSearchCriteriaDto
		return ret
	}

	return o.Criteria
}

// GetCriteriaOk returns a tuple with the Criteria field value
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchMetadataDto) GetCriteriaOk() (*ChargingStationTariffSearchCriteriaDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Criteria, true
}

// SetCriteria sets field value
func (o *ChargingStationTariffSearchMetadataDto) SetCriteria(v ChargingStationTariffSearchCriteriaDto) {
	o.Criteria = v
}

func (o ChargingStationTariffSearchMetadataDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingStationTariffSearchMetadataDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["criteria"] = o.Criteria
	return toSerialize, nil
}

func (o *ChargingStationTariffSearchMetadataDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"criteria",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingStationTariffSearchMetadataDto := _ChargingStationTariffSearchMetadataDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargingStationTariffSearchMetadataDto)

	if err != nil {
		return err
	}

	*o = ChargingStationTariffSearchMetadataDto(varChargingStationTariffSearchMetadataDto)

	return err
}

type NullableChargingStationTariffSearchMetadataDto struct {
	value *ChargingStationTariffSearchMetadataDto
	isSet bool
}

func (v NullableChargingStationTariffSearchMetadataDto) Get() *ChargingStationTariffSearchMetadataDto {
	return v.value
}

func (v *NullableChargingStationTariffSearchMetadataDto) Set(val *ChargingStationTariffSearchMetadataDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingStationTariffSearchMetadataDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingStationTariffSearchMetadataDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingStationTariffSearchMetadataDto(val *ChargingStationTariffSearchMetadataDto) *NullableChargingStationTariffSearchMetadataDto {
	return &NullableChargingStationTariffSearchMetadataDto{value: val, isSet: true}
}

func (v NullableChargingStationTariffSearchMetadataDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingStationTariffSearchMetadataDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
