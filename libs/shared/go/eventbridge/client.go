package eventbridge

import (
	"context"
	"encoding/json"
	"experience/libs/shared/go/aws"
	"fmt"
	"time"

	awsSDK "github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/eventbridge"
	"github.com/aws/aws-sdk-go-v2/service/eventbridge/types"
	smithyendpoints "github.com/aws/smithy-go/endpoints"
)

type ClientOption func(c *Client)

type Client struct {
	eventbridgeClient *eventbridge.Client
	eventBusARN       string
	source            string
}

// WithEventBusARN sets the event bus name for the client; pe
func WithEventBusARN(eventBusARN string) ClientOption {
	return func(c *Client) {
		c.eventBusARN = eventBusARN
	}
}

func WithSource(source string) ClientOption {
	return func(c *Client) {
		c.source = source
	}
}

type eventBridgeEndpointResolver struct {
}

func (e eventBridgeEndpointResolver) ResolveEndpoint(ctx context.Context, params eventbridge.EndpointParameters) (smithyendpoints.Endpoint, error) {
	customEndpoint, err := aws.GetCustomEndpoint()
	if err != nil {
		return smithyendpoints.Endpoint{}, fmt.Errorf("getting custom endpoint: %w", err)
	}

	if customEndpoint == nil {
		// delegate back to the default v2 resolver otherwise
		return eventbridge.NewDefaultEndpointResolverV2().ResolveEndpoint(ctx, params)
	}

	return *customEndpoint, nil
}

func NewClient(ctx context.Context, opts ...ClientOption) (*Client, error) {
	cfg, err := aws.NewCfg(ctx)

	if err != nil {
		return nil, err
	}

	eventBridge := eventbridge.NewFromConfig(cfg, eventbridge.WithEndpointResolverV2(eventBridgeEndpointResolver{}))
	c := &Client{
		eventbridgeClient: eventBridge,
	}
	for _, opt := range opts {
		opt(c)
	}
	return c, nil
}

func (c *Client) PutEvent(ctx context.Context, eventType string, message json.RawMessage) error {
	_, err := c.eventbridgeClient.PutEvents(ctx, &eventbridge.PutEventsInput{
		Entries: []types.PutEventsRequestEntry{
			{
				Detail:       awsSDK.String(string(message)),
				DetailType:   awsSDK.String(eventType),
				EventBusName: awsSDK.String(c.eventBusARN),
				Source:       awsSDK.String(c.source),
				Time:         awsSDK.Time(time.Now().Truncate(time.Millisecond)),
			},
		},
	})
	return err
}
