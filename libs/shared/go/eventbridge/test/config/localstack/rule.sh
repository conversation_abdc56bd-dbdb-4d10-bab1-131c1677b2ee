#!/bin/sh

echo "Create AWS SQS queues for localstack"
awslocal --endpoint-url=http://localhost:4566 sqs create-queue --queue-name test-queue --region eu-west-1

echo "Create AWS EventBridge event bus for localstack"
awslocal --endpoint-url=http://localhost:4566 events create-event-bus --name my-event-bus --region eu-west-1

echo "Create EventBridge rule for localstack"
awslocal --endpoint-url=http://localhost:4566 events put-rule --name send-events-to-test-queue --event-bus-name my-event-bus --event-pattern '{"source": ["test-file"]}' --region eu-west-1

echo "Event rule created: test-events"
awslocal --endpoint-url=http://localhost:4566 events put-targets --event-bus-name my-event-bus --rule send-events-to-test-queue --targets "Id"="1","Arn"="arn:aws:sqs:eu-west-1:000000000000:test-queue" --region eu-west-1

echo "Event rule target created for: test-events"
