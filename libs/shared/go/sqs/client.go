package sqs

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"

	sdk "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

type ClientOption func(c *Client)

func NewClient(sqsClient awsClient, queueURL string, opts ...ClientOption) *Client {
	client := &Client{
		client:              sqsClient,
		queueURL:            queueURL,
		MaxNumberOfMessages: 10,
		WaitTimeSeconds:     20,
		VisibilityTimeout:   5,
	}

	for _, opt := range opts {
		opt(client)
	}

	return client
}

func WithVisibilityTimeout(visibilityTimeout int32) ClientOption {
	return func(c *Client) {
		c.VisibilityTimeout = visibilityTimeout
	}
}

type Client struct {
	client              awsClient
	queueURL            string
	MaxNumberOfMessages int32
	NumberOfWorkers     int32
	VisibilityTimeout   int32
	WaitTimeSeconds     int32
}

func (c Client) DeleteMessage(ctx context.Context, handle ReceiptHandle) error {
	if c.queueURL == "" {
		return fmt.Errorf("cannot call delete message without defining a queue url")
	}
	input := sdk.DeleteMessageInput{
		QueueUrl:      &c.queueURL,
		ReceiptHandle: handle,
	}
	_, err := c.client.DeleteMessage(ctx, &input)
	return err
}

func (c Client) PurgeQueue(ctx context.Context) error {
	if c.queueURL == "" {
		return fmt.Errorf("cannot call purge queue without defining a queue url")
	}
	input := sdk.PurgeQueueInput{
		QueueUrl: &c.queueURL,
	}
	_, err := c.client.PurgeQueue(ctx, &input)
	return err
}

func (c Client) ReceiveMessage(ctx context.Context) (Messages, error) {
	if c.queueURL == "" {
		return nil, fmt.Errorf("cannot call receive message without defining a queue url")
	}
	input := sdk.ReceiveMessageInput{
		AttributeNames:              []types.QueueAttributeName{"AWSTraceHeader"},
		QueueUrl:                    &c.queueURL,
		MaxNumberOfMessages:         c.MaxNumberOfMessages,
		VisibilityTimeout:           c.VisibilityTimeout,
		WaitTimeSeconds:             c.WaitTimeSeconds,
		MessageSystemAttributeNames: []types.MessageSystemAttributeName{types.MessageSystemAttributeNameApproximateReceiveCount},
	}
	output, err := c.client.ReceiveMessage(ctx, &input)
	if err != nil {
		return nil, err
	}
	return output.Messages, nil
}

func (c Client) SendMessage(ctx context.Context, msgBody any) (MessageID, error) {
	if c.queueURL == "" {
		return nil, fmt.Errorf("cannot call send message without defining a queue url")
	}
	str, ok := msgBody.(string)
	if !ok {
		bytes, err := json.Marshal(msgBody)
		if err != nil {
			return nil, err
		}
		str = string(bytes)
	}

	input := sdk.SendMessageInput{
		QueueUrl:    &c.queueURL,
		MessageBody: &str,
	}
	output, err := c.client.SendMessage(ctx, &input)
	if err != nil {
		return nil, err
	}
	return output.MessageId, nil
}

func (c Client) SendMessageBatch(ctx context.Context, batch []string) error {
	if c.queueURL == "" {
		return fmt.Errorf("cannot call send message batch without defining a queue url")
	}
	entries := make([]types.SendMessageBatchRequestEntry, len(batch))
	for entryNumber := 0; entryNumber < len(batch); entryNumber++ {
		entry := types.SendMessageBatchRequestEntry{
			Id:          ptr.String(uuid.New().String()),
			MessageBody: ptr.String(batch[entryNumber]),
		}
		entries[entryNumber] = entry
	}
	input := sdk.SendMessageBatchInput{
		Entries:  entries,
		QueueUrl: &c.queueURL,
	}
	output, err := c.client.SendMessageBatch(ctx, &input)

	if err != nil || len(output.Failed) > 0 {
		return fmt.Errorf("failure to send message batch: %w", err)
	}

	return nil
}

func (c Client) GetQueueDepth(ctx context.Context) (int64, error) {
	var numberOfMessagesQueueAttributeName types.QueueAttributeName = "ApproximateNumberOfMessages"
	if c.queueURL == "" {
		return 0, fmt.Errorf("cannot call queue depth without defining a queue url")
	}
	input := sdk.GetQueueAttributesInput{
		QueueUrl:       &c.queueURL,
		AttributeNames: []types.QueueAttributeName{numberOfMessagesQueueAttributeName},
	}
	attributes, err := c.client.GetQueueAttributes(ctx, &input)
	if err != nil {
		return 0, err
	}
	depthAttribute := attributes.Attributes[string(numberOfMessagesQueueAttributeName)]
	depth, err := strconv.ParseInt(depthAttribute, 10, strconv.IntSize)
	if err != nil {
		return 0, err
	}
	return depth, nil
}
