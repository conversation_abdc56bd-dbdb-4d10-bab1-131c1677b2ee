package utils

import (
	"testing"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
)

func TestCompletedEventUUIDFromNumericID(t *testing.T) {
	tests := []struct {
		name      string
		numericID int
		want      uuid.UUID
	}{
		{
			name:      "numeric part is left-padded by zeroes to 12 characters",
			numericID: 1,
			want:      uuid.MustParse(completedEventImmutablePrefix + "000000000001"),
		},
		{
			name:      "numeric part is converted to hexadecimal before padding",
			numericID: 254,
			want:      uuid.MustParse(completedEventImmutablePrefix + "0000000000fe"),
		},
		{
			name:      "when numeric part is zero we get a fresh uuid",
			numericID: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := FabricateUUIDFromNumericID(tt.numericID)
			if tt.numericID != 0 {
				require.Equal(t, tt.want, got)
			}
		})
	}
}

func TestAuthorisedEventUUIDFromNumericID(t *testing.T) {
	tests := []struct {
		name      string
		numericID int
		want      uuid.UUID
	}{
		{
			name:      "numeric part is left-padded by zeroes to 12 characters",
			numericID: 1,
			want:      uuid.MustParse(authorisedEventImmutablePrefix + "000000000001"),
		},
		{
			name:      "numeric part is converted to hexadecimal before padding",
			numericID: 254,
			want:      uuid.MustParse(authorisedEventImmutablePrefix + "0000000000fe"),
		},
		{
			name:      "when numeric part is zero we get a fresh uuid",
			numericID: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := AuthorisedEventUUIDFromNumericID(tt.numericID)
			if tt.numericID != 0 {
				require.Equal(t, tt.want, got)
			}
		})
	}
}

func TestExtractNumericIDFromUUID(t *testing.T) {
	// range of mysql unsigned int
	numericID := gofakeit.IntRange(0, 4294967295)
	tests := []struct {
		name     string
		uid      uuid.UUID
		expected int
	}{
		{
			name:     "extracts numeric value (completed)",
			uid:      FabricateUUIDFromNumericID(numericID),
			expected: numericID,
		},
		{
			name:     "extracts numeric value (authorised)",
			uid:      AuthorisedEventUUIDFromNumericID(numericID),
			expected: numericID,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, err := ExtractNumericIDFromUUID(tt.uid)
			require.NoError(t, err)
			require.Equal(t, tt.expected, int(actual))
		})
	}
}
