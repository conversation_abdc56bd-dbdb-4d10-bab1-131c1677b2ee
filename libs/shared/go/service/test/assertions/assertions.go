package assertions

import (
	"fmt"
	"log"
	"os"
	"reflect"
	"testing"
	"time"

	"k8s.io/utils/strings/slices"
)

var errorMessages []string
var errLogger = log.New(os.Stderr, "", 0)

func AssertEqualsExclude(t *testing.T, expected, actual any, excludedFields []string) {
	t.<PERSON>er()
	errorMessages = []string{}

	walk(expected, actual, "", func(expVal, actVal any, fieldName string) {
		if slices.Contains(excludedFields, fieldName) {
			return
		}

		if expVal != actVal {
			addError(fmt.Sprintf("Assertion failed for %s. \nExpected: %v \nActual  : %v", fieldName, expVal, actVal))
		}
	})

	for _, errMsg := range errorMessages {
		errLogger.Println(errMsg)
	}

	if len(errorMessages) > 0 {
		t.Fail()
	}
}

func AssertEqualityForAllFieldsExceptExcluded(t *testing.T, expected, actual any, excludedFields []string) bool {
	t.Helper()
	errorMessages = []string{}

	walk(expected, actual, "", func(expVal, actVal any, fieldName string) {
		if slices.Contains(excludedFields, fieldName) {
			return
		}

		if expVal != actVal {
			addError(fmt.Sprintf("Assertion failed for %s. \nExpected: %v \nActual  : %v", fieldName, expVal, actVal))
		}
	})

	for _, errMsg := range errorMessages {
		errLogger.Println(errMsg)
	}

	return len(errorMessages) == 0
}

func walk(expected, actual any, fieldName string, fn func(expVal, actVal any, fieldName string)) {
	expVal := getValue(expected)
	actVal := getValue(actual)

	walkValue := func(expectedValue reflect.Value, actualValue reflect.Value, fieldName string) {
		walk(expectedValue.Interface(), actualValue.Interface(), fieldName, fn)
	}

	switch expVal.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		fn(expVal.Int(), actVal.Int(), fieldName)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		fn(expVal.Uint(), actVal.Uint(), fieldName)
	case reflect.Float32, reflect.Float64:
		fn(expVal.Float(), actVal.Float(), fieldName)
	case reflect.String:
		fn(expVal.String(), actVal.String(), fieldName)
	case reflect.Bool:
		fn(expVal.Bool(), actVal.Bool(), fieldName)
	case reflect.Struct:
		if expTime, ok := expVal.Interface().(time.Time); ok {
			actTime, ok2 := actVal.Interface().(time.Time)
			if !ok2 {
				panic(fmt.Sprintf("Cannot parse datetime for %s. \nExpected: %s \nActual  : %s", fieldName, expTime, actTime))
			}

			fn(expTime, actTime, fieldName)
			return
		}
		for i := 0; i < expVal.NumField(); i++ {
			expValFieldName := expVal.Type().Field(i).Name
			walkValue(expVal.Field(i), actVal.FieldByName(expValFieldName), expValFieldName)
		}
	case reflect.Slice, reflect.Array:
		if expVal.Len() != actVal.Len() {
			addError(fmt.Sprintf("Slice length does not match for key: %s. \nExpected: %d \nActual  : %d", fieldName, expVal.Len(), actVal.Len()))
			return
		}

		for i := 0; i < expVal.Len(); i++ {
			walkValue(expVal.Index(i), actVal.Index(i), "")
		}
	case reflect.Map:
		for _, key := range expVal.MapKeys() {
			walkValue(expVal.MapIndex(key), actVal.MapIndex(key), key.String())
		}
	case reflect.Chan, reflect.Complex64, reflect.Complex128, reflect.Func, reflect.Interface, reflect.Pointer:
	case reflect.UnsafePointer:
		addError(fmt.Sprintf("Unsafe pointer for field: %s", fieldName))
	case reflect.Invalid:
		if actVal.Kind() != reflect.Invalid {
			addError(fmt.Sprintf("Assertion failed for field: %s. Expected: nil. Actual: not nil", fieldName))
		}
	default:
		addError(fmt.Sprintf("Unknown type for field: %s", fieldName))
	}
}

func addError(msg string) {
	errorMessages = append(errorMessages, "============\n"+msg)
}

func getValue(x any) reflect.Value {
	val := reflect.ValueOf(x)

	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	return val
}
