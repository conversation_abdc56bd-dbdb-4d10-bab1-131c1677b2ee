package service_test

import (
	energycost "experience/libs/shared/go/service"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestDateRange_intersects(t *testing.T) {
	type fields struct {
		From time.Time
		To   time.Time
	}

	tests := []struct {
		name      string
		fields    fields
		dateRange *energycost.DateRange
		want      bool
		wantErr   assert.ErrorAssertionFunc
	}{
		{
			name: "date does not intersect when from and to is after to date",
			fields: fields{
				From: time.Now(),
				To:   time.Now().Add(time.Second * 1),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * 2)),
				To:   ptr.To(time.Now().Add(time.Second * 3)),
			},
			want:    false,
			wantErr: assert.NoError,
		},
		{
			name: "date does not intersect when from and to is before from date",
			fields: fields{
				From: time.Now(),
				To:   time.Now().Add(time.Second * 1),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * -3)),
				To:   ptr.To(time.Now().Add(time.Second * -2)),
			},
			want:    false,
			wantErr: assert.NoError,
		},
		{
			name: "date range overlaps on from",
			fields: fields{
				From: time.Now(),
				To:   time.Now().Add(time.Second * 2),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * 1)),
				To:   ptr.To(time.Now().Add(time.Second * 3)),
			},
			want:    true,
			wantErr: assert.NoError,
		},
		{
			name: "date range overlaps on to",
			fields: fields{
				From: time.Now().Add(time.Second * 2),
				To:   time.Now().Add(time.Second * 6),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * 1)),
				To:   ptr.To(time.Now().Add(time.Second * 5)),
			},
			want:    true,
			wantErr: assert.NoError,
		},
		{
			name: "date range encompasses both from and to",
			fields: fields{
				From: time.Now().Add(time.Second * 1),
				To:   time.Now().Add(time.Second * 10),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * 2)),
				To:   ptr.To(time.Now().Add(time.Second * 9)),
			},
			want:    true,
			wantErr: assert.NoError,
		},
		{
			name: "date range lies within both from and to",
			fields: fields{
				From: time.Now().Add(time.Second * 2),
				To:   time.Now().Add(time.Second * 9),
			},
			dateRange: &energycost.DateRange{
				From: ptr.To(time.Now().Add(time.Second * 1)),
				To:   ptr.To(time.Now().Add(time.Second * 10)),
			},
			want:    true,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := energycost.DateRange{
				From: ptr.To(tt.fields.From),
				To:   ptr.To(tt.fields.To),
			}
			got := d.Intersects(*tt.dateRange)
			assert.Equalf(t, tt.want, got, "intersects(%v)", tt.dateRange)
		})
	}
}

func TestDateRange_DurationSeconds(t *testing.T) {
	tests := []struct {
		name             string
		from             time.Time
		to               time.Time
		expectedDuration int64
	}{
		{
			name:             "Returns expected whole number of seconds",
			from:             time.Now().Add(-time.Second * 32),
			to:               time.Now(),
			expectedDuration: 32,
		},
		{
			name:             "Returns truncated duration that would've rounded down (32.2s)",
			from:             time.Now().Add(-time.Second * 32).Add(-time.Millisecond * 200),
			to:               time.Now(),
			expectedDuration: 32,
		},
		{
			name:             "Returns truncated duration that would've rounded up (33.8)",
			from:             time.Now().Add(-time.Second * 33).Add(-time.Millisecond * 800),
			to:               time.Now(),
			expectedDuration: 33,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			from := tt.from
			to := tt.to
			dRange, err := energycost.NewDateRange(&from, &to)
			require.NoError(t, err)
			require.Equal(t, tt.expectedDuration, dRange.DurationSeconds())
		})
	}
}

func TestNewDateRange(t *testing.T) {
	type args struct {
		from *time.Time
		to   *time.Time
	}
	now := ptr.To(time.Now())
	tests := []struct {
		name    string
		args    args
		want    *energycost.DateRange
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "error invalid new date range",
			args: args{
				from: &time.Time{},
				to:   &time.Time{},
			},
			wantErr: assert.Error,
		},
		{
			name: "error invalid to on date range",
			args: args{
				to: &time.Time{},
			},
			wantErr: assert.Error,
		},
		{
			name: "error invalid from on date range",
			args: args{
				from: &time.Time{},
			},
			wantErr: assert.Error,
		},
		{
			name: "creates date range",
			args: args{
				from: now,
				to:   ptr.To(now.Add(time.Second * 1)),
			},
			wantErr: assert.NoError,
			want: &energycost.DateRange{
				From: now,
				To:   ptr.To(now.Add(time.Second * 1)),
			},
		},
		{
			name: "do not validate dates when from and to are nil",
			args: args{
				from: nil,
				to:   nil,
			},
			wantErr: assert.NoError,
			want: &energycost.DateRange{
				From: nil,
				To:   nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			from := tt.args.from
			to := tt.args.to
			got, err := energycost.NewDateRange(from, to)
			if !tt.wantErr(t, err, fmt.Sprintf("NewDateRange(%v, %v)", tt.args.from, tt.args.to)) {
				return
			}
			assert.Equalf(t, tt.want, got, "NewDateRange(%v, %v)", tt.args.from, tt.args.to)
		})
	}
}

// Terminology taken from https://ics.uci.edu/~alspaugh/cls/shr/allen.html
func TestDateRange_IntersectionTime(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name    string
		d1      *energycost.DateRange
		want    float64
		d2      *energycost.DateRange
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "date does not intersect when from and to is after to date",
			d1: &energycost.DateRange{
				From: ptr.To(now),
				To:   ptr.To(now.Add(time.Second * 1)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 3)),
			},
			want:    0,
			wantErr: assert.NoError,
		},
		{
			name: "date does not intersect when from and to is before from date",
			d1: &energycost.DateRange{
				From: ptr.To(now),
				To:   ptr.To(now.Add(time.Second * 1)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * -3)),
				To:   ptr.To(now.Add(time.Second * -2)),
			},
			want:    0,
			wantErr: assert.NoError,
		},
		{
			name: "date range overlaps",
			d1: &energycost.DateRange{
				From: ptr.To(now),
				To:   ptr.To(now.Add(time.Second * 2)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 3)),
			},
			want:    1,
			wantErr: assert.NoError,
		},
		{
			name: "date range overlapped by",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 6)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 5)),
			},
			want:    3,
			wantErr: assert.NoError,
		},
		{
			name: "date range contains",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 9)),
			},
			want:    7,
			wantErr: assert.NoError,
		},
		{
			name: "date range during",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 9)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    7,
			wantErr: assert.NoError,
		},
		{
			name: "date range finished by",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    8,
			wantErr: assert.NoError,
		},
		{
			name: "date range finishes",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 2)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    8,
			wantErr: assert.NoError,
		},
		{
			name: "date range starts",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 8)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    7,
			wantErr: assert.NoError,
		},
		{
			name: "date range started by",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 8)),
			},
			want:    7,
			wantErr: assert.NoError,
		},
		{
			name: "date range equals",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 1)),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    9,
			wantErr: assert.NoError,
		},
		{
			name: "date range meets",
			d1: &energycost.DateRange{
				From: ptr.To(now),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 10)),
				To:   ptr.To(now.Add(time.Second * 20)),
			},
			want:    0,
			wantErr: assert.NoError,
		},
		{
			name: "date range met by",
			d1: &energycost.DateRange{
				From: ptr.To(now.Add(time.Second * 10)),
				To:   ptr.To(now.Add(time.Second * 20)),
			},
			d2: &energycost.DateRange{
				From: ptr.To(now),
				To:   ptr.To(now.Add(time.Second * 10)),
			},
			want:    0,
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.d1.IntersectionTime(*tt.d2), "IntersectionTime(%v)", tt.d2)
		})
	}
}
