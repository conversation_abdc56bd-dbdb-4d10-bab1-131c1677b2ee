package carbonsavings

import (
	"testing"
)

func Test_GetCO2Savings(t *testing.T) {
	tests := []struct {
		name        string
		energyUsage float64
		want        float64
	}{
		{
			name:        "Calculation runs as expected",
			energyUsage: 1000.0,
			want:        560.0,
		},
		{
			name:        "Check edge case of 0 energy used",
			energyUsage: 0.0,
			want:        0.0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := Co2Savings(tt.energyUsage)
			assertIfFailed(t, tt.want, actual)
		})
	}
}

func assertIfFailed(tb testing.TB, expected, actual float64) {
	tb.Helper()
	if actual != expected {
		tb.Errorf("Expected %.2fkg, but received %.2fkg", expected, actual)
	}
}
