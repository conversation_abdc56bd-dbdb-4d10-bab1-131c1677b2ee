import { Injectable, Logger } from '@nestjs/common';
import Strip<PERSON> from 'stripe';

export interface StripePriceResponse extends StripePrice {
  id: string;
}

interface StripePrice {
  lookup_key: string | null;
}

@Injectable()
export class StripePricesService {
  private readonly logger = new Logger(StripePricesService.name);

  constructor(private readonly stripe: Stripe) {}

  async getPriceByLookupKey(
    key: string
  ): Promise<StripePriceResponse | undefined> {
    this.logger.log({ key }, 'getting stripe price by lookup key');

    const prices = await this.stripe.prices.list({
      lookup_keys: [key],
    });

    return prices.data.find((price: StripePrice) => price.lookup_key === key);
  }
}
