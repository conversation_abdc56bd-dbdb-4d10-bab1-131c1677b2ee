import { StripeSubscriptionService } from './subscription.service';
import { TEST_CREATE_SUBSCRIPTION_REQUEST } from '../types/__fixtures__/create-subscription-request';
import { Test, TestingModule } from '@nestjs/testing';
import Stripe from 'stripe';

const mockCreateSubscription = jest.fn();
const mockRetrieveSubscription = jest.fn();
const mockUpdateSubscription = jest.fn();

jest.mock('stripe', () =>
  jest.fn().mockImplementation(() => ({
    subscriptions: {
      // @ts-expect-error mock
      create: (request) => mockCreateSubscription(request),
      // @ts-expect-error mock
      retrieve: (request) => mockRetrieveSubscription(request),
      // @ts-expect-error mock
      update: (id, request) => mockUpdateSubscription(id, request),
    },
  }))
);

describe('StripeSubscriptionService', () => {
  let service: StripeSubscriptionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StripeSubscriptionService,
        {
          provide: Stripe,
          useValue: new Stripe('test', {}),
        },
      ],
    }).compile();

    service = module.get<StripeSubscriptionService>(StripeSubscriptionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a subscription', async () => {
    const subscriptionId = 'sub_1MowQVLkdIwHu7ixeRlqHVzs';
    mockCreateSubscription.mockResolvedValueOnce({
      id: subscriptionId,
      status: 'active',
    });

    const subscription = await service.create(TEST_CREATE_SUBSCRIPTION_REQUEST);

    expect(subscription).toEqual({
      id: subscriptionId,
      status: 'active',
    });
    expect(mockCreateSubscription).toHaveBeenCalledWith({
      collection_method: 'send_invoice',
      customer: TEST_CREATE_SUBSCRIPTION_REQUEST.customerId,
      days_until_due: 30,
      items: [
        {
          price: TEST_CREATE_SUBSCRIPTION_REQUEST.prices[0].id,
          quantity: TEST_CREATE_SUBSCRIPTION_REQUEST.prices[0].quantity,
          tax_rates: TEST_CREATE_SUBSCRIPTION_REQUEST.prices[0].taxRates,
        },
      ],
      payment_settings: {
        payment_method_types: ['bacs_debit', 'card', 'customer_balance'],
      },
    });
  });

  it('should retrieve a subscription', async () => {
    const subscriptionId = 'sub_1MowQVLkdIwHu7ixeRlqHVzs';
    mockRetrieveSubscription.mockResolvedValueOnce({
      id: subscriptionId,
      items: {
        data: [
          {
            id: 'si_1MowQVLkdIwHu7ixX8Z3CvZ8',
            price: {
              id: 'price_1MowQULkdIwHu7ixraBm864M',
              lookup_key: null,
            },
            quantity: 5,
          },
        ],
      },
    });

    const subscription = await service.retrieve(subscriptionId);

    expect(mockRetrieveSubscription).toHaveBeenCalledWith(subscriptionId);
    expect(subscription).toEqual({
      id: subscriptionId,
      items: [
        {
          id: 'si_1MowQVLkdIwHu7ixX8Z3CvZ8',
          price: {
            id: 'price_1MowQULkdIwHu7ixraBm864M',
            lookup_key: null,
          },
          quantity: 5,
        },
      ],
    });
  });

  it('should update a subscription', async () => {
    const subscriptionId = 'sub_1MowQVLkdIwHu7ixeRlqHVzs';
    mockUpdateSubscription.mockResolvedValue({
      id: subscriptionId,
    });

    const subscription = await service.update(subscriptionId, {
      items: [
        {
          id: 'price_1MowQULkdIwHu7ixraBm864M',
          quantity: 20,
        },
      ],
    });

    expect(mockUpdateSubscription).toHaveBeenCalledWith(subscriptionId, {
      items: [
        {
          id: 'price_1MowQULkdIwHu7ixraBm864M',
          quantity: 20,
        },
      ],
      proration_behavior: 'none',
    });

    expect(subscription).toEqual({
      id: subscriptionId,
    });
  });
});
