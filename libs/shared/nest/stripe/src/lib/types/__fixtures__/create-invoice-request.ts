import { CreateInvoiceRequest } from '../create-invoice-request';

export const TEST_CUSTOMER_ID = 'cus_NggdGfEfND3Bfs';

export const TEST_CREATE_INVOICE_REQUEST: CreateInvoiceRequest = {
  amount: 100,
  collectionMethod: 'send_invoice',
  customerId: TEST_CUSTOMER_ID,
  customFields: [
    { name: 'Account reference', value: '123456' },
    { name: 'Site name', value: 'Test Site' },
    { name: 'Site address', value: '123 Test Street' },
  ],
  daysUntilDue: 30,
  description: 'Admin fees',
  endPeriod: '2021-01-31',
  footer: 'Thank you for your business',
  startPeriod: '2021-01-01',
  taxRates: ['txr_1Iv1J2J2eZvKYlo2'],
  taxBehaviour: 'inclusive',
};
