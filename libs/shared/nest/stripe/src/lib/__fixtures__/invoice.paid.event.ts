import Stripe from 'stripe';

export const TEST_INVOICE_PAID_EVENT: Stripe.InvoicePaidEvent = {
  id: 'evt_1R96paFfRqcfiHYMjhvfvxhz',
  object: 'event',
  api_version: '2023-10-16',
  created: **********,
  data: {
    object: {
      id: 'in_1R96o6FfRqcfiHYMTotCjJE7',
      object: 'invoice',
      account_country: 'GB',
      account_name: 'Pod Point Limited',
      account_tax_ids: ['txi_1PBehjFfRqcfiHYMiglbb7Sc'],
      amount_due: 5168,
      amount_paid: 0,
      amount_overpaid: 0,
      amount_remaining: 5168,
      amount_shipping: 0,
      application: null,
      attempt_count: 0,
      attempted: false,
      auto_advance: false,
      automatic_tax: {
        disabled_reason: null,
        enabled: false,
        liability: null,
        provider: null,
        status: null,
      },
      automatically_finalizes_at: null,
      billing_reason: 'manual',
      collection_method: 'send_invoice',
      created: **********,
      currency: 'gbp',
      custom_fields: [
        {
          name: 'Account reference',
          value: 'ref123',
        },
        {
          name: 'Site name',
          value: 'Gallagher Shopping',
        },
        {
          name: 'Site address',
          value: 'Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB',
        },
      ],
      customer: 'cus_Rt4CVtiFNOpdqE',
      customer_address: null,
      customer_email: '<EMAIL>',
      customer_name: 'Gallagher Shopping',
      customer_phone: null,
      customer_shipping: null,
      customer_tax_exempt: 'none',
      customer_tax_ids: [],
      default_payment_method: null,
      default_source: null,
      default_tax_rates: [],
      description: null,
      discounts: [],
      due_date: **********,
      effective_at: **********,
      ending_balance: 0,
      footer:
        'Where required, your charger will comply with The Electric Vehicles (Smart Charge Points) Regulations 2021. Our statement of Compliance can be found at https://pod-point.com/technical/hardware.',
      from_invoice: {
        action: 'revision',
        invoice: 'in_1R96k8FfRqcfiHYMRlMN59V2',
      },
      hosted_invoice_url:
        'https://invoice.stripe.com/i/acct_1OuYEyFfRqcfiHYM/test_YWNjdF8xT3VZRXlGZlJxY2ZpSFlNLF9TM0RFVXdHV1lRQlN5RVNJanNiTUpubkN2ajBiR3V0LDEzNDA2MjQ2Ng0200mhi5B4Hm?s=ap',
      invoice_pdf:
        'https://pay.stripe.com/invoice/acct_1OuYEyFfRqcfiHYM/test_YWNjdF8xT3VZRXlGZlJxY2ZpSFlNLF9TM0RFVXdHV1lRQlN5RVNJanNiTUpubkN2ajBiR3V0LDEzNDA2MjQ2Ng0200mhi5B4Hm/pdf?s=ap',
      issuer: {
        type: 'self',
      },
      last_finalization_error: null,
      latest_revision: null,
      lines: {
        object: 'list',
        data: [
          {
            id: 'il_1R96o6FfRqcfiHYMz98ifo73',
            object: 'line_item',
            amount: 5168,
            currency: 'gbp',
            description: 'Admin fees | GSGS042024',
            discount_amounts: [],
            discountable: true,
            discounts: [],
            invoice: 'in_1R96o6FfRqcfiHYMTotCjJE7',
            livemode: false,
            metadata: {},
            parent: {
              type: 'invoice_item_details',
              invoice_item_details: {
                invoice_item: 'ii_1R96o6FfRqcfiHYMTotCjJE7',
                proration: false,
                proration_details: {
                  credited_items: {
                    invoice: 'in_1R96o6FfRqcfiHYMTotCjJE7',
                    invoice_line_items: [],
                  },
                },
                subscription: null,
              },
              subscription_item_details: null,
            },
            period: {
              end: 1714435200,
              start: 1711929600,
            },
            pricing: {
              type: 'price_details',
              unit_amount_decimal: '5168',
            },
            pretax_credit_amounts: [],
            quantity: 1,
            subscription: null,
            taxes: [
              {
                amount: 861,
                taxability_reason: 'standard_rated',
                taxable_amount: 4307,
                tax_behavior: 'inclusive',
                tax_rate_details: {
                  tax_rate: 'txr_1PFuGoFfRqcfiHYMKzD4YpAb',
                },
                type: 'tax_rate_details',
              },
            ],
          },
        ],
        has_more: false,
        url: '/v1/invoices/in_1R96o6FfRqcfiHYMTotCjJE7/lines',
      },
      livemode: false,
      metadata: {},
      next_payment_attempt: null,
      number: 'ADF-0224',
      on_behalf_of: null,
      parent: null,
      payment_settings: {
        default_mandate: null,
        payment_method_options: null,
        payment_method_types: ['bacs_debit', 'card', 'customer_balance'],
      },
      period_end: 1741706622,
      period_start: 1741706622,
      post_payment_credit_notes_amount: 0,
      pre_payment_credit_notes_amount: 0,
      receipt_number: null,
      rendering: {
        amount_tax_display: null,
        pdf: null,
        template: null,
        template_version: null,
      },
      shipping_cost: null,
      shipping_details: null,
      starting_balance: 0,
      statement_descriptor: null,
      status: 'paid',
      status_transitions: {
        finalized_at: **********,
        marked_uncollectible_at: null,
        paid_at: **********,
        voided_at: null,
      },
      subtotal: 5168,
      subtotal_excluding_tax: 4307,
      test_clock: null,
      total: 5168,
      total_taxes: [],
      total_discount_amounts: [],
      total_excluding_tax: 4307,
      total_pretax_credit_amounts: [],
      webhooks_delivered_at: 1743521575,
    },
  },
  livemode: false,
  pending_webhooks: 2,
  request: {
    id: 'req_ZDMpBmFYHzfE20',
    idempotency_key: '619bef22-89d7-4ce5-9bd4-de08e70787ef',
  },
  type: 'invoice.paid',
};
