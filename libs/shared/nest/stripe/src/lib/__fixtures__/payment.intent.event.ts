import Stripe from 'stripe';

// Create a mock PaymentIntent object
const mockPaymentIntent: Stripe.PaymentIntent = {
  payment_method_configuration_details: null,
  id: 'pi_3MtwBwLkdIwHu7ix28a3tqPa',
  object: 'payment_intent',
  amount: 2000,
  amount_capturable: 0,
  amount_details: {
    tip: {},
  },
  amount_received: 0,
  application: null,
  application_fee_amount: null,
  automatic_payment_methods: {
    enabled: true,
  },
  canceled_at: null,
  cancellation_reason: null,
  capture_method: 'automatic',
  client_secret: 'pi_3MtwBwLkdIwHu7ix28a3tqPa_secret_YrKJUKribcBjcG8HVhfZluoGH',
  confirmation_method: 'automatic',
  created: 1680800504,
  currency: 'usd',
  customer: 'cus_hfJJeHsFdaV2H3s',
  description: 'payment went through',
  last_payment_error: null,
  latest_charge: null,
  livemode: false,
  metadata: {
    origin: 'billing-api',
  },
  next_action: null,
  on_behalf_of: null,
  payment_method: null,
  payment_method_options: {
    card: {
      installments: null,
      mandate_options: null,
      network: null,
      request_three_d_secure: 'automatic',
    },
    link: {
      persistent_token: null,
    },
  },
  payment_method_types: ['card', 'link'],
  processing: null,
  receipt_email: null,
  review: null,
  setup_future_usage: null,
  shipping: null,
  source: null,
  statement_descriptor: null,
  statement_descriptor_suffix: null,
  status: 'requires_payment_method',
  transfer_data: null,
  transfer_group: null,
};

// Create a mock event for payment_intent.succeeded
const mockEvent: Stripe.Event = {
  id: 'evt_1IEAaZG16qv9bVQKQ0Sk0qHg',
  object: 'event',
  api_version: '2020-08-27',
  created: 1617184800,
  data: {
    object: mockPaymentIntent,
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_123456789',
    idempotency_key: null,
  },
  type: 'payment_intent.succeeded',
};

export const getPaymentIntentEvent = () => mockEvent;

export default mockEvent;
