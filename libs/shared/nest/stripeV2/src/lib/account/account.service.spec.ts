import { CreateStripeAccount, UpdateStripeAccount } from './account.types';
import { StripeV2AccountService } from './account.service';
import { Test } from '@nestjs/testing';
import { createMock } from '@golevelup/ts-jest';
import Stripe from '@stripe/stripe';

type StripeAccountResponse = Stripe.Response<Stripe.V2.Account>;

describe('StripeV2AccountService', () => {
  let service: StripeV2AccountService;
  let stripe: Stripe;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        StripeV2AccountService,
        {
          provide: Stripe,
          useValue: createMock<Stripe>(),
        },
      ],
    }).compile();

    module.useLogger(false);

    service = module.get(StripeV2AccountService);
    stripe = module.get(Stripe);
  });

  afterEach(() => jest.restoreAllMocks());

  describe('getUer()', () => {
    it('gets the user from stripe', async () => {
      const mockRetrieveAccount = jest
        .spyOn(stripe.v2.accounts, 'retrieve')
        .mockResolvedValue({
          id: 'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        } as StripeAccountResponse);

      const res = await service.getUser(
        'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e'
      );

      expect(res).toStrictEqual({
        id: 'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
      });

      expect(mockRetrieveAccount).toHaveBeenCalledTimes(1);
      expect(mockRetrieveAccount).toHaveBeenCalledWith(
        'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e'
      );
    });
  });

  describe('updateUser()', () => {
    const MOCK_USER: UpdateStripeAccount = {
      firstName: 'Mobile',
      lastName: 'Tester',
      address: {
        line1: "222 Gray's Inn Road",
        city: 'London',
        county: 'London',
        country: 'gb',
        postcode: 'WC1X 8HB',
      },
    };

    it('updates the stripe user', async () => {
      const mockUpdateAccount = jest
        .spyOn(stripe.v2.accounts, 'update')
        .mockResolvedValue({
          id: 'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        } as StripeAccountResponse);

      const res = await service.updateUser(
        'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        MOCK_USER
      );

      expect(res).toStrictEqual(await mockUpdateAccount.mock.results[0].value);

      expect(mockUpdateAccount).toHaveBeenCalledTimes(1);
      expect(mockUpdateAccount).toHaveBeenCalledWith(
        'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        {
          name: `${MOCK_USER.firstName} ${MOCK_USER.lastName}`,
          legal_entity_data: {
            country: 'gb',
            representative: {
              given_name: MOCK_USER.firstName,
              surname: MOCK_USER.lastName,
              address: {
                line1: MOCK_USER.address.line1,
                line2: MOCK_USER.address.line2,
                city: MOCK_USER.address.city,
                state: MOCK_USER.address.county,
                country: MOCK_USER.address.country,
                postal_code: MOCK_USER.address.postcode,
              },
            },
          },
        }
      );
    });

    it('bubbles up any errors thrown', async () => {
      const error = new Error();

      const mockUpdateAccount = jest
        .spyOn(stripe.v2.accounts, 'update')
        .mockImplementation(() => {
          throw error;
        });

      await expect(
        service.updateUser(
          'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
          MOCK_USER
        )
      ).rejects.toThrow(error);

      expect(mockUpdateAccount).toHaveBeenCalledTimes(1);
      expect(mockUpdateAccount).toHaveBeenCalledWith(
        'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        {
          name: `${MOCK_USER.firstName} ${MOCK_USER.lastName}`,
          legal_entity_data: {
            country: 'gb',
            representative: {
              given_name: MOCK_USER.firstName,
              surname: MOCK_USER.lastName,
              address: {
                line1: MOCK_USER.address.line1,
                line2: MOCK_USER.address.line2,
                city: MOCK_USER.address.city,
                state: MOCK_USER.address.county,
                country: MOCK_USER.address.country,
                postal_code: MOCK_USER.address.postcode,
              },
            },
          },
        }
      );
    });
  });

  describe('createUser()', () => {
    const MOCK_USER: CreateStripeAccount = {
      firstName: 'Mobile',
      lastName: 'Tester',
      email: '<EMAIL>',
      authId: '21186d64-9189-47dd-a6eb-525f69c2c5d4',
      address: {
        line1: "222 Gray's Inn Road",
        city: 'London',
        county: 'London',
        country: 'gb',
        postcode: 'WC1X 8HB',
      },
    };

    it('creates the stripe user', async () => {
      const mockCreateAccount = jest
        .spyOn(stripe.v2.accounts, 'create')
        .mockResolvedValue({
          id: 'acct_6171no1gD4cM7rr9usS2wx00uyr2i0LzmoACAZy0e',
        } as StripeAccountResponse);

      const res = await service.createUser(MOCK_USER);

      expect(res).toStrictEqual(await mockCreateAccount.mock.results[0].value);

      expect(mockCreateAccount).toHaveBeenCalledTimes(1);
      expect(mockCreateAccount).toHaveBeenCalledWith({
        name: `${MOCK_USER.firstName} ${MOCK_USER.lastName}`,
        email: MOCK_USER.email,
        metadata: { authId: MOCK_USER.authId },
        include: ['legal_entity_data', 'configuration.recipient_data'],
        configuration: {
          recipient_data: {
            features: {
              bank_accounts: {
                local: {
                  requested: true,
                },
              },
            },
          },
        },
        legal_entity_data: {
          business_type: 'individual',
          country: 'gb',
          representative: {
            given_name: MOCK_USER.firstName,
            surname: MOCK_USER.lastName,
            address: {
              line1: MOCK_USER.address.line1,
              line2: MOCK_USER.address.line2,
              city: MOCK_USER.address.city,
              state: MOCK_USER.address.county,
              country: MOCK_USER.address.country,
              postal_code: MOCK_USER.address.postcode,
            },
          },
        },
      });
    });

    it('bubbles up any errors thrown', async () => {
      const error = new Error();

      const mockCreateAccount = jest
        .spyOn(stripe.v2.accounts, 'create')
        .mockImplementation(() => {
          throw error;
        });

      await expect(service.createUser(MOCK_USER)).rejects.toThrow(error);

      expect(mockCreateAccount).toHaveBeenCalledTimes(1);
      expect(mockCreateAccount).toHaveBeenCalledWith({
        name: `${MOCK_USER.firstName} ${MOCK_USER.lastName}`,
        email: MOCK_USER.email,
        metadata: { authId: MOCK_USER.authId },
        include: ['legal_entity_data', 'configuration.recipient_data'],
        configuration: {
          recipient_data: {
            features: {
              bank_accounts: {
                local: {
                  requested: true,
                },
              },
            },
          },
        },
        legal_entity_data: {
          business_type: 'individual',
          country: 'gb',
          representative: {
            given_name: MOCK_USER.firstName,
            surname: MOCK_USER.lastName,
            address: {
              line1: MOCK_USER.address.line1,
              line2: MOCK_USER.address.line2,
              city: MOCK_USER.address.city,
              state: MOCK_USER.address.county,
              country: MOCK_USER.address.country,
              postal_code: MOCK_USER.address.postcode,
            },
          },
        },
      });
    });
  });
});
