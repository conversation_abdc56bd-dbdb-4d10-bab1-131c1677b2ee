import type Stripe from '@stripe/stripe';

export type Country = Stripe.V2.Account.LegalEntityData.Country;

export interface StripeAccountAddress {
  line1: string;
  line2?: string;
  city: string;
  county: string;
  country: Country;
  postcode: string;
}

export interface CreateStripeAccount {
  authId: string;
  firstName: string;
  lastName: string;
  email: string;
  address: StripeAccountAddress;
}

export interface UpdateStripeAccount {
  firstName: string;
  lastName: string;
  address: StripeAccountAddress;
}
