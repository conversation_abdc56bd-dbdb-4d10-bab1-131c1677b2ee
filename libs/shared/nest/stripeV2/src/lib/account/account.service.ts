import { CreateStripeAccount, UpdateStripeAccount } from './account.types';
import { Injectable, Logger } from '@nestjs/common';
import Stripe from '@stripe/stripe';

@Injectable()
export class StripeV2AccountService {
  private readonly logger = new Logger(StripeV2AccountService.name);

  constructor(private readonly stripe: Stripe) {}

  async createUser(user: CreateStripeAccount) {
    this.logger.log({ user }, 'creating stripe user');

    try {
      return await this.stripe.v2.accounts.create({
        include: ['legal_entity_data', 'configuration.recipient_data'],
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        metadata: { authId: user.authId },
        legal_entity_data: {
          business_type: 'individual',
          country: user.address.country,
          representative: {
            given_name: user.firstName,
            surname: user.lastName,
            address: {
              city: user.address.city,
              country: user.address.country,
              line1: user.address.line1,
              line2: user.address.line2,
              postal_code: user.address.postcode,
              state: user.address.county,
            },
          },
        },
        configuration: {
          recipient_data: {
            features: {
              bank_accounts: {
                local: {
                  requested: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(
        {
          authId: user.authId,
          error,
        },
        'failed to create stripe user'
      );

      throw error;
    }
  }

  async updateUser(accountId: string, user: UpdateStripeAccount) {
    this.logger.log({ accountId, user }, 'updating user in stripe');

    try {
      return await this.stripe.v2.accounts.update(accountId, {
        name: `${user.firstName} ${user.lastName}`,
        legal_entity_data: {
          country: user.address.country,
          representative: {
            given_name: user.firstName,
            surname: user.lastName,
            address: {
              city: user.address.city,
              country: user.address.country,
              line1: user.address.line1,
              line2: user.address.line2,
              postal_code: user.address.postcode,
              state: user.address.county,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error({ error, accountId, user }, 'failed to update user');

      throw error;
    }
  }

  async getUser(accountId: string) {
    try {
      this.logger.log({ accountId }, 'getting user from stripe');

      return await this.stripe.v2.accounts.retrieve(accountId);
    } catch (error) {
      this.logger.error({ error, accountId }, 'failed to get user from stripe');

      throw error;
    }
  }

  async getTransactions(accountId: string) {
    this.logger.log({ accountId }, 'retrieving stripe transactions');

    const transactions = [];

    try {
      for await (const payment of this.stripe.v2.moneyManagement.outboundPayments.list(
        {
          recipient: accountId,
          status: ['posted'],
        }
      )) {
        transactions.push(payment);
      }

      return transactions;
    } catch (error) {
      this.logger.error(
        { accountId, error },
        'failed to retrieve stripe transactions'
      );

      throw error;
    }
  }
}
