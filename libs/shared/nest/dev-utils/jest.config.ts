/* eslint-disable */
export default {
  displayName: 'shared-nest-dev-utils',
  preset: '../../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../../../coverage/libs/shared/nest/dev-utils',
  testPathIgnorePatterns: ['index.spec.ts'],
};
