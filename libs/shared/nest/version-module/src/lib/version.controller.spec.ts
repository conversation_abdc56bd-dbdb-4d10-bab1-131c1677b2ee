import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { VersionController } from './version.controller';

import request from 'supertest';

describe('VersionController', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VersionController],
    }).compile();

    process.env.APPLICATION_VERSION = 'h4d0-asj21';

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return the version', () =>
    request(app.getHttpServer())
      .get(`/version`)
      .expect(200)
      .expect({ version: 'h4d0-asj21' }));
});
