import { ErrorEvent, EventHint } from '@sentry/node';
import { SentryIgnore, beforeSendSentryError } from './sentry-filter-errors';
import { createMock } from '@golevelup/ts-jest';
import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

const buildAxiosError = (code: number, message: string) =>
  new AxiosError(
    message,
    code.toString(),
    createMock<InternalAxiosRequestConfig>(),
    {},
    createMock<AxiosResponse>({
      status: code,
      statusText: message,
    })
  );

describe('Sentry Filter Errors', () => {
  describe('beforeSendSentryError()', () => {
    let isAxiosErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      isAxiosErrorSpy = jest.spyOn(axios, 'isAxiosError');
    });

    afterEach(() => jest.restoreAllMocks());

    it('returns the event if not an AxiosError', () => {
      const event = createMock<ErrorEvent>();
      const hint = createMock<EventHint>();

      const res = beforeSendSentryError(event, hint, []);

      expect(isAxiosErrorSpy).toHaveBeenCalledTimes(1);
      expect(isAxiosErrorSpy).toHaveBeenCalledWith(hint.originalException);
      expect(isAxiosErrorSpy).toHaveReturnedWith(false);

      expect(res).toStrictEqual(event);
    });

    it.each([
      [400, 'Bad Request'],
      [401, 'Unauthorized'],
      [403, 'Forbidden'],
      [404, 'Not Found'],
      [500, 'Internal Server Error'],
      [501, 'Not Implemented'],
      [502, 'Bad Gateway'],
      [503, 'Service Unavailable'],
    ])('returns the event if a HTTP status of %s', (status, message) => {
      const event = createMock<ErrorEvent>();
      const hint = createMock<EventHint>({
        originalException: buildAxiosError(status, message),
      });

      const res = beforeSendSentryError(event, hint, []);

      expect(isAxiosErrorSpy).toHaveBeenCalledTimes(1);
      expect(isAxiosErrorSpy).toHaveBeenCalledWith(hint.originalException);
      expect(isAxiosErrorSpy).toHaveReturnedWith(true);

      expect(res).toStrictEqual(event);
    });

    it('returns the event if the event name is NOT to be filtered', () => {
      const event = createMock<ErrorEvent>({
        exception: {
          values: [
            {
              stacktrace: {
                frames: [
                  {
                    function: 'myFunctionToNotBeFilteredOut',
                  },
                ],
              },
            },
          ],
        },
      });

      const hint = createMock<EventHint>({
        originalException: new AxiosError('Internal Server Error', '500'),
      });

      const res = beforeSendSentryError(event, hint, []);

      expect(res).toStrictEqual(event);
    });

    it('returns the event if the event name is to be filtered BUT not a valid status', () => {
      const event = createMock<ErrorEvent>({
        exception: {
          values: [
            {
              stacktrace: {
                frames: [
                  {
                    function: 'myFunctionToBeFilteredOut',
                  },
                ],
              },
            },
          ],
        },
      });

      const hint = createMock<EventHint>({
        originalException: buildAxiosError(500, 'Internal Server Error'),
      });

      const res = beforeSendSentryError(event, hint, [
        {
          functions: ['myFunctionToBeFilteredOut'],
          errorCodes: [400],
        },
      ]);

      expect(res).toStrictEqual(event);
    });

    it('returns null if the event name is to be filtered AND a valid status', () => {
      const event = createMock<ErrorEvent>({
        exception: {
          values: [
            {
              stacktrace: {
                frames: [
                  {
                    function: 'myFunctionToBeFilteredOut',
                  },
                ],
              },
            },
          ],
        },
      });

      const hint = createMock<EventHint>({
        originalException: buildAxiosError(409, 'Conflict'),
      });

      const res = beforeSendSentryError(event, hint, [
        {
          functions: ['myFunctionToBeFilteredOut'],
          errorCodes: [409],
        },
      ]);

      expect(res).toBeNull();
    });

    it('returns null if the incoming error is decorated with @SentryIgnore()', () => {
      @SentryIgnore()
      class ExampleError extends Error {}

      const event = createMock<ErrorEvent>();
      const hint = createMock<EventHint>({
        originalException: new ExampleError(),
      });

      const res = beforeSendSentryError(event, hint, []);

      expect(res).toBeNull();
    });
  });
});
