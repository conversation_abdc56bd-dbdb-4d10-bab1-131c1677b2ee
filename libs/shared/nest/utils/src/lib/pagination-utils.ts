import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import { Response as ExpressResponse, Request } from 'express';
import { buildRequestURL } from './request-url-param-decorator';
import dayjs from 'dayjs';

export const DEFAULT_LIMIT = 100;
export const DEFAULT_OFFSET = 0;

export interface Page<T> {
  elements: T[];
  number: number;
  size: number;
  totalElements: number;
  totalPages: number;
}

export interface PaginationParams {
  dateFieldName?: string;
  dateFrom: Date;
  dateTo: Date;
  limit: number;
  offset: number;
}

export const buildPage = <T>(
  elements: T[],
  totalElements: number,
  paginationParams: PaginationParams
): Page<T> => ({
  elements: elements,
  number: paginationParams.offset / paginationParams.limit + 1,
  size: elements.length,
  totalElements: totalElements,
  totalPages: Math.ceil(totalElements / paginationParams.limit),
});

export const buildPaginationParams = (request: Request): PaginationParams => {
  const { searchParams } = buildRequestURL(request);
  const from = searchParams.get('dateFrom') ?? searchParams.get('date_from');
  const to = searchParams.get('dateTo') ?? searchParams.get('date_to');
  const limit = searchParams.get('limit');
  const offset = searchParams.get('offset');
  return {
    dateFrom: from ? dayjs(from).toDate() : dayjs(0).toDate(),
    dateTo: to ? dayjs(to).toDate() : dayjs().toDate(),
    limit: limit ? Math.min(parseInt(limit), DEFAULT_LIMIT) : DEFAULT_LIMIT,
    offset: offset ? parseInt(offset) : DEFAULT_OFFSET,
  };
};

export const Pagination = createParamDecorator(
  (_data: string, ctx: ExecutionContext): PaginationParams => {
    const request: Request = ctx.switchToHttp().getRequest();
    return buildPaginationParams(request);
  }
);

export const setPaginationResponseHeaders = <T>(
  request: Request,
  response: ExpressResponse,
  page: Page<T>
) => {
  const paginationParams = buildPaginationParams(request);

  const limit =
    page.elements.length < paginationParams.limit
      ? page.elements.length
      : paginationParams.limit;

  if (limit >= paginationParams.limit) {
    const link = buildRequestURL(request);
    const offset = paginationParams.offset + paginationParams.limit;
    link.searchParams.set('offset', offset.toString());
    response.setHeader('Link', `<${link.href}>; rel="next"`);
  }

  response.setHeader('X-Total-Count', page.totalElements.toString());
  response.setHeader('X-Limit', limit.toString());
};

export const getNextPage = (response: Response): string | undefined => {
  const next = response.headers.get('Link');
  return next?.match(/<([^>]+)>; rel="next"/)?.[1];
};
