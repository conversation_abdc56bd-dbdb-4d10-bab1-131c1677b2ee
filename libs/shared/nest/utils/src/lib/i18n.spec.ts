import { getLanguageFromCode } from './i18n';

describe('language', () => {
  it('should return language', () => {
    expect(getLanguageFromCode('fr-FR')).toEqual('fr');
    expect(getLanguageFromCode('*')).toEqual('en');
    expect(getLanguageFromCode('en-US,en;q=0.5')).toEqual('en');
  });

  it('falls back to english if we do not support the language', () => {
    expect(getLanguageFromCode('de-CH')).toEqual('en');
    expect(getLanguageFromCode('de')).toEqual('en');
  });
});
