import { NextFunction, Request, Response } from 'express';
export const authCheckMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const auth = {
    login: process.env['API_USERNAME'] || 'experience',
    password: process.env['API_PASSWORD'] || 'experience',
  };

  const b64auth = (req.headers.authorization || '').split(' ')[1] || '';
  const [login, password] = Buffer.from(b64auth, 'base64')
    .toString()
    .split(':');

  if (login && password && login === auth.login && password === auth.password) {
    // Access granted...
    return next();
  }

  res.set('WWW-Authenticate', 'Basic realm="401"'); // change this
  res.status(401).send('Authentication required.'); // custom message
};
