import * as Sentry from '@sentry/node';
import {
  <PERSON><PERSON><PERSON>ler,
  ExecutionContext,
  HttpException,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, catchError } from 'rxjs';

@Injectable()
export class SentryInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SentryInterceptor.name);

  intercept<T>(_context: ExecutionContext, next: CallHandler): Observable<T> {
    return next.handle().pipe(
      catchError((error) => {
        if (error instanceof HttpException) {
          if (error.getStatus() >= 500) {
            this.logger.log({ error }, 'capturing exception');
            Sentry.captureException(error);
          }
        } else {
          this.logger.log({ error }, 'capturing exception');
          Sentry.captureException(error);
        }
        throw error;
      })
    );
  }
}
