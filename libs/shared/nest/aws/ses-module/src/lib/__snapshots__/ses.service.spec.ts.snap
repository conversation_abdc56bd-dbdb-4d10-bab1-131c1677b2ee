// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SimpleEmailService should send an email with an single attachment 1`] = `
"Date: Sat, 15 Apr 2023 00:00:00 +0000
From: =?utf-8?B?UG9kIFBvaW50?= <<EMAIL>>
To: <<EMAIL>>,
 <<EMAIL>>
Cc: 
Bcc: 
Message-ID: <<EMAIL>>
Subject: =?utf-8?B?QSBzdWJqZWN0?=
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: multipart/alternative; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some text

--8isu1in3a57
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some HTML

--8isu1in3a57
--8isu1in3a57
Content-Type: application/pdf; name="statement.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="statement.pdf"

bm90IGEgcGRm
--8isu1in3a57"
`;

exports[`SimpleEmailService should send an email with an single attachment in an array 1`] = `
"Date: Sat, 15 Apr 2023 00:00:00 +0000
From: =?utf-8?B?UG9kIFBvaW50?= <<EMAIL>>
To: <<EMAIL>>,
 <<EMAIL>>
Cc: 
Bcc: 
Message-ID: <<EMAIL>>
Subject: =?utf-8?B?QSBzdWJqZWN0?=
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: multipart/alternative; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some text

--8isu1in3a57
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some HTML

--8isu1in3a57
--8isu1in3a57
Content-Type: application/pdf; name="statement.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="statement.pdf"

bm90IGEgcGRm
--8isu1in3a57"
`;

exports[`SimpleEmailService should send an email with multiple attachments 1`] = `
"Date: Sat, 15 Apr 2023 00:00:00 +0000
From: =?utf-8?B?UG9kIFBvaW50?= <<EMAIL>>
To: <<EMAIL>>,
 <<EMAIL>>
Cc: 
Bcc: 
Message-ID: <<EMAIL>>
Subject: =?utf-8?B?QSBzdWJqZWN0?=
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: multipart/alternative; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some text

--8isu1in3a57
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some HTML

--8isu1in3a57
--8isu1in3a57
Content-Type: application/pdf; name="statement.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="statement.pdf"

bm90IGEgcGRm

--8isu1in3a57
Content-Type: application/pdf; name="invoice.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="invoice.pdf"

bm90IGEgcEDm
--8isu1in3a57"
`;

exports[`SimpleEmailService should send an email with no attachments 1`] = `
"Date: Sat, 15 Apr 2023 00:00:00 +0000
From: =?utf-8?B?UG9kIFBvaW50?= <<EMAIL>>
To: <<EMAIL>>,
 <<EMAIL>>
Cc: 
Bcc: 
Message-ID: <<EMAIL>>
Subject: =?utf-8?B?QSBzdWJqZWN0?=
MIME-Version: 1.0
Content-Type: multipart/alternative; boundary=8isu1in3a57

--8isu1in3a57
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some text

--8isu1in3a57
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: 7bit

Some HTML

--8isu1in3a57"
`;
