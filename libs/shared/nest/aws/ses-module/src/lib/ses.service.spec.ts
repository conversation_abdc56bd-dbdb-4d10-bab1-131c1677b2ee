import 'aws-sdk-client-mock-jest';
import * as MockDate from 'mockdate';
import { CACHE_MANAGER, CacheModule } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  DeleteSuppressedDestinationCommand,
  GetSuppressedDestinationCommand,
  ListSuppressedDestinationsCommand,
  NotFoundException,
  SESv2Client,
  SendEmailCommand,
  TooManyRequestsException,
} from '@aws-sdk/client-sesv2';
import { SimpleEmailService } from './ses.service';
import { SuppressionListReason } from '@aws-sdk/client-sesv2/dist-types/models/models_0';
import { Test, TestingModule } from '@nestjs/testing';
import { mockClient } from 'aws-sdk-client-mock';
import { v4 as uuid } from 'uuid';
import dayjs from 'dayjs';

describe('SimpleEmailService', () => {
  let service: SimpleEmailService;
  let cacheManager: Cache;
  let configService: ConfigService;
  const sesMock = mockClient(SESv2Client);

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule, CacheModule.register()],
      providers: [
        SimpleEmailService,
        {
          provide: SESv2Client,
          useValue: sesMock,
        },
      ],
    }).compile();

    service = module.get<SimpleEmailService>(SimpleEmailService);
    cacheManager = module.get<Cache>(CACHE_MANAGER);
    configService = module.get<ConfigService>(ConfigService);

    MockDate.set(new Date(2023, 3, 15));
  });

  beforeEach(async () => {
    sesMock.reset();
    await cacheManager.clear();
  });

  afterEach(async () => {
    jest.spyOn(configService, 'get').mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(cacheManager).toBeDefined();
    expect(configService).toBeDefined();
  });

  it.each([
    ['with no attachments', []],
    [
      'with an single attachment',
      {
        contentType: 'application/pdf',
        data: 'bm90IGEgcGRm',
        filename: 'statement.pdf',
      },
    ],
    [
      'with an single attachment in an array',
      [
        {
          contentType: 'application/pdf',
          data: 'bm90IGEgcGRm',
          filename: 'statement.pdf',
        },
      ],
    ],
    [
      'with multiple attachments',
      [
        {
          contentType: 'application/pdf',
          data: 'bm90IGEgcGRm',
          filename: 'statement.pdf',
        },
        {
          contentType: 'application/pdf',
          data: 'bm90IGEgcEDm',
          filename: 'invoice.pdf',
        },
      ],
    ],
  ])('should send an email %s', async (_, attachments) => {
    const mockFrom = jest.spyOn(Buffer, 'from');
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>', '<EMAIL>'],
      attachments
    );

    expect(result).toBe(true);
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      2
    );
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      2
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      2,
      GetSuppressedDestinationCommand,
      {
        EmailAddress: '<EMAIL>',
      }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(3, SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>', '<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });

    expect(mockFrom).toHaveBeenNthCalledWith(1, 'Pod Point');
    expect(mockFrom).toHaveBeenNthCalledWith(2, 'A subject');

    const rawMessage = mockFrom.mock.calls[2].toString();
    const rawMessageWithConstantData = rawMessage
      .replace(/Message-ID:.*/, 'Message-ID: <<EMAIL>>')
      .replace(/boundary=.*/g, 'boundary=8isu1in3a57')
      .replace(/--.*/g, '--8isu1in3a57');
    expect(rawMessageWithConstantData).toMatchSnapshot();
  });

  it('should add cc and bcc addresses to the email if present', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>', '<EMAIL>'],
      [],
      ['<EMAIL>'],
      ['<EMAIL>']
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      4
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      2,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      3,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      4,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(5, SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>', '<EMAIL>'],
        CcAddresses: ['<EMAIL>'],
        BccAddresses: ['<EMAIL>'],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  });

  it('should allow custom sender address', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>'],
      [],
      [],
      [],
      '"Test" <<EMAIL>>'
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandTimes(SendEmailCommand, 1);
    expect(sesMock).toHaveReceivedNthCommandWith(2, SendEmailCommand, {
      FromEmailAddress: '"Test" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  });

  it('should specify configuration set if configured', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });
    jest
      .spyOn(configService, 'get')
      .mockImplementation((propertyPath) =>
        propertyPath === 'AWS_SES_CONFIGURATION_SET_NAME' ? 'Test' : undefined
      );

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>']
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandWith(SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: 'Test',
    });
  });

  it.each(['BOUNCE', 'COMPLAINT'])(
    'should not send an email if the recipient is on the suppression list with reason %s',
    async (reason) => {
      sesMock.on(GetSuppressedDestinationCommand).resolvesOnce({
        SuppressedDestination: {
          Reason: reason as SuppressionListReason,
          EmailAddress: '<EMAIL>',
          LastUpdateTime: new Date(),
        },
      });

      const result = await service.sendEmail(
        'Some HTML',
        'Some text',
        'A subject',
        ['<EMAIL>']
      );

      expect(result).toEqual(false);
      expect(sesMock).not.toHaveReceivedCommand(SendEmailCommand);
    }
  );

  it('should filter to addresses if the recipient is on the suppression list', async () => {
    sesMock
      .on(GetSuppressedDestinationCommand)
      .resolvesOnce({
        SuppressedDestination: {
          Reason: 'COMPLAINT',
          EmailAddress: '<EMAIL>',
          LastUpdateTime: new Date(),
        },
      })
      .resolvesOnce({});
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>', '<EMAIL>']
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandWith(SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  });

  it('should filter cc / bcc addresses if the recipient is on the suppression list', async () => {
    sesMock
      .on(GetSuppressedDestinationCommand)
      .resolvesOnce({})
      .resolvesOnce({
        SuppressedDestination: {
          Reason: 'COMPLAINT',
          EmailAddress: '<EMAIL>',
          LastUpdateTime: new Date(),
        },
      })
      .resolvesOnce({
        SuppressedDestination: {
          Reason: 'COMPLAINT',
          EmailAddress: '<EMAIL>',
          LastUpdateTime: new Date(),
        },
      });
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>'],
      [],
      ['<EMAIL>'],
      ['<EMAIL>']
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandWith(SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  });

  it('should not filter addresses if the suppression list cannot be queried', async () => {
    sesMock.on(GetSuppressedDestinationCommand).rejects(
      new NotFoundException({
        message: 'Not Found',
        $metadata: {
          attempts: 1,
          httpStatusCode: 404,
          requestId: uuid(),
          totalRetryDelay: 0,
        },
      })
    );
    sesMock.on(SendEmailCommand).resolves({ MessageId: uuid() });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      ['<EMAIL>'],
      [],
      ['<EMAIL>'],
      ['<EMAIL>']
    );

    expect(result).toEqual(true);
    expect(sesMock).toHaveReceivedCommandWith(SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: ['<EMAIL>'],
        BccAddresses: ['<EMAIL>'],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  });

  it('should send an email and handle a not found exception', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).rejects(
      new NotFoundException({
        message: 'Not Found',
        $metadata: {
          attempts: 1,
          httpStatusCode: 404,
          requestId: uuid(),
          totalRetryDelay: 0,
        },
      })
    );

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      '<EMAIL>'
    );

    expect(result).toEqual(false);
    expectSesMockToHaveReceivedCommands();
  });

  it('should send an email and handle an error', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({});
    sesMock.on(SendEmailCommand).rejects({ message: 'Error' });

    const result = await service.sendEmail(
      'Some HTML',
      'Some text',
      'A subject',
      '<EMAIL>'
    );

    expect(result).toEqual(false);
    expectSesMockToHaveReceivedCommands();
  });

  it.each(['BOUNCE', 'COMPLAINT', 'OTHER'])(
    'should list email suppression list with reason %s',
    async (reason) => {
      sesMock.on(ListSuppressedDestinationsCommand).resolves({
        SuppressedDestinationSummaries: [
          {
            EmailAddress: '<EMAIL>',
            Reason: reason as SuppressionListReason,
            LastUpdateTime: new Date(),
          },
          {
            EmailAddress: '<EMAIL>',
            Reason: reason as SuppressionListReason,
            LastUpdateTime: new Date(),
          },
        ],
      });

      const emailAddresses = await service.listSuppressedDestinations(
        reason as SuppressionListReason
      );

      expect(emailAddresses.length).toEqual(2);
      expect(emailAddresses[0]).toEqual('<EMAIL>');
      expect(emailAddresses[1]).toEqual('<EMAIL>');
      expect(sesMock).toHaveReceivedCommandTimes(
        ListSuppressedDestinationsCommand,
        1
      );
      expect(sesMock).toHaveReceivedNthCommandWith(
        1,
        ListSuppressedDestinationsCommand,
        {
          Reasons: [reason as SuppressionListReason],
          StartDate: dayjs().subtract(1, 'months').toDate(),
        }
      );
    }
  );

  it.each(['BOUNCE', 'COMPLAINT', 'OTHER'])(
    'should list email suppression list with reason %s from cache',
    async (reason) => {
      await cacheManager.set(
        `list-suppressed-destinations-${reason}`,
        ['<EMAIL>', '<EMAIL>'],
        0
      );

      const emailAddresses = await service.listSuppressedDestinations(
        reason as SuppressionListReason
      );

      expect(emailAddresses.length).toEqual(2);
      expect(emailAddresses[0]).toEqual('<EMAIL>');
      expect(emailAddresses[1]).toEqual('<EMAIL>');
      expect(sesMock).toHaveReceivedCommandTimes(
        ListSuppressedDestinationsCommand,
        0
      );
    }
  );

  it('should delete a suppressed destination', async () => {
    sesMock.on(DeleteSuppressedDestinationCommand).resolves({});

    await service.deleteSuppressedDestination('<EMAIL>');

    expect(sesMock).toHaveReceivedCommandTimes(
      DeleteSuppressedDestinationCommand,
      1
    );
  });

  it('should propagate errors when deleting a suppressed destination', async () => {
    sesMock
      .on(DeleteSuppressedDestinationCommand)
      .rejects({ message: 'Error' });

    await expect(
      service.deleteSuppressedDestination('<EMAIL>')
    ).rejects.toThrow('Error');
  });

  it('should return undefined when destination is not suppressed', async () => {
    sesMock.on(GetSuppressedDestinationCommand).rejects(
      new NotFoundException({
        message: 'Not Found',
        $metadata: {
          attempts: 1,
          httpStatusCode: 404,
          requestId: uuid(),
          totalRetryDelay: 0,
        },
      })
    );

    const reason = await service.getSuppressedDestination('<EMAIL>');

    expect(reason).toBeUndefined();
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      1
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
  });

  it('should return reason when destination is suppressed', async () => {
    sesMock.on(GetSuppressedDestinationCommand).resolves({
      SuppressedDestination: {
        Reason: 'BOUNCE',
        EmailAddress: '<EMAIL>',
        LastUpdateTime: new Date(),
      },
    });

    const reason = await service.getSuppressedDestination('<EMAIL>');

    expect(reason).toEqual('BOUNCE');
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      1
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
  });

  it('should return unknown when checking if destination is suppressed fails', async () => {
    sesMock.on(GetSuppressedDestinationCommand).rejects(
      new TooManyRequestsException({
        message: 'Too Many Requests',
        $metadata: {
          attempts: 1,
          httpStatusCode: 429,
          requestId: uuid(),
          totalRetryDelay: 0,
        },
      })
    );

    const reason = await service.getSuppressedDestination('<EMAIL>');

    expect(reason).toEqual('UNKNOWN');
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      1
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
  });

  const expectSesMockToHaveReceivedCommands = () => {
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      1
    );
    expect(sesMock).toHaveReceivedCommandTimes(
      GetSuppressedDestinationCommand,
      1
    );
    expect(sesMock).toHaveReceivedNthCommandWith(
      1,
      GetSuppressedDestinationCommand,
      { EmailAddress: '<EMAIL>' }
    );
    expect(sesMock).toHaveReceivedNthCommandWith(2, SendEmailCommand, {
      FromEmailAddress: '"Pod Point" <<EMAIL>>',
      Destination: {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
      },
      Content: {
        Raw: {
          Data: expect.any(Buffer),
        },
      },
      ConfigurationSetName: undefined,
    });
  };
});
