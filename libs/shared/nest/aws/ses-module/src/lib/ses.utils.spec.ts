import { getTemplateStrings } from './ses.utils';
import fs from 'fs';
import path from 'path';

describe('SES Utils', () => {
  afterEach(() => jest.restoreAllMocks());

  describe('getTemplateStrings()', () => {
    let pathResolveSpy: jest.SpyInstance;
    let fsReadFileSyncSpy: jest.SpyInstance;

    beforeEach(() => {
      pathResolveSpy = jest.spyOn(path, 'resolve');
      fsReadFileSyncSpy = jest.spyOn(fs, 'readFileSync');
    });

    it('resolves and reads the HTML file', () => {
      getTemplateStrings(
        './libs/shared/nest/aws/ses-module/src/lib/__fixtures__/test'
      );

      expect(pathResolveSpy).toHaveBeenCalledWith(
        './libs/shared/nest/aws/ses-module/src/lib/__fixtures__/test.html'
      );
      expect(fsReadFileSyncSpy).toHaveBeenCalledWith(
        pathResolveSpy.mock.results[0].value
      );
    });

    it('resolves and reads the text file', () => {
      getTemplateStrings(
        './libs/shared/nest/aws/ses-module/src/lib/__fixtures__/test'
      );

      expect(pathResolveSpy).toHaveBeenCalledWith(
        './libs/shared/nest/aws/ses-module/src/lib/__fixtures__/test.txt'
      );
      expect(fsReadFileSyncSpy).toHaveBeenCalledWith(
        pathResolveSpy.mock.results[1].value
      );
    });

    it('returns the HTML and plain text template strings', () => {
      const res = getTemplateStrings(
        './libs/shared/nest/aws/ses-module/src/lib/__fixtures__/test'
      );

      expect(res).toHaveProperty(
        'htmlTemplateString',
        fsReadFileSyncSpy.mock.results[0].value.toString()
      );
      expect(res).toHaveProperty(
        'plainTextTemplateString',
        fsReadFileSyncSpy.mock.results[1].value.toString()
      );
    });
  });
});
