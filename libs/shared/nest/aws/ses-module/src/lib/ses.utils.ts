import fs from 'fs';
import path from 'path';

/**
 *
 * @param file The file path of where the template lives. Must NOT include the file extension.
 * @example getTemplateStrings("./assets/mobile-account/email-templates/en/email-update-confirmation")
 */
export const getTemplateStrings = (file: string) => {
  const htmlFilePath = path.resolve(`${file}.html`);
  const htmlTemplateString = fs.readFileSync(htmlFilePath).toString();
  const textFilePath = path.resolve(`${file}.txt`);
  const plainTextTemplateString = fs.readFileSync(textFilePath).toString();

  return { htmlTemplateString, plainTextTemplateString };
};
