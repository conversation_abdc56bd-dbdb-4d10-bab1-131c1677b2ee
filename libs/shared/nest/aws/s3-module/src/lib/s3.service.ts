import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { Injectable, Logger } from '@nestjs/common';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);

  constructor(private s3Client: S3Client) {}

  public async uploadFile(
    bucketName: string,
    dataBuffer: Uint8Array,
    fileName: string,
    contentType?: string
  ): Promise<boolean | undefined> {
    const command: PutObjectCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileName,
      Body: dataBuffer,
      ContentType: contentType,
    });

    await this.s3Client.send(command);
    return true;
  }

  public async getFile(
    bucketName: string,
    fileName: string
  ): Promise<Uint8Array | undefined> {
    const command: GetObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileName,
    });
    const response = await this.s3Client.send(command);

    return response?.Body?.transformToByteArray();
  }

  public async deleteFile(
    bucketName: string,
    fileName: string
  ): Promise<boolean | undefined> {
    const command: DeleteObjectCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: fileName,
    });

    await this.s3Client.send(command);
    return true;
  }

  public getSignedUrl(
    bucketName: string,
    fileName: string,
    expiresIn?: number
  ): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileName,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn });
  }
}
