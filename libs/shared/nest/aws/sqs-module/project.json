{"name": "shared-nest-aws-sqs-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/nest/aws/sqs-module/src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/nest/aws/sqs-module/jest.config.ts", "passWithNoTests": false}}}}