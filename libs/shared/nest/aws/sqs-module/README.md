# Shared Nest AWS sqs Module

Simple NestJS module wrapping around an AWS SQS consumer. This library was generated with [Nx](https://nx.dev).

## Running unit tests

Run `nx test shared-nest-aws-sqs-module` to execute the unit tests via [Jest](https://jestjs.io).

## Example

```
@Module({
  imports: [
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        queueUrl: config.get<string>('USER_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [MigrateService],
})
```

Add your messageHandler

```
type MessageHandler = (message: Message) => Promise<Message | void>;
```

```
  this.queueConsumerService.setMessageHandler(this.handleMessage);
  this.queueConsumerService.start();
```
