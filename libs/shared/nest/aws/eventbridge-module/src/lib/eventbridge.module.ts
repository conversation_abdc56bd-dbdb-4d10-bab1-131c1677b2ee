import { EventBridgeClient } from '@aws-sdk/client-eventbridge';
import { EventBridgeService } from './eventbridge.service';
import { Module } from '@nestjs/common';

@Module({
  providers: [
    EventBridgeService,
    {
      provide: EventBridgeClient,
      useValue: new EventBridgeClient({
        region: 'eu-west-1',
      }),
    },
  ],
  exports: [EventBridgeService],
})
export class EventBridgeModule {}
