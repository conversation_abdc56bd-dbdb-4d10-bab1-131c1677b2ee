import {
  RemoteConfig,
  RemoteConfigTemplate,
} from 'firebase-admin/remote-config';

export const getRemoteConfigResponse = (
  featureFlags: Record<string, string>
) => {
  const remoteConfigTemplate: RemoteConfigTemplate = {
    conditions: [],
    etag: '',
    parameterGroups: {},
    parameters: {},
  };
  for (const key in featureFlags) {
    remoteConfigTemplate.parameters[key] = {
      defaultValue: { value: featureFlags[key] },
    };
  }
  return remoteConfigTemplate;
};

export const mockRemoteConfigResponse = (
  featureFlags: Record<string, string>
) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  jest
    .spyOn(RemoteConfig.prototype, 'getTemplate')
    .mockResolvedValue(getRemoteConfigResponse(featureFlags));
};

export const mockRemoteConfigResponseOnce = (
  featureFlags: Record<string, string>
) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  jest
    .spyOn(RemoteConfig.prototype, 'getTemplate')
    .mockResolvedValueOnce(getRemoteConfigResponse(featureFlags));
};
