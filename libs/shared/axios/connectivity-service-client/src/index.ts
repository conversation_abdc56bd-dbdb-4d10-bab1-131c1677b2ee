/* tslint:disable */
/* eslint-disable */
/**
 * Connectivity Service Status API
 * An API for querying the current connected state of a charging station. The Connectivity Service monitors (PPCP) messages received from charging stations, and persists the last message This status api leverages the last message seen from a device. This status api also tracks which devices are currently connected via websockets ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export * from './api';
export * from './configuration';
