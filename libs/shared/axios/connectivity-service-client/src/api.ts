/* tslint:disable */
/* eslint-disable */
/**
 * Connectivity Service Status API
 * An API for querying the current connected state of a charging station. The Connectivity Service monitors (PPCP) messages received from charging stations, and persists the last message This status api leverages the last message seen from a device. This status api also tracks which devices are currently connected via websockets ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface DataStatusResponse
 */
export interface DataStatusResponse {
  /**
   *
   * @type {TypesChargingStation}
   * @memberof DataStatusResponse
   */
  chargingStation?: TypesChargingStation;
  /**
   *
   * @type {Array<string>}
   * @memberof DataStatusResponse
   */
  connectedComponents?: Array<string>;
  /**
   *
   * @type {Array<TypesEvse>}
   * @memberof DataStatusResponse
   */
  evses?: Array<TypesEvse>;
  /**
   *
   * @type {string}
   * @memberof DataStatusResponse
   */
  ppid?: string;
}
/**
 *
 * @export
 * @interface HandlersMessagesResponse
 */
export interface HandlersMessagesResponse {
  /**
   *
   * @type {Array<object>}
   * @memberof HandlersMessagesResponse
   */
  messages?: Array<object>;
  /**
   *
   * @type {string}
   * @memberof HandlersMessagesResponse
   */
  ppid?: string;
}
/**
 *
 * @export
 * @interface ServerApiErrorResponse
 */
export interface ServerApiErrorResponse {
  /**
   * Verbose error message
   * @type {string}
   * @memberof ServerApiErrorResponse
   */
  reason?: string;
  /**
   * The overall status of the request
   * @type {string}
   * @memberof ServerApiErrorResponse
   */
  status?: string;
}
/**
 *
 * @export
 * @interface TypesChargingStation
 */
export interface TypesChargingStation {
  /**
   *
   * @type {TypesConnectivityState}
   * @memberof TypesChargingStation
   */
  connectivityState?: TypesConnectivityState;
}
/**
 *
 * @export
 * @interface TypesConnectivityState
 */
export interface TypesConnectivityState {
  /**
   *
   * @type {number}
   * @memberof TypesConnectivityState
   */
  connectionQuality?: number;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  connectionStartedAt?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  connectivityStatus?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  lastMessageAt?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  lastSeenAt?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  offlineSubStatus?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnectivityState
   */
  protocol?: string;
  /**
   *
   * @type {number}
   * @memberof TypesConnectivityState
   */
  signalStrength?: number;
}
/**
 *
 * @export
 * @interface TypesConnector
 */
export interface TypesConnector {
  /**
   *
   * @type {Array<TypesFault>}
   * @memberof TypesConnector
   */
  activeFaults?: Array<TypesFault>;
  /**
   *
   * @type {string}
   * @memberof TypesConnector
   */
  chargingState?: string;
  /**
   *
   * @type {string}
   * @memberof TypesConnector
   */
  door?: string;
  /**
   *
   * @type {number}
   * @memberof TypesConnector
   */
  id?: number;
}
/**
 *
 * @export
 * @interface TypesEvse
 */
export interface TypesEvse {
  /**
   *
   * @type {string}
   * @memberof TypesEvse
   */
  architecture?: string;
  /**
   *
   * @type {TypesConnectivityState}
   * @memberof TypesEvse
   */
  connectivityState?: TypesConnectivityState;
  /**
   *
   * @type {Array<TypesConnector>}
   * @memberof TypesEvse
   */
  connectors?: Array<TypesConnector>;
  /**
   *
   * @type {number}
   * @memberof TypesEvse
   */
  id?: number;
  /**
   *
   * @type {string}
   * @memberof TypesEvse
   */
  macAddress?: string;
  /**
   *
   * @type {string}
   * @memberof TypesEvse
   */
  serialNumber?: string;
}
/**
 *
 * @export
 * @interface TypesFault
 */
export interface TypesFault {
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  LatestOccurrence?: string;
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  errorCode?: string;
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  firstOccurrence?: string;
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  info?: string;
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  podPointFault?: string;
  /**
   *
   * @type {string}
   * @memberof TypesFault
   */
  vendorErrorCode?: string;
}

/**
 * ConnectivityStatusApi - axios parameter creator
 * @export
 */
export const ConnectivityStatusApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Provide last messages for a given PPID
     * @summary Provide last messages for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationMessages: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationMessages', 'ppid', ppid);
      const localVarPath =
        `/connectivity/charging-stations/{ppid}/messages`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Provide Connectivity Status for a given PPID
     * @summary Provide Connectivity Status for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationStatus: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationStatus', 'ppid', ppid);
      const localVarPath = `/connectivity/charging-stations/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ConnectivityStatusApi - functional programming interface
 * @export
 */
export const ConnectivityStatusApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ConnectivityStatusApiAxiosParamCreator(configuration);
  return {
    /**
     * Provide last messages for a given PPID
     * @summary Provide last messages for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationMessages(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersMessagesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationMessages(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ConnectivityStatusApi.getChargingStationMessages'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Provide Connectivity Status for a given PPID
     * @summary Provide Connectivity Status for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DataStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationStatus(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ConnectivityStatusApi.getChargingStationStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ConnectivityStatusApi - factory interface
 * @export
 */
export const ConnectivityStatusApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ConnectivityStatusApiFp(configuration);
  return {
    /**
     * Provide last messages for a given PPID
     * @summary Provide last messages for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationMessages(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersMessagesResponse> {
      return localVarFp
        .getChargingStationMessages(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Provide Connectivity Status for a given PPID
     * @summary Provide Connectivity Status for a given PPID
     * @param {string} ppid Charging Station ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DataStatusResponse> {
      return localVarFp
        .getChargingStationStatus(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ConnectivityStatusApi - object-oriented interface
 * @export
 * @class ConnectivityStatusApi
 * @extends {BaseAPI}
 */
export class ConnectivityStatusApi extends BaseAPI {
  /**
   * Provide last messages for a given PPID
   * @summary Provide last messages for a given PPID
   * @param {string} ppid Charging Station ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ConnectivityStatusApi
   */
  public getChargingStationMessages(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ConnectivityStatusApiFp(this.configuration)
      .getChargingStationMessages(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Provide Connectivity Status for a given PPID
   * @summary Provide Connectivity Status for a given PPID
   * @param {string} ppid Charging Station ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ConnectivityStatusApi
   */
  public getChargingStationStatus(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ConnectivityStatusApiFp(this.configuration)
      .getChargingStationStatus(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
