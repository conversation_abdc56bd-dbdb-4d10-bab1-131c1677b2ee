/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/events/security`, async () => {
    const resultArray = [
      [await getGetSecurityEventsPpid200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/evses/:serialNumber/events/security`, async () => {
    const resultArray = [
      [await getGetSecurityEvents200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/logs`, async () => {
    const resultArray = [[await getDownloadLog202Response(), { status: 202 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/logs`, async () => {
    const resultArray = [[await getGetLogs200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/logs/:ppid/:door`, async () => {
    const resultArray = [
      [await getGetLogsForDoor200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/commands/responses`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getGetSecurityEventsPpid200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ppid: 'PSL-12345',
      door: 'A',
      serialNumber: '30303030',
      sequenceNumber: 1,
      techInfo: 'Tamper detected at runtime',
      type: {
        code: 'TamperDetectionActivated',
        name: 'Cover Opened',
        description: 'Device case opened',
        links: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          description: 'A useful link',
          url: 'https://www.pod-point.com',
        })),
      },
      eventTimestamp: faker.date.past(),
      receivedTimestamp: faker.date.past(),
    })),
    meta: {
      pagination: {
        currentPage: 1,
        updateId: 34812,
        perPage: 50,
        pageCount: 1,
        itemCount: 1,
      },
    },
  };
}

export function getGetSecurityEvents200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ppid: 'PSL-12345',
      door: 'A',
      serialNumber: '30303030',
      sequenceNumber: 1,
      techInfo: 'Tamper detected at runtime',
      type: {
        code: 'TamperDetectionActivated',
        name: 'Cover Opened',
        description: 'Device case opened',
        links: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          description: 'A useful link',
          url: 'https://www.pod-point.com',
        })),
      },
      eventTimestamp: faker.date.past(),
      receivedTimestamp: faker.date.past(),
    })),
    meta: {
      pagination: {
        currentPage: 1,
        updateId: 34812,
        perPage: 50,
        pageCount: 1,
        itemCount: 1,
      },
    },
  };
}

export function getDownloadLog202Response() {
  return {
    id: faker.number.int(),
    logType: faker.helpers.arrayElement(['DiagnosticsLog', 'SecurityLog']),
    ppid: 'PSL-12345',
    door: faker.helpers.arrayElement(['A', 'B']),
    location: 'PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log',
    status: faker.helpers.arrayElement([
      'REQUESTED',
      'ACCEPTED',
      'COMPLETED',
      'FAILED',
    ]),
    from: faker.date.past(),
    to: faker.date.past(),
    readUrl:
      'https://pod-point-cs-logs-123.s3.eu-west-1.amazonaws.com/PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=foobarbazetc',
    writeUrl: 'https://short-staging.pod-point.com/foobarbaz',
  };
}

export function getGetLogs200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.number.int(),
      logType: faker.helpers.arrayElement(['DiagnosticsLog', 'SecurityLog']),
      ppid: 'PSL-12345',
      door: faker.helpers.arrayElement(['A', 'B']),
      location: 'PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log',
      status: faker.helpers.arrayElement([
        'REQUESTED',
        'ACCEPTED',
        'COMPLETED',
        'FAILED',
      ]),
      from: faker.date.past(),
      to: faker.date.past(),
      readUrl:
        'https://pod-point-cs-logs-123.s3.eu-west-1.amazonaws.com/PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=foobarbazetc',
    })),
    meta: {
      pagination: {
        currentPage: 1,
        updateId: 34812,
        perPage: 50,
        pageCount: 1,
        itemCount: 1,
      },
    },
  };
}

export function getGetLogsForDoor200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.number.int(),
      logType: faker.helpers.arrayElement(['DiagnosticsLog', 'SecurityLog']),
      ppid: 'PSL-12345',
      door: faker.helpers.arrayElement(['A', 'B']),
      location: 'PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log',
      status: faker.helpers.arrayElement([
        'REQUESTED',
        'ACCEPTED',
        'COMPLETED',
        'FAILED',
      ]),
      from: faker.date.past(),
      to: faker.date.past(),
      readUrl:
        'https://pod-point-cs-logs-123.s3.eu-west-1.amazonaws.com/PSL-123456/A/DiagnosticsLog/2024-05-02/07_36_37.log?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=foobarbazetc',
    })),
    meta: {
      pagination: {
        currentPage: 1,
        updateId: 34812,
        perPage: 50,
        pageCount: 1,
        itemCount: 1,
      },
    },
  };
}
