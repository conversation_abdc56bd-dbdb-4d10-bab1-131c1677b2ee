import { http, HttpHand<PERSON>, HttpResponse } from 'msw';
import { TEST_FIRMWARE_STATUS_TYPES } from '../__fixtures__';
import { BASE_PATH } from '../src/base';

export const createFirmwareUpgradeClientMockRequestHandlers =
  (): Array<HttpHandler> => {
    return [
      http.get(`${BASE_PATH}/firmware/device`, ({ request }) => {
        const expectedSignatureHeaders = [
          'authorization',
          'host',
          'x-amz-content-sha256',
          'x-amz-date',
        ];
        const missingSignatureHeaders = expectedSignatureHeaders.filter(
          (expectedSignatureHeader) =>
            !request.headers.has(expectedSignatureHeader)
        );
        if (missingSignatureHeaders.length > 0) {
          return HttpResponse.json(
            {
              message: `${missingSignatureHeaders.join(
                ', '
              )} headers are missing`,
            } as any,
            { status: 401 }
          );
        }

        return HttpResponse.json(TEST_FIRMWARE_STATUS_TYPES);
      }),
    ];
  };

export const createFirmwareUpgradeClientMockRequestHandlersWithoutSignature = (
  BASE_URL: string
) => [
  http.get(`${BASE_URL}/firmware/device`, () =>
    HttpResponse.json(TEST_FIRMWARE_STATUS_TYPES)
  ),
];
