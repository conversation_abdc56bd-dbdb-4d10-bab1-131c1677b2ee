openapi: 3.0.0
info:
  title: Firmware API
  description: An API for managing firmware updates for a single EVSE or a collection of them.
  version: 1.0.0
  contact:
    name: Software Team
    email: <EMAIL>
    url: 'https://pod-point.com'
servers:
  - description: local
    url: http://localhost:3000
  - description: dev
    url: https://oj1uagd70a-vpce-0b20a72fe87c6c4bb.execute-api.eu-west-1.amazonaws.com/dev
  - description: staging
    url: https://34q4zjsftb-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging
  - description: prod
    url: https://tu6gatoce8-vpce-0481283a82350ef52.execute-api.eu-west-1.amazonaws.com/prod
tags:
  - name: Manifests
  - name: Info
  - name: Updates
  - name: Groups
paths:
  /firmware/device:
    get:
      summary: Search Firmware Device States based on parameters
      description: Search information current firmware for an existing EVSE
      operationId: get-current-firmware
      parameters:
        - $ref: '#/components/parameters/ppid'
        - $ref: '#/components/parameters/macAddress'
      tags:
        - Info
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FirmwareSearchResponse'
  /firmware/device/{serialNumber}:
    get:
      summary: Search Firmware Device States based on serial number
      description: Search information current firmware for an existing EVSE
      operationId: get-current-firmware-serial-number
      parameters:
        - $ref: '#/components/parameters/serialNumber'
      tags:
        - Info
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FirmwareStatusType'
  /firmware/version/{pcbArchitecture}:
    get:
      summary: Find Firmware Version
      description: Find the manufacturing firmware version for a given architecture
      operationId: find-firmware-version
      parameters:
        - $ref: '#/components/parameters/pcbArchitecture'
      tags:
        - Info
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FirmwareVersionType'
  /evses/{serialNumber}/firmware/updates:
    get:
      summary: Get Firmware Updates
      description: Show pending and historical updates for the provided EVSE
      operationId: get-firmware-updates
      tags:
        - Updates
      parameters:
        - $ref: '#/components/parameters/serialNumber'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFirmwareUpdatesResponse'
    post:
      summary: Update Firmware
      description: Request a firmware update
      operationId: create-firmware-update
      tags:
        - Updates
      parameters:
        - $ref: '#/components/parameters/serialNumber'
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFirmwareUpdateRequest'
      responses:
        '202':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PpcpFirmwareUpdateType'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '410':
          description: Gone
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /evses/{serialNumber}/firmware/updates/{updateId}:
    get:
      summary: Get Firmware Update
      description: Show pending and historical updates for the provided EVSE
      operationId: get-firmware-update
      tags:
        - Updates
      parameters:
        - $ref: '#/components/parameters/serialNumber'
        - $ref: '#/components/parameters/updateId'
      responses:
        '200':
          $ref: '#/components/responses/GetFirmwareUpdateResponse'
        '404':
          description: An Firmware Update with this id belonging to this serial number cannot be found
  /firmware/manifests:
    get:
      summary: Get Manifests
      description: Get known manifests and versions
      operationId: get-manifests
      tags:
        - Manifests
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ManifestType'
    post:
      summary: Create Manifest
      description: Create new manifest
      operationId: create-manifest
      tags:
        - Manifests
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManifestType'
      responses:
        '201':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManifestType'
        '409':
          description: Manifest Already Exists
  /firmware/update-batches:
    get:
      operationId: get-update-batch
      summary: Get Update batch
      description: Retrieve a update batch by batchName
      tags:
        - Updates
      parameters:
        - $ref: '#/components/parameters/batchName'
        - $ref: '#/components/parameters/clientRef'
      responses:
        '200':
          $ref: '#/components/responses/FirmwareUpdateBatchResponse'
    post:
      operationId: create-update-batch
      summary: Create Batch Update
      description: Create an update batch
      tags:
        - Updates
      requestBody:
        $ref: '#/components/requestBodies/CreateFirmwareUpdateBatchRequest'
      responses:
        '202':
          $ref: '#/components/responses/FirmwareUpdateBatchResponse'
  /firmware/update-batches/{batchId}:
    get:
      operationId: get-update-batch-by-id
      summary: Get Update batch by batchId
      description: Retrieve an update batch
      tags:
        - Updates
      parameters:
        - $ref: '#/components/parameters/batchId'
      responses:
        '200':
          $ref: '#/components/responses/FirmwareUpdateBatchResponse'
  /serial-number-groups:
    get:
      operationId: list-serial-number-groups
      summary: Get all serial number groups
      description: Get all the serial number groups and their metadata except the list of serial numbers
      tags:
        - Groups
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SerialNumberGroupType'
    post:
      operationId: post-serial-number-group
      summary: Create a new serial number group
      description: Create a new serial number group with a unique name
      tags:
        - Groups
      parameters:
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        $ref: '#/components/requestBodies/CreateSerialNumberGroupRequest'
      responses:
        '200':
          $ref: '#/components/responses/SerialNumberGroupResponse'
  /serial-number-groups/{id}:
    get:
      operationId: get-serial-number-group
      summary: Get specific serial number group
      description: Get a specific serial number group and it's metadata
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
      responses:
        '200':
          $ref: '#/components/responses/SerialNumberGroupResponse'
    patch:
      summary: Update a serial number group
      description: Update a serial number group firmware version and/or manifest
      operationId: patch-serial-number-group
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        $ref: '#/components/requestBodies/PatchSerialNumberGroupRequest'
      responses:
        '200':
          $ref: '#/components/responses/SerialNumberGroupResponse'
    delete:
      summary: Delete a serial number group
      description: Delete a serial number group and all its corresponding serial number group members
      operationId: delete-serial-number-group
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      responses:
        '204':
          description: OK
  /serial-number-groups/{id}/serial-numbers:
    get:
      summary: Get serial numbers for group
      description: Get all serial numbers for a serial number group
      operationId: get-serial-numbers
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
      responses:
        '200':
          $ref: '#/components/responses/SerialNumberGroupMembersResponse'
        '404':
          description: Serial number group not found
    post:
      summary: Add serial numbers
      description: Append to a serial number group's serial numbers
      operationId: post-add-serial-numbers
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        $ref: '#/components/requestBodies/UpdateSerialNumbersRequest'
      responses:
        '200':
          description: OK
    patch:
      summary: Move serial numbers
      description: Move serial numbers to a group
      operationId: patch-move-serial-numbers
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        $ref: '#/components/requestBodies/UpdateSerialNumbersRequest'
      responses:
        '200':
          description: OK
    delete:
      summary: Remove serial numbers
      description: Remove serial numbers from a serial number group
      operationId: post-remove-serial-numbers
      tags:
        - Groups
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      requestBody:
        $ref: '#/components/requestBodies/UpdateSerialNumbersRequest'
      responses:
        '200':
          description: OK
  /serial-number-groups/{id}/trigger-update:
    post:
      summary: Trigger Firmware Update
      description: Trigger a firmware update for all serial numbers in a group
      operationId: trigger-firmware-update
      tags:
        - Groups
        - Updates
      parameters:
        - name: id
          in: path
          schema:
            type: string
            format: uuid
          required: true
          example: 64d16301-dc46-4086-b80d-ecf25eee2b43
        - $ref: '#/components/parameters/clientRef'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TriggerUpdateResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Serial number group not found
  /serial-number-groups/rollout/arch5:
    get:
      operationId: get-serial-number-for-arch5-rollout
      summary: Get serial numbers for arch5 rollout
      description: Get serial numbers for candidates for a new arch5 rollout
      tags:
        - Groups
      parameters:
        - name: rolloutSize
          in: query
          schema:
            type: number
          required: true
          example: 200
      responses:
        '200':
          $ref: '#/components/responses/SerialNumberGroupRolloutResponse'
  /serial-number-groups/serial-numbers:
    get:
      summary: Get serial numbers that match a given string
      description: Get serial numbers that match a given string
      operationId: get-serial-numbers-by-string
      tags:
        - Groups
      parameters:
        - name: substring
          in: query
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SerialNumberSearchResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
          example: 'Error message'
        ppid:
          type: string
          example: 'PSL-123456'
        serialNumber:
          type: string
          example: '3030303030'
        e:
          type: object
    CreateFirmwareUpdateRequest:
      type: object
      properties:
        versionInfo:
          oneOf:
            - $ref: '#/components/schemas/FirmwareUpdateManifestVersionType'
        scheduledDate:
          type: string
          format: date-time
        skipUpdateToSameVersion:
          type: boolean
    FirmwareArchitectureEnumType:
      type: string
      enum:
        - arch2
        - arch2.4
        - arch3
        - arch5
        - rfid
    FirmwareUpdateBatchRequestType:
      type: object
      properties:
        evseSerialNumbers:
          type: array
          items:
            $ref: '#/components/schemas/SerialNumber'
        versionInfo:
          oneOf:
            - $ref: '#/components/schemas/FirmwareUpdateManifestVersionType'
        skipUpdateToSameVersion:
          type: boolean
      required:
        - evseSerialNumbers
    FirmwareUpdateBatchType:
      type: object
      properties:
        id:
          type: string
          format: uuid
        batchName:
          type: string
        manifest:
          oneOf:
            - $ref: '#/components/schemas/ManifestType'
        requestedAt:
          type: string
          format: date-time
        totalSerialNumbers:
          type: integer
    ManifestLegacyType:
      type: object
      properties:
        manifestId:
          $ref: '#/components/schemas/ManifestPropManifestIdType'
        architecture:
          type: string
          enum:
            - arch1
            - arch2
            - arch2.4
            - arch3
        details:
          $ref: '#/components/schemas/StandardChipDetailsType'
        createdDate:
          $ref: '#/components/schemas/ManifestPropCreatedDateType'
      required:
        - manifestId
        - architecture
        - details
    ManifestArch5Type:
      type: object
      properties:
        manifestId:
          $ref: '#/components/schemas/ManifestPropManifestIdType'
        architecture:
          type: string
          enum:
            - arch5
        createdDate:
          $ref: '#/components/schemas/ManifestPropCreatedDateType'
        path:
          type: string
          example: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb'
      required:
        - manifestId
        - architecture
        - path
    ManifestPropManifestIdType:
      type: string
      example: A22P-2.37.0-00001
    ManifestPropCreatedDateType:
      type: string
      format: date
      example: 2022-01-01
      nullable: true
    ManifestType:
      oneOf:
        - $ref: '#/components/schemas/ManifestLegacyType'
        - $ref: '#/components/schemas/ManifestArch5Type'
    VersionInfoType:
      type: object
      properties:
        manifestId:
          type: string
          example: A50P-X.Y.Z-0000P
          nullable: true
        architecture:
          type: string
          enum:
            - arch1
            - arch2
            - arch2.4
            - arch3
            - arch5
        details:
          oneOf:
            - $ref: '#/components/schemas/StandardChipDetailsType'
            - $ref: '#/components/schemas/RfidChipDetailsType'
            - type: object # empty object in case of arch5
    FirmwareSearchResponse:
      type: array
      items:
        $ref: '#/components/schemas/FirmwareStatusType'
    FirmwareStatusType:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        versionInfo:
          $ref: '#/components/schemas/VersionInfoType'
        updateStatus:
          type: object
          properties:
            isUpdateAvailable:
              type: boolean
              example: true
            updateVersion:
              $ref: '#/components/schemas/VersionInfoType'
            updateId:
              type: string
              example: '5'
            status:
              type: string
              enum:
                - NOT_REQUESTED
                - NOT_ACCEPTED
                - IN_PROGRESS
                - FAILED
              example: 'IN_PROGRESS'
          required:
            - isUpdateAvailable
      required:
        - serialNumber
        - versionInfo
        - updateStatus
    TriggerUpdateResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - allAccepted
            - someAccepted
    FirmwareVersionType:
      type: object
      properties:
        manufacturing:
          type: object
          properties:
            manifest:
              type: object
              properties:
                manifestId:
                  type: string
                  example: A50P-X.Y.Z-0000P
            url:
              type: string
              format: uri
        ota:
          type: object
          properties:
            manifest:
              type: object
              properties:
                manifestId:
                  type: string
                  example: A50P-X.Y.Z-0000P
            url:
              type: string
              format: uri
    FirmwareUpdateManifestVersionType:
      type: object
      properties:
        manifest:
          type: string
          example: A22P-2.37.0-00001
      required:
        - manifest
    PpcpFirmwareUpdateType:
      type: object
      properties:
        id:
          type: string
          format: uuid
        targetVersionInfo:
          oneOf:
            - $ref: '#/components/schemas/ManifestType'
        sourceVersionInfo:
          oneOf:
            - $ref: '#/components/schemas/ManifestType'
          nullable: true
        currentStatus:
          $ref: '#/components/schemas/PpcpFirmwareUpdateStatusEnumType'
        attempts:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              pcbType:
                type: string
                enum:
                  - dsp
                  - wifi
              status:
                type: string
                enum:
                  - AWAITING_PILOT_STATE_A
                  - PENDING
                  - STARTED
                  - INSTALLED
                  - FAILED
                  - SKIPPED
              sequence:
                type: integer
              startedClientTimestamp:
                type: string
                nullable: true
                format: date-time
        requestedAt:
          type: string
          format: date-time
        scheduledDate:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
          nullable: true
      required:
        - id
        - targetVersionInfo
        - sourceVersionInfo
        - currentStatus
        - requestedAt
    OcppFirmwareUpdateType:
      type: object
      properties:
        id:
          type: string
          format: uuid
        targetManifestId:
          type: string
          example: 'A50P-1.9.1'
        sourceManifestId:
          type: string
          example: 'A50P-1.9.1'
        currentStatus:
          $ref: '#/components/schemas/OcppFirmwareUpdateStatusEnumType'
        requestedAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
          nullable: true
      required:
        - id
        - targetVersionInfo
        - sourceVersionInfo
        - currentStatus
        - requestedAt
    PpcpFirmwareUpdateStatusEnumType:
      type: string
      enum:
        - QUEUED
        - SCHEDULED
        - IN_PROGRESS
        - COMPLETED
        - FAILED
    OcppFirmwareUpdateStatusEnumType:
      type: string
      enum:
        - NOT_ACCEPTED
        - IN_PROGRESS
        - COMPLETED
        - FAILED
    PcbArchitectureEnumType:
      type: string
      enum:
        - '2.0'
        - '2.1'
        - '2.4'
        - '3.0'
        - '5.0'
        - '5.1'
    GetFirmwareUpdatesResponse:
      type: object
      properties:
        updates:
          type: array
          items:
            $ref: '#/components/schemas/PpcpFirmwareUpdateType'
    ManifestSearchResponse:
      type: array
      items:
        $ref: '#/components/schemas/ManifestType'
    ManifestStatusEnumType:
      type: string
      enum:
        - candidate
        - alpha
        - beta
        - release
        - archived
    RfidChipDetailsType:
      type: object
      properties:
        rfidVersion:
          type: string
          example: 1.1.1
      required:
        - rfidVersion
    SerialNumber:
      type: string
      example: '30303030'
    StandardChipDetailsType:
      type: object
      properties:
        dspVersion:
          type: string
          example: 2.37.0-00005
        wifiVersion:
          type: string
          example: '1.14'
      required:
        - dspVersion
        - wifiVersion
    CreateSerialNumberGroupRequestType:
      type: object
      properties:
        name:
          type: string
          example: 3_x_alpha
        targetManifestId:
          type: string
          nullable: true
          example: A22P-2.37.0-00001
        architecture:
          $ref: '#/components/schemas/FirmwareArchitectureEnumType'
      required:
        - name
        - architecture
    PatchSerialNumberGroupRequestType:
      type: object
      properties:
        targetManifestId:
          type: string
          nullable: true
          example: A22P-2.37.0-00001
      required:
        - name
        - architecture
    UpdateSerialNumbersRequestType:
      type: object
      properties:
        serialNumbers:
          type: array
          nullable: false
          items:
            $ref: '#/components/schemas/SerialNumber'
      required:
        - serialNumbers
    SerialNumberGroupType:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        targetManifestId:
          type: string
          nullable: true
        architecture:
          $ref: '#/components/schemas/FirmwareArchitectureEnumType'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    SerialNumberGroupMemberType:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        currentManifestId:
          type: string
          nullable: true
        isOnTargetManifest:
          type: boolean
        createdAt:
          type: string
          format: date-time
    SerialNumberSearchResponse:
      type: array
      items:
        $ref: '#/components/schemas/SerialNumberSearchType'
    SerialNumberSearchType:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        id:
          type: string
          format: uuid
        name:
          type: string
        targetManifestId:
          type: string
          nullable: true
        currentManifestId:
          type: string
          nullable: true
        isOnTargetManifest:
          type: boolean
        architecture:
          $ref: '#/components/schemas/FirmwareArchitectureEnumType'
      required:
        - serialNumber
        - id
        - name
        - architecture
  parameters:
    batchId:
      name: batchId
      description: ID of an existing update batch
      in: path
      required: true
      schema:
        type: string
    batchName:
      name: batchName
      description: Name of an existing update batch
      in: query
      required: true
      schema:
        type: string
    clientRef:
      name: clientRef
      description: Client reference
      in: header
      schema:
        type: string
        example: '<EMAIL>'
    ppid:
      name: ppid
      description: PPID from an existing unit
      example: PSL-12345
      in: query
      required: true
      schema:
        type: string
    macAddress:
      name: macAddress
      description: mac address from an existing unit
      example: 0cb2b703c778
      in: query
      required: false
      schema:
        type: string
    serialNumber:
      name: serialNumber
      description: The serial number of an existing EVSE
      in: path
      schema:
        $ref: '#/components/schemas/SerialNumber'
      required: true
    updateId:
      name: updateId
      description: ID of an existing PPCP update or request id of an existing OCPP update
      in: path
      required: true
      schema:
        type: string
    pcbArchitecture:
      name: pcbArchitecture
      description: The hardware architecture of the PCB (only applicable to PodPoint units)
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/PcbArchitectureEnumType'
  requestBodies:
    CreateFirmwareUpdateBatchRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/FirmwareUpdateBatchRequestType'
    CreateSerialNumberGroupRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateSerialNumberGroupRequestType'
    PatchSerialNumberGroupRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PatchSerialNumberGroupRequestType'
    UpdateSerialNumbersRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UpdateSerialNumbersRequestType'
  responses:
    FirmwareUpdateBatchResponse:
      description: Example response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/FirmwareUpdateBatchType'
    GetFirmwareUpdateResponse:
      description: Example response
      content:
        application/json:
          schema:
            oneOf:
              - $ref: '#/components/schemas/PpcpFirmwareUpdateType'
              - $ref: '#/components/schemas/OcppFirmwareUpdateType'
    SerialNumberGroupRolloutResponse:
      description: Example response for an arch 5 serial number group rollout
      content:
        application/json:
          schema:
            type: object
            properties:
              serialNumbers:
                type: array
                items:
                  $ref: '#/components/schemas/SerialNumber'
    SerialNumberGroupResponse:
      description: Example serial number group response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SerialNumberGroupType'
    SerialNumberGroupMembersResponse:
      description: Example serial number group members response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SerialNumberGroupMemberType'
