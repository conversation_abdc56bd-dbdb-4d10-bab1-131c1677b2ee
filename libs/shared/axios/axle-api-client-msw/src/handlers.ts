/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/entities/asset`, async () => {
    const resultArray = [
      [await getGetAssetsEntitiesAssetGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/entities/asset`, async () => {
    const resultArray = [
      [await getCreateAssetEntitiesAssetPost200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/entities/asset/:assetId`, async () => {
    const resultArray = [
      [await getGetAssetEntitiesAssetAssetIdGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/entities/asset/:assetId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/entities/asset/external-id/:externalId`, async () => {
    const resultArray = [
      [
        await getGetAssetByExternalIdEntitiesAssetExternalIdExternalIdGet200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(
    `${baseURL}/entities/asset/:assetId/callback/:eventId`,
    async () => {
      const resultArray = [
        [
          await getEventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/entities/asset/:assetId/event`, async () => {
    const resultArray = [
      [
        await getHandleAssetEventEntitiesAssetAssetIdEventPost200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/entities/site`, async () => {
    const resultArray = [
      [await getGetSitesEntitiesSiteGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/entities/site`, async () => {
    const resultArray = [
      [await getCreateSiteEntitiesSitePost200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/entities/site/:siteId`, async () => {
    const resultArray = [
      [await getGetSiteEntitiesSiteSiteIdGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/entities/site/:siteId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/entities/site/:siteId`, async () => {
    const resultArray = [
      [
        await getUpdateSiteEntitiesSiteSiteIdPatch200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/entities/site/:siteId/market-consent`, async () => {
    const resultArray = [
      [
        await getAddSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/entities/site/:siteId/market-deconsent`, async () => {
    const resultArray = [
      [
        await getRemoveSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/qualification/dno/:postcode`, async () => {
    const resultArray = [
      [
        await getCheckPostcodeQualificationQualificationDnoPostcodeGet200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/flexible-power/v2/dispatch`, async () => {
    const resultArray = [
      [
        await getFlexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/electron-connect/dispatch`, async () => {
    const resultArray = [
      [
        await getElectronConnectDispatchApiElectronConnectDispatchPost200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/frequency/historic`, async () => {
    const resultArray = [
      [await getGetFrequencyFrequencyHistoricGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/meter/search`, async () => {
    const resultArray = [
      [await getSearchMetersMeterSearchGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/meter/:meterRef/mpan`, async () => {
    const resultArray = [
      [
        await getGetMpanFromMeterRefMeterMeterRefMpanGet200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/auth/token-form`, async () => {
    const resultArray = [
      [
        await getLoginForAccessTokenFormAuthTokenFormPost200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/auth/token`, async () => {
    const resultArray = [
      [await getLoginForAccessTokenAuthTokenPost200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/auth/users/me/`, async () => {
    const resultArray = [
      [await getReadUsersMeAuthUsersMeGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/data/reports`, async () => {
    const resultArray = [
      [await getRecordReadingsDataReportsPost200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/healthz`, async () => {
    const resultArray = [
      [await getHealthzHealthzGet200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getGetAssetsEntitiesAssetGet200Response() {
  return {
    assets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      external_id: faker.lorem.words(),
      type: faker.helpers.arrayElement([
        'boundary meter',
        'charger',
        'battery',
        'heat pump',
        'hot water tank',
        'frequency meter',
      ]),
      site_id: faker.string.uuid(),
    })),
    total_num_assets: faker.number.int(),
    total_num_pages: faker.number.int(),
    page_number: faker.number.int(),
  };
}

export function getCreateAssetEntitiesAssetPost200Response() {
  return {
    id: faker.string.uuid(),
    external_id: faker.lorem.words(),
    type: faker.helpers.arrayElement([
      'boundary meter',
      'charger',
      'battery',
      'heat pump',
      'hot water tank',
      'frequency meter',
    ]),
    site_id: faker.string.uuid(),
  };
}

export function getGetAssetEntitiesAssetAssetIdGet200Response() {
  return {
    id: faker.string.uuid(),
    external_id: faker.lorem.words(),
    type: faker.helpers.arrayElement([
      'boundary meter',
      'charger',
      'battery',
      'heat pump',
      'hot water tank',
      'frequency meter',
    ]),
    site_id: faker.string.uuid(),
  };
}

export function getGetAssetByExternalIdEntitiesAssetExternalIdExternalIdGet200Response() {
  return {
    id: faker.string.uuid(),
    external_id: faker.lorem.words(),
    type: faker.helpers.arrayElement([
      'boundary meter',
      'charger',
      'battery',
      'heat pump',
      'hot water tank',
      'frequency meter',
    ]),
    site_id: faker.string.uuid(),
  };
}

export function getEventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost200Response() {
  return null;
}

export function getHandleAssetEventEntitiesAssetAssetIdEventPost200Response() {
  return null;
}

export function getGetSitesEntitiesSiteGet200Response() {
  return {
    sites: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      mpan: faker.lorem.words(),
      postcode: faker.lorem.words(),
      address: faker.lorem.words(),
      asset_ids: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      markets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) =>
        faker.helpers.arrayElement([
          'cm',
          'dfs',
          'dno',
          'lcm',
          'sffr',
          'wholesale',
          'dno-no-dispatch',
        ])
      ),
      tariff: {
        tariff_type: faker.helpers.arrayElement([
          'single_rate',
          'dual_rate',
          'dynamic',
          'smart',
          'unknown',
        ]),
        tariff_cheap_start_time: faker.date.past(),
        tariff_cheap_end_time: faker.date.past(),
      },
    })),
    total_num_sites: faker.number.int(),
    total_num_pages: faker.number.int(),
    page_number: faker.number.int(),
  };
}

export function getCreateSiteEntitiesSitePost200Response() {
  return {
    id: faker.string.uuid(),
    mpan: faker.lorem.words(),
    postcode: faker.lorem.words(),
    address: faker.lorem.words(),
    asset_ids: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.string.uuid()),
    markets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        'cm',
        'dfs',
        'dno',
        'lcm',
        'sffr',
        'wholesale',
        'dno-no-dispatch',
      ])
    ),
    tariff: {
      tariff_type: faker.helpers.arrayElement([
        'single_rate',
        'dual_rate',
        'dynamic',
        'smart',
        'unknown',
      ]),
      tariff_cheap_start_time: faker.date.past(),
      tariff_cheap_end_time: faker.date.past(),
    },
  };
}

export function getGetSiteEntitiesSiteSiteIdGet200Response() {
  return {
    id: faker.string.uuid(),
    mpan: faker.lorem.words(),
    postcode: faker.lorem.words(),
    address: faker.lorem.words(),
    asset_ids: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.string.uuid()),
    markets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        'cm',
        'dfs',
        'dno',
        'lcm',
        'sffr',
        'wholesale',
        'dno-no-dispatch',
      ])
    ),
    tariff: {
      tariff_type: faker.helpers.arrayElement([
        'single_rate',
        'dual_rate',
        'dynamic',
        'smart',
        'unknown',
      ]),
      tariff_cheap_start_time: faker.date.past(),
      tariff_cheap_end_time: faker.date.past(),
    },
  };
}

export function getUpdateSiteEntitiesSiteSiteIdPatch200Response() {
  return {
    id: faker.string.uuid(),
    mpan: faker.lorem.words(),
    postcode: faker.lorem.words(),
    address: faker.lorem.words(),
    asset_ids: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.string.uuid()),
    markets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        'cm',
        'dfs',
        'dno',
        'lcm',
        'sffr',
        'wholesale',
        'dno-no-dispatch',
      ])
    ),
    tariff: {
      tariff_type: faker.helpers.arrayElement([
        'single_rate',
        'dual_rate',
        'dynamic',
        'smart',
        'unknown',
      ]),
      tariff_cheap_start_time: faker.date.past(),
      tariff_cheap_end_time: faker.date.past(),
    },
  };
}

export function getAddSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost200Response() {
  return {
    id: faker.string.uuid(),
    mpan: faker.lorem.words(),
    postcode: faker.lorem.words(),
    address: faker.lorem.words(),
    asset_ids: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.string.uuid()),
    markets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        'cm',
        'dfs',
        'dno',
        'lcm',
        'sffr',
        'wholesale',
        'dno-no-dispatch',
      ])
    ),
    tariff: {
      tariff_type: faker.helpers.arrayElement([
        'single_rate',
        'dual_rate',
        'dynamic',
        'smart',
        'unknown',
      ]),
      tariff_cheap_start_time: faker.date.past(),
      tariff_cheap_end_time: faker.date.past(),
    },
  };
}

export function getRemoveSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost200Response() {
  return {
    id: faker.string.uuid(),
    mpan: faker.lorem.words(),
    postcode: faker.lorem.words(),
    address: faker.lorem.words(),
    asset_ids: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.string.uuid()),
    markets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        'cm',
        'dfs',
        'dno',
        'lcm',
        'sffr',
        'wholesale',
        'dno-no-dispatch',
      ])
    ),
    tariff: {
      tariff_type: faker.helpers.arrayElement([
        'single_rate',
        'dual_rate',
        'dynamic',
        'smart',
        'unknown',
      ]),
      tariff_cheap_start_time: faker.date.past(),
      tariff_cheap_end_time: faker.date.past(),
    },
  };
}

export function getCheckPostcodeQualificationQualificationDnoPostcodeGet200Response() {
  return {
    eligible: faker.datatype.boolean(),
    procurer_name: faker.helpers.arrayElement([
      'UKPN',
      'NGED',
      'SSEN',
      'SPEN',
      'NPG',
      'ENW',
    ]),
    next_competition: {
      start_date: faker.lorem.words(),
      end_date: faker.lorem.words(),
      max_total_revenue_per_kw: faker.number.int(),
      expected_value_per_kw_per_month: faker.number.int(),
    },
  };
}

export function getFlexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut200Response() {
  return null;
}

export function getElectronConnectDispatchApiElectronConnectDispatchPost200Response() {
  return null;
}

export function getGetFrequencyFrequencyHistoricGet200Response() {
  return [...new Array(5).keys()]
    .map((_) => ({ [faker.lorem.word()]: faker.number.int() }))
    .reduce((acc, next) => Object.assign(acc, next), {});
}

export function getSearchMetersMeterSearchGet200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    ref: faker.lorem.words(),
    address: faker.lorem.words(),
  }));
}

export function getGetMpanFromMeterRefMeterMeterRefMpanGet200Response() {
  return {
    mpan: faker.lorem.words(),
    half_hourly_settled: true,
    supplier_mpid: faker.lorem.words(),
    supplier_name: faker.person.fullName(),
  };
}

export function getLoginForAccessTokenFormAuthTokenFormPost200Response() {
  return {
    access_token: faker.lorem.words(),
    token_type: faker.lorem.words(),
  };
}

export function getLoginForAccessTokenAuthTokenPost200Response() {
  return {
    access_token: faker.lorem.words(),
    token_type: faker.lorem.words(),
  };
}

export function getReadUsersMeAuthUsersMeGet200Response() {
  return {
    username: faker.person.fullName(),
    email: faker.internet.email(),
    full_name: faker.person.fullName(),
    disabled: faker.datatype.boolean(),
  };
}

export function getRecordReadingsDataReportsPost200Response() {
  return {
    charger_consumption_data_response: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      asset_id: faker.string.uuid(),
      accepted: faker.datatype.boolean(),
      error: faker.lorem.words(),
    })),
  };
}

export function getHealthzHealthzGet200Response() {
  return faker.lorem.words();
}
