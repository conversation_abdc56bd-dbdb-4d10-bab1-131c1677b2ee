/* tslint:disable */
/* eslint-disable */
/**
 * provisioning-api
 * An API for provisioning assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AddTestResultForUnprovisionedChargingStation200Response
 */
export interface AddTestResultForUnprovisionedChargingStation200Response {
  /**
   *
   * @type {string}
   * @memberof AddTestResultForUnprovisionedChargingStation200Response
   */
  message: AddTestResultForUnprovisionedChargingStation200ResponseMessageEnum;
}

export const AddTestResultForUnprovisionedChargingStation200ResponseMessageEnum =
  {
    Success: 'success',
  } as const;

export type AddTestResultForUnprovisionedChargingStation200ResponseMessageEnum =
  (typeof AddTestResultForUnprovisionedChargingStation200ResponseMessageEnum)[keyof typeof AddTestResultForUnprovisionedChargingStation200ResponseMessageEnum];

/**
 *
 * @export
 * @interface AttachPcbRequest
 */
export interface AttachPcbRequest {
  /**
   * The serial number of an EVSE (aka PCB)
   * @type {string}
   * @memberof AttachPcbRequest
   */
  serialNumber: string;
  /**
   *
   * @type {Door}
   * @memberof AttachPcbRequest
   */
  door?: Door;
}

/**
 *
 * @export
 * @interface ChargingStationStatusResponse
 */
export interface ChargingStationStatusResponse {
  /**
   *
   * @type {string}
   * @memberof ChargingStationStatusResponse
   */
  status?: ChargingStationStatusResponseStatusEnum;
}

export const ChargingStationStatusResponseStatusEnum = {
  Online: 'online',
  Offline: 'offline',
} as const;

export type ChargingStationStatusResponseStatusEnum =
  (typeof ChargingStationStatusResponseStatusEnum)[keyof typeof ChargingStationStatusResponseStatusEnum];

/**
 *
 * @export
 * @enum {string}
 */

export const ConfigurationStatus = {
  Accepted: 'Accepted',
  Requested: 'Requested',
  Error: 'Error',
} as const;

export type ConfigurationStatus =
  (typeof ConfigurationStatus)[keyof typeof ConfigurationStatus];

/**
 *
 * @export
 * @interface ConfigurationStatusResponse
 */
export interface ConfigurationStatusResponse {
  /**
   *
   * @type {ConfigurationStatus}
   * @memberof ConfigurationStatusResponse
   */
  status?: ConfigurationStatus;
}

/**
 *
 * @export
 * @interface CreateAPcb201Response
 */
export interface CreateAPcb201Response {
  /**
   * The serial number of an EVSE (aka PCB)
   * @type {string}
   * @memberof CreateAPcb201Response
   */
  serialNumber: string;
  /**
   *
   * @type {string}
   * @memberof CreateAPcb201Response
   */
  pcbType: string;
}
/**
 *
 * @export
 * @interface CreateChargingStation
 */
export interface CreateChargingStation {
  /**
   *
   * @type {string}
   * @memberof CreateChargingStation
   */
  serialNumber: string;
  /**
   *
   * @type {string}
   * @memberof CreateChargingStation
   */
  sku: string;
  /**
   *
   * @type {string}
   * @memberof CreateChargingStation
   */
  ppid: string;
}
/**
 *
 * @export
 * @interface CreatePcbRequestType
 */
export interface CreatePcbRequestType {
  /**
   * The serial number of an EVSE (aka PCB)
   * @type {string}
   * @memberof CreatePcbRequestType
   */
  serialNumber: string;
  /**
   *
   * @type {string}
   * @memberof CreatePcbRequestType
   */
  pcbType: string;
}
/**
 *
 * @export
 * @interface CreateUnprovisionedChargingStation201Response
 */
export interface CreateUnprovisionedChargingStation201Response {
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStation201Response
   */
  id: string;
}
/**
 *
 * @export
 * @interface CreateUnprovisionedChargingStationRequest
 */
export interface CreateUnprovisionedChargingStationRequest {
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStationRequest
   */
  ppid: string;
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStationRequest
   */
  sku: string;
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStationRequest
   */
  serialNumberDoorA: string;
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStationRequest
   */
  serialNumberDoorB?: string;
  /**
   *
   * @type {string}
   * @memberof CreateUnprovisionedChargingStationRequest
   */
  routerSim?: string;
}
/**
 * The door of a charging station
 * @export
 * @enum {string}
 */

export const Door = {
  A: 'A',
  B: 'B',
} as const;

export type Door = (typeof Door)[keyof typeof Door];

/**
 *
 * @export
 * @interface FctEvent
 */
export interface FctEvent {
  /**
   * The type of event
   * @type {string}
   * @memberof FctEvent
   */
  type?: FctEventTypeEnum;
  /**
   * Any metadata associated with the event
   * @type {object}
   * @memberof FctEvent
   */
  data?: object;
}

export const FctEventTypeEnum = {
  FctStarted: 'fctStarted',
  FctFailed: 'fctFailed',
  FctCompleted: 'fctCompleted',
} as const;

export type FctEventTypeEnum =
  (typeof FctEventTypeEnum)[keyof typeof FctEventTypeEnum];

/**
 *
 * @export
 * @interface GenerateWifiCredentials200Response
 */
export interface GenerateWifiCredentials200Response {
  /**
   *
   * @type {string}
   * @memberof GenerateWifiCredentials200Response
   */
  url?: string;
  /**
   *
   * @type {string}
   * @memberof GenerateWifiCredentials200Response
   */
  key?: string;
}
/**
 *
 * @export
 * @interface GetPcbEventsResponseInner
 */
export interface GetPcbEventsResponseInner {
  /**
   * Event metadata
   * @type {object}
   * @memberof GetPcbEventsResponseInner
   */
  data?: object;
  /**
   * The serial number of the PCBA
   * @type {string}
   * @memberof GetPcbEventsResponseInner
   */
  serialNumber?: string;
  /**
   * The type of the event
   * @type {string}
   * @memberof GetPcbEventsResponseInner
   */
  type?: string;
  /**
   * The unique UUID for that event
   * @type {string}
   * @memberof GetPcbEventsResponseInner
   */
  uuid?: string;
}
/**
 *
 * @export
 * @interface GetPcbSwaps200Response
 */
export interface GetPcbSwaps200Response {
  /**
   *
   * @type {Array<PcbSwapType>}
   * @memberof GetPcbSwaps200Response
   */
  data?: Array<PcbSwapType>;
}
/**
 *
 * @export
 * @interface GetProvisioningRequirementsSku200Response
 */
export interface GetProvisioningRequirementsSku200Response {
  /**
   *
   * @type {Array<ModelProvisioningRequirements>}
   * @memberof GetProvisioningRequirementsSku200Response
   */
  data?: Array<ModelProvisioningRequirements>;
}
/**
 *
 * @export
 * @interface GetUnprovisionedChargingStationConfiguration200Response
 */
export interface GetUnprovisionedChargingStationConfiguration200Response {
  /**
   *
   * @type {SetRequestStatus}
   * @memberof GetUnprovisionedChargingStationConfiguration200Response
   */
  serialNumberDoorA?: SetRequestStatus;
  /**
   *
   * @type {SetRequestStatus}
   * @memberof GetUnprovisionedChargingStationConfiguration200Response
   */
  serialNumberDoorB?: SetRequestStatus;
}

/**
 *
 * @export
 * @interface GetUnprovisionedChargingStationStatus200Response
 */
export interface GetUnprovisionedChargingStationStatus200Response {
  /**
   *
   * @type {string}
   * @memberof GetUnprovisionedChargingStationStatus200Response
   */
  status?: GetUnprovisionedChargingStationStatus200ResponseStatusEnum;
}

export const GetUnprovisionedChargingStationStatus200ResponseStatusEnum = {
  Online: 'online',
  Offline: 'offline',
} as const;

export type GetUnprovisionedChargingStationStatus200ResponseStatusEnum =
  (typeof GetUnprovisionedChargingStationStatus200ResponseStatusEnum)[keyof typeof GetUnprovisionedChargingStationStatus200ResponseStatusEnum];

/**
 *
 * @export
 * @interface GetWifiCredentials200Response
 */
export interface GetWifiCredentials200Response {
  /**
   *
   * @type {string}
   * @memberof GetWifiCredentials200Response
   */
  ppid?: string;
  /**
   *
   * @type {string}
   * @memberof GetWifiCredentials200Response
   */
  ssid?: string;
  /**
   *
   * @type {string}
   * @memberof GetWifiCredentials200Response
   */
  password?: string;
  /**
   *
   * @type {GetWifiCredentials200ResponseTwinSsid}
   * @memberof GetWifiCredentials200Response
   */
  twinSsid?: GetWifiCredentials200ResponseTwinSsid;
}
/**
 *
 * @export
 * @interface GetWifiCredentials200ResponseTwinSsid
 */
export interface GetWifiCredentials200ResponseTwinSsid {
  /**
   *
   * @type {string}
   * @memberof GetWifiCredentials200ResponseTwinSsid
   */
  doorA?: string;
  /**
   *
   * @type {string}
   * @memberof GetWifiCredentials200ResponseTwinSsid
   */
  doorB?: string;
}
/**
 *
 * @export
 * @interface ModelProvisioningRequirements
 */
export interface ModelProvisioningRequirements {
  /**
   *
   * @type {number}
   * @memberof ModelProvisioningRequirements
   */
  evseCount: number;
  /**
   *
   * @type {boolean}
   * @memberof ModelProvisioningRequirements
   */
  rfidAtManufacture: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ModelProvisioningRequirements
   */
  routerAtManufacture: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ModelProvisioningRequirements
   */
  midMeterAtManufacture: boolean;
}
/**
 *
 * @export
 * @interface PcbProvisioningInfo
 */
export interface PcbProvisioningInfo {
  /**
   * The identity of the FCT box used to provision the PCB
   * @type {string}
   * @memberof PcbProvisioningInfo
   */
  provisionedBy?: string;
  /**
   * The date and time the PCB was (re)-provisioned
   * @type {string}
   * @memberof PcbProvisioningInfo
   */
  provisionedAt?: string;
  /**
   * JSON data provided by the FCT box at the time it is provisioned
   * @type {{ [key: string]: any; }}
   * @memberof PcbProvisioningInfo
   */
  provisionedData?: { [key: string]: any };
  /**
   * A MAC (Media Access Control) address, sometimes referred to as a hardware or physical address, is a unique, 12-character alphanumeric attribute that is used to identify individual electronic devices on a network. Each PodPoint EVSE (or PCB) has a unique MAC address
   * @type {string}
   * @memberof PcbProvisioningInfo
   */
  macAddress?: string;
}
/**
 *
 * @export
 * @interface PcbSwapRequestType
 */
export interface PcbSwapRequestType {
  /**
   * The serial number of the new PCB you want to swap into the charging station.
   * @type {string}
   * @memberof PcbSwapRequestType
   */
  serialNumber: string;
  /**
   * The PPID of the existing charging station the PCB is being swapped into.
   * @type {string}
   * @memberof PcbSwapRequestType
   */
  ppid: string;
  /**
   *
   * @type {Door}
   * @memberof PcbSwapRequestType
   */
  door?: Door;
}

/**
 *
 * @export
 * @enum {string}
 */

export const PcbSwapStatus = {
  Success: 'Success',
  InProgress: 'In Progress',
  Failed: 'Failed',
  PartiallySuccessful: 'Partially Successful',
} as const;

export type PcbSwapStatus = (typeof PcbSwapStatus)[keyof typeof PcbSwapStatus];

/**
 *
 * @export
 * @interface PcbSwapType
 */
export interface PcbSwapType {
  /**
   * The swap id of the PCB swap
   * @type {string}
   * @memberof PcbSwapType
   */
  id: string;
  /**
   * The PPID of the existing charging station the PCB is being swapped into.
   * @type {string}
   * @memberof PcbSwapType
   */
  ppid: string;
  /**
   * The serial number of the new PCB you want to swap into the charging station.
   * @type {string}
   * @memberof PcbSwapType
   */
  serialNumber: string;
  /**
   *
   * @type {Door}
   * @memberof PcbSwapType
   */
  door: Door;
  /**
   *
   * @type {PcbSwapStatus}
   * @memberof PcbSwapType
   */
  status: PcbSwapStatus;
  /**
   * The error message if the status is \'Failed\'.
   * @type {string}
   * @memberof PcbSwapType
   */
  errorCode?: string;
  /**
   *
   * @type {string}
   * @memberof PcbSwapType
   */
  timestamp: string;
}

/**
 *
 * @export
 * @interface SetConfigurationRequest
 */
export interface SetConfigurationRequest {
  /**
   *
   * @type {string}
   * @memberof SetConfigurationRequest
   */
  clientRef: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export const SetRequestStatus = {
  Accepted: 'Accepted',
  Requested: 'Requested',
  Error: 'Error',
} as const;

export type SetRequestStatus =
  (typeof SetRequestStatus)[keyof typeof SetRequestStatus];

/**
 *
 * @export
 * @interface SwapAPcb202Response
 */
export interface SwapAPcb202Response {
  /**
   *
   * @type {string}
   * @memberof SwapAPcb202Response
   */
  id?: string;
}
/**
 *
 * @export
 * @interface SwapPcbStatus200Response
 */
export interface SwapPcbStatus200Response {
  /**
   *
   * @type {PcbSwapStatus}
   * @memberof SwapPcbStatus200Response
   */
  status?: PcbSwapStatus;
  /**
   * The error message if the status is \'Failed\'.
   * @type {string}
   * @memberof SwapPcbStatus200Response
   */
  errorCode?: string;
}

/**
 *
 * @export
 * @interface TestResultRequest
 */
export interface TestResultRequest {
  /**
   *
   * @type {string}
   * @memberof TestResultRequest
   */
  result: TestResultRequestResultEnum;
}

export const TestResultRequestResultEnum = {
  Pass: 'pass',
  Fail: 'fail',
} as const;

export type TestResultRequestResultEnum =
  (typeof TestResultRequestResultEnum)[keyof typeof TestResultRequestResultEnum];

/**
 *
 * @export
 * @interface WifiCredentialsRequest
 */
export interface WifiCredentialsRequest {
  /**
   *
   * @type {number}
   * @memberof WifiCredentialsRequest
   */
  amount: number;
  /**
   *
   * @type {string}
   * @memberof WifiCredentialsRequest
   */
  prefix?: WifiCredentialsRequestPrefixEnum;
}

export const WifiCredentialsRequestPrefixEnum = {
  Pg: 'PG',
  Psl: 'PSL',
} as const;

export type WifiCredentialsRequestPrefixEnum =
  (typeof WifiCredentialsRequestPrefixEnum)[keyof typeof WifiCredentialsRequestPrefixEnum];

/**
 * AttachDetachTestHelpersApi - axios parameter creator
 * @export
 */
export const AttachDetachTestHelpersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Attach a PCB to a unit in podadmin.
     * @summary Attach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {AttachPcbRequest} [attachPcbRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    attachAPcb: async (
      ppid: string,
      attachPcbRequest?: AttachPcbRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('attachAPcb', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/pcbs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        attachPcbRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Detach a PCB from a unit in podadmin.
     * @summary Detach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    detachAPcb: async (
      ppid: string,
      serialNumber: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('detachAPcb', 'ppid', ppid);
      // verify required parameter 'serialNumber' is not null or undefined
      assertParamExists('detachAPcb', 'serialNumber', serialNumber);
      const localVarPath =
        `/charging-stations/{ppid}/pcbs/{serialNumber}/detach`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(
            `{${'serialNumber'}}`,
            encodeURIComponent(String(serialNumber))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AttachDetachTestHelpersApi - functional programming interface
 * @export
 */
export const AttachDetachTestHelpersApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    AttachDetachTestHelpersApiAxiosParamCreator(configuration);
  return {
    /**
     * Attach a PCB to a unit in podadmin.
     * @summary Attach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {AttachPcbRequest} [attachPcbRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async attachAPcb(
      ppid: string,
      attachPcbRequest?: AttachPcbRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.attachAPcb(
        ppid,
        attachPcbRequest,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AttachDetachTestHelpersApi.attachAPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Detach a PCB from a unit in podadmin.
     * @summary Detach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async detachAPcb(
      ppid: string,
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.detachAPcb(
        ppid,
        serialNumber,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AttachDetachTestHelpersApi.detachAPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AttachDetachTestHelpersApi - factory interface
 * @export
 */
export const AttachDetachTestHelpersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AttachDetachTestHelpersApiFp(configuration);
  return {
    /**
     * Attach a PCB to a unit in podadmin.
     * @summary Attach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {AttachPcbRequest} [attachPcbRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    attachAPcb(
      ppid: string,
      attachPcbRequest?: AttachPcbRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .attachAPcb(ppid, attachPcbRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Detach a PCB from a unit in podadmin.
     * @summary Detach a PCB
     * @param {string} ppid The PPID of the charging station
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    detachAPcb(
      ppid: string,
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .detachAPcb(ppid, serialNumber, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AttachDetachTestHelpersApi - object-oriented interface
 * @export
 * @class AttachDetachTestHelpersApi
 * @extends {BaseAPI}
 */
export class AttachDetachTestHelpersApi extends BaseAPI {
  /**
   * Attach a PCB to a unit in podadmin.
   * @summary Attach a PCB
   * @param {string} ppid The PPID of the charging station
   * @param {AttachPcbRequest} [attachPcbRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AttachDetachTestHelpersApi
   */
  public attachAPcb(
    ppid: string,
    attachPcbRequest?: AttachPcbRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AttachDetachTestHelpersApiFp(this.configuration)
      .attachAPcb(ppid, attachPcbRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Detach a PCB from a unit in podadmin.
   * @summary Detach a PCB
   * @param {string} ppid The PPID of the charging station
   * @param {string} serialNumber The serial number of the PCB
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AttachDetachTestHelpersApi
   */
  public detachAPcb(
    ppid: string,
    serialNumber: string,
    options?: RawAxiosRequestConfig
  ) {
    return AttachDetachTestHelpersApiFp(this.configuration)
      .detachAPcb(ppid, serialNumber, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargingStationApi - axios parameter creator
 * @export
 */
export const ChargingStationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Add a test result for a charging station identified by its PPID.
     * @summary Add Test Result
     * @param {string} ppid The PPID of the charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addTestResult: async (
      ppid: string,
      testResultRequest?: TestResultRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('addTestResult', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/test-result`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        testResultRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used to create a new charging station.
     * @summary Create a Charging Station
     * @param {CreateChargingStation} [createChargingStation]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStation: async (
      createChargingStation?: CreateChargingStation,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/charging-stations`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createChargingStation,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the online/offline status of a charging station by its PPID.
     * @summary Get Charging Station Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationStatus: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationStatus', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/status`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the configuration status of a charging station identified by its PPID.
     * @summary Get Configuration Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getConfigurationStatus: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getConfigurationStatus', 'ppid', ppid);
      const localVarPath =
        `/charging-stations/{ppid}/configuration/status`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Initiate provisioning for a charging station identified by its PPID.
     * @summary Provision Charging Station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('provisionChargingStation', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/provision`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set configuration for a charging station identified by its PPID.
     * @summary Set Configuration
     * @param {string} ppid The PPID of the charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setConfiguration: async (
      ppid: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('setConfiguration', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/configuration`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setConfigurationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingStationApi - functional programming interface
 * @export
 */
export const ChargingStationApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargingStationApiAxiosParamCreator(configuration);
  return {
    /**
     * Add a test result for a charging station identified by its PPID.
     * @summary Add Test Result
     * @param {string} ppid The PPID of the charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async addTestResult(
      ppid: string,
      testResultRequest?: TestResultRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.addTestResult(
        ppid,
        testResultRequest,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.addTestResult']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used to create a new charging station.
     * @summary Create a Charging Station
     * @param {CreateChargingStation} [createChargingStation]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createChargingStation(
      createChargingStation?: CreateChargingStation,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createChargingStation(
          createChargingStation,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.createChargingStation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the online/offline status of a charging station by its PPID.
     * @summary Get Charging Station Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargingStationStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationStatus(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.getChargingStationStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the configuration status of a charging station identified by its PPID.
     * @summary Get Configuration Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getConfigurationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ConfigurationStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getConfigurationStatus(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.getConfigurationStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Initiate provisioning for a charging station identified by its PPID.
     * @summary Provision Charging Station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async provisionChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.provisionChargingStation(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.provisionChargingStation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Set configuration for a charging station identified by its PPID.
     * @summary Set Configuration
     * @param {string} ppid The PPID of the charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setConfiguration(
      ppid: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setConfiguration(
          ppid,
          setConfigurationRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationApi.setConfiguration']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingStationApi - factory interface
 * @export
 */
export const ChargingStationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingStationApiFp(configuration);
  return {
    /**
     * Add a test result for a charging station identified by its PPID.
     * @summary Add Test Result
     * @param {string} ppid The PPID of the charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addTestResult(
      ppid: string,
      testResultRequest?: TestResultRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .addTestResult(ppid, testResultRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used to create a new charging station.
     * @summary Create a Charging Station
     * @param {CreateChargingStation} [createChargingStation]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStation(
      createChargingStation?: CreateChargingStation,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .createChargingStation(createChargingStation, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the online/offline status of a charging station by its PPID.
     * @summary Get Charging Station Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargingStationStatusResponse> {
      return localVarFp
        .getChargingStationStatus(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the configuration status of a charging station identified by its PPID.
     * @summary Get Configuration Status
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getConfigurationStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ConfigurationStatusResponse> {
      return localVarFp
        .getConfigurationStatus(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Initiate provisioning for a charging station identified by its PPID.
     * @summary Provision Charging Station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .provisionChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set configuration for a charging station identified by its PPID.
     * @summary Set Configuration
     * @param {string} ppid The PPID of the charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setConfiguration(
      ppid: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .setConfiguration(ppid, setConfigurationRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingStationApi - object-oriented interface
 * @export
 * @class ChargingStationApi
 * @extends {BaseAPI}
 */
export class ChargingStationApi extends BaseAPI {
  /**
   * Add a test result for a charging station identified by its PPID.
   * @summary Add Test Result
   * @param {string} ppid The PPID of the charging station
   * @param {TestResultRequest} [testResultRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public addTestResult(
    ppid: string,
    testResultRequest?: TestResultRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationApiFp(this.configuration)
      .addTestResult(ppid, testResultRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used to create a new charging station.
   * @summary Create a Charging Station
   * @param {CreateChargingStation} [createChargingStation]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public createChargingStation(
    createChargingStation?: CreateChargingStation,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationApiFp(this.configuration)
      .createChargingStation(createChargingStation, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the online/offline status of a charging station by its PPID.
   * @summary Get Charging Station Status
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public getChargingStationStatus(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationApiFp(this.configuration)
      .getChargingStationStatus(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the configuration status of a charging station identified by its PPID.
   * @summary Get Configuration Status
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public getConfigurationStatus(ppid: string, options?: RawAxiosRequestConfig) {
    return ChargingStationApiFp(this.configuration)
      .getConfigurationStatus(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Initiate provisioning for a charging station identified by its PPID.
   * @summary Provision Charging Station
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public provisionChargingStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationApiFp(this.configuration)
      .provisionChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set configuration for a charging station identified by its PPID.
   * @summary Set Configuration
   * @param {string} ppid The PPID of the charging station
   * @param {SetConfigurationRequest} [setConfigurationRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationApi
   */
  public setConfiguration(
    ppid: string,
    setConfigurationRequest?: SetConfigurationRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationApiFp(this.configuration)
      .setConfiguration(ppid, setConfigurationRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ModelApi - axios parameter creator
 * @export
 */
export const ModelApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Used to get the provisioning requirements for the SKU
     * @summary Gets provisioning requirements for the SKU
     * @param {string} sku sku from an existing unit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProvisioningRequirementsSku: async (
      sku: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sku' is not null or undefined
      assertParamExists('getProvisioningRequirementsSku', 'sku', sku);
      const localVarPath = `/models/{sku}`.replace(
        `{${'sku'}}`,
        encodeURIComponent(String(sku))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ModelApi - functional programming interface
 * @export
 */
export const ModelApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ModelApiAxiosParamCreator(configuration);
  return {
    /**
     * Used to get the provisioning requirements for the SKU
     * @summary Gets provisioning requirements for the SKU
     * @param {string} sku sku from an existing unit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProvisioningRequirementsSku(
      sku: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetProvisioningRequirementsSku200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getProvisioningRequirementsSku(
          sku,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ModelApi.getProvisioningRequirementsSku']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ModelApi - factory interface
 * @export
 */
export const ModelApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ModelApiFp(configuration);
  return {
    /**
     * Used to get the provisioning requirements for the SKU
     * @summary Gets provisioning requirements for the SKU
     * @param {string} sku sku from an existing unit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProvisioningRequirementsSku(
      sku: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetProvisioningRequirementsSku200Response> {
      return localVarFp
        .getProvisioningRequirementsSku(sku, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ModelApi - object-oriented interface
 * @export
 * @class ModelApi
 * @extends {BaseAPI}
 */
export class ModelApi extends BaseAPI {
  /**
   * Used to get the provisioning requirements for the SKU
   * @summary Gets provisioning requirements for the SKU
   * @param {string} sku sku from an existing unit
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ModelApi
   */
  public getProvisioningRequirementsSku(
    sku: string,
    options?: RawAxiosRequestConfig
  ) {
    return ModelApiFp(this.configuration)
      .getProvisioningRequirementsSku(sku, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PcbApi - axios parameter creator
 * @export
 */
export const PcbApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Used to record that a pcb exists, returns that firmware version that should be used during provisioning.
     * @summary Create a PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {CreatePcbRequestType} [createPcbRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createAPcb: async (
      certificateParsedChainJson: string,
      createPcbRequestType?: CreatePcbRequestType,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'certificateParsedChainJson' is not null or undefined
      assertParamExists(
        'createAPcb',
        'certificateParsedChainJson',
        certificateParsedChainJson
      );
      const localVarPath = `/pcbs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (certificateParsedChainJson != null) {
        localVarHeaderParameter['Certificate-Parsed-Chain-Json'] = String(
          certificateParsedChainJson
        );
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createPcbRequestType,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used by our FCT boxes to retrieve details of any previous FCT runs
     * @summary Retrieve events for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPcbEvents: async (
      serialNumber: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'serialNumber' is not null or undefined
      assertParamExists('getPcbEvents', 'serialNumber', serialNumber);
      const localVarPath = `/pcbs/{serialNumber}/events`.replace(
        `{${'serialNumber'}}`,
        encodeURIComponent(String(serialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used by our FCT boxes to retrieve passwords for provisioned PCBs when re-flashing after a test failure
     * @summary Retrieve the provisionedData for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProvisionedData: async (
      serialNumber: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'serialNumber' is not null or undefined
      assertParamExists('getProvisionedData', 'serialNumber', serialNumber);
      const localVarPath = `/pcbs/{serialNumber}/provisionedData`.replace(
        `{${'serialNumber'}}`,
        encodeURIComponent(String(serialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used by our FCT boxes to track progress through FCT process
     * @summary Post an FCT event for the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {FctEvent} [fctEvent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postFctEvent: async (
      serialNumber: string,
      fctEvent?: FctEvent,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'serialNumber' is not null or undefined
      assertParamExists('postFctEvent', 'serialNumber', serialNumber);
      const localVarPath = `/pcbs/{serialNumber}/fctEvent`.replace(
        `{${'serialNumber'}}`,
        encodeURIComponent(String(serialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        fctEvent,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used by our FCT boxes to mark a PCB as Provisioned (supplies provisioning-info)
     * @summary Provision the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {PcbProvisioningInfo} [pcbProvisioningInfo]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionAPcb: async (
      serialNumber: string,
      certificateParsedChainJson: string,
      pcbProvisioningInfo?: PcbProvisioningInfo,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'serialNumber' is not null or undefined
      assertParamExists('provisionAPcb', 'serialNumber', serialNumber);
      // verify required parameter 'certificateParsedChainJson' is not null or undefined
      assertParamExists(
        'provisionAPcb',
        'certificateParsedChainJson',
        certificateParsedChainJson
      );
      const localVarPath = `/pcbs/{serialNumber}/provision`.replace(
        `{${'serialNumber'}}`,
        encodeURIComponent(String(serialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (certificateParsedChainJson != null) {
        localVarHeaderParameter['Certificate-Parsed-Chain-Json'] = String(
          certificateParsedChainJson
        );
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        pcbProvisioningInfo,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PcbApi - functional programming interface
 * @export
 */
export const PcbApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PcbApiAxiosParamCreator(configuration);
  return {
    /**
     * Used to record that a pcb exists, returns that firmware version that should be used during provisioning.
     * @summary Create a PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {CreatePcbRequestType} [createPcbRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createAPcb(
      certificateParsedChainJson: string,
      createPcbRequestType?: CreatePcbRequestType,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateAPcb201Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.createAPcb(
        certificateParsedChainJson,
        createPcbRequestType,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbApi.createAPcb']?.[localVarOperationServerIndex]
          ?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used by our FCT boxes to retrieve details of any previous FCT runs
     * @summary Retrieve events for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getPcbEvents(
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<GetPcbEventsResponseInner>>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getPcbEvents(
        serialNumber,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbApi.getPcbEvents']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used by our FCT boxes to retrieve passwords for provisioned PCBs when re-flashing after a test failure
     * @summary Retrieve the provisionedData for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProvisionedData(
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<{ [key: string]: any }>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getProvisionedData(
          serialNumber,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbApi.getProvisionedData']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used by our FCT boxes to track progress through FCT process
     * @summary Post an FCT event for the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {FctEvent} [fctEvent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async postFctEvent(
      serialNumber: string,
      fctEvent?: FctEvent,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.postFctEvent(
        serialNumber,
        fctEvent,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbApi.postFctEvent']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used by our FCT boxes to mark a PCB as Provisioned (supplies provisioning-info)
     * @summary Provision the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {PcbProvisioningInfo} [pcbProvisioningInfo]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async provisionAPcb(
      serialNumber: string,
      certificateParsedChainJson: string,
      pcbProvisioningInfo?: PcbProvisioningInfo,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.provisionAPcb(
        serialNumber,
        certificateParsedChainJson,
        pcbProvisioningInfo,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbApi.provisionAPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PcbApi - factory interface
 * @export
 */
export const PcbApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PcbApiFp(configuration);
  return {
    /**
     * Used to record that a pcb exists, returns that firmware version that should be used during provisioning.
     * @summary Create a PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {CreatePcbRequestType} [createPcbRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createAPcb(
      certificateParsedChainJson: string,
      createPcbRequestType?: CreatePcbRequestType,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateAPcb201Response> {
      return localVarFp
        .createAPcb(certificateParsedChainJson, createPcbRequestType, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used by our FCT boxes to retrieve details of any previous FCT runs
     * @summary Retrieve events for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPcbEvents(
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<GetPcbEventsResponseInner>> {
      return localVarFp
        .getPcbEvents(serialNumber, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used by our FCT boxes to retrieve passwords for provisioned PCBs when re-flashing after a test failure
     * @summary Retrieve the provisionedData for a PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProvisionedData(
      serialNumber: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<{ [key: string]: any }> {
      return localVarFp
        .getProvisionedData(serialNumber, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used by our FCT boxes to track progress through FCT process
     * @summary Post an FCT event for the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {FctEvent} [fctEvent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postFctEvent(
      serialNumber: string,
      fctEvent?: FctEvent,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .postFctEvent(serialNumber, fctEvent, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used by our FCT boxes to mark a PCB as Provisioned (supplies provisioning-info)
     * @summary Provision the PCB
     * @param {string} serialNumber The serial number of the PCB
     * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
     * @param {PcbProvisioningInfo} [pcbProvisioningInfo]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionAPcb(
      serialNumber: string,
      certificateParsedChainJson: string,
      pcbProvisioningInfo?: PcbProvisioningInfo,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .provisionAPcb(
          serialNumber,
          certificateParsedChainJson,
          pcbProvisioningInfo,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PcbApi - object-oriented interface
 * @export
 * @class PcbApi
 * @extends {BaseAPI}
 */
export class PcbApi extends BaseAPI {
  /**
   * Used to record that a pcb exists, returns that firmware version that should be used during provisioning.
   * @summary Create a PCB
   * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
   * @param {CreatePcbRequestType} [createPcbRequestType]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbApi
   */
  public createAPcb(
    certificateParsedChainJson: string,
    createPcbRequestType?: CreatePcbRequestType,
    options?: RawAxiosRequestConfig
  ) {
    return PcbApiFp(this.configuration)
      .createAPcb(certificateParsedChainJson, createPcbRequestType, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used by our FCT boxes to retrieve details of any previous FCT runs
   * @summary Retrieve events for a PCB
   * @param {string} serialNumber The serial number of the PCB
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbApi
   */
  public getPcbEvents(serialNumber: string, options?: RawAxiosRequestConfig) {
    return PcbApiFp(this.configuration)
      .getPcbEvents(serialNumber, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used by our FCT boxes to retrieve passwords for provisioned PCBs when re-flashing after a test failure
   * @summary Retrieve the provisionedData for a PCB
   * @param {string} serialNumber The serial number of the PCB
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbApi
   */
  public getProvisionedData(
    serialNumber: string,
    options?: RawAxiosRequestConfig
  ) {
    return PcbApiFp(this.configuration)
      .getProvisionedData(serialNumber, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used by our FCT boxes to track progress through FCT process
   * @summary Post an FCT event for the PCB
   * @param {string} serialNumber The serial number of the PCB
   * @param {FctEvent} [fctEvent]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbApi
   */
  public postFctEvent(
    serialNumber: string,
    fctEvent?: FctEvent,
    options?: RawAxiosRequestConfig
  ) {
    return PcbApiFp(this.configuration)
      .postFctEvent(serialNumber, fctEvent, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used by our FCT boxes to mark a PCB as Provisioned (supplies provisioning-info)
   * @summary Provision the PCB
   * @param {string} serialNumber The serial number of the PCB
   * @param {string} certificateParsedChainJson A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
   * @param {PcbProvisioningInfo} [pcbProvisioningInfo]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbApi
   */
  public provisionAPcb(
    serialNumber: string,
    certificateParsedChainJson: string,
    pcbProvisioningInfo?: PcbProvisioningInfo,
    options?: RawAxiosRequestConfig
  ) {
    return PcbApiFp(this.configuration)
      .provisionAPcb(
        serialNumber,
        certificateParsedChainJson,
        pcbProvisioningInfo,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PcbSwapApi - axios parameter creator
 * @export
 */
export const PcbSwapApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Used to get the PCB swaps for a ppid
     * @summary Get PCB swaps
     * @param {string} ppid The PPID of a charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPcbSwaps: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getPcbSwaps', 'ppid', ppid);
      const localVarPath = `/pcb-swaps`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ppid !== undefined) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used to swap a PCB in a charging station
     * @summary Swap a PCB
     * @param {PcbSwapRequestType} [pcbSwapRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    swapAPcb: async (
      pcbSwapRequestType?: PcbSwapRequestType,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/pcb-swaps`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        pcbSwapRequestType,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used to get the status of a PCB swap
     * @summary Get status of a PCB swap
     * @param {string} swapId the swap id of the PCB swap
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    swapPcbStatus: async (
      swapId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'swapId' is not null or undefined
      assertParamExists('swapPcbStatus', 'swapId', swapId);
      const localVarPath = `/pcb-swaps/{swapId}`.replace(
        `{${'swapId'}}`,
        encodeURIComponent(String(swapId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PcbSwapApi - functional programming interface
 * @export
 */
export const PcbSwapApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PcbSwapApiAxiosParamCreator(configuration);
  return {
    /**
     * Used to get the PCB swaps for a ppid
     * @summary Get PCB swaps
     * @param {string} ppid The PPID of a charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getPcbSwaps(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetPcbSwaps200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getPcbSwaps(
        ppid,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbSwapApi.getPcbSwaps']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used to swap a PCB in a charging station
     * @summary Swap a PCB
     * @param {PcbSwapRequestType} [pcbSwapRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async swapAPcb(
      pcbSwapRequestType?: PcbSwapRequestType,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SwapAPcb202Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.swapAPcb(
        pcbSwapRequestType,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbSwapApi.swapAPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used to get the status of a PCB swap
     * @summary Get status of a PCB swap
     * @param {string} swapId the swap id of the PCB swap
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async swapPcbStatus(
      swapId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SwapPcbStatus200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.swapPcbStatus(
        swapId,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PcbSwapApi.swapPcbStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PcbSwapApi - factory interface
 * @export
 */
export const PcbSwapApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PcbSwapApiFp(configuration);
  return {
    /**
     * Used to get the PCB swaps for a ppid
     * @summary Get PCB swaps
     * @param {string} ppid The PPID of a charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPcbSwaps(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetPcbSwaps200Response> {
      return localVarFp
        .getPcbSwaps(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used to swap a PCB in a charging station
     * @summary Swap a PCB
     * @param {PcbSwapRequestType} [pcbSwapRequestType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    swapAPcb(
      pcbSwapRequestType?: PcbSwapRequestType,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SwapAPcb202Response> {
      return localVarFp
        .swapAPcb(pcbSwapRequestType, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used to get the status of a PCB swap
     * @summary Get status of a PCB swap
     * @param {string} swapId the swap id of the PCB swap
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    swapPcbStatus(
      swapId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SwapPcbStatus200Response> {
      return localVarFp
        .swapPcbStatus(swapId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PcbSwapApi - object-oriented interface
 * @export
 * @class PcbSwapApi
 * @extends {BaseAPI}
 */
export class PcbSwapApi extends BaseAPI {
  /**
   * Used to get the PCB swaps for a ppid
   * @summary Get PCB swaps
   * @param {string} ppid The PPID of a charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbSwapApi
   */
  public getPcbSwaps(ppid: string, options?: RawAxiosRequestConfig) {
    return PcbSwapApiFp(this.configuration)
      .getPcbSwaps(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used to swap a PCB in a charging station
   * @summary Swap a PCB
   * @param {PcbSwapRequestType} [pcbSwapRequestType]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbSwapApi
   */
  public swapAPcb(
    pcbSwapRequestType?: PcbSwapRequestType,
    options?: RawAxiosRequestConfig
  ) {
    return PcbSwapApiFp(this.configuration)
      .swapAPcb(pcbSwapRequestType, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used to get the status of a PCB swap
   * @summary Get status of a PCB swap
   * @param {string} swapId the swap id of the PCB swap
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PcbSwapApi
   */
  public swapPcbStatus(swapId: string, options?: RawAxiosRequestConfig) {
    return PcbSwapApiFp(this.configuration)
      .swapPcbStatus(swapId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UnprovisionedChargingStationApi - axios parameter creator
 * @export
 */
export const UnprovisionedChargingStationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Add test result for an unprovisioned charging station by its ID.
     * @summary Add test result for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addTestResultForUnprovisionedChargingStation: async (
      id: string,
      testResultRequest?: TestResultRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists(
        'addTestResultForUnprovisionedChargingStation',
        'id',
        id
      );
      const localVarPath =
        `/unprovisioned-charging-stations/{id}/test-result`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        testResultRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used to create a new unprovisioned charging station.
     * @summary Create an Unprovisioned Charging Station
     * @param {CreateUnprovisionedChargingStationRequest} [createUnprovisionedChargingStationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createUnprovisionedChargingStation: async (
      createUnprovisionedChargingStationRequest?: CreateUnprovisionedChargingStationRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/unprovisioned-charging-stations`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createUnprovisionedChargingStationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get configuration status for an unprovisioned charging station by its ID.
     * @summary Get configuration status for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUnprovisionedChargingStationConfiguration: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists(
        'getUnprovisionedChargingStationConfiguration',
        'id',
        id
      );
      const localVarPath =
        `/unprovisioned-charging-stations/{id}/configuration`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the status of an unprovisioned charging station by its ID.
     * @summary Get Unprovisioned Charging Station Status
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUnprovisionedChargingStationStatus: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('getUnprovisionedChargingStationStatus', 'id', id);
      const localVarPath =
        `/unprovisioned-charging-stations/{id}/status`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Used to provision a new unprovisioned charging station.
     * @summary Provision an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionUnprovisionedChargingStation: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('provisionUnprovisionedChargingStation', 'id', id);
      const localVarPath =
        `/unprovisioned-charging-stations/{id}/provision`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set configuration for an unprovisioned charging station by its ID.
     * @summary Set Configuration for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setUnprovisionedChargingStationConfiguration: async (
      id: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists(
        'setUnprovisionedChargingStationConfiguration',
        'id',
        id
      );
      const localVarPath =
        `/unprovisioned-charging-stations/{id}/configuration`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setConfigurationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UnprovisionedChargingStationApi - functional programming interface
 * @export
 */
export const UnprovisionedChargingStationApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    UnprovisionedChargingStationApiAxiosParamCreator(configuration);
  return {
    /**
     * Add test result for an unprovisioned charging station by its ID.
     * @summary Add test result for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async addTestResultForUnprovisionedChargingStation(
      id: string,
      testResultRequest?: TestResultRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AddTestResultForUnprovisionedChargingStation200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.addTestResultForUnprovisionedChargingStation(
          id,
          testResultRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.addTestResultForUnprovisionedChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used to create a new unprovisioned charging station.
     * @summary Create an Unprovisioned Charging Station
     * @param {CreateUnprovisionedChargingStationRequest} [createUnprovisionedChargingStationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createUnprovisionedChargingStation(
      createUnprovisionedChargingStationRequest?: CreateUnprovisionedChargingStationRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateUnprovisionedChargingStation201Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createUnprovisionedChargingStation(
          createUnprovisionedChargingStationRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.createUnprovisionedChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get configuration status for an unprovisioned charging station by its ID.
     * @summary Get configuration status for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getUnprovisionedChargingStationConfiguration(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetUnprovisionedChargingStationConfiguration200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getUnprovisionedChargingStationConfiguration(
          id,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.getUnprovisionedChargingStationConfiguration'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the status of an unprovisioned charging station by its ID.
     * @summary Get Unprovisioned Charging Station Status
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getUnprovisionedChargingStationStatus(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetUnprovisionedChargingStationStatus200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getUnprovisionedChargingStationStatus(
          id,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.getUnprovisionedChargingStationStatus'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Used to provision a new unprovisioned charging station.
     * @summary Provision an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async provisionUnprovisionedChargingStation(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.provisionUnprovisionedChargingStation(
          id,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.provisionUnprovisionedChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Set configuration for an unprovisioned charging station by its ID.
     * @summary Set Configuration for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setUnprovisionedChargingStationConfiguration(
      id: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setUnprovisionedChargingStationConfiguration(
          id,
          setConfigurationRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UnprovisionedChargingStationApi.setUnprovisionedChargingStationConfiguration'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UnprovisionedChargingStationApi - factory interface
 * @export
 */
export const UnprovisionedChargingStationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UnprovisionedChargingStationApiFp(configuration);
  return {
    /**
     * Add test result for an unprovisioned charging station by its ID.
     * @summary Add test result for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {TestResultRequest} [testResultRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addTestResultForUnprovisionedChargingStation(
      id: string,
      testResultRequest?: TestResultRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AddTestResultForUnprovisionedChargingStation200Response> {
      return localVarFp
        .addTestResultForUnprovisionedChargingStation(
          id,
          testResultRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Used to create a new unprovisioned charging station.
     * @summary Create an Unprovisioned Charging Station
     * @param {CreateUnprovisionedChargingStationRequest} [createUnprovisionedChargingStationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createUnprovisionedChargingStation(
      createUnprovisionedChargingStationRequest?: CreateUnprovisionedChargingStationRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateUnprovisionedChargingStation201Response> {
      return localVarFp
        .createUnprovisionedChargingStation(
          createUnprovisionedChargingStationRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get configuration status for an unprovisioned charging station by its ID.
     * @summary Get configuration status for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUnprovisionedChargingStationConfiguration(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetUnprovisionedChargingStationConfiguration200Response> {
      return localVarFp
        .getUnprovisionedChargingStationConfiguration(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the status of an unprovisioned charging station by its ID.
     * @summary Get Unprovisioned Charging Station Status
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUnprovisionedChargingStationStatus(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetUnprovisionedChargingStationStatus200Response> {
      return localVarFp
        .getUnprovisionedChargingStationStatus(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Used to provision a new unprovisioned charging station.
     * @summary Provision an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    provisionUnprovisionedChargingStation(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .provisionUnprovisionedChargingStation(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set configuration for an unprovisioned charging station by its ID.
     * @summary Set Configuration for an Unprovisioned Charging Station
     * @param {string} id The ID of the unprovisioned charging station
     * @param {SetConfigurationRequest} [setConfigurationRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setUnprovisionedChargingStationConfiguration(
      id: string,
      setConfigurationRequest?: SetConfigurationRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .setUnprovisionedChargingStationConfiguration(
          id,
          setConfigurationRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UnprovisionedChargingStationApi - object-oriented interface
 * @export
 * @class UnprovisionedChargingStationApi
 * @extends {BaseAPI}
 */
export class UnprovisionedChargingStationApi extends BaseAPI {
  /**
   * Add test result for an unprovisioned charging station by its ID.
   * @summary Add test result for an Unprovisioned Charging Station
   * @param {string} id The ID of the unprovisioned charging station
   * @param {TestResultRequest} [testResultRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public addTestResultForUnprovisionedChargingStation(
    id: string,
    testResultRequest?: TestResultRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .addTestResultForUnprovisionedChargingStation(
        id,
        testResultRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used to create a new unprovisioned charging station.
   * @summary Create an Unprovisioned Charging Station
   * @param {CreateUnprovisionedChargingStationRequest} [createUnprovisionedChargingStationRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public createUnprovisionedChargingStation(
    createUnprovisionedChargingStationRequest?: CreateUnprovisionedChargingStationRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .createUnprovisionedChargingStation(
        createUnprovisionedChargingStationRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get configuration status for an unprovisioned charging station by its ID.
   * @summary Get configuration status for an Unprovisioned Charging Station
   * @param {string} id The ID of the unprovisioned charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public getUnprovisionedChargingStationConfiguration(
    id: string,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .getUnprovisionedChargingStationConfiguration(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the status of an unprovisioned charging station by its ID.
   * @summary Get Unprovisioned Charging Station Status
   * @param {string} id The ID of the unprovisioned charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public getUnprovisionedChargingStationStatus(
    id: string,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .getUnprovisionedChargingStationStatus(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Used to provision a new unprovisioned charging station.
   * @summary Provision an Unprovisioned Charging Station
   * @param {string} id The ID of the unprovisioned charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public provisionUnprovisionedChargingStation(
    id: string,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .provisionUnprovisionedChargingStation(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set configuration for an unprovisioned charging station by its ID.
   * @summary Set Configuration for an Unprovisioned Charging Station
   * @param {string} id The ID of the unprovisioned charging station
   * @param {SetConfigurationRequest} [setConfigurationRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnprovisionedChargingStationApi
   */
  public setUnprovisionedChargingStationConfiguration(
    id: string,
    setConfigurationRequest?: SetConfigurationRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UnprovisionedChargingStationApiFp(this.configuration)
      .setUnprovisionedChargingStationConfiguration(
        id,
        setConfigurationRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * WifiCredentialsApi - axios parameter creator
 * @export
 */
export const WifiCredentialsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Generates new PPIDs with new wifi credentials for a specified number of charging stations.
     * @summary Generate wifi credentials for a specified number of charging stations
     * @param {WifiCredentialsRequest} [wifiCredentialsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateWifiCredentials: async (
      wifiCredentialsRequest?: WifiCredentialsRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/wifi-credentials/generate`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        wifiCredentialsRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns the wifi credentials associated with a charging station.
     * @summary Get wifi credentials for a charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getWifiCredentials: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getWifiCredentials', 'ppid', ppid);
      const localVarPath = `/wifi-credentials/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * WifiCredentialsApi - functional programming interface
 * @export
 */
export const WifiCredentialsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    WifiCredentialsApiAxiosParamCreator(configuration);
  return {
    /**
     * Generates new PPIDs with new wifi credentials for a specified number of charging stations.
     * @summary Generate wifi credentials for a specified number of charging stations
     * @param {WifiCredentialsRequest} [wifiCredentialsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async generateWifiCredentials(
      wifiCredentialsRequest?: WifiCredentialsRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GenerateWifiCredentials200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.generateWifiCredentials(
          wifiCredentialsRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['WifiCredentialsApi.generateWifiCredentials']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Returns the wifi credentials associated with a charging station.
     * @summary Get wifi credentials for a charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getWifiCredentials(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetWifiCredentials200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getWifiCredentials(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['WifiCredentialsApi.getWifiCredentials']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * WifiCredentialsApi - factory interface
 * @export
 */
export const WifiCredentialsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = WifiCredentialsApiFp(configuration);
  return {
    /**
     * Generates new PPIDs with new wifi credentials for a specified number of charging stations.
     * @summary Generate wifi credentials for a specified number of charging stations
     * @param {WifiCredentialsRequest} [wifiCredentialsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateWifiCredentials(
      wifiCredentialsRequest?: WifiCredentialsRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GenerateWifiCredentials200Response> {
      return localVarFp
        .generateWifiCredentials(wifiCredentialsRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns the wifi credentials associated with a charging station.
     * @summary Get wifi credentials for a charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getWifiCredentials(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetWifiCredentials200Response> {
      return localVarFp
        .getWifiCredentials(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * WifiCredentialsApi - object-oriented interface
 * @export
 * @class WifiCredentialsApi
 * @extends {BaseAPI}
 */
export class WifiCredentialsApi extends BaseAPI {
  /**
   * Generates new PPIDs with new wifi credentials for a specified number of charging stations.
   * @summary Generate wifi credentials for a specified number of charging stations
   * @param {WifiCredentialsRequest} [wifiCredentialsRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof WifiCredentialsApi
   */
  public generateWifiCredentials(
    wifiCredentialsRequest?: WifiCredentialsRequest,
    options?: RawAxiosRequestConfig
  ) {
    return WifiCredentialsApiFp(this.configuration)
      .generateWifiCredentials(wifiCredentialsRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns the wifi credentials associated with a charging station.
   * @summary Get wifi credentials for a charging station
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof WifiCredentialsApi
   */
  public getWifiCredentials(ppid: string, options?: RawAxiosRequestConfig) {
    return WifiCredentialsApiFp(this.configuration)
      .getWifiCredentials(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
