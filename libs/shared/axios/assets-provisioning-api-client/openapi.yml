openapi: 3.0.2
info:
  title: provisioning-api
  version: '1.0'
  description: |
    An API for provisioning assets - Charging stations & Sub-assemblies (EVSEs/PCBs)
    ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
  contact:
    name: Software Team
    email: <EMAIL>
    url: 'https://pod-point.com'
servers:
  - description: local
    url: 'http://localhost:4001'
  - description: dev
    url: 'http://provisioning-api-dev.pod-point.com'
  - description: staging
    url: 'http://provisioning-api-staging.pod-point.com'
  - description: prod
    url: 'http://provisioning-api.pod-point.com'
tags:
  - name: ChargingStation
  - name: Pcb
  - name: Pcb Swap
  - name: Attach/Detach Test Helpers
  - name: Wifi credentials
  - name: Unprovisioned Charging Station
  - name: Model
paths:
  /charging-stations:
    post:
      summary: Create a Charging Station
      description: 'Used to create a new charging station.'
      operationId: create-charging-station
      tags:
        - ChargingStation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChargingStation'
      responses:
        '201':
          description: 'Charging Station created successfully'
        '500':
          description: 'Internal Server Error'
  /charging-stations/{ppid}/status:
    get:
      summary: Get Charging Station Status
      description: 'Get the online/offline status of a charging station by its PPID.'
      operationId: get-charging-station-status
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - ChargingStation
      responses:
        '200':
          description: 'Successful operation'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargingStationStatusResponse'
        '404':
          description: 'Charging Station not found'
  /charging-stations/{ppid}/test-result:
    post:
      summary: Add Test Result
      description: 'Add a test result for a charging station identified by its PPID.'
      operationId: add-test-result
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - ChargingStation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestResultRequest'
      responses:
        '200':
          description: 'Test result added successfully'
        '404':
          description: 'Charging Station not found'
  /charging-stations/{ppid}/configuration:
    post:
      summary: Set Configuration
      description: 'Set configuration for a charging station identified by its PPID.'
      operationId: set-configuration
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - ChargingStation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetConfigurationRequest'
      responses:
        '201':
          description: 'Configuration set successfully'
        '404':
          description: 'Charging Station not found'
        '410':
          description: 'Charging Station not online'
  /charging-stations/{ppid}/configuration/status:
    get:
      summary: Get Configuration Status
      description: 'Get the configuration status of a charging station identified by its PPID.'
      operationId: get-configuration-status
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - ChargingStation
      responses:
        '200':
          description: 'Successful operation'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationStatusResponse'
        '404':
          description: 'Charging Station not found'
  /charging-stations/{ppid}/provision:
    post:
      summary: Provision Charging Station
      description: 'Initiate provisioning for a charging station identified by its PPID.'
      operationId: provision-charging-station
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - ChargingStation
      responses:
        '200':
          description: 'Charging Station provisioned successfully'
        '404':
          description: 'Charging Station not found'
  /charging-stations/{ppid}/pcbs/{serialNumber}/detach:
    post:
      summary: Detach a PCB
      description: 'Detach a PCB from a unit in podadmin.'
      operationId: detach-a-pcb
      parameters:
        - $ref: '#/components/parameters/p_ppid'
        - $ref: '#/components/parameters/p_serialNumber'
      tags:
        - Attach/Detach Test Helpers
      responses:
        '200':
          description: 'Successfully detached'
        '400':
          description: 'Will throw an error if the PCB is not linked to the PPID'
        '404':
          description: 'Pod Unit not found'
  /charging-stations/{ppid}/pcbs:
    post:
      summary: Attach a PCB
      description: 'Attach a PCB to a unit in podadmin.'
      operationId: attach-a-pcb
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - Attach/Detach Test Helpers
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AttachPcbRequest'
      responses:
        '201':
          description: 'Successfully attached'
        '404':
          description: 'Pod Unit not found'
        '409':
          description: 'PCB already linked to a unit'

  /pcbs:
    post:
      summary: Create a PCB
      description: Used to record that a pcb exists, returns that firmware version that should be used during provisioning.
      operationId: create-a-pcb
      parameters:
        - $ref: '#/components/parameters/h_CertificateParsedChainJson'
      requestBody:
        $ref: '#/components/requestBodies/CreatePcbRequest'
      tags:
        - Pcb
      responses:
        '201':
          $ref: '#/components/responses/CreatePcbResponse'
        '400':
          description: 'Will throw an error when the PCB type is not found, when the PCB type is not supported or when a PCB already exists'
  /pcbs/{serialNumber}/provision:
    post:
      summary: Provision the PCB
      description: 'Used by our FCT boxes to mark a PCB as Provisioned (supplies provisioning-info)'
      operationId: provision-a-pcb
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
        - $ref: '#/components/parameters/h_CertificateParsedChainJson'
      requestBody:
        $ref: '#/components/requestBodies/ProvisionPcbRequest'
      tags:
        - Pcb
      responses:
        '201':
          description: 'Successfully provisioned'
        '400':
          description: 'Will throw an error when the PCB has already been provisioned or when the MAC Address already exists'
        '404':
          description: 'PCB not found'
  /pcbs/{serialNumber}/provisionedData:
    get:
      summary: Retrieve the provisionedData for a PCB
      description: 'Used by our FCT boxes to retrieve passwords for provisioned PCBs when re-flashing after a test failure'
      operationId: get-provisioned-data
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
      tags:
        - Pcb
      responses:
        '200':
          description: 'Got provisionedData'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProvisionedDataResponse'
        '404':
          description: 'PCB not found'
  /pcbs/{serialNumber}/events:
    get:
      summary: Retrieve events for a PCB
      description: 'Used by our FCT boxes to retrieve details of any previous FCT runs'
      operationId: get-pcb-events
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
      tags:
        - Pcb
      responses:
        '200':
          description: 'Got PCB events'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPcbEventsResponse'
        '404':
          description: 'PCB not found'
  /pcbs/{serialNumber}/fctEvent:
    post:
      summary: Post an FCT event for the PCB
      description: 'Used by our FCT boxes to track progress through FCT process'
      operationId: post-fct-event
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
      requestBody:
        $ref: '#/components/requestBodies/PostFctEvent'
      tags:
        - Pcb
      responses:
        '201':
          description: 'Successfully stored event'
        '400':
          description: 'Will throw an error when the event type is incorrect'
  /wifi-credentials/generate:
    post:
      summary: Generate wifi credentials for a specified number of charging stations
      description: Generates new PPIDs with new wifi credentials for a specified number of charging stations.
      operationId: generate-wifi-credentials
      tags:
        - Wifi credentials
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WifiCredentialsRequest'
      responses:
        '200':
          $ref: '#/components/responses/GenerateWifiCredentialsResponse'
        '404':
          description: No update available or PCB not found
  /wifi-credentials/{ppid}:
    get:
      summary: Get wifi credentials for a charging station
      description: Returns the wifi credentials associated with a charging station.
      operationId: get-wifi-credentials
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - Wifi credentials
      responses:
        '200':
          $ref: '#/components/responses/GetWifiCredentialsResponse'
        '404':
          description: No wifi credentials found for charging station
  /pcb-swaps:
    post:
      summary: Swap a PCB
      description: Used to swap a PCB in a charging station
      operationId: swap-a-pcb
      requestBody:
        $ref: '#/components/requestBodies/PcbSwapRequest'
      tags:
        - Pcb Swap
      responses:
        '202':
          $ref: '#/components/responses/PcbSwapResponse'
    get:
      summary: Get PCB swaps
      description: Used to get the PCB swaps for a ppid
      operationId: get-pcb-swaps
      parameters:
        - $ref: '#/components/parameters/ppid'
      tags:
        - Pcb Swap
      responses:
        '200':
          $ref: '#/components/responses/PcbSwapsResponse'
  /pcb-swaps/{swapId}:
    get:
      summary: Get status of a PCB swap
      description: Used to get the status of a PCB swap
      operationId: swap-pcb-status
      parameters:
        - $ref: '#/components/parameters/swapId'
      tags:
        - Pcb Swap
      responses:
        '200':
          $ref: '#/components/responses/PcbSwapStatusResponse'

  /unprovisioned-charging-stations:
    post:
      summary: Create an Unprovisioned Charging Station
      description: 'Used to create a new unprovisioned charging station.'
      operationId: create-unprovisioned-charging-station
      tags:
        - Unprovisioned Charging Station
      requestBody:
        $ref: '#/components/requestBodies/CreateUnprovisionedChargingStation'
      responses:
        '201':
          $ref: '#/components/responses/CreateUnprovisionedChargingStationResponse'
        '400':
          description: 'Will throw an error if the unprovisioned charging station can not be created'
  /unprovisioned-charging-stations/{id}/status:
    get:
      summary: Get Unprovisioned Charging Station Status
      description: 'Get the status of an unprovisioned charging station by its ID.'
      operationId: get-unprovisioned-charging-station-status
      parameters:
        - name: id
          in: path
          description: 'The ID of the unprovisioned charging station'
          required: true
          schema:
            type: string
            example: '31c6007f-fa23-438f-b045-faf345492e9f'
      tags:
        - Unprovisioned Charging Station
      responses:
        '200':
          $ref: '#/components/responses/UnprovisionedChargingStationStatusResponse'
        '404':
          description: 'Unprovisioned Charging Station not found'
  /unprovisioned-charging-stations/{id}/configuration:
    post:
      summary: Set Configuration for an Unprovisioned Charging Station
      description: 'Set configuration for an unprovisioned charging station by its ID.'
      operationId: set-unprovisioned-charging-station-configuration
      parameters:
        - name: id
          in: path
          description: 'The ID of the unprovisioned charging station'
          required: true
          schema:
            type: string
            example: '31c6007f-fa23-438f-b045-faf345492e9f'
      tags:
        - Unprovisioned Charging Station
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetConfigurationRequest'
      responses:
        '201':
          description: 'Configuration set successfully'
        '404':
          description: 'Unprovisioned Charging Station not found'
        '410':
          description: 'Unprovisioned Charging Station not online'
    get:
      summary: Get configuration status for an Unprovisioned Charging Station
      description: 'Get configuration status for an unprovisioned charging station by its ID.'
      operationId: get-unprovisioned-charging-station-configuration
      parameters:
        - name: id
          in: path
          description: 'The ID of the unprovisioned charging station'
          required: true
          schema:
            type: string
            example: '31c6007f-fa23-438f-b045-faf345492e9f'
      tags:
        - Unprovisioned Charging Station
      responses:
        '200':
          $ref: '#/components/responses/UnprovisionedChargingStationConfigurationStatusResponse'
        '404':
          description: 'Unprovisioned Charging Station not found'
  /unprovisioned-charging-stations/{id}/test-result:
    post:
      summary: Add test result for an Unprovisioned Charging Station
      description: 'Add test result for an unprovisioned charging station by its ID.'
      operationId: add-test-result-for-unprovisioned-charging-station
      parameters:
        - name: id
          in: path
          description: 'The ID of the unprovisioned charging station'
          required: true
          schema:
            type: string
            example: '31c6007f-fa23-438f-b045-faf345492e9f'
      tags:
        - Unprovisioned Charging Station
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestResultRequest'
      responses:
        '200':
          description: 'Test result added successfully'
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'success'
                    enum:
                      - success
                required:
                  - message
        '404':
          description: 'Unprovisioned Charging Station not found'
        '400':
          description: 'Invalid request body'
  /unprovisioned-charging-stations/{id}/provision:
    post:
      summary: Provision an Unprovisioned Charging Station
      description: 'Used to provision a new unprovisioned charging station.'
      operationId: provision-unprovisioned-charging-station
      parameters:
        - name: id
          in: path
          description: 'The ID of the unprovisioned charging station'
          required: true
          schema:
            type: string
            example: '31c6007f-fa23-438f-b045-faf345492e9f'
      tags:
        - Unprovisioned Charging Station
      responses:
        '201':
          description: 'Unprovisioned Charging Station provisioned successfully'
        '400':
          description: 'Will throw an error if the unprovisioned charging station can not be created'
        '404':
          description: 'Will throw an error if the data required for creation is missing'

  /models/{sku}:
    get:
      summary: 'Gets provisioning requirements for the SKU'
      description: Used to get the provisioning requirements for the SKU
      operationId: get-provisioning-requirements-sku
      parameters:
        - $ref: '#/components/parameters/p_sku'
      tags:
        - Model
      responses:
        '200':
          $ref: '#/components/responses/GetProvisioningRequirementsResponse'
        '404':
          description: Pod model not found exception

components:
  schemas:
    CreateChargingStation:
      type: object
      properties:
        serialNumber:
          type: string
        sku:
          type: string
        ppid:
          type: string
      required:
        - serialNumber
        - sku
        - ppid
    TestResultRequest:
      type: object
      properties:
        result:
          type: string
          enum:
            - pass
            - fail
      required:
        - result
    WifiCredentialsRequest:
      type: object
      properties:
        amount:
          type: number
          example: 5000
        prefix:
          type: string
          enum:
            - PG
            - PSL
      required:
        - amount
    AttachPcbRequest:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        door:
          $ref: '#/components/schemas/Door'
      required:
        - serialNumber
    SetConfigurationRequest:
      type: object
      properties:
        clientRef:
          type: string
      required:
        - clientRef
    ChargingStationStatusResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - online
            - offline
    SetRequestStatus:
      type: string
      enum:
        - Accepted
        - Requested
        - Error
    ConfigurationStatus:
      type: string
      enum:
        - Accepted
        - Requested
        - Error
    ConfigurationStatusResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/ConfigurationStatus'
    SerialNumber:
      description: The serial number of an EVSE (aka PCB)
      type: string
      example: '30303030'
    CreatePcbRequestType:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        pcbType:
          type: string
          example: 'PP-A-220270-1'
      required:
        - serialNumber
        - pcbType
    PcbProvisioningInfo:
      type: object
      properties:
        provisionedBy:
          type: string
          description: The identity of the FCT box used to provision the PCB
          example: Celestica.P00001
        provisionedAt:
          type: string
          description: The date and time the PCB was (re)-provisioned
          format: date-time
          example: '2023-06-01T00:00:00.000Z'
        provisionedData:
          type: object
          additionalProperties: true
          description: JSON data provided by the FCT box at the time it is provisioned
          example: {}
        macAddress:
          $ref: './openapi_asset_api.yml#/components/schemas/MacAddress'
    FctEvent:
      type: object
      properties:
        type:
          type: string
          enum: [fctStarted, fctFailed, fctCompleted]
          description: The type of event
        data:
          type: object
          description: Any metadata associated with the event
          example: {}
    GetProvisionedDataResponse:
      type: object
      additionalProperties: true
      description: JSON data provided by the FCT box at the time it is provisioned
      example: {}
    GetPcbEventsResponse:
      type: array
      items:
        type: object
        properties:
          data:
            type: object
            description: Event metadata
            example: {}
          serialNumber:
            type: string
            description: The serial number of the PCBA
            example: '0000000000'
          type:
            type: string
            description: The type of the event
            example: 'created'
          uuid:
            type: string
            description: The unique UUID for that event
            example: '2cd3b3b6-f920-451b-9dfb-531aa469d8b2'
    PcbSwapRequestType:
      type: object
      properties:
        serialNumber:
          type: string
          description: The serial number of the new PCB you want to swap into the charging station.
          example: '12345678'
        ppid:
          type: string
          description: The PPID of the existing charging station the PCB is being swapped into.
          example: 'PSL-123456'
        door:
          $ref: '#/components/schemas/Door'
      required:
        - serialNumber
        - ppid
    SwapIdType:
      description: The swap id of the PCB swap
      type: string
      example: '2a8eabaa-8a1f-4456-bb8d-1384456846a4'
    PcbSwapType:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/SwapIdType'
        ppid:
          type: string
          description: The PPID of the existing charging station the PCB is being swapped into.
          example: 'PSL-123456'
        serialNumber:
          type: string
          description: The serial number of the new PCB you want to swap into the charging station.
          example: '12345678'
        door:
          $ref: '#/components/schemas/Door'
        status:
          $ref: '#/components/schemas/PcbSwapStatus'
        errorCode:
          type: string
          description: The error message if the status is 'Failed'.
          example: 'EAP0006'
        timestamp:
          type: string
          format: date-time
          example: '2021-07-01T12:00:00Z'
      required:
        - id
        - ppid
        - serialNumber
        - door
        - status
        - timestamp
    PcbSwapStatus:
      type: string
      enum:
        - Success
        - In Progress
        - Failed
        - Partially Successful
      example: 'Success'
    Door:
      description: The door of a charging station
      type: string
      enum:
        - A
        - B
    Sku:
      description: The Sku for the model
      type: string
      example: Solo Pro
    ModelProvisioningRequirements:
      type: object
      properties:
        evseCount:
          type: number
          example: 1
        rfidAtManufacture:
          type: boolean
          example: true
        routerAtManufacture:
          type: boolean
          example: true
        midMeterAtManufacture:
          type: boolean
          example: true
      required:
        - evseCount
        - rfidAtManufacture
        - routerAtManufacture
        - midMeterAtManufacture
  parameters:
    p_ppid:
      in: path
      name: ppid
      description: 'The PPID of the charging station'
      required: true
      schema:
        type: string
        example: 'PSL-123456'
    p_serialNumber:
      in: path
      name: serialNumber
      description: 'The serial number of the PCB'
      required: true
      schema:
        type: string
        example: '30303030'
    p_sku:
      name: sku
      description: sku from an existing unit
      example: Solo Pro
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/Sku'
    h_CertificateParsedChainJson:
      in: header
      description: A JSON string of the TLS certificate chain, passed to us from the mtls-gateway.
      name: Certificate-Parsed-Chain-Json
      required: true
      schema:
        type: string
        example: '[{"subject":{"commonName":"Celestica.P00001"}}]'
    swapId:
      name: swapId
      description: the swap id of the PCB swap
      example: 0cb2b703c778,0cb2b703c779
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/SwapIdType'
    ppid:
      name: ppid
      description: The PPID of a charging station
      example: PSL-123456
      in: query
      required: true
      schema:
        type: string
        example: 'PSL-123456'
  requestBodies:
    CreatePcbRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreatePcbRequestType'
    CreateUnprovisionedChargingStation:
      content:
        application/json:
          schema:
            type: object
            properties:
              ppid:
                type: string
                example: 'PG-123456'
              sku:
                type: string
                example: 'S7-UC-05-AKA-UK-0001'
              serialNumberDoorA:
                type: string
                example: '1234567890'
              serialNumberDoorB:
                type: string
                example: '9087654321'
              routerSim:
                type: string
                example: '1111222233334444'
            required:
              - ppid
              - sku
              - serialNumberDoorA
    ProvisionPcbRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PcbProvisioningInfo'
    PostFctEvent:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/FctEvent'
    PcbSwapRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PcbSwapRequestType'
  responses:
    CreatePcbResponse:
      description: PCB successfully provisioned
      content:
        application/json:
          schema:
            type: object
            properties:
              serialNumber:
                $ref: '#/components/schemas/SerialNumber'
              pcbType:
                type: string
                example: 'PP-A-220270-1'
            required:
              - serialNumber
              - pcbType
    CreateUnprovisionedChargingStationResponse:
      description: 'Returns the id of the unprovisioned charging station.'
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                example: '31c6007f-fa23-438f-b045-faf345492e9f'
            required:
              - id
    UnprovisionedChargingStationStatusResponse:
      description: 'Returns the status of the unprovisioned charging station.'
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                enum:
                  - online
                  - offline
    UnprovisionedChargingStationConfigurationStatusResponse:
      description: 'Returns the configuration status of the unprovisioned charging station.'
      content:
        application/json:
          schema:
            type: object
            properties:
              serialNumberDoorA:
                $ref: '#/components/schemas/SetRequestStatus'
              serialNumberDoorB:
                $ref: '#/components/schemas/SetRequestStatus'
    GenerateWifiCredentialsResponse:
      description: 'Returns the presigned url for the file in s3 containing the wifi credentials for the charging stations.'
      content:
        application/json:
          schema:
            type: object
            properties:
              url:
                type: string
                format: uri
                example: 'https://pod-point-provisioning.s3.eu-west-1.amazonaws.com/soft-ap-credentials-PSL-123456-2.csv'
              key:
                type: string
                example: 'soft-ap-credentials-PSL-123456-2.csv'
    GetWifiCredentialsResponse:
      description: 'Returns wifi credentials for a charging station'
      content:
        application/json:
          schema:
            type: object
            properties:
              ppid:
                type: string
                example: 'PSL-123456'
              ssid:
                type: string
                example: 'PP-123456'
              password:
                type: string
                example: 'password123'
              twinSsid:
                type: object
                properties:
                  doorA:
                    type: string
                    example: 'PP-123456-A'
                  doorB:
                    type: string
                    example: 'PP-123456-B'
    PcbSwapResponse:
      description: Returns the id of the PCB swap.
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                format: uuid
                example: '2a8eabaa-8a1f-4456-bb8d-1384456846a4'
    PcbSwapStatusResponse:
      description: Returns the status of the PCB swap.
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                $ref: '#/components/schemas/PcbSwapStatus'
              errorCode:
                type: string
                description: The error message if the status is 'Failed'.
                example: 'EAP0006'
    PcbSwapsResponse:
      description: Returns the list of PCB swaps for a ppid.
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: '#/components/schemas/PcbSwapType'
    GetProvisioningRequirementsResponse:
      description: Returns the provisioning requirements for a model
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: '#/components/schemas/ModelProvisioningRequirements'
