export const TEST_BATCH_SET_CONFIG_REQUEST = {
  clientRef: 'support-tool_<PERSON>.<PERSON>@pod-point.com',
  configuration: [
    { key: 'ChargeCurrentLimitA', value: '32' },
    {
      key: 'OfflineSchedulingEnabled',
      value: 'true',
    },
    {
      key: 'PowerBalancingCurrentLimitImportA',
      value: '50',
    },
    { key: 'PowerBalancingEnabled', value: 'true' },
    { key: 'PowerBalancingSensor', value: 'CT_CLAMP_1' },
    { key: 'PowerBalancingSensorInstalled', value: 'true' },
    { key: 'PowerBalancingSensorPolarityInverted', value: 'true' },
    { key: 'PPDevClampFaultThreshold', value: '2' },
    {
      key: 'RcdBreakerSize',
      value: '10',
    },
    { key: 'SolarExportMargin', value: '5' },
    { key: 'SolarMatchingEnabled', value: 'true' },
    { key: 'SolarMaxGridImport', value: '4.1' },
    { key: 'SolarStartHysteresis', value: '10' },
    { key: 'SolarStopHysteresis', value: '64' },
    { key: 'SolarSystemInstalled', value: 'true' },
    { key: 'UnlockConnectorOnEVSideDisconnect', value: 'true' },
  ],
};
