import { CurrencyConverter } from '../base/types';
import { Decimal } from 'decimal.js';
import { OXRLatestResponse } from './types';
import { UnsupportedCurrencyError } from '../base/errors';
import axios, { type AxiosInstance } from 'axios';

export class OXRCurrencyConverter implements CurrencyConverter {
  private static readonly BASE_URL = 'https://openexchangerates.org/api/';

  private readonly client: AxiosInstance;

  constructor(appId: string, client = axios.create()) {
    client.defaults.baseURL = OXRCurrencyConverter.BASE_URL;
    client.defaults.headers.common = {
      Authorization: `Token ${appId}`,
    };

    this.client = client;
  }

  async convert(amount: number, from: string, to: string): Promise<number> {
    const { data } = await this.client.get<OXRLatestResponse>('latest.json', {
      params: {
        base: from,
        symbols: to,
      },
    });

    const rate = data.rates[to.toUpperCase()];

    if (!rate) {
      throw new UnsupportedCurrencyError(to);
    }

    return new Decimal(amount).mul(rate).toNumber();
  }
}
