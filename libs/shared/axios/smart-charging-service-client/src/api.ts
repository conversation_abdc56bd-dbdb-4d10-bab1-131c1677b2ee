/* tslint:disable */
/* eslint-disable */
/**
 * Smart Charging Service Api
 * API for managing smart charging service api
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface API3ChargeSchedule
 */
export interface API3ChargeSchedule {
  /**
   *
   * @type {string}
   * @memberof API3ChargeSchedule
   */
  uid: string;
  /**
   *
   * @type {number}
   * @memberof API3ChargeSchedule
   */
  start_day: API3ChargeScheduleStartDayEnum;
  /**
   *
   * @type {string}
   * @memberof API3ChargeSchedule
   */
  start_time: string;
  /**
   *
   * @type {number}
   * @memberof API3ChargeSchedule
   */
  end_day: API3ChargeScheduleEndDayEnum;
  /**
   *
   * @type {string}
   * @memberof API3ChargeSchedule
   */
  end_time: string;
  /**
   *
   * @type {ChargeScheduleStatus}
   * @memberof API3ChargeSchedule
   */
  status: ChargeScheduleStatus;
}

export const API3ChargeScheduleStartDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type API3ChargeScheduleStartDayEnum =
  (typeof API3ChargeScheduleStartDayEnum)[keyof typeof API3ChargeScheduleStartDayEnum];
export const API3ChargeScheduleEndDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type API3ChargeScheduleEndDayEnum =
  (typeof API3ChargeScheduleEndDayEnum)[keyof typeof API3ChargeScheduleEndDayEnum];

/**
 *
 * @export
 * @interface API3PatchChargeScheduleBody
 */
export interface API3PatchChargeScheduleBody {
  /**
   *
   * @type {number}
   * @memberof API3PatchChargeScheduleBody
   */
  start_day?: API3PatchChargeScheduleBodyStartDayEnum;
  /**
   *
   * @type {object}
   * @memberof API3PatchChargeScheduleBody
   */
  start_time?: object;
  /**
   *
   * @type {number}
   * @memberof API3PatchChargeScheduleBody
   */
  end_day?: API3PatchChargeScheduleBodyEndDayEnum;
  /**
   *
   * @type {object}
   * @memberof API3PatchChargeScheduleBody
   */
  end_time?: object;
  /**
   *
   * @type {ChargeScheduleStatus}
   * @memberof API3PatchChargeScheduleBody
   */
  status?: ChargeScheduleStatus;
}

export const API3PatchChargeScheduleBodyStartDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type API3PatchChargeScheduleBodyStartDayEnum =
  (typeof API3PatchChargeScheduleBodyStartDayEnum)[keyof typeof API3PatchChargeScheduleBodyStartDayEnum];
export const API3PatchChargeScheduleBodyEndDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type API3PatchChargeScheduleBodyEndDayEnum =
  (typeof API3PatchChargeScheduleBodyEndDayEnum)[keyof typeof API3PatchChargeScheduleBodyEndDayEnum];

/**
 *
 * @export
 * @interface API3PutChargeScheduleBody
 */
export interface API3PutChargeScheduleBody {
  /**
   *
   * @type {Array<Data>}
   * @memberof API3PutChargeScheduleBody
   */
  data: Array<Data>;
}
/**
 *
 * @export
 * @interface CannotMeetSessionIntent
 */
export interface CannotMeetSessionIntent {
  /**
   *
   * @type {string}
   * @memberof CannotMeetSessionIntent
   */
  cannotMeetTargetReason: CannotMeetSessionIntentCannotMeetTargetReasonEnum;
  /**
   *
   * @type {number}
   * @memberof CannotMeetSessionIntent
   */
  expectedChargeByTarget_kWh: number | null;
  /**
   *
   * @type {number}
   * @memberof CannotMeetSessionIntent
   */
  expectedChargeByTargetPercent: number | null;
  /**
   *
   * @type {string}
   * @memberof CannotMeetSessionIntent
   */
  fullChargeByTime: string | null;
}

export const CannotMeetSessionIntentCannotMeetTargetReasonEnum = {
  Price: 'PRICE',
  Time: 'TIME',
} as const;

export type CannotMeetSessionIntentCannotMeetTargetReasonEnum =
  (typeof CannotMeetSessionIntentCannotMeetTargetReasonEnum)[keyof typeof CannotMeetSessionIntentCannotMeetTargetReasonEnum];

/**
 *
 * @export
 * @interface ChargeDetailDto
 */
export interface ChargeDetailDto {
  /**
   *
   * @type {number}
   * @memberof ChargeDetailDto
   */
  expectedChargeByTarget_kWh: number | null;
  /**
   *
   * @type {number}
   * @memberof ChargeDetailDto
   */
  expectedChargeByTargetPercent: number | null;
  /**
   *
   * @type {string}
   * @memberof ChargeDetailDto
   */
  fullChargeByTime: string | null;
}
/**
 *
 * @export
 * @interface ChargeOverrideRequest
 */
export interface ChargeOverrideRequest {
  /**
   * The date and time the charge override was requested
   * @type {string}
   * @memberof ChargeOverrideRequest
   */
  requestedAt: string;
  /**
   * The date and time the charge override should end
   * @type {string}
   * @memberof ChargeOverrideRequest
   */
  endAt?: string | null;
}
/**
 *
 * @export
 * @interface ChargeOverrideResponse
 */
export interface ChargeOverrideResponse {
  /**
   * The ID of the charge override
   * @type {string}
   * @memberof ChargeOverrideResponse
   */
  id: string;
  /**
   * The date and time the charge override was requested
   * @type {string}
   * @memberof ChargeOverrideResponse
   */
  requestedAt: string;
  /**
   * The date and time the charge override was received by the api
   * @type {string}
   * @memberof ChargeOverrideResponse
   */
  receivedAt: string;
  /**
   * The date and time the charge override should end
   * @type {string}
   * @memberof ChargeOverrideResponse
   */
  endAt: string | null;
  /**
   * The evse associated with the charge override
   * @type {EvseResponse}
   * @memberof ChargeOverrideResponse
   */
  evse: EvseResponse;
  /**
   * The charging station associated with the charge override
   * @type {ChargingStationResponseDto}
   * @memberof ChargeOverrideResponse
   */
  chargingStation: ChargingStationResponseDto;
  /**
   * The date and time the charge override was deleted
   * @type {string}
   * @memberof ChargeOverrideResponse
   */
  deletedAt: string | null;
}
/**
 *
 * @export
 * @interface ChargeScheduleDto
 */
export interface ChargeScheduleDto {
  /**
   * The weekday the charge schedule starts on
   * @type {number}
   * @memberof ChargeScheduleDto
   */
  startDay: ChargeScheduleDtoStartDayEnum;
  /**
   * The weekday the charge schedule ends on
   * @type {number}
   * @memberof ChargeScheduleDto
   */
  endDay: ChargeScheduleDtoEndDayEnum;
  /**
   * The time the charge schedule starts
   * @type {string}
   * @memberof ChargeScheduleDto
   */
  startTime: string;
  /**
   * The time the charge schedule ends
   * @type {string}
   * @memberof ChargeScheduleDto
   */
  endTime: string;
  /**
   * The date the charge schedule was created
   * @type {string}
   * @memberof ChargeScheduleDto
   */
  createdAt: string;
  /**
   * The date the charge schedule was last updated
   * @type {string}
   * @memberof ChargeScheduleDto
   */
  deletedAt: string;
}

export const ChargeScheduleDtoStartDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type ChargeScheduleDtoStartDayEnum =
  (typeof ChargeScheduleDtoStartDayEnum)[keyof typeof ChargeScheduleDtoStartDayEnum];
export const ChargeScheduleDtoEndDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type ChargeScheduleDtoEndDayEnum =
  (typeof ChargeScheduleDtoEndDayEnum)[keyof typeof ChargeScheduleDtoEndDayEnum];

/**
 *
 * @export
 * @interface ChargeScheduleStatus
 */
export interface ChargeScheduleStatus {
  /**
   *
   * @type {boolean}
   * @memberof ChargeScheduleStatus
   */
  is_active: boolean;
}
/**
 *
 * @export
 * @interface ChargeStatisticsDto
 */
export interface ChargeStatisticsDto {
  /**
   * The start time of the charge (ISO 8601)
   * @type {string}
   * @memberof ChargeStatisticsDto
   */
  chargeStartTime: string;
  /**
   * The end time of the charge (ISO 8601)
   * @type {string}
   * @memberof ChargeStatisticsDto
   */
  chargeEndTime: string;
  /**
   * The energy (kWh) from the grid used by the charge
   * @type {number}
   * @memberof ChargeStatisticsDto
   */
  kWhFromGrid: number;
  /**
   * The energy (kWh) from local-generated sources (e.g. by solar) used by the charge
   * @type {number}
   * @memberof ChargeStatisticsDto
   */
  kWhFromGeneration: number;
}
/**
 *
 * @export
 * @interface ChargingProfileDeleteRangeDto
 */
export interface ChargingProfileDeleteRangeDto {
  /**
   * Start of stack level range to delete (inclusive)
   * @type {number}
   * @memberof ChargingProfileDeleteRangeDto
   */
  start: number;
  /**
   * End of stack level range to delete (inclusive)
   * @type {number}
   * @memberof ChargingProfileDeleteRangeDto
   */
  end: number;
}
/**
 *
 * @export
 * @interface ChargingProfileDto
 */
export interface ChargingProfileDto {
  /**
   * OCPP 1.6 charging profile, note that chargingProfileId is not required, and if it is provided it will be overwritten by this api
   * @type {object}
   * @memberof ChargingProfileDto
   */
  chargingProfile: object;
}
/**
 *
 * @export
 * @interface ChargingProfilesRequestDto
 */
export interface ChargingProfilesRequestDto {
  /**
   * Charging profiles
   * @type {Array<ChargingProfileDto>}
   * @memberof ChargingProfilesRequestDto
   */
  profiles: Array<ChargingProfileDto>;
}
/**
 *
 * @export
 * @interface ChargingScheduleDto
 */
export interface ChargingScheduleDto {
  /**
   * Duration
   * @type {number}
   * @memberof ChargingScheduleDto
   */
  duration?: number;
  /**
   * Start schedule
   * @type {string}
   * @memberof ChargingScheduleDto
   */
  startSchedule?: string;
  /**
   * Charging rate unit
   * @type {string}
   * @memberof ChargingScheduleDto
   */
  chargingRateUnit: ChargingScheduleDtoChargingRateUnitEnum;
  /**
   * Charging schedule period
   * @type {Array<ChargingSchedulePeriodDto>}
   * @memberof ChargingScheduleDto
   */
  chargingSchedulePeriod: Array<ChargingSchedulePeriodDto>;
  /**
   * Minimum charging rate
   * @type {number}
   * @memberof ChargingScheduleDto
   */
  minChargingRate?: number;
}

export const ChargingScheduleDtoChargingRateUnitEnum = {
  W: 'W',
  A: 'A',
} as const;

export type ChargingScheduleDtoChargingRateUnitEnum =
  (typeof ChargingScheduleDtoChargingRateUnitEnum)[keyof typeof ChargingScheduleDtoChargingRateUnitEnum];

/**
 *
 * @export
 * @interface ChargingSchedulePeriodDto
 */
export interface ChargingSchedulePeriodDto {
  /**
   * Start period
   * @type {number}
   * @memberof ChargingSchedulePeriodDto
   */
  startPeriod: number;
  /**
   * Limit
   * @type {number}
   * @memberof ChargingSchedulePeriodDto
   */
  limit: number;
  /**
   * Number of phases
   * @type {number}
   * @memberof ChargingSchedulePeriodDto
   */
  numberPhases?: number;
}
/**
 *
 * @export
 * @interface ChargingStationResponseDto
 */
export interface ChargingStationResponseDto {
  /**
   * The ID of the charging station
   * @type {string}
   * @memberof ChargingStationResponseDto
   */
  id?: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargingStationResponseDto
   */
  ppid: string;
  /**
   *
   * @type {string}
   * @memberof ChargingStationResponseDto
   */
  mpan?: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargingStationResponseDto
   */
  addressId?: string | null;
}
/**
 *
 * @export
 * @interface CommandEvent
 */
export interface CommandEvent {
  /**
   * The command message ID
   * @type {string}
   * @memberof CommandEvent
   */
  messageId: string;
  /**
   * The command payload
   * @type {string}
   * @memberof CommandEvent
   */
  payload?: string;
  /**
   * The command error code
   * @type {string}
   * @memberof CommandEvent
   */
  errorCode?: string;
  /**
   * The command error description
   * @type {string}
   * @memberof CommandEvent
   */
  errorDescription?: string;
  /**
   * The command error details
   * @type {string}
   * @memberof CommandEvent
   */
  errorDetails?: string;
}
/**
 *
 * @export
 * @interface CommandMetadata
 */
export interface CommandMetadata {
  /**
   * The charging station PPID
   * @type {string}
   * @memberof CommandMetadata
   */
  ppid: string;
}
/**
 *
 * @export
 * @interface CommandResponse
 */
export interface CommandResponse {
  /**
   * The command ID
   * @type {string}
   * @memberof CommandResponse
   */
  id: string;
  /**
   * The command type
   * @type {string}
   * @memberof CommandResponse
   */
  type: CommandResponseTypeEnum;
  /**
   * The command response type
   * @type {string}
   * @memberof CommandResponse
   */
  responseType: CommandResponseResponseTypeEnum;
  /**
   * The client reference
   * @type {string}
   * @memberof CommandResponse
   */
  clientRef: string;
  /**
   * The metadata
   * @type {CommandMetadata}
   * @memberof CommandResponse
   */
  metadata: CommandMetadata;
  /**
   * The event
   * @type {CommandEvent}
   * @memberof CommandResponse
   */
  event: CommandEvent;
  /**
   * The protocol
   * @type {string}
   * @memberof CommandResponse
   */
  protocol: string;
  /**
   * The date and time the command response was received
   * @type {string}
   * @memberof CommandResponse
   */
  receivedAt: string;
}

export const CommandResponseTypeEnum = {
  ClearChargingProfile: 'ClearChargingProfile',
  SetChargingProfile: 'SetChargingProfile',
} as const;

export type CommandResponseTypeEnum =
  (typeof CommandResponseTypeEnum)[keyof typeof CommandResponseTypeEnum];
export const CommandResponseResponseTypeEnum = {
  Res: 'RES',
  Err: 'ERR',
} as const;

export type CommandResponseResponseTypeEnum =
  (typeof CommandResponseResponseTypeEnum)[keyof typeof CommandResponseResponseTypeEnum];

/**
 *
 * @export
 * @interface CompositeScheduleDto
 */
export interface CompositeScheduleDto {
  /**
   * Status of the request. The Charge Point will indicate if it was able to process the request
   * @type {string}
   * @memberof CompositeScheduleDto
   */
  status: CompositeScheduleDtoStatusEnum;
  /**
   * The charging schedule contained in this notification applies to a Connector.
   * @type {number}
   * @memberof CompositeScheduleDto
   */
  connectorId: number;
  /**
   * Time. Periods contained in the charging profile are relative to this point in time. If status is \"Rejected\", this field may be absent.
   * @type {string}
   * @memberof CompositeScheduleDto
   */
  scheduleStart?: string;
  /**
   * Planned Composite Charging Schedule, the energy consumption over time. Always relative to ScheduleStart. If status is \"Rejected\", this field may be absent.
   * @type {ChargeScheduleDto}
   * @memberof CompositeScheduleDto
   */
  chargingSchedule?: ChargeScheduleDto;
}

export const CompositeScheduleDtoStatusEnum = {
  Accepted: 'Accepted',
  Rejected: 'Rejected',
} as const;

export type CompositeScheduleDtoStatusEnum =
  (typeof CompositeScheduleDtoStatusEnum)[keyof typeof CompositeScheduleDtoStatusEnum];

/**
 *
 * @export
 * @interface ConnectedChargeState
 */
export interface ConnectedChargeState {
  /**
   * The vehicle battery capacity in kWh
   * @type {number}
   * @memberof ConnectedChargeState
   */
  batteryCapacity: number;
  /**
   * The vehicle battery level in percent
   * @type {number}
   * @memberof ConnectedChargeState
   */
  batteryLevelPercent: number | null;
  /**
   * The vehicle charge limit in percent
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeLimitPercent: number | null;
  /**
   * The vehicle charge rate in kW
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeRate: number | null;
  /**
   * The vehicle charge time remaining in minutes
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeTimeRemaining: number | null;
  /**
   * The vehicle charging status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isCharging: boolean | null;
  /**
   * The vehicle fully charged status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isFullyCharged: boolean | null;
  /**
   * The vehicle plugged in status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isPluggedIn: boolean | null;
  /**
   * The vehicle last updated timestamp
   * @type {string}
   * @memberof ConnectedChargeState
   */
  lastUpdated: string | null;
  /**
   * The vehicle max current in A
   * @type {number}
   * @memberof ConnectedChargeState
   */
  maxCurrent: number | null;
  /**
   * The vehicle power delivery state
   * @type {string}
   * @memberof ConnectedChargeState
   */
  powerDeliveryState: ConnectedChargeStatePowerDeliveryStateEnum | null;
  /**
   * The vehicle range in km
   * @type {number}
   * @memberof ConnectedChargeState
   */
  range: number | null;
}

export const ConnectedChargeStatePowerDeliveryStateEnum = {
  Unplugged: 'UNPLUGGED',
  PluggedInNoPower: 'PLUGGED_IN:NO_POWER',
  PluggedInStopped: 'PLUGGED_IN:STOPPED',
  PluggedInComplete: 'PLUGGED_IN:COMPLETE',
  PluggedInCharging: 'PLUGGED_IN:CHARGING',
  Unknown: 'UNKNOWN',
  PluggedInInitializing: 'PLUGGED_IN:INITIALIZING',
  PluggedInFault: 'PLUGGED_IN:FAULT',
} as const;

export type ConnectedChargeStatePowerDeliveryStateEnum =
  (typeof ConnectedChargeStatePowerDeliveryStateEnum)[keyof typeof ConnectedChargeStatePowerDeliveryStateEnum];

/**
 *
 * @export
 * @interface ConnectedStatefulVehicleDto
 */
export interface ConnectedStatefulVehicleDto {
  /**
   * The vehicle ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  id: string;
  /**
   * The enode user ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The enode vehicle ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  enodeVehicleId?: string | null;
  /**
   * The vehicle information
   * @type {VehicleInformation}
   * @memberof ConnectedStatefulVehicleDto
   */
  vehicleInformation: VehicleInformation;
  /**
   * The vehicle charge state
   * @type {ConnectedChargeState}
   * @memberof ConnectedStatefulVehicleDto
   */
  chargeState: ConnectedChargeState;
  /**
   * The vehicle interventions
   * @type {InterventionDto}
   * @memberof ConnectedStatefulVehicleDto
   */
  interventions: InterventionDto;
}
/**
 *
 * @export
 * @interface CreateFlexRequestDto
 */
export interface CreateFlexRequestDto {
  /**
   * The date and time the flexibility request was requested
   * @type {string}
   * @memberof CreateFlexRequestDto
   */
  requestedAt: string;
  /**
   * The date and time the flexibility request will start
   * @type {string}
   * @memberof CreateFlexRequestDto
   */
  startAt: string;
  /**
   * The date and time the flexibility request will end
   * @type {string}
   * @memberof CreateFlexRequestDto
   */
  endAt: string;
  /**
   * The direction of the flexibility request
   * @type {string}
   * @memberof CreateFlexRequestDto
   */
  direction: CreateFlexRequestDtoDirectionEnum;
  /**
   * The limit of the flexibility request
   * @type {FlexRequestLimitDto}
   * @memberof CreateFlexRequestDto
   */
  limit?: FlexRequestLimitDto;
  /**
   * Details of the provider which originated this flex request
   * @type {FlexRequestProviderDto}
   * @memberof CreateFlexRequestDto
   */
  provider?: FlexRequestProviderDto;
  /**
   * For a Programme-based flex request, the id of the message which triggered the request
   * @type {string}
   * @memberof CreateFlexRequestDto
   */
  triggerMessageId?: string;
}

export const CreateFlexRequestDtoDirectionEnum = {
  Increase: 'INCREASE',
  Reduce: 'REDUCE',
} as const;

export type CreateFlexRequestDtoDirectionEnum =
  (typeof CreateFlexRequestDtoDirectionEnum)[keyof typeof CreateFlexRequestDtoDirectionEnum];

/**
 *
 * @export
 * @interface CreateFlexibilityRequestForProgramme200Response
 */
export interface CreateFlexibilityRequestForProgramme200Response {
  /**
   *
   * @type {Array<string>}
   * @memberof CreateFlexibilityRequestForProgramme200Response
   */
  successPpids?: Array<string>;
  /**
   *
   * @type {Array<string>}
   * @memberof CreateFlexibilityRequestForProgramme200Response
   */
  failedPpids?: Array<string>;
}
/**
 *
 * @export
 * @interface CreateVehicleLinkRequestDto
 */
export interface CreateVehicleLinkRequestDto {
  /**
   *
   * @type {CreateVehicleRequestDto}
   * @memberof CreateVehicleLinkRequestDto
   */
  vehicle: CreateVehicleRequestDto;
  /**
   *
   * @type {Array<VehicleIntentEntryDto>}
   * @memberof CreateVehicleLinkRequestDto
   */
  intents: Array<VehicleIntentEntryDto>;
}
/**
 *
 * @export
 * @interface CreateVehicleRequestDto
 */
export interface CreateVehicleRequestDto {
  /**
   * Vehicle information
   * @type {ExtendedVehicleInformation}
   * @memberof CreateVehicleRequestDto
   */
  vehicleInformation?: ExtendedVehicleInformation;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof CreateVehicleRequestDto
   */
  chargeState: GenericChargeState;
  /**
   * The vehicle user ID
   * @type {string}
   * @memberof CreateVehicleRequestDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof CreateVehicleRequestDto
   */
  enodeVehicleId?: string | null;
}
/**
 *
 * @export
 * @interface Data
 */
export interface Data {
  /**
   *
   * @type {string}
   * @memberof Data
   */
  uid?: string;
  /**
   *
   * @type {number}
   * @memberof Data
   */
  start_day: DataStartDayEnum;
  /**
   *
   * @type {string}
   * @memberof Data
   */
  start_time: string;
  /**
   *
   * @type {number}
   * @memberof Data
   */
  end_day: DataEndDayEnum;
  /**
   *
   * @type {string}
   * @memberof Data
   */
  end_time: string;
  /**
   *
   * @type {ChargeScheduleStatus}
   * @memberof Data
   */
  status: ChargeScheduleStatus;
}

export const DataStartDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type DataStartDayEnum =
  (typeof DataStartDayEnum)[keyof typeof DataStartDayEnum];
export const DataEndDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type DataEndDayEnum =
  (typeof DataEndDayEnum)[keyof typeof DataEndDayEnum];

/**
 *
 * @export
 * @interface DelegatedControlChargingStationResponseDto
 */
export interface DelegatedControlChargingStationResponseDto {
  /**
   * The charging station ID
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  id: string;
  /**
   * The charging station PPID
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  ppid: string;
  /**
   * The status of the charging station
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  status?: DelegatedControlChargingStationResponseDtoStatusEnum;
  /**
   * When the current status became effective
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  statusEffectiveFrom?: string;
  /**
   * The charging station creation date
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  createdAt: string;
  /**
   *
   * @type {Array<VehicleLinkResponseDto>}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  vehicleLinks?: Array<VehicleLinkResponseDto>;
  /**
   * The third party manager provider ID
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDto
   */
  thirdPartyManagerProviderId?: string | null;
}

export const DelegatedControlChargingStationResponseDtoStatusEnum = {
  Candidate: 'CANDIDATE',
  Pending: 'PENDING',
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  Unknown: 'UNKNOWN',
} as const;

export type DelegatedControlChargingStationResponseDtoStatusEnum =
  (typeof DelegatedControlChargingStationResponseDtoStatusEnum)[keyof typeof DelegatedControlChargingStationResponseDtoStatusEnum];

/**
 *
 * @export
 * @interface DelegatedControlChargingStationSearchCriteriaDto
 */
export interface DelegatedControlChargingStationSearchCriteriaDto {
  /**
   *
   * @type {string}
   * @memberof DelegatedControlChargingStationSearchCriteriaDto
   */
  status?: DelegatedControlChargingStationSearchCriteriaDtoStatusEnum;
  /**
   *
   * @type {boolean}
   * @memberof DelegatedControlChargingStationSearchCriteriaDto
   */
  hasVehiclePluggedIn?: boolean;
  /**
   *
   * @type {string}
   * @memberof DelegatedControlChargingStationSearchCriteriaDto
   */
  providerId?: string;
}

export const DelegatedControlChargingStationSearchCriteriaDtoStatusEnum = {
  Candidate: 'CANDIDATE',
  Pending: 'PENDING',
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  Unknown: 'UNKNOWN',
} as const;

export type DelegatedControlChargingStationSearchCriteriaDtoStatusEnum =
  (typeof DelegatedControlChargingStationSearchCriteriaDtoStatusEnum)[keyof typeof DelegatedControlChargingStationSearchCriteriaDtoStatusEnum];

/**
 *
 * @export
 * @interface DelegatedControlChargingStationSearchMetadataDto
 */
export interface DelegatedControlChargingStationSearchMetadataDto {
  /**
   *
   * @type {DelegatedControlChargingStationSearchMetadataDtoPagination}
   * @memberof DelegatedControlChargingStationSearchMetadataDto
   */
  pagination: DelegatedControlChargingStationSearchMetadataDtoPagination;
  /**
   *
   * @type {DelegatedControlChargingStationSearchCriteriaDto}
   * @memberof DelegatedControlChargingStationSearchMetadataDto
   */
  criteria: DelegatedControlChargingStationSearchCriteriaDto;
}
/**
 * Pagination information
 * @export
 * @interface DelegatedControlChargingStationSearchMetadataDtoPagination
 */
export interface DelegatedControlChargingStationSearchMetadataDtoPagination {
  /**
   *
   * @type {number}
   * @memberof DelegatedControlChargingStationSearchMetadataDtoPagination
   */
  pages?: number;
  /**
   *
   * @type {number}
   * @memberof DelegatedControlChargingStationSearchMetadataDtoPagination
   */
  total?: number;
  /**
   *
   * @type {number}
   * @memberof DelegatedControlChargingStationSearchMetadataDtoPagination
   */
  page?: number;
  /**
   *
   * @type {number}
   * @memberof DelegatedControlChargingStationSearchMetadataDtoPagination
   */
  itemsPerPage?: number;
}
/**
 *
 * @export
 * @interface DelegatedControlChargingStationSearchResponseDto
 */
export interface DelegatedControlChargingStationSearchResponseDto {
  /**
   *
   * @type {Array<DelegatedControlChargingStationResponseDto>}
   * @memberof DelegatedControlChargingStationSearchResponseDto
   */
  data: Array<DelegatedControlChargingStationResponseDto>;
  /**
   *
   * @type {DelegatedControlChargingStationSearchMetadataDto}
   * @memberof DelegatedControlChargingStationSearchResponseDto
   */
  metadata: DelegatedControlChargingStationSearchMetadataDto;
}
/**
 *
 * @export
 * @interface EffectiveChargeSchedulesDto
 */
export interface EffectiveChargeSchedulesDto {
  /**
   * The start date and time of the effective charge schedules
   * @type {string}
   * @memberof EffectiveChargeSchedulesDto
   */
  from: string;
  /**
   * The end date and time of the effective charge schedules
   * @type {string}
   * @memberof EffectiveChargeSchedulesDto
   */
  to: string;
  /**
   * The ppids for which the effective charge schedules should be retrieved
   * @type {Array<string>}
   * @memberof EffectiveChargeSchedulesDto
   */
  ppids: Array<string>;
}
/**
 *
 * @export
 * @interface EffectiveChargeSchedulesResponseDto
 */
export interface EffectiveChargeSchedulesResponseDto {
  /**
   * the ppid that the effective charge schedules are for
   * @type {string}
   * @memberof EffectiveChargeSchedulesResponseDto
   */
  ppid: string;
  /**
   * the effective charge schedules for the given ppid
   * @type {Array<EffectiveScheduleDto>}
   * @memberof EffectiveChargeSchedulesResponseDto
   */
  effectiveSchedules: Array<EffectiveScheduleDto>;
}
/**
 *
 * @export
 * @interface EffectiveScheduleDto
 */
export interface EffectiveScheduleDto {
  /**
   * the period ISO string of the effective charge schedule
   * @type {string}
   * @memberof EffectiveScheduleDto
   */
  period: string;
}
/**
 *
 * @export
 * @interface EnergyOfferStatus
 */
export interface EnergyOfferStatus {
  /**
   * Whether the charging station is offering energy
   * @type {boolean}
   * @memberof EnergyOfferStatus
   */
  isOfferingEnergy: boolean;
  /**
   * The date and time the reason for the offering energy status will change
   * @type {string}
   * @memberof EnergyOfferStatus
   */
  until: string | null;
  /**
   * The reason the for the offering energy
   * @type {string}
   * @memberof EnergyOfferStatus
   */
  reason: EnergyOfferStatusReasonEnum;
  /**
   *
   * @type {boolean}
   * @memberof EnergyOfferStatus
   */
  randomDelay?: boolean | null;
}

export const EnergyOfferStatusReasonEnum = {
  ChargeSchedule: 'CHARGE_SCHEDULE',
  FlexRequest: 'FLEX_REQUEST',
  ChargeNow: 'CHARGE_NOW',
  Unknown: 'UNKNOWN',
} as const;

export type EnergyOfferStatusReasonEnum =
  (typeof EnergyOfferStatusReasonEnum)[keyof typeof EnergyOfferStatusReasonEnum];

/**
 *
 * @export
 * @interface EnergyOfferStatusResponse
 */
export interface EnergyOfferStatusResponse {
  /**
   * The PPID of the charging station
   * @type {string}
   * @memberof EnergyOfferStatusResponse
   */
  ppid: string;
  /**
   * The ID of the evse
   * @type {number}
   * @memberof EnergyOfferStatusResponse
   */
  evseId: number;
  /**
   * The status of the energy offer
   * @type {EnergyOfferStatus}
   * @memberof EnergyOfferStatusResponse
   */
  energyOfferStatus: EnergyOfferStatus;
}
/**
 *
 * @export
 * @interface EnrolmentRequestDto
 */
export interface EnrolmentRequestDto {
  /**
   * The provider name
   * @type {string}
   * @memberof EnrolmentRequestDto
   */
  providerName?: string | null;
  /**
   * The initial status
   * @type {string}
   * @memberof EnrolmentRequestDto
   */
  status?: EnrolmentRequestDtoStatusEnum;
  /**
   * The charging station\'s 13 digit mpan
   * @type {string}
   * @memberof EnrolmentRequestDto
   */
  mpan?: string;
  /**
   * The charging station\'s postcode
   * @type {string}
   * @memberof EnrolmentRequestDto
   */
  postcode?: string;
}

export const EnrolmentRequestDtoStatusEnum = {
  Candidate: 'CANDIDATE',
  Pending: 'PENDING',
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  Unknown: 'UNKNOWN',
} as const;

export type EnrolmentRequestDtoStatusEnum =
  (typeof EnrolmentRequestDtoStatusEnum)[keyof typeof EnrolmentRequestDtoStatusEnum];

/**
 *
 * @export
 * @interface EvseResponse
 */
export interface EvseResponse {
  /**
   * The door of the evse
   * @type {string}
   * @memberof EvseResponse
   */
  door: string;
  /**
   * The ocppEvseId of the evse
   * @type {number}
   * @memberof EvseResponse
   */
  ocppEvseId: number;
}
/**
 *
 * @export
 * @interface ExtendedVehicleInformation
 */
export interface ExtendedVehicleInformation {
  /**
   * The vehicle make
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  brand?: string | null;
  /**
   * The vehicle model
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  model?: string | null;
  /**
   * The vehicle model variant
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  modelVariant?: string | null;
  /**
   * The vehicle registration plate
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  vehicleRegistrationPlate?: string | null;
  /**
   * The vehicle display name
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  displayName?: string | null;
  /**
   * The vehicle EV database ID
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  evDatabaseId?: string | null;
  /**
   * The vehicle VIN
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  vin?: string | null;
}
/**
 *
 * @export
 * @interface ExtendedVehicleLinkResponseDto
 */
export interface ExtendedVehicleLinkResponseDto {
  /**
   *
   * @type {string}
   * @memberof ExtendedVehicleLinkResponseDto
   */
  id: string;
  /**
   *
   * @type {boolean}
   * @memberof ExtendedVehicleLinkResponseDto
   */
  isPluggedInToThisCharger: boolean;
  /**
   *
   * @type {VehicleLinkResponseDtoVehicle}
   * @memberof ExtendedVehicleLinkResponseDto
   */
  vehicle: VehicleLinkResponseDtoVehicle;
  /**
   *
   * @type {VehicleIntentsResponseDto}
   * @memberof ExtendedVehicleLinkResponseDto
   */
  intents: VehicleIntentsResponseDto;
  /**
   *
   * @type {VehicleChargeInfoDto}
   * @memberof ExtendedVehicleLinkResponseDto
   */
  currentIntent: VehicleChargeInfoDto | null;
}
/**
 *
 * @export
 * @interface ExtendedVehicleLinksResponseDto
 */
export interface ExtendedVehicleLinksResponseDto {
  /**
   *
   * @type {Array<ExtendedVehicleLinkResponseDto>}
   * @memberof ExtendedVehicleLinksResponseDto
   */
  data: Array<ExtendedVehicleLinkResponseDto>;
}
/**
 *
 * @export
 * @interface FlexRequestLimitDto
 */
export interface FlexRequestLimitDto {
  /**
   * The unit of the limit
   * @type {string}
   * @memberof FlexRequestLimitDto
   */
  unit: FlexRequestLimitDtoUnitEnum;
  /**
   * The value of the limit
   * @type {number}
   * @memberof FlexRequestLimitDto
   */
  value: number;
}

export const FlexRequestLimitDtoUnitEnum = {
  Amp: 'AMP',
  Kw: 'KW',
} as const;

export type FlexRequestLimitDtoUnitEnum =
  (typeof FlexRequestLimitDtoUnitEnum)[keyof typeof FlexRequestLimitDtoUnitEnum];

/**
 *
 * @export
 * @interface FlexRequestProviderDto
 */
export interface FlexRequestProviderDto {
  /**
   * The name of the provider
   * @type {string}
   * @memberof FlexRequestProviderDto
   */
  name: string;
  /**
   * The provider\'s id for this flex request
   * @type {string}
   * @memberof FlexRequestProviderDto
   */
  externalFlexRequestId: string;
}
/**
 *
 * @export
 * @interface FlexRequestResponse
 */
export interface FlexRequestResponse {
  /**
   * The ID of the flexibility request
   * @type {string}
   * @memberof FlexRequestResponse
   */
  id: string;
  /**
   *
   * @type {ChargingStationResponseDto}
   * @memberof FlexRequestResponse
   */
  chargingStation: ChargingStationResponseDto;
  /**
   * The date and time the flexibility request was requested
   * @type {string}
   * @memberof FlexRequestResponse
   */
  requestedAt: string;
  /**
   * The date and time the flexibility request will start
   * @type {string}
   * @memberof FlexRequestResponse
   */
  startAt: string;
  /**
   * The date and time the flexibility request will end
   * @type {string}
   * @memberof FlexRequestResponse
   */
  endAt?: string;
  /**
   * The direction of the flexibility request
   * @type {string}
   * @memberof FlexRequestResponse
   */
  direction: FlexRequestResponseDirectionEnum;
  /**
   *
   * @type {FlexRequestResponseLimit}
   * @memberof FlexRequestResponse
   */
  limit: FlexRequestResponseLimit;
  /**
   * The date and time the flexibility request was deleted (cancelled) if applicable
   * @type {string}
   * @memberof FlexRequestResponse
   */
  deletedAt?: string | null;
}

export const FlexRequestResponseDirectionEnum = {
  Increase: 'INCREASE',
  Reduce: 'REDUCE',
} as const;

export type FlexRequestResponseDirectionEnum =
  (typeof FlexRequestResponseDirectionEnum)[keyof typeof FlexRequestResponseDirectionEnum];

/**
 * The limit of the flexibility request
 * @export
 * @interface FlexRequestResponseLimit
 */
export interface FlexRequestResponseLimit {
  /**
   *
   * @type {string}
   * @memberof FlexRequestResponseLimit
   */
  unit?: FlexRequestResponseLimitUnitEnum;
  /**
   *
   * @type {number}
   * @memberof FlexRequestResponseLimit
   */
  value?: number;
}

export const FlexRequestResponseLimitUnitEnum = {
  Amp: 'AMP',
  Kw: 'KW',
} as const;

export type FlexRequestResponseLimitUnitEnum =
  (typeof FlexRequestResponseLimitUnitEnum)[keyof typeof FlexRequestResponseLimitUnitEnum];

/**
 *
 * @export
 * @interface FlexRequestSearchResponse
 */
export interface FlexRequestSearchResponse {
  /**
   * The list of matching flex requests
   * @type {Array<FlexRequestResponse>}
   * @memberof FlexRequestSearchResponse
   */
  data: Array<FlexRequestResponse>;
}
/**
 *
 * @export
 * @interface GenericChargeState
 */
export interface GenericChargeState {
  /**
   * The vehicle battery capacity in kWh
   * @type {number}
   * @memberof GenericChargeState
   */
  batteryCapacity: number;
}
/**
 *
 * @export
 * @interface GenericStatefulVehicleDto
 */
export interface GenericStatefulVehicleDto {
  /**
   * The vehicle ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  id: string;
  /**
   * The enode user ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  enodeVehicleId?: string | null;
  /**
   * The vehicle information
   * @type {VehicleInformation}
   * @memberof GenericStatefulVehicleDto
   */
  vehicleInformation: VehicleInformation;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof GenericStatefulVehicleDto
   */
  chargeState: GenericChargeState;
}
/**
 *
 * @export
 * @interface GetApi3ChargeSchedules
 */
export interface GetApi3ChargeSchedules {
  /**
   *
   * @type {Array<API3ChargeSchedule>}
   * @memberof GetApi3ChargeSchedules
   */
  data: Array<API3ChargeSchedule>;
  /**
   *
   * @type {Meta}
   * @memberof GetApi3ChargeSchedules
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface GetChargeSchedules200ResponseValueInner
 */
export interface GetChargeSchedules200ResponseValueInner {
  /**
   *
   * @type {number}
   * @memberof GetChargeSchedules200ResponseValueInner
   */
  startDay: GetChargeSchedules200ResponseValueInnerStartDayEnum;
  /**
   *
   * @type {number}
   * @memberof GetChargeSchedules200ResponseValueInner
   */
  endDay: GetChargeSchedules200ResponseValueInnerEndDayEnum;
  /**
   *
   * @type {string}
   * @memberof GetChargeSchedules200ResponseValueInner
   */
  startTime: string;
  /**
   *
   * @type {string}
   * @memberof GetChargeSchedules200ResponseValueInner
   */
  endTime: string;
}

export const GetChargeSchedules200ResponseValueInnerStartDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type GetChargeSchedules200ResponseValueInnerStartDayEnum =
  (typeof GetChargeSchedules200ResponseValueInnerStartDayEnum)[keyof typeof GetChargeSchedules200ResponseValueInnerStartDayEnum];
export const GetChargeSchedules200ResponseValueInnerEndDayEnum = {
  NUMBER_1: 1,
  NUMBER_2: 2,
  NUMBER_3: 3,
  NUMBER_4: 4,
  NUMBER_5: 5,
  NUMBER_6: 6,
  NUMBER_7: 7,
} as const;

export type GetChargeSchedules200ResponseValueInnerEndDayEnum =
  (typeof GetChargeSchedules200ResponseValueInnerEndDayEnum)[keyof typeof GetChargeSchedules200ResponseValueInnerEndDayEnum];

/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface InterventionDto
 */
export interface InterventionDto {
  /**
   * The endpoint to extract all interventions
   * @type {string}
   * @memberof InterventionDto
   */
  all: string;
  /**
   * The individual interventions for charge state
   * @type {Array<string>}
   * @memberof InterventionDto
   */
  chargeState: Array<string>;
  /**
   * The individual interventions for vehicle information
   * @type {Array<string>}
   * @memberof InterventionDto
   */
  information: Array<string>;
}
/**
 *
 * @export
 * @interface Meta
 */
export interface Meta {
  /**
   *
   * @type {object}
   * @memberof Meta
   */
  pagination: object;
}
/**
 *
 * @export
 * @interface PaginatedSchedulesDto
 */
export interface PaginatedSchedulesDto {
  /**
   *
   * @type {DelegatedControlChargingStationSearchMetadataDtoPagination}
   * @memberof PaginatedSchedulesDto
   */
  pagination: DelegatedControlChargingStationSearchMetadataDtoPagination;
  /**
   * The charge schedules for the given ppids
   * @type {Array<ChargeScheduleDto>}
   * @memberof PaginatedSchedulesDto
   */
  schedules: Array<ChargeScheduleDto>;
}
/**
 *
 * @export
 * @interface RewardInfoDto
 */
export interface RewardInfoDto {
  /**
   * The energy delivered in the session that is rewardable. If 0 is returned, the session is not eligible for rewards
   * @type {number}
   * @memberof RewardInfoDto
   */
  rewardableEnergyKwh: number;
  /**
   * The vehicle charged during the session
   * @type {string}
   * @memberof RewardInfoDto
   */
  vehicleId: string | null;
}
/**
 *
 * @export
 * @interface RewardPointsDto
 */
export interface RewardPointsDto {
  /**
   * The number of points the charge is worth
   * @type {number}
   * @memberof RewardPointsDto
   */
  points: number;
  /**
   *
   * @type {string}
   * @memberof RewardPointsDto
   */
  reason: RewardPointsDtoReasonEnum;
}

export const RewardPointsDtoReasonEnum = {
  Eligible: 'ELIGIBLE',
  IneligibleChargeOverride: 'INELIGIBLE_CHARGE_OVERRIDE',
  IneligibleInsufficientGridImport: 'INELIGIBLE_INSUFFICIENT_GRID_IMPORT',
  IneligibleNotEnrolled: 'INELIGIBLE_NOT_ENROLLED',
  IneligibleSolarOnly: 'INELIGIBLE_SOLAR_ONLY',
} as const;

export type RewardPointsDtoReasonEnum =
  (typeof RewardPointsDtoReasonEnum)[keyof typeof RewardPointsDtoReasonEnum];

/**
 *
 * @export
 * @interface SetDelegatedControlIntentsResponseDto
 */
export interface SetDelegatedControlIntentsResponseDto {
  /**
   *
   * @type {string}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  delegatedControlChargingStationVehicleId: string;
  /**
   *
   * @type {Array<VehicleIntentEntryDto>}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  intentDetails: Array<VehicleIntentEntryDto>;
  /**
   *
   * @type {number}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  maxPrice: number | null;
  /**
   *
   * @type {string}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof SetDelegatedControlIntentsResponseDto
   */
  updatedAt: string;
}
/**
 *
 * @export
 * @interface TriggerCommandRequest
 */
export interface TriggerCommandRequest {
  /**
   * A list of PPIDs to send the command to
   * @type {Array<string>}
   * @memberof TriggerCommandRequest
   */
  ppids: Array<string>;
  /**
   * The command to send to the charging stations
   * @type {string}
   * @memberof TriggerCommandRequest
   */
  command: TriggerCommandRequestCommandEnum;
}

export const TriggerCommandRequestCommandEnum = {
  Heartbeat: 'Heartbeat',
} as const;

export type TriggerCommandRequestCommandEnum =
  (typeof TriggerCommandRequestCommandEnum)[keyof typeof TriggerCommandRequestCommandEnum];

/**
 *
 * @export
 * @interface TxProfileInfoDto
 */
export interface TxProfileInfoDto {
  /**
   * ocpp TxProfile charging profile
   * @type {object}
   * @memberof TxProfileInfoDto
   */
  profile: object;
}
/**
 *
 * @export
 * @interface UpdateFlexRequestDto
 */
export interface UpdateFlexRequestDto {
  /**
   * The date and time the flexibility request will start
   * @type {string}
   * @memberof UpdateFlexRequestDto
   */
  startAt: string;
  /**
   * The date and time the flexibility request will end
   * @type {string}
   * @memberof UpdateFlexRequestDto
   */
  endAt: string;
}
/**
 * @type UpdateVehicleById200Response
 * @export
 */
export type UpdateVehicleById200Response =
  | ConnectedStatefulVehicleDto
  | GenericStatefulVehicleDto;

/**
 *
 * @export
 * @interface UpdateVehicleLinkRequestDto
 */
export interface UpdateVehicleLinkRequestDto {
  /**
   *
   * @type {boolean}
   * @memberof UpdateVehicleLinkRequestDto
   */
  isPluggedInToThisCharger?: boolean;
  /**
   *
   * @type {UpdateVehicleRequestDto}
   * @memberof UpdateVehicleLinkRequestDto
   */
  vehicle?: UpdateVehicleRequestDto;
}
/**
 *
 * @export
 * @interface UpdateVehicleRequestDto
 */
export interface UpdateVehicleRequestDto {
  /**
   * Vehicle information
   * @type {ExtendedVehicleInformation}
   * @memberof UpdateVehicleRequestDto
   */
  vehicleInformation?: ExtendedVehicleInformation;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof UpdateVehicleRequestDto
   */
  chargeState?: GenericChargeState;
  /**
   * The vehicle user ID
   * @type {string}
   * @memberof UpdateVehicleRequestDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof UpdateVehicleRequestDto
   */
  enodeVehicleId?: string | null;
}
/**
 *
 * @export
 * @interface ValidateChargingProfileRequestDto
 */
export interface ValidateChargingProfileRequestDto {
  /**
   * Transaction id
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  transactionId?: string;
  /**
   * The stack level
   * @type {number}
   * @memberof ValidateChargingProfileRequestDto
   */
  stackLevel: number;
  /**
   * The charging profile purpose
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  chargingProfilePurpose: ValidateChargingProfileRequestDtoChargingProfilePurposeEnum;
  /**
   * The charging profile kind
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  chargingProfileKind: ValidateChargingProfileRequestDtoChargingProfileKindEnum;
  /**
   * The recurrency kind
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  recurrencyKind?: ValidateChargingProfileRequestDtoRecurrencyKindEnum;
  /**
   * The valid from date
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  validFrom?: string;
  /**
   * The valid to date
   * @type {string}
   * @memberof ValidateChargingProfileRequestDto
   */
  validTo?: string;
  /**
   * The charging schedule
   * @type {ChargingScheduleDto}
   * @memberof ValidateChargingProfileRequestDto
   */
  chargingSchedule: ChargingScheduleDto;
}

export const ValidateChargingProfileRequestDtoChargingProfilePurposeEnum = {
  ChargePointMaxProfile: 'ChargePointMaxProfile',
  TxDefaultProfile: 'TxDefaultProfile',
  TxProfile: 'TxProfile',
} as const;

export type ValidateChargingProfileRequestDtoChargingProfilePurposeEnum =
  (typeof ValidateChargingProfileRequestDtoChargingProfilePurposeEnum)[keyof typeof ValidateChargingProfileRequestDtoChargingProfilePurposeEnum];
export const ValidateChargingProfileRequestDtoChargingProfileKindEnum = {
  Absolute: 'Absolute',
  Recurring: 'Recurring',
  Relative: 'Relative',
} as const;

export type ValidateChargingProfileRequestDtoChargingProfileKindEnum =
  (typeof ValidateChargingProfileRequestDtoChargingProfileKindEnum)[keyof typeof ValidateChargingProfileRequestDtoChargingProfileKindEnum];
export const ValidateChargingProfileRequestDtoRecurrencyKindEnum = {
  Daily: 'Daily',
  Weekly: 'Weekly',
} as const;

export type ValidateChargingProfileRequestDtoRecurrencyKindEnum =
  (typeof ValidateChargingProfileRequestDtoRecurrencyKindEnum)[keyof typeof ValidateChargingProfileRequestDtoRecurrencyKindEnum];

/**
 *
 * @export
 * @interface ValidateChargingProfileResponseDto
 */
export interface ValidateChargingProfileResponseDto {
  /**
   * The validity of the charging profile that was presented for validation
   * @type {boolean}
   * @memberof ValidateChargingProfileResponseDto
   */
  valid: boolean;
  /**
   * The reason the charging profile is considered invalid
   * @type {string}
   * @memberof ValidateChargingProfileResponseDto
   */
  reason?: string;
}
/**
 *
 * @export
 * @interface VehicleChargeInfoDto
 */
export interface VehicleChargeInfoDto {
  /**
   *
   * @type {boolean}
   * @memberof VehicleChargeInfoDto
   */
  canMeetTarget: boolean;
  /**
   *
   * @type {string}
   * @memberof VehicleChargeInfoDto
   */
  cannotMeetTargetReason: VehicleChargeInfoDtoCannotMeetTargetReasonEnum | null;
  /**
   *
   * @type {ChargeDetailDto}
   * @memberof VehicleChargeInfoDto
   */
  chargeDetail: ChargeDetailDto;
  /**
   *
   * @type {VehicleChargeInfoDtoChargingStation}
   * @memberof VehicleChargeInfoDto
   */
  chargingStation: VehicleChargeInfoDtoChargingStation;
}

export const VehicleChargeInfoDtoCannotMeetTargetReasonEnum = {
  Price: 'PRICE',
  Time: 'TIME',
} as const;

export type VehicleChargeInfoDtoCannotMeetTargetReasonEnum =
  (typeof VehicleChargeInfoDtoCannotMeetTargetReasonEnum)[keyof typeof VehicleChargeInfoDtoCannotMeetTargetReasonEnum];

/**
 *
 * @export
 * @interface VehicleChargeInfoDtoChargingStation
 */
export interface VehicleChargeInfoDtoChargingStation {
  /**
   *
   * @type {string}
   * @memberof VehicleChargeInfoDtoChargingStation
   */
  ppid?: string;
}
/**
 *
 * @export
 * @interface VehicleChargingStationsResponseDto
 */
export interface VehicleChargingStationsResponseDto {
  /**
   * An array of charging station ppids that the vehicle is linked to
   * @type {Array<string>}
   * @memberof VehicleChargingStationsResponseDto
   */
  data: Array<string>;
}
/**
 *
 * @export
 * @interface VehicleInformation
 */
export interface VehicleInformation {
  /**
   * The vehicle make
   * @type {string}
   * @memberof VehicleInformation
   */
  brand?: string | null;
  /**
   * The vehicle model
   * @type {string}
   * @memberof VehicleInformation
   */
  model?: string | null;
  /**
   * The vehicle model variant
   * @type {string}
   * @memberof VehicleInformation
   */
  modelVariant?: string | null;
  /**
   * The vehicle registration plate
   * @type {string}
   * @memberof VehicleInformation
   */
  vehicleRegistrationPlate?: string | null;
  /**
   * The vehicle display name
   * @type {string}
   * @memberof VehicleInformation
   */
  displayName?: string | null;
  /**
   * The vehicle EV database ID
   * @type {string}
   * @memberof VehicleInformation
   */
  evDatabaseId?: string | null;
}
/**
 *
 * @export
 * @interface VehicleIntentEntryDto
 */
export interface VehicleIntentEntryDto {
  /**
   * Time by which the charging should be completed (HH:MM:SS format)
   * @type {string}
   * @memberof VehicleIntentEntryDto
   */
  chargeByTime: string;
  /**
   * Number of kWh to charge
   * @type {number}
   * @memberof VehicleIntentEntryDto
   */
  chargeKWh: number;
  /**
   * The day of the week for the charging intent. Valid values: MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY.
   * @type {string}
   * @memberof VehicleIntentEntryDto
   */
  dayOfWeek: VehicleIntentEntryDtoDayOfWeekEnum;
}

export const VehicleIntentEntryDtoDayOfWeekEnum = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type VehicleIntentEntryDtoDayOfWeekEnum =
  (typeof VehicleIntentEntryDtoDayOfWeekEnum)[keyof typeof VehicleIntentEntryDtoDayOfWeekEnum];

/**
 *
 * @export
 * @interface VehicleIntentsRequestDto
 */
export interface VehicleIntentsRequestDto {
  /**
   *
   * @type {Array<VehicleIntentEntryDto>}
   * @memberof VehicleIntentsRequestDto
   */
  intentDetails: Array<VehicleIntentEntryDto>;
  /**
   * The maximum price.
   * @type {number}
   * @memberof VehicleIntentsRequestDto
   */
  maxPrice?: number;
}
/**
 *
 * @export
 * @interface VehicleIntentsResponseDto
 */
export interface VehicleIntentsResponseDto {
  /**
   * The vehicle ID
   * @type {string}
   * @memberof VehicleIntentsResponseDto
   */
  id: string;
  /**
   * The maximum price.
   * @type {number}
   * @memberof VehicleIntentsResponseDto
   */
  maxPrice: number | null;
  /**
   * The vehicle intents
   * @type {Array<VehicleIntentEntryDto>}
   * @memberof VehicleIntentsResponseDto
   */
  details: Array<VehicleIntentEntryDto>;
}
/**
 *
 * @export
 * @interface VehicleLinkResponseDto
 */
export interface VehicleLinkResponseDto {
  /**
   *
   * @type {string}
   * @memberof VehicleLinkResponseDto
   */
  id: string;
  /**
   *
   * @type {boolean}
   * @memberof VehicleLinkResponseDto
   */
  isPluggedInToThisCharger: boolean;
  /**
   *
   * @type {VehicleLinkResponseDtoVehicle}
   * @memberof VehicleLinkResponseDto
   */
  vehicle: VehicleLinkResponseDtoVehicle;
  /**
   *
   * @type {VehicleIntentsResponseDto}
   * @memberof VehicleLinkResponseDto
   */
  intents: VehicleIntentsResponseDto;
}
/**
 * @type VehicleLinkResponseDtoVehicle
 * @export
 */
export type VehicleLinkResponseDtoVehicle =
  | ConnectedStatefulVehicleDto
  | GenericStatefulVehicleDto;

/**
 * ChargeOverridesApi - axios parameter creator
 * @export
 */
export const ChargeOverridesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Removes any existing overrides that are in place for a given charging station (aka unit)
     * @summary Removes an override for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearChargingStationChargeOverrides: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('clearChargingStationChargeOverrides', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Override and schedules on the device (all evses) either indefinitely or until the specified end time
     * @summary Create an override for a given charger, that should be applied to all EVSEs
     * @param {string} ppid The PPID of the charging station
     * @param {ChargeOverrideRequest} chargeOverrideRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStationChargeOverride: async (
      ppid: string,
      chargeOverrideRequest: ChargeOverrideRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('createChargingStationChargeOverride', 'ppid', ppid);
      // verify required parameter 'chargeOverrideRequest' is not null or undefined
      assertParamExists(
        'createChargingStationChargeOverride',
        'chargeOverrideRequest',
        chargeOverrideRequest
      );
      const localVarPath = `/charging-stations/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargeOverrideRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Gets all overrides for a given charging station (All EVSEs)
     * @summary Get all overrides for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includePast] Include past overrides
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationChargeOverrides: async (
      ppid: string,
      includePast?: boolean,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationChargeOverrides', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includePast !== undefined) {
        localVarQueryParameter['includePast'] = includePast;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`
     * @summary Search for any active overrides in place
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchActiveOverrides: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('searchActiveOverrides', 'ppid', ppid);
      const localVarPath = `/charge-overrides`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ppid !== undefined) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeOverridesApi - functional programming interface
 * @export
 */
export const ChargeOverridesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargeOverridesApiAxiosParamCreator(configuration);
  return {
    /**
     * Removes any existing overrides that are in place for a given charging station (aka unit)
     * @summary Removes an override for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async clearChargingStationChargeOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.clearChargingStationChargeOverrides(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeOverridesApi.clearChargingStationChargeOverrides'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Override and schedules on the device (all evses) either indefinitely or until the specified end time
     * @summary Create an override for a given charger, that should be applied to all EVSEs
     * @param {string} ppid The PPID of the charging station
     * @param {ChargeOverrideRequest} chargeOverrideRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createChargingStationChargeOverride(
      ppid: string,
      chargeOverrideRequest: ChargeOverrideRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargeOverrideResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createChargingStationChargeOverride(
          ppid,
          chargeOverrideRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeOverridesApi.createChargingStationChargeOverride'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Gets all overrides for a given charging station (All EVSEs)
     * @summary Get all overrides for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includePast] Include past overrides
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationChargeOverrides(
      ppid: string,
      includePast?: boolean,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargeOverrideResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationChargeOverrides(
          ppid,
          includePast,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeOverridesApi.getChargingStationChargeOverrides'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`
     * @summary Search for any active overrides in place
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchActiveOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargeOverrideResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.searchActiveOverrides(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeOverridesApi.searchActiveOverrides']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeOverridesApi - factory interface
 * @export
 */
export const ChargeOverridesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeOverridesApiFp(configuration);
  return {
    /**
     * Removes any existing overrides that are in place for a given charging station (aka unit)
     * @summary Removes an override for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearChargingStationChargeOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .clearChargingStationChargeOverrides(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Override and schedules on the device (all evses) either indefinitely or until the specified end time
     * @summary Create an override for a given charger, that should be applied to all EVSEs
     * @param {string} ppid The PPID of the charging station
     * @param {ChargeOverrideRequest} chargeOverrideRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStationChargeOverride(
      ppid: string,
      chargeOverrideRequest: ChargeOverrideRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargeOverrideResponse>> {
      return localVarFp
        .createChargingStationChargeOverride(
          ppid,
          chargeOverrideRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Gets all overrides for a given charging station (All EVSEs)
     * @summary Get all overrides for a given charging station (All EVSEs)
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includePast] Include past overrides
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationChargeOverrides(
      ppid: string,
      includePast?: boolean,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargeOverrideResponse>> {
      return localVarFp
        .getChargingStationChargeOverrides(ppid, includePast, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`
     * @summary Search for any active overrides in place
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchActiveOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargeOverrideResponse>> {
      return localVarFp
        .searchActiveOverrides(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeOverridesApi - object-oriented interface
 * @export
 * @class ChargeOverridesApi
 * @extends {BaseAPI}
 */
export class ChargeOverridesApi extends BaseAPI {
  /**
   * Removes any existing overrides that are in place for a given charging station (aka unit)
   * @summary Removes an override for a given charging station (All EVSEs)
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeOverridesApi
   */
  public clearChargingStationChargeOverrides(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeOverridesApiFp(this.configuration)
      .clearChargingStationChargeOverrides(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Override and schedules on the device (all evses) either indefinitely or until the specified end time
   * @summary Create an override for a given charger, that should be applied to all EVSEs
   * @param {string} ppid The PPID of the charging station
   * @param {ChargeOverrideRequest} chargeOverrideRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeOverridesApi
   */
  public createChargingStationChargeOverride(
    ppid: string,
    chargeOverrideRequest: ChargeOverrideRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeOverridesApiFp(this.configuration)
      .createChargingStationChargeOverride(ppid, chargeOverrideRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Gets all overrides for a given charging station (All EVSEs)
   * @summary Get all overrides for a given charging station (All EVSEs)
   * @param {string} ppid The PPID of the charging station
   * @param {boolean} [includePast] Include past overrides
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeOverridesApi
   */
  public getChargingStationChargeOverrides(
    ppid: string,
    includePast?: boolean,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeOverridesApiFp(this.configuration)
      .getChargingStationChargeOverrides(ppid, includePast, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Search for any overrides based on a PPID. If search criteria is valid but no schedules are in effect then a 200 response is returned with an empty array body `[]`. Also if the services does not find an EVSE for the given criteria then return `[]`
   * @summary Search for any active overrides in place
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeOverridesApi
   */
  public searchActiveOverrides(ppid: string, options?: RawAxiosRequestConfig) {
    return ChargeOverridesApiFp(this.configuration)
      .searchActiveOverrides(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargeSchedulesApi - axios parameter creator
 * @export
 */
export const ChargeSchedulesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} from Start date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeScheduleControllerGetChargingStationsWithChargeSchedules: async (
      from: string,
      items?: number,
      page?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'chargeScheduleControllerGetChargingStationsWithChargeSchedules',
        'from',
        from
      );
      const localVarPath = `/charge-schedules/charging-stations`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (items !== undefined) {
        localVarQueryParameter['items'] = items;
      }

      if (page !== undefined) {
        localVarQueryParameter['page'] = page;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] = from;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete old charge schedules for the given ppid
     * @summary Delete old charge schedules
     * @param {string} ppid Accepts a single ppid to delete old charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteChargeScheduleHistory: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('deleteChargeScheduleHistory', 'ppid', ppid);
      const localVarPath = `/charge-schedules/{ppid}/history`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all active charge schedules for the given ppids
     * @summary Get all active charge schedules
     * @param {Array<string>} ppid Accepts comma separated ppid(s) to batch fetch their charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargeSchedules: async (
      ppid: Array<string>,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargeSchedules', 'ppid', ppid);
      const localVarPath = `/charge-schedules`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ppid) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get historical charge schedules for the given ppid
     * @summary Get historical charge schedules
     * @param {string} ppid Accepts a single ppid to fetch old charge schedules
     * @param {string} from Start date of the charge schedule
     * @param {string} to End date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargerChargeScheduleHistory: async (
      ppid: string,
      from: string,
      to: string,
      items?: number,
      page?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargerChargeScheduleHistory', 'ppid', ppid);
      // verify required parameter 'from' is not null or undefined
      assertParamExists('getChargerChargeScheduleHistory', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('getChargerChargeScheduleHistory', 'to', to);
      const localVarPath = `/charge-schedules/{ppid}/history`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (items !== undefined) {
        localVarQueryParameter['items'] = items;
      }

      if (page !== undefined) {
        localVarQueryParameter['page'] = page;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] = from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] = to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf
     * @summary Get composite charge schedule
     * @param {string} ppid
     * @param {number} [duration] The duration in seconds for which the composite schedule is requested
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCompositeChargeSchedule: async (
      ppid: string,
      duration?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getCompositeChargeSchedule', 'ppid', ppid);
      const localVarPath = `/charge-schedules/{ppid}/composite`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (duration !== undefined) {
        localVarQueryParameter['duration'] = duration;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get effective charge schedules for the given ppids in the time range
     * @summary Get effective charge schedules in time range
     * @param {EffectiveChargeSchedulesDto} effectiveChargeSchedulesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getEffectiveChargeSchedulesInTimeRange: async (
      effectiveChargeSchedulesDto: EffectiveChargeSchedulesDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'effectiveChargeSchedulesDto' is not null or undefined
      assertParamExists(
        'getEffectiveChargeSchedulesInTimeRange',
        'effectiveChargeSchedulesDto',
        effectiveChargeSchedulesDto
      );
      const localVarPath = `/charge-schedules/effective`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        effectiveChargeSchedulesDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Set the default charge schedule for a charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setDefaultChargeSchedule: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('setDefaultChargeSchedule', 'ppid', ppid);
      const localVarPath = `/charge-schedules/{ppid}/default`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeSchedulesApi - functional programming interface
 * @export
 */
export const ChargeSchedulesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargeSchedulesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} from Start date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeScheduleControllerGetChargingStationsWithChargeSchedules(
      from: string,
      items?: number,
      page?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeScheduleControllerGetChargingStationsWithChargeSchedules(
          from,
          items,
          page,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesApi.chargeScheduleControllerGetChargingStationsWithChargeSchedules'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete old charge schedules for the given ppid
     * @summary Delete old charge schedules
     * @param {string} ppid Accepts a single ppid to delete old charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteChargeScheduleHistory(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteChargeScheduleHistory(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeSchedulesApi.deleteChargeScheduleHistory']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all active charge schedules for the given ppids
     * @summary Get all active charge schedules
     * @param {Array<string>} ppid Accepts comma separated ppid(s) to batch fetch their charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargeSchedules(
      ppid: Array<string>,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<{
        [key: string]: Array<GetChargeSchedules200ResponseValueInner>;
      }>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargeSchedules(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeSchedulesApi.getChargeSchedules']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get historical charge schedules for the given ppid
     * @summary Get historical charge schedules
     * @param {string} ppid Accepts a single ppid to fetch old charge schedules
     * @param {string} from Start date of the charge schedule
     * @param {string} to End date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargerChargeScheduleHistory(
      ppid: string,
      from: string,
      to: string,
      items?: number,
      page?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PaginatedSchedulesDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargerChargeScheduleHistory(
          ppid,
          from,
          to,
          items,
          page,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesApi.getChargerChargeScheduleHistory'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf
     * @summary Get composite charge schedule
     * @param {string} ppid
     * @param {number} [duration] The duration in seconds for which the composite schedule is requested
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getCompositeChargeSchedule(
      ppid: string,
      duration?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CompositeScheduleDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getCompositeChargeSchedule(
          ppid,
          duration,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeSchedulesApi.getCompositeChargeSchedule']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get effective charge schedules for the given ppids in the time range
     * @summary Get effective charge schedules in time range
     * @param {EffectiveChargeSchedulesDto} effectiveChargeSchedulesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getEffectiveChargeSchedulesInTimeRange(
      effectiveChargeSchedulesDto: EffectiveChargeSchedulesDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<EffectiveChargeSchedulesResponseDto>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getEffectiveChargeSchedulesInTimeRange(
          effectiveChargeSchedulesDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesApi.getEffectiveChargeSchedulesInTimeRange'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Set the default charge schedule for a charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setDefaultChargeSchedule(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setDefaultChargeSchedule(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeSchedulesApi.setDefaultChargeSchedule']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeSchedulesApi - factory interface
 * @export
 */
export const ChargeSchedulesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeSchedulesApiFp(configuration);
  return {
    /**
     *
     * @param {string} from Start date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeScheduleControllerGetChargingStationsWithChargeSchedules(
      from: string,
      items?: number,
      page?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .chargeScheduleControllerGetChargingStationsWithChargeSchedules(
          from,
          items,
          page,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete old charge schedules for the given ppid
     * @summary Delete old charge schedules
     * @param {string} ppid Accepts a single ppid to delete old charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteChargeScheduleHistory(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteChargeScheduleHistory(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all active charge schedules for the given ppids
     * @summary Get all active charge schedules
     * @param {Array<string>} ppid Accepts comma separated ppid(s) to batch fetch their charge schedules
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargeSchedules(
      ppid: Array<string>,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<{
      [key: string]: Array<GetChargeSchedules200ResponseValueInner>;
    }> {
      return localVarFp
        .getChargeSchedules(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get historical charge schedules for the given ppid
     * @summary Get historical charge schedules
     * @param {string} ppid Accepts a single ppid to fetch old charge schedules
     * @param {string} from Start date of the charge schedule
     * @param {string} to End date of the charge schedule
     * @param {number} [items] Number of items to return
     * @param {number} [page] Page number
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargerChargeScheduleHistory(
      ppid: string,
      from: string,
      to: string,
      items?: number,
      page?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PaginatedSchedulesDto> {
      return localVarFp
        .getChargerChargeScheduleHistory(ppid, from, to, items, page, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf
     * @summary Get composite charge schedule
     * @param {string} ppid
     * @param {number} [duration] The duration in seconds for which the composite schedule is requested
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCompositeChargeSchedule(
      ppid: string,
      duration?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CompositeScheduleDto> {
      return localVarFp
        .getCompositeChargeSchedule(ppid, duration, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get effective charge schedules for the given ppids in the time range
     * @summary Get effective charge schedules in time range
     * @param {EffectiveChargeSchedulesDto} effectiveChargeSchedulesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getEffectiveChargeSchedulesInTimeRange(
      effectiveChargeSchedulesDto: EffectiveChargeSchedulesDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<EffectiveChargeSchedulesResponseDto>> {
      return localVarFp
        .getEffectiveChargeSchedulesInTimeRange(
          effectiveChargeSchedulesDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Set the default charge schedule for a charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setDefaultChargeSchedule(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .setDefaultChargeSchedule(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeSchedulesApi - object-oriented interface
 * @export
 * @class ChargeSchedulesApi
 * @extends {BaseAPI}
 */
export class ChargeSchedulesApi extends BaseAPI {
  /**
   *
   * @param {string} from Start date of the charge schedule
   * @param {number} [items] Number of items to return
   * @param {number} [page] Page number
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public chargeScheduleControllerGetChargingStationsWithChargeSchedules(
    from: string,
    items?: number,
    page?: number,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .chargeScheduleControllerGetChargingStationsWithChargeSchedules(
        from,
        items,
        page,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete old charge schedules for the given ppid
   * @summary Delete old charge schedules
   * @param {string} ppid Accepts a single ppid to delete old charge schedules
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public deleteChargeScheduleHistory(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .deleteChargeScheduleHistory(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all active charge schedules for the given ppids
   * @summary Get all active charge schedules
   * @param {Array<string>} ppid Accepts comma separated ppid(s) to batch fetch their charge schedules
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public getChargeSchedules(
    ppid: Array<string>,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .getChargeSchedules(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get historical charge schedules for the given ppid
   * @summary Get historical charge schedules
   * @param {string} ppid Accepts a single ppid to fetch old charge schedules
   * @param {string} from Start date of the charge schedule
   * @param {string} to End date of the charge schedule
   * @param {number} [items] Number of items to return
   * @param {number} [page] Page number
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public getChargerChargeScheduleHistory(
    ppid: string,
    from: string,
    to: string,
    items?: number,
    page?: number,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .getChargerChargeScheduleHistory(ppid, from, to, items, page, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve the composite charge schedule from the charging by executing GetCompositeSchedule.conf
   * @summary Get composite charge schedule
   * @param {string} ppid
   * @param {number} [duration] The duration in seconds for which the composite schedule is requested
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public getCompositeChargeSchedule(
    ppid: string,
    duration?: number,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .getCompositeChargeSchedule(ppid, duration, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get effective charge schedules for the given ppids in the time range
   * @summary Get effective charge schedules in time range
   * @param {EffectiveChargeSchedulesDto} effectiveChargeSchedulesDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public getEffectiveChargeSchedulesInTimeRange(
    effectiveChargeSchedulesDto: EffectiveChargeSchedulesDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .getEffectiveChargeSchedulesInTimeRange(
        effectiveChargeSchedulesDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Set the default charge schedule for a charging station
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesApi
   */
  public setDefaultChargeSchedule(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesApiFp(this.configuration)
      .setDefaultChargeSchedule(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargeSchedulesAPI3Api - axios parameter creator
 * @export
 */
export const ChargeSchedulesAPI3ApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get charging station charge schedules
     * @summary Get charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationChargeSchedules: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationChargeSchedules', 'ppid', ppid);
      const localVarPath =
        `/api3/charging-stations/{ppid}/charge-schedules`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Resend charge schedules to the charging station
     * @summary Resend charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    resendChargeSchedules: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('resendChargeSchedules', 'ppid', ppid);
      const localVarPath =
        `/api3/charging-stations/{ppid}/charge-schedules/resend`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set charging station charge schedules
     * @summary Set charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {API3PutChargeScheduleBody} aPI3PutChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setChargingStationChargeSchedules: async (
      ppid: string,
      aPI3PutChargeScheduleBody: API3PutChargeScheduleBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('setChargingStationChargeSchedules', 'ppid', ppid);
      // verify required parameter 'aPI3PutChargeScheduleBody' is not null or undefined
      assertParamExists(
        'setChargingStationChargeSchedules',
        'aPI3PutChargeScheduleBody',
        aPI3PutChargeScheduleBody
      );
      const localVarPath =
        `/api3/charging-stations/{ppid}/charge-schedules`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        aPI3PutChargeScheduleBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a charge schedule
     * @summary Update a charge schedule
     * @param {string} ppid The PPID of the charging station
     * @param {string} scheduleId The UID of the charge schedule
     * @param {API3PatchChargeScheduleBody} aPI3PatchChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateChargingStationChargeSchedule: async (
      ppid: string,
      scheduleId: string,
      aPI3PatchChargeScheduleBody: API3PatchChargeScheduleBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('updateChargingStationChargeSchedule', 'ppid', ppid);
      // verify required parameter 'scheduleId' is not null or undefined
      assertParamExists(
        'updateChargingStationChargeSchedule',
        'scheduleId',
        scheduleId
      );
      // verify required parameter 'aPI3PatchChargeScheduleBody' is not null or undefined
      assertParamExists(
        'updateChargingStationChargeSchedule',
        'aPI3PatchChargeScheduleBody',
        aPI3PatchChargeScheduleBody
      );
      const localVarPath =
        `/api3/charging-stations/{ppid}/charge-schedules/{scheduleId}`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'scheduleId'}}`, encodeURIComponent(String(scheduleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        aPI3PatchChargeScheduleBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeSchedulesAPI3Api - functional programming interface
 * @export
 */
export const ChargeSchedulesAPI3ApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ChargeSchedulesAPI3ApiAxiosParamCreator(configuration);
  return {
    /**
     * Get charging station charge schedules
     * @summary Get charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationChargeSchedules(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetApi3ChargeSchedules>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationChargeSchedules(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesAPI3Api.getChargingStationChargeSchedules'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Resend charge schedules to the charging station
     * @summary Resend charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async resendChargeSchedules(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.resendChargeSchedules(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargeSchedulesAPI3Api.resendChargeSchedules']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Set charging station charge schedules
     * @summary Set charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {API3PutChargeScheduleBody} aPI3PutChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setChargingStationChargeSchedules(
      ppid: string,
      aPI3PutChargeScheduleBody: API3PutChargeScheduleBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<API3ChargeSchedule>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setChargingStationChargeSchedules(
          ppid,
          aPI3PutChargeScheduleBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesAPI3Api.setChargingStationChargeSchedules'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a charge schedule
     * @summary Update a charge schedule
     * @param {string} ppid The PPID of the charging station
     * @param {string} scheduleId The UID of the charge schedule
     * @param {API3PatchChargeScheduleBody} aPI3PatchChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateChargingStationChargeSchedule(
      ppid: string,
      scheduleId: string,
      aPI3PatchChargeScheduleBody: API3PatchChargeScheduleBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<API3ChargeSchedule>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateChargingStationChargeSchedule(
          ppid,
          scheduleId,
          aPI3PatchChargeScheduleBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeSchedulesAPI3Api.updateChargingStationChargeSchedule'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeSchedulesAPI3Api - factory interface
 * @export
 */
export const ChargeSchedulesAPI3ApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeSchedulesAPI3ApiFp(configuration);
  return {
    /**
     * Get charging station charge schedules
     * @summary Get charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationChargeSchedules(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetApi3ChargeSchedules> {
      return localVarFp
        .getChargingStationChargeSchedules(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Resend charge schedules to the charging station
     * @summary Resend charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    resendChargeSchedules(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .resendChargeSchedules(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set charging station charge schedules
     * @summary Set charge schedules
     * @param {string} ppid The PPID of the charging station
     * @param {API3PutChargeScheduleBody} aPI3PutChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setChargingStationChargeSchedules(
      ppid: string,
      aPI3PutChargeScheduleBody: API3PutChargeScheduleBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<API3ChargeSchedule>> {
      return localVarFp
        .setChargingStationChargeSchedules(
          ppid,
          aPI3PutChargeScheduleBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a charge schedule
     * @summary Update a charge schedule
     * @param {string} ppid The PPID of the charging station
     * @param {string} scheduleId The UID of the charge schedule
     * @param {API3PatchChargeScheduleBody} aPI3PatchChargeScheduleBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateChargingStationChargeSchedule(
      ppid: string,
      scheduleId: string,
      aPI3PatchChargeScheduleBody: API3PatchChargeScheduleBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<API3ChargeSchedule> {
      return localVarFp
        .updateChargingStationChargeSchedule(
          ppid,
          scheduleId,
          aPI3PatchChargeScheduleBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeSchedulesAPI3Api - object-oriented interface
 * @export
 * @class ChargeSchedulesAPI3Api
 * @extends {BaseAPI}
 */
export class ChargeSchedulesAPI3Api extends BaseAPI {
  /**
   * Get charging station charge schedules
   * @summary Get charge schedules
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesAPI3Api
   */
  public getChargingStationChargeSchedules(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesAPI3ApiFp(this.configuration)
      .getChargingStationChargeSchedules(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Resend charge schedules to the charging station
   * @summary Resend charge schedules
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesAPI3Api
   */
  public resendChargeSchedules(ppid: string, options?: RawAxiosRequestConfig) {
    return ChargeSchedulesAPI3ApiFp(this.configuration)
      .resendChargeSchedules(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set charging station charge schedules
   * @summary Set charge schedules
   * @param {string} ppid The PPID of the charging station
   * @param {API3PutChargeScheduleBody} aPI3PutChargeScheduleBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesAPI3Api
   */
  public setChargingStationChargeSchedules(
    ppid: string,
    aPI3PutChargeScheduleBody: API3PutChargeScheduleBody,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesAPI3ApiFp(this.configuration)
      .setChargingStationChargeSchedules(
        ppid,
        aPI3PutChargeScheduleBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a charge schedule
   * @summary Update a charge schedule
   * @param {string} ppid The PPID of the charging station
   * @param {string} scheduleId The UID of the charge schedule
   * @param {API3PatchChargeScheduleBody} aPI3PatchChargeScheduleBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeSchedulesAPI3Api
   */
  public updateChargingStationChargeSchedule(
    ppid: string,
    scheduleId: string,
    aPI3PatchChargeScheduleBody: API3PatchChargeScheduleBody,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeSchedulesAPI3ApiFp(this.configuration)
      .updateChargingStationChargeSchedule(
        ppid,
        scheduleId,
        aPI3PatchChargeScheduleBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargingProfilesApi - axios parameter creator
 * @export
 */
export const ChargingProfilesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Delete profiles in stack level range
     * @summary Delete profiles in stack level range
     * @param {string} ppid
     * @param {ChargingProfileDeleteRangeDto} chargingProfileDeleteRangeDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteProfilesInStackLevelRange: async (
      ppid: string,
      chargingProfileDeleteRangeDto: ChargingProfileDeleteRangeDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('deleteProfilesInStackLevelRange', 'ppid', ppid);
      // verify required parameter 'chargingProfileDeleteRangeDto' is not null or undefined
      assertParamExists(
        'deleteProfilesInStackLevelRange',
        'chargingProfileDeleteRangeDto',
        chargingProfileDeleteRangeDto
      );
      const localVarPath = `/charging-profile/{ppid}/stack-level-range`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargingProfileDeleteRangeDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Submit charging profiles for the given ppid
     * @summary Submit charging profiles
     * @param {string} ppid
     * @param {ChargingProfilesRequestDto} chargingProfilesRequestDto Body with list of charging profiles to submit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    submitChargingProfiles: async (
      ppid: string,
      chargingProfilesRequestDto: ChargingProfilesRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('submitChargingProfiles', 'ppid', ppid);
      // verify required parameter 'chargingProfilesRequestDto' is not null or undefined
      assertParamExists(
        'submitChargingProfiles',
        'chargingProfilesRequestDto',
        chargingProfilesRequestDto
      );
      const localVarPath = `/charging-profile/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargingProfilesRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingProfilesApi - functional programming interface
 * @export
 */
export const ChargingProfilesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargingProfilesApiAxiosParamCreator(configuration);
  return {
    /**
     * Delete profiles in stack level range
     * @summary Delete profiles in stack level range
     * @param {string} ppid
     * @param {ChargingProfileDeleteRangeDto} chargingProfileDeleteRangeDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteProfilesInStackLevelRange(
      ppid: string,
      chargingProfileDeleteRangeDto: ChargingProfileDeleteRangeDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteProfilesInStackLevelRange(
          ppid,
          chargingProfileDeleteRangeDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingProfilesApi.deleteProfilesInStackLevelRange'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Submit charging profiles for the given ppid
     * @summary Submit charging profiles
     * @param {string} ppid
     * @param {ChargingProfilesRequestDto} chargingProfilesRequestDto Body with list of charging profiles to submit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async submitChargingProfiles(
      ppid: string,
      chargingProfilesRequestDto: ChargingProfilesRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.submitChargingProfiles(
          ppid,
          chargingProfilesRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingProfilesApi.submitChargingProfiles']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingProfilesApi - factory interface
 * @export
 */
export const ChargingProfilesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingProfilesApiFp(configuration);
  return {
    /**
     * Delete profiles in stack level range
     * @summary Delete profiles in stack level range
     * @param {string} ppid
     * @param {ChargingProfileDeleteRangeDto} chargingProfileDeleteRangeDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteProfilesInStackLevelRange(
      ppid: string,
      chargingProfileDeleteRangeDto: ChargingProfileDeleteRangeDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteProfilesInStackLevelRange(
          ppid,
          chargingProfileDeleteRangeDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Submit charging profiles for the given ppid
     * @summary Submit charging profiles
     * @param {string} ppid
     * @param {ChargingProfilesRequestDto} chargingProfilesRequestDto Body with list of charging profiles to submit
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    submitChargingProfiles(
      ppid: string,
      chargingProfilesRequestDto: ChargingProfilesRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .submitChargingProfiles(ppid, chargingProfilesRequestDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingProfilesApi - object-oriented interface
 * @export
 * @class ChargingProfilesApi
 * @extends {BaseAPI}
 */
export class ChargingProfilesApi extends BaseAPI {
  /**
   * Delete profiles in stack level range
   * @summary Delete profiles in stack level range
   * @param {string} ppid
   * @param {ChargingProfileDeleteRangeDto} chargingProfileDeleteRangeDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingProfilesApi
   */
  public deleteProfilesInStackLevelRange(
    ppid: string,
    chargingProfileDeleteRangeDto: ChargingProfileDeleteRangeDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingProfilesApiFp(this.configuration)
      .deleteProfilesInStackLevelRange(
        ppid,
        chargingProfileDeleteRangeDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Submit charging profiles for the given ppid
   * @summary Submit charging profiles
   * @param {string} ppid
   * @param {ChargingProfilesRequestDto} chargingProfilesRequestDto Body with list of charging profiles to submit
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingProfilesApi
   */
  public submitChargingProfiles(
    ppid: string,
    chargingProfilesRequestDto: ChargingProfilesRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingProfilesApiFp(this.configuration)
      .submitChargingProfiles(ppid, chargingProfilesRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargingStationCacheApi - axios parameter creator
 * @export
 */
export const ChargingStationCacheApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * This endpoint will clear the cache for a charging station
     * @summary Clear the cache for a charging station
     * @param {string} ppid The charging station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearCacheForChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('clearCacheForChargingStation', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/cache`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingStationCacheApi - functional programming interface
 * @export
 */
export const ChargingStationCacheApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ChargingStationCacheApiAxiosParamCreator(configuration);
  return {
    /**
     * This endpoint will clear the cache for a charging station
     * @summary Clear the cache for a charging station
     * @param {string} ppid The charging station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async clearCacheForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.clearCacheForChargingStation(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStationCacheApi.clearCacheForChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingStationCacheApi - factory interface
 * @export
 */
export const ChargingStationCacheApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingStationCacheApiFp(configuration);
  return {
    /**
     * This endpoint will clear the cache for a charging station
     * @summary Clear the cache for a charging station
     * @param {string} ppid The charging station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearCacheForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .clearCacheForChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingStationCacheApi - object-oriented interface
 * @export
 * @class ChargingStationCacheApi
 * @extends {BaseAPI}
 */
export class ChargingStationCacheApi extends BaseAPI {
  /**
   * This endpoint will clear the cache for a charging station
   * @summary Clear the cache for a charging station
   * @param {string} ppid The charging station PPID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationCacheApi
   */
  public clearCacheForChargingStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationCacheApiFp(this.configuration)
      .clearCacheForChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CommandsApi - axios parameter creator
 * @export
 */
export const CommandsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Send a response from the connectivity commands API
     * @param {CommandResponse} commandResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postCommandResponse: async (
      commandResponse: CommandResponse,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'commandResponse' is not null or undefined
      assertParamExists(
        'postCommandResponse',
        'commandResponse',
        commandResponse
      );
      const localVarPath = `/commands/responses`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        commandResponse,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Trigger a command on the charging stations
     * @param {TriggerCommandRequest} triggerCommandRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerCommand: async (
      triggerCommandRequest: TriggerCommandRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'triggerCommandRequest' is not null or undefined
      assertParamExists(
        'triggerCommand',
        'triggerCommandRequest',
        triggerCommandRequest
      );
      const localVarPath = `/commands/trigger`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        triggerCommandRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CommandsApi - functional programming interface
 * @export
 */
export const CommandsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = CommandsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Send a response from the connectivity commands API
     * @param {CommandResponse} commandResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async postCommandResponse(
      commandResponse: CommandResponse,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.postCommandResponse(
          commandResponse,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CommandsApi.postCommandResponse']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Trigger a command on the charging stations
     * @param {TriggerCommandRequest} triggerCommandRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async triggerCommand(
      triggerCommandRequest: TriggerCommandRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.triggerCommand(
        triggerCommandRequest,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CommandsApi.triggerCommand']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CommandsApi - factory interface
 * @export
 */
export const CommandsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CommandsApiFp(configuration);
  return {
    /**
     *
     * @summary Send a response from the connectivity commands API
     * @param {CommandResponse} commandResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postCommandResponse(
      commandResponse: CommandResponse,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .postCommandResponse(commandResponse, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Trigger a command on the charging stations
     * @param {TriggerCommandRequest} triggerCommandRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerCommand(
      triggerCommandRequest: TriggerCommandRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .triggerCommand(triggerCommandRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CommandsApi - object-oriented interface
 * @export
 * @class CommandsApi
 * @extends {BaseAPI}
 */
export class CommandsApi extends BaseAPI {
  /**
   *
   * @summary Send a response from the connectivity commands API
   * @param {CommandResponse} commandResponse
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CommandsApi
   */
  public postCommandResponse(
    commandResponse: CommandResponse,
    options?: RawAxiosRequestConfig
  ) {
    return CommandsApiFp(this.configuration)
      .postCommandResponse(commandResponse, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Trigger a command on the charging stations
   * @param {TriggerCommandRequest} triggerCommandRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CommandsApi
   */
  public triggerCommand(
    triggerCommandRequest: TriggerCommandRequest,
    options?: RawAxiosRequestConfig
  ) {
    return CommandsApiFp(this.configuration)
      .triggerCommand(triggerCommandRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlChargingProfilesApi - axios parameter creator
 * @export
 */
export const DelegatedControlChargingProfilesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Returns a validated charging profile object with a valid flag. The flag indicates if the provided charging profile is valid or not.
     * @summary Returns the validated charging profile
     * @param {string} ppid The ppid of the charging station
     * @param {ValidateChargingProfileRequestDto} validateChargingProfileRequestDto The charging profile to validate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    validateChargingProfile: async (
      ppid: string,
      validateChargingProfileRequestDto: ValidateChargingProfileRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('validateChargingProfile', 'ppid', ppid);
      // verify required parameter 'validateChargingProfileRequestDto' is not null or undefined
      assertParamExists(
        'validateChargingProfile',
        'validateChargingProfileRequestDto',
        validateChargingProfileRequestDto
      );
      const localVarPath =
        `/delegated-controls/{ppid}/charging-profiles/validate`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        validateChargingProfileRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlChargingProfilesApi - functional programming interface
 * @export
 */
export const DelegatedControlChargingProfilesApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlChargingProfilesApiAxiosParamCreator(configuration);
  return {
    /**
     * Returns a validated charging profile object with a valid flag. The flag indicates if the provided charging profile is valid or not.
     * @summary Returns the validated charging profile
     * @param {string} ppid The ppid of the charging station
     * @param {ValidateChargingProfileRequestDto} validateChargingProfileRequestDto The charging profile to validate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async validateChargingProfile(
      ppid: string,
      validateChargingProfileRequestDto: ValidateChargingProfileRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ValidateChargingProfileResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.validateChargingProfile(
          ppid,
          validateChargingProfileRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingProfilesApi.validateChargingProfile'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlChargingProfilesApi - factory interface
 * @export
 */
export const DelegatedControlChargingProfilesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlChargingProfilesApiFp(configuration);
  return {
    /**
     * Returns a validated charging profile object with a valid flag. The flag indicates if the provided charging profile is valid or not.
     * @summary Returns the validated charging profile
     * @param {string} ppid The ppid of the charging station
     * @param {ValidateChargingProfileRequestDto} validateChargingProfileRequestDto The charging profile to validate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    validateChargingProfile(
      ppid: string,
      validateChargingProfileRequestDto: ValidateChargingProfileRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ValidateChargingProfileResponseDto> {
      return localVarFp
        .validateChargingProfile(
          ppid,
          validateChargingProfileRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlChargingProfilesApi - object-oriented interface
 * @export
 * @class DelegatedControlChargingProfilesApi
 * @extends {BaseAPI}
 */
export class DelegatedControlChargingProfilesApi extends BaseAPI {
  /**
   * Returns a validated charging profile object with a valid flag. The flag indicates if the provided charging profile is valid or not.
   * @summary Returns the validated charging profile
   * @param {string} ppid The ppid of the charging station
   * @param {ValidateChargingProfileRequestDto} validateChargingProfileRequestDto The charging profile to validate
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingProfilesApi
   */
  public validateChargingProfile(
    ppid: string,
    validateChargingProfileRequestDto: ValidateChargingProfileRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingProfilesApiFp(this.configuration)
      .validateChargingProfile(ppid, validateChargingProfileRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlChargingStationsApi - axios parameter creator
 * @export
 */
export const DelegatedControlChargingStationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a delegated control charging station and set the intent via providing a charge time
     * @summary Create a delegated control charging station and set the intent
     * @param {string} ppid The PPID of the charging station
     * @param {EnrolmentRequestDto} enrolmentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createDelegatedControlChargingStation: async (
      ppid: string,
      enrolmentRequestDto: EnrolmentRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('createDelegatedControlChargingStation', 'ppid', ppid);
      // verify required parameter 'enrolmentRequestDto' is not null or undefined
      assertParamExists(
        'createDelegatedControlChargingStation',
        'enrolmentRequestDto',
        enrolmentRequestDto
      );
      const localVarPath = `/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        enrolmentRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete a delegated control charging station by its PPID
     * @summary Delete a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteDelegatedControlChargingStationByPpid: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'deleteDelegatedControlChargingStationByPpid',
        'ppid',
        ppid
      );
      const localVarPath = `/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Gets a delegated control charging stations details by its PPID
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStationByPpid: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'getDelegatedControlChargingStationByPpid',
        'ppid',
        ppid
      );
      const localVarPath = `/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns a delegated control charging stations object with the intent. The ppid is used as they key.
     * @summary Returns delegated control charging stations
     * @param {Array<string>} ppid The PPIDs of the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStations: async (
      ppid: Array<string>,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getDelegatedControlChargingStations', 'ppid', ppid);
      const localVarPath = `/delegated-controls`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (ppid) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Search criteria can be used to filter charging stations returned, or omitted to return all charging stations
     * @summary Search for delegated control charging stations that match given criteria
     * @param {string} [hasActiveChargingSession] Does the charging station have a active charging session
     * @param {string} [providerId] The delegated control provider id
     * @param {number} [itemsPerPage] Pagination option: items per page. Defaults to 10
     * @param {number} [page] Pagination option: page number. Defaults to 1
     * @param {string} [hasVehiclePluggedIn] Does the charging station have a vehicle plugged in
     * @param {string} [status] The delegated control status
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchDelegatedControlChargingStations: async (
      hasActiveChargingSession?: string,
      providerId?: string,
      itemsPerPage?: number,
      page?: number,
      hasVehiclePluggedIn?: string,
      status?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/delegated-controls/search`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (hasActiveChargingSession !== undefined) {
        localVarQueryParameter['hasActiveChargingSession'] =
          hasActiveChargingSession;
      }

      if (providerId !== undefined) {
        localVarQueryParameter['providerId'] = providerId;
      }

      if (itemsPerPage !== undefined) {
        localVarQueryParameter['itemsPerPage'] = itemsPerPage;
      }

      if (page !== undefined) {
        localVarQueryParameter['page'] = page;
      }

      if (hasVehiclePluggedIn !== undefined) {
        localVarQueryParameter['hasVehiclePluggedIn'] = hasVehiclePluggedIn;
      }

      if (status !== undefined) {
        localVarQueryParameter['status'] = status;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlChargingStationsApi - functional programming interface
 * @export
 */
export const DelegatedControlChargingStationsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlChargingStationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a delegated control charging station and set the intent via providing a charge time
     * @summary Create a delegated control charging station and set the intent
     * @param {string} ppid The PPID of the charging station
     * @param {EnrolmentRequestDto} enrolmentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createDelegatedControlChargingStation(
      ppid: string,
      enrolmentRequestDto: EnrolmentRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createDelegatedControlChargingStation(
          ppid,
          enrolmentRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingStationsApi.createDelegatedControlChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete a delegated control charging station by its PPID
     * @summary Delete a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteDelegatedControlChargingStationByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteDelegatedControlChargingStationByPpid(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Gets a delegated control charging stations details by its PPID
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getDelegatedControlChargingStationByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DelegatedControlChargingStationResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getDelegatedControlChargingStationByPpid(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Returns a delegated control charging stations object with the intent. The ppid is used as they key.
     * @summary Returns delegated control charging stations
     * @param {Array<string>} ppid The PPIDs of the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getDelegatedControlChargingStations(
      ppid: Array<string>,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<{
        [key: string]: DelegatedControlChargingStationResponseDto;
      }>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getDelegatedControlChargingStations(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingStationsApi.getDelegatedControlChargingStations'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Search criteria can be used to filter charging stations returned, or omitted to return all charging stations
     * @summary Search for delegated control charging stations that match given criteria
     * @param {string} [hasActiveChargingSession] Does the charging station have a active charging session
     * @param {string} [providerId] The delegated control provider id
     * @param {number} [itemsPerPage] Pagination option: items per page. Defaults to 10
     * @param {number} [page] Pagination option: page number. Defaults to 1
     * @param {string} [hasVehiclePluggedIn] Does the charging station have a vehicle plugged in
     * @param {string} [status] The delegated control status
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchDelegatedControlChargingStations(
      hasActiveChargingSession?: string,
      providerId?: string,
      itemsPerPage?: number,
      page?: number,
      hasVehiclePluggedIn?: string,
      status?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DelegatedControlChargingStationSearchResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.searchDelegatedControlChargingStations(
          hasActiveChargingSession,
          providerId,
          itemsPerPage,
          page,
          hasVehiclePluggedIn,
          status,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlChargingStationsApi.searchDelegatedControlChargingStations'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlChargingStationsApi - factory interface
 * @export
 */
export const DelegatedControlChargingStationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlChargingStationsApiFp(configuration);
  return {
    /**
     * Create a delegated control charging station and set the intent via providing a charge time
     * @summary Create a delegated control charging station and set the intent
     * @param {string} ppid The PPID of the charging station
     * @param {EnrolmentRequestDto} enrolmentRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createDelegatedControlChargingStation(
      ppid: string,
      enrolmentRequestDto: EnrolmentRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .createDelegatedControlChargingStation(
          ppid,
          enrolmentRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete a delegated control charging station by its PPID
     * @summary Delete a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteDelegatedControlChargingStationByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteDelegatedControlChargingStationByPpid(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Gets a delegated control charging stations details by its PPID
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStationByPpid(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DelegatedControlChargingStationResponseDto> {
      return localVarFp
        .getDelegatedControlChargingStationByPpid(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns a delegated control charging stations object with the intent. The ppid is used as they key.
     * @summary Returns delegated control charging stations
     * @param {Array<string>} ppid The PPIDs of the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStations(
      ppid: Array<string>,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<{
      [key: string]: DelegatedControlChargingStationResponseDto;
    }> {
      return localVarFp
        .getDelegatedControlChargingStations(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Search criteria can be used to filter charging stations returned, or omitted to return all charging stations
     * @summary Search for delegated control charging stations that match given criteria
     * @param {string} [hasActiveChargingSession] Does the charging station have a active charging session
     * @param {string} [providerId] The delegated control provider id
     * @param {number} [itemsPerPage] Pagination option: items per page. Defaults to 10
     * @param {number} [page] Pagination option: page number. Defaults to 1
     * @param {string} [hasVehiclePluggedIn] Does the charging station have a vehicle plugged in
     * @param {string} [status] The delegated control status
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchDelegatedControlChargingStations(
      hasActiveChargingSession?: string,
      providerId?: string,
      itemsPerPage?: number,
      page?: number,
      hasVehiclePluggedIn?: string,
      status?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DelegatedControlChargingStationSearchResponseDto> {
      return localVarFp
        .searchDelegatedControlChargingStations(
          hasActiveChargingSession,
          providerId,
          itemsPerPage,
          page,
          hasVehiclePluggedIn,
          status,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlChargingStationsApi - object-oriented interface
 * @export
 * @class DelegatedControlChargingStationsApi
 * @extends {BaseAPI}
 */
export class DelegatedControlChargingStationsApi extends BaseAPI {
  /**
   * Create a delegated control charging station and set the intent via providing a charge time
   * @summary Create a delegated control charging station and set the intent
   * @param {string} ppid The PPID of the charging station
   * @param {EnrolmentRequestDto} enrolmentRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingStationsApi
   */
  public createDelegatedControlChargingStation(
    ppid: string,
    enrolmentRequestDto: EnrolmentRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingStationsApiFp(this.configuration)
      .createDelegatedControlChargingStation(ppid, enrolmentRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete a delegated control charging station by its PPID
   * @summary Delete a delegated control charging station
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingStationsApi
   */
  public deleteDelegatedControlChargingStationByPpid(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingStationsApiFp(this.configuration)
      .deleteDelegatedControlChargingStationByPpid(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Gets a delegated control charging stations details by its PPID
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingStationsApi
   */
  public getDelegatedControlChargingStationByPpid(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingStationsApiFp(this.configuration)
      .getDelegatedControlChargingStationByPpid(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns a delegated control charging stations object with the intent. The ppid is used as they key.
   * @summary Returns delegated control charging stations
   * @param {Array<string>} ppid The PPIDs of the charging stations
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingStationsApi
   */
  public getDelegatedControlChargingStations(
    ppid: Array<string>,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingStationsApiFp(this.configuration)
      .getDelegatedControlChargingStations(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Search criteria can be used to filter charging stations returned, or omitted to return all charging stations
   * @summary Search for delegated control charging stations that match given criteria
   * @param {string} [hasActiveChargingSession] Does the charging station have a active charging session
   * @param {string} [providerId] The delegated control provider id
   * @param {number} [itemsPerPage] Pagination option: items per page. Defaults to 10
   * @param {number} [page] Pagination option: page number. Defaults to 1
   * @param {string} [hasVehiclePluggedIn] Does the charging station have a vehicle plugged in
   * @param {string} [status] The delegated control status
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlChargingStationsApi
   */
  public searchDelegatedControlChargingStations(
    hasActiveChargingSession?: string,
    providerId?: string,
    itemsPerPage?: number,
    page?: number,
    hasVehiclePluggedIn?: string,
    status?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlChargingStationsApiFp(this.configuration)
      .searchDelegatedControlChargingStations(
        hasActiveChargingSession,
        providerId,
        itemsPerPage,
        page,
        hasVehiclePluggedIn,
        status,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlIntentsApi - axios parameter creator
 * @export
 */
export const DelegatedControlIntentsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Calculate charge info from profile
     * @summary Calculate charge info from ocpp1.6 profile
     * @param {string} ppid The PPID of the charging station
     * @param {TxProfileInfoDto} txProfileInfoDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    calculateChargeInfoFromProfile: async (
      ppid: string,
      txProfileInfoDto: TxProfileInfoDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('calculateChargeInfoFromProfile', 'ppid', ppid);
      // verify required parameter 'txProfileInfoDto' is not null or undefined
      assertParamExists(
        'calculateChargeInfoFromProfile',
        'txProfileInfoDto',
        txProfileInfoDto
      );
      const localVarPath =
        `/delegated-controls/{ppid}/intent/calculate-charge-info-from-profile`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        txProfileInfoDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid
     * @param {string} vehicleId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegatedControlIntentControllerGetCurrentIntentDetail: async (
      ppid: string,
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'delegatedControlIntentControllerGetCurrentIntentDetail',
        'ppid',
        ppid
      );
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'delegatedControlIntentControllerGetCurrentIntentDetail',
        'vehicleId',
        vehicleId
      );
      const localVarPath =
        `/delegated-controls/{ppid}/vehicles/{vehicleId}/intents/current`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get vehicle intents
     * @summary Get vehicle intents
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntents: async (
      ppid: string,
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getVehicleIntents', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getVehicleIntents', 'vehicleId', vehicleId);
      const localVarPath =
        `/delegated-controls/{ppid}/vehicles/{vehicleId}/intents`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Notify intent cannot be met
     * @summary Notify that intent cannot be met
     * @param {string} ppid The PPID of the charging station
     * @param {CannotMeetSessionIntent} cannotMeetSessionIntent
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notifyIntentCannotBeMet: async (
      ppid: string,
      cannotMeetSessionIntent: CannotMeetSessionIntent,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('notifyIntentCannotBeMet', 'ppid', ppid);
      // verify required parameter 'cannotMeetSessionIntent' is not null or undefined
      assertParamExists(
        'notifyIntentCannotBeMet',
        'cannotMeetSessionIntent',
        cannotMeetSessionIntent
      );
      const localVarPath =
        `/delegated-controls/{ppid}/notify/intent-cannot-be-met`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        cannotMeetSessionIntent,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sets delegated control intents. The ppid and vehicleId are required as parameters.
     * @summary Sets delegated control intents
     * @param {string} ppid The ppid of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {VehicleIntentsRequestDto} vehicleIntentsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setDelegatedControlIntents: async (
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDto: VehicleIntentsRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('setDelegatedControlIntents', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('setDelegatedControlIntents', 'vehicleId', vehicleId);
      // verify required parameter 'vehicleIntentsRequestDto' is not null or undefined
      assertParamExists(
        'setDelegatedControlIntents',
        'vehicleIntentsRequestDto',
        vehicleIntentsRequestDto
      );
      const localVarPath =
        `/delegated-controls/{ppid}/vehicles/{vehicleId}/intents`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        vehicleIntentsRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlIntentsApi - functional programming interface
 * @export
 */
export const DelegatedControlIntentsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlIntentsApiAxiosParamCreator(configuration);
  return {
    /**
     * Calculate charge info from profile
     * @summary Calculate charge info from ocpp1.6 profile
     * @param {string} ppid The PPID of the charging station
     * @param {TxProfileInfoDto} txProfileInfoDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async calculateChargeInfoFromProfile(
      ppid: string,
      txProfileInfoDto: TxProfileInfoDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleChargeInfoDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.calculateChargeInfoFromProfile(
          ppid,
          txProfileInfoDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlIntentsApi.calculateChargeInfoFromProfile'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid
     * @param {string} vehicleId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async delegatedControlIntentControllerGetCurrentIntentDetail(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.delegatedControlIntentControllerGetCurrentIntentDetail(
          ppid,
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlIntentsApi.delegatedControlIntentControllerGetCurrentIntentDetail'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get vehicle intents
     * @summary Get vehicle intents
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getVehicleIntents(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleIntentsResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getVehicleIntents(
          ppid,
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DelegatedControlIntentsApi.getVehicleIntents']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Notify intent cannot be met
     * @summary Notify that intent cannot be met
     * @param {string} ppid The PPID of the charging station
     * @param {CannotMeetSessionIntent} cannotMeetSessionIntent
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async notifyIntentCannotBeMet(
      ppid: string,
      cannotMeetSessionIntent: CannotMeetSessionIntent,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.notifyIntentCannotBeMet(
          ppid,
          cannotMeetSessionIntent,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlIntentsApi.notifyIntentCannotBeMet'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sets delegated control intents. The ppid and vehicleId are required as parameters.
     * @summary Sets delegated control intents
     * @param {string} ppid The ppid of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {VehicleIntentsRequestDto} vehicleIntentsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setDelegatedControlIntents(
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDto: VehicleIntentsRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SetDelegatedControlIntentsResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setDelegatedControlIntents(
          ppid,
          vehicleId,
          vehicleIntentsRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlIntentsApi.setDelegatedControlIntents'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlIntentsApi - factory interface
 * @export
 */
export const DelegatedControlIntentsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlIntentsApiFp(configuration);
  return {
    /**
     * Calculate charge info from profile
     * @summary Calculate charge info from ocpp1.6 profile
     * @param {string} ppid The PPID of the charging station
     * @param {TxProfileInfoDto} txProfileInfoDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    calculateChargeInfoFromProfile(
      ppid: string,
      txProfileInfoDto: TxProfileInfoDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleChargeInfoDto> {
      return localVarFp
        .calculateChargeInfoFromProfile(ppid, txProfileInfoDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid
     * @param {string} vehicleId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegatedControlIntentControllerGetCurrentIntentDetail(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .delegatedControlIntentControllerGetCurrentIntentDetail(
          ppid,
          vehicleId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get vehicle intents
     * @summary Get vehicle intents
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntents(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleIntentsResponseDto> {
      return localVarFp
        .getVehicleIntents(ppid, vehicleId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Notify intent cannot be met
     * @summary Notify that intent cannot be met
     * @param {string} ppid The PPID of the charging station
     * @param {CannotMeetSessionIntent} cannotMeetSessionIntent
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    notifyIntentCannotBeMet(
      ppid: string,
      cannotMeetSessionIntent: CannotMeetSessionIntent,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .notifyIntentCannotBeMet(ppid, cannotMeetSessionIntent, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Sets delegated control intents. The ppid and vehicleId are required as parameters.
     * @summary Sets delegated control intents
     * @param {string} ppid The ppid of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {VehicleIntentsRequestDto} vehicleIntentsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setDelegatedControlIntents(
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDto: VehicleIntentsRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SetDelegatedControlIntentsResponseDto> {
      return localVarFp
        .setDelegatedControlIntents(
          ppid,
          vehicleId,
          vehicleIntentsRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlIntentsApi - object-oriented interface
 * @export
 * @class DelegatedControlIntentsApi
 * @extends {BaseAPI}
 */
export class DelegatedControlIntentsApi extends BaseAPI {
  /**
   * Calculate charge info from profile
   * @summary Calculate charge info from ocpp1.6 profile
   * @param {string} ppid The PPID of the charging station
   * @param {TxProfileInfoDto} txProfileInfoDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlIntentsApi
   */
  public calculateChargeInfoFromProfile(
    ppid: string,
    txProfileInfoDto: TxProfileInfoDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlIntentsApiFp(this.configuration)
      .calculateChargeInfoFromProfile(ppid, txProfileInfoDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid
   * @param {string} vehicleId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlIntentsApi
   */
  public delegatedControlIntentControllerGetCurrentIntentDetail(
    ppid: string,
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlIntentsApiFp(this.configuration)
      .delegatedControlIntentControllerGetCurrentIntentDetail(
        ppid,
        vehicleId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get vehicle intents
   * @summary Get vehicle intents
   * @param {string} ppid The PPID of the charging station
   * @param {string} vehicleId The vehicleId associated to the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlIntentsApi
   */
  public getVehicleIntents(
    ppid: string,
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlIntentsApiFp(this.configuration)
      .getVehicleIntents(ppid, vehicleId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Notify intent cannot be met
   * @summary Notify that intent cannot be met
   * @param {string} ppid The PPID of the charging station
   * @param {CannotMeetSessionIntent} cannotMeetSessionIntent
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlIntentsApi
   */
  public notifyIntentCannotBeMet(
    ppid: string,
    cannotMeetSessionIntent: CannotMeetSessionIntent,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlIntentsApiFp(this.configuration)
      .notifyIntentCannotBeMet(ppid, cannotMeetSessionIntent, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sets delegated control intents. The ppid and vehicleId are required as parameters.
   * @summary Sets delegated control intents
   * @param {string} ppid The ppid of the charging station
   * @param {string} vehicleId The vehicleId associated to the charging station
   * @param {VehicleIntentsRequestDto} vehicleIntentsRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlIntentsApi
   */
  public setDelegatedControlIntents(
    ppid: string,
    vehicleId: string,
    vehicleIntentsRequestDto: VehicleIntentsRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlIntentsApiFp(this.configuration)
      .setDelegatedControlIntents(
        ppid,
        vehicleId,
        vehicleIntentsRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlNotificationsApi - axios parameter creator
 * @export
 */
export const DelegatedControlNotificationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Notify plug-in
     * @summary Notify plug-in
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegateControlNotifyChargerPlugIn: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('delegateControlNotifyChargerPlugIn', 'ppid', ppid);
      const localVarPath = `/delegated-controls/{ppid}/plug-in/notify`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Notify un-plug
     * @summary Notify un-plug
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegateControlNotifyChargerUnPlug: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('delegateControlNotifyChargerUnPlug', 'ppid', ppid);
      const localVarPath = `/delegated-controls/{ppid}/un-plug/notify`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlNotificationsApi - functional programming interface
 * @export
 */
export const DelegatedControlNotificationsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlNotificationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Notify plug-in
     * @summary Notify plug-in
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async delegateControlNotifyChargerPlugIn(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.delegateControlNotifyChargerPlugIn(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlNotificationsApi.delegateControlNotifyChargerPlugIn'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Notify un-plug
     * @summary Notify un-plug
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async delegateControlNotifyChargerUnPlug(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.delegateControlNotifyChargerUnPlug(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlNotificationsApi.delegateControlNotifyChargerUnPlug'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlNotificationsApi - factory interface
 * @export
 */
export const DelegatedControlNotificationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlNotificationsApiFp(configuration);
  return {
    /**
     * Notify plug-in
     * @summary Notify plug-in
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegateControlNotifyChargerPlugIn(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .delegateControlNotifyChargerPlugIn(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Notify un-plug
     * @summary Notify un-plug
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    delegateControlNotifyChargerUnPlug(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .delegateControlNotifyChargerUnPlug(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlNotificationsApi - object-oriented interface
 * @export
 * @class DelegatedControlNotificationsApi
 * @extends {BaseAPI}
 */
export class DelegatedControlNotificationsApi extends BaseAPI {
  /**
   * Notify plug-in
   * @summary Notify plug-in
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlNotificationsApi
   */
  public delegateControlNotifyChargerPlugIn(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlNotificationsApiFp(this.configuration)
      .delegateControlNotifyChargerPlugIn(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Notify un-plug
   * @summary Notify un-plug
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlNotificationsApi
   */
  public delegateControlNotifyChargerUnPlug(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlNotificationsApiFp(this.configuration)
      .delegateControlNotifyChargerUnPlug(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlSchedulesApi - axios parameter creator
 * @export
 */
export const DelegatedControlSchedulesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)
     * @summary Clear target schedule(s) for a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearTargetScheduleForChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('clearTargetScheduleForChargingStation', 'ppid', ppid);
      const localVarPath = `/delegated-controls/{ppid}/schedules`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Request target schedules be set for the likely connected or default vehicle of a delegated control charging station
     * @summary Request target schedules be set for connected vehicle connected to a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum} [trigger] What triggered this request (if not provided, this is assumed to be PLUGIN)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setTargetSchedulesForDelegatedControlChargingStation: async (
      ppid: string,
      trigger?: SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'setTargetSchedulesForDelegatedControlChargingStation',
        'ppid',
        ppid
      );
      const localVarPath = `/delegated-controls/{ppid}/schedules`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (trigger !== undefined) {
        localVarQueryParameter['trigger'] = trigger;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlSchedulesApi - functional programming interface
 * @export
 */
export const DelegatedControlSchedulesApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlSchedulesApiAxiosParamCreator(configuration);
  return {
    /**
     * This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)
     * @summary Clear target schedule(s) for a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async clearTargetScheduleForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.clearTargetScheduleForChargingStation(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlSchedulesApi.clearTargetScheduleForChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Request target schedules be set for the likely connected or default vehicle of a delegated control charging station
     * @summary Request target schedules be set for connected vehicle connected to a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum} [trigger] What triggered this request (if not provided, this is assumed to be PLUGIN)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setTargetSchedulesForDelegatedControlChargingStation(
      ppid: string,
      trigger?: SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setTargetSchedulesForDelegatedControlChargingStation(
          ppid,
          trigger,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlSchedulesApi.setTargetSchedulesForDelegatedControlChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlSchedulesApi - factory interface
 * @export
 */
export const DelegatedControlSchedulesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlSchedulesApiFp(configuration);
  return {
    /**
     * This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)
     * @summary Clear target schedule(s) for a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    clearTargetScheduleForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .clearTargetScheduleForChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Request target schedules be set for the likely connected or default vehicle of a delegated control charging station
     * @summary Request target schedules be set for connected vehicle connected to a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum} [trigger] What triggered this request (if not provided, this is assumed to be PLUGIN)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setTargetSchedulesForDelegatedControlChargingStation(
      ppid: string,
      trigger?: SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .setTargetSchedulesForDelegatedControlChargingStation(
          ppid,
          trigger,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlSchedulesApi - object-oriented interface
 * @export
 * @class DelegatedControlSchedulesApi
 * @extends {BaseAPI}
 */
export class DelegatedControlSchedulesApi extends BaseAPI {
  /**
   * This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)
   * @summary Clear target schedule(s) for a delegated control charging station
   * @param {string} ppid The PPID of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlSchedulesApi
   */
  public clearTargetScheduleForChargingStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlSchedulesApiFp(this.configuration)
      .clearTargetScheduleForChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Request target schedules be set for the likely connected or default vehicle of a delegated control charging station
   * @summary Request target schedules be set for connected vehicle connected to a delegated control charging station
   * @param {string} ppid The PPID of the charging station
   * @param {SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum} [trigger] What triggered this request (if not provided, this is assumed to be PLUGIN)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlSchedulesApi
   */
  public setTargetSchedulesForDelegatedControlChargingStation(
    ppid: string,
    trigger?: SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlSchedulesApiFp(this.configuration)
      .setTargetSchedulesForDelegatedControlChargingStation(
        ppid,
        trigger,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum = {
  Plugin: 'PLUGIN',
  IntentUpdate: 'INTENT_UPDATE',
} as const;
export type SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum =
  (typeof SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum)[keyof typeof SetTargetSchedulesForDelegatedControlChargingStationTriggerEnum];

/**
 * DelegatedControlSessionsApi - axios parameter creator
 * @export
 */
export const DelegatedControlSessionsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Search for delegated control sessions
     * @summary Search for delegated control sessions
     * @param {string} plugInFrom The date and time the vehicle was plugged in. It must at least one hour ago
     * @param {string} [plugInTo] The date and time the vehicle was plugged out. It must be after plugInFrom
     * @param {Array<string>} [ppid] A comma separated list of PPIDs
     * @param {number} [page] The current page number
     * @param {number} [itemsPerPage] The number of items per page
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchDelegatedControlSessions: async (
      plugInFrom: string,
      plugInTo?: string,
      ppid?: Array<string>,
      page?: number,
      itemsPerPage?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'plugInFrom' is not null or undefined
      assertParamExists(
        'searchDelegatedControlSessions',
        'plugInFrom',
        plugInFrom
      );
      const localVarPath = `/delegated-controls/charging-sessions/search`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (plugInFrom !== undefined) {
        localVarQueryParameter['plugInFrom'] =
          (plugInFrom as any) instanceof Date
            ? (plugInFrom as any).toISOString()
            : plugInFrom;
      }

      if (plugInTo !== undefined) {
        localVarQueryParameter['plugInTo'] =
          (plugInTo as any) instanceof Date
            ? (plugInTo as any).toISOString()
            : plugInTo;
      }

      if (ppid) {
        localVarQueryParameter['ppid'] = ppid;
      }

      if (page !== undefined) {
        localVarQueryParameter['page'] = page;
      }

      if (itemsPerPage !== undefined) {
        localVarQueryParameter['itemsPerPage'] = itemsPerPage;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlSessionsApi - functional programming interface
 * @export
 */
export const DelegatedControlSessionsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlSessionsApiAxiosParamCreator(configuration);
  return {
    /**
     * Search for delegated control sessions
     * @summary Search for delegated control sessions
     * @param {string} plugInFrom The date and time the vehicle was plugged in. It must at least one hour ago
     * @param {string} [plugInTo] The date and time the vehicle was plugged out. It must be after plugInFrom
     * @param {Array<string>} [ppid] A comma separated list of PPIDs
     * @param {number} [page] The current page number
     * @param {number} [itemsPerPage] The number of items per page
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchDelegatedControlSessions(
      plugInFrom: string,
      plugInTo?: string,
      ppid?: Array<string>,
      page?: number,
      itemsPerPage?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.searchDelegatedControlSessions(
          plugInFrom,
          plugInTo,
          ppid,
          page,
          itemsPerPage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlSessionsApi.searchDelegatedControlSessions'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlSessionsApi - factory interface
 * @export
 */
export const DelegatedControlSessionsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlSessionsApiFp(configuration);
  return {
    /**
     * Search for delegated control sessions
     * @summary Search for delegated control sessions
     * @param {string} plugInFrom The date and time the vehicle was plugged in. It must at least one hour ago
     * @param {string} [plugInTo] The date and time the vehicle was plugged out. It must be after plugInFrom
     * @param {Array<string>} [ppid] A comma separated list of PPIDs
     * @param {number} [page] The current page number
     * @param {number} [itemsPerPage] The number of items per page
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchDelegatedControlSessions(
      plugInFrom: string,
      plugInTo?: string,
      ppid?: Array<string>,
      page?: number,
      itemsPerPage?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .searchDelegatedControlSessions(
          plugInFrom,
          plugInTo,
          ppid,
          page,
          itemsPerPage,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlSessionsApi - object-oriented interface
 * @export
 * @class DelegatedControlSessionsApi
 * @extends {BaseAPI}
 */
export class DelegatedControlSessionsApi extends BaseAPI {
  /**
   * Search for delegated control sessions
   * @summary Search for delegated control sessions
   * @param {string} plugInFrom The date and time the vehicle was plugged in. It must at least one hour ago
   * @param {string} [plugInTo] The date and time the vehicle was plugged out. It must be after plugInFrom
   * @param {Array<string>} [ppid] A comma separated list of PPIDs
   * @param {number} [page] The current page number
   * @param {number} [itemsPerPage] The number of items per page
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlSessionsApi
   */
  public searchDelegatedControlSessions(
    plugInFrom: string,
    plugInTo?: string,
    ppid?: Array<string>,
    page?: number,
    itemsPerPage?: number,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlSessionsApiFp(this.configuration)
      .searchDelegatedControlSessions(
        plugInFrom,
        plugInTo,
        ppid,
        page,
        itemsPerPage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DelegatedControlVehiclesApi - axios parameter creator
 * @export
 */
export const DelegatedControlVehiclesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Add vehicle to delegated control charging station
     * @summary Add vehicle to delegated control charging station
     * @param {string} ppid
     * @param {CreateVehicleLinkRequestDto} createVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addVehicleToDelegatedControlChargingStation: async (
      ppid: string,
      createVehicleLinkRequestDto: CreateVehicleLinkRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'addVehicleToDelegatedControlChargingStation',
        'ppid',
        ppid
      );
      // verify required parameter 'createVehicleLinkRequestDto' is not null or undefined
      assertParamExists(
        'addVehicleToDelegatedControlChargingStation',
        'createVehicleLinkRequestDto',
        createVehicleLinkRequestDto
      );
      const localVarPath = `/delegated-controls/{ppid}/vehicles`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createVehicleLinkRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all charging stations linked to a vehicle
     * @summary Get all charging stations linked to a vehicle
     * @param {string} vehicleId The vehicleId linked to the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationsLinkedToVehicle: async (
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'getChargingStationsLinkedToVehicle',
        'vehicleId',
        vehicleId
      );
      const localVarPath =
        `/delegated-controls/vehicles/{vehicleId}/charging-stations`.replace(
          `{${'vehicleId'}}`,
          encodeURIComponent(String(vehicleId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all vehicles linked to a delegated control charging station
     * @summary Get delegated control charging station vehicles
     * @param {string} ppid The PPID of the delegated charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStationVehicles: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'getDelegatedControlChargingStationVehicles',
        'ppid',
        ppid
      );
      const localVarPath = `/delegated-controls/{ppid}/vehicles`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get vehicle
     * @summary Get vehicle
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicle: async (
      ppid: string,
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getVehicle', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getVehicle', 'vehicleId', vehicleId);
      const localVarPath = `/delegated-controls/{ppid}/vehicles/{vehicleId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Unlink a vehicle from a delegated control charging station
     * @summary Unlink a vehicle from a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unlinkVehicleFromDelegatedControlChargingStation: async (
      ppid: string,
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'unlinkVehicleFromDelegatedControlChargingStation',
        'ppid',
        ppid
      );
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'unlinkVehicleFromDelegatedControlChargingStation',
        'vehicleId',
        vehicleId
      );
      const localVarPath = `/delegated-controls/{ppid}/vehicles/{vehicleId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Unlink a vehicle from all linked delegated control charging stations
     * @summary Unlink a vehicle from all linked delegated control charging stations
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unlinkVehicleFromDelegatedControlChargingStations: async (
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'unlinkVehicleFromDelegatedControlChargingStations',
        'vehicleId',
        vehicleId
      );
      const localVarPath =
        `/delegated-controls/vehicles/{vehicleId}/charging-stations`.replace(
          `{${'vehicleId'}}`,
          encodeURIComponent(String(vehicleId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicle ID
     * @param {UpdateVehicleLinkRequestDto} updateVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateVehicleById: async (
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDto: UpdateVehicleLinkRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('updateVehicleById', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('updateVehicleById', 'vehicleId', vehicleId);
      // verify required parameter 'updateVehicleLinkRequestDto' is not null or undefined
      assertParamExists(
        'updateVehicleById',
        'updateVehicleLinkRequestDto',
        updateVehicleLinkRequestDto
      );
      const localVarPath = `/delegated-controls/{ppid}/vehicles/{vehicleId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateVehicleLinkRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DelegatedControlVehiclesApi - functional programming interface
 * @export
 */
export const DelegatedControlVehiclesApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    DelegatedControlVehiclesApiAxiosParamCreator(configuration);
  return {
    /**
     * Add vehicle to delegated control charging station
     * @summary Add vehicle to delegated control charging station
     * @param {string} ppid
     * @param {CreateVehicleLinkRequestDto} createVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async addVehicleToDelegatedControlChargingStation(
      ppid: string,
      createVehicleLinkRequestDto: CreateVehicleLinkRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleLinkResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.addVehicleToDelegatedControlChargingStation(
          ppid,
          createVehicleLinkRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all charging stations linked to a vehicle
     * @summary Get all charging stations linked to a vehicle
     * @param {string} vehicleId The vehicleId linked to the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationsLinkedToVehicle(
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleChargingStationsResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationsLinkedToVehicle(
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlVehiclesApi.getChargingStationsLinkedToVehicle'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all vehicles linked to a delegated control charging station
     * @summary Get delegated control charging station vehicles
     * @param {string} ppid The PPID of the delegated charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getDelegatedControlChargingStationVehicles(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ExtendedVehicleLinksResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getDelegatedControlChargingStationVehicles(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlVehiclesApi.getDelegatedControlChargingStationVehicles'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get vehicle
     * @summary Get vehicle
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getVehicle(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ExtendedVehicleLinkResponseDto>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getVehicle(
        ppid,
        vehicleId,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DelegatedControlVehiclesApi.getVehicle']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Unlink a vehicle from a delegated control charging station
     * @summary Unlink a vehicle from a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async unlinkVehicleFromDelegatedControlChargingStation(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.unlinkVehicleFromDelegatedControlChargingStation(
          ppid,
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Unlink a vehicle from all linked delegated control charging stations
     * @summary Unlink a vehicle from all linked delegated control charging stations
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async unlinkVehicleFromDelegatedControlChargingStations(
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.unlinkVehicleFromDelegatedControlChargingStations(
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DelegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStations'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicle ID
     * @param {UpdateVehicleLinkRequestDto} updateVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateVehicleById(
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDto: UpdateVehicleLinkRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UpdateVehicleById200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateVehicleById(
          ppid,
          vehicleId,
          updateVehicleLinkRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DelegatedControlVehiclesApi.updateVehicleById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DelegatedControlVehiclesApi - factory interface
 * @export
 */
export const DelegatedControlVehiclesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DelegatedControlVehiclesApiFp(configuration);
  return {
    /**
     * Add vehicle to delegated control charging station
     * @summary Add vehicle to delegated control charging station
     * @param {string} ppid
     * @param {CreateVehicleLinkRequestDto} createVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addVehicleToDelegatedControlChargingStation(
      ppid: string,
      createVehicleLinkRequestDto: CreateVehicleLinkRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleLinkResponseDto> {
      return localVarFp
        .addVehicleToDelegatedControlChargingStation(
          ppid,
          createVehicleLinkRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all charging stations linked to a vehicle
     * @summary Get all charging stations linked to a vehicle
     * @param {string} vehicleId The vehicleId linked to the charging stations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationsLinkedToVehicle(
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleChargingStationsResponseDto> {
      return localVarFp
        .getChargingStationsLinkedToVehicle(vehicleId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all vehicles linked to a delegated control charging station
     * @summary Get delegated control charging station vehicles
     * @param {string} ppid The PPID of the delegated charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDelegatedControlChargingStationVehicles(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ExtendedVehicleLinksResponseDto> {
      return localVarFp
        .getDelegatedControlChargingStationVehicles(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get vehicle
     * @summary Get vehicle
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicle(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ExtendedVehicleLinkResponseDto> {
      return localVarFp
        .getVehicle(ppid, vehicleId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Unlink a vehicle from a delegated control charging station
     * @summary Unlink a vehicle from a delegated control charging station
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unlinkVehicleFromDelegatedControlChargingStation(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .unlinkVehicleFromDelegatedControlChargingStation(
          ppid,
          vehicleId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Unlink a vehicle from all linked delegated control charging stations
     * @summary Unlink a vehicle from all linked delegated control charging stations
     * @param {string} vehicleId The vehicleId associated to the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unlinkVehicleFromDelegatedControlChargingStations(
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .unlinkVehicleFromDelegatedControlChargingStations(vehicleId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} ppid The PPID of the charging station
     * @param {string} vehicleId The vehicle ID
     * @param {UpdateVehicleLinkRequestDto} updateVehicleLinkRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateVehicleById(
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDto: UpdateVehicleLinkRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UpdateVehicleById200Response> {
      return localVarFp
        .updateVehicleById(
          ppid,
          vehicleId,
          updateVehicleLinkRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DelegatedControlVehiclesApi - object-oriented interface
 * @export
 * @class DelegatedControlVehiclesApi
 * @extends {BaseAPI}
 */
export class DelegatedControlVehiclesApi extends BaseAPI {
  /**
   * Add vehicle to delegated control charging station
   * @summary Add vehicle to delegated control charging station
   * @param {string} ppid
   * @param {CreateVehicleLinkRequestDto} createVehicleLinkRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public addVehicleToDelegatedControlChargingStation(
    ppid: string,
    createVehicleLinkRequestDto: CreateVehicleLinkRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .addVehicleToDelegatedControlChargingStation(
        ppid,
        createVehicleLinkRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all charging stations linked to a vehicle
   * @summary Get all charging stations linked to a vehicle
   * @param {string} vehicleId The vehicleId linked to the charging stations
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public getChargingStationsLinkedToVehicle(
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .getChargingStationsLinkedToVehicle(vehicleId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all vehicles linked to a delegated control charging station
   * @summary Get delegated control charging station vehicles
   * @param {string} ppid The PPID of the delegated charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public getDelegatedControlChargingStationVehicles(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .getDelegatedControlChargingStationVehicles(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get vehicle
   * @summary Get vehicle
   * @param {string} ppid The PPID of the charging station
   * @param {string} vehicleId The vehicleId associated to the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public getVehicle(
    ppid: string,
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .getVehicle(ppid, vehicleId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Unlink a vehicle from a delegated control charging station
   * @summary Unlink a vehicle from a delegated control charging station
   * @param {string} ppid The PPID of the charging station
   * @param {string} vehicleId The vehicleId associated to the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public unlinkVehicleFromDelegatedControlChargingStation(
    ppid: string,
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .unlinkVehicleFromDelegatedControlChargingStation(
        ppid,
        vehicleId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Unlink a vehicle from all linked delegated control charging stations
   * @summary Unlink a vehicle from all linked delegated control charging stations
   * @param {string} vehicleId The vehicleId associated to the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public unlinkVehicleFromDelegatedControlChargingStations(
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .unlinkVehicleFromDelegatedControlChargingStations(vehicleId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle ID and new data, updates the vehicle
   * @summary Update vehicle data by ID
   * @param {string} ppid The PPID of the charging station
   * @param {string} vehicleId The vehicle ID
   * @param {UpdateVehicleLinkRequestDto} updateVehicleLinkRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DelegatedControlVehiclesApi
   */
  public updateVehicleById(
    ppid: string,
    vehicleId: string,
    updateVehicleLinkRequestDto: UpdateVehicleLinkRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return DelegatedControlVehiclesApiFp(this.configuration)
      .updateVehicleById(ppid, vehicleId, updateVehicleLinkRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * EnergyOfferStatusApi - axios parameter creator
 * @export
 */
export const EnergyOfferStatusApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnergyOfferStatus: async (
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationEnergyOfferStatus', 'ppid', ppid);
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'getChargingStationEnergyOfferStatus',
        'evseId',
        evseId
      );
      const localVarPath =
        `/charging-stations/{ppid}/evses/{evseId}/energy-offer-status`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'evseId'}}`, encodeURIComponent(String(evseId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (cacheControl != null) {
        localVarHeaderParameter['Cache-Control'] = String(cacheControl);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnergyOfferStatus_1: async (
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationEnergyOfferStatus_1', 'ppid', ppid);
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'getChargingStationEnergyOfferStatus_1',
        'evseId',
        evseId
      );
      const localVarPath =
        `/charging-stations/{ppid}/evses/{evseId}/energy-offer-status`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'evseId'}}`, encodeURIComponent(String(evseId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (cacheControl != null) {
        localVarHeaderParameter['Cache-Control'] = String(cacheControl);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * EnergyOfferStatusApi - functional programming interface
 * @export
 */
export const EnergyOfferStatusApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    EnergyOfferStatusApiAxiosParamCreator(configuration);
  return {
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationEnergyOfferStatus(
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<EnergyOfferStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationEnergyOfferStatus(
          ppid,
          evseId,
          cacheControl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'EnergyOfferStatusApi.getChargingStationEnergyOfferStatus'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationEnergyOfferStatus_1(
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<EnergyOfferStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationEnergyOfferStatus_1(
          ppid,
          evseId,
          cacheControl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'EnergyOfferStatusApi.getChargingStationEnergyOfferStatus_1'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * EnergyOfferStatusApi - factory interface
 * @export
 */
export const EnergyOfferStatusApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = EnergyOfferStatusApiFp(configuration);
  return {
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnergyOfferStatus(
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EnergyOfferStatusResponse> {
      return localVarFp
        .getChargingStationEnergyOfferStatus(
          ppid,
          evseId,
          cacheControl,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns if a charger should offer energy
     * @summary Returns if a charger should offer energy
     * @param {string} ppid The PPID of the charging station
     * @param {number} evseId The evseId of the door
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnergyOfferStatus_1(
      ppid: string,
      evseId: number,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EnergyOfferStatusResponse> {
      return localVarFp
        .getChargingStationEnergyOfferStatus_1(
          ppid,
          evseId,
          cacheControl,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * EnergyOfferStatusApi - object-oriented interface
 * @export
 * @class EnergyOfferStatusApi
 * @extends {BaseAPI}
 */
export class EnergyOfferStatusApi extends BaseAPI {
  /**
   * Returns if a charger should offer energy
   * @summary Returns if a charger should offer energy
   * @param {string} ppid The PPID of the charging station
   * @param {number} evseId The evseId of the door
   * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnergyOfferStatusApi
   */
  public getChargingStationEnergyOfferStatus(
    ppid: string,
    evseId: number,
    cacheControl?: string,
    options?: RawAxiosRequestConfig
  ) {
    return EnergyOfferStatusApiFp(this.configuration)
      .getChargingStationEnergyOfferStatus(ppid, evseId, cacheControl, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns if a charger should offer energy
   * @summary Returns if a charger should offer energy
   * @param {string} ppid The PPID of the charging station
   * @param {number} evseId The evseId of the door
   * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnergyOfferStatusApi
   */
  public getChargingStationEnergyOfferStatus_1(
    ppid: string,
    evseId: number,
    cacheControl?: string,
    options?: RawAxiosRequestConfig
  ) {
    return EnergyOfferStatusApiFp(this.configuration)
      .getChargingStationEnergyOfferStatus_1(
        ppid,
        evseId,
        cacheControl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * FlexibilityRequestsApi - axios parameter creator
 * @export
 */
export const FlexibilityRequestsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a flexibility request for all charging stations in a given programme asynchronously
     * @summary Create flexibility request for all charging stations in a given programme asynchronously
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    asyncCreateFlexibilityRequestForProgramme: async (
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists(
        'asyncCreateFlexibilityRequestForProgramme',
        'programmeId',
        programmeId
      );
      // verify required parameter 'createFlexRequestDto' is not null or undefined
      assertParamExists(
        'asyncCreateFlexibilityRequestForProgramme',
        'createFlexRequestDto',
        createFlexRequestDto
      );
      const localVarPath =
        `/programmes/{programmeId}/flexibility-requests/submit-async`.replace(
          `{${'programmeId'}}`,
          encodeURIComponent(String(programmeId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createFlexRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a flexibility request for a given charger
     * @summary Create flexibility request
     * @param {string} ppid The PPID of the charging station
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStationFlexibilityRequest: async (
      ppid: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'createChargingStationFlexibilityRequest',
        'ppid',
        ppid
      );
      // verify required parameter 'createFlexRequestDto' is not null or undefined
      assertParamExists(
        'createChargingStationFlexibilityRequest',
        'createFlexRequestDto',
        createFlexRequestDto
      );
      const localVarPath =
        `/charging-stations/{ppid}/flexibility-requests`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createFlexRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a flexibility request for all charging stations in a given programme
     * @summary Create flexibility request for all charging stations in a given programme
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createFlexibilityRequestForProgramme: async (
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists(
        'createFlexibilityRequestForProgramme',
        'programmeId',
        programmeId
      );
      // verify required parameter 'createFlexRequestDto' is not null or undefined
      assertParamExists(
        'createFlexibilityRequestForProgramme',
        'createFlexRequestDto',
        createFlexRequestDto
      );
      const localVarPath =
        `/programmes/{programmeId}/flexibility-requests`.replace(
          `{${'programmeId'}}`,
          encodeURIComponent(String(programmeId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createFlexRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete a flexibility request given its id
     * @summary Delete flexibility request
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteFlexibilityRequest: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('deleteFlexibilityRequest', 'id', id);
      const localVarPath = `/flexibility-requests/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete all flexibility requests for a given charging station
     * @summary Delete flexibility requests for charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteFlexibilityRequestsForChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'deleteFlexibilityRequestsForChargingStation',
        'ppid',
        ppid
      );
      const localVarPath =
        `/charging-stations/{ppid}/flexibility-requests`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the active flexibility requests for a given charger Results are limited to the first 1000, either ordered by increasing end times Or by decreasing start times if the includePast query param is set to true
     * @summary Get active flexibility requests
     * @param {string} ppid
     * @param {boolean} [includePast] Include past requests (defaults to false) if \&quot;true\&quot;
     * @param {boolean} [includeDeleted] Include deleted (canalled) requests (defaults to false) if \&quot;true\&quot;
     * @param {string} [from] Return only requests starting after this date
     * @param {string} [to] Return only requests starting before this date
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getActiveChargingStationFlexibilityRequests: async (
      ppid: string,
      includePast?: boolean,
      includeDeleted?: boolean,
      from?: string,
      to?: string,
      cacheControl?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'getActiveChargingStationFlexibilityRequests',
        'ppid',
        ppid
      );
      const localVarPath =
        `/charging-stations/{ppid}/flexibility-requests`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includePast !== undefined) {
        localVarQueryParameter['includePast'] = includePast;
      }

      if (includeDeleted !== undefined) {
        localVarQueryParameter['includeDeleted'] = includeDeleted;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] = from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] = to;
      }

      if (cacheControl != null) {
        localVarHeaderParameter['Cache-Control'] = String(cacheControl);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a flexibility request given it\'s external provider info
     * @summary Get flexibility request by provider info
     * @param {string} providerName The name of the competition provider
     * @param {string} providerFlexRequestId The provider\&#39;s id for this flex request
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getFlexibilityRequestByProviderInfo: async (
      providerName: string,
      providerFlexRequestId: string,
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'providerName' is not null or undefined
      assertParamExists(
        'getFlexibilityRequestByProviderInfo',
        'providerName',
        providerName
      );
      // verify required parameter 'providerFlexRequestId' is not null or undefined
      assertParamExists(
        'getFlexibilityRequestByProviderInfo',
        'providerFlexRequestId',
        providerFlexRequestId
      );
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getFlexibilityRequestByProviderInfo', 'ppid', ppid);
      const localVarPath = `/flexibility-requests`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (providerName !== undefined) {
        localVarQueryParameter['providerName'] = providerName;
      }

      if (providerFlexRequestId !== undefined) {
        localVarQueryParameter['providerFlexRequestId'] = providerFlexRequestId;
      }

      if (ppid !== undefined) {
        localVarQueryParameter['ppid'] = ppid;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Search for flexibility requests given criteria
     * @summary Search for flexibility requests given criteria
     * @param {string} startAt The flex request start date/time
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchFlexibilityRequests: async (
      startAt: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'startAt' is not null or undefined
      assertParamExists('searchFlexibilityRequests', 'startAt', startAt);
      const localVarPath = `/flexibility-requests/search`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (startAt !== undefined) {
        localVarQueryParameter['startAt'] = startAt;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a flexibility request given its id
     * @summary Update flexibility request
     * @param {string} id The id of the flexibility request
     * @param {UpdateFlexRequestDto} updateFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateFlexibilityRequest: async (
      id: string,
      updateFlexRequestDto: UpdateFlexRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('updateFlexibilityRequest', 'id', id);
      // verify required parameter 'updateFlexRequestDto' is not null or undefined
      assertParamExists(
        'updateFlexibilityRequest',
        'updateFlexRequestDto',
        updateFlexRequestDto
      );
      const localVarPath = `/flexibility-requests/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateFlexRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * FlexibilityRequestsApi - functional programming interface
 * @export
 */
export const FlexibilityRequestsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    FlexibilityRequestsApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a flexibility request for all charging stations in a given programme asynchronously
     * @summary Create flexibility request for all charging stations in a given programme asynchronously
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async asyncCreateFlexibilityRequestForProgramme(
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.asyncCreateFlexibilityRequestForProgramme(
          programmeId,
          createFlexRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.asyncCreateFlexibilityRequestForProgramme'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Create a flexibility request for a given charger
     * @summary Create flexibility request
     * @param {string} ppid The PPID of the charging station
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createChargingStationFlexibilityRequest(
      ppid: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FlexRequestResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createChargingStationFlexibilityRequest(
          ppid,
          createFlexRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.createChargingStationFlexibilityRequest'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Create a flexibility request for all charging stations in a given programme
     * @summary Create flexibility request for all charging stations in a given programme
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createFlexibilityRequestForProgramme(
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateFlexibilityRequestForProgramme200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createFlexibilityRequestForProgramme(
          programmeId,
          createFlexRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.createFlexibilityRequestForProgramme'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete a flexibility request given its id
     * @summary Delete flexibility request
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteFlexibilityRequest(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteFlexibilityRequest(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['FlexibilityRequestsApi.deleteFlexibilityRequest']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete all flexibility requests for a given charging station
     * @summary Delete flexibility requests for charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteFlexibilityRequestsForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteFlexibilityRequestsForChargingStation(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.deleteFlexibilityRequestsForChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the active flexibility requests for a given charger Results are limited to the first 1000, either ordered by increasing end times Or by decreasing start times if the includePast query param is set to true
     * @summary Get active flexibility requests
     * @param {string} ppid
     * @param {boolean} [includePast] Include past requests (defaults to false) if \&quot;true\&quot;
     * @param {boolean} [includeDeleted] Include deleted (canalled) requests (defaults to false) if \&quot;true\&quot;
     * @param {string} [from] Return only requests starting after this date
     * @param {string} [to] Return only requests starting before this date
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getActiveChargingStationFlexibilityRequests(
      ppid: string,
      includePast?: boolean,
      includeDeleted?: boolean,
      from?: string,
      to?: string,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<FlexRequestResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getActiveChargingStationFlexibilityRequests(
          ppid,
          includePast,
          includeDeleted,
          from,
          to,
          cacheControl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.getActiveChargingStationFlexibilityRequests'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a flexibility request given it\'s external provider info
     * @summary Get flexibility request by provider info
     * @param {string} providerName The name of the competition provider
     * @param {string} providerFlexRequestId The provider\&#39;s id for this flex request
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getFlexibilityRequestByProviderInfo(
      providerName: string,
      providerFlexRequestId: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FlexRequestResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getFlexibilityRequestByProviderInfo(
          providerName,
          providerFlexRequestId,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.getFlexibilityRequestByProviderInfo'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Search for flexibility requests given criteria
     * @summary Search for flexibility requests given criteria
     * @param {string} startAt The flex request start date/time
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchFlexibilityRequests(
      startAt: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FlexRequestSearchResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.searchFlexibilityRequests(
          startAt,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexibilityRequestsApi.searchFlexibilityRequests'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a flexibility request given its id
     * @summary Update flexibility request
     * @param {string} id The id of the flexibility request
     * @param {UpdateFlexRequestDto} updateFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateFlexibilityRequest(
      id: string,
      updateFlexRequestDto: UpdateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FlexRequestResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateFlexibilityRequest(
          id,
          updateFlexRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['FlexibilityRequestsApi.updateFlexibilityRequest']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * FlexibilityRequestsApi - factory interface
 * @export
 */
export const FlexibilityRequestsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = FlexibilityRequestsApiFp(configuration);
  return {
    /**
     * Create a flexibility request for all charging stations in a given programme asynchronously
     * @summary Create flexibility request for all charging stations in a given programme asynchronously
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    asyncCreateFlexibilityRequestForProgramme(
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .asyncCreateFlexibilityRequestForProgramme(
          programmeId,
          createFlexRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a flexibility request for a given charger
     * @summary Create flexibility request
     * @param {string} ppid The PPID of the charging station
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createChargingStationFlexibilityRequest(
      ppid: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FlexRequestResponse> {
      return localVarFp
        .createChargingStationFlexibilityRequest(
          ppid,
          createFlexRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a flexibility request for all charging stations in a given programme
     * @summary Create flexibility request for all charging stations in a given programme
     * @param {string} programmeId
     * @param {CreateFlexRequestDto} createFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createFlexibilityRequestForProgramme(
      programmeId: string,
      createFlexRequestDto: CreateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateFlexibilityRequestForProgramme200Response> {
      return localVarFp
        .createFlexibilityRequestForProgramme(
          programmeId,
          createFlexRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete a flexibility request given its id
     * @summary Delete flexibility request
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteFlexibilityRequest(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteFlexibilityRequest(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete all flexibility requests for a given charging station
     * @summary Delete flexibility requests for charging station
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteFlexibilityRequestsForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteFlexibilityRequestsForChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the active flexibility requests for a given charger Results are limited to the first 1000, either ordered by increasing end times Or by decreasing start times if the includePast query param is set to true
     * @summary Get active flexibility requests
     * @param {string} ppid
     * @param {boolean} [includePast] Include past requests (defaults to false) if \&quot;true\&quot;
     * @param {boolean} [includeDeleted] Include deleted (canalled) requests (defaults to false) if \&quot;true\&quot;
     * @param {string} [from] Return only requests starting after this date
     * @param {string} [to] Return only requests starting before this date
     * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getActiveChargingStationFlexibilityRequests(
      ppid: string,
      includePast?: boolean,
      includeDeleted?: boolean,
      from?: string,
      to?: string,
      cacheControl?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<FlexRequestResponse>> {
      return localVarFp
        .getActiveChargingStationFlexibilityRequests(
          ppid,
          includePast,
          includeDeleted,
          from,
          to,
          cacheControl,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a flexibility request given it\'s external provider info
     * @summary Get flexibility request by provider info
     * @param {string} providerName The name of the competition provider
     * @param {string} providerFlexRequestId The provider\&#39;s id for this flex request
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getFlexibilityRequestByProviderInfo(
      providerName: string,
      providerFlexRequestId: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FlexRequestResponse> {
      return localVarFp
        .getFlexibilityRequestByProviderInfo(
          providerName,
          providerFlexRequestId,
          ppid,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Search for flexibility requests given criteria
     * @summary Search for flexibility requests given criteria
     * @param {string} startAt The flex request start date/time
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchFlexibilityRequests(
      startAt: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FlexRequestSearchResponse> {
      return localVarFp
        .searchFlexibilityRequests(startAt, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a flexibility request given its id
     * @summary Update flexibility request
     * @param {string} id The id of the flexibility request
     * @param {UpdateFlexRequestDto} updateFlexRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateFlexibilityRequest(
      id: string,
      updateFlexRequestDto: UpdateFlexRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FlexRequestResponse> {
      return localVarFp
        .updateFlexibilityRequest(id, updateFlexRequestDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * FlexibilityRequestsApi - object-oriented interface
 * @export
 * @class FlexibilityRequestsApi
 * @extends {BaseAPI}
 */
export class FlexibilityRequestsApi extends BaseAPI {
  /**
   * Create a flexibility request for all charging stations in a given programme asynchronously
   * @summary Create flexibility request for all charging stations in a given programme asynchronously
   * @param {string} programmeId
   * @param {CreateFlexRequestDto} createFlexRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public asyncCreateFlexibilityRequestForProgramme(
    programmeId: string,
    createFlexRequestDto: CreateFlexRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .asyncCreateFlexibilityRequestForProgramme(
        programmeId,
        createFlexRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a flexibility request for a given charger
   * @summary Create flexibility request
   * @param {string} ppid The PPID of the charging station
   * @param {CreateFlexRequestDto} createFlexRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public createChargingStationFlexibilityRequest(
    ppid: string,
    createFlexRequestDto: CreateFlexRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .createChargingStationFlexibilityRequest(
        ppid,
        createFlexRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a flexibility request for all charging stations in a given programme
   * @summary Create flexibility request for all charging stations in a given programme
   * @param {string} programmeId
   * @param {CreateFlexRequestDto} createFlexRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public createFlexibilityRequestForProgramme(
    programmeId: string,
    createFlexRequestDto: CreateFlexRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .createFlexibilityRequestForProgramme(
        programmeId,
        createFlexRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete a flexibility request given its id
   * @summary Delete flexibility request
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public deleteFlexibilityRequest(id: string, options?: RawAxiosRequestConfig) {
    return FlexibilityRequestsApiFp(this.configuration)
      .deleteFlexibilityRequest(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete all flexibility requests for a given charging station
   * @summary Delete flexibility requests for charging station
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public deleteFlexibilityRequestsForChargingStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .deleteFlexibilityRequestsForChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the active flexibility requests for a given charger Results are limited to the first 1000, either ordered by increasing end times Or by decreasing start times if the includePast query param is set to true
   * @summary Get active flexibility requests
   * @param {string} ppid
   * @param {boolean} [includePast] Include past requests (defaults to false) if \&quot;true\&quot;
   * @param {boolean} [includeDeleted] Include deleted (canalled) requests (defaults to false) if \&quot;true\&quot;
   * @param {string} [from] Return only requests starting after this date
   * @param {string} [to] Return only requests starting before this date
   * @param {string} [cacheControl] Set to \&quot;refresh\&quot; to force a refresh
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public getActiveChargingStationFlexibilityRequests(
    ppid: string,
    includePast?: boolean,
    includeDeleted?: boolean,
    from?: string,
    to?: string,
    cacheControl?: string,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .getActiveChargingStationFlexibilityRequests(
        ppid,
        includePast,
        includeDeleted,
        from,
        to,
        cacheControl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a flexibility request given it\'s external provider info
   * @summary Get flexibility request by provider info
   * @param {string} providerName The name of the competition provider
   * @param {string} providerFlexRequestId The provider\&#39;s id for this flex request
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public getFlexibilityRequestByProviderInfo(
    providerName: string,
    providerFlexRequestId: string,
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .getFlexibilityRequestByProviderInfo(
        providerName,
        providerFlexRequestId,
        ppid,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Search for flexibility requests given criteria
   * @summary Search for flexibility requests given criteria
   * @param {string} startAt The flex request start date/time
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public searchFlexibilityRequests(
    startAt: string,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .searchFlexibilityRequests(startAt, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a flexibility request given its id
   * @summary Update flexibility request
   * @param {string} id The id of the flexibility request
   * @param {UpdateFlexRequestDto} updateFlexRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexibilityRequestsApi
   */
  public updateFlexibilityRequest(
    id: string,
    updateFlexRequestDto: UpdateFlexRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return FlexibilityRequestsApiFp(this.configuration)
      .updateFlexibilityRequest(id, updateFlexRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthApi - axios parameter creator
 * @export
 */
export const HealthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthApi - functional programming interface
 * @export
 */
export const HealthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = HealthApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthApi - factory interface
 * @export
 */
export const HealthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthApi - object-oriented interface
 * @export
 * @class HealthApi
 * @extends {BaseAPI}
 */
export class HealthApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RewardsApi - axios parameter creator
 * @export
 */
export const RewardsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.
     * @summary Convert a charge into reward points
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    convertChargeToRewardPoints: async (
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('convertChargeToRewardPoints', 'ppid', ppid);
      // verify required parameter 'chargeStatisticsDto' is not null or undefined
      assertParamExists(
        'convertChargeToRewardPoints',
        'chargeStatisticsDto',
        chargeStatisticsDto
      );
      const localVarPath = `/reward-points/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargeStatisticsDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get reward information for a charging session
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingSessionRewardInfo: async (
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingSessionRewardInfo', 'ppid', ppid);
      // verify required parameter 'chargeStatisticsDto' is not null or undefined
      assertParamExists(
        'getChargingSessionRewardInfo',
        'chargeStatisticsDto',
        chargeStatisticsDto
      );
      const localVarPath = `/reward-info/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargeStatisticsDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RewardsApi - functional programming interface
 * @export
 */
export const RewardsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = RewardsApiAxiosParamCreator(configuration);
  return {
    /**
     * Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.
     * @summary Convert a charge into reward points
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async convertChargeToRewardPoints(
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<RewardPointsDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.convertChargeToRewardPoints(
          ppid,
          chargeStatisticsDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.convertChargeToRewardPoints']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get reward information for a charging session
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingSessionRewardInfo(
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<RewardInfoDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingSessionRewardInfo(
          ppid,
          chargeStatisticsDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.getChargingSessionRewardInfo']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RewardsApi - factory interface
 * @export
 */
export const RewardsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RewardsApiFp(configuration);
  return {
    /**
     * Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.
     * @summary Convert a charge into reward points
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    convertChargeToRewardPoints(
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<RewardPointsDto> {
      return localVarFp
        .convertChargeToRewardPoints(ppid, chargeStatisticsDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get reward information for a charging session
     * @param {string} ppid
     * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingSessionRewardInfo(
      ppid: string,
      chargeStatisticsDto: ChargeStatisticsDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<RewardInfoDto> {
      return localVarFp
        .getChargingSessionRewardInfo(ppid, chargeStatisticsDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RewardsApi - object-oriented interface
 * @export
 * @class RewardsApi
 * @extends {BaseAPI}
 */
export class RewardsApi extends BaseAPI {
  /**
   * Get reward points from charge statistics. Note: this endpoint is to be replaced by POST /reward-info/:ppid and should be considered deprecated.
   * @summary Convert a charge into reward points
   * @param {string} ppid
   * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public convertChargeToRewardPoints(
    ppid: string,
    chargeStatisticsDto: ChargeStatisticsDto,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .convertChargeToRewardPoints(ppid, chargeStatisticsDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get reward information for a charging session
   * @param {string} ppid
   * @param {ChargeStatisticsDto} chargeStatisticsDto Charge statistics
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public getChargingSessionRewardInfo(
    ppid: string,
    chargeStatisticsDto: ChargeStatisticsDto,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .getChargingSessionRewardInfo(ppid, chargeStatisticsDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
