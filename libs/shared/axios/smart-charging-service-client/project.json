{"name": "shared-axios-smart-charging-service-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/smart-charging-service-client/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i openapi.json -o src", "rm openapitools.json", "npx prettier . --write"]}}, "regenerate-client": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Fetch the latest OpenAPI spec from the smart-charging-service repository", "command": "gh api -H \"Accept: application/vnd.github.v3.raw\" /repos/Pod-Point/smart-charging-service/contents/docs/api/current/smart-charging-service-api.json > libs/shared/axios/smart-charging-service-client/openapi.json"}, {"description": "Generate the client from the OpenAPI spec", "command": "nx run shared-axios-smart-charging-service-client:generate-sources"}], "parallel": false}}}, "tags": ["shared"]}