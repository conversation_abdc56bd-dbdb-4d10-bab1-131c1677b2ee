{"name": "shared-axios-tariffs-api-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/tariffs-api-client/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i openapi.json -o src", "rm openapitools.json", "npx prettier . --write"]}, "configurations": {"docker": {"cwd": "", "commands": ["docker run --rm -v $(pwd):/local -w /local openapitools/openapi-generator-cli generate -g typescript-axios -i libs/shared/axios/tariffs-api-client/openapi.json -o libs/shared/axios/tariffs-api-client/src", "npx prettier libs/shared/axios/tariffs-api-client/src --write"]}}}}, "tags": ["shared"]}