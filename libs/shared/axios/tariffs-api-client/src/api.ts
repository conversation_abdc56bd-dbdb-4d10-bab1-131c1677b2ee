/* tslint:disable */
/* eslint-disable */
/**
 * Tariffs Api
 * API for managing tariffs api
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface ChargingStationTariffSearchCriteriaDto
 */
export interface ChargingStationTariffSearchCriteriaDto {
  /**
   * Charging Station PPID
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDto
   */
  ppid: string;
  /**
   * The effective from date
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDto
   */
  effectiveFrom?: string;
  /**
   * The effective to date
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDto
   */
  effectiveTo?: string;
}
/**
 *
 * @export
 * @interface ChargingStationTariffSearchMetadataDto
 */
export interface ChargingStationTariffSearchMetadataDto {
  /**
   * The search criteria used
   * @type {ChargingStationTariffSearchCriteriaDto}
   * @memberof ChargingStationTariffSearchMetadataDto
   */
  criteria: ChargingStationTariffSearchCriteriaDto;
}
/**
 *
 * @export
 * @interface ChargingStationTariffSearchResponseDto
 */
export interface ChargingStationTariffSearchResponseDto {
  /**
   * The tariffs matching the given criteria
   * @type {Array<PersistedTariffRowDto>}
   * @memberof ChargingStationTariffSearchResponseDto
   */
  data: Array<PersistedTariffRowDto>;
  /**
   *
   * @type {ChargingStationTariffSearchMetadataDto}
   * @memberof ChargingStationTariffSearchResponseDto
   */
  metadata: ChargingStationTariffSearchMetadataDto;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface PersistedTariffRowDto
 */
export interface PersistedTariffRowDto {
  /**
   * The charging station PPID
   * @type {string}
   * @memberof PersistedTariffRowDto
   */
  ppid: string;
  /**
   * Reference to the supplier. Null if the supplier is unknown.
   * @type {string}
   * @memberof PersistedTariffRowDto
   */
  supplierId: string | null;
  /**
   * Array of tariff information applicable to specific days and times.
   * @type {Array<TariffInfoDto>}
   * @memberof PersistedTariffRowDto
   */
  tariffInfo: Array<TariffInfoDto>;
  /**
   * Timezone the tariff information applies to.
   * @type {string}
   * @memberof PersistedTariffRowDto
   */
  timezone: string;
  /**
   * The maximum price during off-peak hours (e.g., £0.20).
   * @type {number}
   * @memberof PersistedTariffRowDto
   */
  maxChargePrice?: number;
  /**
   * The date from which the tariff is effective in the format YYYY-MM-DD
   * @type {string}
   * @memberof PersistedTariffRowDto
   */
  effectiveFrom: string;
  /**
   * Unique ID for the tariff
   * @type {string}
   * @memberof PersistedTariffRowDto
   */
  id: string;
  /**
   * The cheapest unit price (e.g., £0.10).
   * @type {number}
   * @memberof PersistedTariffRowDto
   */
  cheapestUnitPrice: number;
}
/**
 *
 * @export
 * @interface SupplierDto
 */
export interface SupplierDto {
  /**
   *
   * @type {string}
   * @memberof SupplierDto
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof SupplierDto
   */
  name: string;
  /**
   * The timezone of the off peak hours
   * @type {string}
   * @memberof SupplierDto
   */
  timeZone: SupplierDtoTimeZoneEnum;
  /**
   *
   * @type {string}
   * @memberof SupplierDto
   */
  icon: string;
  /**
   * Array of tariff information applicable to specific days and times.
   * @type {Array<TariffInfoDto>}
   * @memberof SupplierDto
   */
  defaultTariffInfo: Array<TariffInfoDto>;
  /**
   * The maximum price during off-peak hours (e.g., £0.20).
   * @type {number}
   * @memberof SupplierDto
   */
  defaultMaxChargePrice: number;
}

export const SupplierDtoTimeZoneEnum = {
  EuropeLondon: 'Europe/London',
  EuropeMadrid: 'Europe/Madrid',
  EuropeParis: 'Europe/Paris',
  EtcUtc: 'Etc/UTC',
} as const;

export type SupplierDtoTimeZoneEnum =
  (typeof SupplierDtoTimeZoneEnum)[keyof typeof SupplierDtoTimeZoneEnum];

/**
 *
 * @export
 * @interface TariffInfoDto
 */
export interface TariffInfoDto {
  /**
   * Start time in the format HH:mm:ss.
   * @type {string}
   * @memberof TariffInfoDto
   */
  start: string;
  /**
   * End time in the format HH:mm:ss.
   * @type {string}
   * @memberof TariffInfoDto
   */
  end: string;
  /**
   * Price per unit (e.g., £0.30).
   * @type {number}
   * @memberof TariffInfoDto
   */
  price: number;
  /**
   * Days of the week this tariff applies to. A tariff should always cover the entire week - all hours of each day.
   * @type {Array<string>}
   * @memberof TariffInfoDto
   */
  days: Array<TariffInfoDtoDaysEnum>;
}

export const TariffInfoDtoDaysEnum = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type TariffInfoDtoDaysEnum =
  (typeof TariffInfoDtoDaysEnum)[keyof typeof TariffInfoDtoDaysEnum];

/**
 *
 * @export
 * @interface TariffRowDto
 */
export interface TariffRowDto {
  /**
   * The charging station PPID
   * @type {string}
   * @memberof TariffRowDto
   */
  ppid: string;
  /**
   * Reference to the supplier. Null if the supplier is unknown.
   * @type {string}
   * @memberof TariffRowDto
   */
  supplierId: string | null;
  /**
   * Array of tariff information applicable to specific days and times.
   * @type {Array<TariffInfoDto>}
   * @memberof TariffRowDto
   */
  tariffInfo: Array<TariffInfoDto>;
  /**
   * Timezone the tariff information applies to.
   * @type {string}
   * @memberof TariffRowDto
   */
  timezone: string;
  /**
   * The maximum price during off-peak hours (e.g., £0.20).
   * @type {number}
   * @memberof TariffRowDto
   */
  maxChargePrice?: number;
  /**
   * The date from which the tariff is effective in the format YYYY-MM-DD
   * @type {string}
   * @memberof TariffRowDto
   */
  effectiveFrom: string;
}

/**
 * ChargingStationsTariffsApi - axios parameter creator
 * @export
 */
export const ChargingStationsTariffsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates a new tariff for a charging station
     * @summary Create a new tariff
     * @param {string} ppid Charging Station PPID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTariffForChargingStation: async (
      ppid: string,
      tariffRowDto: TariffRowDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('createTariffForChargingStation', 'ppid', ppid);
      // verify required parameter 'tariffRowDto' is not null or undefined
      assertParamExists(
        'createTariffForChargingStation',
        'tariffRowDto',
        tariffRowDto
      );
      const localVarPath = `/charging-stations/{ppid}/tariffs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        tariffRowDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a PPID, delete all the tariffs for the charging station
     * @summary Delete all tariffs
     * @param {string} ppid Charging Station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteAllTariffForChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('deleteAllTariffForChargingStation', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/tariffs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a PPID and tariff ID, delete the tariff
     * @summary Delete a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteTariff: async (
      ppid: string,
      tariffId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('deleteTariff', 'ppid', ppid);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists('deleteTariff', 'tariffId', tariffId);
      const localVarPath = `/charging-stations/{ppid}/tariffs/{tariffId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'tariffId'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a PPID, return the tariffs with optional effective date filtering.
     * @summary Get Tariffs by Charging Station PPID
     * @param {string} ppid Charging Station PPID
     * @param {string} [effectiveFrom] Include tariffs effective from this date (inclusive).
     * @param {string} [effectiveTo] Include tariffs effective to this date (inclusive).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTariffsByPpid: async (
      ppid: string,
      effectiveFrom?: string,
      effectiveTo?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getTariffsByPpid', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/tariffs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (effectiveFrom !== undefined) {
        localVarQueryParameter['effectiveFrom'] = effectiveFrom;
      }

      if (effectiveTo !== undefined) {
        localVarQueryParameter['effectiveTo'] = effectiveTo;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary Update a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateTariff: async (
      ppid: string,
      tariffId: string,
      tariffRowDto: TariffRowDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('updateTariff', 'ppid', ppid);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists('updateTariff', 'tariffId', tariffId);
      // verify required parameter 'tariffRowDto' is not null or undefined
      assertParamExists('updateTariff', 'tariffRowDto', tariffRowDto);
      const localVarPath = `/charging-stations/{ppid}/tariffs/{tariffId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'tariffId'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        tariffRowDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingStationsTariffsApi - functional programming interface
 * @export
 */
export const ChargingStationsTariffsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ChargingStationsTariffsApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates a new tariff for a charging station
     * @summary Create a new tariff
     * @param {string} ppid Charging Station PPID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createTariffForChargingStation(
      ppid: string,
      tariffRowDto: TariffRowDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PersistedTariffRowDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createTariffForChargingStation(
          ppid,
          tariffRowDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStationsTariffsApi.createTariffForChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a PPID, delete all the tariffs for the charging station
     * @summary Delete all tariffs
     * @param {string} ppid Charging Station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteAllTariffForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteAllTariffForChargingStation(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStationsTariffsApi.deleteAllTariffForChargingStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a PPID and tariff ID, delete the tariff
     * @summary Delete a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteTariff(
      ppid: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.deleteTariff(
        ppid,
        tariffId,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationsTariffsApi.deleteTariff']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a PPID, return the tariffs with optional effective date filtering.
     * @summary Get Tariffs by Charging Station PPID
     * @param {string} ppid Charging Station PPID
     * @param {string} [effectiveFrom] Include tariffs effective from this date (inclusive).
     * @param {string} [effectiveTo] Include tariffs effective to this date (inclusive).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getTariffsByPpid(
      ppid: string,
      effectiveFrom?: string,
      effectiveTo?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargingStationTariffSearchResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getTariffsByPpid(
          ppid,
          effectiveFrom,
          effectiveTo,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationsTariffsApi.getTariffsByPpid']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary Update a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateTariff(
      ppid: string,
      tariffId: string,
      tariffRowDto: TariffRowDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PersistedTariffRowDto>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.updateTariff(
        ppid,
        tariffId,
        tariffRowDto,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationsTariffsApi.updateTariff']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingStationsTariffsApi - factory interface
 * @export
 */
export const ChargingStationsTariffsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingStationsTariffsApiFp(configuration);
  return {
    /**
     * Creates a new tariff for a charging station
     * @summary Create a new tariff
     * @param {string} ppid Charging Station PPID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTariffForChargingStation(
      ppid: string,
      tariffRowDto: TariffRowDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PersistedTariffRowDto> {
      return localVarFp
        .createTariffForChargingStation(ppid, tariffRowDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a PPID, delete all the tariffs for the charging station
     * @summary Delete all tariffs
     * @param {string} ppid Charging Station PPID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteAllTariffForChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteAllTariffForChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a PPID and tariff ID, delete the tariff
     * @summary Delete a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteTariff(
      ppid: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteTariff(ppid, tariffId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a PPID, return the tariffs with optional effective date filtering.
     * @summary Get Tariffs by Charging Station PPID
     * @param {string} ppid Charging Station PPID
     * @param {string} [effectiveFrom] Include tariffs effective from this date (inclusive).
     * @param {string} [effectiveTo] Include tariffs effective to this date (inclusive).
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTariffsByPpid(
      ppid: string,
      effectiveFrom?: string,
      effectiveTo?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargingStationTariffSearchResponseDto> {
      return localVarFp
        .getTariffsByPpid(ppid, effectiveFrom, effectiveTo, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary Update a tariff
     * @param {string} ppid Charging Station PPID
     * @param {string} tariffId Tariff ID
     * @param {TariffRowDto} tariffRowDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateTariff(
      ppid: string,
      tariffId: string,
      tariffRowDto: TariffRowDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PersistedTariffRowDto> {
      return localVarFp
        .updateTariff(ppid, tariffId, tariffRowDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingStationsTariffsApi - object-oriented interface
 * @export
 * @class ChargingStationsTariffsApi
 * @extends {BaseAPI}
 */
export class ChargingStationsTariffsApi extends BaseAPI {
  /**
   * Creates a new tariff for a charging station
   * @summary Create a new tariff
   * @param {string} ppid Charging Station PPID
   * @param {TariffRowDto} tariffRowDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsTariffsApi
   */
  public createTariffForChargingStation(
    ppid: string,
    tariffRowDto: TariffRowDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsTariffsApiFp(this.configuration)
      .createTariffForChargingStation(ppid, tariffRowDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a PPID, delete all the tariffs for the charging station
   * @summary Delete all tariffs
   * @param {string} ppid Charging Station PPID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsTariffsApi
   */
  public deleteAllTariffForChargingStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsTariffsApiFp(this.configuration)
      .deleteAllTariffForChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a PPID and tariff ID, delete the tariff
   * @summary Delete a tariff
   * @param {string} ppid Charging Station PPID
   * @param {string} tariffId Tariff ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsTariffsApi
   */
  public deleteTariff(
    ppid: string,
    tariffId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsTariffsApiFp(this.configuration)
      .deleteTariff(ppid, tariffId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a PPID, return the tariffs with optional effective date filtering.
   * @summary Get Tariffs by Charging Station PPID
   * @param {string} ppid Charging Station PPID
   * @param {string} [effectiveFrom] Include tariffs effective from this date (inclusive).
   * @param {string} [effectiveTo] Include tariffs effective to this date (inclusive).
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsTariffsApi
   */
  public getTariffsByPpid(
    ppid: string,
    effectiveFrom?: string,
    effectiveTo?: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsTariffsApiFp(this.configuration)
      .getTariffsByPpid(ppid, effectiveFrom, effectiveTo, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a PPID and tariff ID, replace the tariff details with the provided details
   * @summary Update a tariff
   * @param {string} ppid Charging Station PPID
   * @param {string} tariffId Tariff ID
   * @param {TariffRowDto} tariffRowDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsTariffsApi
   */
  public updateTariff(
    ppid: string,
    tariffId: string,
    tariffRowDto: TariffRowDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsTariffsApiFp(this.configuration)
      .updateTariff(ppid, tariffId, tariffRowDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthApi - axios parameter creator
 * @export
 */
export const HealthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthApi - functional programming interface
 * @export
 */
export const HealthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = HealthApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthApi - factory interface
 * @export
 */
export const HealthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthApi - object-oriented interface
 * @export
 * @class HealthApi
 * @extends {BaseAPI}
 */
export class HealthApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SuppliersApi - axios parameter creator
 * @export
 */
export const SuppliersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get a list of suppliers
     * @summary Get suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSuppliers: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/suppliers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SuppliersApi - functional programming interface
 * @export
 */
export const SuppliersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SuppliersApiAxiosParamCreator(configuration);
  return {
    /**
     * Get a list of suppliers
     * @summary Get suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getSuppliers(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<SupplierDto>>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getSuppliers(
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SuppliersApi.getSuppliers']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SuppliersApi - factory interface
 * @export
 */
export const SuppliersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SuppliersApiFp(configuration);
  return {
    /**
     * Get a list of suppliers
     * @summary Get suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSuppliers(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<SupplierDto>> {
      return localVarFp
        .getSuppliers(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SuppliersApi - object-oriented interface
 * @export
 * @class SuppliersApi
 * @extends {BaseAPI}
 */
export class SuppliersApi extends BaseAPI {
  /**
   * Get a list of suppliers
   * @summary Get suppliers
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SuppliersApi
   */
  public getSuppliers(options?: RawAxiosRequestConfig) {
    return SuppliersApiFp(this.configuration)
      .getSuppliers(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
