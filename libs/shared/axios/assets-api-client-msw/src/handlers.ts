/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/charging-stations`, async () => {
    const resultArray = [
      [await getSearchChargingStations200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/charging-stations`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/charging-stations/ownership`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/charging-stations/:ppid`, async () => {
    const resultArray = [
      [await getGetChargingStation200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/charging-stations/:ppid/decommission`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/pcbs/decommission`, async () => {
    const resultArray = [
      [await getBatchDecommissionPcbs200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/pcbs/:serialNumber`, async () => {
    const resultArray = [[await getGetPcb200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/pcbs/:serialNumber/decommission`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/locations/:locationId`, async () => {
    const resultArray = [[await getGetLocation200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/locations`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/addresses`, async () => {
    const resultArray = [
      [await getCreateAddress201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/tags/charging-stations`, async () => {
    const resultArray = [
      [await getGetChargingStationsForTags200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/tags/charging-stations/:ppid`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/tags/charging-stations/:ppid`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getSearchChargingStations200Response() {
  return {
    chargingStations: [...new Array(5).keys()]
      .map((_) => ({
        [faker.lorem.word()]: {
          ppid: 'PSL-12345',
          rfid: {
            serialNumber: '30303030',
            macAddress: '0cb2b703c778',
          },
          evses: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => ({
            evseId: 1,
            ocpiEvseId: 'GB*POD*E*PG12345E1',
            connectors: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => ({
              id: faker.number.int({ min: 1 }),
              door: faker.helpers.arrayElement([
                faker.helpers.arrayElement(['A', 'B', 'C']),
                faker.number.int(),
              ]),
              modelPowerRating: 3.6,
            })),
            serialNumber: '30303030',
            macAddress: '0cb2b703c778',
            pcb: {
              revision: 'PP-A-220270-1',
              architecture: '5.0',
              serialNumber: '30303030',
              macAddress: '0cb2b703c778',
              generation: '5',
            },
          })),
          ownership: faker.helpers.arrayElement(['POD_POINT', 'CUSTOMER']),
          model: {
            sku: 'T7-S-07-AMC-BLK',
            range: {
              name: 'solo',
            },
            vendor: {
              name: 'Pod Point',
            },
            regions: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => ({
              name: 'UK',
            })),
          },
          router: {
            serialNumber: '1102538429',
            macAddress: '001E4223CF13',
            simNumber: '123456789123000043',
            model: 'RUT241',
          },
          location: {
            id: 'dcc774eb-5bb3-42f3-a4f3-3eb00b20bdb3',
            isPublic: faker.datatype.boolean(),
            type: faker.helpers.arrayElement(['DOMESTIC', 'COMMERCIAL']),
          },
          tags: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => ({
            id: faker.string.uuid(),
            key: faker.lorem.words(),
            value: faker.lorem.words(),
          })),
          provisioningInfo: {
            provisionedAt: '2023-06-01T00:00:00.000Z',
          },
        },
      }))
      .reduce((acc, next) => Object.assign(acc, next), {}),
  };
}

export function getGetChargingStation200Response() {
  return {
    ppid: 'PSL-12345',
    rfid: {
      serialNumber: '30303030',
      macAddress: '0cb2b703c778',
    },
    evses: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      evseId: 1,
      ocpiEvseId: 'GB*POD*E*PG12345E1',
      connectors: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int({ min: 1 }),
        door: faker.helpers.arrayElement([
          faker.helpers.arrayElement(['A', 'B', 'C']),
          faker.number.int(),
        ]),
        modelPowerRating: 3.6,
      })),
      serialNumber: '30303030',
      macAddress: '0cb2b703c778',
      pcb: {
        revision: 'PP-A-220270-1',
        architecture: '5.0',
        serialNumber: '30303030',
        macAddress: '0cb2b703c778',
        generation: '5',
      },
    })),
    ownership: faker.helpers.arrayElement(['POD_POINT', 'CUSTOMER']),
    model: {
      sku: 'T7-S-07-AMC-BLK',
      range: {
        name: 'solo',
      },
      vendor: {
        name: 'Pod Point',
      },
      regions: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        name: 'UK',
      })),
    },
    router: {
      serialNumber: '1102538429',
      macAddress: '001E4223CF13',
      simNumber: '123456789123000043',
      model: 'RUT241',
    },
    location: {
      id: 'dcc774eb-5bb3-42f3-a4f3-3eb00b20bdb3',
      isPublic: faker.datatype.boolean(),
      type: faker.helpers.arrayElement(['DOMESTIC', 'COMMERCIAL']),
    },
    tags: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      key: faker.lorem.words(),
      value: faker.lorem.words(),
    })),
    provisioningInfo: {
      provisionedAt: '2023-06-01T00:00:00.000Z',
    },
  };
}

export function getBatchDecommissionPcbs200Response() {
  return {
    decommissioned: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => '30303030'),
    failed: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      serialNumber: '30303030',
      reason: 'Pcb can not be decommissioned',
    })),
  };
}

export function getGetPcb200Response() {
  return null;
}

export function getGetLocation200Response() {
  return {
    id: 'dcc774eb-5bb3-42f3-a4f3-3eb00b20bdb3',
    isPublic: faker.datatype.boolean(),
    type: faker.helpers.arrayElement(['DOMESTIC', 'COMMERCIAL']),
    confirmRequired: faker.datatype.boolean(),
    description: faker.lorem.words(),
    contactlessEnabled: faker.datatype.boolean(),
    midMeterEnabled: faker.datatype.boolean(),
    isEvZone: faker.datatype.boolean(),
    mpan: faker.lorem.words(),
    localTimezone: 'Europe/London',
    latitude: 1,
    longitude: 1,
    chargingStation: 'PSL-12345',
    createdAt: '2023-06-01T00:00:00.000Z',
    updatedAt: '2023-08-01T00:00:00.000Z',
    unitLinkedAt: '2023-07-01T00:00:00.000Z',
  };
}

export function getCreateAddress201Response() {
  return {
    id: 5,
  };
}

export function getGetChargingStationsForTags200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    chargingStations: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ppid: 'PSL-12345',
    })),
  }));
}
