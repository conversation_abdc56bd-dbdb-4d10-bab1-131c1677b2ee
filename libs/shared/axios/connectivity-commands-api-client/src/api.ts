/* tslint:disable */
/* eslint-disable */
/**
 * Connectivity Service Commands API
 * An API for sending commands to a currently connected charging station. Only works for devices connected via a websocket ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
 *
 * The version of the OpenAPI document: 1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface HandlersAuthorizationData
 */
export interface HandlersAuthorizationData {
  /**
   *
   * @type {string}
   * @memberof HandlersAuthorizationData
   */
  idTag: string;
  /**
   *
   * @type {HandlersIdTagInfo}
   * @memberof HandlersAuthorizationData
   */
  idTagInfo?: HandlersIdTagInfo;
}
/**
 *
 * @export
 * @interface HandlersCertificateSignedRequestBody
 */
export interface HandlersCertificateSignedRequestBody {
  /**
   *
   * @type {string}
   * @memberof HandlersCertificateSignedRequestBody
   */
  certificateChain: string;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersCertificateSignedRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersCertificateSignedRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersCertificateSignedRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersCertificateSignedRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersCertificateSignedRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersChargingProfile
 */
export interface HandlersChargingProfile {
  /**
   * Unique identifier for this profile.
   * @type {number}
   * @memberof HandlersChargingProfile
   */
  chargingProfileId: number;
  /**
   * Indicates the kind of schedule.
   * @type {string}
   * @memberof HandlersChargingProfile
   */
  chargingProfileKind: HandlersChargingProfileChargingProfileKindEnum;
  /**
   * Defines the purpose of the schedule transferred by this message.
   * @type {string}
   * @memberof HandlersChargingProfile
   */
  chargingProfilePurpose: HandlersChargingProfileChargingProfilePurposeEnum;
  /**
   * Contains limits for the available power or current over time.
   * @type {HandlersChargingSchedule}
   * @memberof HandlersChargingProfile
   */
  chargingSchedule: HandlersChargingSchedule;
  /**
   * Indicates the start point of a recurrence.
   * @type {string}
   * @memberof HandlersChargingProfile
   */
  recurrencyKind?: HandlersChargingProfileRecurrencyKindEnum;
  /**
   * Value determining level in hierarchy stack of profiles. Higher values have precedence over lower values. Lowest level is 0.
   * @type {number}
   * @memberof HandlersChargingProfile
   */
  stackLevel: number;
  /**
   * Only valid if ChargingProfilePurpose is set to TxProfile, the transactionId MAY be used to match the profile to a specific transaction.
   * @type {number}
   * @memberof HandlersChargingProfile
   */
  transactionId?: number;
  /**
   * Point in time at which the profile starts to be valid. If absent, the profile is valid as soon as it is received by the Charge Point.
   * @type {string}
   * @memberof HandlersChargingProfile
   */
  validFrom?: string;
  /**
   * Point in time at which the profile stops to be valid. If absent, the profile is valid until it is replaced by another profile.
   * @type {string}
   * @memberof HandlersChargingProfile
   */
  validTo?: string;
}

export const HandlersChargingProfileChargingProfileKindEnum = {
  Absolute: 'Absolute',
  Recurring: 'Recurring',
  Relative: 'Relative',
} as const;

export type HandlersChargingProfileChargingProfileKindEnum =
  (typeof HandlersChargingProfileChargingProfileKindEnum)[keyof typeof HandlersChargingProfileChargingProfileKindEnum];
export const HandlersChargingProfileChargingProfilePurposeEnum = {
  ChargePointMaxProfile: 'ChargePointMaxProfile',
  TxDefaultProfile: 'TxDefaultProfile',
  TxProfile: 'TxProfile',
} as const;

export type HandlersChargingProfileChargingProfilePurposeEnum =
  (typeof HandlersChargingProfileChargingProfilePurposeEnum)[keyof typeof HandlersChargingProfileChargingProfilePurposeEnum];
export const HandlersChargingProfileRecurrencyKindEnum = {
  Daily: 'Daily',
  Weekly: 'Weekly',
} as const;

export type HandlersChargingProfileRecurrencyKindEnum =
  (typeof HandlersChargingProfileRecurrencyKindEnum)[keyof typeof HandlersChargingProfileRecurrencyKindEnum];

/**
 *
 * @export
 * @interface HandlersChargingSchedule
 */
export interface HandlersChargingSchedule {
  /**
   * The unit of measure Limit is expressed in.
   * @type {string}
   * @memberof HandlersChargingSchedule
   */
  chargingRateUnit: HandlersChargingScheduleChargingRateUnitEnum;
  /**
   * List of ChargingSchedulePeriod elements defining maximum power or current usage over time. The startSchedule of the first ChargingSchedulePeriod SHALL always be 0.
   * @type {Array<HandlersChargingSchedulePeriod>}
   * @memberof HandlersChargingSchedule
   */
  chargingSchedulePeriod: Array<HandlersChargingSchedulePeriod>;
  /**
   * Duration of the charging schedule in seconds. If the duration is left empty, the last period will continue indefinitely or until end of the transaction in case startSchedule is absent.
   * @type {number}
   * @memberof HandlersChargingSchedule
   */
  duration?: number;
  /**
   * Minimum charging rate supported by the electric vehicle. The unit of measure is defined by the chargingRateUnit. This parameter is intended to be used by a local smart charging algorithm to optimize the power allocation for in the case a charging process is inefficient at lower charging rates. Accepts at most one digit fraction (e.g. 8.1)
   * @type {number}
   * @memberof HandlersChargingSchedule
   */
  minChargingRate?: number;
  /**
   * Starting point of an absolute schedule. If absent the schedule will be relative to start of charging.
   * @type {string}
   * @memberof HandlersChargingSchedule
   */
  startSchedule?: string;
}

export const HandlersChargingScheduleChargingRateUnitEnum = {
  A: 'A',
  W: 'W',
} as const;

export type HandlersChargingScheduleChargingRateUnitEnum =
  (typeof HandlersChargingScheduleChargingRateUnitEnum)[keyof typeof HandlersChargingScheduleChargingRateUnitEnum];

/**
 *
 * @export
 * @interface HandlersChargingSchedulePeriod
 */
export interface HandlersChargingSchedulePeriod {
  /**
   * Charging rate limit during the schedule period, in the applicable chargingRateUnit, for example in Amperes or Watts. Accepts at most one digit fraction (e.g. 8.1).
   * @type {number}
   * @memberof HandlersChargingSchedulePeriod
   */
  limit: number;
  /**
   * The number of phases that can be used for charging. If a number of phases is needed, numberPhases=3 will be assumed unless another number is given.
   * @type {number}
   * @memberof HandlersChargingSchedulePeriod
   */
  numberPhases?: number;
  /**
   * Start of the period, in seconds from the start of schedule. The value of StartPeriod also defines the stop time of the previous period.
   * @type {number}
   * @memberof HandlersChargingSchedulePeriod
   */
  startPeriod: number;
}
/**
 *
 * @export
 * @interface HandlersClearChargingProfileRequestBody
 */
export interface HandlersClearChargingProfileRequestBody {
  /**
   * The ID of the charging profile to clear
   * @type {number}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  chargingProfileId?: number;
  /**
   * Specifies to purpose of the charging profiles that will be cleared, if they meet the other criteria in the request
   * @type {string}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  chargingProfilePurpose?: HandlersClearChargingProfileRequestBodyChargingProfilePurposeEnum;
  /**
   * Specifies the ID of the connector for which to clear charging profiles. A connectorId of zero (0) specifies the charging profile for the overall Charge Point. Absence of this parameter means the clearing applies to all charging profiles that match the other criteria in the request.
   * @type {number}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  connectorId?: number;
  /**
   * Specifies the stackLevel for which charging profiles will be cleared, if they meet the other criteria in the request
   * @type {number}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  stackLevel?: number;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersClearChargingProfileRequestBody
   */
  timeout?: number;
}

export const HandlersClearChargingProfileRequestBodyChargingProfilePurposeEnum =
  {
    ChargePointMaxProfile: 'ChargePointMaxProfile',
    TxDefaultProfile: 'TxDefaultProfile',
    TxProfile: 'TxProfile',
  } as const;

export type HandlersClearChargingProfileRequestBodyChargingProfilePurposeEnum =
  (typeof HandlersClearChargingProfileRequestBodyChargingProfilePurposeEnum)[keyof typeof HandlersClearChargingProfileRequestBodyChargingProfilePurposeEnum];

/**
 *
 * @export
 * @interface HandlersCommandResponse
 */
export interface HandlersCommandResponse {
  /**
   *
   * @type {string}
   * @memberof HandlersCommandResponse
   */
  commandId?: string;
  /**
   *
   * @type {string}
   * @memberof HandlersCommandResponse
   */
  commandType?: string;
  /**
   *
   * @type {string}
   * @memberof HandlersCommandResponse
   */
  messageId?: string;
  /**
   * This field will only be populated if the request was a successful synchronous command.
   * @type {HandlersSynchronousCommandResponse}
   * @memberof HandlersCommandResponse
   */
  response?: HandlersSynchronousCommandResponse;
  /**
   * The overall status of the sent command.
   * @type {string}
   * @memberof HandlersCommandResponse
   */
  status?: HandlersCommandResponseStatusEnum;
  /**
   *
   * @type {string}
   * @memberof HandlersCommandResponse
   */
  statusMessage?: string;
}

export const HandlersCommandResponseStatusEnum = {
  Success: 'Success',
  Accepted: 'Accepted',
  TimedOut: 'Timed Out',
} as const;

export type HandlersCommandResponseStatusEnum =
  (typeof HandlersCommandResponseStatusEnum)[keyof typeof HandlersCommandResponseStatusEnum];

/**
 *
 * @export
 * @interface HandlersDebugRequestBody
 */
export interface HandlersDebugRequestBody {
  /**
   * The OCPP Action to send
   * @type {string}
   * @memberof HandlersDebugRequestBody
   */
  action: string;
  /**
   * The OCPP Payload to send
   * @type {{ [key: string]: object; }}
   * @memberof HandlersDebugRequestBody
   */
  payload: { [key: string]: object };
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersDebugRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersDebugRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersDebugRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersDebugRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersDebugRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersGetConfigVariablesRequestBody
 */
export interface HandlersGetConfigVariablesRequestBody {
  /**
   * Config Variables to request. Each string item must not exceed 50 characters.
   * @type {Array<string>}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  keys?: Array<string>;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersGetConfigVariablesRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersGetLogsRequestBody
 */
export interface HandlersGetLogsRequestBody {
  /**
   * The date and time of the oldest logging information to include in the log
   * @type {string}
   * @memberof HandlersGetLogsRequestBody
   */
  fromTimestamp?: string;
  /**
   * The type of log file that the Charging Station should send
   * @type {string}
   * @memberof HandlersGetLogsRequestBody
   */
  logType: HandlersGetLogsRequestBodyLogTypeEnum;
  /**
   * The ID of this request
   * @type {number}
   * @memberof HandlersGetLogsRequestBody
   */
  requestId: number;
  /**
   * How many times the Charge Point must try to upload the log before giving up. If this field is not present, it is left to Charging Station to decide how many times it wants to retry
   * @type {number}
   * @memberof HandlersGetLogsRequestBody
   */
  retries?: number;
  /**
   * The interval in seconds after which a retry may be attempted. If this field is not present, it is left to Charging Station to decide how long to wait between attempts
   * @type {number}
   * @memberof HandlersGetLogsRequestBody
   */
  retryInterval?: number;
  /**
   * The date and time of the latest logging information to include in the log
   * @type {string}
   * @memberof HandlersGetLogsRequestBody
   */
  toTimestamp?: string;
  /**
   * The URL of the location at the remote system where the log should be stored
   * @type {string}
   * @memberof HandlersGetLogsRequestBody
   */
  uploadURL: string;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersGetLogsRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersGetLogsRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersGetLogsRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersGetLogsRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersGetLogsRequestBody
   */
  timeout?: number;
}

export const HandlersGetLogsRequestBodyLogTypeEnum = {
  DiagnosticsLog: 'DiagnosticsLog',
  SecurityLog: 'SecurityLog',
} as const;

export type HandlersGetLogsRequestBodyLogTypeEnum =
  (typeof HandlersGetLogsRequestBodyLogTypeEnum)[keyof typeof HandlersGetLogsRequestBodyLogTypeEnum];

/**
 *
 * @export
 * @interface HandlersGetSchedulesRequestBody
 */
export interface HandlersGetSchedulesRequestBody {
  /**
   * The ID of the Connector for which the schedule is requested. When ConnectorId=0, the Charging Station will calculate the expected consumption for the grid connection
   * @type {number}
   * @memberof HandlersGetSchedulesRequestBody
   */
  connectorId: number;
  /**
   * Length of requested schedule in seconds
   * @type {number}
   * @memberof HandlersGetSchedulesRequestBody
   */
  duration: number;
  /**
   * Unit to force a power or current profile
   * @type {string}
   * @memberof HandlersGetSchedulesRequestBody
   */
  unit?: HandlersGetSchedulesRequestBodyUnitEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersGetSchedulesRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersGetSchedulesRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersGetSchedulesRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersGetSchedulesRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersGetSchedulesRequestBody
   */
  timeout?: number;
}

export const HandlersGetSchedulesRequestBodyUnitEnum = {
  A: 'A',
  W: 'W',
} as const;

export type HandlersGetSchedulesRequestBodyUnitEnum =
  (typeof HandlersGetSchedulesRequestBodyUnitEnum)[keyof typeof HandlersGetSchedulesRequestBodyUnitEnum];

/**
 *
 * @export
 * @interface HandlersIdTagInfo
 */
export interface HandlersIdTagInfo {
  /**
   *
   * @type {string}
   * @memberof HandlersIdTagInfo
   */
  expiryDate?: string;
  /**
   *
   * @type {string}
   * @memberof HandlersIdTagInfo
   */
  parentIdTag?: string;
  /**
   *
   * @type {string}
   * @memberof HandlersIdTagInfo
   */
  status: HandlersIdTagInfoStatusEnum;
}

export const HandlersIdTagInfoStatusEnum = {
  Accepted: 'Accepted',
  Blocked: 'Blocked',
  Expired: 'Expired',
  Invalid: 'Invalid',
  ConcurrentTx: 'ConcurrentTx',
} as const;

export type HandlersIdTagInfoStatusEnum =
  (typeof HandlersIdTagInfoStatusEnum)[keyof typeof HandlersIdTagInfoStatusEnum];

/**
 *
 * @export
 * @interface HandlersLimitInfo
 */
export interface HandlersLimitInfo {
  /**
   *
   * @type {number}
   * @memberof HandlersLimitInfo
   */
  energy?: number;
  /**
   *
   * @type {number}
   * @memberof HandlersLimitInfo
   */
  time?: number;
}
/**
 *
 * @export
 * @interface HandlersResetRequestBody
 */
export interface HandlersResetRequestBody {
  /**
   * The type of reset to initiate
   * @type {string}
   * @memberof HandlersResetRequestBody
   */
  type: HandlersResetRequestBodyTypeEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersResetRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersResetRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersResetRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersResetRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersResetRequestBody
   */
  timeout?: number;
}

export const HandlersResetRequestBodyTypeEnum = {
  Soft: 'Soft',
  Hard: 'Hard',
} as const;

export type HandlersResetRequestBodyTypeEnum =
  (typeof HandlersResetRequestBodyTypeEnum)[keyof typeof HandlersResetRequestBodyTypeEnum];

/**
 *
 * @export
 * @interface HandlersSendLocalListRequestBody
 */
export interface HandlersSendLocalListRequestBody {
  /**
   * In case of a full update this is the version number of the full list. In case of a differential update it is the version number of the list after the update has been applied
   * @type {number}
   * @memberof HandlersSendLocalListRequestBody
   */
  listVersion: number;
  /**
   * In case of a full update this contains the list of values that form the new local authorization list. In case of a differential update it contains the changes to be applied to the local authorization list in the Charging Station
   * @type {Array<HandlersAuthorizationData>}
   * @memberof HandlersSendLocalListRequestBody
   */
  localAuthorizationList?: Array<HandlersAuthorizationData>;
  /**
   * The type of update (full or differential)
   * @type {string}
   * @memberof HandlersSendLocalListRequestBody
   */
  updateType: HandlersSendLocalListRequestBodyUpdateTypeEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersSendLocalListRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersSendLocalListRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersSendLocalListRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersSendLocalListRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersSendLocalListRequestBody
   */
  timeout?: number;
}

export const HandlersSendLocalListRequestBodyUpdateTypeEnum = {
  Differential: 'Differential',
  Full: 'Full',
} as const;

export type HandlersSendLocalListRequestBodyUpdateTypeEnum =
  (typeof HandlersSendLocalListRequestBodyUpdateTypeEnum)[keyof typeof HandlersSendLocalListRequestBodyUpdateTypeEnum];

/**
 *
 * @export
 * @interface HandlersSetAvailabilityRequestBody
 */
export interface HandlersSetAvailabilityRequestBody {
  /**
   * The connector in which the availability status should be set
   * @type {number}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  connectorId: number;
  /**
   * The availability type to set
   * @type {string}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  type: HandlersSetAvailabilityRequestBodyTypeEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersSetAvailabilityRequestBody
   */
  timeout?: number;
}

export const HandlersSetAvailabilityRequestBodyTypeEnum = {
  Operative: 'Operative',
  Inoperative: 'Inoperative',
} as const;

export type HandlersSetAvailabilityRequestBodyTypeEnum =
  (typeof HandlersSetAvailabilityRequestBodyTypeEnum)[keyof typeof HandlersSetAvailabilityRequestBodyTypeEnum];

/**
 *
 * @export
 * @interface HandlersSetChargingProfileRequestBody
 */
export interface HandlersSetChargingProfileRequestBody {
  /**
   * The charging profile to be set on the Charging Station
   * @type {HandlersChargingProfile}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  chargingProfile: HandlersChargingProfile;
  /**
   * The connector to which the charging profile applies. If connectorId = 0, the message contains an overall limit for the Charging Station
   * @type {number}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  connectorId: number;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersSetChargingProfileRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersSetConfigVariablesRequestBody
 */
export interface HandlersSetConfigVariablesRequestBody {
  /**
   * Configuration variable name is case-insensitive
   * @type {string}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  key: string;
  /**
   * Configuration variable value is case-insensitive
   * @type {string}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  value: string;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersSetConfigVariablesRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersStartTransactionRequestBody
 */
export interface HandlersStartTransactionRequestBody {
  /**
   * ID of the transaction authoriser
   * @type {string}
   * @memberof HandlersStartTransactionRequestBody
   */
  authoriserId: string;
  /**
   * ID of the connector to begin a transaction on
   * @type {number}
   * @memberof HandlersStartTransactionRequestBody
   */
  connectorId: number;
  /**
   * Any limitations that should be enforced on the charging cycle
   * @type {HandlersLimitInfo}
   * @memberof HandlersStartTransactionRequestBody
   */
  limit?: HandlersLimitInfo;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersStartTransactionRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersStartTransactionRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersStartTransactionRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersStartTransactionRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersStartTransactionRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersStopTransactionRequestBody
 */
export interface HandlersStopTransactionRequestBody {
  /**
   * ID of the charge cycle to stop
   * @type {string}
   * @memberof HandlersStopTransactionRequestBody
   */
  chargeCycleId: string;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersStopTransactionRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersStopTransactionRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersStopTransactionRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersStopTransactionRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersStopTransactionRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersSynchronousCommandResponse
 */
export interface HandlersSynchronousCommandResponse {
  /**
   *
   * @type {string}
   * @memberof HandlersSynchronousCommandResponse
   */
  errorCode?: string;
  /**
   *
   * @type {string}
   * @memberof HandlersSynchronousCommandResponse
   */
  errorReason?: string;
  /**
   *
   * @type {object}
   * @memberof HandlersSynchronousCommandResponse
   */
  payload?: object;
  /**
   *
   * @type {string}
   * @memberof HandlersSynchronousCommandResponse
   */
  status?: string;
}
/**
 *
 * @export
 * @interface HandlersTriggerRequestBody
 */
export interface HandlersTriggerRequestBody {
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersTriggerRequestBody
   */
  evseId?: number;
  /**
   * OCPP Message to send
   * @type {string}
   * @memberof HandlersTriggerRequestBody
   */
  requestedMessage: HandlersTriggerRequestBodyRequestedMessageEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersTriggerRequestBody
   */
  clientRef: string;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersTriggerRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersTriggerRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersTriggerRequestBody
   */
  timeout?: number;
}

export const HandlersTriggerRequestBodyRequestedMessageEnum = {
  BootNotification: 'BootNotification',
  DiagnosticsStatusNotification: 'DiagnosticsStatusNotification',
  LogStatusNotification: 'LogStatusNotification',
  FirmwareStatusNotification: 'FirmwareStatusNotification',
  Heartbeat: 'Heartbeat',
  MeterValues: 'MeterValues',
  SignCertificate: 'SignCertificate',
  StatusNotification: 'StatusNotification',
} as const;

export type HandlersTriggerRequestBodyRequestedMessageEnum =
  (typeof HandlersTriggerRequestBodyRequestedMessageEnum)[keyof typeof HandlersTriggerRequestBodyRequestedMessageEnum];

/**
 *
 * @export
 * @interface HandlersUnlockConnectorRequestBody
 */
export interface HandlersUnlockConnectorRequestBody {
  /**
   * ID of the connector to unlock
   * @type {number}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  connectorId: number;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersUnlockConnectorRequestBody
   */
  timeout?: number;
}
/**
 *
 * @export
 * @interface HandlersUpdateFirmwareRequestBody
 */
export interface HandlersUpdateFirmwareRequestBody {
  /**
   * Certificate with which the firmware was signed. PEM encoded X.509 certificate. (When updateType is set to \'unsigned\', this parameter is optional and will be ignored.)
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  certificate?: string;
  /**
   * URI defining the origin of the firmware
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  firmwareURL: string;
  /**
   * Date and time at which the firmware shall be installed (When updateType is set to \'unsigned\', this parameter is optional and will be ignored.)
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  installAt?: string;
  /**
   * ID that will be used by the charging station when sending status updates on the firmware update process. (When updateType is set to \'unsigned\', this parameter is optional and will be ignored.)
   * @type {number}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  requestId?: number;
  /**
   * This specifies how many times Charging Station must try to download the firmware before giving up. If this field is not present, it is left to Charging Station to decide how many times it wants to retry
   * @type {number}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  retries?: number;
  /**
   * Date and time at which the firmware shall be retrieved
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  retrieveAt: string;
  /**
   * The interval (in seconds) after which a retry may be attempted. If this field is not present, it is left to Charging Station to decide how long to wait between attempts
   * @type {number}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  retryInterval?: number;
  /**
   * Base64 encoded firmware signature. (When updateType is set to \'unsigned\', this parameter is optional and will be ignored)
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  signature?: string;
  /**
   * Whether the firmware update is to be a signed (whitepaper) or unsigned (regular OCPP1.6) request
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  updateType: HandlersUpdateFirmwareRequestBodyUpdateTypeEnum;
  /**
   * Reference of the client invoking the API endpoint
   * @type {string}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  clientRef: string;
  /**
   * The EVSE to respond to the command. Required when interacting with arch5 charging stations
   * @type {number}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  evseId?: number;
  /**
   * If true, send this command via a queue to be sent when the Charging Station/PCB is next online
   * @type {boolean}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  queued?: boolean;
  /**
   * If true, send the command and synchronously wait for the response
   * @type {boolean}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  sync?: boolean;
  /**
   * Requires sync=true. The number of seconds to wait for a response
   * @type {number}
   * @memberof HandlersUpdateFirmwareRequestBody
   */
  timeout?: number;
}

export const HandlersUpdateFirmwareRequestBodyUpdateTypeEnum = {
  Signed: 'Signed',
  Unsigned: 'Unsigned',
} as const;

export type HandlersUpdateFirmwareRequestBodyUpdateTypeEnum =
  (typeof HandlersUpdateFirmwareRequestBodyUpdateTypeEnum)[keyof typeof HandlersUpdateFirmwareRequestBodyUpdateTypeEnum];

/**
 *
 * @export
 * @interface ServerApiErrorResponse
 */
export interface ServerApiErrorResponse {
  /**
   * Verbose error message
   * @type {string}
   * @memberof ServerApiErrorResponse
   */
  reason?: string;
  /**
   * The overall status of the request
   * @type {string}
   * @memberof ServerApiErrorResponse
   */
  status?: string;
}

/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Command to change the availability of a charging station or its connector
     * @summary Command to change the availability of a charging station or its connector
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetAvailabilityRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidAvailabilitySetPost: async (
      ppid: string,
      req: HandlersSetAvailabilityRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidAvailabilitySetPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidAvailabilitySetPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/availability/set`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a ClearChargingProfile message to the charging station
     * @summary Command to send a ClearChargingProfile message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersClearChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidChargingProfileClearPost: async (
      ppid: string,
      req: HandlersClearChargingProfileRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidChargingProfileClearPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidChargingProfileClearPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/charging-profile/clear`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to set charging profiles for a charger
     * @summary Command to set charging profiles for a charger
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidChargingProfileSetPost: async (
      ppid: string,
      req: HandlersSetChargingProfileRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidChargingProfileSetPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidChargingProfileSetPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/charging-profile/set`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a GetConfiguration message to the charging station
     * @summary Command to send a GetConfiguration message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidConfigVariablesGetPost: async (
      ppid: string,
      req: HandlersGetConfigVariablesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidConfigVariablesGetPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidConfigVariablesGetPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/config/variables/get`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to set configuration variables on the charging station
     * @summary Command to set configuration variables on the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidConfigVariablesSetPost: async (
      ppid: string,
      req: HandlersSetConfigVariablesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidConfigVariablesSetPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidConfigVariablesSetPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/config/variables/set`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send an action and payload to the charging station
     * @summary Command to send an action and payload to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersDebugRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidDebugPost: async (
      ppid: string,
      req: HandlersDebugRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('commandsChargingStationsPpidDebugPost', 'ppid', ppid);
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsChargingStationsPpidDebugPost', 'req', req);
      const localVarPath = `/commands/charging-stations/{ppid}/debug`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @summary Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersUpdateFirmwareRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidFirmwareUpdatePost: async (
      ppid: string,
      req: HandlersUpdateFirmwareRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidFirmwareUpdatePost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidFirmwareUpdatePost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/firmware/update`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @summary Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @param {string} ppid Charging Station ID
     * @param {HandlersSendLocalListRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidLocalListSendPost: async (
      ppid: string,
      req: HandlersSendLocalListRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidLocalListSendPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidLocalListSendPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/local-list/send`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a GetLog message to the charging station
     * @summary Command to send a GetLog message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetLogsRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidLogsGetPost: async (
      ppid: string,
      req: HandlersGetLogsRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidLogsGetPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsChargingStationsPpidLogsGetPost', 'req', req);
      const localVarPath =
        `/commands/charging-stations/{ppid}/logs/get`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a reset message to the charging station
     * @summary Command to send a reset message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidResetPost: async (
      ppid: string,
      req: HandlersResetRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('commandsChargingStationsPpidResetPost', 'ppid', ppid);
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsChargingStationsPpidResetPost', 'req', req);
      const localVarPath = `/commands/charging-stations/{ppid}/reset`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @summary Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetSchedulesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidSchedulesPost: async (
      ppid: string,
      req: HandlersGetSchedulesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidSchedulesPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidSchedulesPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/schedules`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a RemoteStartTransaction message to the charging station
     * @summary Command to send a RemoteStartTransaction message to the charging station
     * @param {string} ppid Charge Station PPID.
     * @param {HandlersStartTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTransactionStartPost: async (
      ppid: string,
      req: HandlersStartTransactionRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidTransactionStartPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidTransactionStartPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/transaction/start`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a RemoteStopTransaction message to the charging station
     * @summary Command to send a RemoteStopTransaction message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersStopTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTransactionStopPost: async (
      ppid: string,
      req: HandlersStopTransactionRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidTransactionStopPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidTransactionStopPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/transaction/stop`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTriggerPost: async (
      ppid: string,
      req: HandlersTriggerRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidTriggerPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsChargingStationsPpidTriggerPost', 'req', req);
      const localVarPath = `/commands/charging-stations/{ppid}/trigger`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send an UnlockConnector message to the charging station
     * @summary Command to send an UnlockConnector message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersUnlockConnectorRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidUnlockConnectorPost: async (
      ppid: string,
      req: HandlersUnlockConnectorRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidUnlockConnectorPost',
        'ppid',
        ppid
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsChargingStationsPpidUnlockConnectorPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/charging-stations/{ppid}/unlock-connector`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send back the signed certificate back to the PCB
     * @summary Command to send back the signed certificate back to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersCertificateSignedRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberCertificateSignedPost: async (
      pcbSerialNumber: string,
      req: HandlersCertificateSignedRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSerialNumber' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberCertificateSignedPost',
        'pcbSerialNumber',
        pcbSerialNumber
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberCertificateSignedPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/pcbs/:pcbSerialNumber/certificate-signed`.replace(
          `{${'pcbSerialNumber'}}`,
          encodeURIComponent(String(pcbSerialNumber))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a GetConfiguration message to the PCB
     * @summary Command to send a GetConfiguration message to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {Array<string>} [keys] Config Variables to request. Each string item must not exceed 50 characters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberConfigVariablesGetPost: async (
      pcbSerialNumber: string,
      keys?: Array<string>,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSerialNumber' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberConfigVariablesGetPost',
        'pcbSerialNumber',
        pcbSerialNumber
      );
      const localVarPath =
        `/commands/pcbs/{pcbSerialNumber}/config/variables/get`.replace(
          `{${'pcbSerialNumber'}}`,
          encodeURIComponent(String(pcbSerialNumber))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        keys,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to set configuration variables on the PCB
     * @summary Command to set configuration variables on the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberConfigVariablesSetPost: async (
      pcbSerialNumber: string,
      req: HandlersSetConfigVariablesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSerialNumber' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberConfigVariablesSetPost',
        'pcbSerialNumber',
        pcbSerialNumber
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberConfigVariablesSetPost',
        'req',
        req
      );
      const localVarPath =
        `/commands/pcbs/{pcbSerialNumber}/config/variables/set`.replace(
          `{${'pcbSerialNumber'}}`,
          encodeURIComponent(String(pcbSerialNumber))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a reset message to a PCB
     * @summary Command to send a reset message to a PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberResetPost: async (
      pcbSerialNumber: string,
      req: HandlersResetRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSerialNumber' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberResetPost',
        'pcbSerialNumber',
        pcbSerialNumber
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsPcbsPcbSerialNumberResetPost', 'req', req);
      const localVarPath = `/commands/pcbs/{pcbSerialNumber}/reset`.replace(
        `{${'pcbSerialNumber'}}`,
        encodeURIComponent(String(pcbSerialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberTriggerPost: async (
      pcbSerialNumber: string,
      req: HandlersTriggerRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSerialNumber' is not null or undefined
      assertParamExists(
        'commandsPcbsPcbSerialNumberTriggerPost',
        'pcbSerialNumber',
        pcbSerialNumber
      );
      // verify required parameter 'req' is not null or undefined
      assertParamExists('commandsPcbsPcbSerialNumberTriggerPost', 'req', req);
      const localVarPath = `/commands/pcbs/{pcbSerialNumber}/trigger`.replace(
        `{${'pcbSerialNumber'}}`,
        encodeURIComponent(String(pcbSerialNumber))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        req,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration);
  return {
    /**
     * Command to change the availability of a charging station or its connector
     * @summary Command to change the availability of a charging station or its connector
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetAvailabilityRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidAvailabilitySetPost(
      ppid: string,
      req: HandlersSetAvailabilityRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidAvailabilitySetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidAvailabilitySetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a ClearChargingProfile message to the charging station
     * @summary Command to send a ClearChargingProfile message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersClearChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidChargingProfileClearPost(
      ppid: string,
      req: HandlersClearChargingProfileRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidChargingProfileClearPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidChargingProfileClearPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to set charging profiles for a charger
     * @summary Command to set charging profiles for a charger
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidChargingProfileSetPost(
      ppid: string,
      req: HandlersSetChargingProfileRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidChargingProfileSetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidChargingProfileSetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a GetConfiguration message to the charging station
     * @summary Command to send a GetConfiguration message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidConfigVariablesGetPost(
      ppid: string,
      req: HandlersGetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidConfigVariablesGetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidConfigVariablesGetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to set configuration variables on the charging station
     * @summary Command to set configuration variables on the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidConfigVariablesSetPost(
      ppid: string,
      req: HandlersSetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidConfigVariablesSetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidConfigVariablesSetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send an action and payload to the charging station
     * @summary Command to send an action and payload to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersDebugRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidDebugPost(
      ppid: string,
      req: HandlersDebugRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidDebugPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidDebugPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @summary Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersUpdateFirmwareRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidFirmwareUpdatePost(
      ppid: string,
      req: HandlersUpdateFirmwareRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidFirmwareUpdatePost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidFirmwareUpdatePost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @summary Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @param {string} ppid Charging Station ID
     * @param {HandlersSendLocalListRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidLocalListSendPost(
      ppid: string,
      req: HandlersSendLocalListRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidLocalListSendPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidLocalListSendPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a GetLog message to the charging station
     * @summary Command to send a GetLog message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetLogsRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidLogsGetPost(
      ppid: string,
      req: HandlersGetLogsRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidLogsGetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidLogsGetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a reset message to the charging station
     * @summary Command to send a reset message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidResetPost(
      ppid: string,
      req: HandlersResetRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidResetPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidResetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @summary Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetSchedulesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidSchedulesPost(
      ppid: string,
      req: HandlersGetSchedulesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidSchedulesPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidSchedulesPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a RemoteStartTransaction message to the charging station
     * @summary Command to send a RemoteStartTransaction message to the charging station
     * @param {string} ppid Charge Station PPID.
     * @param {HandlersStartTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidTransactionStartPost(
      ppid: string,
      req: HandlersStartTransactionRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidTransactionStartPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidTransactionStartPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a RemoteStopTransaction message to the charging station
     * @summary Command to send a RemoteStopTransaction message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersStopTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidTransactionStopPost(
      ppid: string,
      req: HandlersStopTransactionRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidTransactionStopPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidTransactionStopPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidTriggerPost(
      ppid: string,
      req: HandlersTriggerRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidTriggerPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidTriggerPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send an UnlockConnector message to the charging station
     * @summary Command to send an UnlockConnector message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersUnlockConnectorRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsChargingStationsPpidUnlockConnectorPost(
      ppid: string,
      req: HandlersUnlockConnectorRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsChargingStationsPpidUnlockConnectorPost(
          ppid,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsChargingStationsPpidUnlockConnectorPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send back the signed certificate back to the PCB
     * @summary Command to send back the signed certificate back to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersCertificateSignedRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsPcbsPcbSerialNumberCertificateSignedPost(
      pcbSerialNumber: string,
      req: HandlersCertificateSignedRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsPcbsPcbSerialNumberCertificateSignedPost(
          pcbSerialNumber,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsPcbsPcbSerialNumberCertificateSignedPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a GetConfiguration message to the PCB
     * @summary Command to send a GetConfiguration message to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {Array<string>} [keys] Config Variables to request. Each string item must not exceed 50 characters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsPcbsPcbSerialNumberConfigVariablesGetPost(
      pcbSerialNumber: string,
      keys?: Array<string>,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsPcbsPcbSerialNumberConfigVariablesGetPost(
          pcbSerialNumber,
          keys,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsPcbsPcbSerialNumberConfigVariablesGetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to set configuration variables on the PCB
     * @summary Command to set configuration variables on the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsPcbsPcbSerialNumberConfigVariablesSetPost(
      pcbSerialNumber: string,
      req: HandlersSetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsPcbsPcbSerialNumberConfigVariablesSetPost(
          pcbSerialNumber,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsPcbsPcbSerialNumberConfigVariablesSetPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a reset message to a PCB
     * @summary Command to send a reset message to a PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsPcbsPcbSerialNumberResetPost(
      pcbSerialNumber: string,
      req: HandlersResetRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsPcbsPcbSerialNumberResetPost(
          pcbSerialNumber,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.commandsPcbsPcbSerialNumberResetPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async commandsPcbsPcbSerialNumberTriggerPost(
      pcbSerialNumber: string,
      req: HandlersTriggerRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HandlersCommandResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.commandsPcbsPcbSerialNumberTriggerPost(
          pcbSerialNumber,
          req,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DefaultApi.commandsPcbsPcbSerialNumberTriggerPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DefaultApiFp(configuration);
  return {
    /**
     * Command to change the availability of a charging station or its connector
     * @summary Command to change the availability of a charging station or its connector
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetAvailabilityRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidAvailabilitySetPost(
      ppid: string,
      req: HandlersSetAvailabilityRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidAvailabilitySetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a ClearChargingProfile message to the charging station
     * @summary Command to send a ClearChargingProfile message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersClearChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidChargingProfileClearPost(
      ppid: string,
      req: HandlersClearChargingProfileRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidChargingProfileClearPost(
          ppid,
          req,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to set charging profiles for a charger
     * @summary Command to set charging profiles for a charger
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetChargingProfileRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidChargingProfileSetPost(
      ppid: string,
      req: HandlersSetChargingProfileRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidChargingProfileSetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a GetConfiguration message to the charging station
     * @summary Command to send a GetConfiguration message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidConfigVariablesGetPost(
      ppid: string,
      req: HandlersGetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidConfigVariablesGetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to set configuration variables on the charging station
     * @summary Command to set configuration variables on the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidConfigVariablesSetPost(
      ppid: string,
      req: HandlersSetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidConfigVariablesSetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send an action and payload to the charging station
     * @summary Command to send an action and payload to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersDebugRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidDebugPost(
      ppid: string,
      req: HandlersDebugRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidDebugPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @summary Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersUpdateFirmwareRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidFirmwareUpdatePost(
      ppid: string,
      req: HandlersUpdateFirmwareRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidFirmwareUpdatePost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @summary Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
     * @param {string} ppid Charging Station ID
     * @param {HandlersSendLocalListRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidLocalListSendPost(
      ppid: string,
      req: HandlersSendLocalListRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidLocalListSendPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a GetLog message to the charging station
     * @summary Command to send a GetLog message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetLogsRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidLogsGetPost(
      ppid: string,
      req: HandlersGetLogsRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidLogsGetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a reset message to the charging station
     * @summary Command to send a reset message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidResetPost(
      ppid: string,
      req: HandlersResetRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidResetPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @summary Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
     * @param {string} ppid Charging Station ID
     * @param {HandlersGetSchedulesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidSchedulesPost(
      ppid: string,
      req: HandlersGetSchedulesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidSchedulesPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a RemoteStartTransaction message to the charging station
     * @summary Command to send a RemoteStartTransaction message to the charging station
     * @param {string} ppid Charge Station PPID.
     * @param {HandlersStartTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTransactionStartPost(
      ppid: string,
      req: HandlersStartTransactionRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidTransactionStartPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a RemoteStopTransaction message to the charging station
     * @summary Command to send a RemoteStopTransaction message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersStopTransactionRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTransactionStopPost(
      ppid: string,
      req: HandlersStopTransactionRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidTransactionStopPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} ppid Charging Station ID
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidTriggerPost(
      ppid: string,
      req: HandlersTriggerRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidTriggerPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send an UnlockConnector message to the charging station
     * @summary Command to send an UnlockConnector message to the charging station
     * @param {string} ppid Charge Station PPID
     * @param {HandlersUnlockConnectorRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsChargingStationsPpidUnlockConnectorPost(
      ppid: string,
      req: HandlersUnlockConnectorRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsChargingStationsPpidUnlockConnectorPost(ppid, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send back the signed certificate back to the PCB
     * @summary Command to send back the signed certificate back to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersCertificateSignedRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberCertificateSignedPost(
      pcbSerialNumber: string,
      req: HandlersCertificateSignedRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsPcbsPcbSerialNumberCertificateSignedPost(
          pcbSerialNumber,
          req,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a GetConfiguration message to the PCB
     * @summary Command to send a GetConfiguration message to the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {Array<string>} [keys] Config Variables to request. Each string item must not exceed 50 characters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberConfigVariablesGetPost(
      pcbSerialNumber: string,
      keys?: Array<string>,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsPcbsPcbSerialNumberConfigVariablesGetPost(
          pcbSerialNumber,
          keys,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to set configuration variables on the PCB
     * @summary Command to set configuration variables on the PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersSetConfigVariablesRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberConfigVariablesSetPost(
      pcbSerialNumber: string,
      req: HandlersSetConfigVariablesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsPcbsPcbSerialNumberConfigVariablesSetPost(
          pcbSerialNumber,
          req,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a reset message to a PCB
     * @summary Command to send a reset message to a PCB
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersResetRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberResetPost(
      pcbSerialNumber: string,
      req: HandlersResetRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsPcbsPcbSerialNumberResetPost(pcbSerialNumber, req, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Command to send a trigger message to the charging station
     * @summary Command to send a trigger message to the charging station
     * @param {string} pcbSerialNumber PCB Serial Number
     * @param {HandlersTriggerRequestBody} req Request body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    commandsPcbsPcbSerialNumberTriggerPost(
      pcbSerialNumber: string,
      req: HandlersTriggerRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HandlersCommandResponse> {
      return localVarFp
        .commandsPcbsPcbSerialNumberTriggerPost(pcbSerialNumber, req, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
  /**
   * Command to change the availability of a charging station or its connector
   * @summary Command to change the availability of a charging station or its connector
   * @param {string} ppid Charging Station ID
   * @param {HandlersSetAvailabilityRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidAvailabilitySetPost(
    ppid: string,
    req: HandlersSetAvailabilityRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidAvailabilitySetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a ClearChargingProfile message to the charging station
   * @summary Command to send a ClearChargingProfile message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersClearChargingProfileRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidChargingProfileClearPost(
    ppid: string,
    req: HandlersClearChargingProfileRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidChargingProfileClearPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to set charging profiles for a charger
   * @summary Command to set charging profiles for a charger
   * @param {string} ppid Charging Station ID
   * @param {HandlersSetChargingProfileRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidChargingProfileSetPost(
    ppid: string,
    req: HandlersSetChargingProfileRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidChargingProfileSetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a GetConfiguration message to the charging station
   * @summary Command to send a GetConfiguration message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersGetConfigVariablesRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidConfigVariablesGetPost(
    ppid: string,
    req: HandlersGetConfigVariablesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidConfigVariablesGetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to set configuration variables on the charging station
   * @summary Command to set configuration variables on the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersSetConfigVariablesRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidConfigVariablesSetPost(
    ppid: string,
    req: HandlersSetConfigVariablesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidConfigVariablesSetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send an action and payload to the charging station
   * @summary Command to send an action and payload to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersDebugRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidDebugPost(
    ppid: string,
    req: HandlersDebugRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidDebugPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
   * @summary Command to send an UpdateFirmware or SignedUpdateFirmware message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersUpdateFirmwareRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidFirmwareUpdatePost(
    ppid: string,
    req: HandlersUpdateFirmwareRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidFirmwareUpdatePost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
   * @summary Central System can send a Local Authorization List that a Charge Point can use for authorization of idTags. The list MAY be either a full list to replace the current list in the Charge Point, or it MAY be a differential list with updates to be applied to the current list in the Charge Point.
   * @param {string} ppid Charging Station ID
   * @param {HandlersSendLocalListRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidLocalListSendPost(
    ppid: string,
    req: HandlersSendLocalListRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidLocalListSendPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a GetLog message to the charging station
   * @summary Command to send a GetLog message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersGetLogsRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidLogsGetPost(
    ppid: string,
    req: HandlersGetLogsRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidLogsGetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a reset message to the charging station
   * @summary Command to send a reset message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersResetRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidResetPost(
    ppid: string,
    req: HandlersResetRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidResetPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
   * @summary Command to see the scheduled upcoming behaviour of the charger as a result of the charge profiles applied
   * @param {string} ppid Charging Station ID
   * @param {HandlersGetSchedulesRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidSchedulesPost(
    ppid: string,
    req: HandlersGetSchedulesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidSchedulesPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a RemoteStartTransaction message to the charging station
   * @summary Command to send a RemoteStartTransaction message to the charging station
   * @param {string} ppid Charge Station PPID.
   * @param {HandlersStartTransactionRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidTransactionStartPost(
    ppid: string,
    req: HandlersStartTransactionRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidTransactionStartPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a RemoteStopTransaction message to the charging station
   * @summary Command to send a RemoteStopTransaction message to the charging station
   * @param {string} ppid Charge Station PPID
   * @param {HandlersStopTransactionRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidTransactionStopPost(
    ppid: string,
    req: HandlersStopTransactionRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidTransactionStopPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a trigger message to the charging station
   * @summary Command to send a trigger message to the charging station
   * @param {string} ppid Charging Station ID
   * @param {HandlersTriggerRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidTriggerPost(
    ppid: string,
    req: HandlersTriggerRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidTriggerPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send an UnlockConnector message to the charging station
   * @summary Command to send an UnlockConnector message to the charging station
   * @param {string} ppid Charge Station PPID
   * @param {HandlersUnlockConnectorRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsChargingStationsPpidUnlockConnectorPost(
    ppid: string,
    req: HandlersUnlockConnectorRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsChargingStationsPpidUnlockConnectorPost(ppid, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send back the signed certificate back to the PCB
   * @summary Command to send back the signed certificate back to the PCB
   * @param {string} pcbSerialNumber PCB Serial Number
   * @param {HandlersCertificateSignedRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsPcbsPcbSerialNumberCertificateSignedPost(
    pcbSerialNumber: string,
    req: HandlersCertificateSignedRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsPcbsPcbSerialNumberCertificateSignedPost(
        pcbSerialNumber,
        req,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a GetConfiguration message to the PCB
   * @summary Command to send a GetConfiguration message to the PCB
   * @param {string} pcbSerialNumber PCB Serial Number
   * @param {Array<string>} [keys] Config Variables to request. Each string item must not exceed 50 characters.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsPcbsPcbSerialNumberConfigVariablesGetPost(
    pcbSerialNumber: string,
    keys?: Array<string>,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsPcbsPcbSerialNumberConfigVariablesGetPost(
        pcbSerialNumber,
        keys,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to set configuration variables on the PCB
   * @summary Command to set configuration variables on the PCB
   * @param {string} pcbSerialNumber PCB Serial Number
   * @param {HandlersSetConfigVariablesRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsPcbsPcbSerialNumberConfigVariablesSetPost(
    pcbSerialNumber: string,
    req: HandlersSetConfigVariablesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsPcbsPcbSerialNumberConfigVariablesSetPost(
        pcbSerialNumber,
        req,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a reset message to a PCB
   * @summary Command to send a reset message to a PCB
   * @param {string} pcbSerialNumber PCB Serial Number
   * @param {HandlersResetRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsPcbsPcbSerialNumberResetPost(
    pcbSerialNumber: string,
    req: HandlersResetRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsPcbsPcbSerialNumberResetPost(pcbSerialNumber, req, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Command to send a trigger message to the charging station
   * @summary Command to send a trigger message to the charging station
   * @param {string} pcbSerialNumber PCB Serial Number
   * @param {HandlersTriggerRequestBody} req Request body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public commandsPcbsPcbSerialNumberTriggerPost(
    pcbSerialNumber: string,
    req: HandlersTriggerRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DefaultApiFp(this.configuration)
      .commandsPcbsPcbSerialNumberTriggerPost(pcbSerialNumber, req, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
