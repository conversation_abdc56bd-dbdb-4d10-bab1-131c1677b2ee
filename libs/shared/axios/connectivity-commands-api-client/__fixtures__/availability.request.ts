import {
  HandlersSetAvailabilityRequestBody,
  HandlersSetAvailabilityRequestBodyTypeEnum,
} from '../src';

export const TEST_SET_IN_SERVICE_COMMAND_REQUEST: HandlersSetAvailabilityRequestBody =
  {
    connectorId: 1,
    clientRef: 'support-tool',
    type: HandlersSetAvailabilityRequestBodyTypeEnum.Operative,
    evseId: 1,
    sync: true,
  };

export const TEST_SET_OUT_OF_SERVICE_COMMAND_REQUEST: HandlersSetAvailabilityRequestBody =
  {
    ...TEST_SET_IN_SERVICE_COMMAND_REQUEST,
    type: HandlersSetAvailabilityRequestBodyTypeEnum.Inoperative,
  };
