import {
  HandlersResetRequestBody,
  HandlersResetRequestBodyTypeEnum,
} from '../src';

export const TEST_SOFT_RESET_COMMAND_REQUEST: HandlersResetRequestBody = {
  clientRef: 'support-tool',
  evseId: 1,
  sync: true,
  type: HandlersResetRequestBodyTypeEnum.Soft,
};

export const TEST_HARD_RESET_COMMAND_REQUEST: HandlersResetRequestBody = {
  ...TEST_SOFT_RESET_COMMAND_REQUEST,
  type: HandlersResetRequestBodyTypeEnum.Hard,
};
