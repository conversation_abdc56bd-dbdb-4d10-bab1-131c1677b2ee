openapi: 3.0.1
info:
  contact:
    email: <EMAIL>
    name: Connectivity Squad (Parrot)
    url: https://pod-point.com
  description: |-
    An API for sending commands to a currently connected charging station.
    Only works for devices connected via a websocket
    ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
  title: Connectivity Service Commands API
  version: '1.1'
servers:
  - url: //commands.connectivity-service.connectivity.prod/
paths:
  /commands/charging-stations/{ppid}/availability/set:
    post:
      description:
        Command to change the availability of a charging station or its
        connector
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.SetAvailabilityRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to change the availability of a charging station or its connector
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/charging-profile/clear:
    post:
      description:
        Command to send a ClearChargingProfile message to the charging
        station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.ClearChargingProfileRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a ClearChargingProfile message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/charging-profile/set:
    post:
      description: Command to set charging profiles for a charger
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.SetChargingProfileRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to set charging profiles for a charger
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/config/variables/get:
    post:
      description: Command to send a GetConfiguration message to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.GetConfigVariablesRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a GetConfiguration message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/config/variables/set:
    post:
      description: Command to set configuration variables on the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.SetConfigVariablesRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to set configuration variables on the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/debug:
    post:
      description: Command to send an action and payload to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.DebugRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send an action and payload to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/firmware/update:
    post:
      description:
        Command to send an UpdateFirmware or SignedUpdateFirmware message
        to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.UpdateFirmwareRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary:
        Command to send an UpdateFirmware or SignedUpdateFirmware message to
        the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/local-list/send:
    post:
      description:
        "Central System can send a Local Authorization List that a Charge\
        \ Point can use for authorization of idTags. The list MAY be either a full\
        \ list to replace the current list in the Charge Point, or it MAY be a differential\
        \ list with updates to be applied to the current list in the Charge Point."
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.SendLocalListRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary:
        "Central System can send a Local Authorization List that a Charge Point\
        \ can use for authorization of idTags. The list MAY be either a full list\
        \ to replace the current list in the Charge Point, or it MAY be a differential\
        \ list with updates to be applied to the current list in the Charge Point."
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/logs/get:
    post:
      description: Command to send a GetLog message to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.GetLogsRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a GetLog message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/reset:
    post:
      description: Command to send a reset message to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.ResetRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a reset message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/schedules:
    post:
      description:
        Command to see the scheduled upcoming behaviour of the charger
        as a result of the charge profiles applied
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.GetSchedulesRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary:
        Command to see the scheduled upcoming behaviour of the charger as a
        result of the charge profiles applied
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/transaction/start:
    post:
      description:
        Command to send a RemoteStartTransaction message to the charging
        station
      parameters:
        - description: Charge Station PPID.
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.StartTransactionRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a RemoteStartTransaction message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/transaction/stop:
    post:
      description:
        Command to send a RemoteStopTransaction message to the charging
        station
      parameters:
        - description: Charge Station PPID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.StopTransactionRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a RemoteStopTransaction message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/trigger:
    post:
      description: Command to send a trigger message to the charging station
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.TriggerRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a trigger message to the charging station
      x-codegen-request-body-name: req
  /commands/charging-stations/{ppid}/unlock-connector:
    post:
      description: Command to send an UnlockConnector message to the charging station
      parameters:
        - description: Charge Station PPID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.UnlockConnectorRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send an UnlockConnector message to the charging station
      x-codegen-request-body-name: req
  /commands/pcbs/:pcbSerialNumber/certificate-signed:
    post:
      description: Command to send back the signed certificate back to the PCB
      parameters:
        - description: PCB Serial Number
          in: path
          name: pcbSerialNumber
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.CertificateSignedRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '202':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: Accepted
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send back the signed certificate back to the PCB
      x-codegen-request-body-name: req
  /commands/pcbs/{pcbSerialNumber}/config/variables/get:
    post:
      description: Command to send a GetConfiguration message to the PCB
      parameters:
        - description: PCB Serial Number
          in: path
          name: pcbSerialNumber
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
              type: array
        description:
          Config Variables to request. Each string item must not exceed
          50 characters.
        required: false
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a GetConfiguration message to the PCB
      x-codegen-request-body-name: keys
  /commands/pcbs/{pcbSerialNumber}/config/variables/set:
    post:
      description: Command to set configuration variables on the PCB
      parameters:
        - description: PCB Serial Number
          in: path
          name: pcbSerialNumber
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.SetConfigVariablesRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to set configuration variables on the PCB
      x-codegen-request-body-name: req
  /commands/pcbs/{pcbSerialNumber}/reset:
    post:
      description: Command to send a reset message to a PCB
      parameters:
        - description: PCB Serial Number
          in: path
          name: pcbSerialNumber
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.ResetRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a reset message to a PCB
      x-codegen-request-body-name: req
  /commands/pcbs/{pcbSerialNumber}/trigger:
    post:
      description: Command to send a trigger message to the charging station
      parameters:
        - description: PCB Serial Number
          in: path
          name: pcbSerialNumber
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/handlers.TriggerRequestBody'
        description: Request body
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.CommandResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '405':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Method Not Allowed
        '410':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Gone
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Command to send a trigger message to the charging station
      x-codegen-request-body-name: req
components:
  schemas:
    handlers.AuthorizationData:
      properties:
        idTag:
          example: some-id-tag
          maxLength: 20
          type: string
        idTagInfo:
          $ref: '#/components/schemas/handlers.IdTagInfo'
      required:
        - idTag
      type: object
    handlers.CertificateSignedRequestBody:
      properties:
        certificateChain:
          example: |-
            -----BEGIN CERTIFICATE-----
            ...
          maxLength: 10000
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - certificateChain
        - clientRef
      type: object
    handlers.ChargingProfile:
      properties:
        chargingProfileId:
          description: Unique identifier for this profile.
          example: 1234
          type: integer
        chargingProfileKind:
          description: Indicates the kind of schedule.
          enum:
            - Absolute
            - Recurring
            - Relative
          example: Absolute
          type: string
        chargingProfilePurpose:
          description: Defines the purpose of the schedule transferred by this message.
          enum:
            - ChargePointMaxProfile
            - TxDefaultProfile
            - TxProfile
          example: ChargePointMaxProfile
          type: string
        chargingSchedule:
          allOf:
            - $ref: '#/components/schemas/handlers.ChargingSchedule'
          description: Contains limits for the available power or current over time.
          type: object
        recurrencyKind:
          description: Indicates the start point of a recurrence.
          enum:
            - Daily
            - Weekly
          example: Weekly
          type: string
        stackLevel:
          description:
            Value determining level in hierarchy stack of profiles. Higher
            values have precedence over lower values. Lowest level is 0.
          example: 1001
          minimum: 0
          type: integer
        transactionId:
          description:
            "Only valid if ChargingProfilePurpose is set to TxProfile,\
            \ the transactionId MAY be used to match the profile to a specific transaction."
          example: 12345
          type: integer
        validFrom:
          description:
            "Point in time at which the profile starts to be valid. If\
            \ absent, the profile is valid as soon as it is received by the Charge\
            \ Point."
          format: date-time
          type: string
        validTo:
          description:
            "Point in time at which the profile stops to be valid. If absent,\
            \ the profile is valid until it is replaced by another profile."
          format: date-time
          type: string
      required:
        - chargingProfileId
        - chargingProfileKind
        - chargingProfilePurpose
        - chargingSchedule
        - stackLevel
      type: object
    handlers.ChargingSchedule:
      properties:
        chargingRateUnit:
          description: The unit of measure Limit is expressed in.
          enum:
            - A
            - W
          example: A
          type: string
        chargingSchedulePeriod:
          description:
            List of ChargingSchedulePeriod elements defining maximum power
            or current usage over time. The startSchedule of the first ChargingSchedulePeriod
            SHALL always be 0.
          items:
            $ref: '#/components/schemas/handlers.ChargingSchedulePeriod'
          minItems: 1
          type: array
        duration:
          description:
            "Duration of the charging schedule in seconds. If the duration\
            \ is left empty, the last period will continue indefinitely or until end\
            \ of the transaction in case startSchedule is absent."
          example: 3600
          type: integer
        minChargingRate:
          description:
            Minimum charging rate supported by the electric vehicle. The
            unit of measure is defined by the chargingRateUnit. This parameter is
            intended to be used by a local smart charging algorithm to optimize the
            power allocation for in the case a charging process is inefficient at
            lower charging rates. Accepts at most one digit fraction (e.g. 8.1)
          example: 8.1
          type: number
        startSchedule:
          description:
            Starting point of an absolute schedule. If absent the schedule
            will be relative to start of charging.
          format: date-time
          type: string
      required:
        - chargingRateUnit
        - chargingSchedulePeriod
      type: object
    handlers.ChargingSchedulePeriod:
      properties:
        limit:
          description:
            "Charging rate limit during the schedule period, in the applicable\
            \ chargingRateUnit, for example in Amperes or Watts. Accepts at most one\
            \ digit fraction (e.g. 8.1)."
          example: 8.1
          type: number
        numberPhases:
          description:
            "The number of phases that can be used for charging. If a number\
            \ of phases is needed, numberPhases=3 will be assumed unless another number\
            \ is given."
          example: 3
          type: integer
        startPeriod:
          description:
            "Start of the period, in seconds from the start of schedule.\
            \ The value of StartPeriod also defines the stop time of the previous\
            \ period."
          example: 0
          minimum: 0
          type: integer
      required:
        - limit
        - startPeriod
      type: object
    handlers.ClearChargingProfileRequestBody:
      properties:
        chargingProfileId:
          description: The ID of the charging profile to clear
          example: 1001
          type: integer
        chargingProfilePurpose:
          description:
            "Specifies to purpose of the charging profiles that will be\
            \ cleared, if they meet the other criteria in the request"
          enum:
            - ChargePointMaxProfile
            - TxDefaultProfile
            - TxProfile
          type: string
        connectorId:
          description:
            Specifies the ID of the connector for which to clear charging
            profiles. A connectorId of zero (0) specifies the charging profile for
            the overall Charge Point. Absence of this parameter means the clearing
            applies to all charging profiles that match the other criteria in the
            request.
          maximum: 3
          minimum: 0
          type: integer
        stackLevel:
          description:
            "Specifies the stackLevel for which charging profiles will\
            \ be cleared, if they meet the other criteria in the request"
          type: integer
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
      type: object
    handlers.CommandResponse:
      example:
        commandType: GetVariables
        response: '{}'
        messageId: b98e38a2-35d0-4eef-b04c-16a7a9240557
        commandId: 715eea26-1719-4655-b99c-7f561f888703
        statusMessage: statusMessage
        status: Success
      properties:
        commandId:
          example: 715eea26-1719-4655-b99c-7f561f888703
          type: string
        commandType:
          example: GetVariables
          type: string
        messageId:
          example: b98e38a2-35d0-4eef-b04c-16a7a9240557
          type: string
        response:
          allOf:
            - $ref: '#/components/schemas/handlers.SynchronousCommandResponse'
          description:
            This field will only be populated if the request was a successful
            synchronous command.
          type: object
        status:
          description: The overall status of the sent command.
          enum:
            - Success
            - Accepted
            - Timed Out
          example: Success
          type: string
        statusMessage:
          type: string
      type: object
    handlers.DebugRequestBody:
      properties:
        action:
          description: The OCPP Action to send
          example: TriggerMessage
          type: string
        payload:
          additionalProperties:
            type: object
          description: The OCPP Payload to send
          type: object
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - action
        - clientRef
        - payload
      type: object
    handlers.GetConfigVariablesRequestBody:
      properties:
        keys:
          description:
            Config Variables to request. Each string item must not exceed
            50 characters.
          items:
            type: string
          type: array
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
      type: object
    handlers.GetLogsRequestBody:
      properties:
        fromTimestamp:
          description:
            The date and time of the oldest logging information to include
            in the log
          example: 2024-04-10T15:03:27Z
          format: date-time
          type: string
        logType:
          description: The type of log file that the Charging Station should send
          enum:
            - DiagnosticsLog
            - SecurityLog
          example: DiagnosticsLog
          type: string
        requestId:
          description: The ID of this request
          example: 12345
          type: integer
        retries:
          description:
            "How many times the Charge Point must try to upload the log\
            \ before giving up. If this field is not present, it is left to Charging\
            \ Station to decide how many times it wants to retry"
          example: 2
          type: integer
        retryInterval:
          description:
            "The interval in seconds after which a retry may be attempted.\
            \ If this field is not present, it is left to Charging Station to decide\
            \ how long to wait between attempts"
          example: 600
          type: integer
        toTimestamp:
          description:
            The date and time of the latest logging information to include
            in the log
          example: 2024-04-10T16:12:19Z
          format: date-time
          type: string
        uploadURL:
          description:
            The URL of the location at the remote system where the log
            should be stored
          maxLength: 512
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - logType
        - requestId
        - uploadURL
      type: object
    handlers.GetSchedulesRequestBody:
      properties:
        connectorId:
          description:
            "The ID of the Connector for which the schedule is requested.\
            \ When ConnectorId=0, the Charging Station will calculate the expected\
            \ consumption for the grid connection"
          example: 0
          maximum: 3
          minimum: 0
          type: integer
        duration:
          description: Length of requested schedule in seconds
          example: 3600
          type: integer
        unit:
          description: Unit to force a power or current profile
          enum:
            - A
            - W
          example: W
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - connectorId
        - duration
      type: object
    handlers.IdTagInfo:
      properties:
        expiryDate:
          example: 2030-12-31T23:59:59Z
          format: date-time
          type: string
        parentIdTag:
          example: parent-id
          maxLength: 20
          type: string
        status:
          enum:
            - Accepted
            - Blocked
            - Expired
            - Invalid
            - ConcurrentTx
          example: Accepted
          type: string
      required:
        - status
      type: object
    handlers.LimitInfo:
      properties:
        energy:
          example: 15
          type: integer
        time:
          example: 3600
          type: integer
      type: object
    handlers.ResetRequestBody:
      properties:
        type:
          description: The type of reset to initiate
          enum:
            - Soft
            - Hard
          example: Soft
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - type
      type: object
    handlers.SendLocalListRequestBody:
      properties:
        listVersion:
          description:
            In case of a full update this is the version number of the
            full list. In case of a differential update it is the version number of
            the list after the update has been applied
          example: 1
          type: integer
        localAuthorizationList:
          description:
            In case of a full update this contains the list of values that
            form the new local authorization list. In case of a differential update
            it contains the changes to be applied to the local authorization list
            in the Charging Station
          items:
            $ref: '#/components/schemas/handlers.AuthorizationData'
          type: array
        updateType:
          description: The type of update (full or differential)
          enum:
            - Differential
            - Full
          example: Differential
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - listVersion
        - updateType
      type: object
    handlers.SetAvailabilityRequestBody:
      properties:
        connectorId:
          description: The connector in which the availability status should be set
          example: 1
          maximum: 3
          minimum: 0
          type: integer
        type:
          description: The availability type to set
          enum:
            - Operative
            - Inoperative
          example: Operative
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - connectorId
        - type
      type: object
    handlers.SetChargingProfileRequestBody:
      properties:
        chargingProfile:
          allOf:
            - $ref: '#/components/schemas/handlers.ChargingProfile'
          description: The charging profile to be set on the Charging Station
          type: object
        connectorId:
          description:
            "The connector to which the charging profile applies. If connectorId\
            \ = 0, the message contains an overall limit for the Charging Station"
          example: 1
          maximum: 3
          minimum: 0
          type: integer
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - chargingProfile
        - clientRef
        - connectorId
      type: object
    handlers.SetConfigVariablesRequestBody:
      properties:
        key:
          description: Configuration variable name is case-insensitive
          maxLength: 50
          type: string
        value:
          description: Configuration variable value is case-insensitive
          maxLength: 500
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - key
        - value
      type: object
    handlers.StartTransactionRequestBody:
      properties:
        authoriserId:
          description: ID of the transaction authoriser
          maxLength: 20
          type: string
        connectorId:
          description: ID of the connector to begin a transaction on
          example: 1
          maximum: 3
          minimum: 1
          type: integer
        limit:
          allOf:
            - $ref: '#/components/schemas/handlers.LimitInfo'
          description: Any limitations that should be enforced on the charging cycle
          type: object
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - authoriserId
        - clientRef
        - connectorId
      type: object
    handlers.StopTransactionRequestBody:
      properties:
        chargeCycleId:
          description: ID of the charge cycle to stop
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - chargeCycleId
        - clientRef
      type: object
    handlers.SynchronousCommandResponse:
      properties:
        errorCode:
          type: string
        errorReason:
          type: string
        payload:
          type: object
        status:
          type: string
      type: object
    handlers.TriggerRequestBody:
      properties:
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        requestedMessage:
          description: OCPP Message to send
          enum:
            - BootNotification
            - DiagnosticsStatusNotification
            - LogStatusNotification
            - FirmwareStatusNotification
            - Heartbeat
            - MeterValues
            - SignCertificate
            - StatusNotification
          example: Heartbeat
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - requestedMessage
      type: object
    handlers.UnlockConnectorRequestBody:
      properties:
        connectorId:
          description: ID of the connector to unlock
          example: 1
          maximum: 3
          minimum: 1
          type: integer
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - connectorId
      type: object
    handlers.UpdateFirmwareRequestBody:
      properties:
        certificate:
          description:
            "Certificate with which the firmware was signed. PEM encoded\
            \ X.509 certificate. (When updateType is set to 'unsigned', this parameter\
            \ is optional and will be ignored.)"
          type: string
        firmwareURL:
          description: URI defining the origin of the firmware
          type: string
        installAt:
          description:
            "Date and time at which the firmware shall be installed (When\
            \ updateType is set to 'unsigned', this parameter is optional and will\
            \ be ignored.)"
          format: date-time
          type: string
        requestId:
          description:
            "ID that will be used by the charging station when sending\
            \ status updates on the firmware update process. (When updateType is set\
            \ to 'unsigned', this parameter is optional and will be ignored.)"
          type: integer
        retries:
          description:
            "This specifies how many times Charging Station must try to\
            \ download the firmware before giving up. If this field is not present,\
            \ it is left to Charging Station to decide how many times it wants to\
            \ retry"
          example: 3
          type: integer
        retrieveAt:
          description: Date and time at which the firmware shall be retrieved
          format: date-time
          type: string
        retryInterval:
          description:
            "The interval (in seconds) after which a retry may be attempted.\
            \ If this field is not present, it is left to Charging Station to decide\
            \ how long to wait between attempts"
          example: 300
          type: integer
        signature:
          description:
            "Base64 encoded firmware signature. (When updateType is set\
            \ to 'unsigned', this parameter is optional and will be ignored)"
          format: byte
          pattern: '^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$'
          type: string
        updateType:
          description:
            Whether the firmware update is to be a signed (whitepaper)
            or unsigned (regular OCPP1.6) request
          enum:
            - Signed
            - Unsigned
          example: Unsigned
          type: string
        clientRef:
          description: Reference of the client invoking the API endpoint
          example: my-service
          type: string
        evseId:
          description:
            The EVSE to respond to the command. Required when interacting
            with arch5 charging stations
          example: 1
          type: integer
        queued:
          default: false
          description:
            "If true, send this command via a queue to be sent when the\
            \ Charging Station/PCB is next online"
          example: false
          type: boolean
        sync:
          default: false
          description: 'If true, send the command and synchronously wait for the response'
          example: false
          type: boolean
        timeout:
          description: Requires sync=true. The number of seconds to wait for a response
          example: 10
          maximum: 30
          minimum: 1
          type: integer
      required:
        - clientRef
        - firmwareURL
        - retrieveAt
        - updateType
      type: object
    server.ApiErrorResponse:
      example:
        reason: Validation error
        status: Rejected
      properties:
        reason:
          description: Verbose error message
          example: Validation error
          type: string
        status:
          description: The overall status of the request
          example: Rejected
          type: string
      type: object
x-original-swagger-version: '2.0'
