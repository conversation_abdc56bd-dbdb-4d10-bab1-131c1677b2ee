/* tslint:disable */
/* eslint-disable */
/**
 * Axle API
 * For full documentation please see our official docs at <a href=\'http://docs.axle.energy\'>docs.axle.energy</a>
 *
 * The version of the OpenAPI document: 1.4.6
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AssetEventRequest
 */
export interface AssetEventRequest {
  /**
   *
   * @type {AssetEventType}
   * @memberof AssetEventRequest
   */
  event_type: AssetEventType;
  /**
   *
   * @type {Intent}
   * @memberof AssetEventRequest
   */
  intent?: Intent;
  /**
   *
   * @type {Vehicle}
   * @memberof AssetEventRequest
   */
  vehicle?: Vehicle;
  /**
   *
   * @type {Tariff}
   * @memberof AssetEventRequest
   */
  tariff?: Tariff;
}

/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum AssetEventType {
  PlugIn = 'plug_in',
  PlugOut = 'plug_out',
  IntentUpdate = 'intent_update',
}

/**
 *
 * @export
 * @interface AssetRequest
 */
export interface AssetRequest {
  /**
   *
   * @type {string}
   * @memberof AssetRequest
   */
  external_id: string;
  /**
   *
   * @type {CreatableAssetType}
   * @memberof AssetRequest
   */
  type: CreatableAssetType;
  /**
   *
   * @type {string}
   * @memberof AssetRequest
   */
  site_id: string;
}

/**
 *
 * @export
 * @interface AssetResponse
 */
export interface AssetResponse {
  /**
   *
   * @type {string}
   * @memberof AssetResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof AssetResponse
   */
  external_id: string;
  /**
   *
   * @type {AssetType}
   * @memberof AssetResponse
   */
  type: AssetType;
  /**
   *
   * @type {string}
   * @memberof AssetResponse
   */
  site_id: string;
}

/**
 *
 * @export
 * @interface AssetResponsePage
 */
export interface AssetResponsePage {
  /**
   *
   * @type {Array<AssetResponse>}
   * @memberof AssetResponsePage
   */
  assets: Array<AssetResponse>;
  /**
   *
   * @type {number}
   * @memberof AssetResponsePage
   */
  total_num_assets: number;
  /**
   *
   * @type {number}
   * @memberof AssetResponsePage
   */
  total_num_pages: number;
  /**
   *
   * @type {number}
   * @memberof AssetResponsePage
   */
  page_number: number;
}
/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum AssetType {
  BoundaryMeter = 'boundary meter',
  Charger = 'charger',
  Battery = 'battery',
  HeatPump = 'heat pump',
  HotWaterTank = 'hot water tank',
  FrequencyMeter = 'frequency meter',
}

/**
 *
 * @export
 * @interface BadRequestResponse
 */
export interface BadRequestResponse {
  /**
   *
   * @type {string}
   * @memberof BadRequestResponse
   */
  detail: string;
}
/**
 * For now this is only used with podpoint when they push us readings for wholesale
 * @export
 * @interface ConsumptionDatum
 */
export interface ConsumptionDatum {
  /**
   *
   * @type {string}
   * @memberof ConsumptionDatum
   */
  asset_id: string;
  /**
   *
   * @type {number}
   * @memberof ConsumptionDatum
   */
  energy?: number;
}
/**
 * For now this is only used when responding to podpoint for readings they push us for wholesale
 * @export
 * @interface ConsumptionDatumResponse
 */
export interface ConsumptionDatumResponse {
  /**
   *
   * @type {string}
   * @memberof ConsumptionDatumResponse
   */
  asset_id: string;
  /**
   *
   * @type {boolean}
   * @memberof ConsumptionDatumResponse
   */
  accepted: boolean;
  /**
   *
   * @type {string}
   * @memberof ConsumptionDatumResponse
   */
  error?: string;
}
/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum CreatableAssetType {
  Charger = 'charger',
  Battery = 'battery',
}

/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum DNO {
  Ukpn = 'UKPN',
  Nged = 'NGED',
  Ssen = 'SSEN',
  Spen = 'SPEN',
  Npg = 'NPG',
  Enw = 'ENW',
}

/**
 * For now this is only used with podpoint when they push us readings for wholesale
 * @export
 * @interface DataReport
 */
export interface DataReport {
  /**
   *
   * @type {string}
   * @memberof DataReport
   */
  interval_start: string;
  /**
   *
   * @type {Array<ConsumptionDatum>}
   * @memberof DataReport
   */
  charger_consumption_data: Array<ConsumptionDatum>;
}
/**
 * For now this is only used when responding to podpoint for readings they push us for wholesale
 * @export
 * @interface DataResponse
 */
export interface DataResponse {
  /**
   *
   * @type {Array<ConsumptionDatumResponse>}
   * @memberof DataResponse
   */
  charger_consumption_data_response: Array<ConsumptionDatumResponse>;
}
/**
 *
 * @export
 * @interface DnoQualificationResponse
 */
export interface DnoQualificationResponse {
  /**
   *
   * @type {boolean}
   * @memberof DnoQualificationResponse
   */
  eligible: boolean;
  /**
   *
   * @type {DNO}
   * @memberof DnoQualificationResponse
   */
  procurer_name?: DNO;
  /**
   *
   * @type {NextCompetitionResponse}
   * @memberof DnoQualificationResponse
   */
  next_competition?: NextCompetitionResponse;
}

/**
 *
 * @export
 * @interface ErrorWithDetail
 */
export interface ErrorWithDetail {
  /**
   *
   * @type {string}
   * @memberof ErrorWithDetail
   */
  detail: string;
}
/**
 *
 * @export
 * @interface EventResponse
 */
export interface EventResponse {
  /**
   * Timestamp for the response (UTC)
   * @type {string}
   * @memberof EventResponse
   */
  timestamp: string;
  /**
   *
   * @type {EventStatus}
   * @memberof EventResponse
   */
  status: EventStatus;
}

/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum EventStatus {
  Received = 'RECEIVED',
  Accepted = 'ACCEPTED',
  Rejected = 'REJECTED',
  Failed = 'FAILED',
  Executed = 'EXECUTED',
}

/**
 *
 * @export
 * @interface HTTPValidationError
 */
export interface HTTPValidationError {
  /**
   *
   * @type {Array<ValidationError>}
   * @memberof HTTPValidationError
   */
  detail?: Array<ValidationError>;
}
/**
 *
 * @export
 * @interface Intent
 */
export interface Intent {
  /**
   *
   * @type {number}
   * @memberof Intent
   */
  energy_required_kwh: number;
  /**
   *
   * @type {string}
   * @memberof Intent
   */
  ready_by: string;
  /**
   *
   * @type {boolean}
   * @memberof Intent
   */
  energy_estimated: boolean;
}
/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum MarketResponse {
  Cm = 'cm',
  Dfs = 'dfs',
  Dno = 'dno',
  Lcm = 'lcm',
  Sffr = 'sffr',
  Wholesale = 'wholesale',
  DnoNoDispatch = 'dno-no-dispatch',
}

/**
 *
 * @export
 * @interface MeterResponse
 */
export interface MeterResponse {
  /**
   *
   * @type {string}
   * @memberof MeterResponse
   */
  mpan: string;
  /**
   *
   * @type {boolean}
   * @memberof MeterResponse
   */
  half_hourly_settled?: boolean;
  /**
   *
   * @type {string}
   * @memberof MeterResponse
   */
  supplier_mpid?: string;
  /**
   *
   * @type {string}
   * @memberof MeterResponse
   */
  supplier_name?: string;
}
/**
 *
 * @export
 * @interface MeterSearchResponse
 */
export interface MeterSearchResponse {
  /**
   *
   * @type {string}
   * @memberof MeterSearchResponse
   */
  ref: string;
  /**
   *
   * @type {string}
   * @memberof MeterSearchResponse
   */
  address: string;
}
/**
 *
 * @export
 * @interface NextCompetitionResponse
 */
export interface NextCompetitionResponse {
  /**
   *
   * @type {string}
   * @memberof NextCompetitionResponse
   */
  start_date: string;
  /**
   *
   * @type {string}
   * @memberof NextCompetitionResponse
   */
  end_date: string;
  /**
   *
   * @type {number}
   * @memberof NextCompetitionResponse
   */
  max_total_revenue_per_kw: number;
  /**
   *
   * @type {number}
   * @memberof NextCompetitionResponse
   */
  expected_value_per_kw_per_month: number;
}
/**
 *
 * @export
 * @interface SiteMarketConsentRequest
 */
export interface SiteMarketConsentRequest {
  /**
   *
   * @type {Set<MarketResponse>}
   * @memberof SiteMarketConsentRequest
   */
  markets: Set<MarketResponse>;
}
/**
 *
 * @export
 * @interface SiteRequest
 */
export interface SiteRequest {
  /**
   * Meter Point Administration Number, if you don\'t have please see the /meter/ endpoints
   * @type {string}
   * @memberof SiteRequest
   */
  mpan: string;
  /**
   *
   * @type {string}
   * @memberof SiteRequest
   */
  postcode: string;
  /**
   *
   * @type {string}
   * @memberof SiteRequest
   */
  street_address?: string;
}
/**
 *
 * @export
 * @interface SiteResponse
 */
export interface SiteResponse {
  /**
   *
   * @type {string}
   * @memberof SiteResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof SiteResponse
   */
  mpan?: string;
  /**
   *
   * @type {string}
   * @memberof SiteResponse
   */
  postcode?: string;
  /**
   *
   * @type {string}
   * @memberof SiteResponse
   */
  address?: string;
  /**
   *
   * @type {Array<string>}
   * @memberof SiteResponse
   */
  asset_ids?: Array<string>;
  /**
   *
   * @type {Set<MarketResponse>}
   * @memberof SiteResponse
   */
  markets?: Set<MarketResponse>;
  /**
   *
   * @type {TariffResponse}
   * @memberof SiteResponse
   */
  tariff?: TariffResponse;
}
/**
 *
 * @export
 * @interface SiteResponsePage
 */
export interface SiteResponsePage {
  /**
   *
   * @type {Array<SiteResponse>}
   * @memberof SiteResponsePage
   */
  sites: Array<SiteResponse>;
  /**
   *
   * @type {number}
   * @memberof SiteResponsePage
   */
  total_num_sites: number;
  /**
   *
   * @type {number}
   * @memberof SiteResponsePage
   */
  total_num_pages: number;
  /**
   *
   * @type {number}
   * @memberof SiteResponsePage
   */
  page_number: number;
}
/**
 *
 * @export
 * @interface SiteUpdateRequest
 */
export interface SiteUpdateRequest {
  /**
   *
   * @type {Set<MarketResponse>}
   * @memberof SiteUpdateRequest
   */
  markets?: Set<MarketResponse>;
  /**
   *
   * @type {TariffRequest}
   * @memberof SiteUpdateRequest
   */
  tariff?: TariffRequest;
}
/**
 *
 * @export
 * @interface Tariff
 */
export interface Tariff {
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  off_peak_times: string;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  timezone: string;
}
/**
 *
 * @export
 * @interface TariffRequest
 */
export interface TariffRequest {
  /**
   *
   * @type {string}
   * @memberof TariffRequest
   */
  tariff_cheap_start_time?: string;
  /**
   *
   * @type {string}
   * @memberof TariffRequest
   */
  tariff_cheap_end_time?: string;
  /**
   *
   * @type {TariffType}
   * @memberof TariffRequest
   */
  tariff_type: TariffType;
}

/**
 *
 * @export
 * @interface TariffResponse
 */
export interface TariffResponse {
  /**
   *
   * @type {TariffType}
   * @memberof TariffResponse
   */
  tariff_type?: TariffType;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  tariff_cheap_start_time?: string;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  tariff_cheap_end_time?: string;
}

/**
 * An enumeration.
 * @export
 * @enum {string}
 */

export enum TariffType {
  SingleRate = 'single_rate',
  DualRate = 'dual_rate',
  Dynamic = 'dynamic',
  Smart = 'smart',
  Unknown = 'unknown',
}

/**
 *
 * @export
 * @interface Token
 */
export interface Token {
  /**
   *
   * @type {string}
   * @memberof Token
   */
  access_token: string;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  token_type: string;
}
/**
 *
 * @export
 * @interface User
 */
export interface User {
  /**
   *
   * @type {string}
   * @memberof User
   */
  username: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  email?: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  full_name?: string;
  /**
   *
   * @type {boolean}
   * @memberof User
   */
  disabled?: boolean;
}
/**
 *
 * @export
 * @interface ValidationError
 */
export interface ValidationError {
  /**
   *
   * @type {Array<ValidationErrorLocInner>}
   * @memberof ValidationError
   */
  loc: Array<ValidationErrorLocInner>;
  /**
   *
   * @type {string}
   * @memberof ValidationError
   */
  msg: string;
  /**
   *
   * @type {string}
   * @memberof ValidationError
   */
  type: string;
}
/**
 *
 * @export
 * @interface ValidationErrorLocInner
 */
export interface ValidationErrorLocInner {}
/**
 *
 * @export
 * @interface Vehicle
 */
export interface Vehicle {
  /**
   *
   * @type {number}
   * @memberof Vehicle
   */
  charging_rate_kw: number;
}

/**
 * AssetsApi - axios parameter creator
 * @export
 */
export const AssetsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create an asset
     * @summary Create Asset
     * @param {AssetRequest} assetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createAssetEntitiesAssetPost: async (
      assetRequest: AssetRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'assetRequest' is not null or undefined
      assertParamExists(
        'createAssetEntitiesAssetPost',
        'assetRequest',
        assetRequest
      );
      const localVarPath = `/entities/asset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        assetRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete an asset
     * @summary Delete Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteAssetEntitiesAssetAssetIdDelete: async (
      assetId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'assetId' is not null or undefined
      assertParamExists(
        'deleteAssetEntitiesAssetAssetIdDelete',
        'assetId',
        assetId
      );
      const localVarPath = `/entities/asset/{asset_id}`.replace(
        `{${'asset_id'}}`,
        encodeURIComponent(String(assetId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Provide an update on the handling of a given event by a given asset
     * @summary Event Handling Status Update
     * @param {string} assetId
     * @param {string} eventId
     * @param {EventResponse} eventResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost: async (
      assetId: string,
      eventId: string,
      eventResponse: EventResponse,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'assetId' is not null or undefined
      assertParamExists(
        'eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost',
        'assetId',
        assetId
      );
      // verify required parameter 'eventId' is not null or undefined
      assertParamExists(
        'eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost',
        'eventId',
        eventId
      );
      // verify required parameter 'eventResponse' is not null or undefined
      assertParamExists(
        'eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost',
        'eventResponse',
        eventResponse
      );
      const localVarPath = `/entities/asset/{asset_id}/callback/{event_id}`
        .replace(`{${'asset_id'}}`, encodeURIComponent(String(assetId)))
        .replace(`{${'event_id'}}`, encodeURIComponent(String(eventId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        eventResponse,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a specific asset by its external id
     * @summary Get Asset By External Id
     * @param {string} externalId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet: async (
      externalId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'externalId' is not null or undefined
      assertParamExists(
        'getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet',
        'externalId',
        externalId
      );
      const localVarPath = `/entities/asset/external-id/{external_id}`.replace(
        `{${'external_id'}}`,
        encodeURIComponent(String(externalId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a specific asset
     * @summary Get Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetEntitiesAssetAssetIdGet: async (
      assetId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'assetId' is not null or undefined
      assertParamExists('getAssetEntitiesAssetAssetIdGet', 'assetId', assetId);
      const localVarPath = `/entities/asset/{asset_id}`.replace(
        `{${'asset_id'}}`,
        encodeURIComponent(String(assetId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all assets
     * @summary Get Assets
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetsEntitiesAssetGet: async (
      limit?: number,
      offset?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/entities/asset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      if (limit !== undefined) {
        localVarQueryParameter['limit'] = limit;
      }

      if (offset !== undefined) {
        localVarQueryParameter['offset'] = offset;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Handle notification of an event happening on an asset
     * @summary Handle Asset Event
     * @param {string} assetId
     * @param {AssetEventRequest} assetEventRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    handleAssetEventEntitiesAssetAssetIdEventPost: async (
      assetId: string,
      assetEventRequest: AssetEventRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'assetId' is not null or undefined
      assertParamExists(
        'handleAssetEventEntitiesAssetAssetIdEventPost',
        'assetId',
        assetId
      );
      // verify required parameter 'assetEventRequest' is not null or undefined
      assertParamExists(
        'handleAssetEventEntitiesAssetAssetIdEventPost',
        'assetEventRequest',
        assetEventRequest
      );
      const localVarPath = `/entities/asset/{asset_id}/event`.replace(
        `{${'asset_id'}}`,
        encodeURIComponent(String(assetId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        assetEventRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AssetsApi - functional programming interface
 * @export
 */
export const AssetsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AssetsApiAxiosParamCreator(configuration);
  return {
    /**
     * Create an asset
     * @summary Create Asset
     * @param {AssetRequest} assetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createAssetEntitiesAssetPost(
      assetRequest: AssetRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<AssetResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createAssetEntitiesAssetPost(
          assetRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AssetsApi.createAssetEntitiesAssetPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete an asset
     * @summary Delete Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteAssetEntitiesAssetAssetIdDelete(
      assetId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteAssetEntitiesAssetAssetIdDelete(
          assetId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AssetsApi.deleteAssetEntitiesAssetAssetIdDelete']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Provide an update on the handling of a given event by a given asset
     * @summary Event Handling Status Update
     * @param {string} assetId
     * @param {string} eventId
     * @param {EventResponse} eventResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
      assetId: string,
      eventId: string,
      eventResponse: EventResponse,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
          assetId,
          eventId,
          eventResponse,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AssetsApi.eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a specific asset by its external id
     * @summary Get Asset By External Id
     * @param {string} externalId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
      externalId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<AssetResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
          externalId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AssetsApi.getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a specific asset
     * @summary Get Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAssetEntitiesAssetAssetIdGet(
      assetId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<AssetResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAssetEntitiesAssetAssetIdGet(
          assetId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AssetsApi.getAssetEntitiesAssetAssetIdGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all assets
     * @summary Get Assets
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAssetsEntitiesAssetGet(
      limit?: number,
      offset?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AssetResponsePage>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAssetsEntitiesAssetGet(
          limit,
          offset,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AssetsApi.getAssetsEntitiesAssetGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Handle notification of an event happening on an asset
     * @summary Handle Asset Event
     * @param {string} assetId
     * @param {AssetEventRequest} assetEventRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async handleAssetEventEntitiesAssetAssetIdEventPost(
      assetId: string,
      assetEventRequest: AssetEventRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.handleAssetEventEntitiesAssetAssetIdEventPost(
          assetId,
          assetEventRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AssetsApi.handleAssetEventEntitiesAssetAssetIdEventPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AssetsApi - factory interface
 * @export
 */
export const AssetsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AssetsApiFp(configuration);
  return {
    /**
     * Create an asset
     * @summary Create Asset
     * @param {AssetRequest} assetRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createAssetEntitiesAssetPost(
      assetRequest: AssetRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AssetResponse> {
      return localVarFp
        .createAssetEntitiesAssetPost(assetRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete an asset
     * @summary Delete Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteAssetEntitiesAssetAssetIdDelete(
      assetId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteAssetEntitiesAssetAssetIdDelete(assetId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Provide an update on the handling of a given event by a given asset
     * @summary Event Handling Status Update
     * @param {string} assetId
     * @param {string} eventId
     * @param {EventResponse} eventResponse
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
      assetId: string,
      eventId: string,
      eventResponse: EventResponse,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<any> {
      return localVarFp
        .eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
          assetId,
          eventId,
          eventResponse,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a specific asset by its external id
     * @summary Get Asset By External Id
     * @param {string} externalId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
      externalId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AssetResponse> {
      return localVarFp
        .getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
          externalId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a specific asset
     * @summary Get Asset
     * @param {string} assetId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetEntitiesAssetAssetIdGet(
      assetId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AssetResponse> {
      return localVarFp
        .getAssetEntitiesAssetAssetIdGet(assetId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all assets
     * @summary Get Assets
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAssetsEntitiesAssetGet(
      limit?: number,
      offset?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AssetResponsePage> {
      return localVarFp
        .getAssetsEntitiesAssetGet(limit, offset, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Handle notification of an event happening on an asset
     * @summary Handle Asset Event
     * @param {string} assetId
     * @param {AssetEventRequest} assetEventRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    handleAssetEventEntitiesAssetAssetIdEventPost(
      assetId: string,
      assetEventRequest: AssetEventRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<any> {
      return localVarFp
        .handleAssetEventEntitiesAssetAssetIdEventPost(
          assetId,
          assetEventRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AssetsApi - object-oriented interface
 * @export
 * @class AssetsApi
 * @extends {BaseAPI}
 */
export class AssetsApi extends BaseAPI {
  /**
   * Create an asset
   * @summary Create Asset
   * @param {AssetRequest} assetRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public createAssetEntitiesAssetPost(
    assetRequest: AssetRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .createAssetEntitiesAssetPost(assetRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete an asset
   * @summary Delete Asset
   * @param {string} assetId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public deleteAssetEntitiesAssetAssetIdDelete(
    assetId: string,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .deleteAssetEntitiesAssetAssetIdDelete(assetId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Provide an update on the handling of a given event by a given asset
   * @summary Event Handling Status Update
   * @param {string} assetId
   * @param {string} eventId
   * @param {EventResponse} eventResponse
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
    assetId: string,
    eventId: string,
    eventResponse: EventResponse,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .eventHandlingStatusUpdateEntitiesAssetAssetIdCallbackEventIdPost(
        assetId,
        eventId,
        eventResponse,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a specific asset by its external id
   * @summary Get Asset By External Id
   * @param {string} externalId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
    externalId: string,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .getAssetByExternalIdEntitiesAssetExternalIdExternalIdGet(
        externalId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a specific asset
   * @summary Get Asset
   * @param {string} assetId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public getAssetEntitiesAssetAssetIdGet(
    assetId: string,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .getAssetEntitiesAssetAssetIdGet(assetId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all assets
   * @summary Get Assets
   * @param {number} [limit]
   * @param {number} [offset]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public getAssetsEntitiesAssetGet(
    limit?: number,
    offset?: number,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .getAssetsEntitiesAssetGet(limit, offset, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Handle notification of an event happening on an asset
   * @summary Handle Asset Event
   * @param {string} assetId
   * @param {AssetEventRequest} assetEventRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AssetsApi
   */
  public handleAssetEventEntitiesAssetAssetIdEventPost(
    assetId: string,
    assetEventRequest: AssetEventRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AssetsApiFp(this.configuration)
      .handleAssetEventEntitiesAssetAssetIdEventPost(
        assetId,
        assetEventRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * AuthenticationApi - axios parameter creator
 * @export
 */
export const AuthenticationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Login For Access Token
     * @param {string} username
     * @param {string} password
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    loginForAccessTokenAuthTokenPost: async (
      username: string,
      password: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'username' is not null or undefined
      assertParamExists(
        'loginForAccessTokenAuthTokenPost',
        'username',
        username
      );
      // verify required parameter 'password' is not null or undefined
      assertParamExists(
        'loginForAccessTokenAuthTokenPost',
        'password',
        password
      );
      const localVarPath = `/auth/token`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (username !== undefined) {
        localVarQueryParameter['username'] = username;
      }

      if (password !== undefined) {
        localVarQueryParameter['password'] = password;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Login For Access Token Form
     * @param {string} username
     * @param {string} password
     * @param {string} [grantType]
     * @param {string} [scope]
     * @param {string} [clientId]
     * @param {string} [clientSecret]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    loginForAccessTokenFormAuthTokenFormPost: async (
      username: string,
      password: string,
      grantType?: string,
      scope?: string,
      clientId?: string,
      clientSecret?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'username' is not null or undefined
      assertParamExists(
        'loginForAccessTokenFormAuthTokenFormPost',
        'username',
        username
      );
      // verify required parameter 'password' is not null or undefined
      assertParamExists(
        'loginForAccessTokenFormAuthTokenFormPost',
        'password',
        password
      );
      const localVarPath = `/auth/token-form`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;
      const localVarFormParams = new URLSearchParams();

      if (grantType !== undefined) {
        localVarFormParams.set('grant_type', grantType as any);
      }

      if (username !== undefined) {
        localVarFormParams.set('username', username as any);
      }

      if (password !== undefined) {
        localVarFormParams.set('password', password as any);
      }

      if (scope !== undefined) {
        localVarFormParams.set('scope', scope as any);
      }

      if (clientId !== undefined) {
        localVarFormParams.set('client_id', clientId as any);
      }

      if (clientSecret !== undefined) {
        localVarFormParams.set('client_secret', clientSecret as any);
      }

      localVarHeaderParameter['Content-Type'] =
        'application/x-www-form-urlencoded';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = localVarFormParams.toString();

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Read Users Me
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    readUsersMeAuthUsersMeGet: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/auth/users/me/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AuthenticationApi - functional programming interface
 * @export
 */
export const AuthenticationApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    AuthenticationApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Login For Access Token
     * @param {string} username
     * @param {string} password
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async loginForAccessTokenAuthTokenPost(
      username: string,
      password: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Token>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.loginForAccessTokenAuthTokenPost(
          username,
          password,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthenticationApi.loginForAccessTokenAuthTokenPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Login For Access Token Form
     * @param {string} username
     * @param {string} password
     * @param {string} [grantType]
     * @param {string} [scope]
     * @param {string} [clientId]
     * @param {string} [clientSecret]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async loginForAccessTokenFormAuthTokenFormPost(
      username: string,
      password: string,
      grantType?: string,
      scope?: string,
      clientId?: string,
      clientSecret?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Token>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.loginForAccessTokenFormAuthTokenFormPost(
          username,
          password,
          grantType,
          scope,
          clientId,
          clientSecret,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthenticationApi.loginForAccessTokenFormAuthTokenFormPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Read Users Me
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async readUsersMeAuthUsersMeGet(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<User>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.readUsersMeAuthUsersMeGet(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthenticationApi.readUsersMeAuthUsersMeGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AuthenticationApi - factory interface
 * @export
 */
export const AuthenticationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AuthenticationApiFp(configuration);
  return {
    /**
     *
     * @summary Login For Access Token
     * @param {string} username
     * @param {string} password
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    loginForAccessTokenAuthTokenPost(
      username: string,
      password: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Token> {
      return localVarFp
        .loginForAccessTokenAuthTokenPost(username, password, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Login For Access Token Form
     * @param {string} username
     * @param {string} password
     * @param {string} [grantType]
     * @param {string} [scope]
     * @param {string} [clientId]
     * @param {string} [clientSecret]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    loginForAccessTokenFormAuthTokenFormPost(
      username: string,
      password: string,
      grantType?: string,
      scope?: string,
      clientId?: string,
      clientSecret?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Token> {
      return localVarFp
        .loginForAccessTokenFormAuthTokenFormPost(
          username,
          password,
          grantType,
          scope,
          clientId,
          clientSecret,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Read Users Me
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    readUsersMeAuthUsersMeGet(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<User> {
      return localVarFp
        .readUsersMeAuthUsersMeGet(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AuthenticationApi - object-oriented interface
 * @export
 * @class AuthenticationApi
 * @extends {BaseAPI}
 */
export class AuthenticationApi extends BaseAPI {
  /**
   *
   * @summary Login For Access Token
   * @param {string} username
   * @param {string} password
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthenticationApi
   */
  public loginForAccessTokenAuthTokenPost(
    username: string,
    password: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthenticationApiFp(this.configuration)
      .loginForAccessTokenAuthTokenPost(username, password, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Login For Access Token Form
   * @param {string} username
   * @param {string} password
   * @param {string} [grantType]
   * @param {string} [scope]
   * @param {string} [clientId]
   * @param {string} [clientSecret]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthenticationApi
   */
  public loginForAccessTokenFormAuthTokenFormPost(
    username: string,
    password: string,
    grantType?: string,
    scope?: string,
    clientId?: string,
    clientSecret?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthenticationApiFp(this.configuration)
      .loginForAccessTokenFormAuthTokenFormPost(
        username,
        password,
        grantType,
        scope,
        clientId,
        clientSecret,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Read Users Me
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthenticationApi
   */
  public readUsersMeAuthUsersMeGet(options?: RawAxiosRequestConfig) {
    return AuthenticationApiFp(this.configuration)
      .readUsersMeAuthUsersMeGet(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DataApi - axios parameter creator
 * @export
 */
export const DataApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Record Readings
     * @param {DataReport} dataReport
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    recordReadingsDataReportsPost: async (
      dataReport: DataReport,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'dataReport' is not null or undefined
      assertParamExists(
        'recordReadingsDataReportsPost',
        'dataReport',
        dataReport
      );
      const localVarPath = `/data/reports`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        dataReport,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DataApi - functional programming interface
 * @export
 */
export const DataApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DataApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Record Readings
     * @param {DataReport} dataReport
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async recordReadingsDataReportsPost(
      dataReport: DataReport,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<DataResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.recordReadingsDataReportsPost(
          dataReport,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DataApi.recordReadingsDataReportsPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DataApi - factory interface
 * @export
 */
export const DataApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DataApiFp(configuration);
  return {
    /**
     *
     * @summary Record Readings
     * @param {DataReport} dataReport
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    recordReadingsDataReportsPost(
      dataReport: DataReport,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DataResponse> {
      return localVarFp
        .recordReadingsDataReportsPost(dataReport, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DataApi - object-oriented interface
 * @export
 * @class DataApi
 * @extends {BaseAPI}
 */
export class DataApi extends BaseAPI {
  /**
   *
   * @summary Record Readings
   * @param {DataReport} dataReport
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DataApi
   */
  public recordReadingsDataReportsPost(
    dataReport: DataReport,
    options?: RawAxiosRequestConfig
  ) {
    return DataApiFp(this.configuration)
      .recordReadingsDataReportsPost(dataReport, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ElectronConnectApi - axios parameter creator
 * @export
 */
export const ElectronConnectApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Electron Connect Dispatch Api
     * @param {string} [xSignature]
     * @param {string} [xCorrelationId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    electronConnectDispatchApiElectronConnectDispatchPost: async (
      xSignature?: string,
      xCorrelationId?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/electron-connect/dispatch`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (xSignature != null) {
        localVarHeaderParameter['x-signature'] = String(xSignature);
      }
      if (xCorrelationId != null) {
        localVarHeaderParameter['x-correlation-id'] = String(xCorrelationId);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ElectronConnectApi - functional programming interface
 * @export
 */
export const ElectronConnectApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ElectronConnectApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Electron Connect Dispatch Api
     * @param {string} [xSignature]
     * @param {string} [xCorrelationId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async electronConnectDispatchApiElectronConnectDispatchPost(
      xSignature?: string,
      xCorrelationId?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.electronConnectDispatchApiElectronConnectDispatchPost(
          xSignature,
          xCorrelationId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ElectronConnectApi.electronConnectDispatchApiElectronConnectDispatchPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ElectronConnectApi - factory interface
 * @export
 */
export const ElectronConnectApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ElectronConnectApiFp(configuration);
  return {
    /**
     *
     * @summary Electron Connect Dispatch Api
     * @param {string} [xSignature]
     * @param {string} [xCorrelationId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    electronConnectDispatchApiElectronConnectDispatchPost(
      xSignature?: string,
      xCorrelationId?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<any> {
      return localVarFp
        .electronConnectDispatchApiElectronConnectDispatchPost(
          xSignature,
          xCorrelationId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ElectronConnectApi - object-oriented interface
 * @export
 * @class ElectronConnectApi
 * @extends {BaseAPI}
 */
export class ElectronConnectApi extends BaseAPI {
  /**
   *
   * @summary Electron Connect Dispatch Api
   * @param {string} [xSignature]
   * @param {string} [xCorrelationId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ElectronConnectApi
   */
  public electronConnectDispatchApiElectronConnectDispatchPost(
    xSignature?: string,
    xCorrelationId?: string,
    options?: RawAxiosRequestConfig
  ) {
    return ElectronConnectApiFp(this.configuration)
      .electronConnectDispatchApiElectronConnectDispatchPost(
        xSignature,
        xCorrelationId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * FlexiblePowerApi - axios parameter creator
 * @export
 */
export const FlexiblePowerApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Flexible Power Dispatch V2 Api
     * @param {string} [xSignature]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut: async (
      xSignature?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/flexible-power/v2/dispatch`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (xSignature != null) {
        localVarHeaderParameter['x-signature'] = String(xSignature);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * FlexiblePowerApi - functional programming interface
 * @export
 */
export const FlexiblePowerApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    FlexiblePowerApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Flexible Power Dispatch V2 Api
     * @param {string} [xSignature]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(
      xSignature?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(
          xSignature,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'FlexiblePowerApi.flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * FlexiblePowerApi - factory interface
 * @export
 */
export const FlexiblePowerApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = FlexiblePowerApiFp(configuration);
  return {
    /**
     *
     * @summary Flexible Power Dispatch V2 Api
     * @param {string} [xSignature]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(
      xSignature?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<any> {
      return localVarFp
        .flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(
          xSignature,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * FlexiblePowerApi - object-oriented interface
 * @export
 * @class FlexiblePowerApi
 * @extends {BaseAPI}
 */
export class FlexiblePowerApi extends BaseAPI {
  /**
   *
   * @summary Flexible Power Dispatch V2 Api
   * @param {string} [xSignature]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FlexiblePowerApi
   */
  public flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(
    xSignature?: string,
    options?: RawAxiosRequestConfig
  ) {
    return FlexiblePowerApiFp(this.configuration)
      .flexiblePowerDispatchV2ApiFlexiblePowerV2DispatchPut(xSignature, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * FrequencyApi - axios parameter creator
 * @export
 */
export const FrequencyApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get all of the frequency data for a given time period
     * @summary Get Frequency
     * @param {number} seconds
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getFrequencyFrequencyHistoricGet: async (
      seconds: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'seconds' is not null or undefined
      assertParamExists('getFrequencyFrequencyHistoricGet', 'seconds', seconds);
      const localVarPath = `/frequency/historic`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      if (seconds !== undefined) {
        localVarQueryParameter['seconds'] = seconds;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * FrequencyApi - functional programming interface
 * @export
 */
export const FrequencyApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    FrequencyApiAxiosParamCreator(configuration);
  return {
    /**
     * Get all of the frequency data for a given time period
     * @summary Get Frequency
     * @param {number} seconds
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getFrequencyFrequencyHistoricGet(
      seconds: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<{ [key: string]: number }>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getFrequencyFrequencyHistoricGet(
          seconds,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['FrequencyApi.getFrequencyFrequencyHistoricGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * FrequencyApi - factory interface
 * @export
 */
export const FrequencyApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = FrequencyApiFp(configuration);
  return {
    /**
     * Get all of the frequency data for a given time period
     * @summary Get Frequency
     * @param {number} seconds
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getFrequencyFrequencyHistoricGet(
      seconds: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<{ [key: string]: number }> {
      return localVarFp
        .getFrequencyFrequencyHistoricGet(seconds, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * FrequencyApi - object-oriented interface
 * @export
 * @class FrequencyApi
 * @extends {BaseAPI}
 */
export class FrequencyApi extends BaseAPI {
  /**
   * Get all of the frequency data for a given time period
   * @summary Get Frequency
   * @param {number} seconds
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FrequencyApi
   */
  public getFrequencyFrequencyHistoricGet(
    seconds: number,
    options?: RawAxiosRequestConfig
  ) {
    return FrequencyApiFp(this.configuration)
      .getFrequencyFrequencyHistoricGet(seconds, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * MetersApi - axios parameter creator
 * @export
 */
export const MetersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve the MPAN associated with a given meter reference
     * @summary Get Mpan From Meter Ref
     * @param {string} meterRef Reference of the meter whose MPAN is requested, as returned from the /meters/search endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMpanFromMeterRefMeterMeterRefMpanGet: async (
      meterRef: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'meterRef' is not null or undefined
      assertParamExists(
        'getMpanFromMeterRefMeterMeterRefMpanGet',
        'meterRef',
        meterRef
      );
      const localVarPath = `/meter/{meter_ref}/mpan`.replace(
        `{${'meter_ref'}}`,
        encodeURIComponent(String(meterRef))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a list of possible meters that match the given address details
     * @summary Search Meters
     * @param {string} postcode Postcode at which the requested meter is registered
     * @param {string} [buildingIdentifier] House number or house/ building name, e.g. \&#39;3\&#39;, \&#39;10A\&#39;, \&#39;Flat 4\&#39;, \&#39;The Old Barn\&#39;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchMetersMeterSearchGet: async (
      postcode: string,
      buildingIdentifier?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'postcode' is not null or undefined
      assertParamExists('searchMetersMeterSearchGet', 'postcode', postcode);
      const localVarPath = `/meter/search`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      if (postcode !== undefined) {
        localVarQueryParameter['postcode'] = postcode;
      }

      if (buildingIdentifier !== undefined) {
        localVarQueryParameter['building_identifier'] = buildingIdentifier;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * MetersApi - functional programming interface
 * @export
 */
export const MetersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = MetersApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve the MPAN associated with a given meter reference
     * @summary Get Mpan From Meter Ref
     * @param {string} meterRef Reference of the meter whose MPAN is requested, as returned from the /meters/search endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getMpanFromMeterRefMeterMeterRefMpanGet(
      meterRef: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<MeterResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getMpanFromMeterRefMeterMeterRefMpanGet(
          meterRef,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'MetersApi.getMpanFromMeterRefMeterMeterRefMpanGet'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a list of possible meters that match the given address details
     * @summary Search Meters
     * @param {string} postcode Postcode at which the requested meter is registered
     * @param {string} [buildingIdentifier] House number or house/ building name, e.g. \&#39;3\&#39;, \&#39;10A\&#39;, \&#39;Flat 4\&#39;, \&#39;The Old Barn\&#39;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchMetersMeterSearchGet(
      postcode: string,
      buildingIdentifier?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<MeterSearchResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.searchMetersMeterSearchGet(
          postcode,
          buildingIdentifier,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['MetersApi.searchMetersMeterSearchGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * MetersApi - factory interface
 * @export
 */
export const MetersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = MetersApiFp(configuration);
  return {
    /**
     * Retrieve the MPAN associated with a given meter reference
     * @summary Get Mpan From Meter Ref
     * @param {string} meterRef Reference of the meter whose MPAN is requested, as returned from the /meters/search endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getMpanFromMeterRefMeterMeterRefMpanGet(
      meterRef: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<MeterResponse> {
      return localVarFp
        .getMpanFromMeterRefMeterMeterRefMpanGet(meterRef, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a list of possible meters that match the given address details
     * @summary Search Meters
     * @param {string} postcode Postcode at which the requested meter is registered
     * @param {string} [buildingIdentifier] House number or house/ building name, e.g. \&#39;3\&#39;, \&#39;10A\&#39;, \&#39;Flat 4\&#39;, \&#39;The Old Barn\&#39;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchMetersMeterSearchGet(
      postcode: string,
      buildingIdentifier?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<MeterSearchResponse>> {
      return localVarFp
        .searchMetersMeterSearchGet(postcode, buildingIdentifier, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * MetersApi - object-oriented interface
 * @export
 * @class MetersApi
 * @extends {BaseAPI}
 */
export class MetersApi extends BaseAPI {
  /**
   * Retrieve the MPAN associated with a given meter reference
   * @summary Get Mpan From Meter Ref
   * @param {string} meterRef Reference of the meter whose MPAN is requested, as returned from the /meters/search endpoint
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MetersApi
   */
  public getMpanFromMeterRefMeterMeterRefMpanGet(
    meterRef: string,
    options?: RawAxiosRequestConfig
  ) {
    return MetersApiFp(this.configuration)
      .getMpanFromMeterRefMeterMeterRefMpanGet(meterRef, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a list of possible meters that match the given address details
   * @summary Search Meters
   * @param {string} postcode Postcode at which the requested meter is registered
   * @param {string} [buildingIdentifier] House number or house/ building name, e.g. \&#39;3\&#39;, \&#39;10A\&#39;, \&#39;Flat 4\&#39;, \&#39;The Old Barn\&#39;
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MetersApi
   */
  public searchMetersMeterSearchGet(
    postcode: string,
    buildingIdentifier?: string,
    options?: RawAxiosRequestConfig
  ) {
    return MetersApiFp(this.configuration)
      .searchMetersMeterSearchGet(postcode, buildingIdentifier, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * MiscApi - axios parameter creator
 * @export
 */
export const MiscApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Healthz
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthzHealthzGet: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/healthz`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * MiscApi - functional programming interface
 * @export
 */
export const MiscApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = MiscApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Healthz
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthzHealthzGet(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthzHealthzGet(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['MiscApi.healthzHealthzGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * MiscApi - factory interface
 * @export
 */
export const MiscApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = MiscApiFp(configuration);
  return {
    /**
     *
     * @summary Healthz
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthzHealthzGet(options?: RawAxiosRequestConfig): AxiosPromise<string> {
      return localVarFp
        .healthzHealthzGet(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * MiscApi - object-oriented interface
 * @export
 * @class MiscApi
 * @extends {BaseAPI}
 */
export class MiscApi extends BaseAPI {
  /**
   *
   * @summary Healthz
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MiscApi
   */
  public healthzHealthzGet(options?: RawAxiosRequestConfig) {
    return MiscApiFp(this.configuration)
      .healthzHealthzGet(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * QualificationApi - axios parameter creator
 * @export
 */
export const QualificationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Check Postcode Qualification
     * @param {string} postcode
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkPostcodeQualificationQualificationDnoPostcodeGet: async (
      postcode: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'postcode' is not null or undefined
      assertParamExists(
        'checkPostcodeQualificationQualificationDnoPostcodeGet',
        'postcode',
        postcode
      );
      const localVarPath = `/qualification/dno/{postcode}`.replace(
        `{${'postcode'}}`,
        encodeURIComponent(String(postcode))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * QualificationApi - functional programming interface
 * @export
 */
export const QualificationApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    QualificationApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Check Postcode Qualification
     * @param {string} postcode
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async checkPostcodeQualificationQualificationDnoPostcodeGet(
      postcode: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DnoQualificationResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.checkPostcodeQualificationQualificationDnoPostcodeGet(
          postcode,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'QualificationApi.checkPostcodeQualificationQualificationDnoPostcodeGet'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * QualificationApi - factory interface
 * @export
 */
export const QualificationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = QualificationApiFp(configuration);
  return {
    /**
     *
     * @summary Check Postcode Qualification
     * @param {string} postcode
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkPostcodeQualificationQualificationDnoPostcodeGet(
      postcode: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DnoQualificationResponse> {
      return localVarFp
        .checkPostcodeQualificationQualificationDnoPostcodeGet(
          postcode,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * QualificationApi - object-oriented interface
 * @export
 * @class QualificationApi
 * @extends {BaseAPI}
 */
export class QualificationApi extends BaseAPI {
  /**
   *
   * @summary Check Postcode Qualification
   * @param {string} postcode
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof QualificationApi
   */
  public checkPostcodeQualificationQualificationDnoPostcodeGet(
    postcode: string,
    options?: RawAxiosRequestConfig
  ) {
    return QualificationApiFp(this.configuration)
      .checkPostcodeQualificationQualificationDnoPostcodeGet(postcode, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SitesApi - axios parameter creator
 * @export
 */
export const SitesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Add Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost: async (
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost',
        'siteId',
        siteId
      );
      // verify required parameter 'siteMarketConsentRequest' is not null or undefined
      assertParamExists(
        'addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost',
        'siteMarketConsentRequest',
        siteMarketConsentRequest
      );
      const localVarPath = `/entities/site/{site_id}/market-consent`.replace(
        `{${'site_id'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        siteMarketConsentRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Register a new site
     * @summary Create Site
     * @param {SiteRequest} siteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createSiteEntitiesSitePost: async (
      siteRequest: SiteRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteRequest' is not null or undefined
      assertParamExists(
        'createSiteEntitiesSitePost',
        'siteRequest',
        siteRequest
      );
      const localVarPath = `/entities/site`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        siteRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete an existing site
     * @summary Delete Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteSiteEntitiesSiteSiteIdDelete: async (
      siteId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('deleteSiteEntitiesSiteSiteIdDelete', 'siteId', siteId);
      const localVarPath = `/entities/site/{site_id}`.replace(
        `{${'site_id'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve an existing site
     * @summary Get Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSiteEntitiesSiteSiteIdGet: async (
      siteId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('getSiteEntitiesSiteSiteIdGet', 'siteId', siteId);
      const localVarPath = `/entities/site/{site_id}`.replace(
        `{${'site_id'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve all existing sites for a given user in pages
     * @summary Get Sites
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSitesEntitiesSiteGet: async (
      limit?: number,
      offset?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/entities/site`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      if (limit !== undefined) {
        localVarQueryParameter['limit'] = limit;
      }

      if (offset !== undefined) {
        localVarQueryParameter['offset'] = offset;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Remove Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost: async (
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost',
        'siteId',
        siteId
      );
      // verify required parameter 'siteMarketConsentRequest' is not null or undefined
      assertParamExists(
        'removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost',
        'siteMarketConsentRequest',
        siteMarketConsentRequest
      );
      const localVarPath = `/entities/site/{site_id}/market-deconsent`.replace(
        `{${'site_id'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        siteMarketConsentRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Update Site
     * @param {string} siteId
     * @param {SiteUpdateRequest} siteUpdateRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateSiteEntitiesSiteSiteIdPatch: async (
      siteId: string,
      siteUpdateRequest: SiteUpdateRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('updateSiteEntitiesSiteSiteIdPatch', 'siteId', siteId);
      // verify required parameter 'siteUpdateRequest' is not null or undefined
      assertParamExists(
        'updateSiteEntitiesSiteSiteIdPatch',
        'siteUpdateRequest',
        siteUpdateRequest
      );
      const localVarPath = `/entities/site/{site_id}`.replace(
        `{${'site_id'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OAuth2PasswordBearer required
      // oauth required
      await setOAuthToObject(
        localVarHeaderParameter,
        'OAuth2PasswordBearer',
        [],
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        siteUpdateRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SitesApi - functional programming interface
 * @export
 */
export const SitesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = SitesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Add Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
          siteId,
          siteMarketConsentRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SitesApi.addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Register a new site
     * @summary Create Site
     * @param {SiteRequest} siteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createSiteEntitiesSitePost(
      siteRequest: SiteRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createSiteEntitiesSitePost(
          siteRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.createSiteEntitiesSitePost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete an existing site
     * @summary Delete Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteSiteEntitiesSiteSiteIdDelete(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteSiteEntitiesSiteSiteIdDelete(
          siteId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.deleteSiteEntitiesSiteSiteIdDelete']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve an existing site
     * @summary Get Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getSiteEntitiesSiteSiteIdGet(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getSiteEntitiesSiteSiteIdGet(
          siteId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.getSiteEntitiesSiteSiteIdGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve all existing sites for a given user in pages
     * @summary Get Sites
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getSitesEntitiesSiteGet(
      limit?: number,
      offset?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SiteResponsePage>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getSitesEntitiesSiteGet(
          limit,
          offset,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.getSitesEntitiesSiteGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Remove Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
          siteId,
          siteMarketConsentRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SitesApi.removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Update Site
     * @param {string} siteId
     * @param {SiteUpdateRequest} siteUpdateRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateSiteEntitiesSiteSiteIdPatch(
      siteId: string,
      siteUpdateRequest: SiteUpdateRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<SiteResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateSiteEntitiesSiteSiteIdPatch(
          siteId,
          siteUpdateRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.updateSiteEntitiesSiteSiteIdPatch']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SitesApi - factory interface
 * @export
 */
export const SitesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SitesApiFp(configuration);
  return {
    /**
     *
     * @summary Add Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponse> {
      return localVarFp
        .addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
          siteId,
          siteMarketConsentRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Register a new site
     * @summary Create Site
     * @param {SiteRequest} siteRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createSiteEntitiesSitePost(
      siteRequest: SiteRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponse> {
      return localVarFp
        .createSiteEntitiesSitePost(siteRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete an existing site
     * @summary Delete Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteSiteEntitiesSiteSiteIdDelete(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteSiteEntitiesSiteSiteIdDelete(siteId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve an existing site
     * @summary Get Site
     * @param {string} siteId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSiteEntitiesSiteSiteIdGet(
      siteId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponse> {
      return localVarFp
        .getSiteEntitiesSiteSiteIdGet(siteId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve all existing sites for a given user in pages
     * @summary Get Sites
     * @param {number} [limit]
     * @param {number} [offset]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getSitesEntitiesSiteGet(
      limit?: number,
      offset?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponsePage> {
      return localVarFp
        .getSitesEntitiesSiteGet(limit, offset, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Remove Site Market Consent
     * @param {string} siteId
     * @param {SiteMarketConsentRequest} siteMarketConsentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
      siteId: string,
      siteMarketConsentRequest: SiteMarketConsentRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponse> {
      return localVarFp
        .removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
          siteId,
          siteMarketConsentRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Update Site
     * @param {string} siteId
     * @param {SiteUpdateRequest} siteUpdateRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateSiteEntitiesSiteSiteIdPatch(
      siteId: string,
      siteUpdateRequest: SiteUpdateRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteResponse> {
      return localVarFp
        .updateSiteEntitiesSiteSiteIdPatch(siteId, siteUpdateRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SitesApi - object-oriented interface
 * @export
 * @class SitesApi
 * @extends {BaseAPI}
 */
export class SitesApi extends BaseAPI {
  /**
   *
   * @summary Add Site Market Consent
   * @param {string} siteId
   * @param {SiteMarketConsentRequest} siteMarketConsentRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
    siteId: string,
    siteMarketConsentRequest: SiteMarketConsentRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .addSiteMarketConsentEntitiesSiteSiteIdMarketConsentPost(
        siteId,
        siteMarketConsentRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Register a new site
   * @summary Create Site
   * @param {SiteRequest} siteRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public createSiteEntitiesSitePost(
    siteRequest: SiteRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .createSiteEntitiesSitePost(siteRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete an existing site
   * @summary Delete Site
   * @param {string} siteId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public deleteSiteEntitiesSiteSiteIdDelete(
    siteId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .deleteSiteEntitiesSiteSiteIdDelete(siteId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve an existing site
   * @summary Get Site
   * @param {string} siteId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public getSiteEntitiesSiteSiteIdGet(
    siteId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .getSiteEntitiesSiteSiteIdGet(siteId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve all existing sites for a given user in pages
   * @summary Get Sites
   * @param {number} [limit]
   * @param {number} [offset]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public getSitesEntitiesSiteGet(
    limit?: number,
    offset?: number,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .getSitesEntitiesSiteGet(limit, offset, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Remove Site Market Consent
   * @param {string} siteId
   * @param {SiteMarketConsentRequest} siteMarketConsentRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
    siteId: string,
    siteMarketConsentRequest: SiteMarketConsentRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .removeSiteMarketConsentEntitiesSiteSiteIdMarketDeconsentPost(
        siteId,
        siteMarketConsentRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Update Site
   * @param {string} siteId
   * @param {SiteUpdateRequest} siteUpdateRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public updateSiteEntitiesSiteSiteIdPatch(
    siteId: string,
    siteUpdateRequest: SiteUpdateRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .updateSiteEntitiesSiteSiteIdPatch(siteId, siteUpdateRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
