/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/batteries`, async () => {
    const resultArray = [
      [await getListBatteries200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/batteries`, async () => {
    const resultArray = [
      [await getListUserBatteries200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/batteries/:batteryId`, async () => {
    const resultArray = [[await getGetBattery200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/batteries/:batteryId/operation-mode`, async () => {
    const resultArray = [
      [await getPostUserSetOperationMode200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/batteries/actions/:actionId`, async () => {
    const resultArray = [
      [await getGetBatteriesAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/batteries/actions/:actionId/cancel`, async () => {
    const resultArray = [
      [await getCancelBatteryAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/batteries/:batteryId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/chargers`, async () => {
    const resultArray = [[await getListChargers200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/chargers`, async () => {
    const resultArray = [
      [await getListUserChargers200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/chargers/:chargerId`, async () => {
    const resultArray = [[await getGetCharger200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/chargers/:chargerId`, async () => {
    const resultArray = [
      [await getUpdateCharger200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/chargers/:chargerId/charging`, async () => {
    const resultArray = [
      [await getControlChargerCharging200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/chargers/:chargerId/max-current`, async () => {
    const resultArray = [
      [await getPostSetChargerMaxCurrent200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/chargers/actions/:actionId`, async () => {
    const resultArray = [
      [await getGetChargersAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/chargers/actions/:actionId/cancel`, async () => {
    const resultArray = [
      [await getCancelChargerAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/chargers/:chargerId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/chargers/:chargerId/smart-override`, async () => {
    const resultArray = [
      [await getChargerCreateSmartOverride200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/chargers/:chargerId/smart-override`, async () => {
    const resultArray = [
      [await getChargerEndSmartOverride200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/chargers/:chargerId/smart-charging-policy`, async () => {
    const resultArray = [
      [await getChargerSmartPolicy200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/chargers/:chargerId/smart-charging-policy`, async () => {
    const resultArray = [
      [await getUpdateChargerSmartPolicy200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/chargers/:chargerId/smart-charging-status`, async () => {
    const resultArray = [
      [await getChargerSmartChargingStatus200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/hvacs`, async () => {
    const resultArray = [[await getListHvaCs200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/hvacs`, async () => {
    const resultArray = [
      [await getListUserHvaCs200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/hvacs/:hvacId/smart-policy`, async () => {
    const resultArray = [
      [await getGetHvacsHvacIdSmartPolicy200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/hvacs/:hvacId/smart-policy`, async () => {
    const resultArray = [
      [await getUpdateHvacSmartPolicy200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/hvacs/:hvacId/smart-status`, async () => {
    const resultArray = [
      [await getGetHvacSmartStatus200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/hvacs/:hvacId`, async () => {
    const resultArray = [[await getUpdateHvac200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/hvacs/:hvacId`, async () => {
    const resultArray = [[await getGetHvac200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/hvacs/actions/:actionId`, async () => {
    const resultArray = [
      [await getGetHvacsAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/hvacs/actions/:actionId/cancel`, async () => {
    const resultArray = [
      [await getCancelHvacAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/hvacs/:hvacId/follow-schedule`, async () => {
    const resultArray = [
      [await getPostSetHvacFollowSchedule200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/hvacs/:hvacId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/hvacs/:hvacId/permanent-hold`, async () => {
    const resultArray = [
      [await getPostSetHvacPermanentHold200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/interventions`, async () => {
    const resultArray = [
      [await getListInterventions200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/interventions/:interventionId`, async () => {
    const resultArray = [
      [await getGetIntervention200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/inverters`, async () => {
    const resultArray = [
      [await getListInverters200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/inverters`, async () => {
    const resultArray = [
      [await getListUserInverters200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/inverters/:inverterId`, async () => {
    const resultArray = [[await getGetInverter200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/inverters/:inverterId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/locations`, async () => {
    const resultArray = [[await getGetLocations200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/locations`, async () => {
    const resultArray = [
      [await getGetUserlocations200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/users/:userId/locations`, async () => {
    const resultArray = [
      [await getCreateLocation200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/locations/:locationId`, async () => {
    const resultArray = [[await getGetLocation200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/locations/:locationId`, async () => {
    const resultArray = [
      [await getDeleteLocation200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/locations/:locationId`, async () => {
    const resultArray = [
      [await getUpdateLocation200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/meters/:meterId`, async () => {
    const resultArray = [[await getGetMeter200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/meters/:meterId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/meters`, async () => {
    const resultArray = [
      [await getListUserMeters200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/meters`, async () => {
    const resultArray = [[await getListMeters200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/schedules`, async () => {
    const resultArray = [[await getGetSchedules200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/users/:userId/schedules`, async () => {
    const resultArray = [
      [await getCreateSchedule200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/schedules/:scheduleId`, async () => {
    const resultArray = [[await getGetSchedule200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/schedules/:scheduleId`, async () => {
    const resultArray = [
      [await getUpdateSchedule200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/schedules/:scheduleId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/schedules/:scheduleId/status`, async () => {
    const resultArray = [
      [await getGetScheduleStatus200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/statistics/charging`, async () => {
    const resultArray = [
      [await getGetChargingStatistics200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(
    `${baseURL}/users/:userId/statistics/charging/sessions`,
    async () => {
      const resultArray = [
        [await getGetChargingSessionsStatistics200Response(), { status: 200 }],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.get(`${baseURL}/users/:userId/statistics/production`, async () => {
    const resultArray = [
      [await getGetProductionStatistics200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/tariffs/:tariffId`, async () => {
    const resultArray = [
      [await getGetTariffInformation200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/tariffs/:tariffId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/locations/:locationId/tariff`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/locations/:locationId/tariff`, async () => {
    const resultArray = [
      [await getGetUserLocationTariff200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users`, async () => {
    const resultArray = [[await getListUsers200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId`, async () => {
    const resultArray = [[await getGetUser200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/users/:userId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/users/:userId/vendors/:vendor`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(
    `${baseURL}/users/:userId/vendors/:vendor/:vendorType`,
    async () => {
      const resultArray = [[undefined, { status: 204 }]];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/users/:userId/link`, async () => {
    const resultArray = [
      [await getPostUsersUseridLink200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/users/:userId/authorization`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles`, async () => {
    const resultArray = [[await getGetVehicles200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/users/:userId/vehicles`, async () => {
    const resultArray = [
      [await getListUserVehicles200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles/:vehicleId`, async () => {
    const resultArray = [[await getGetVehicle200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/vehicles/:vehicleId/charging`, async () => {
    const resultArray = [
      [await getPostVehiclesVehicleidCharging200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/vehicles/:vehicleId/max-current`, async () => {
    const resultArray = [
      [await getPostVehiclesVehicleidMaxCurrent200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles/actions/:actionId`, async () => {
    const resultArray = [
      [await getGetVehiclesAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/vehicles/actions/:actionId/cancel`, async () => {
    const resultArray = [
      [await getCancelVehicleAction200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/vehicles/:vehicleId/refresh-hint`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(
    `${baseURL}/vehicles/:vehicleId/smart-charging-plans/:smartChargingPlanId`,
    async () => {
      const resultArray = [
        [
          await getGetVehiclesVehicleidSmartchargingplans200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.get(`${baseURL}/vehicles/:vehicleId/smart-charging-policy`, async () => {
    const resultArray = [
      [
        await getGetVehiclesVehicleidSmartchargingpolicy200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/vehicles/:vehicleId/smart-charging-policy`, async () => {
    const resultArray = [
      [await getUpdateVehicleSmartChargingPolicy200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/vehicles/:vehicleId/smart-override`, async () => {
    const resultArray = [
      [await getVehicleCreateSmartOverride200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/vehicles/:vehicleId/smart-override`, async () => {
    const resultArray = [
      [await getVehicleEndSmartOverride200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles/:vehicleId/smart-charging-status`, async () => {
    const resultArray = [
      [
        await getGetVehiclesVehicleidSmartchargingstatus200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.put(`${baseURL}/webhooks/firehose`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/webhooks/firehose`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/webhooks/firehose/test`, async () => {
    const resultArray = [[await getTestFirehose200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/webhooks`, async () => {
    const resultArray = [
      [await getCreateWebhook200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/webhooks`, async () => {
    const resultArray = [[await getListWebhooks200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/webhooks/:webhookId`, async () => {
    const resultArray = [
      [await getUpdateWebhook200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/webhooks/:webhookId`, async () => {
    const resultArray = [[await getGetWebhook200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/webhooks/:webhookId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/webhooks/:webhookId/test`, async () => {
    const resultArray = [[await getTestWebhook200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/chargers`, async () => {
    const resultArray = [
      [await getGetHealthChargerVendors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/vehicles`, async () => {
    const resultArray = [
      [await getGetHealthVehicleVendors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/inverter`, async () => {
    const resultArray = [
      [await getGetHealthInverterVendors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/hvacs`, async () => {
    const resultArray = [
      [await getGetHealthHvacVendors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/meters`, async () => {
    const resultArray = [
      [await getGetHealthMeterVendors200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/health/ready`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/simulated/vehicles`, async () => {
    const resultArray = [
      [await getCreateSimulatedVehicle200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/simulated/vehicles`, async () => {
    const resultArray = [
      [await getListSimulatedVehicle200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/simulated/vehicles/:simulatedVehicleId`, async () => {
    const resultArray = [
      [await getGetSimulatedVehicle200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/simulated/vehicles/:simulatedVehicleId`, async () => {
    const resultArray = [
      [await getUpdateSimulatedVehicle200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/simulated/vehicles/:simulatedVehicleId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getListBatteries200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '54d827e1-8355-4fed-97b5-55940d1d09ba',
      userId: '4f6fecd0-bdae-49be-b6e8-ee442e1e3da9',
      vendor: 'TESLA',
      locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      chargeState: {
        status: 'CHARGING',
        batteryCapacity: 13.5,
        batteryLevel: 80,
        chargeRate: 4.6,
        dischargeLimit: 20,
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      config: {
        operationMode: faker.helpers.arrayElement([
          faker.helpers.arrayElement([
            'IMPORT_FOCUS',
            'EXPORT_FOCUS',
            'TIME_OF_USE',
            'SELF_RELIANCE',
          ]),
          faker.helpers.arrayElement([
            'IMPORT_FOCUS',
            'EXPORT_FOCUS',
            'TIME_OF_USE',
            'SELF_RELIANCE',
          ]),
        ]),
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      information: {
        id: '7deb27f8-794f-467b-855e-5c61dd9f2cb3',
        brand: 'Tesla',
        model: 'Powerwall',
        siteName: 'Powerwall Home',
        installationDate: '2020-04-07T17:04:26Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      capabilities: {
        exportFocus: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        importFocus: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        timeOfUse: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        selfReliance: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListUserBatteries200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '54d827e1-8355-4fed-97b5-55940d1d09ba',
      userId: '4f6fecd0-bdae-49be-b6e8-ee442e1e3da9',
      vendor: 'TESLA',
      locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      chargeState: {
        status: 'CHARGING',
        batteryCapacity: 13.5,
        batteryLevel: 80,
        chargeRate: 4.6,
        dischargeLimit: 20,
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      config: {
        operationMode: faker.helpers.arrayElement([
          faker.helpers.arrayElement([
            'IMPORT_FOCUS',
            'EXPORT_FOCUS',
            'TIME_OF_USE',
            'SELF_RELIANCE',
          ]),
          faker.helpers.arrayElement([
            'IMPORT_FOCUS',
            'EXPORT_FOCUS',
            'TIME_OF_USE',
            'SELF_RELIANCE',
          ]),
        ]),
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      information: {
        id: '7deb27f8-794f-467b-855e-5c61dd9f2cb3',
        brand: 'Tesla',
        model: 'Powerwall',
        siteName: 'Powerwall Home',
        installationDate: '2020-04-07T17:04:26Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      capabilities: {
        exportFocus: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        importFocus: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        timeOfUse: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        selfReliance: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetBattery200Response() {
  return {
    id: '54d827e1-8355-4fed-97b5-55940d1d09ba',
    userId: '4f6fecd0-bdae-49be-b6e8-ee442e1e3da9',
    vendor: 'TESLA',
    locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
    lastSeen: '2020-04-07T17:04:26Z',
    isReachable: faker.datatype.boolean(),
    chargeState: {
      status: 'CHARGING',
      batteryCapacity: 13.5,
      batteryLevel: 80,
      chargeRate: 4.6,
      dischargeLimit: 20,
      lastUpdated: '2020-04-07T17:04:26Z',
    },
    config: {
      operationMode: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          'IMPORT_FOCUS',
          'EXPORT_FOCUS',
          'TIME_OF_USE',
          'SELF_RELIANCE',
        ]),
        faker.helpers.arrayElement([
          'IMPORT_FOCUS',
          'EXPORT_FOCUS',
          'TIME_OF_USE',
          'SELF_RELIANCE',
        ]),
      ]),
      lastUpdated: '2020-04-07T17:04:26Z',
    },
    information: {
      id: '7deb27f8-794f-467b-855e-5c61dd9f2cb3',
      brand: 'Tesla',
      model: 'Powerwall',
      siteName: 'Powerwall Home',
      installationDate: '2020-04-07T17:04:26Z',
    },
    location: {
      longitude: 10.7197486,
      latitude: 59.9173985,
    },
    capabilities: {
      exportFocus: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      importFocus: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      timeOfUse: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      selfReliance: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
    },
    scopes: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.lorem.words()),
  };
}

export function getPostUserSetOperationMode200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'ac03a513-5494-4e1c-9dd7-2a29dc024312',
    targetType: faker.helpers.arrayElement(['battery']),
    targetState: {
      operationMode: 'IMPORT_FOCUS',
    },
  };
}

export function getGetBatteriesAction200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'ac03a513-5494-4e1c-9dd7-2a29dc024312',
    targetType: faker.helpers.arrayElement(['battery']),
    targetState: {
      operationMode: 'IMPORT_FOCUS',
    },
  };
}

export function getCancelBatteryAction200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'ac03a513-5494-4e1c-9dd7-2a29dc024312',
    targetType: faker.helpers.arrayElement(['battery']),
    targetState: {
      operationMode: 'IMPORT_FOCUS',
    },
  };
}

export function getBatteriesRefreshHint204Response() {
  return null;
}

export function getListChargers200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '2211e263-0362-4235-83f4-887bdf3ee414',
      userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
      vendor: 'ZAPTEC',
      lastSeen: '2023-03-21T21:08:27.596Z',
      isReachable: true,
      locationId: '2211e263-d6d4-d6d4-d6d4-dbdd77ec82b6',
      chargeState: {
        isPluggedIn: true,
        isCharging: true,
        chargeRate: 6.939,
        lastUpdated: '2023-03-21T16:39:20.000Z',
        maxCurrent: 16,
        powerDeliveryState: 'PLUGGED_IN:CHARGING',
      },
      information: { brand: 'Zaptec', model: 'ZAPTEC PRO', year: null },
      capabilities: {
        information: { isCapable: true, interventionIds: [] },
        chargeState: { isCapable: true, interventionIds: [] },
        startCharging: { isCapable: true, interventionIds: [] },
        stopCharging: { isCapable: true, interventionIds: [] },
        setMaxCurrent: {
          isCapable: false,
          interventionIds: ['dbdd77ec82b6-d6d4-d6d4-d6d4-dbdd77ec82b6'],
        },
      },
      scopes: ['charger:control:charging', 'charger:read:data'],
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListUserChargers200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '2211e263-0362-4235-83f4-887bdf3ee414',
      userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
      vendor: 'ZAPTEC',
      lastSeen: '2023-03-21T21:08:27.596Z',
      isReachable: true,
      locationId: '2211e263-d6d4-d6d4-d6d4-dbdd77ec82b6',
      chargeState: {
        isPluggedIn: true,
        isCharging: true,
        chargeRate: 6.939,
        lastUpdated: '2023-03-21T16:39:20.000Z',
        maxCurrent: 16,
        powerDeliveryState: 'PLUGGED_IN:CHARGING',
      },
      information: { brand: 'Zaptec', model: 'ZAPTEC PRO', year: null },
      capabilities: {
        information: { isCapable: true, interventionIds: [] },
        chargeState: { isCapable: true, interventionIds: [] },
        startCharging: { isCapable: true, interventionIds: [] },
        stopCharging: { isCapable: true, interventionIds: [] },
        setMaxCurrent: {
          isCapable: false,
          interventionIds: ['dbdd77ec82b6-d6d4-d6d4-d6d4-dbdd77ec82b6'],
        },
      },
      scopes: ['charger:control:charging', 'charger:read:data'],
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetCharger200Response() {
  return {
    id: '2211e263-0362-4235-83f4-887bdf3ee414',
    userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
    vendor: 'ZAPTEC',
    lastSeen: '2023-03-21T21:08:27.596Z',
    isReachable: true,
    locationId: '2211e263-d6d4-d6d4-d6d4-dbdd77ec82b6',
    chargeState: {
      isPluggedIn: true,
      isCharging: true,
      chargeRate: 6.939,
      lastUpdated: '2023-03-21T16:39:20.000Z',
      maxCurrent: 16,
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
    },
    information: { brand: 'Zaptec', model: 'ZAPTEC PRO', year: null },
    capabilities: {
      information: { isCapable: true, interventionIds: [] },
      chargeState: { isCapable: true, interventionIds: [] },
      startCharging: { isCapable: true, interventionIds: [] },
      stopCharging: { isCapable: true, interventionIds: [] },
      setMaxCurrent: {
        isCapable: false,
        interventionIds: ['dbdd77ec82b6-d6d4-d6d4-d6d4-dbdd77ec82b6'],
      },
    },
    scopes: ['charger:control:charging', 'charger:read:data'],
  };
}

export function getUpdateCharger200Response() {
  return {
    id: '2211e263-0362-4235-83f4-887bdf3ee414',
    userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
    vendor: 'ZAPTEC',
    lastSeen: '2023-03-21T21:08:27.596Z',
    isReachable: true,
    locationId: '2211e263-d6d4-d6d4-d6d4-dbdd77ec82b6',
    chargeState: {
      isPluggedIn: true,
      isCharging: true,
      chargeRate: 6.939,
      lastUpdated: '2023-03-21T16:39:20.000Z',
      maxCurrent: 16,
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
    },
    information: { brand: 'Zaptec', model: 'ZAPTEC PRO', year: null },
    capabilities: {
      information: { isCapable: true, interventionIds: [] },
      chargeState: { isCapable: true, interventionIds: [] },
      startCharging: { isCapable: true, interventionIds: [] },
      stopCharging: { isCapable: true, interventionIds: [] },
      setMaxCurrent: {
        isCapable: false,
        interventionIds: ['dbdd77ec82b6-d6d4-d6d4-d6d4-dbdd77ec82b6'],
      },
    },
    scopes: ['charger:control:charging', 'charger:read:data'],
  };
}

export function getControlChargerCharging200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
    targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
    kind: faker.helpers.arrayElement(['START', 'STOP']),
    failureReason: faker.helpers.arrayElement([
      {
        type: faker.helpers.arrayElement([
          'NO_RESPONSE',
          'FAILED_PRECONDITION',
          'CONFLICT',
          'NOT_FOUND',
          'UNNECESSARY',
          'REQUESTED_CANCELLATION',
        ]),
        detail: 'The chargeable device remained unreachable.',
      },
      null,
    ]),
  };
}

export function getPostSetChargerMaxCurrent200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: faker.lorem.words(),
    targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
    targetState: {
      maxCurrent: 10,
    },
    failureReason: faker.helpers.arrayElement([
      {
        type: faker.helpers.arrayElement([
          'NO_RESPONSE',
          'FAILED_PRECONDITION',
          'CONFLICT',
          'NOT_FOUND',
          'UNNECESSARY',
          'REQUESTED_CANCELLATION',
        ]),
        detail: 'The chargeable device remained unreachable.',
      },
      null,
    ]),
  };
}

export function getGetChargersAction200Response() {
  return faker.helpers.arrayElement([
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
      targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
      kind: faker.helpers.arrayElement(['START', 'STOP']),
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
    },
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
      targetState: {
        maxCurrent: 10,
      },
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
    },
  ]);
}

export function getCancelChargerAction200Response() {
  return faker.helpers.arrayElement([
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
      targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
      kind: faker.helpers.arrayElement(['START', 'STOP']),
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
    },
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
      targetState: {
        maxCurrent: 10,
      },
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
    },
  ]);
}

export function getChargersRefreshHint204Response() {
  return null;
}

export function getChargerCreateSmartOverride200Response() {
  return {
    createdAt: '2020-04-07T17:04:26Z',
    endedAt: '2020-04-07T17:04:26Z',
    targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
    userId: '0bec82e0-0d54-4f2f-83b1-5b248604de0b',
    vendor: faker.helpers.arrayElement(['TESLA', 'ZAPTEC', 'TESLA']),
    targetType: faker.helpers.arrayElement(['charger']),
  };
}

export function getChargerEndSmartOverride200Response() {
  return {
    createdAt: '2020-04-07T17:04:26Z',
    endedAt: '2020-04-07T17:04:26Z',
    targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
    userId: '0bec82e0-0d54-4f2f-83b1-5b248604de0b',
    vendor: faker.helpers.arrayElement(['TESLA', 'ZAPTEC', 'TESLA']),
    targetType: faker.helpers.arrayElement(['charger']),
  };
}

export function getChargerSmartPolicy200Response() {
  return {};
}

export function getUpdateChargerSmartPolicy200Response() {
  return {};
}

export function getChargerSmartChargingStatus200Response() {
  return {
    chargerId: faker.lorem.words(),
    userId: faker.lorem.words(),
    state: faker.helpers.arrayElement([
      'DISABLED',
      'CHARGER_NOT_REACHABLE',
      'VEHICLE_NOT_PLUGGED_IN',
      'CHARGING_PAUSED',
      'CHARGING',
      'AWAITING_PRICES',
    ]),
    chargingIntervals: faker.helpers.arrayElement([
      [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        status: faker.helpers.arrayElement([
          'IN_PROGRESS',
          'COMPLETED',
          'PLANNED',
        ]),
        startTime: faker.lorem.words(),
        endTime: faker.lorem.words(),
      })),
    ]),
  };
}

export function getListHvaCs200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '8f39fa8d-8f10-4984-a319-741dc23848c0',
      userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
      vendor: 'ADAX',
      lastSeen: '2020-04-07T17:04:26.000Z',
      isReachable: true,
      isActive: true,
      currentTemperature: 20.8,
      consumptionRate: 1.8,
      mode: 'HEAT',
      heatSetpoint: 22,
      coolSetpoint: 24,
      holdType: 'PERMANENT',
      information: {
        brand: 'ADAX',
        model: 'Neo Wi-Fi Skirting',
        displayName: 'Bedroom Panel Heater',
        groupName: 'Bedroom',
        category: 'HEATING',
      },
      capabilities: {
        capableModes: ['HEAT', 'COOL', 'OFF'],
        capableHoldTypes: ['PERMANENT'],
        coolSetpointRange: { min: 15, max: 25 },
        heatSetpointRange: { min: 15, max: 25 },
        setpointDifferenceRange: { min: 15, max: 25 },
        setFollowSchedule: { isCapable: true, interventionIds: [] },
        setPermanentHold: { isCapable: true, interventionIds: [] },
      },
      thermostatState: {
        mode: 'HEAT',
        heatSetpoint: 22,
        coolSetpoint: 24,
        holdType: 'PERMANENT',
        lastUpdated: '2020-04-07T17:04:26.000Z',
      },
      temperatureState: {
        currentTemperature: 20.8,
        isActive: true,
        lastUpdated: '2020-04-07T17:03:26.000Z',
      },
      scopes: ['hvac:control:mode', 'hvac:read:data'],
      locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListUserHvaCs200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '8f39fa8d-8f10-4984-a319-741dc23848c0',
      userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
      vendor: 'ADAX',
      lastSeen: '2020-04-07T17:04:26.000Z',
      isReachable: true,
      isActive: true,
      currentTemperature: 20.8,
      consumptionRate: 1.8,
      mode: 'HEAT',
      heatSetpoint: 22,
      coolSetpoint: 24,
      holdType: 'PERMANENT',
      information: {
        brand: 'ADAX',
        model: 'Neo Wi-Fi Skirting',
        displayName: 'Bedroom Panel Heater',
        groupName: 'Bedroom',
        category: 'HEATING',
      },
      capabilities: {
        capableModes: ['HEAT', 'COOL', 'OFF'],
        capableHoldTypes: ['PERMANENT'],
        coolSetpointRange: { min: 15, max: 25 },
        heatSetpointRange: { min: 15, max: 25 },
        setpointDifferenceRange: { min: 15, max: 25 },
        setFollowSchedule: { isCapable: true, interventionIds: [] },
        setPermanentHold: { isCapable: true, interventionIds: [] },
      },
      thermostatState: {
        mode: 'HEAT',
        heatSetpoint: 22,
        coolSetpoint: 24,
        holdType: 'PERMANENT',
        lastUpdated: '2020-04-07T17:04:26.000Z',
      },
      temperatureState: {
        currentTemperature: 20.8,
        isActive: true,
        lastUpdated: '2020-04-07T17:03:26.000Z',
      },
      scopes: ['hvac:control:mode', 'hvac:read:data'],
      locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetHvacsHvacIdSmartPolicy200Response() {
  return {};
}

export function getUpdateHvacSmartPolicy200Response() {
  return {};
}

export function getGetHvacSmartStatus200Response() {
  return {
    hvacId: '8f39fa8d-8f10-4984-a319-741dc23848c0',
    userId: '8f39fa8d-8f10-4984-a319-741dc23848c0',
    intervals: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      from: '10:00',
      to: '11:00',
      heatSetpoint: 20,
    })),
  };
}

export function getUpdateHvac200Response() {
  return {
    id: '8f39fa8d-8f10-4984-a319-741dc23848c0',
    userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
    vendor: 'ADAX',
    lastSeen: '2020-04-07T17:04:26.000Z',
    isReachable: true,
    isActive: true,
    currentTemperature: 20.8,
    consumptionRate: 1.8,
    mode: 'HEAT',
    heatSetpoint: 22,
    coolSetpoint: 24,
    holdType: 'PERMANENT',
    information: {
      brand: 'ADAX',
      model: 'Neo Wi-Fi Skirting',
      displayName: 'Bedroom Panel Heater',
      groupName: 'Bedroom',
      category: 'HEATING',
    },
    capabilities: {
      capableModes: ['HEAT', 'COOL', 'OFF'],
      capableHoldTypes: ['PERMANENT'],
      coolSetpointRange: { min: 15, max: 25 },
      heatSetpointRange: { min: 15, max: 25 },
      setpointDifferenceRange: { min: 15, max: 25 },
      setFollowSchedule: { isCapable: true, interventionIds: [] },
      setPermanentHold: { isCapable: true, interventionIds: [] },
    },
    thermostatState: {
      mode: 'HEAT',
      heatSetpoint: 22,
      coolSetpoint: 24,
      holdType: 'PERMANENT',
      lastUpdated: '2020-04-07T17:04:26.000Z',
    },
    temperatureState: {
      currentTemperature: 20.8,
      isActive: true,
      lastUpdated: '2020-04-07T17:03:26.000Z',
    },
    scopes: ['hvac:control:mode', 'hvac:read:data'],
    locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
  };
}

export function getGetHvac200Response() {
  return {
    id: '8f39fa8d-8f10-4984-a319-741dc23848c0',
    userId: '17d9f847-8a1c-4158-adaa-4911a7acd5f9',
    vendor: 'ADAX',
    lastSeen: '2020-04-07T17:04:26.000Z',
    isReachable: true,
    isActive: true,
    currentTemperature: 20.8,
    consumptionRate: 1.8,
    mode: 'HEAT',
    heatSetpoint: 22,
    coolSetpoint: 24,
    holdType: 'PERMANENT',
    information: {
      brand: 'ADAX',
      model: 'Neo Wi-Fi Skirting',
      displayName: 'Bedroom Panel Heater',
      groupName: 'Bedroom',
      category: 'HEATING',
    },
    capabilities: {
      capableModes: ['HEAT', 'COOL', 'OFF'],
      capableHoldTypes: ['PERMANENT'],
      coolSetpointRange: { min: 15, max: 25 },
      heatSetpointRange: { min: 15, max: 25 },
      setpointDifferenceRange: { min: 15, max: 25 },
      setFollowSchedule: { isCapable: true, interventionIds: [] },
      setPermanentHold: { isCapable: true, interventionIds: [] },
    },
    thermostatState: {
      mode: 'HEAT',
      heatSetpoint: 22,
      coolSetpoint: 24,
      holdType: 'PERMANENT',
      lastUpdated: '2020-04-07T17:04:26.000Z',
    },
    temperatureState: {
      currentTemperature: 20.8,
      isActive: true,
      lastUpdated: '2020-04-07T17:03:26.000Z',
    },
    scopes: ['hvac:control:mode', 'hvac:read:data'],
    locationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
  };
}

export function getGetHvacsAction200Response() {
  return faker.helpers.arrayElement([
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['hvac']),
      target: faker.helpers.arrayElement([
        {
          coolSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['COOL']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          heatSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['HEAT']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          coolSetpoint: faker.number.int(),
          heatSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['AUTO']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          mode: faker.helpers.arrayElement(['OFF']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
      ]),
    },
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['hvac']),
      target: {
        holdType: faker.helpers.arrayElement(['SCHEDULED']),
      },
    },
  ]);
}

export function getCancelHvacAction200Response() {
  return faker.helpers.arrayElement([
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['hvac']),
      target: faker.helpers.arrayElement([
        {
          coolSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['COOL']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          heatSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['HEAT']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          coolSetpoint: faker.number.int(),
          heatSetpoint: faker.number.int(),
          mode: faker.helpers.arrayElement(['AUTO']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
        {
          mode: faker.helpers.arrayElement(['OFF']),
          holdType: faker.helpers.arrayElement(['PERMANENT']),
        },
      ]),
    },
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetType: faker.helpers.arrayElement(['hvac']),
      target: {
        holdType: faker.helpers.arrayElement(['SCHEDULED']),
      },
    },
  ]);
}

export function getPostSetHvacFollowSchedule200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: faker.lorem.words(),
    targetType: faker.helpers.arrayElement(['hvac']),
    target: {
      holdType: faker.helpers.arrayElement(['SCHEDULED']),
    },
  };
}

export function getHvacsRefreshHint204Response() {
  return null;
}

export function getPostSetHvacPermanentHold200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: faker.lorem.words(),
    targetType: faker.helpers.arrayElement(['hvac']),
    target: faker.helpers.arrayElement([
      {
        coolSetpoint: faker.number.int(),
        mode: faker.helpers.arrayElement(['COOL']),
        holdType: faker.helpers.arrayElement(['PERMANENT']),
      },
      {
        heatSetpoint: faker.number.int(),
        mode: faker.helpers.arrayElement(['HEAT']),
        holdType: faker.helpers.arrayElement(['PERMANENT']),
      },
      {
        coolSetpoint: faker.number.int(),
        heatSetpoint: faker.number.int(),
        mode: faker.helpers.arrayElement(['AUTO']),
        holdType: faker.helpers.arrayElement(['PERMANENT']),
      },
      {
        mode: faker.helpers.arrayElement(['OFF']),
        holdType: faker.helpers.arrayElement(['PERMANENT']),
      },
    ]),
  };
}

export function getListInterventions200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: '9d90a9ad-9b24-4ce0-94e9-e888b1b877f4',
    vendor: 'AUDI',
    vendorType: 'vehicle',
    brand: 'Audi',
    introducedAt: '2023-03-16T00:00:00',
    domain: 'Account',
    resolution: {
      title: 'Accept the Audi terms and conditions',
      description:
        "To gain access to your vehicle's telemetry data, it's necessary to accept Audi's terms and conditions. Follow these steps to proceed:<br><br>1. Open the **myAudi app** on your phone<br>2. Follow the prompts to accept Audi's terms and conditions",
      access: 'Remote',
      agent: 'User',
    },
  }));
}

export function getGetIntervention200Response() {
  return {
    id: '9d90a9ad-9b24-4ce0-94e9-e888b1b877f4',
    vendor: 'AUDI',
    vendorType: 'vehicle',
    brand: 'Audi',
    introducedAt: '2023-03-16T00:00:00',
    domain: 'Account',
    resolution: {
      title: 'Accept the Audi terms and conditions',
      description:
        "To gain access to your vehicle's telemetry data, it's necessary to accept Audi's terms and conditions. Follow these steps to proceed:<br><br>1. Open the **myAudi app** on your phone<br>2. Follow the prompts to accept Audi's terms and conditions",
      access: 'Remote',
      agent: 'User',
    },
  };
}

export function getListInverters200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '195d8649-ad12-4755-a9cc-44e2d40ce4d9',
      userId: 'f9d9b134-05f3-452a-80aa-be0deb22f492',
      vendor: 'ENPHASE',
      chargingLocationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      productionState: {
        productionRate: faker.helpers.arrayElement([faker.number.int(), null]),
        isProducing: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
        totalLifetimeProduction: 100152.56,
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      information: {
        id: '7a18eb4e-ee65-4d5b-bb7c-d8e530006b18',
        brand: 'Enphase',
        model: 'Sunny Boy',
        siteName: 'Sunny Plant',
        installationDate: '2020-04-07T17:04:26Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      timezone: 'Europe/Oslo',
      scopes: ['inverter:read:location', 'inverter:read:data'],
      capabilities: {
        productionState: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        productionStatistics: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListUserInverters200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '195d8649-ad12-4755-a9cc-44e2d40ce4d9',
      userId: 'f9d9b134-05f3-452a-80aa-be0deb22f492',
      vendor: 'ENPHASE',
      chargingLocationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      productionState: {
        productionRate: faker.helpers.arrayElement([faker.number.int(), null]),
        isProducing: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
        totalLifetimeProduction: 100152.56,
        lastUpdated: '2020-04-07T17:04:26Z',
      },
      information: {
        id: '7a18eb4e-ee65-4d5b-bb7c-d8e530006b18',
        brand: 'Enphase',
        model: 'Sunny Boy',
        siteName: 'Sunny Plant',
        installationDate: '2020-04-07T17:04:26Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      timezone: 'Europe/Oslo',
      scopes: ['inverter:read:location', 'inverter:read:data'],
      capabilities: {
        productionState: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        productionStatistics: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetInverter200Response() {
  return {
    id: '195d8649-ad12-4755-a9cc-44e2d40ce4d9',
    userId: 'f9d9b134-05f3-452a-80aa-be0deb22f492',
    vendor: 'ENPHASE',
    chargingLocationId: '8d90101b-3f2f-462a-bbb4-1ed320d33bbe',
    lastSeen: '2020-04-07T17:04:26Z',
    isReachable: faker.datatype.boolean(),
    productionState: {
      productionRate: faker.helpers.arrayElement([faker.number.int(), null]),
      isProducing: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
      totalLifetimeProduction: 100152.56,
      lastUpdated: '2020-04-07T17:04:26Z',
    },
    information: {
      id: '7a18eb4e-ee65-4d5b-bb7c-d8e530006b18',
      brand: 'Enphase',
      model: 'Sunny Boy',
      siteName: 'Sunny Plant',
      installationDate: '2020-04-07T17:04:26Z',
    },
    location: {
      longitude: 10.7197486,
      latitude: 59.9173985,
    },
    timezone: 'Europe/Oslo',
    scopes: ['inverter:read:location', 'inverter:read:data'],
    capabilities: {
      productionState: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      productionStatistics: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
    },
  };
}

export function getInvertersRefreshHint204Response() {
  return null;
}

export function getGetLocations200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
      name: 'Enode',
      latitude: 59.9165915,
      longitude: 10.7582268,
      timezoneName: 'Europe/Oslo',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetUserlocations200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
      name: 'Enode',
      latitude: 59.9165915,
      longitude: 10.7582268,
      timezoneName: 'Europe/Oslo',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getCreateLocation200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
    name: 'Enode',
    latitude: 59.9165915,
    longitude: 10.7582268,
    timezoneName: 'Europe/Oslo',
  };
}

export function getGetLocation200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
    name: 'Enode',
    latitude: 59.9165915,
    longitude: 10.7582268,
    timezoneName: 'Europe/Oslo',
  };
}

export function getDeleteLocation200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
    name: 'Enode',
    latitude: 59.9165915,
    longitude: 10.7582268,
    timezoneName: 'Europe/Oslo',
  };
}

export function getUpdateLocation200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: '0fc4b1e7-9bdf-4958-b343-86eff3d9f92f',
    name: 'Enode',
    latitude: 59.9165915,
    longitude: 10.7582268,
    timezoneName: 'Europe/Oslo',
  };
}

export function getGetMeter200Response() {
  return {
    id: faker.string.uuid(),
    userId: faker.lorem.words(),
    vendor: 'TESLA',
    lastSeen: '2020-04-07T17:04:26Z',
    isReachable: faker.datatype.boolean(),
    information: {
      brand: 'Tesla',
      model: 'Tesla Powerwall built-in meter',
      siteName: 'Powerwall Home',
      installationDate: '2020-04-07T17:04:26Z',
    },
    energyState: {
      power: 2.2,
      lastUpdated: '2022-03-01T12:34:56Z',
    },
    location: {
      longitude: 10.7197486,
      latitude: 59.9173985,
    },
    capabilities: {
      measuresConsumption: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      measuresProduction: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
    },
    scopes: ['meter:read:data', 'meter:read:location'],
  };
}

export function getMetersRefreshHint204Response() {
  return null;
}

export function getListUserMeters200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      userId: faker.lorem.words(),
      vendor: 'TESLA',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      information: {
        brand: 'Tesla',
        model: 'Tesla Powerwall built-in meter',
        siteName: 'Powerwall Home',
        installationDate: '2020-04-07T17:04:26Z',
      },
      energyState: {
        power: 2.2,
        lastUpdated: '2022-03-01T12:34:56Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      capabilities: {
        measuresConsumption: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        measuresProduction: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: ['meter:read:data', 'meter:read:location'],
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListMeters200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      userId: faker.lorem.words(),
      vendor: 'TESLA',
      lastSeen: '2020-04-07T17:04:26Z',
      isReachable: faker.datatype.boolean(),
      information: {
        brand: 'Tesla',
        model: 'Tesla Powerwall built-in meter',
        siteName: 'Powerwall Home',
        installationDate: '2020-04-07T17:04:26Z',
      },
      energyState: {
        power: 2.2,
        lastUpdated: '2022-03-01T12:34:56Z',
      },
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
      },
      capabilities: {
        measuresConsumption: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        measuresProduction: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: ['meter:read:data', 'meter:read:location'],
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetSchedules200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        {
          id: faker.lorem.words(),
        },
        {
          id: faker.lorem.words(),
        },
      ])
    ),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getCreateSchedule200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.lorem.words(),
    },
    {
      id: faker.lorem.words(),
    },
  ]);
}

export function getGetSchedule200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.lorem.words(),
    },
    {
      id: faker.lorem.words(),
    },
  ]);
}

export function getUpdateSchedule200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.lorem.words(),
    },
    {
      id: faker.lorem.words(),
    },
  ]);
}

export function getDeleteSchedule204Response() {
  return null;
}

export function getGetScheduleStatus200Response() {
  return faker.helpers.arrayElement([
    {
      scheduleId: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      scheduleType: faker.helpers.arrayElement(['CHARGE']),
      changedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'ALIGNED',
        'MISALIGNED',
        'PENDING',
        'INACTIVE:OVERRIDDEN',
        'INACTIVE:DISABLED',
        'INACTIVE:AWAY',
        'INACTIVE:INCAPABLE',
      ]),
      isCharging: faker.datatype.boolean(),
      isChargingExpected: faker.datatype.boolean(),
      isChargingExpectedParts: {
        needsCharge: faker.datatype.boolean(),
        isPluggedIn: faker.datatype.boolean(),
        shouldCharge: faker.datatype.boolean(),
      },
      upcomingTransitions: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        at: '2020-04-07T17:04:26Z',
        shouldCharge: faker.datatype.boolean(),
      })),
      smartOverride: faker.helpers.arrayElement([
        {
          createdAt: '2020-04-07T17:04:26Z',
          endedAt: '2020-04-07T17:04:26Z',
          targetType: 'vehicle',
          targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
          vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
        },
        null,
      ]),
    },
    {
      scheduleId: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      scheduleType: faker.helpers.arrayElement(['TEMPERATURE']),
      changedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'ALIGNED',
        'MISALIGNED',
        'PENDING',
        'INACTIVE:OVERRIDDEN',
        'INACTIVE:DISABLED',
        'INACTIVE:AWAY',
        'INACTIVE:INCAPABLE',
      ]),
      current: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            coolSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['COOL']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            heatSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['HEAT']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            coolSetpoint: faker.number.int(),
            heatSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['AUTO']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            mode: faker.helpers.arrayElement(['OFF']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
        ]),
        {
          holdType: faker.helpers.arrayElement(['SCHEDULED']),
        },
        null,
      ]),
      expected: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            coolSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['COOL']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            heatSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['HEAT']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            coolSetpoint: faker.number.int(),
            heatSetpoint: faker.number.int(),
            mode: faker.helpers.arrayElement(['AUTO']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
          {
            mode: faker.helpers.arrayElement(['OFF']),
            holdType: faker.helpers.arrayElement(['PERMANENT']),
          },
        ]),
        {
          holdType: faker.helpers.arrayElement(['SCHEDULED']),
        },
      ]),
      upcomingTransitions: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        at: '2020-04-07T17:04:26Z',
        target: faker.helpers.arrayElement([
          faker.helpers.arrayElement([
            {
              coolSetpoint: faker.number.int(),
              mode: faker.helpers.arrayElement(['COOL']),
              holdType: faker.helpers.arrayElement(['PERMANENT']),
            },
            {
              heatSetpoint: faker.number.int(),
              mode: faker.helpers.arrayElement(['HEAT']),
              holdType: faker.helpers.arrayElement(['PERMANENT']),
            },
            {
              coolSetpoint: faker.number.int(),
              heatSetpoint: faker.number.int(),
              mode: faker.helpers.arrayElement(['AUTO']),
              holdType: faker.helpers.arrayElement(['PERMANENT']),
            },
            {
              mode: faker.helpers.arrayElement(['OFF']),
              holdType: faker.helpers.arrayElement(['PERMANENT']),
            },
          ]),
          {
            holdType: faker.helpers.arrayElement(['SCHEDULED']),
          },
        ]),
      })),
    },
  ]);
}

export function getGetChargingStatistics200Response() {
  return [
    {
      kw: { min: 0, max: 78, mean: 61 },
      kwhSum: 120,
      price: { min: 13.8, max: 14.4, mean: 14.1 },
      nonSmartPrice: { min: 12.9, max: 16.7, mean: 14.8 },
      costSum: 3.14,
      estimatedSavings: 1.07,
      date: '2021-01-19T09:37:36.845Z',
    },
  ];
}

export function getGetChargingSessionsStatistics200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    kw: {
      min: faker.number.int(),
      max: faker.number.int(),
      mean: faker.number.int(),
    },
    kwhSum: 120,
    price: {
      min: faker.number.int(),
      max: faker.number.int(),
      mean: faker.number.int(),
    },
    costSum: 3.14,
    id: faker.lorem.words(),
    locationId: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    from: '2020-04-07T17:04:26Z',
    to: '2020-04-07T17:04:26Z',
    nonSmartPrice: {
      min: faker.helpers.arrayElement([faker.number.int(), null]),
      max: faker.helpers.arrayElement([faker.number.int(), null]),
      mean: faker.helpers.arrayElement([faker.number.int(), null]),
    },
    estimatedSavings: 1.07,
  }));
}

export function getGetProductionStatistics200Response() {
  return [
    {
      kw: { min: 0, max: 78, mean: 61 },
      kwhSum: 120,
      price: { min: 13.8, max: 14.4, mean: 14.1 },
      earningsSum: 3.14,
      date: '2021-01-19T09:37:36.845Z',
    },
  ];
}

export function getGetTariffInformation200Response() {
  return [
    { name: 'PEAK', cost: '13.37' },
    { name: 'OFF-PEAK', cost: '12.34' },
  ];
}

export function getSendTariffInformation204Response() {
  return null;
}

export function getAssociateUserLocationWithTariff204Response() {
  return null;
}

export function getGetUserLocationTariff200Response() {
  return [
    {
      weekday: 0,
      fromHourMinute: '00:00',
      toHourMinute: '18:00',
      tariffId: 'FLEX-TARIFF-A',
      tariffName: 'OFF-PEAK',
    },
    {
      weekday: 0,
      fromHourMinute: '18:00',
      toHourMinute: '24:00',
      tariffId: 'FLEX-TARIFF-A',
      tariffName: 'PEAK',
    },
  ];
}

export function getListUsers200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: 'ad84e742-0f46-4cf4-b0db-7d890f8f23f5',
      createdAt: '2020-04-07T17:04:26Z',
      scopes: ['all'],
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetUser200Response() {
  return {
    id: '123456789-ABc',
    linkedVendors: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      vendor: 'TESLA',
      vendorType: 'vehicle',
      isValid: faker.datatype.boolean(),
    })),
  };
}

export function getDeleteUsersUserid204Response() {
  return null;
}

export function getDisconnectUserVendor204Response() {
  return null;
}

export function getDisconnectUserVendorVendorType204Response() {
  return null;
}

export function getPostUsersUseridLink200Response() {
  return {
    linkUrl:
      'https://link.enode.com/YzIwZThhYjYtMjMzMi00ZTAyLTg0OTYtYzdjOTlhZTY3Zjc3QDI2YzI1MDExLTdhYTctNGE2NS1iNjBmLTZmMzc5NmRhODUyMDowNDViYjFiYmE0M2Y5NDU5YTc5OTgxZmEyYTg1NmI4YzhkOGU4YjgyNmNmMzQzZmFmMGNhZTlmNDBjMmZmOTgy',
    linkToken:
      'U2FtcGxlIFNESyB0b2tlbgpTYW1wbGUgU0RLIHRva2VuClNhbXBsZSBTREsgdG9rZW4KU2FtcGxlIFNESyB0b2tlbg==',
  };
}

export function getDeleteUsersUseridAuthorization204Response() {
  return null;
}

export function getGetVehicles200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '05ee9df5-d072-4ee8-b773-399dec8f5693',
      userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
      vendor: 'TESLA',
      lastSeen: '2024-01-07T17:04:26.000Z',
      isReachable: true,
      information: {
        vin: '2HGFB2F5XEH542858',
        brand: 'Tesla',
        model: 'Model S P85',
        year: 2020,
        displayName: 'Batmobile',
      },
      chargeState: {
        batteryLevel: 66,
        range: 228,
        isPluggedIn: true,
        isCharging: true,
        isFullyCharged: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
        batteryCapacity: 48.1,
        chargeLimit: 90,
        chargeRate: 2,
        chargeTimeRemaining: 285,
        lastUpdated: '2020-04-07T17:04:26Z',
        maxCurrent: 16,
        powerDeliveryState: 'PLUGGED_IN:CHARGING',
      },
      smartChargingPolicy: {},
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
        lastUpdated: '2024-01-07T17:04:26.000Z',
      },
      odometer: {
        distance: 65393,
        lastUpdated: '2024-01-07T17:04:26.000Z',
      },
      capabilities: {
        information: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        chargeState: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        location: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        odometer: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        setMaxCurrent: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        startCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        stopCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        smartCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: [
        'vehicle:control:charging',
        'vehicle:read:data',
        'vehicle:read:location',
      ],
      locationId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getListUserVehicles200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '05ee9df5-d072-4ee8-b773-399dec8f5693',
      userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
      vendor: 'TESLA',
      lastSeen: '2024-01-07T17:04:26.000Z',
      isReachable: true,
      information: {
        vin: '2HGFB2F5XEH542858',
        brand: 'Tesla',
        model: 'Model S P85',
        year: 2020,
        displayName: 'Batmobile',
      },
      chargeState: {
        batteryLevel: 66,
        range: 228,
        isPluggedIn: true,
        isCharging: true,
        isFullyCharged: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
        batteryCapacity: 48.1,
        chargeLimit: 90,
        chargeRate: 2,
        chargeTimeRemaining: 285,
        lastUpdated: '2020-04-07T17:04:26Z',
        maxCurrent: 16,
        powerDeliveryState: 'PLUGGED_IN:CHARGING',
      },
      smartChargingPolicy: {},
      location: {
        longitude: 10.7197486,
        latitude: 59.9173985,
        lastUpdated: '2024-01-07T17:04:26.000Z',
      },
      odometer: {
        distance: 65393,
        lastUpdated: '2024-01-07T17:04:26.000Z',
      },
      capabilities: {
        information: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        chargeState: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        location: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        odometer: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        setMaxCurrent: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        startCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        stopCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
        smartCharging: {
          isCapable: faker.datatype.boolean(),
          interventionIds: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
        },
      },
      scopes: [
        'vehicle:control:charging',
        'vehicle:read:data',
        'vehicle:read:location',
      ],
      locationId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetVehicle200Response() {
  return {
    id: '05ee9df5-d072-4ee8-b773-399dec8f5693',
    userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
    vendor: 'TESLA',
    lastSeen: '2024-01-07T17:04:26.000Z',
    isReachable: true,
    information: {
      vin: '2HGFB2F5XEH542858',
      brand: 'Tesla',
      model: 'Model S P85',
      year: 2020,
      displayName: 'Batmobile',
    },
    chargeState: {
      batteryLevel: 66,
      range: 228,
      isPluggedIn: true,
      isCharging: true,
      isFullyCharged: faker.helpers.arrayElement([
        faker.datatype.boolean(),
        null,
      ]),
      batteryCapacity: 48.1,
      chargeLimit: 90,
      chargeRate: 2,
      chargeTimeRemaining: 285,
      lastUpdated: '2020-04-07T17:04:26Z',
      maxCurrent: 16,
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
    },
    smartChargingPolicy: {},
    location: {
      longitude: 10.7197486,
      latitude: 59.9173985,
      lastUpdated: '2024-01-07T17:04:26.000Z',
    },
    odometer: {
      distance: 65393,
      lastUpdated: '2024-01-07T17:04:26.000Z',
    },
    capabilities: {
      information: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      chargeState: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      location: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      odometer: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      setMaxCurrent: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      startCharging: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      stopCharging: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
      smartCharging: {
        isCapable: faker.datatype.boolean(),
        interventionIds: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => '4eaeb363-296d-4ccc-a973-7805e6f400bd'),
      },
    },
    scopes: [
      'vehicle:control:charging',
      'vehicle:read:data',
      'vehicle:read:location',
    ],
    locationId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
  };
}

export function getPostVehiclesVehicleidCharging200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
    targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
    kind: faker.helpers.arrayElement(['START', 'STOP']),
    failureReason: faker.helpers.arrayElement([
      {
        type: faker.helpers.arrayElement([
          'NO_RESPONSE',
          'FAILED_PRECONDITION',
          'CONFLICT',
          'NOT_FOUND',
          'UNNECESSARY',
          'REQUESTED_CANCELLATION',
        ]),
        detail: 'The chargeable device remained unreachable.',
      },
      null,
    ]),
  };
}

export function getPostVehiclesVehicleidMaxCurrent200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: faker.lorem.words(),
    targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
    targetState: {
      maxCurrent: 10,
    },
    failureReason: faker.helpers.arrayElement([
      {
        type: faker.helpers.arrayElement([
          'NO_RESPONSE',
          'FAILED_PRECONDITION',
          'CONFLICT',
          'NOT_FOUND',
          'UNNECESSARY',
          'REQUESTED_CANCELLATION',
        ]),
        detail: 'The chargeable device remained unreachable.',
      },
      null,
    ]),
  };
}

export function getGetVehiclesAction200Response() {
  return faker.helpers.arrayElement([
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
      kind: faker.helpers.arrayElement(['START', 'STOP']),
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
      targetType: faker.helpers.arrayElement(['vehicle']),
    },
    {
      id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
      userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
      createdAt: '2020-04-07T17:04:26Z',
      updatedAt: '2020-04-07T17:04:26Z',
      completedAt: '2020-04-07T17:04:26Z',
      state: faker.helpers.arrayElement([
        'PENDING',
        'CONFIRMED',
        'FAILED',
        'CANCELLED',
      ]),
      targetId: faker.lorem.words(),
      targetState: {
        maxCurrent: 10,
      },
      failureReason: faker.helpers.arrayElement([
        {
          type: faker.helpers.arrayElement([
            'NO_RESPONSE',
            'FAILED_PRECONDITION',
            'CONFLICT',
            'NOT_FOUND',
            'UNNECESSARY',
            'REQUESTED_CANCELLATION',
          ]),
          detail: 'The chargeable device remained unreachable.',
        },
        null,
      ]),
      targetType: faker.helpers.arrayElement(['vehicle']),
    },
  ]);
}

export function getCancelVehicleAction200Response() {
  return {
    id: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
    userId: 'd5bd4771-864e-4ae5-b913-dfb5cdcd5772',
    createdAt: '2020-04-07T17:04:26Z',
    updatedAt: '2020-04-07T17:04:26Z',
    completedAt: '2020-04-07T17:04:26Z',
    state: faker.helpers.arrayElement([
      'PENDING',
      'CONFIRMED',
      'FAILED',
      'CANCELLED',
    ]),
    targetId: 'bfbccded-8a1c-45a8-bbda-dcaeef29977a',
    targetType: faker.helpers.arrayElement(['vehicle', 'charger']),
    kind: faker.helpers.arrayElement(['START', 'STOP']),
    failureReason: faker.helpers.arrayElement([
      {
        type: faker.helpers.arrayElement([
          'NO_RESPONSE',
          'FAILED_PRECONDITION',
          'CONFLICT',
          'NOT_FOUND',
          'UNNECESSARY',
          'REQUESTED_CANCELLATION',
        ]),
        detail: 'The chargeable device remained unreachable.',
      },
      null,
    ]),
  };
}

export function getVehiclesRefreshHint204Response() {
  return null;
}

export function getGetVehiclesVehicleidSmartchargingplans200Response() {
  return {
    id: '53559d39-019a-443b-a2a7-a2ca29f54d4b',
    vehicleId: '4b5ada14-ea87-4ca1-aab9-3c979c34cf4a',
    userId: '3b568b76-e30a-426e-aacd-609db4d7be81',
    locationId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    vendor: 'TESLA',
    currency: 'USD',
    nonSmartCost: 17.2,
    smartCost: 12.8,
    stopAt: '2020-04-07T17:04:26Z',
    startAt: '2020-04-07T17:04:26Z',
    estimatedFinishAt: '2020-04-07T17:04:26Z',
    stopConfirmedAt: '2020-04-07T17:04:26Z',
    startConfirmedAt: '2020-04-07T17:04:26Z',
    endedAt: '2020-04-07T17:04:26Z',
    finalState: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        'PLAN:ENDED:FINISHED',
        'PLAN:ENDED:UNPLUGGED',
        'PLAN:ENDED:FAILED',
        'PLAN:ENDED:DISABLED',
        'PLAN:ENDED:DEADLINE_CHANGED',
      ]),
      faker.helpers.arrayElement([
        'PLAN:ENDED:FINISHED',
        'PLAN:ENDED:UNPLUGGED',
        'PLAN:ENDED:FAILED',
        'PLAN:ENDED:DISABLED',
        'PLAN:ENDED:DEADLINE_CHANGED',
      ]),
    ]),
    failureCondition: 'CHARGE_INTERRUPTED',
    externalStart: faker.helpers.arrayElement([
      {
        createdAt: '2020-04-07T17:04:26Z',
        endedAt: '2020-04-07T17:04:26Z',
        targetType: 'vehicle',
        targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
        vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
      },
      null,
    ]),
    chargingLocationId: '4eaeb363-296d-4ccc-a973-7805e6f400bd',
  };
}

export function getGetVehiclesVehicleidSmartchargingpolicy200Response() {
  return {};
}

export function getUpdateVehicleSmartChargingPolicy200Response() {
  return {};
}

export function getVehicleCreateSmartOverride200Response() {
  return {
    createdAt: '2020-04-07T17:04:26Z',
    endedAt: '2020-04-07T17:04:26Z',
    targetType: 'vehicle',
    targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
    userId: '0bec82e0-0d54-4f2f-83b1-5b248604de0b',
    vendor: faker.helpers.arrayElement(['TESLA', 'ZAPTEC', 'TESLA']),
  };
}

export function getVehicleEndSmartOverride200Response() {
  return {
    createdAt: '2020-04-07T17:04:26Z',
    endedAt: '2020-04-07T17:04:26Z',
    targetType: 'vehicle',
    targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
    vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
    userId: '0bec82e0-0d54-4f2f-83b1-5b248604de0b',
    vendor: faker.helpers.arrayElement(['TESLA', 'ZAPTEC', 'TESLA']),
  };
}

export function getGetVehiclesVehicleidSmartchargingstatus200Response() {
  return {
    updatedAt: '2020-04-07T17:04:26Z',
    vehicleId: '4b5ada14-ea87-4ca1-aab9-3c979c34cf4a',
    userId: '3b568b76-e30a-426e-aacd-609db4d7be81',
    vendor: 'TESLA',
    state: 'PLAN:EXECUTING:STARTING',
    stateChangedAt: '2020-04-07T17:04:26Z',
    consideration: faker.helpers.arrayElement([
      {
        isPluggedIn: faker.datatype.boolean(),
        isCharging: faker.datatype.boolean(),
        atChargingLocation: faker.datatype.boolean(),
        hasTimeEstimate: faker.datatype.boolean(),
      },
      null,
    ]),
    smartOverride: faker.helpers.arrayElement([
      {
        createdAt: '2020-04-07T17:04:26Z',
        endedAt: '2020-04-07T17:04:26Z',
        targetType: 'vehicle',
        targetId: '07f8368d-be7e-4dbd-8cf0-94d00dd67ad3',
        vendorActionId: '213ae0a8-fb65-40be-981a-6a86df3e1c7f',
      },
      null,
    ]),
  };
}

export function getPutWebhooksFirehose204Response() {
  return null;
}

export function getDeleteWebhooksFirehose204Response() {
  return null;
}

export function getTestFirehose200Response() {
  return {
    status: faker.helpers.arrayElement(['SUCCESS', 'FAILURE']),
    description: faker.lorem.words(),
    response: faker.helpers.arrayElement([
      {
        code: 200,
        body: '{}',
        headers: ['content-type: application/json; } charset=utf-8'],
      },
      null,
    ]),
  };
}

export function getCreateWebhook200Response() {
  return {
    id: '1de1as-dsa12wed-15sa',
    url: 'https://example.com/enode-webhooks/firehose',
    events: [
      'user:vehicle:discovered',
      'user:vehicle:updated',
      'user:vehicle:deleted',
    ],
    lastSuccess: '14-08-2023T13:01:00.000Z',
    isActive: true,
    createdAt: '14-08-2023T13:01:00.000Z',
    apiVersion: '2023-08-01',
    authentication: faker.helpers.arrayElement([
      {
        headerName: faker.person.fullName(),
      },
      null,
    ]),
  };
}

export function getListWebhooks200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: '1de1as-dsa12wed-15sa',
      url: 'https://example.com/enode-webhooks/firehose',
      events: [
        'user:vehicle:discovered',
        'user:vehicle:updated',
        'user:vehicle:deleted',
      ],
      lastSuccess: '14-08-2023T13:01:00.000Z',
      isActive: true,
      createdAt: '14-08-2023T13:01:00.000Z',
      apiVersion: '2023-08-01',
      authentication: faker.helpers.arrayElement([
        {
          headerName: faker.person.fullName(),
        },
        null,
      ]),
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getUpdateWebhook200Response() {
  return {
    id: '1de1as-dsa12wed-15sa',
    url: 'https://example.com/enode-webhooks/firehose',
    events: [
      'user:vehicle:discovered',
      'user:vehicle:updated',
      'user:vehicle:deleted',
    ],
    lastSuccess: '14-08-2023T13:01:00.000Z',
    isActive: true,
    createdAt: '14-08-2023T13:01:00.000Z',
    apiVersion: '2023-08-01',
    authentication: faker.helpers.arrayElement([
      {
        headerName: faker.person.fullName(),
      },
      null,
    ]),
  };
}

export function getGetWebhook200Response() {
  return {
    id: '1de1as-dsa12wed-15sa',
    url: 'https://example.com/enode-webhooks/firehose',
    events: [
      'user:vehicle:discovered',
      'user:vehicle:updated',
      'user:vehicle:deleted',
    ],
    lastSuccess: '14-08-2023T13:01:00.000Z',
    isActive: true,
    createdAt: '14-08-2023T13:01:00.000Z',
    apiVersion: '2023-08-01',
    authentication: faker.helpers.arrayElement([
      {
        headerName: faker.person.fullName(),
      },
      null,
    ]),
  };
}

export function getDeleteWebhook204Response() {
  return null;
}

export function getTestWebhook200Response() {
  return {
    status: faker.helpers.arrayElement(['SUCCESS', 'FAILURE']),
    description: faker.lorem.words(),
    response: faker.helpers.arrayElement([
      {
        code: 200,
        body: '{}',
        headers: ['content-type: application/json; } charset=utf-8'],
      },
      null,
    ]),
  };
}

export function getGetHealthChargerVendors200Response() {
  return [
    {
      vendor: 'EASEE',
      displayName: 'Easee',
      portalName: 'Easee',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'WALLBOX',
      displayName: 'Wallbox',
      portalName: 'Wallbox',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'ZAPTEC',
      displayName: 'Zaptec',
      portalName: 'Zaptec',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'EO',
      displayName: 'EO',
      portalName: 'EO',
      status: 'READY',
      linkingStatus: 'READY',
    },
  ];
}

export function getGetHealthVehicleVendors200Response() {
  return [
    {
      vendor: 'TESLA',
      displayName: 'Tesla',
      portalName: 'Tesla',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'BMW',
      displayName: 'BMW',
      portalName: 'My BMW',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'AUDI',
      displayName: 'Audi',
      portalName: 'myAudi',
      status: 'READY',
      linkingStatus: 'READY',
    },
  ];
}

export function getGetHealthInverterVendors200Response() {
  return [
    {
      vendor: 'SOLAREDGE',
      displayName: 'SolarEdge',
      portalName: 'Solar Edge',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'SMA',
      displayName: 'SMA',
      portalName: 'SMA Energy',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'SOLIS',
      displayName: 'Solis',
      portalName: 'Solis',
      status: 'READY',
      linkingStatus: 'READY',
    },
    {
      vendor: 'FRONIUS',
      displayName: 'Fronius',
      status: 'READY',
      linkingStatus: 'READY',
      portalName: 'Fronius',
    },
  ];
}

export function getGetHealthHvacVendors200Response() {
  return [
    {
      vendor: 'MILL',
      displayName: 'Mill',
      portalName: 'Mill',
      status: 'READY',
      linkingStatus: 'READY',
    },
  ];
}

export function getGetHealthMeterVendors200Response() {
  return [
    {
      vendor: 'TESLA',
      displayName: 'Tesla',
      portalName: 'Tesla',
      status: 'READY',
      linkingStatus: 'READY',
    },
  ];
}

export function getGetHealthReady204Response() {
  return null;
}

export function getCreateSimulatedVehicle200Response() {
  return {
    id: faker.lorem.words(),
    name: 'Test vehicle 1',
    username: '<EMAIL>',
    password: 'password',
    vin: '5YJ3F7EA1LF700671',
    vendor: 'TESLA',
    model: 'Model S P85',
    year: 2020,
    isReachable: true,
    chargeState: {
      batteryLevel: faker.helpers.arrayElement([faker.number.int(), null]),
      range: faker.helpers.arrayElement([faker.number.int(), null]),
      batteryCapacity: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeLimit: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeRate: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeTimeRemaining: faker.helpers.arrayElement([
        faker.number.int(),
        null,
      ]),
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
      isPluggedIn: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
      isCharging: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
    },
    location: {
      longitude: faker.helpers.arrayElement([faker.number.int(), null]),
      latitude: faker.helpers.arrayElement([faker.number.int(), null]),
    },
    userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
    linkedVehicleId: '05ee9df5-d072-4ee8-b773-399dec8f5693',
  };
}

export function getListSimulatedVehicle200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.lorem.words(),
      name: 'Test vehicle 1',
      username: '<EMAIL>',
      password: 'password',
      vin: '5YJ3F7EA1LF700671',
      vendor: 'TESLA',
      model: 'Model S P85',
      year: 2020,
      isReachable: true,
      chargeState: {
        batteryLevel: faker.helpers.arrayElement([faker.number.int(), null]),
        range: faker.helpers.arrayElement([faker.number.int(), null]),
        batteryCapacity: faker.helpers.arrayElement([faker.number.int(), null]),
        chargeLimit: faker.helpers.arrayElement([faker.number.int(), null]),
        chargeRate: faker.helpers.arrayElement([faker.number.int(), null]),
        chargeTimeRemaining: faker.helpers.arrayElement([
          faker.number.int(),
          null,
        ]),
        powerDeliveryState: 'PLUGGED_IN:CHARGING',
        isPluggedIn: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
        isCharging: faker.helpers.arrayElement([
          faker.datatype.boolean(),
          null,
        ]),
      },
      location: {
        longitude: faker.helpers.arrayElement([faker.number.int(), null]),
        latitude: faker.helpers.arrayElement([faker.number.int(), null]),
      },
      userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
      linkedVehicleId: '05ee9df5-d072-4ee8-b773-399dec8f5693',
    })),
    pagination: {
      after: 'MjAyMy0wNy0xOFQxMDowODowMi4zNzNa',
      before: 'MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa',
    },
  };
}

export function getGetSimulatedVehicle200Response() {
  return {
    id: faker.lorem.words(),
    name: 'Test vehicle 1',
    username: '<EMAIL>',
    password: 'password',
    vin: '5YJ3F7EA1LF700671',
    vendor: 'TESLA',
    model: 'Model S P85',
    year: 2020,
    isReachable: true,
    chargeState: {
      batteryLevel: faker.helpers.arrayElement([faker.number.int(), null]),
      range: faker.helpers.arrayElement([faker.number.int(), null]),
      batteryCapacity: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeLimit: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeRate: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeTimeRemaining: faker.helpers.arrayElement([
        faker.number.int(),
        null,
      ]),
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
      isPluggedIn: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
      isCharging: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
    },
    location: {
      longitude: faker.helpers.arrayElement([faker.number.int(), null]),
      latitude: faker.helpers.arrayElement([faker.number.int(), null]),
    },
    userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
    linkedVehicleId: '05ee9df5-d072-4ee8-b773-399dec8f5693',
  };
}

export function getUpdateSimulatedVehicle200Response() {
  return {
    id: faker.lorem.words(),
    name: 'Test vehicle 1',
    username: '<EMAIL>',
    password: 'password',
    vin: '5YJ3F7EA1LF700671',
    vendor: 'TESLA',
    model: 'Model S P85',
    year: 2020,
    isReachable: true,
    chargeState: {
      batteryLevel: faker.helpers.arrayElement([faker.number.int(), null]),
      range: faker.helpers.arrayElement([faker.number.int(), null]),
      batteryCapacity: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeLimit: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeRate: faker.helpers.arrayElement([faker.number.int(), null]),
      chargeTimeRemaining: faker.helpers.arrayElement([
        faker.number.int(),
        null,
      ]),
      powerDeliveryState: 'PLUGGED_IN:CHARGING',
      isPluggedIn: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
      isCharging: faker.helpers.arrayElement([faker.datatype.boolean(), null]),
    },
    location: {
      longitude: faker.helpers.arrayElement([faker.number.int(), null]),
      latitude: faker.helpers.arrayElement([faker.number.int(), null]),
    },
    userId: 'bb28471e-cde6-4eff-ace4-9a7f4f50882a',
    linkedVehicleId: '05ee9df5-d072-4ee8-b773-399dec8f5693',
  };
}
