{"name": "shared-axios-assets-configuration-api-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/assets-configuration-api-client/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i openapi.yml -o src", "rm openapitools.json", "npx prettier . --write"]}}, "regenerate-client": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Fetch the latest OpenAPI spec from the asset-service repository", "command": "gh api -H \"Accept: application/vnd.github.v3.raw\" /repos/Pod-Point/asset-service/contents/docs/api/current/openapi_configuration_api.yml > libs/shared/axios/assets-configuration-api-client/openapi.yml"}, {"description": "Generate the client from the OpenAPI spec", "command": "nx run shared-axios-assets-configuration-api-client:generate-sources"}], "parallel": false}}}, "tags": ["shared"]}