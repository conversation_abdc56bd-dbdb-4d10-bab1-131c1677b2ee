{"name": "shared-axios-competitions-service-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/competitions-service-client/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i openapi.json -o src", "npx prettier . --write"]}}}, "tags": ["shared"]}