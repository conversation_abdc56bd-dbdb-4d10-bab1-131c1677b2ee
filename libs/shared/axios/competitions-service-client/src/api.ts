/* tslint:disable */
/* eslint-disable */
/**
 * Competitions Api
 * API for managing competitions api
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface Address
 */
export interface Address {
  /**
   * The ID of the address
   * @type {string}
   * @memberof Address
   */
  id: string;
  /**
   * The street address
   * @type {string}
   * @memberof Address
   */
  streetAddress?: string;
  /**
   * The city
   * @type {string}
   * @memberof Address
   */
  city?: string;
  /**
   * The postcode
   * @type {string}
   * @memberof Address
   */
  postcode?: string;
}
/**
 *
 * @export
 * @interface AddressRequest
 */
export interface AddressRequest {
  /**
   * The street address
   * @type {string}
   * @memberof AddressRequest
   */
  streetAddress?: string;
  /**
   * The city
   * @type {string}
   * @memberof AddressRequest
   */
  city?: string;
  /**
   * The postcode
   * @type {string}
   * @memberof AddressRequest
   */
  postcode?: string;
}
/**
 *
 * @export
 * @interface ChargingStation
 */
export interface ChargingStation {
  /**
   * The ID of the charging station
   * @type {string}
   * @memberof ChargingStation
   */
  id: string;
  /**
   * The unit ppid
   * @type {string}
   * @memberof ChargingStation
   */
  ppid: string;
  /**
   * The supply mpan
   * @type {string}
   * @memberof ChargingStation
   */
  mpan?: string | null;
  /**
   * The address of the charging station
   * @type {Address}
   * @memberof ChargingStation
   */
  address?: Address | null;
}
/**
 *
 * @export
 * @interface ChargingStationProgramme
 */
export interface ChargingStationProgramme {
  /**
   *
   * @type {ChargingStationProgrammeProgramme}
   * @memberof ChargingStationProgramme
   */
  programme: ChargingStationProgrammeProgramme;
  /**
   * Date and time the charging station was enrolled in the programme
   * @type {string}
   * @memberof ChargingStationProgramme
   */
  enrolledAt: string;
  /**
   * If not null, this is the date and time the charging station was unenrolled from the programme
   * @type {string}
   * @memberof ChargingStationProgramme
   */
  unenrolledAt: string | null;
}
/**
 * Programme id and name
 * @export
 * @interface ChargingStationProgrammeProgramme
 */
export interface ChargingStationProgrammeProgramme {
  /**
   *
   * @type {string}
   * @memberof ChargingStationProgrammeProgramme
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ChargingStationProgrammeProgramme
   */
  name?: string;
}
/**
 *
 * @export
 * @interface ChargingStationProgrammesResponse
 */
export interface ChargingStationProgrammesResponse {
  /**
   * The programmes associated with the charging station
   * @type {Array<ChargingStationProgramme>}
   * @memberof ChargingStationProgrammesResponse
   */
  programmes: Array<ChargingStationProgramme>;
}
/**
 *
 * @export
 * @interface CompetitionProviderResponse
 */
export interface CompetitionProviderResponse {
  /**
   * The ID of the competition provider
   * @type {string}
   * @memberof CompetitionProviderResponse
   */
  id: string;
  /**
   * The name of the competition provider
   * @type {string}
   * @memberof CompetitionProviderResponse
   */
  name: string;
}
/**
 *
 * @export
 * @interface CreateChargingStationRequest
 */
export interface CreateChargingStationRequest {
  /**
   * The unit ppid
   * @type {string}
   * @memberof CreateChargingStationRequest
   */
  ppid: string;
  /**
   * The supply mpan
   * @type {string}
   * @memberof CreateChargingStationRequest
   */
  mpan?: string;
}
/**
 *
 * @export
 * @interface CreateCustomerRequest
 */
export interface CreateCustomerRequest {
  /**
   * Customer email
   * @type {string}
   * @memberof CreateCustomerRequest
   */
  email: string;
  /**
   * Customer first name
   * @type {string}
   * @memberof CreateCustomerRequest
   */
  firstName?: string;
  /**
   * Customer last name
   * @type {string}
   * @memberof CreateCustomerRequest
   */
  lastName?: string;
  /**
   * Customer address
   * @type {AddressRequest}
   * @memberof CreateCustomerRequest
   */
  address?: AddressRequest;
  /**
   * Customer charging station
   * @type {CreateChargingStationRequest}
   * @memberof CreateCustomerRequest
   */
  chargingStation: CreateChargingStationRequest;
}
/**
 *
 * @export
 * @interface CreateProgrammeRequestDto
 */
export interface CreateProgrammeRequestDto {
  /**
   *
   * @type {string}
   * @memberof CreateProgrammeRequestDto
   */
  name: string;
  /**
   *
   * @type {boolean}
   * @memberof CreateProgrammeRequestDto
   */
  isActive: boolean;
  /**
   *
   * @type {string}
   * @memberof CreateProgrammeRequestDto
   */
  gspGroup?: string;
  /**
   *
   * @type {string}
   * @memberof CreateProgrammeRequestDto
   */
  poolApiAssetId?: string;
}
/**
 *
 * @export
 * @interface Customer
 */
export interface Customer {
  /**
   * The ID of the customer
   * @type {string}
   * @memberof Customer
   */
  id: string;
  /**
   * Customer email
   * @type {string}
   * @memberof Customer
   */
  email: string;
  /**
   * Customer first name
   * @type {string}
   * @memberof Customer
   */
  firstName?: string | null;
  /**
   * Customer last name
   * @type {string}
   * @memberof Customer
   */
  lastName?: string | null;
  /**
   * Customer charging stations
   * @type {Array<ChargingStation>}
   * @memberof Customer
   */
  chargingStations?: Array<ChargingStation>;
}
/**
 *
 * @export
 * @interface EnrolmentStatusResponse
 */
export interface EnrolmentStatusResponse {
  /**
   * The unit ppid
   * @type {string}
   * @memberof EnrolmentStatusResponse
   */
  ppid: string;
  /**
   *
   * @type {EnrolmentStatusResponseEnrolmentStatus}
   * @memberof EnrolmentStatusResponse
   */
  enrolmentStatus: EnrolmentStatusResponseEnrolmentStatus;
}
/**
 * The units enrolment status
 * @export
 * @interface EnrolmentStatusResponseEnrolmentStatus
 */
export interface EnrolmentStatusResponseEnrolmentStatus {
  /**
   * The enrolment status state
   * @type {boolean}
   * @memberof EnrolmentStatusResponseEnrolmentStatus
   */
  isEnrolled?: boolean;
  /**
   * The opt out url
   * @type {string}
   * @memberof EnrolmentStatusResponseEnrolmentStatus
   */
  optOutUrl?: string;
}
/**
 *
 * @export
 * @interface GetChargingStationEnrolmentStatus404Response
 */
export interface GetChargingStationEnrolmentStatus404Response {
  /**
   *
   * @type {string}
   * @memberof GetChargingStationEnrolmentStatus404Response
   */
  error?: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface Invitation
 */
export interface Invitation {
  /**
   * The ID of the invitation
   * @type {string}
   * @memberof Invitation
   */
  token: string;
  /**
   * The customer who is invited
   * @type {Customer}
   * @memberof Invitation
   */
  customer?: Customer;
  /**
   * The charging station
   * @type {ChargingStation}
   * @memberof Invitation
   */
  chargingStation?: ChargingStation;
  /**
   * When the invitation expires
   * @type {string}
   * @memberof Invitation
   */
  expiresAt: string;
  /**
   * Connection to a provider
   * @type {ProviderConnection}
   * @memberof Invitation
   */
  providerConnection?: ProviderConnection;
}
/**
 *
 * @export
 * @interface InvitationRequest
 */
export interface InvitationRequest {
  /**
   * The ID of the customer who is invited
   * @type {string}
   * @memberof InvitationRequest
   */
  customerId: string;
  /**
   * The ID of the charging station
   * @type {string}
   * @memberof InvitationRequest
   */
  chargingStationId: string;
  /**
   * The ID of the programme to be enrolled in
   * @type {string}
   * @memberof InvitationRequest
   */
  programmeId?: string;
  /**
   * The name of the provider
   * @type {string}
   * @memberof InvitationRequest
   */
  providerName?: string;
}
/**
 *
 * @export
 * @interface LinkChargingStationRequest
 */
export interface LinkChargingStationRequest {
  /**
   * The id used by this flex provider to identify the charging station
   * @type {string}
   * @memberof LinkChargingStationRequest
   */
  externalChargingStationId?: string;
  /**
   * The PPID of the charging station
   * @type {string}
   * @memberof LinkChargingStationRequest
   */
  ppid: string;
}
/**
 *
 * @export
 * @interface LinkChargingStationResponse
 */
export interface LinkChargingStationResponse {
  /**
   * The id used by this flex provider to identify the charging station
   * @type {string}
   * @memberof LinkChargingStationResponse
   */
  externalChargingStationId: string;
  /**
   * The name of the competition provider
   * @type {string}
   * @memberof LinkChargingStationResponse
   */
  providerName: string;
  /**
   * The charging station that was linked
   * @type {LinkedChargingStationResponse}
   * @memberof LinkChargingStationResponse
   */
  chargingStation: LinkedChargingStationResponse;
}
/**
 *
 * @export
 * @interface LinkedChargingStationResponse
 */
export interface LinkedChargingStationResponse {
  /**
   * The internal id of this charging station
   * @type {string}
   * @memberof LinkedChargingStationResponse
   */
  id: string;
  /**
   * The PPID of the charging station
   * @type {string}
   * @memberof LinkedChargingStationResponse
   */
  ppid: string;
  /**
   * The MPAN of the charging station (if known)
   * @type {string}
   * @memberof LinkedChargingStationResponse
   */
  mpan: string;
}
/**
 *
 * @export
 * @interface ProgrammeChargingStationsResponse
 */
export interface ProgrammeChargingStationsResponse {
  /**
   *
   * @type {string}
   * @memberof ProgrammeChargingStationsResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof ProgrammeChargingStationsResponse
   */
  name: string;
  /**
   *
   * @type {boolean}
   * @memberof ProgrammeChargingStationsResponse
   */
  isActive: boolean;
  /**
   *
   * @type {string}
   * @memberof ProgrammeChargingStationsResponse
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof ProgrammeChargingStationsResponse
   */
  updatedAt: string;
  /**
   *
   * @type {Array<string>}
   * @memberof ProgrammeChargingStationsResponse
   */
  chargingStations: Array<string>;
}
/**
 *
 * @export
 * @interface ProgrammeResponse
 */
export interface ProgrammeResponse {
  /**
   *
   * @type {string}
   * @memberof ProgrammeResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof ProgrammeResponse
   */
  name: string;
  /**
   *
   * @type {boolean}
   * @memberof ProgrammeResponse
   */
  isActive: boolean;
  /**
   *
   * @type {string}
   * @memberof ProgrammeResponse
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof ProgrammeResponse
   */
  gspGroup?: string;
  /**
   *
   * @type {string}
   * @memberof ProgrammeResponse
   */
  poolApiAssetId?: string;
}
/**
 *
 * @export
 * @interface ProviderChargingStationByPPIDResponse
 */
export interface ProviderChargingStationByPPIDResponse {
  /**
   * The ID of the provider charging station
   * @type {string}
   * @memberof ProviderChargingStationByPPIDResponse
   */
  id: string;
  /**
   * The PPID of the provider charging station
   * @type {string}
   * @memberof ProviderChargingStationByPPIDResponse
   */
  ppid: string;
  /**
   * The provider\'s ID of the charging station
   * @type {string}
   * @memberof ProviderChargingStationByPPIDResponse
   */
  externalChargingStationId?: string | null;
}
/**
 *
 * @export
 * @interface ProviderConnection
 */
export interface ProviderConnection {
  /**
   * The name of the provider
   * @type {string}
   * @memberof ProviderConnection
   */
  providerName?: string;
  /**
   * The external ID of the charging station
   * @type {string}
   * @memberof ProviderConnection
   */
  externalChargingStationId?: string;
}
/**
 *
 * @export
 * @interface SetProgrammeChargingStationsRequestDto
 */
export interface SetProgrammeChargingStationsRequestDto {
  /**
   *
   * @type {Array<string>}
   * @memberof SetProgrammeChargingStationsRequestDto
   */
  ppids: Array<string>;
}
/**
 *
 * @export
 * @interface UpdateProgrammeRequestDto
 */
export interface UpdateProgrammeRequestDto {
  /**
   *
   * @type {string}
   * @memberof UpdateProgrammeRequestDto
   */
  name?: string;
  /**
   *
   * @type {boolean}
   * @memberof UpdateProgrammeRequestDto
   */
  isActive?: boolean;
  /**
   *
   * @type {string}
   * @memberof UpdateProgrammeRequestDto
   */
  gspGroup?: string;
  /**
   *
   * @type {string}
   * @memberof UpdateProgrammeRequestDto
   */
  poolApiAssetId?: string;
}

/**
 * ChargingStationsApi - axios parameter creator
 * @export
 */
export const ChargingStationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Gets the programmes associated with the specified charging station.
     * @summary Gets the programmes associated with the specified charging station.
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includeUnenrolled] Whether to include programmes that the charging station has left.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationProgrammes: async (
      ppid: string,
      includeUnenrolled?: boolean,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationProgrammes', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/programmes`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includeUnenrolled !== undefined) {
        localVarQueryParameter['includeUnenrolled'] = includeUnenrolled;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Removes the charging station from the specified programme.
     * @summary Removes the charging station from the specified programme.
     * @param {string} ppid The PPID of the charging station
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFromProgramme: async (
      ppid: string,
      programmeId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('removeFromProgramme', 'ppid', ppid);
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists('removeFromProgramme', 'programmeId', programmeId);
      const localVarPath = `/charging-stations/{ppid}/programmes/{programmeId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'programmeId'}}`, encodeURIComponent(String(programmeId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingStationsApi - functional programming interface
 * @export
 */
export const ChargingStationsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargingStationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Gets the programmes associated with the specified charging station.
     * @summary Gets the programmes associated with the specified charging station.
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includeUnenrolled] Whether to include programmes that the charging station has left.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationProgrammes(
      ppid: string,
      includeUnenrolled?: boolean,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargingStationProgrammesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationProgrammes(
          ppid,
          includeUnenrolled,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStationsApi.getChargingStationProgrammes'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Removes the charging station from the specified programme.
     * @summary Removes the charging station from the specified programme.
     * @param {string} ppid The PPID of the charging station
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async removeFromProgramme(
      ppid: string,
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.removeFromProgramme(
          ppid,
          programmeId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargingStationsApi.removeFromProgramme']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingStationsApi - factory interface
 * @export
 */
export const ChargingStationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingStationsApiFp(configuration);
  return {
    /**
     * Gets the programmes associated with the specified charging station.
     * @summary Gets the programmes associated with the specified charging station.
     * @param {string} ppid The PPID of the charging station
     * @param {boolean} [includeUnenrolled] Whether to include programmes that the charging station has left.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationProgrammes(
      ppid: string,
      includeUnenrolled?: boolean,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargingStationProgrammesResponse> {
      return localVarFp
        .getChargingStationProgrammes(ppid, includeUnenrolled, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Removes the charging station from the specified programme.
     * @summary Removes the charging station from the specified programme.
     * @param {string} ppid The PPID of the charging station
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    removeFromProgramme(
      ppid: string,
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .removeFromProgramme(ppid, programmeId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingStationsApi - object-oriented interface
 * @export
 * @class ChargingStationsApi
 * @extends {BaseAPI}
 */
export class ChargingStationsApi extends BaseAPI {
  /**
   * Gets the programmes associated with the specified charging station.
   * @summary Gets the programmes associated with the specified charging station.
   * @param {string} ppid The PPID of the charging station
   * @param {boolean} [includeUnenrolled] Whether to include programmes that the charging station has left.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsApi
   */
  public getChargingStationProgrammes(
    ppid: string,
    includeUnenrolled?: boolean,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsApiFp(this.configuration)
      .getChargingStationProgrammes(ppid, includeUnenrolled, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Removes the charging station from the specified programme.
   * @summary Removes the charging station from the specified programme.
   * @param {string} ppid The PPID of the charging station
   * @param {string} programmeId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStationsApi
   */
  public removeFromProgramme(
    ppid: string,
    programmeId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStationsApiFp(this.configuration)
      .removeFromProgramme(ppid, programmeId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CustomersApi - axios parameter creator
 * @export
 */
export const CustomersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a new customer
     * @summary Create a new customer
     * @param {CreateCustomerRequest} createCustomerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createCustomer: async (
      createCustomerRequest: CreateCustomerRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createCustomerRequest' is not null or undefined
      assertParamExists(
        'createCustomer',
        'createCustomerRequest',
        createCustomerRequest
      );
      const localVarPath = `/customers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createCustomerRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get customer by id
     * @summary Get customer by id
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCustomerById: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('getCustomerById', 'id', id);
      const localVarPath = `/customers/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CustomersApi - functional programming interface
 * @export
 */
export const CustomersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CustomersApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a new customer
     * @summary Create a new customer
     * @param {CreateCustomerRequest} createCustomerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createCustomer(
      createCustomerRequest: CreateCustomerRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Customer>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.createCustomer(
        createCustomerRequest,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CustomersApi.createCustomer']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get customer by id
     * @summary Get customer by id
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getCustomerById(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Customer>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getCustomerById(
        id,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CustomersApi.getCustomerById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CustomersApi - factory interface
 * @export
 */
export const CustomersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CustomersApiFp(configuration);
  return {
    /**
     * Create a new customer
     * @summary Create a new customer
     * @param {CreateCustomerRequest} createCustomerRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createCustomer(
      createCustomerRequest: CreateCustomerRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Customer> {
      return localVarFp
        .createCustomer(createCustomerRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get customer by id
     * @summary Get customer by id
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCustomerById(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Customer> {
      return localVarFp
        .getCustomerById(id, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CustomersApi - object-oriented interface
 * @export
 * @class CustomersApi
 * @extends {BaseAPI}
 */
export class CustomersApi extends BaseAPI {
  /**
   * Create a new customer
   * @summary Create a new customer
   * @param {CreateCustomerRequest} createCustomerRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CustomersApi
   */
  public createCustomer(
    createCustomerRequest: CreateCustomerRequest,
    options?: RawAxiosRequestConfig
  ) {
    return CustomersApiFp(this.configuration)
      .createCustomer(createCustomerRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get customer by id
   * @summary Get customer by id
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CustomersApi
   */
  public getCustomerById(id: string, options?: RawAxiosRequestConfig) {
    return CustomersApiFp(this.configuration)
      .getCustomerById(id, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DefaultApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DefaultApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return DefaultApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * EnrolmentApi - axios parameter creator
 * @export
 */
export const EnrolmentApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get the enrolment status of a charging station
     * @summary Get charging station enrolment status
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnrolmentStatus: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getChargingStationEnrolmentStatus', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/enrolment/status`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Remove the charging station from the flex programme
     * @summary Unenrol charging station from flex
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unenrolChargingStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('unenrolChargingStation', 'ppid', ppid);
      const localVarPath = `/charging-stations/{ppid}/enrolment`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * EnrolmentApi - functional programming interface
 * @export
 */
export const EnrolmentApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    EnrolmentApiAxiosParamCreator(configuration);
  return {
    /**
     * Get the enrolment status of a charging station
     * @summary Get charging station enrolment status
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationEnrolmentStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<EnrolmentStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationEnrolmentStatus(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['EnrolmentApi.getChargingStationEnrolmentStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Remove the charging station from the flex programme
     * @summary Unenrol charging station from flex
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async unenrolChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.unenrolChargingStation(ppid, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['EnrolmentApi.unenrolChargingStation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * EnrolmentApi - factory interface
 * @export
 */
export const EnrolmentApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = EnrolmentApiFp(configuration);
  return {
    /**
     * Get the enrolment status of a charging station
     * @summary Get charging station enrolment status
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationEnrolmentStatus(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EnrolmentStatusResponse> {
      return localVarFp
        .getChargingStationEnrolmentStatus(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Remove the charging station from the flex programme
     * @summary Unenrol charging station from flex
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    unenrolChargingStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .unenrolChargingStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * EnrolmentApi - object-oriented interface
 * @export
 * @class EnrolmentApi
 * @extends {BaseAPI}
 */
export class EnrolmentApi extends BaseAPI {
  /**
   * Get the enrolment status of a charging station
   * @summary Get charging station enrolment status
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnrolmentApi
   */
  public getChargingStationEnrolmentStatus(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return EnrolmentApiFp(this.configuration)
      .getChargingStationEnrolmentStatus(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Remove the charging station from the flex programme
   * @summary Unenrol charging station from flex
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnrolmentApi
   */
  public unenrolChargingStation(ppid: string, options?: RawAxiosRequestConfig) {
    return EnrolmentApiFp(this.configuration)
      .unenrolChargingStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * InvitationsApi - axios parameter creator
 * @export
 */
export const InvitationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Accept invitation
     * @summary Accept invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    acceptInvitation: async (
      token: string,
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'token' is not null or undefined
      assertParamExists('acceptInvitation', 'token', token);
      // verify required parameter 'body' is not null or undefined
      assertParamExists('acceptInvitation', 'body', body);
      const localVarPath = `/invitations/{token}/accept`.replace(
        `{${'token'}}`,
        encodeURIComponent(String(token))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a new invitation
     * @summary Create a new invitation
     * @param {InvitationRequest} invitationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createInvitation: async (
      invitationRequest: InvitationRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'invitationRequest' is not null or undefined
      assertParamExists(
        'createInvitation',
        'invitationRequest',
        invitationRequest
      );
      const localVarPath = `/invitations`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        invitationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * decline invitation
     * @summary decline invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    declineInvitation: async (
      token: string,
      body: object,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'token' is not null or undefined
      assertParamExists('declineInvitation', 'token', token);
      // verify required parameter 'body' is not null or undefined
      assertParamExists('declineInvitation', 'body', body);
      const localVarPath = `/invitations/{token}/decline`.replace(
        `{${'token'}}`,
        encodeURIComponent(String(token))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get invitation by token
     * @summary Get invitation by token
     * @param {string} token
     * @param {string} [includeUsed] Include used invitations if \&quot;true\&quot;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getInvitationByToken: async (
      token: string,
      includeUsed?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'token' is not null or undefined
      assertParamExists('getInvitationByToken', 'token', token);
      const localVarPath = `/invitations/{token}`.replace(
        `{${'token'}}`,
        encodeURIComponent(String(token))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (includeUsed !== undefined) {
        localVarQueryParameter['includeUsed'] = includeUsed;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * InvitationsApi - functional programming interface
 * @export
 */
export const InvitationsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    InvitationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Accept invitation
     * @summary Accept invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async acceptInvitation(
      token: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.acceptInvitation(token, body, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InvitationsApi.acceptInvitation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Create a new invitation
     * @summary Create a new invitation
     * @param {InvitationRequest} invitationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createInvitation(
      invitationRequest: InvitationRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Invitation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.createInvitation(
          invitationRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InvitationsApi.createInvitation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * decline invitation
     * @summary decline invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async declineInvitation(
      token: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.declineInvitation(token, body, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InvitationsApi.declineInvitation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get invitation by token
     * @summary Get invitation by token
     * @param {string} token
     * @param {string} [includeUsed] Include used invitations if \&quot;true\&quot;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getInvitationByToken(
      token: string,
      includeUsed?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Invitation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getInvitationByToken(
          token,
          includeUsed,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InvitationsApi.getInvitationByToken']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * InvitationsApi - factory interface
 * @export
 */
export const InvitationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = InvitationsApiFp(configuration);
  return {
    /**
     * Accept invitation
     * @summary Accept invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    acceptInvitation(
      token: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .acceptInvitation(token, body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a new invitation
     * @summary Create a new invitation
     * @param {InvitationRequest} invitationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createInvitation(
      invitationRequest: InvitationRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Invitation> {
      return localVarFp
        .createInvitation(invitationRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * decline invitation
     * @summary decline invitation
     * @param {string} token
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    declineInvitation(
      token: string,
      body: object,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .declineInvitation(token, body, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get invitation by token
     * @summary Get invitation by token
     * @param {string} token
     * @param {string} [includeUsed] Include used invitations if \&quot;true\&quot;
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getInvitationByToken(
      token: string,
      includeUsed?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Invitation> {
      return localVarFp
        .getInvitationByToken(token, includeUsed, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * InvitationsApi - object-oriented interface
 * @export
 * @class InvitationsApi
 * @extends {BaseAPI}
 */
export class InvitationsApi extends BaseAPI {
  /**
   * Accept invitation
   * @summary Accept invitation
   * @param {string} token
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InvitationsApi
   */
  public acceptInvitation(
    token: string,
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return InvitationsApiFp(this.configuration)
      .acceptInvitation(token, body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a new invitation
   * @summary Create a new invitation
   * @param {InvitationRequest} invitationRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InvitationsApi
   */
  public createInvitation(
    invitationRequest: InvitationRequest,
    options?: RawAxiosRequestConfig
  ) {
    return InvitationsApiFp(this.configuration)
      .createInvitation(invitationRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * decline invitation
   * @summary decline invitation
   * @param {string} token
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InvitationsApi
   */
  public declineInvitation(
    token: string,
    body: object,
    options?: RawAxiosRequestConfig
  ) {
    return InvitationsApiFp(this.configuration)
      .declineInvitation(token, body, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get invitation by token
   * @summary Get invitation by token
   * @param {string} token
   * @param {string} [includeUsed] Include used invitations if \&quot;true\&quot;
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InvitationsApi
   */
  public getInvitationByToken(
    token: string,
    includeUsed?: string,
    options?: RawAxiosRequestConfig
  ) {
    return InvitationsApiFp(this.configuration)
      .getInvitationByToken(token, includeUsed, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ProgrammesApi - axios parameter creator
 * @export
 */
export const ProgrammesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a programme
     * @summary Create programme
     * @param {CreateProgrammeRequestDto} createProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createProgramme: async (
      createProgrammeRequestDto: CreateProgrammeRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createProgrammeRequestDto' is not null or undefined
      assertParamExists(
        'createProgramme',
        'createProgrammeRequestDto',
        createProgrammeRequestDto
      );
      const localVarPath = `/programmes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createProgrammeRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get charging stations ppids in programme
     * @summary Get charging stations ppids in programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationsPpidsInProgramme: async (
      programmeId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists(
        'getChargingStationsPpidsInProgramme',
        'programmeId',
        programmeId
      );
      const localVarPath =
        `/programmes/{programmeId}/charging-stations/ppid`.replace(
          `{${'programmeId'}}`,
          encodeURIComponent(String(programmeId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a programme
     * @summary Get programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProgramme: async (
      programmeId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists('getProgramme', 'programmeId', programmeId);
      const localVarPath = `/programmes/{programmeId}`.replace(
        `{${'programmeId'}}`,
        encodeURIComponent(String(programmeId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all programmes
     * @summary Get all programmes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProgrammes: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/programmes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set a programmes charging stations
     * @summary Set programmes charging stations
     * @param {string} programmeId
     * @param {SetProgrammeChargingStationsRequestDto} setProgrammeChargingStationsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setProgrammesChargingStations: async (
      programmeId: string,
      setProgrammeChargingStationsRequestDto: SetProgrammeChargingStationsRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists(
        'setProgrammesChargingStations',
        'programmeId',
        programmeId
      );
      // verify required parameter 'setProgrammeChargingStationsRequestDto' is not null or undefined
      assertParamExists(
        'setProgrammesChargingStations',
        'setProgrammeChargingStationsRequestDto',
        setProgrammeChargingStationsRequestDto
      );
      const localVarPath =
        `/programmes/{programmeId}/charging-stations`.replace(
          `{${'programmeId'}}`,
          encodeURIComponent(String(programmeId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setProgrammeChargingStationsRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a programme
     * @summary Update programme
     * @param {string} programmeId
     * @param {UpdateProgrammeRequestDto} updateProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateProgramme: async (
      programmeId: string,
      updateProgrammeRequestDto: UpdateProgrammeRequestDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'programmeId' is not null or undefined
      assertParamExists('updateProgramme', 'programmeId', programmeId);
      // verify required parameter 'updateProgrammeRequestDto' is not null or undefined
      assertParamExists(
        'updateProgramme',
        'updateProgrammeRequestDto',
        updateProgrammeRequestDto
      );
      const localVarPath = `/programmes/{programmeId}`.replace(
        `{${'programmeId'}}`,
        encodeURIComponent(String(programmeId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateProgrammeRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ProgrammesApi - functional programming interface
 * @export
 */
export const ProgrammesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ProgrammesApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a programme
     * @summary Create programme
     * @param {CreateProgrammeRequestDto} createProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createProgramme(
      createProgrammeRequestDto: CreateProgrammeRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProgrammeResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.createProgramme(
        createProgrammeRequestDto,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProgrammesApi.createProgramme']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get charging stations ppids in programme
     * @summary Get charging stations ppids in programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationsPpidsInProgramme(
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<string>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationsPpidsInProgramme(
          programmeId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ProgrammesApi.getChargingStationsPpidsInProgramme'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a programme
     * @summary Get programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProgramme(
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProgrammeResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getProgramme(
        programmeId,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProgrammesApi.getProgramme']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all programmes
     * @summary Get all programmes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProgrammes(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ProgrammeResponse>>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getProgrammes(
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProgrammesApi.getProgrammes']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Set a programmes charging stations
     * @summary Set programmes charging stations
     * @param {string} programmeId
     * @param {SetProgrammeChargingStationsRequestDto} setProgrammeChargingStationsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setProgrammesChargingStations(
      programmeId: string,
      setProgrammeChargingStationsRequestDto: SetProgrammeChargingStationsRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProgrammeChargingStationsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setProgrammesChargingStations(
          programmeId,
          setProgrammeChargingStationsRequestDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProgrammesApi.setProgrammesChargingStations']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a programme
     * @summary Update programme
     * @param {string} programmeId
     * @param {UpdateProgrammeRequestDto} updateProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateProgramme(
      programmeId: string,
      updateProgrammeRequestDto: UpdateProgrammeRequestDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProgrammeResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.updateProgramme(
        programmeId,
        updateProgrammeRequestDto,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProgrammesApi.updateProgramme']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ProgrammesApi - factory interface
 * @export
 */
export const ProgrammesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ProgrammesApiFp(configuration);
  return {
    /**
     * Create a programme
     * @summary Create programme
     * @param {CreateProgrammeRequestDto} createProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createProgramme(
      createProgrammeRequestDto: CreateProgrammeRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProgrammeResponse> {
      return localVarFp
        .createProgramme(createProgrammeRequestDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get charging stations ppids in programme
     * @summary Get charging stations ppids in programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationsPpidsInProgramme(
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<string>> {
      return localVarFp
        .getChargingStationsPpidsInProgramme(programmeId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a programme
     * @summary Get programme
     * @param {string} programmeId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProgramme(
      programmeId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProgrammeResponse> {
      return localVarFp
        .getProgramme(programmeId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all programmes
     * @summary Get all programmes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProgrammes(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ProgrammeResponse>> {
      return localVarFp
        .getProgrammes(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set a programmes charging stations
     * @summary Set programmes charging stations
     * @param {string} programmeId
     * @param {SetProgrammeChargingStationsRequestDto} setProgrammeChargingStationsRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setProgrammesChargingStations(
      programmeId: string,
      setProgrammeChargingStationsRequestDto: SetProgrammeChargingStationsRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProgrammeChargingStationsResponse> {
      return localVarFp
        .setProgrammesChargingStations(
          programmeId,
          setProgrammeChargingStationsRequestDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a programme
     * @summary Update programme
     * @param {string} programmeId
     * @param {UpdateProgrammeRequestDto} updateProgrammeRequestDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateProgramme(
      programmeId: string,
      updateProgrammeRequestDto: UpdateProgrammeRequestDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProgrammeResponse> {
      return localVarFp
        .updateProgramme(programmeId, updateProgrammeRequestDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ProgrammesApi - object-oriented interface
 * @export
 * @class ProgrammesApi
 * @extends {BaseAPI}
 */
export class ProgrammesApi extends BaseAPI {
  /**
   * Create a programme
   * @summary Create programme
   * @param {CreateProgrammeRequestDto} createProgrammeRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public createProgramme(
    createProgrammeRequestDto: CreateProgrammeRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return ProgrammesApiFp(this.configuration)
      .createProgramme(createProgrammeRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get charging stations ppids in programme
   * @summary Get charging stations ppids in programme
   * @param {string} programmeId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public getChargingStationsPpidsInProgramme(
    programmeId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ProgrammesApiFp(this.configuration)
      .getChargingStationsPpidsInProgramme(programmeId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a programme
   * @summary Get programme
   * @param {string} programmeId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public getProgramme(programmeId: string, options?: RawAxiosRequestConfig) {
    return ProgrammesApiFp(this.configuration)
      .getProgramme(programmeId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all programmes
   * @summary Get all programmes
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public getProgrammes(options?: RawAxiosRequestConfig) {
    return ProgrammesApiFp(this.configuration)
      .getProgrammes(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set a programmes charging stations
   * @summary Set programmes charging stations
   * @param {string} programmeId
   * @param {SetProgrammeChargingStationsRequestDto} setProgrammeChargingStationsRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public setProgrammesChargingStations(
    programmeId: string,
    setProgrammeChargingStationsRequestDto: SetProgrammeChargingStationsRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return ProgrammesApiFp(this.configuration)
      .setProgrammesChargingStations(
        programmeId,
        setProgrammeChargingStationsRequestDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a programme
   * @summary Update programme
   * @param {string} programmeId
   * @param {UpdateProgrammeRequestDto} updateProgrammeRequestDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProgrammesApi
   */
  public updateProgramme(
    programmeId: string,
    updateProgrammeRequestDto: UpdateProgrammeRequestDto,
    options?: RawAxiosRequestConfig
  ) {
    return ProgrammesApiFp(this.configuration)
      .updateProgramme(programmeId, updateProgrammeRequestDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ProvidersApi - axios parameter creator
 * @export
 */
export const ProvidersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get a charging station by the provider\'s id
     * @summary Get a charging station by the provider\'s id
     * @param {string} name The name of the competition provider
     * @param {string} externalChargingStationId The ID of the charging station according to the external provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationByExternalId: async (
      name: string,
      externalChargingStationId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'name' is not null or undefined
      assertParamExists('getChargingStationByExternalId', 'name', name);
      // verify required parameter 'externalChargingStationId' is not null or undefined
      assertParamExists(
        'getChargingStationByExternalId',
        'externalChargingStationId',
        externalChargingStationId
      );
      const localVarPath = `/providers/{name}/charging-stations`.replace(
        `{${'name'}}`,
        encodeURIComponent(String(name))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (externalChargingStationId !== undefined) {
        localVarQueryParameter['externalChargingStationId'] =
          externalChargingStationId;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Find a competition provider by its id.
     * @summary Find a provider by id
     * @param {string} id
     * @param {string} name The id of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderById: async (
      id: string,
      name: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('getProviderById', 'id', id);
      // verify required parameter 'name' is not null or undefined
      assertParamExists('getProviderById', 'name', name);
      const localVarPath = `/providers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (id !== undefined) {
        localVarQueryParameter['id'] = id;
      }

      if (name !== undefined) {
        localVarQueryParameter['name'] = name;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a competition provider by its name.
     * @summary Get a provider by name
     * @param {string} name The name of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderByName: async (
      name: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'name' is not null or undefined
      assertParamExists('getProviderByName', 'name', name);
      const localVarPath = `/providers/{name}`.replace(
        `{${'name'}}`,
        encodeURIComponent(String(name))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a charging station by ppid
     * @summary Get a charging station by ppid
     * @param {string} name The name of the competition provider
     * @param {string} ppid The ppid of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderChargingStationByPpid: async (
      name: string,
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'name' is not null or undefined
      assertParamExists('getProviderChargingStationByPpid', 'name', name);
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('getProviderChargingStationByPpid', 'ppid', ppid);
      const localVarPath = `/providers/{name}/provider-charging-stations/{ppid}`
        .replace(`{${'name'}}`, encodeURIComponent(String(name)))
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Link a charging station to a provider. An externalChargingStationId can be provided to correlate our identifier with the provider\'s identifier (if it has one).
     * @summary Link a charging station to a provider
     * @param {string} name The name of the competition provider
     * @param {LinkChargingStationRequest} linkChargingStationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkChargingStation: async (
      name: string,
      linkChargingStationRequest: LinkChargingStationRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'name' is not null or undefined
      assertParamExists('linkChargingStation', 'name', name);
      // verify required parameter 'linkChargingStationRequest' is not null or undefined
      assertParamExists(
        'linkChargingStation',
        'linkChargingStationRequest',
        linkChargingStationRequest
      );
      const localVarPath = `/providers/{name}/charging-stations`.replace(
        `{${'name'}}`,
        encodeURIComponent(String(name))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        linkChargingStationRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ProvidersApi - functional programming interface
 * @export
 */
export const ProvidersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ProvidersApiAxiosParamCreator(configuration);
  return {
    /**
     * Get a charging station by the provider\'s id
     * @summary Get a charging station by the provider\'s id
     * @param {string} name The name of the competition provider
     * @param {string} externalChargingStationId The ID of the charging station according to the external provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getChargingStationByExternalId(
      name: string,
      externalChargingStationId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargingStation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getChargingStationByExternalId(
          name,
          externalChargingStationId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProvidersApi.getChargingStationByExternalId']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Find a competition provider by its id.
     * @summary Find a provider by id
     * @param {string} id
     * @param {string} name The id of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProviderById(
      id: string,
      name: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CompetitionProviderResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getProviderById(
        id,
        name,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProvidersApi.getProviderById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a competition provider by its name.
     * @summary Get a provider by name
     * @param {string} name The name of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProviderByName(
      name: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CompetitionProviderResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getProviderByName(name, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProvidersApi.getProviderByName']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a charging station by ppid
     * @summary Get a charging station by ppid
     * @param {string} name The name of the competition provider
     * @param {string} ppid The ppid of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getProviderChargingStationByPpid(
      name: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProviderChargingStationByPPIDResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getProviderChargingStationByPpid(
          name,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProvidersApi.getProviderChargingStationByPpid']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Link a charging station to a provider. An externalChargingStationId can be provided to correlate our identifier with the provider\'s identifier (if it has one).
     * @summary Link a charging station to a provider
     * @param {string} name The name of the competition provider
     * @param {LinkChargingStationRequest} linkChargingStationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async linkChargingStation(
      name: string,
      linkChargingStationRequest: LinkChargingStationRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<LinkChargingStationResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.linkChargingStation(
          name,
          linkChargingStationRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ProvidersApi.linkChargingStation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ProvidersApi - factory interface
 * @export
 */
export const ProvidersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ProvidersApiFp(configuration);
  return {
    /**
     * Get a charging station by the provider\'s id
     * @summary Get a charging station by the provider\'s id
     * @param {string} name The name of the competition provider
     * @param {string} externalChargingStationId The ID of the charging station according to the external provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getChargingStationByExternalId(
      name: string,
      externalChargingStationId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargingStation> {
      return localVarFp
        .getChargingStationByExternalId(
          name,
          externalChargingStationId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Find a competition provider by its id.
     * @summary Find a provider by id
     * @param {string} id
     * @param {string} name The id of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderById(
      id: string,
      name: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CompetitionProviderResponse> {
      return localVarFp
        .getProviderById(id, name, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a competition provider by its name.
     * @summary Get a provider by name
     * @param {string} name The name of the competition provider
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderByName(
      name: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CompetitionProviderResponse> {
      return localVarFp
        .getProviderByName(name, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a charging station by ppid
     * @summary Get a charging station by ppid
     * @param {string} name The name of the competition provider
     * @param {string} ppid The ppid of the charging station
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getProviderChargingStationByPpid(
      name: string,
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProviderChargingStationByPPIDResponse> {
      return localVarFp
        .getProviderChargingStationByPpid(name, ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Link a charging station to a provider. An externalChargingStationId can be provided to correlate our identifier with the provider\'s identifier (if it has one).
     * @summary Link a charging station to a provider
     * @param {string} name The name of the competition provider
     * @param {LinkChargingStationRequest} linkChargingStationRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkChargingStation(
      name: string,
      linkChargingStationRequest: LinkChargingStationRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<LinkChargingStationResponse> {
      return localVarFp
        .linkChargingStation(name, linkChargingStationRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ProvidersApi - object-oriented interface
 * @export
 * @class ProvidersApi
 * @extends {BaseAPI}
 */
export class ProvidersApi extends BaseAPI {
  /**
   * Get a charging station by the provider\'s id
   * @summary Get a charging station by the provider\'s id
   * @param {string} name The name of the competition provider
   * @param {string} externalChargingStationId The ID of the charging station according to the external provider
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProvidersApi
   */
  public getChargingStationByExternalId(
    name: string,
    externalChargingStationId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ProvidersApiFp(this.configuration)
      .getChargingStationByExternalId(name, externalChargingStationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Find a competition provider by its id.
   * @summary Find a provider by id
   * @param {string} id
   * @param {string} name The id of the competition provider
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProvidersApi
   */
  public getProviderById(
    id: string,
    name: string,
    options?: RawAxiosRequestConfig
  ) {
    return ProvidersApiFp(this.configuration)
      .getProviderById(id, name, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a competition provider by its name.
   * @summary Get a provider by name
   * @param {string} name The name of the competition provider
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProvidersApi
   */
  public getProviderByName(name: string, options?: RawAxiosRequestConfig) {
    return ProvidersApiFp(this.configuration)
      .getProviderByName(name, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a charging station by ppid
   * @summary Get a charging station by ppid
   * @param {string} name The name of the competition provider
   * @param {string} ppid The ppid of the charging station
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProvidersApi
   */
  public getProviderChargingStationByPpid(
    name: string,
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ProvidersApiFp(this.configuration)
      .getProviderChargingStationByPpid(name, ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Link a charging station to a provider. An externalChargingStationId can be provided to correlate our identifier with the provider\'s identifier (if it has one).
   * @summary Link a charging station to a provider
   * @param {string} name The name of the competition provider
   * @param {LinkChargingStationRequest} linkChargingStationRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProvidersApi
   */
  public linkChargingStation(
    name: string,
    linkChargingStationRequest: LinkChargingStationRequest,
    options?: RawAxiosRequestConfig
  ) {
    return ProvidersApiFp(this.configuration)
      .linkChargingStation(name, linkChargingStationRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
