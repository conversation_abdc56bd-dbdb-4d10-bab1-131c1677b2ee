/* tslint:disable */
/* eslint-disable */
/**
 * Vehicles API
 * API for managing vehicles, primarily vehicles that form our \"Smart Charging\" ecosystem
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface ConnectedChargeState
 */
export interface ConnectedChargeState {
  /**
   * The vehicle battery capacity in kWh
   * @type {number}
   * @memberof ConnectedChargeState
   */
  batteryCapacity: number;
  /**
   * The vehicle battery level in percent
   * @type {number}
   * @memberof ConnectedChargeState
   */
  batteryLevelPercent: number | null;
  /**
   * The vehicle charge limit in percent
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeLimitPercent: number | null;
  /**
   * The vehicle charge rate in kW
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeRate: number | null;
  /**
   * The vehicle charge time remaining in minutes
   * @type {number}
   * @memberof ConnectedChargeState
   */
  chargeTimeRemaining: number | null;
  /**
   * The vehicle charging status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isCharging: boolean | null;
  /**
   * The vehicle fully charged status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isFullyCharged: boolean | null;
  /**
   * The vehicle plugged in status
   * @type {boolean}
   * @memberof ConnectedChargeState
   */
  isPluggedIn: boolean | null;
  /**
   * The vehicle last updated timestamp
   * @type {string}
   * @memberof ConnectedChargeState
   */
  lastUpdated: string | null;
  /**
   * The vehicle max current in A
   * @type {number}
   * @memberof ConnectedChargeState
   */
  maxCurrent: number | null;
  /**
   * The vehicle power delivery state
   * @type {string}
   * @memberof ConnectedChargeState
   */
  powerDeliveryState: ConnectedChargeStatePowerDeliveryStateEnum | null;
  /**
   * The vehicle range in km
   * @type {number}
   * @memberof ConnectedChargeState
   */
  range: number | null;
}

export const ConnectedChargeStatePowerDeliveryStateEnum = {
  Unplugged: 'UNPLUGGED',
  PluggedInnoPower: 'PLUGGED_IN:NO_POWER',
  PluggedInstopped: 'PLUGGED_IN:STOPPED',
  PluggedIncomplete: 'PLUGGED_IN:COMPLETE',
  PluggedIncharging: 'PLUGGED_IN:CHARGING',
  Unknown: 'UNKNOWN',
  PluggedIninitializing: 'PLUGGED_IN:INITIALIZING',
  PluggedInfault: 'PLUGGED_IN:FAULT',
} as const;

export type ConnectedChargeStatePowerDeliveryStateEnum =
  (typeof ConnectedChargeStatePowerDeliveryStateEnum)[keyof typeof ConnectedChargeStatePowerDeliveryStateEnum];

/**
 *
 * @export
 * @interface ConnectedStatefulVehicleDto
 */
export interface ConnectedStatefulVehicleDto {
  /**
   * The vehicle ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  id: string;
  /**
   * The time that the vehicle was last seen by enode
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  lastSeen?: string | null;
  /**
   * The enode user ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof ConnectedStatefulVehicleDto
   */
  enodeVehicleId?: string | null;
  /**
   * The vehicle information
   * @type {VehicleInformation}
   * @memberof ConnectedStatefulVehicleDto
   */
  vehicleInformation: VehicleInformation;
  /**
   * The vehicle charge state
   * @type {ConnectedChargeState}
   * @memberof ConnectedStatefulVehicleDto
   */
  chargeState: ConnectedChargeState;
  /**
   * The vehicle intervention endpoints
   * @type {InterventionDto}
   * @memberof ConnectedStatefulVehicleDto
   */
  interventions: InterventionDto;
}
/**
 * @type CreateVehicle201Response
 * @export
 */
export type CreateVehicle201Response =
  | ConnectedStatefulVehicleDto
  | GenericStatefulVehicleDto;

/**
 *
 * @export
 * @interface ExtendedVehicleInformation
 */
export interface ExtendedVehicleInformation {
  /**
   * The vehicle make
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  brand?: string | null;
  /**
   * The vehicle model
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  model?: string | null;
  /**
   * The vehicle model variant
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  modelVariant?: string | null;
  /**
   * The vehicle registration plate
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  vehicleRegistrationPlate?: string | null;
  /**
   * The vehicle display name
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  displayName?: string | null;
  /**
   * The EV database ID
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  evDatabaseId?: string | null;
  /**
   * The vehicle VIN
   * @type {string}
   * @memberof ExtendedVehicleInformation
   */
  vin?: string | null;
}
/**
 *
 * @export
 * @interface GenericChargeState
 */
export interface GenericChargeState {
  /**
   * The vehicle battery capacity in kWh
   * @type {number}
   * @memberof GenericChargeState
   */
  batteryCapacity: number;
}
/**
 *
 * @export
 * @interface GenericStatefulVehicleDto
 */
export interface GenericStatefulVehicleDto {
  /**
   * The vehicle ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  id: string;
  /**
   * The vehicle information
   * @type {VehicleInformation}
   * @memberof GenericStatefulVehicleDto
   */
  vehicleInformation: VehicleInformation;
  /**
   * The enode user ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof GenericStatefulVehicleDto
   */
  enodeVehicleId?: string | null;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof GenericStatefulVehicleDto
   */
  chargeState: GenericChargeState;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface InterventionDto
 */
export interface InterventionDto {
  /**
   * The endpoint to extract all interventions
   * @type {string}
   * @memberof InterventionDto
   */
  all: string;
  /**
   * The individual interventions for charge state
   * @type {string}
   * @memberof InterventionDto
   */
  chargeState: string;
  /**
   * The individual interventions for vehicle information
   * @type {string}
   * @memberof InterventionDto
   */
  information: string;
}
/**
 *
 * @export
 * @interface PatchVehicleDto
 */
export interface PatchVehicleDto {
  /**
   * Vehicle information
   * @type {ExtendedVehicleInformation}
   * @memberof PatchVehicleDto
   */
  vehicleInformation?: ExtendedVehicleInformation;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof PatchVehicleDto
   */
  chargeState?: GenericChargeState;
  /**
   * The vehicle user ID
   * @type {string}
   * @memberof PatchVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof PatchVehicleDto
   */
  enodeVehicleId?: string | null;
}
/**
 *
 * @export
 * @interface PostVehicleDto
 */
export interface PostVehicleDto {
  /**
   * The enode user ID
   * @type {string}
   * @memberof PostVehicleDto
   */
  enodeUserId?: string | null;
  /**
   * The vehicle enode ID
   * @type {string}
   * @memberof PostVehicleDto
   */
  enodeVehicleId?: string | null;
  /**
   * The vehicle information
   * @type {ExtendedVehicleInformation}
   * @memberof PostVehicleDto
   */
  vehicleInformation: ExtendedVehicleInformation;
  /**
   * The vehicle charge state
   * @type {GenericChargeState}
   * @memberof PostVehicleDto
   */
  chargeState: GenericChargeState;
}
/**
 *
 * @export
 * @interface VehicleInformation
 */
export interface VehicleInformation {
  /**
   * The vehicle make
   * @type {string}
   * @memberof VehicleInformation
   */
  brand?: string | null;
  /**
   * The vehicle model
   * @type {string}
   * @memberof VehicleInformation
   */
  model?: string | null;
  /**
   * The vehicle model variant
   * @type {string}
   * @memberof VehicleInformation
   */
  modelVariant?: string | null;
  /**
   * The vehicle registration plate
   * @type {string}
   * @memberof VehicleInformation
   */
  vehicleRegistrationPlate?: string | null;
  /**
   * The vehicle display name
   * @type {string}
   * @memberof VehicleInformation
   */
  displayName?: string | null;
  /**
   * The EV database ID
   * @type {string}
   * @memberof VehicleInformation
   */
  evDatabaseId?: string | null;
}
/**
 *
 * @export
 * @interface VehicleInterventionDto
 */
export interface VehicleInterventionDto {
  /**
   * The identifier of the intervention
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  id: string;
  /**
   * The vendor of the intervention
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  vendor: string | null;
  /**
   * The type of the intervention
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  vendorType: string | null;
  /**
   * The brand of the intervention
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  brand: string | null;
  /**
   * The date the intervention was introduced
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  introducedAt: string;
  /**
   * The domain of the intervention
   * @type {string}
   * @memberof VehicleInterventionDto
   */
  domain: VehicleInterventionDtoDomainEnum;
  /**
   * The resolution of the intervention
   * @type {VehicleInterventionResolutionDto}
   * @memberof VehicleInterventionDto
   */
  resolution: VehicleInterventionResolutionDto;
}

export const VehicleInterventionDtoDomainEnum = {
  Account: 'Account',
  Device: 'Device',
} as const;

export type VehicleInterventionDtoDomainEnum =
  (typeof VehicleInterventionDtoDomainEnum)[keyof typeof VehicleInterventionDtoDomainEnum];

/**
 *
 * @export
 * @interface VehicleInterventionResolutionDto
 */
export interface VehicleInterventionResolutionDto {
  /**
   * The title of the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDto
   */
  title: string;
  /**
   * The description of the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDto
   */
  description: string;
  /**
   * The access required to perform the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDto
   */
  access: VehicleInterventionResolutionDtoAccessEnum;
  /**
   * The agent responsible for performing the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDto
   */
  agent: VehicleInterventionResolutionDtoAgentEnum;
}

export const VehicleInterventionResolutionDtoAccessEnum = {
  Remote: 'Remote',
  Physical: 'Physical',
} as const;

export type VehicleInterventionResolutionDtoAccessEnum =
  (typeof VehicleInterventionResolutionDtoAccessEnum)[keyof typeof VehicleInterventionResolutionDtoAccessEnum];
export const VehicleInterventionResolutionDtoAgentEnum = {
  User: 'User',
  ThirdParty: 'ThirdParty',
} as const;

export type VehicleInterventionResolutionDtoAgentEnum =
  (typeof VehicleInterventionResolutionDtoAgentEnum)[keyof typeof VehicleInterventionResolutionDtoAgentEnum];

/**
 *
 * @export
 * @interface VehicleInterventionResponseDto
 */
export interface VehicleInterventionResponseDto {
  /**
   * The information interventions
   * @type {Array<VehicleInterventionDto>}
   * @memberof VehicleInterventionResponseDto
   */
  information: Array<VehicleInterventionDto>;
  /**
   * The charge state interventions
   * @type {Array<VehicleInterventionDto>}
   * @memberof VehicleInterventionResponseDto
   */
  chargeState: Array<VehicleInterventionDto>;
}
/**
 *
 * @export
 * @interface VehicleSearchResponseDto
 */
export interface VehicleSearchResponseDto {
  /**
   * Array of vehicles returned from the search
   * @type {Array<CreateVehicle201Response>}
   * @memberof VehicleSearchResponseDto
   */
  data: Array<CreateVehicle201Response>;
}

/**
 * HealthApi - axios parameter creator
 * @export
 */
export const HealthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthApi - functional programming interface
 * @export
 */
export const HealthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = HealthApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthApi - factory interface
 * @export
 */
export const HealthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthApiFp(configuration);
  return {
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthApi - object-oriented interface
 * @export
 * @class HealthApi
 * @extends {BaseAPI}
 */
export class HealthApi extends BaseAPI {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * InterventionsApi - axios parameter creator
 * @export
 */
export const InterventionsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllVehicleInterventions: async (
      vehicleId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getAllVehicleInterventions', 'vehicleId', vehicleId);
      const localVarPath = `/vehicles/{vehicleId}/enode/interventions`.replace(
        `{${'vehicleId'}}`,
        encodeURIComponent(String(vehicleId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntervention: async (
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getVehicleIntervention', 'vehicleId', vehicleId);
      // verify required parameter 'interventionId' is not null or undefined
      assertParamExists(
        'getVehicleIntervention',
        'interventionId',
        interventionId
      );
      const localVarPath =
        `/vehicles/{vehicleId}/enode/interventions/{interventionId}`
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)))
          .replace(
            `{${'interventionId'}}`,
            encodeURIComponent(String(interventionId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * InterventionsApi - functional programming interface
 * @export
 */
export const InterventionsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    InterventionsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAllVehicleInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAllVehicleInterventions(
          vehicleId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InterventionsApi.getAllVehicleInterventions']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getVehicleIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getVehicleIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InterventionsApi.getVehicleIntervention']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * InterventionsApi - factory interface
 * @export
 */
export const InterventionsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = InterventionsApiFp(configuration);
  return {
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllVehicleInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionResponseDto> {
      return localVarFp
        .getAllVehicleInterventions(vehicleId, acceptLanguage, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionDto> {
      return localVarFp
        .getVehicleIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * InterventionsApi - object-oriented interface
 * @export
 * @class InterventionsApi
 * @extends {BaseAPI}
 */
export class InterventionsApi extends BaseAPI {
  /**
   *
   * @summary Get all interventions for a vehicle
   * @param {string} vehicleId The vehicle ID
   * @param {string} [acceptLanguage]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InterventionsApi
   */
  public getAllVehicleInterventions(
    vehicleId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return InterventionsApiFp(this.configuration)
      .getAllVehicleInterventions(vehicleId, acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get a vehicle intervention
   * @param {string} vehicleId The vehicle ID
   * @param {string} interventionId The vehicle intervention ID
   * @param {string} [acceptLanguage]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InterventionsApi
   */
  public getVehicleIntervention(
    vehicleId: string,
    interventionId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return InterventionsApiFp(this.configuration)
      .getVehicleIntervention(
        vehicleId,
        interventionId,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VehiclesApi - axios parameter creator
 * @export
 */
export const VehiclesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates a new vehicle
     * @summary Post vehicle data
     * @param {PostVehicleDto} postVehicleDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createVehicle: async (
      postVehicleDto: PostVehicleDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'postVehicleDto' is not null or undefined
      assertParamExists('createVehicle', 'postVehicleDto', postVehicleDto);
      const localVarPath = `/vehicles`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        postVehicleDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle ID, deletes the vehicle
     * @summary Delete vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteVehicleById: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('deleteVehicleById', 'id', id);
      const localVarPath = `/vehicles/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllVehicleInterventions: async (
      vehicleId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getAllVehicleInterventions', 'vehicleId', vehicleId);
      const localVarPath = `/vehicles/{vehicleId}/enode/interventions`.replace(
        `{${'vehicleId'}}`,
        encodeURIComponent(String(vehicleId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle ID, returns vehicle information
     * @summary Get vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleById: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('getVehicleById', 'id', id);
      const localVarPath = `/vehicles/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntervention: async (
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists('getVehicleIntervention', 'vehicleId', vehicleId);
      // verify required parameter 'interventionId' is not null or undefined
      assertParamExists(
        'getVehicleIntervention',
        'interventionId',
        interventionId
      );
      const localVarPath =
        `/vehicles/{vehicleId}/enode/interventions/{interventionId}`
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)))
          .replace(
            `{${'interventionId'}}`,
            encodeURIComponent(String(interventionId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle registration plate or enodeVehicle and enodeUserId, returns a list of vehicles
     * @summary Get vehicle data by registration plate or enodeVehicleId and enodeUserId
     * @param {string} [enodeUserId] The enode user ID
     * @param {string} [enodeVehicleId] The enode vehicle ID
     * @param {string} [vehicleRegistrationPlate] The vehicle registration plate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchVehicles: async (
      enodeUserId?: string,
      enodeVehicleId?: string,
      vehicleRegistrationPlate?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/vehicles`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (enodeUserId !== undefined) {
        localVarQueryParameter['enodeUserId'] = enodeUserId;
      }

      if (enodeVehicleId !== undefined) {
        localVarQueryParameter['enodeVehicleId'] = enodeVehicleId;
      }

      if (vehicleRegistrationPlate !== undefined) {
        localVarQueryParameter['vehicleRegistrationPlate'] =
          vehicleRegistrationPlate;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle ID, suggests a state refresh
     * @summary Suggest a state refresh
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    suggestStateRefresh: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('suggestStateRefresh', 'id', id);
      const localVarPath =
        `/vehicles/{id}/charge-state/suggest-refresh`.replace(
          `{${'id'}}`,
          encodeURIComponent(String(id))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {PatchVehicleDto} patchVehicleDto The new vehicle data
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateVehicleById: async (
      id: string,
      patchVehicleDto: PatchVehicleDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('updateVehicleById', 'id', id);
      // verify required parameter 'patchVehicleDto' is not null or undefined
      assertParamExists(
        'updateVehicleById',
        'patchVehicleDto',
        patchVehicleDto
      );
      const localVarPath = `/vehicles/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        patchVehicleDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VehiclesApi - functional programming interface
 * @export
 */
export const VehiclesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VehiclesApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates a new vehicle
     * @summary Post vehicle data
     * @param {PostVehicleDto} postVehicleDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async createVehicle(
      postVehicleDto: PostVehicleDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateVehicle201Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.createVehicle(
        postVehicleDto,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.createVehicle']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle ID, deletes the vehicle
     * @summary Delete vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async deleteVehicleById(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.deleteVehicleById(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.deleteVehicleById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAllVehicleInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAllVehicleInterventions(
          vehicleId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.getAllVehicleInterventions']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle ID, returns vehicle information
     * @summary Get vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getVehicleById(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateVehicle201Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getVehicleById(
        id,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.getVehicleById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getVehicleIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getVehicleIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.getVehicleIntervention']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle registration plate or enodeVehicle and enodeUserId, returns a list of vehicles
     * @summary Get vehicle data by registration plate or enodeVehicleId and enodeUserId
     * @param {string} [enodeUserId] The enode user ID
     * @param {string} [enodeVehicleId] The enode vehicle ID
     * @param {string} [vehicleRegistrationPlate] The vehicle registration plate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async searchVehicles(
      enodeUserId?: string,
      enodeVehicleId?: string,
      vehicleRegistrationPlate?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleSearchResponseDto>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.searchVehicles(
        enodeUserId,
        enodeVehicleId,
        vehicleRegistrationPlate,
        options
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.searchVehicles']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle ID, suggests a state refresh
     * @summary Suggest a state refresh
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async suggestStateRefresh(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.suggestStateRefresh(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.suggestStateRefresh']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {PatchVehicleDto} patchVehicleDto The new vehicle data
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateVehicleById(
      id: string,
      patchVehicleDto: PatchVehicleDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateVehicle201Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateVehicleById(
          id,
          patchVehicleDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.updateVehicleById']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VehiclesApi - factory interface
 * @export
 */
export const VehiclesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VehiclesApiFp(configuration);
  return {
    /**
     * Creates a new vehicle
     * @summary Post vehicle data
     * @param {PostVehicleDto} postVehicleDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    createVehicle(
      postVehicleDto: PostVehicleDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateVehicle201Response> {
      return localVarFp
        .createVehicle(postVehicleDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle ID, deletes the vehicle
     * @summary Delete vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteVehicleById(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .deleteVehicleById(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get all interventions for a vehicle
     * @param {string} vehicleId The vehicle ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllVehicleInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionResponseDto> {
      return localVarFp
        .getAllVehicleInterventions(vehicleId, acceptLanguage, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle ID, returns vehicle information
     * @summary Get vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleById(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateVehicle201Response> {
      return localVarFp
        .getVehicleById(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get a vehicle intervention
     * @param {string} vehicleId The vehicle ID
     * @param {string} interventionId The vehicle intervention ID
     * @param {string} [acceptLanguage]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getVehicleIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionDto> {
      return localVarFp
        .getVehicleIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle registration plate or enodeVehicle and enodeUserId, returns a list of vehicles
     * @summary Get vehicle data by registration plate or enodeVehicleId and enodeUserId
     * @param {string} [enodeUserId] The enode user ID
     * @param {string} [enodeVehicleId] The enode vehicle ID
     * @param {string} [vehicleRegistrationPlate] The vehicle registration plate
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    searchVehicles(
      enodeUserId?: string,
      enodeVehicleId?: string,
      vehicleRegistrationPlate?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleSearchResponseDto> {
      return localVarFp
        .searchVehicles(
          enodeUserId,
          enodeVehicleId,
          vehicleRegistrationPlate,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle ID, suggests a state refresh
     * @summary Suggest a state refresh
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    suggestStateRefresh(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .suggestStateRefresh(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a vehicle ID and new data, updates the vehicle
     * @summary Update vehicle data by ID
     * @param {string} id The vehicle ID
     * @param {PatchVehicleDto} patchVehicleDto The new vehicle data
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateVehicleById(
      id: string,
      patchVehicleDto: PatchVehicleDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateVehicle201Response> {
      return localVarFp
        .updateVehicleById(id, patchVehicleDto, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VehiclesApi - object-oriented interface
 * @export
 * @class VehiclesApi
 * @extends {BaseAPI}
 */
export class VehiclesApi extends BaseAPI {
  /**
   * Creates a new vehicle
   * @summary Post vehicle data
   * @param {PostVehicleDto} postVehicleDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public createVehicle(
    postVehicleDto: PostVehicleDto,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .createVehicle(postVehicleDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle ID, deletes the vehicle
   * @summary Delete vehicle data by ID
   * @param {string} id The vehicle ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public deleteVehicleById(id: string, options?: RawAxiosRequestConfig) {
    return VehiclesApiFp(this.configuration)
      .deleteVehicleById(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get all interventions for a vehicle
   * @param {string} vehicleId The vehicle ID
   * @param {string} [acceptLanguage]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public getAllVehicleInterventions(
    vehicleId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .getAllVehicleInterventions(vehicleId, acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle ID, returns vehicle information
   * @summary Get vehicle data by ID
   * @param {string} id The vehicle ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public getVehicleById(id: string, options?: RawAxiosRequestConfig) {
    return VehiclesApiFp(this.configuration)
      .getVehicleById(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get a vehicle intervention
   * @param {string} vehicleId The vehicle ID
   * @param {string} interventionId The vehicle intervention ID
   * @param {string} [acceptLanguage]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public getVehicleIntervention(
    vehicleId: string,
    interventionId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .getVehicleIntervention(
        vehicleId,
        interventionId,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle registration plate or enodeVehicle and enodeUserId, returns a list of vehicles
   * @summary Get vehicle data by registration plate or enodeVehicleId and enodeUserId
   * @param {string} [enodeUserId] The enode user ID
   * @param {string} [enodeVehicleId] The enode vehicle ID
   * @param {string} [vehicleRegistrationPlate] The vehicle registration plate
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public searchVehicles(
    enodeUserId?: string,
    enodeVehicleId?: string,
    vehicleRegistrationPlate?: string,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .searchVehicles(
        enodeUserId,
        enodeVehicleId,
        vehicleRegistrationPlate,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle ID, suggests a state refresh
   * @summary Suggest a state refresh
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public suggestStateRefresh(id: string, options?: RawAxiosRequestConfig) {
    return VehiclesApiFp(this.configuration)
      .suggestStateRefresh(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a vehicle ID and new data, updates the vehicle
   * @summary Update vehicle data by ID
   * @param {string} id The vehicle ID
   * @param {PatchVehicleDto} patchVehicleDto The new vehicle data
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public updateVehicleById(
    id: string,
    patchVehicleDto: PatchVehicleDto,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .updateVehicleById(id, patchVehicleDto, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
