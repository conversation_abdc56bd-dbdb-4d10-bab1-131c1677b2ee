/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.post(`${baseURL}/vehicles`, async () => {
    const resultArray = [
      [await getCreateVehicle201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles`, async () => {
    const resultArray = [
      [await getSearchVehicles200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/vehicles/:id`, async () => {
    const resultArray = [
      [await getGetVehicleById200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/vehicles/:id`, async () => {
    const resultArray = [
      [await getUpdateVehicleById200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/vehicles/:id`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(
    `${baseURL}/vehicles/:id/charge-state/suggest-refresh`,
    async () => {
      const resultArray = [[undefined, { status: 202 }]];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.get(`${baseURL}/vehicles/:vehicleId/enode/interventions`, async () => {
    const resultArray = [
      [await getGetAllVehicleInterventions200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(
    `${baseURL}/vehicles/:vehicleId/enode/interventions/:interventionId`,
    async () => {
      const resultArray = [
        [await getGetVehicleIntervention200Response(), { status: 200 }],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [await getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getCreateVehicle201Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      chargeState: {
        batteryCapacity: 78,
      },
    },
    {
      id: faker.string.uuid(),
      lastSeen: faker.lorem.words(),
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      chargeState: {
        batteryCapacity: 78,
        batteryLevelPercent: 78,
        chargeLimitPercent: 78,
        chargeRate: 78,
        chargeTimeRemaining: 78,
        isCharging: true,
        isFullyCharged: true,
        isPluggedIn: true,
        lastUpdated: '2021-08-12T09:00:00Z',
        maxCurrent: 32,
        powerDeliveryState: faker.helpers.arrayElement([
          'UNPLUGGED',
          'PLUGGED_IN:NO_POWER',
          'PLUGGED_IN:STOPPED',
          'PLUGGED_IN:COMPLETE',
          'PLUGGED_IN:CHARGING',
          'UNKNOWN',
          'PLUGGED_IN:INITIALIZING',
          'PLUGGED_IN:FAULT',
        ]),
        range: 300,
      },
      interventions: {
        all: faker.lorem.words(),
        chargeState: faker.lorem.words(),
        information: faker.lorem.words(),
      },
    },
  ]);
}

export function getSearchVehicles200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        {
          id: faker.string.uuid(),
          vehicleInformation: {
            brand: 'Polestar',
            model: '2',
            modelVariant: 'Long range',
            vehicleRegistrationPlate: 'ABC123',
            displayName: 'My car',
            evDatabaseId: '123456',
          },
          enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          chargeState: {
            batteryCapacity: 78,
          },
        },
        {
          id: faker.string.uuid(),
          lastSeen: faker.lorem.words(),
          enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
          vehicleInformation: {
            brand: 'Polestar',
            model: '2',
            modelVariant: 'Long range',
            vehicleRegistrationPlate: 'ABC123',
            displayName: 'My car',
            evDatabaseId: '123456',
          },
          chargeState: {
            batteryCapacity: 78,
            batteryLevelPercent: 78,
            chargeLimitPercent: 78,
            chargeRate: 78,
            chargeTimeRemaining: 78,
            isCharging: true,
            isFullyCharged: true,
            isPluggedIn: true,
            lastUpdated: '2021-08-12T09:00:00Z',
            maxCurrent: 32,
            powerDeliveryState: faker.helpers.arrayElement([
              'UNPLUGGED',
              'PLUGGED_IN:NO_POWER',
              'PLUGGED_IN:STOPPED',
              'PLUGGED_IN:COMPLETE',
              'PLUGGED_IN:CHARGING',
              'UNKNOWN',
              'PLUGGED_IN:INITIALIZING',
              'PLUGGED_IN:FAULT',
            ]),
            range: 300,
          },
          interventions: {
            all: faker.lorem.words(),
            chargeState: faker.lorem.words(),
            information: faker.lorem.words(),
          },
        },
      ])
    ),
  };
}

export function getGetVehicleById200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      chargeState: {
        batteryCapacity: 78,
      },
    },
    {
      id: faker.string.uuid(),
      lastSeen: faker.lorem.words(),
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      chargeState: {
        batteryCapacity: 78,
        batteryLevelPercent: 78,
        chargeLimitPercent: 78,
        chargeRate: 78,
        chargeTimeRemaining: 78,
        isCharging: true,
        isFullyCharged: true,
        isPluggedIn: true,
        lastUpdated: '2021-08-12T09:00:00Z',
        maxCurrent: 32,
        powerDeliveryState: faker.helpers.arrayElement([
          'UNPLUGGED',
          'PLUGGED_IN:NO_POWER',
          'PLUGGED_IN:STOPPED',
          'PLUGGED_IN:COMPLETE',
          'PLUGGED_IN:CHARGING',
          'UNKNOWN',
          'PLUGGED_IN:INITIALIZING',
          'PLUGGED_IN:FAULT',
        ]),
        range: 300,
      },
      interventions: {
        all: faker.lorem.words(),
        chargeState: faker.lorem.words(),
        information: faker.lorem.words(),
      },
    },
  ]);
}

export function getUpdateVehicleById200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      chargeState: {
        batteryCapacity: 78,
      },
    },
    {
      id: faker.string.uuid(),
      lastSeen: faker.lorem.words(),
      enodeUserId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      enodeVehicleId: 'efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b',
      vehicleInformation: {
        brand: 'Polestar',
        model: '2',
        modelVariant: 'Long range',
        vehicleRegistrationPlate: 'ABC123',
        displayName: 'My car',
        evDatabaseId: '123456',
      },
      chargeState: {
        batteryCapacity: 78,
        batteryLevelPercent: 78,
        chargeLimitPercent: 78,
        chargeRate: 78,
        chargeTimeRemaining: 78,
        isCharging: true,
        isFullyCharged: true,
        isPluggedIn: true,
        lastUpdated: '2021-08-12T09:00:00Z',
        maxCurrent: 32,
        powerDeliveryState: faker.helpers.arrayElement([
          'UNPLUGGED',
          'PLUGGED_IN:NO_POWER',
          'PLUGGED_IN:STOPPED',
          'PLUGGED_IN:COMPLETE',
          'PLUGGED_IN:CHARGING',
          'UNKNOWN',
          'PLUGGED_IN:INITIALIZING',
          'PLUGGED_IN:FAULT',
        ]),
        range: 300,
      },
      interventions: {
        all: faker.lorem.words(),
        chargeState: faker.lorem.words(),
        information: faker.lorem.words(),
      },
    },
  ]);
}

export function getGetAllVehicleInterventions200Response() {
  return {
    information: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      vendor: faker.lorem.words(),
      vendorType: faker.lorem.words(),
      brand: faker.lorem.words(),
      introducedAt: '2024-08-25T20:05:46.646Z',
      domain: faker.helpers.arrayElement(['Account', 'Device']),
      resolution: {
        title: 'Replace battery',
        description: 'Replace the battery with a new one',
        access: 'Physical',
        agent: 'Device',
      },
    })),
    chargeState: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      vendor: faker.lorem.words(),
      vendorType: faker.lorem.words(),
      brand: faker.lorem.words(),
      introducedAt: '2024-08-25T20:05:46.646Z',
      domain: faker.helpers.arrayElement(['Account', 'Device']),
      resolution: {
        title: 'Replace battery',
        description: 'Replace the battery with a new one',
        access: 'Physical',
        agent: 'Device',
      },
    })),
  };
}

export function getGetVehicleIntervention200Response() {
  return {
    id: faker.string.uuid(),
    vendor: faker.lorem.words(),
    vendorType: faker.lorem.words(),
    brand: faker.lorem.words(),
    introducedAt: '2024-08-25T20:05:46.646Z',
    domain: faker.helpers.arrayElement(['Account', 'Device']),
    resolution: {
      title: 'Replace battery',
      description: 'Replace the battery with a new one',
      access: 'Physical',
      agent: 'Device',
    },
  };
}

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}
