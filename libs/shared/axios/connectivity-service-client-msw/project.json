{"name": "shared-axios-connectivity-service-client-msw", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/connectivity-service-client-msw/src", "projectType": "library", "tags": ["shared"], "implicitDependencies": ["shared-axios-connectivity-service-client"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/libs/shared/axios/connectivity-service-client-msw/api-msw", "main": "libs/shared/axios/connectivity-service-client-msw/src/node.ts", "tsConfig": "libs/shared/axios/connectivity-service-client-msw/tsconfig.app.json", "webpackConfig": "libs/shared/axios/connectivity-service-client-msw/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "shared-axios-connectivity-service-client-msw:build"}, "configurations": {"development": {"buildTarget": "shared-axios-connectivity-service-client-msw:build:development"}, "production": {"buildTarget": "shared-axios-connectivity-service-client-msw:build:production"}}}, "generate-mocks": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Generate mocks using configuration file.", "command": "npx msw-auto-mock libs/shared/axios/connectivity-service-client/openapi.yml -o libs/shared/axios/connectivity-service-client-msw/src -c 200,201,202,204", "forwardAllArgs": false}, {"description": "Convert handlers.js to typescript", "command": "mv libs/shared/axios/connectivity-service-client-msw/src/handlers.js libs/shared/axios/connectivity-service-client-msw/src/handlers.ts", "forwardAllArgs": false}, {"description": "Convert and rename node.js to main.ts", "command": "mv libs/shared/axios/connectivity-service-client-msw/src/node.js libs/shared/axios/connectivity-service-client-msw/src/node.ts", "forwardAllArgs": false}, {"description": "<PERSON><PERSON> generated mocks.", "command": "npx nx lint shared-axios-connectivity-service-client-msw --fix", "forwardAllArgs": false}, {"description": "Format generated mocks.", "command": "npx prettier libs/shared/axios/connectivity-service-client-msw --write", "forwardAllArgs": false}], "parallel": false}, "outputs": ["{projectRoot}/src/models"]}}}