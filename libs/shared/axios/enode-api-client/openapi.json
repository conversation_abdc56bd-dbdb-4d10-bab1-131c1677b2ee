{"openapi": "3.1.0", "info": {"title": "Enode API", "version": "2024-01-01", "description": "The Enode API is designed to make smart charging applications easy to develop. We provide an abstraction layer that reduces the complexity when extracting vehicle data and sending commands to vehicles from a variety of manufacturers.\nThe API has a RESTful architecture and utilizes OAuth2 authorization.", "license": {"name": "Enode Commercial License", "url": "https://enode.io"}}, "tags": [{"name": "Chargers", "description": "EV Chargers provide charging data and can be controlled via [Control Charging](/api/reference#controlChargerCharging) and [Schedules](/docs/scheduling)"}, {"name": "Charging Locations", "description": "A user creates charging locations to denote locations where they pay for the power used to charge their vehicle, heat their home, etc. Smart Charging is active at these locations only."}, {"name": "Interventions", "description": "Endpoints that return information about interventions. More information and examples are available in the [Interventions](/docs/interventions) guide."}, {"name": "Locations", "description": "Locations are used to assign devices to a geographical group. This is useful when running schedules, smart charging, or smart heating, as target behavior usually depends on a common locality of demand (charging, heating, cooling, etc.) and supply (tariff, battery, inverter, etc.) of energy."}, {"name": "Experimental", "description": "Endpoint is not stable and may change without notice. Do not use in production applications."}, {"name": "HVAC", "description": "HVAC units (heaters, heat pumps, air conditioning, thermostats, etc.) are controlled by altering the mode & target setpoints. This can be done directly using the [Set Permanent Hold](/api/reference#postSetHvacPermanentHold) endpoint, [Return to Schedule](/api/reference#postSetHvacFollowSchedule), or via [Schedules](/docs/scheduling)."}, {"name": "Me", "description": "The `Me` endpoint returns metadata about the authenticated User."}, {"name": "Schedules", "description": "Endpoints to manage schedules for Vehicles, Chargers and HVACs. \n\nMore information and examples are available in the [Scheduling](/docs/scheduling) guide."}, {"name": "Service Health", "description": "Endpoints that return information about the health of <PERSON><PERSON> and our integrations."}, {"name": "Solar inverters", "description": "Solar inverters can be queried for current production state"}, {"name": "Statistics", "description": "Endpoints returning timeseries data collected from linked devices."}, {"name": "Tariffs", "description": "Use the Tariffs API to submit pricing information to Enode. These prices will be preferred over market prices in Enode's smart products, like [Smart HVAC](https://developers.enode.com/api/reference#updateHvacSmartPolicy), and will be used to calculate costs in [Statistics](https://developers.enode.com/api/reference#getChargingStatistics)."}, {"name": "User Management", "description": "Endpoints used to link and unlink users or vendors."}, {"name": "Vehicles", "description": "EVs provide charge, location, and odometer data. Vehicles can be controlled either directly using the [Control Charging](/api/reference#postVehiclesVehicleidCharging) endpoint, or through [Smart Charging](/docs/smart-charging/introduction) and [Schedules](/docs/scheduling)."}, {"name": "Webhooks", "description": "Webhooks are a mechanism that allows your server to receive notifications of events from the Enode system.\n\nSee the related guide for more information about timeouts, failures, and retry logic: [Using Webhooks](/docs/webhooks)"}, {"name": "Beta", "description": "Endpoint is in beta and may change without notice."}, {"name": "Meters", "description": "The `Meter` object represents a unit responsible for measuring energy usage. It provides detailed information about the meter itself and the energy consumption data it records."}, {"name": "Batteries", "description": "The `Battery` object represents a residential energy storage unit, like a Tesla Powerwall or Enphase IQ. It offers detailed insights into the battery's operation and allows you to instruct the battery on handling its stored energy."}], "servers": [{"url": "https://enode-api.production.enode.io"}], "components": {"securitySchemes": {"ClientAccessToken": {"type": "oauth2", "description": "A `ClientAccessToken` is obtained via the [OAuth 2.0 Client Credentials grant](https://www.oauth.com/oauth2-servers/access-tokens/client-credentials/) and carries authorization to access all functionalities and data in your Enode account.\nFull details at [The ClientAccessToken](/api/reference#getting-an-access-token)\n", "flows": {"clientCredentials": {"tokenUrl": "https://oauth.production.enode.io/oauth2/token", "scopes": {}}}}}, "schemas": {"BatteryVendor": {"type": "string", "enum": ["TESLA", "ENPHASE", "HUAWEI"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "TESLA"}, "BatteryChargeState": {"type": "object", "properties": {"status": {"type": ["string", "null"], "enum": ["CHARGING", "DISCHARGING", "IDLE", "FAULT", "UNKNOWN"], "description": "The power delivery state of the battery.", "example": "CHARGING"}, "batteryCapacity": {"type": ["number", "null"], "description": "Battery capacity in kWh.", "example": 13.5}, "batteryLevel": {"type": ["number", "null"], "description": "Remaining battery charge level in percent.", "example": 80}, "chargeRate": {"type": ["number", "null"], "description": "The current charge rate in kW. A positive value indicates that the battery is charging.", "example": 4.6}, "dischargeLimit": {"type": ["number", "null"], "description": "Minimum charge level for a battery, expressed as a percentage. Batteries will not discharge below this user-set reserve level except in emergency power situations.", "example": 20}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received charge state update", "example": "2020-04-07T17:04:26Z"}}, "required": ["status", "batteryCapacity", "batteryLevel", "chargeRate", "dischargeLimit", "lastUpdated"], "additionalProperties": false, "description": "Latest information about the battery. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "BatteryConfig": {"type": "object", "properties": {"operationMode": {"type": ["string", "null"], "enum": ["IMPORT_FOCUS", "EXPORT_FOCUS", "TIME_OF_USE", "SELF_RELIANCE"], "description": "The current operation mode of the battery.\n\n * `IMPORT_FOCUS`: Prioritizes charging the battery. Draws power from the grid and any excess solar for charging. \n * `EXPORT_FOCUS`: Prioritizes discharging energy stored in the battery back to the grid. \n * `TIME_OF_USE`: Maximizes energy cost savings in accordance with a user-defined utility rate plan. Energy may be consumed from solar, battery, or grid sources, depending on the current prices and your user's settings in the OEM app. Energy may be exported to the grid from solar or battery sources, depending on current prices and your user's settings in the OEM app. \n * `SELF_RELIANCE`: Minimizes household reliance on the grid. Prioritize using own energy from solar or battery for household consumption before importing from grid. Energy may be exported to the grid from solar, depending on excess solar and your user's settings in the OEM app. "}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received configuration update", "example": "2020-04-07T17:04:26Z"}}, "required": ["operationMode", "lastUpdated"], "additionalProperties": false}, "BatteryBrand": {"type": "string", "enum": ["Tesla", "Enphase", "HUAWEI"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Tesla"}, "BatteryInformation": {"type": "object", "properties": {"id": {"type": "string", "description": "Battery vendor ID", "example": "7deb27f8-794f-467b-855e-5c61dd9f2cb3"}, "brand": {"allOf": [{"$ref": "#/components/schemas/BatteryBrand"}, {"description": "Battery brand"}]}, "model": {"type": "string", "description": "Battery model", "example": "Powerwall"}, "siteName": {"type": "string", "description": "Name of the site, as set by the user on the device/vendor. If no user-specified name is available, we construct a fallback name using the vendor/device/model names.", "example": "Powerwall Home"}, "installationDate": {"type": "string", "format": "date-time", "description": "Battery installation date", "example": "2020-04-07T17:04:26Z"}}, "required": ["id", "brand", "model", "siteName", "installationDate"], "additionalProperties": false, "description": "Descriptive information about the battery"}, "BatteryLocation": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees", "example": 10.7197486}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees", "example": 59.9173985}}, "required": ["longitude", "latitude"], "additionalProperties": false, "description": "Battery's GPS coordinates"}, "Capability": {"type": "object", "properties": {"isCapable": {"type": "boolean", "description": "The entity is fully capable of providing this data or functionality. If false, support is partial or missing.", "example": false}, "interventionIds": {"type": "array", "items": {"type": "string", "format": "uuid", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "description": "IDs of interventions the user can make to alter the availability of this capability. Please refer to [the interventions guide](/docs/interventions#accessing-interventions-via-our-api) for more information."}}, "required": ["isCapable", "interventionIds"], "additionalProperties": false}, "BatteryCapabilities": {"type": "object", "properties": {"exportFocus": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports EXPORT_FOCUS operation mode."}]}, "importFocus": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports IMPORT_FOCUS operation mode."}]}, "timeOfUse": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports TIME_OF_USE operation mode."}]}, "selfReliance": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports SELF_RELIANCE operation mode."}]}}, "required": ["exportFocus", "importFocus", "timeOfUse", "selfReliance"], "additionalProperties": false, "description": "A collection of descriptors that describe the capabilities of this specific battery"}, "PaginationCursors": {"type": "object", "properties": {"after": {"type": ["string", "null"], "example": "MjAyMy0wNy0xOFQxMDowODowMi4zNzNa"}, "before": {"type": ["string", "null"], "example": "MjAyMy0wNi0xNlQwOTowMzowMS4yNjJa"}}, "required": ["after", "before"], "additionalProperties": false, "description": "Cursors to the pages before and after current page. See the [Pagination](/api/reference#pagination) section for reference."}, "PaginatedBatteryList": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the battery object", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "userId": {"type": "string", "description": "The ID of the user that linked this battery.", "example": "4f6fecd0-bdae-49be-b6e8-ee442e1e3da9"}, "vendor": {"$ref": "#/components/schemas/BatteryVendor"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the battery is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the battery was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the battery is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the battery's cloud service if that service has determined that its cache is valid."}, "chargeState": {"$ref": "#/components/schemas/BatteryChargeState"}, "config": {"$ref": "#/components/schemas/BatteryConfig"}, "information": {"$ref": "#/components/schemas/BatteryInformation"}, "location": {"$ref": "#/components/schemas/BatteryLocation"}, "capabilities": {"$ref": "#/components/schemas/BatteryCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this battery."}}, "required": ["id", "userId", "vendor", "locationId", "lastSeen", "isReachable", "chargeState", "config", "information", "location", "capabilities", "scopes"], "additionalProperties": false}, "description": "Paginated list of batteries"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of batteries"}, "setBatteryOperationModePayload": {"type": "object", "properties": {"operationMode": {"type": "string", "enum": ["IMPORT_FOCUS", "EXPORT_FOCUS", "TIME_OF_USE", "SELF_RELIANCE"], "description": "Desired operation mode of the battery.\n\n * `IMPORT_FOCUS`: Prioritizes charging the battery. Draws power from the grid and any excess solar for charging. \n * `EXPORT_FOCUS`: Prioritizes discharging energy stored in the battery back to the grid. \n * `TIME_OF_USE`: Maximizes energy cost savings in accordance with a user-defined utility rate plan. Energy may be consumed from solar, battery, or grid sources, depending on the current prices and your user's settings in the OEM app. Energy may be exported to the grid from solar or battery sources, depending on current prices and your user's settings in the OEM app. \n * `SELF_RELIANCE`: Minimizes household reliance on the grid. Prioritize using own energy from solar or battery for household consumption before importing from grid. Energy may be exported to the grid from solar, depending on excess solar and your user's settings in the OEM app. ", "example": "IMPORT_FOCUS"}}, "required": ["operationMode"], "additionalProperties": false, "description": "Target battery operation mode"}, "ActionState": {"type": "string", "enum": ["PENDING", "CONFIRMED", "FAILED", "CANCELLED"], "description": "The real-time status of an action executed on a target. \n\n * `PENDING`: The initial state. Enode is actively sending commands and monitoring the target for changes. \n * `CONFIRMED`: Successful transition of the target to the desired state. \n * `FAILED`: The target did not respond to the action before timing out. Enode has ceased sending additional commands. \n * `CANCELLED`: A required precondition was not met during the action's timeout window or another action has been sent to the target, overriding this one."}, "OperationModeAction": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the battery which this action is controlling.", "example": "ac03a513-5494-4e1c-9dd7-2a29dc024312"}, "targetType": {"type": "string", "enum": ["battery"]}, "targetState": {"$ref": "#/components/schemas/setBatteryOperationModePayload"}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetType", "targetState"], "additionalProperties": false, "title": "Battery Operation Mode Action"}, "Problem": {"type": "object", "properties": {"type": {"type": "string", "description": "A URI reference that identifies the problem type."}, "title": {"type": "string", "description": "A short, human-readable summary of the problem type."}, "detail": {"type": "string", "description": "A human-readable explanation specific to this occurrence of the problem."}}, "required": ["type", "title", "detail"], "additionalProperties": false, "description": "Standard Problem Error", "example": {"type": "https://docs.enode.io/problems/im-a-teapot", "title": "I'm a teapot", "detail": "The requested entity body is short and stout."}}, "ChargerVendor": {"type": "string", "enum": ["ZAPTEC", "EASEE", "WALLBOX", "EO", "CHARGEAMPS", "EVBOX", "GOE", "FRONIUS", "CHARGEPOINT", "ENELX", "TESLA", "OHME", "GARO"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "ZAPTEC"}, "ChargerPowerDeliveryState": {"type": "string", "enum": ["UNKNOWN", "UNPLUGGED", "PLUGGED_IN:INITIALIZING", "PLUGGED_IN:CHARGING", "PLUGGED_IN:STOPPED", "PLUGGED_IN:NO_POWER", "PLUGGED_IN:FAULT"], "description": "The current state of power delivery between the vehicle and charger.\n\n * `UNKNOWN`: The state of power delivery is currently unknown. \n * `UNPLUGGED`: The vehicle is not connected to the charger. \n * `PLUGGED_IN:INITIALIZING`: The charging station is preparing to deliver power to the vehicle. It is expected for this state to shortly transition into PLUGGED_IN:CHARGING. \n * `PLUGGED_IN:CHARGING`: The charger is actively delivering power to the vehicle, causing the battery level to increase. \n * `PLUGGED_IN:STOPPED`: The vehicle is plugged in, but the charger has been stopped. It is possible to transition into a charging state by sending a start command.\n * `PLUGGED_IN:NO_POWER`: The charger attempted to initialize charging, however no external power was accepted by the vehicle. It is not possible to transition into a charging state with a remote command until there is some user intervention to resolve the issue. \n * `PLUGGED_IN:FAULT`: A malfunction in the charging process is preventing power from being delivered. Possible causes include a charging cable not being properly locked, extreme temperatures, or malfunctions in either the charging station or the vehicle's internal system. It is not possible to transition into a charging state with a remote command until there is some user intervention to resolve the issue.", "example": "UNPLUGGED"}, "ChargerChargeState": {"type": "object", "properties": {"isPluggedIn": {"type": ["boolean", "null"], "description": "Indicates whether the charger has a vehicle plugged into it (regardless of whether that vehicle is actually charging)"}, "isCharging": {"type": ["boolean", "null"], "description": "Indicates whether the charger is currently delivering power to the vehicle and actively charging its battery."}, "chargeRate": {"type": ["number", "null"], "description": "The current charge rate in kW.\n\nThis property is only available when the charger is actively charging a vehicle, and is `null` any other time."}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of latest charge state update", "example": "2020-04-07T17:04:26Z"}, "maxCurrent": {"type": ["number", "null"], "description": "Desired max current in amperes, if set"}, "powerDeliveryState": {"$ref": "#/components/schemas/ChargerPowerDeliveryState"}}, "required": ["isPluggedIn", "isCharging", "chargeRate", "lastUpdated", "max<PERSON><PERSON><PERSON>", "powerDeliveryState"], "additionalProperties": false, "description": "Latest information about the charger. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "ChargerBrand": {"type": "string", "enum": ["Zaptec", "<PERSON><PERSON><PERSON>", "Wallbox", "EO", "EVBox", "Charge Amps", "go-e", "<PERSON><PERSON><PERSON>", "ChargePoint", "Enel X", "Tesla", "Ohme", "Garo"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "<PERSON><PERSON><PERSON>"}, "ChargerInformation": {"type": "object", "properties": {"brand": {"allOf": [{"$ref": "#/components/schemas/ChargerBrand"}, {"description": "Charger brand"}]}, "model": {"type": "string", "description": "Charger model", "example": "Home"}, "year": {"type": ["number", "null"], "description": "Charger production year"}}, "required": ["brand", "model", "year"], "additionalProperties": false, "description": "Descriptive information about the Charger"}, "ChargerCapabilities": {"type": "object", "properties": {"information": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of information data."}]}, "chargeState": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of chargeState data."}]}, "startCharging": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports START charging command."}]}, "stopCharging": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports STOP charging command."}]}, "setMaxCurrent": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Ability to set the max charge rate."}]}}, "required": ["information", "chargeState", "startCharging", "stopCharging", "setMaxCurrent"], "additionalProperties": false, "description": "A collection of descriptors that describe the capabilities of this specific charger"}, "Scopes": {"type": "string", "enum": ["battery:control:operation_mode", "battery:read:data", "battery:read:location", "charger:control:charging", "charger:read:data", "hvac:control:mode", "hvac:read:data", "inverter:read:data", "inverter:read:location", "meter:read:data", "meter:read:location", "vehicle:control:charging", "vehicle:read:data", "vehicle:read:location"]}, "Charger": {"type": "object", "properties": {"id": {"type": "string", "description": "Charger ID"}, "userId": {"type": "string", "description": "The ID of the user that linked this charger."}, "vendor": {"$ref": "#/components/schemas/ChargerVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time <PERSON><PERSON> successfully communicated with the charger.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the charger is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the charger's cloud service if that service has determined that its cache is valid."}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the charger is currently positioned at (if any).", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "chargeState": {"$ref": "#/components/schemas/ChargerChargeState"}, "information": {"$ref": "#/components/schemas/ChargerInformation"}, "capabilities": {"$ref": "#/components/schemas/ChargerCapabilities"}, "scopes": {"type": "array", "items": {"$ref": "#/components/schemas/Scopes"}, "description": "Scopes that the user has granted for this charger."}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "locationId", "chargeState", "information", "capabilities", "scopes"], "additionalProperties": false, "example": {"id": "2211e263-0362-4235-83f4-887bdf3ee414", "userId": "17d9f847-8a1c-4158-adaa-4911a7acd5f9", "vendor": "ZAPTEC", "lastSeen": "2023-03-21T21:08:27.596Z", "isReachable": true, "locationId": "2211e263-d6d4-d6d4-d6d4-dbdd77ec82b6", "chargeState": {"isPluggedIn": true, "isCharging": true, "chargeRate": 6.939, "lastUpdated": "2023-03-21T16:39:20.000Z", "maxCurrent": 16, "powerDeliveryState": "PLUGGED_IN:CHARGING"}, "information": {"brand": "Zaptec", "model": "ZAPTEC PRO", "year": null}, "capabilities": {"information": {"isCapable": true, "interventionIds": []}, "chargeState": {"isCapable": true, "interventionIds": []}, "startCharging": {"isCapable": true, "interventionIds": []}, "stopCharging": {"isCapable": true, "interventionIds": []}, "setMaxCurrent": {"isCapable": false, "interventionIds": ["dbdd77ec82b6-d6d4-d6d4-d6d4-dbdd77ec82b6"]}}, "scopes": ["charger:control:charging", "charger:read:data"]}}, "PaginatedChargerList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Charger"}, "description": "Paginated list of chargers"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of chargers"}, "ChargerUpdatePayload": {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}, "ChargingAction": {"type": "string", "enum": ["START", "STOP"]}, "ChargeableVendorType": {"type": "string", "enum": ["vehicle", "charger"]}, "ChargingActionFailureReason": {"type": "object", "properties": {"type": {"type": "string", "enum": ["NO_RESPONSE", "FAILED_PRECONDITION", "CONFLICT", "NOT_FOUND", "UNNECESSARY", "REQUESTED_CANCELLATION"], "description": "A machine-readable high level error category.\n\n * `NO_RESPONSE`: The chargeable device did not react to our charge commands within the action's timeout window.  \n * `FAILED_PRECONDITION`: The chargeable device did not meet all required preconditions for this action to be executed during the action's timeout window. \n * `UNNECESSARY`: The action was not carried out given that the device was already in the desired state. \n * `CONFLICT`: A newer action for this chargeable has been created. This action is now abandoned.  \n * `REQUESTED_CANCELLATION`: This action was cancelled by request of the controlling owner. The controlling owner may refer to another Enode entity which initiated the command, such as a schedule or smart override. \n * `NOT_FOUND`: The chargeable was deleted while the action was `PENDING`.\n"}, "detail": {"type": "string", "description": "A human-readable explanation of why the charging action was unsuccessful.", "example": "The chargeable device remained unreachable."}}, "required": ["type", "detail"], "additionalProperties": false, "description": "Information about why was this action not executed successfully."}, "ChargeAction": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the target which this action is controlling.", "example": "bfbccded-8a1c-45a8-bbda-dcaeef29977a"}, "targetType": {"$ref": "#/components/schemas/ChargeableVendorType"}, "kind": {"allOf": [{"$ref": "#/components/schemas/ChargingAction"}, {"description": "The charging action to perform"}]}, "failureReason": {"allOf": [{"$ref": "#/components/schemas/ChargingActionFailureReason"}, {"type": ["object", "null"]}]}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetType", "kind", "failureReason"], "additionalProperties": false, "title": "Charge Action"}, "TargetMaxCurrent": {"type": "object", "properties": {"maxCurrent": {"type": "number", "exclusiveMinimum": 0, "description": "Desired max current in ampere", "example": 10}}, "required": ["max<PERSON><PERSON><PERSON>"], "additionalProperties": false, "description": "Target maximum current for entity"}, "MaxCurrentAction": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the entity which this action is controlling."}, "targetType": {"$ref": "#/components/schemas/ChargeableVendorType"}, "targetState": {"$ref": "#/components/schemas/TargetMaxCurrent"}, "failureReason": {"allOf": [{"$ref": "#/components/schemas/ChargingActionFailureReason"}, {"type": ["object", "null"]}]}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetType", "targetState", "failureReason"], "additionalProperties": false, "title": "Max Current Action"}, "ChargerAction": {"anyOf": [{"$ref": "#/components/schemas/ChargeAction"}, {"$ref": "#/components/schemas/MaxCurrentAction"}]}, "SmartOverrideStatus": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time", "description": "Time at which the smart override was created.", "example": "2020-04-07T17:04:26Z"}, "endedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time at which the smart override was ended. If null, the smart override is still active.", "example": "2020-04-07T17:04:26Z"}, "targetType": {"allOf": [{"$ref": "#/components/schemas/ChargeableVendorType"}, {"example": "vehicle"}]}, "targetId": {"type": "string", "description": "ID of the target which this smart override is affecting.", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3"}, "vendorActionId": {"type": ["string", "null"], "format": "uuid", "description": "The ID of the Action responsible for starting charging on the target. Use the [Get Vehicle Action](/api/reference#getVehiclesAction) or the [Get Charger Action](/api/reference#getChargersAction) endpoints to monitor action results.", "example": "213ae0a8-fb65-40be-981a-6a86df3e1c7f", "x-format": {"guid": true}}}, "required": ["createdAt", "endedAt", "targetType", "targetId", "vendorActionId"], "additionalProperties": false, "description": "This field populates after using Vehicle [Create Smart Override](/api/reference#vehicleCreateSmartOverride) or Charger [Create Smart Override](/api/reference#chargerCreateSmartOverride). Once populated, the target enters an overridden state, stopping Enode from sending Smart Charging actions. The Smart Override persists until the target ceases charging for any reason or is manually ended via Vehicle [End Smart Override](/api/reference#vehicleEndSmartOverride) or Charger [End Smart Override](/api/reference#chargerEndSmartOverride)."}, "VehicleVendor": {"type": "string", "enum": ["AUDI", "BMW", "HONDA", "HYUNDAI", "JAGUAR", "LANDROVER", "KIA", "MERCEDES", "MINI", "NISSAN", "PEUGEOT", "PORSCHE", "RENAULT", "SEAT", "SKODA", "TESLA", "VOLKSWAGEN", "VOLVO", "FORD", "OPEL", "DS", "TOYOTA", "LEXUS", "CITROEN", "CUPRA", "VAUXHALL", "FIAT", "RIVIAN", "NIO", "CHEVROLET", "GMC", "CADILLAC", "XPENG"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "TESLA"}, "ChargeableVendor": {"anyOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}]}, "ChargerOnlySmartOverrideResponse": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time", "description": "Time at which the smart override was created.", "example": "2020-04-07T17:04:26Z"}, "endedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time at which the smart override was ended. If null, the smart override is still active.", "example": "2020-04-07T17:04:26Z"}, "targetId": {"type": "string", "description": "ID of the target which this smart override is affecting.", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3"}, "vendorActionId": {"type": ["string", "null"], "format": "uuid", "description": "The ID of the Action responsible for starting charging on the target. Use the [Get Vehicle Action](/api/reference#getVehiclesAction) or the [Get Charger Action](/api/reference#getChargersAction) endpoints to monitor action results.", "example": "213ae0a8-fb65-40be-981a-6a86df3e1c7f", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "ID of the User", "example": "0bec82e0-0d54-4f2f-83b1-5b248604de0b"}, "vendor": {"$ref": "#/components/schemas/ChargeableVendor"}, "targetType": {"type": "string", "enum": ["charger"]}}, "required": ["createdAt", "endedAt", "targetId", "vendorActionId", "userId", "vendor", "targetType"], "additionalProperties": false}, "PartialChargerSmartChargingPolicy": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "default": false, "description": "When enabled, this charger's charging status may be controlled by [Smart Charging](/docs/smart-charging/introduction)"}, "deadline": {"type": "string", "description": "The `HH:MM` deadline for the cycle. If a timezone is set on the charging location at which the smart charging occurs, the deadline is interpreted in that timezone, otherwise UTC is used.", "pattern": "^\\d{2}:\\d{2}$", "example": "07:00"}, "chargingDuration": {"type": "string", "description": "The `HH:MM` charging duration for each cycle.", "pattern": "^\\d{2}:\\d{2}$", "example": "07:00"}}, "additionalProperties": false, "title": "Partial Smart Charging Policy"}, "ChargerSmartChargingPolicy": {"type": "object", "title": "Partial Smart Charging Policy", "allOf": [{"$ref": "#/components/schemas/PartialChargerSmartChargingPolicy"}, {"type": "object", "title": "Smart Charging Policy", "description": "Smart Charging configuration properties. Configured via the [Update Smart Charging Policy](/api/reference#updateChargerSmartChargingPolicy) endpoint.", "required": ["isEnabled", "deadline", "chargingDuration"]}]}, "ChargerState": {"type": "string", "enum": ["DISABLED", "CHARGER_NOT_REACHABLE", "VEHICLE_NOT_PLUGGED_IN", "CHARGING_PAUSED", "CHARGING", "AWAITING_PRICES"], "description": "An enum value that describes the current smart charging state of the charger. Every charger is in exactly one state, at all times"}, "ChargerSmartChargingIntervals": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "enum": ["IN_PROGRESS", "COMPLETED", "PLANNED"]}, "startTime": {"type": "string", "description": "The start time of this charging interval"}, "endTime": {"type": "string", "description": "The end time of this charging interval"}}, "required": ["status", "startTime", "endTime"], "additionalProperties": false}}, "HvacVendor": {"type": "string", "enum": ["TADO", "MILL", "ADAX", "ECOBEE", "SENSIBO", "HONEYWELL", "RESIDEO", "MITSUBISHI", "MICROMATIC", "NIBE", "PANASONIC", "TOSHIBA", "DAIKIN", "NEST", "FUJITSU", "BOSCH", "NETATMO"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "ADAX"}, "HvacBrand": {"type": "string", "enum": ["<PERSON><PERSON>", "Mill", "ADAX", "Ecobee", "Sensibo", "Honeywell TCC", "Resideo", "Mitsubishi", "Micro Matic", "NIBE", "Panasonic", "Toshiba", "DAIKIN", "Nest", "Fujitsu", "<PERSON><PERSON>", "Netatmo"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Honeywell TCC"}, "HvacMode": {"type": "string", "enum": ["OFF", "AUTO", "COOL", "HEAT"]}, "HvacHoldType": {"type": "string", "enum": ["PERMANENT", "SCHEDULED"]}, "NullableTemperatureRange": {"type": ["object", "null"], "properties": {"min": {"type": ["number", "null"], "description": "The minimum allowable temperature, inclusive."}, "max": {"type": ["number", "null"], "description": "The maximum allowable temperature, inclusive."}}, "required": ["min", "max"], "additionalProperties": false}, "NullableTemperatureDifferenceRange": {"type": ["object", "null"], "properties": {"min": {"type": ["number", "null"], "description": "The minimum allowable difference, inclusive."}, "max": {"type": ["number", "null"], "description": "The maximum allowable difference, inclusive."}}, "required": ["min", "max"], "additionalProperties": false}, "Hvac": {"type": "object", "properties": {"id": {"type": "string", "description": "HVAC unit ID"}, "userId": {"type": "string", "description": "The ID of the user that linked this hvac."}, "vendor": {"$ref": "#/components/schemas/HvacVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the HVAC unit was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the HVAC unit is currently reachable from Enode's perspective. It can happen that this 'reachability' refers to reading from a cache operated by the vendor's cloud service, if that service has determined that its cache is valid."}, "consumptionRate": {"type": ["number", "null"], "description": "The current rate of energy consumption in kW. An inactive HVAC will have a consumption rate of 0. HVACs not supporting consumption rate will report null. This value is currently only supported by a small number of devices."}, "information": {"type": "object", "properties": {"brand": {"allOf": [{"$ref": "#/components/schemas/HvacBrand"}, {"description": "Display name of OEM/vendor"}]}, "model": {"type": ["string", "null"], "description": "Device model name"}, "displayName": {"type": "string", "description": "Name of the device, as set by the user on the device/vendor. If no user-specified name is available, we construct a fallback name using the vendor/device/model names."}, "groupName": {"type": ["string", "null"], "description": "Name of the group the device belongs to, as set by the user on the device/vendor. Groups are typically presented as \"rooms\" or \"zones\"."}, "category": {"type": "string", "enum": ["HEATING", "COOLING", "HEAT_PUMP", "AGGREGATOR"], "description": "Classification of the connected HVAC device.\n\n * `HEATING`: A direct heating device, such as an electric panel heater\n\n * `COOLING`: A direct cooling device, such as an air conditioner \n\n * `HEAT_PUMP`: An air-to-air heat pump capable of both heating and cooling\n\n * `AGGREGATOR`: A device that is capable of controlling various HVAC systems behind it, such as a smart thermostat"}}, "required": ["brand", "model", "displayName", "groupName", "category"], "additionalProperties": false, "description": "Descriptive information about the HVAC unit"}, "capabilities": {"type": "object", "properties": {"capableModes": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/HvacMode"}, "description": "A list of valid modes for this HVAC unit."}, "capableHoldTypes": {"type": ["array", "null"], "items": {"$ref": "#/components/schemas/HvacHoldType"}, "description": "A list of valid hold types for this HVAC unit.\n\nDeprecated: Check the `setFollowSchedule` and `setPermanentHold` capabilities instead.", "deprecated": true}, "coolSetpointRange": {"allOf": [{"$ref": "#/components/schemas/NullableTemperatureRange"}, {"description": "The range of allowable values for `coolSetpoint`."}]}, "heatSetpointRange": {"allOf": [{"$ref": "#/components/schemas/NullableTemperatureRange"}, {"description": "The range of allowable values for `heatSetpoint`."}]}, "setpointDifferenceRange": {"allOf": [{"$ref": "#/components/schemas/NullableTemperatureDifferenceRange"}, {"description": "A constraint specifying the minimum and maximum allowable difference between `heatSetpoint` and `coolSetpoint`. Only applicable in `AUTO` mode."}]}, "setFollowSchedule": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports following a schedule set on the device."}]}, "setPermanentHold": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports setting a permanent hold."}]}}, "required": ["capableModes", "capableHoldTypes", "coolSetpointRange", "heatSetpointRange", "setpointDifferenceRange", "setFollowSchedule", "setPermanentHold"], "additionalProperties": false, "description": "An object describing valid states for this HVAC unit."}, "temperatureState": {"type": "object", "properties": {"currentTemperature": {"type": ["number", "null"], "description": "Current air temperature reported by device in degrees Celsius."}, "isActive": {"type": "boolean", "description": "Whether the HVAC unit is actively heating or cooling."}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last temperature state update. Reflects when the OEM reported a change or Enode recorded a change in any field, whichever is newer", "example": "2020-04-07T17:04:26Z"}}, "required": ["currentTemperature", "isActive", "lastUpdated"], "additionalProperties": false, "description": "Latest information about temperature. These values replace the deprecated top-level fields and include a freshness indicator. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "thermostatState": {"type": "object", "properties": {"mode": {"allOf": [{"$ref": "#/components/schemas/HvacMode"}, {"type": ["string", "null"], "description": "The HVAC's mode."}]}, "heatSetpoint": {"type": ["number", "null"], "description": "If mode allows, heat when `currentTemperature` falls below this point."}, "coolSetpoint": {"type": ["number", "null"], "description": "If mode allows, cool when `currentTemperature` rises above this point."}, "holdType": {"allOf": [{"$ref": "#/components/schemas/HvacHoldType"}, {"type": ["string", "null"], "description": "The duration the setpoints and mode are expected to be held. If `SCHEDULED`, the device is being controlled by an OEM schedule configured on the device."}]}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last thermostat state update. Reflects when the OEM reported a change or Enode recorded a change in any field, whichever is newer.", "example": "2020-04-07T17:04:26Z"}}, "required": ["mode", "heatSetpoint", "coolSetpoint", "holdType", "lastUpdated"], "additionalProperties": false, "description": "Latest information about the thermostat state. These values replace the deprecated top-level fields and include a freshness indicator. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "mode": {"allOf": [{"$ref": "#/components/schemas/HvacMode"}, {"type": ["string", "null"], "description": "Replaced by `thermostatState.mode`. The HVAC's mode.", "deprecated": true}]}, "heatSetpoint": {"type": ["number", "null"], "description": "Replaced by `thermostatState.heatSetpoint`. If mode allows, heat when `currentTemperature` falls below this point.", "deprecated": true}, "coolSetpoint": {"type": ["number", "null"], "description": "Replaced by `thermostatState.coolSetpoint`. If mode allows, cool when `currentTemperature` rises above this point.", "deprecated": true}, "holdType": {"allOf": [{"$ref": "#/components/schemas/HvacHoldType"}, {"type": ["string", "null"], "description": "Replaced by `thermostatState.holdType`. The duration the setpoints and mode are expected to be held. If `SCHEDULED`, the device is being controlled by an OEM schedule configured on the device.", "deprecated": true}]}, "isActive": {"type": "boolean", "description": "Replaced by `temperatureState.isActive`. Whether the HVAC unit is actively heating or cooling.", "deprecated": true}, "currentTemperature": {"type": ["number", "null"], "description": "Replaced by `temperatureState.currentTemperature`. Current air temperature reported by device in degrees Celsius.", "deprecated": true}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this HVAC unit."}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the HVAC unit is housed at (if any)", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "consumptionRate", "information", "capabilities", "temperatureState", "thermostatState", "mode", "heatSetpoint", "coolSetpoint", "holdType", "isActive", "currentTemperature", "scopes", "locationId"], "additionalProperties": false, "example": {"id": "8f39fa8d-8f10-4984-a319-741dc23848c0", "userId": "17d9f847-8a1c-4158-adaa-4911a7acd5f9", "vendor": "ADAX", "lastSeen": "2020-04-07T17:04:26.000Z", "isReachable": true, "isActive": true, "currentTemperature": 20.8, "consumptionRate": 1.8, "mode": "HEAT", "heatSetpoint": 22, "coolSetpoint": 24, "holdType": "PERMANENT", "information": {"brand": "ADAX", "model": "Neo Wi-Fi Skirting", "displayName": "Bedroom Panel Heater", "groupName": "Bedroom", "category": "HEATING"}, "capabilities": {"capableModes": ["HEAT", "COOL", "OFF"], "capableHoldTypes": ["PERMANENT"], "coolSetpointRange": {"min": 15, "max": 25}, "heatSetpointRange": {"min": 15, "max": 25}, "setpointDifferenceRange": {"min": 15, "max": 25}, "setFollowSchedule": {"isCapable": true, "interventionIds": []}, "setPermanentHold": {"isCapable": true, "interventionIds": []}}, "thermostatState": {"mode": "HEAT", "heatSetpoint": 22, "coolSetpoint": 24, "holdType": "PERMANENT", "lastUpdated": "2020-04-07T17:04:26.000Z"}, "temperatureState": {"currentTemperature": 20.8, "isActive": true, "lastUpdated": "2020-04-07T17:03:26.000Z"}, "scopes": ["hvac:control:mode", "hvac:read:data"], "locationId": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe"}}, "PaginatedHVACList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Hvac"}, "description": "List of paginated HVAC units"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of HVAC units"}, "PartialHvacSmartPolicy": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "default": false, "description": "When enabled, this HVAC unit is under smart control."}}, "additionalProperties": false}, "HvacSmartPolicy": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PartialHvacSmartPolicy"}, {"type": "object", "required": ["isEnabled"]}]}, "HvacSmartInterval": {"type": "object", "properties": {"from": {"type": "string", "description": "Clock time from which this rule should apply each day.", "pattern": "^\\d{2}:\\d{2}$", "example": "10:00"}, "to": {"type": "string", "description": "Clock time until which this rule should apply each day.", "pattern": "^\\d{2}:\\d{2}$", "example": "11:00"}, "heatSetpoint": {"type": "number", "description": "Heat setpoint for the interval.", "example": 20}}, "required": ["from", "to", "heatSetpoint"], "additionalProperties": false}, "HvacSmartStatus": {"type": "object", "properties": {"hvacId": {"type": "string", "description": "ID of the HVAC unit.", "example": "8f39fa8d-8f10-4984-a319-741dc23848c0"}, "userId": {"type": "string", "description": "ID of the user who owns the HVAC unit.", "example": "8f39fa8d-8f10-4984-a319-741dc23848c0"}, "intervals": {"type": "array", "items": {"$ref": "#/components/schemas/HvacSmartInterval"}}}, "required": ["hvacId", "userId", "intervals"], "additionalProperties": false}, "hvacUpdatePayload": {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}, "CoolTargetState": {"type": "object", "properties": {"coolSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["COOL"]}, "holdType": {"type": "string", "enum": ["PERMANENT"]}}, "required": ["coolSetpoint", "mode", "holdType"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "COOL"}, "HeatTargetState": {"type": "object", "properties": {"heatSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["HEAT"]}, "holdType": {"type": "string", "enum": ["PERMANENT"]}}, "required": ["heatSetpoint", "mode", "holdType"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "HEAT"}, "AutoTargetState": {"type": "object", "properties": {"coolSetpoint": {"type": "number"}, "heatSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["AUTO"]}, "holdType": {"type": "string", "enum": ["PERMANENT"]}}, "required": ["coolSetpoint", "heatSetpoint", "mode", "holdType"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "AUTO"}, "OffTargetState": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["OFF"]}, "holdType": {"type": "string", "enum": ["PERMANENT"]}}, "required": ["mode", "holdType"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "OFF"}, "PermanentHoldTargetState": {"oneOf": [{"$ref": "#/components/schemas/CoolTargetState"}, {"$ref": "#/components/schemas/HeatTargetState"}, {"$ref": "#/components/schemas/AutoTargetState"}, {"$ref": "#/components/schemas/OffTargetState"}], "discriminator": {"propertyName": "mode", "mapping": {"COOL": "#/components/schemas/CoolTargetState", "HEAT": "#/components/schemas/HeatTargetState", "AUTO": "#/components/schemas/AutoTargetState", "OFF": "#/components/schemas/OffTargetState"}}, "title": "Permanent Hold"}, "HvacActionPermanentHold": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the entity asset (HVAC) which this action is controlling."}, "targetType": {"type": "string", "enum": ["hvac"]}, "target": {"$ref": "#/components/schemas/PermanentHoldTargetState"}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetType", "target"], "additionalProperties": false, "title": "Permanent Hold Action"}, "ScheduledTargetState": {"type": "object", "properties": {"holdType": {"type": "string", "enum": ["SCHEDULED"]}}, "required": ["holdType"], "additionalProperties": false, "title": "Follow Device Schedule"}, "HvacActionFollowSchedule": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the entity asset (HVAC) which this action is controlling."}, "targetType": {"type": "string", "enum": ["hvac"]}, "target": {"$ref": "#/components/schemas/ScheduledTargetState"}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetType", "target"], "additionalProperties": false, "title": "Follow Schedule Action"}, "HvacAction": {"anyOf": [{"$ref": "#/components/schemas/HvacActionPermanentHold"}, {"$ref": "#/components/schemas/HvacActionFollowSchedule"}]}, "CoolPermanentHold": {"type": "object", "properties": {"coolSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["COOL"]}}, "required": ["coolSetpoint", "mode"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "COOL"}, "HeatPermanentHold": {"type": "object", "properties": {"heatSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["HEAT"]}}, "required": ["heatSetpoint", "mode"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "HEAT"}, "AutoPermanentHold": {"type": "object", "properties": {"coolSetpoint": {"type": "number"}, "heatSetpoint": {"type": "number"}, "mode": {"type": "string", "enum": ["AUTO"]}}, "required": ["coolSetpoint", "heatSetpoint", "mode"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "AUTO"}, "OffPermanentHold": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["OFF"]}}, "required": ["mode"], "additionalProperties": false, "description": "Target mode for an HVAC unit", "title": "OFF"}, "HVACSetPermanentHoldPayload": {"oneOf": [{"$ref": "#/components/schemas/CoolPermanentHold"}, {"$ref": "#/components/schemas/HeatPermanentHold"}, {"$ref": "#/components/schemas/AutoPermanentHold"}, {"$ref": "#/components/schemas/OffPermanentHold"}], "discriminator": {"propertyName": "mode", "mapping": {"COOL": "#/components/schemas/CoolPermanentHold", "HEAT": "#/components/schemas/HeatPermanentHold", "AUTO": "#/components/schemas/AutoPermanentHold", "OFF": "#/components/schemas/OffPermanentHold"}}, "title": "Permanent Hold"}, "VendorType": {"type": "string", "enum": ["vehicle", "charger", "hvac", "inverter", "battery", "meter"], "example": "vehicle"}, "InverterVendor": {"type": "string", "enum": ["APSYSTEMS", "CSISolar", "<PERSON><PERSON>", "ENPHASE", "FOXESS", "FRONIUS", "GOODWE", "GROWATT", "<PERSON><PERSON><PERSON>", "HUAWEI", "INVT", "SMA", "SOFAR", "SOLAREDGE", "SOLARK", "SOLAX", "SOLIS", "SOLPLANET", "SUNGROW", "SUNSYNK", "TESLA", "TSUN"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "ENPHASE"}, "MeterVendor": {"type": "string", "enum": ["ENPHASE", "HUAWEI", "TESLA", "SOLAREDGE"], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "TESLA"}, "InverterBrand": {"type": "string", "enum": ["APsystems", "CSISolar", "<PERSON><PERSON>", "Enphase", "FOXESS", "<PERSON><PERSON><PERSON>", "GoodWe", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "INVT", "SMA", "Sofar", "SolarEdge", "SolArk", "Solax", "Solis", "Solplanet", "<PERSON><PERSON>", "SUNSYNK", "Tesla", "TSUN"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Enphase"}, "VehicleBrand": {"type": "string", "enum": ["Audi", "BMW", "Honda", "Hyundai", "Jaguar", "Land Rover", "<PERSON><PERSON>", "Mercedes", "MINI", "Nissan", "Peugeot", "Porsche", "Renault", "SEAT", "ŠKODA", "Tesla", "Volkswagen", "Volvo", "Ford", "Opel", "DS", "Toyota", "<PERSON>us", "Citroën", "Cupra", "Vauxhall", "Fiat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chevrolet", "GMC", "Cadillac", "XPENG"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Tesla"}, "MeterBrand": {"type": "string", "enum": ["Enphase", "<PERSON><PERSON><PERSON>", "Tesla", "SolarEdge"], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Tesla"}, "Intervention": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "9d90a9ad-9b24-4ce0-94e9-e888b1b877f4", "x-format": {"guid": true}}, "vendor": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Machine-friendly representation of the OEM's name.", "example": "AUDI"}, "vendorType": {"allOf": [{"$ref": "#/components/schemas/VendorType"}, {"description": "Type of device this intervention relates to."}]}, "brand": {"anyOf": [{"$ref": "#/components/schemas/InverterBrand"}, {"$ref": "#/components/schemas/VehicleBrand"}, {"$ref": "#/components/schemas/HvacBrand"}, {"$ref": "#/components/schemas/ChargerBrand"}, {"$ref": "#/components/schemas/BatteryBrand"}, {"$ref": "#/components/schemas/MeterBrand"}], "description": "A formatted and properly cased OEM brand name, suitable for reading by humans. May contain special characters.", "example": "Audi"}, "introducedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp of when the intervention was introduced.", "example": "2023-03-16T00:00:00"}, "domain": {"type": "string", "enum": ["Account", "<PERSON><PERSON>"], "description": "The domain the intervention is related to. i.e. Is the intervention related to the vendor service account or a setting on the device.", "example": "Account"}, "resolution": {"type": "object", "properties": {"title": {"type": "string", "description": "A localized title for the intervention.", "example": "Accept the Audi terms and conditions"}, "description": {"type": "string", "description": "A localized description of how to resolve the intervention. Formatted as Markdown.", "example": "To gain access to your vehicle's telemetry data, it's necessary to accept Audi's terms and conditions. Follow these steps to proceed:<br><br>1. Open the **myAudi app** on your phone<br>2. Follow the prompts to accept Audi's terms and conditions"}, "access": {"type": "string", "enum": ["Remote", "Physical"], "description": "Where the intervention needs to be resolved. i.e. Remotely using the vendor's app or directly on the device.", "example": "Remote"}, "agent": {"type": "string", "enum": ["User", "ThirdParty"], "description": "Who can resolve the intervention. i.e. A user can resolve the intervention themselves, or a licensed service retailer is needed.", "example": "User"}}, "required": ["title", "description", "access", "agent"], "additionalProperties": false}}, "required": ["id", "vendor", "vendorType", "brand", "introducedAt", "domain", "resolution"], "additionalProperties": false}, "InterventionsList": {"type": "array", "items": {"$ref": "#/components/schemas/Intervention"}}, "InverterProductionState": {"type": "object", "properties": {"productionRate": {"type": ["number", "null"], "description": "The current production rate in kW."}, "isProducing": {"type": ["boolean", "null"], "description": "Whether the solar inverter is actively producing energy or not."}, "totalLifetimeProduction": {"type": ["number", "null"], "description": "The total lifetime production in kWh.", "example": 100152.56}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received production state update", "example": "2020-04-07T17:04:26Z"}}, "required": ["productionRate", "isProducing", "totalLifetimeProduction", "lastUpdated"], "additionalProperties": false, "description": "Latest information about solar production. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "InverterInformation": {"type": "object", "properties": {"id": {"type": "string", "description": "Solar inverter vendor ID", "example": "7a18eb4e-ee65-4d5b-bb7c-d8e530006b18"}, "brand": {"allOf": [{"$ref": "#/components/schemas/InverterBrand"}, {"description": "Solar inverter brand"}]}, "model": {"type": "string", "description": "Solar inverter model", "example": "<PERSON>"}, "siteName": {"type": "string", "description": "Name of the site, as set by the user on the device/vendor. If no user-specified name is available, we construct a fallback name using the vendor/device/model names.", "example": "Sunny Plant"}, "installationDate": {"type": "string", "format": "date-time", "description": "Solar inverter installation date", "example": "2020-04-07T17:04:26Z"}}, "required": ["id", "brand", "model", "siteName", "installationDate"], "additionalProperties": false, "description": "Descriptive information about the solar inverter"}, "InverterLocation": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees", "example": 10.7197486}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees", "example": 59.9173985}}, "required": ["longitude", "latitude"], "additionalProperties": false, "description": "Solar inverter's GPS coordinates"}, "InverterCapabilities": {"type": "object", "properties": {"productionState": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of productionState data."}]}, "productionStatistics": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Availability of production statistics directly from the vendor."}]}}, "required": ["productionState", "productionStatistics"], "additionalProperties": false, "description": "A collection of descriptors that describe the capabilities of this specific inverter.\n\nNote: [Sample-based statistics](/api/reference#getProductionStatistics) collected by Enode rely on the `productionState` capability and are not affected by this capability."}, "Inverter": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Solar Inverter ID", "example": "195d8649-ad12-4755-a9cc-44e2d40ce4d9"}, "userId": {"type": "string", "description": "The ID of the user that linked this inverter.", "example": "f9d9b134-05f3-452a-80aa-be0deb22f492"}, "vendor": {"$ref": "#/components/schemas/InverterVendor"}, "chargingLocationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the charging location the solar inverter is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode received live data or when the solar inverter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the solar inverter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the solar inverter's cloud service if that service has determined that its cache is valid."}, "productionState": {"$ref": "#/components/schemas/InverterProductionState"}, "information": {"$ref": "#/components/schemas/InverterInformation"}, "location": {"$ref": "#/components/schemas/InverterLocation"}, "timezone": {"type": ["string", "null"], "description": "IANA TZ database timezone name representing the location of this inverter.", "example": "Europe/Oslo"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this inverter.", "example": ["inverter:read:location", "inverter:read:data"]}, "capabilities": {"$ref": "#/components/schemas/InverterCapabilities"}}, "required": ["id", "userId", "vendor", "chargingLocationId", "lastSeen", "isReachable", "productionState", "information", "location", "timezone", "scopes", "capabilities"], "additionalProperties": false}, "PaginatedInverterList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Inverter"}, "description": "Paginated list of solar inverters"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of solar inverters"}, "PartialLocation": {"type": "object", "properties": {"name": {"type": "string", "description": "User-supplied name for the Location", "example": "Enode"}, "latitude": {"type": "number", "description": "Latitude in degrees", "example": 59.9165915}, "longitude": {"type": "number", "description": "Longitude in degrees", "example": 10.7582268}, "timezoneName": {"type": "string", "default": "UTC", "description": "An IANA TZ database timezone name. This value will be used to convert rules and deadlines for tariffs, smart charging, and schedules into local time. Defaults to `UTC`.", "example": "Europe/Oslo"}}, "additionalProperties": false}, "LocationPayload": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PartialLocation"}, {"type": "object", "required": ["name", "latitude", "longitude", "timezoneName"]}]}, "LocationResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the Location.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "User ID the location belongs to", "example": "0fc4b1e7-9bdf-4958-b343-86eff3d9f92f"}, "name": {"type": "string", "description": "User-supplied name for the Location", "example": "Enode"}, "latitude": {"type": "number", "description": "Latitude in degrees", "example": 59.9165915}, "longitude": {"type": "number", "description": "Longitude in degrees", "example": 10.7582268}, "timezoneName": {"type": "string", "default": "UTC", "description": "An IANA TZ database timezone name. This value will be used to convert rules and deadlines for tariffs, smart charging, and schedules into local time. Defaults to `UTC`.", "example": "Europe/Oslo"}}, "required": ["id", "userId", "name", "latitude", "longitude"]}, "PaginatedLocationList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LocationResponse"}, "description": "Paginated list of locations"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of locations"}, "LocationUpdatePayload": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PartialLocation"}, {"type": "object", "required": ["name", "latitude", "longitude", "timezoneName"]}]}, "MeterInformation": {"type": "object", "properties": {"brand": {"$ref": "#/components/schemas/MeterBrand"}, "model": {"type": "string", "description": "Meter model", "example": "Tesla Powerwall built-in meter"}, "siteName": {"type": "string", "description": "Name of the site, as set by the user on the device/vendor. If no user-specified name is available, we construct a fallback name using the vendor/device/model names.", "example": "Powerwall Home"}, "installationDate": {"type": "string", "format": "date-time", "description": "Meter installation date", "example": "2020-04-07T17:04:26Z"}}, "required": ["brand", "model", "siteName", "installationDate"], "additionalProperties": false, "description": "Descriptive information about the meter"}, "MeterEnergyState": {"type": "object", "properties": {"power": {"type": ["number", "null"], "description": "Indicates the current load on the meter. \n\n * Positive value: Power is imported from the grid to the site, indicating consumption. \n * Negative value: Power is exported back to the grid, typically when the site generates excess electricity (e.g. from solar panels, or battery discharge). ", "example": 2.2}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received energy state update.", "example": "2022-03-01T12:34:56Z"}}, "required": ["power", "lastUpdated"], "additionalProperties": false, "description": "Latest information about meter load. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "MeterLocation": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees", "example": 10.7197486}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees", "example": 59.9173985}}, "required": ["longitude", "latitude"], "additionalProperties": false, "description": "<PERSON><PERSON>'s GPS coordinates"}, "MeterCapabilities": {"type": "object", "properties": {"measuresConsumption": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Indicates if the meter can measure consumed energy."}]}, "measuresProduction": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Indicates if the meter can measure energy produced or generated."}]}}, "required": ["measuresConsumption", "measuresProduction"], "additionalProperties": false, "description": "The specific meter's capabilities for recording energy consumption and production data."}, "PaginatedMeterList": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the meter object"}, "userId": {"type": "string", "description": "The ID of the user that linked this meter."}, "vendor": {"$ref": "#/components/schemas/MeterVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the meter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the meter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the meter's cloud service if that service has determined that its cache is valid."}, "information": {"$ref": "#/components/schemas/MeterInformation"}, "energyState": {"$ref": "#/components/schemas/MeterEnergyState"}, "location": {"$ref": "#/components/schemas/MeterLocation"}, "capabilities": {"$ref": "#/components/schemas/MeterCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this meter. ", "example": ["meter:read:data", "meter:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "energyState", "location", "capabilities", "scopes"], "additionalProperties": false}, "description": "Paginated list of meters"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of meters"}, "ScheduleFilters": {"type": "object", "properties": {"hourMinute": {"type": "object", "properties": {"from": {"type": "string", "description": "Clock time from which this rule should apply each day.", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}, "to": {"type": "string", "description": "Clock time until which this rule should apply each day.", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}}, "required": ["from", "to"], "additionalProperties": false, "description": "An interval composed of two clock times during which this rule applies each day. `to` always resolves to a timestamp after `from`, and thus may span across midnight and fall on the next day."}, "fromTimestamp": {"type": "string", "format": "date-time", "description": "UTC timestamp from which this rule should apply.", "example": "2020-04-07T17:04:26Z"}, "toTimestamp": {"type": "string", "format": "date-time", "description": "UTC timestamp until which this rule should apply.", "example": "2020-04-07T17:04:26Z"}, "weekdays": {"type": "array", "items": {"type": "integer", "enum": [0, 1, 2, 3, 4, 5, 6]}, "description": "An array of weekdays to which this rule should apply. A weekday starts with 0 for Monday and ends with 6 for Sunday.", "example": [0, 1, 2, 3, 4, 5]}}, "additionalProperties": false}, "PartialChargeSchedule": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "default": true, "description": "Whether this Schedule should be attempting to control the target's charge state."}, "defaultShouldCharge": {"type": "boolean", "description": "When no rule is active, the default charge state for the target."}, "rules": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/ScheduleFilters"}, {"type": "object", "properties": {"shouldCharge": {"type": "boolean", "description": "Whether a chargeable asset should want to charge while this rule is active."}}, "required": ["shouldCharge"], "additionalProperties": false}]}, "description": "Each rule sets a value for `shouldCharge`, either `true` or `false`. All other properties of the rule are optional filters that limit the times to which this rule applies."}, "targetId": {"type": "string", "minLength": 1, "description": "ID of the asset (Vehicle/Charger) to which this schedule applies"}, "targetType": {"type": "string", "enum": ["vehicle", "charger"]}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the Location to which this schedule applies. The behavior of a null value differs based on the `targetType`. For chargers, a null value is essentially ignored and the schedule is applied even if the charger isn't at a charging location. This is designed to prevent schedules from controlling vehicles at public chargers where the user doesn't pay for electricity.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}}, "additionalProperties": false, "title": "Partial Charge Schedule"}, "ChargeScheduleResponse": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PartialChargeSchedule"}, {"type": "object", "title": "Charge Schedule", "properties": {"id": {"type": "string"}}, "required": ["isEnabled", "defaultShouldCharge", "rules", "targetId", "targetType", "locationId", "id"]}]}, "TemperatureTargetState": {"anyOf": [{"$ref": "#/components/schemas/PermanentHoldTargetState"}, {"$ref": "#/components/schemas/ScheduledTargetState"}]}, "PartialTemperatureSchedule": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "default": true, "description": "Whether this Schedule should be attempting to control the HVAC's temperature settings."}, "targetId": {"type": "string", "minLength": 1, "description": "ID of the HVAC asset to which this schedule applies."}, "targetType": {"type": "string", "enum": ["hvac"]}, "defaultTargetState": {"allOf": [{"$ref": "#/components/schemas/TemperatureTargetState"}, {"description": "The target state to apply when no rules are active."}]}, "rules": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/ScheduleFilters"}, {"type": "object", "properties": {"targetState": {"allOf": [{"$ref": "#/components/schemas/TemperatureTargetState"}, {"description": "The target state to apply when this rule is active."}]}}, "required": ["targetState"], "additionalProperties": false}]}, "description": "Each rule sets a value for `holdType` and associated settings. All other properties of the rule are optional filters that limit the times to which this rule applies."}}, "additionalProperties": false, "title": "Temperature Schedule"}, "TemperatureScheduleResponse": {"type": "object", "allOf": [{"$ref": "#/components/schemas/PartialTemperatureSchedule"}, {"type": "object", "title": "Temperature Schedule", "required": ["isEnabled", "targetId", "targetType", "defaultTargetState", "rules", "id"], "properties": {"id": {"type": "string"}}}]}, "ScheduleResponse": {"oneOf": [{"$ref": "#/components/schemas/ChargeScheduleResponse"}, {"$ref": "#/components/schemas/TemperatureScheduleResponse"}], "discriminator": {"propertyName": "targetType", "mapping": {"vehicle": "#/components/schemas/ChargeScheduleResponse", "charger": "#/components/schemas/ChargeScheduleResponse", "hvac": "#/components/schemas/TemperatureScheduleResponse"}}}, "PaginatedScheduleList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleResponse"}, "description": "Paginated list of schedules"}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of schedules"}, "ChargeSchedule": {"type": "object", "title": "Partial Charge Schedule", "allOf": [{"$ref": "#/components/schemas/PartialChargeSchedule"}, {"type": "object", "title": "Charge Schedule", "required": ["isEnabled", "defaultShouldCharge", "rules", "targetId", "targetType", "locationId"]}]}, "TemperatureSchedule": {"type": "object", "title": "Temperature Schedule", "allOf": [{"$ref": "#/components/schemas/PartialTemperatureSchedule"}, {"type": "object", "title": "Temperature Schedule", "required": ["isEnabled", "targetId", "targetType", "defaultTargetState", "rules"]}]}, "Schedule": {"oneOf": [{"$ref": "#/components/schemas/ChargeSchedule"}, {"$ref": "#/components/schemas/TemperatureSchedule"}], "discriminator": {"propertyName": "targetType", "mapping": {"vehicle": "#/components/schemas/ChargeSchedule", "charger": "#/components/schemas/ChargeSchedule", "hvac": "#/components/schemas/TemperatureSchedule"}}}, "PartialSchedule": {"anyOf": [{"$ref": "#/components/schemas/PartialChargeSchedule"}, {"$ref": "#/components/schemas/PartialTemperatureSchedule"}]}, "ScheduleState": {"type": "string", "enum": ["ALIGNED", "MISALIGNED", "PENDING", "INACTIVE:OVERRIDDEN", "INACTIVE:DISABLED", "INACTIVE:AWAY", "INACTIVE:INCAPABLE"], "description": "An enum value that describes the current state of the Schedule"}, "ChargeScheduleStatus": {"type": "object", "properties": {"scheduleId": {"type": "string", "format": "uuid", "description": "ID of the schedule.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "scheduleType": {"type": "string", "enum": ["CHARGE"]}, "changedAt": {"type": "string", "format": "date-time", "description": "Time at which any value of the status last changed", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ScheduleState"}, "isCharging": {"type": "boolean", "description": "Whether the target is currently actually charging"}, "isChargingExpected": {"type": "boolean", "description": "Whether the target is currently expected to be charging"}, "isChargingExpectedParts": {"type": "object", "properties": {"needsCharge": {"type": "boolean"}, "isPluggedIn": {"type": "boolean"}, "shouldCharge": {"type": "boolean"}}, "required": ["needsCharge", "isPluggedIn", "shouldCharge"], "additionalProperties": false, "description": "Collection of booleans that - when combined via AND operator - forms the `isChargingExpected` value"}, "upcomingTransitions": {"type": "array", "items": {"type": "object", "properties": {"at": {"type": "string", "format": "date-time", "description": "ISO8601 UTC timestamp", "example": "2020-04-07T17:04:26Z"}, "shouldCharge": {"type": "boolean"}}, "required": ["at", "shouldCharge"], "additionalProperties": false}, "description": "List of upcoming transitions of the `shouldCharge` or `targetTemperature` value. A maximum of 2 items are returned."}, "smartOverride": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": ["object", "null"]}]}}, "required": ["scheduleId", "scheduleType", "changedAt", "state", "isCharging", "isChargingExpected", "isChargingExpectedParts", "upcomingTransitions", "smartOverride"], "additionalProperties": false, "description": "The state of a Charge Schedule at a particular point in time.", "title": "Charge Schedule Status"}, "NullableTemperatureTargetState": {"anyOf": [{"$ref": "#/components/schemas/PermanentHoldTargetState"}, {"$ref": "#/components/schemas/ScheduledTargetState"}, {"type": "null"}]}, "TemperatureScheduleStatus": {"type": "object", "properties": {"scheduleId": {"type": "string", "format": "uuid", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "scheduleType": {"type": "string", "enum": ["TEMPERATURE"]}, "changedAt": {"type": "string", "format": "date-time", "description": "Time at which any value of the status last changed", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ScheduleState"}, "current": {"$ref": "#/components/schemas/NullableTemperatureTargetState"}, "expected": {"$ref": "#/components/schemas/TemperatureTargetState"}, "upcomingTransitions": {"type": "array", "items": {"type": "object", "properties": {"at": {"type": "string", "format": "date-time", "description": "ISO8601 UTC timestamp", "example": "2020-04-07T17:04:26Z"}, "target": {"$ref": "#/components/schemas/TemperatureTargetState"}}, "required": ["at", "target"], "additionalProperties": false}, "description": "List of upcoming transitions. A maximum of 2 items are returned."}}, "required": ["scheduleId", "scheduleType", "changedAt", "state", "current", "expected", "upcomingTransitions"], "additionalProperties": false, "description": "The state of a Temperature Schedule at a particular point in time.", "title": "Temperature Schedule Status"}, "ScheduleStatus": {"anyOf": [{"$ref": "#/components/schemas/ChargeScheduleStatus"}, {"$ref": "#/components/schemas/TemperatureScheduleStatus"}]}, "Aggregates": {"type": "object", "properties": {"min": {"type": "number", "description": "The minimum amount for this period."}, "max": {"type": "number", "description": "The maximum amount for this period."}, "mean": {"type": "number", "description": "The average for this period."}}, "required": ["min", "max", "mean"], "additionalProperties": false}, "NullableAggregates": {"type": "object", "properties": {"min": {"type": ["number", "null"], "description": "The minimum amount for this period."}, "max": {"type": ["number", "null"], "description": "The maximum amount for this period."}, "mean": {"type": ["number", "null"], "description": "The average for this period."}}, "required": ["min", "max", "mean"], "additionalProperties": false}, "chargingStatisticsTimeseries": {"type": "array", "items": {"type": "object", "properties": {"kw": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate statistics in kW for the time bucket"}]}, "kwhSum": {"type": "number", "description": "Total power consumption in kWh for the time bucket", "example": 120}, "price": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate per kWh price statistics for the time bucket. Currency is `NOK` for Norway, Sweden, Denmark, Finland, Estonia, Latvia, Lithuania; and `EUR` for Austria, Belgium, Germany, France, Netherlands, Poland, Spain. Unless different price data has been provided via the Tariffs endpoints"}]}, "costSum": {"type": "number", "description": "Total cost for the consumed kWh for the time bucket", "example": 3.14}, "nonSmartPrice": {"allOf": [{"$ref": "#/components/schemas/NullableAggregates"}, {"description": "Aggregate per kWh price statistics for the time bucket if Smart Charging has actively stopped consumption from occurring during this `resolution`. This can be used to show the potential price if Smart Charging had not been active. Currency is `NOK` for Norway, Sweden, Denmark, Finland, Estonia, Latvia, Lithuania; and `EUR` for Austria, Belgium, Germany, France, Netherlands, Poland, Spain. Unless different price data has been provided via the Tariffs endpoints"}]}, "estimatedSavings": {"type": ["number", "null"], "description": "Total estimated savings achieved by Smart Charging for this `resolution`. If `null`, no part of this `resolution` was impacted by Smart Charging. Currency is `NOK` for Norway, Sweden, Denmark, Finland, Estonia, Latvia, Lithuania; and `EUR` for Austria, Belgium, Germany, France, Netherlands, Poland, Spain. Unless different price data has been provided via the Tariffs endpoints", "example": 1.07}, "date": {"type": "string", "format": "date-time", "description": "The timestamp that represents the start of this `resolution`", "example": "2020-04-07T17:04:26Z"}}, "required": ["kw", "kwhSum", "price", "costSum", "nonSmartPrice", "estimatedSavings", "date"], "additionalProperties": false}, "example": [{"kw": {"min": 0, "max": 78, "mean": 61}, "kwhSum": 120, "price": {"min": 13.8, "max": 14.4, "mean": 14.1}, "nonSmartPrice": {"min": 12.9, "max": 16.7, "mean": 14.8}, "costSum": 3.14, "estimatedSavings": 1.07, "date": "2021-01-19T09:37:36.845Z"}]}, "sessionsStatisticsTimeseries": {"type": "array", "items": {"type": "object", "properties": {"kw": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate statistics in kW for the session"}]}, "kwhSum": {"type": "number", "description": "Total power consumption in kWh for the session", "example": 120}, "price": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate per kWh price statistics for the session. Currency is `NOK` for Norway, Sweden, Denmark, Finland, Estonia, Latvia, Lithuania; and `EUR` for Austria, Belgium, Germany, France, Netherlands, Poland, Spain. Unless different price data has been provided via the Tariffs endpoints"}]}, "costSum": {"type": "number", "description": "Total cost for the consumed kWh for this session", "example": 3.14}, "id": {"type": "string", "description": "ID of the asset for this session."}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "Charging Location ID for this session.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "from": {"type": "string", "description": "Start time of the session.", "format": "date-time", "example": "2020-04-07T17:04:26Z"}, "to": {"type": "string", "description": "End time of the session.", "format": "date-time", "example": "2020-04-07T17:04:26Z"}, "nonSmartPrice": {"allOf": [{"$ref": "#/components/schemas/NullableAggregates"}, {"description": "If Smart Charging has actively stopped the charging session from happening at another time; these fields show the aggregate per kWh price statistics that would have occurred without Smart Charging. This can be used to show the potential price if Smart Charging had not been active. This is expressed in NOK unless different price data has been provided via the Tariffs endpoints"}]}, "estimatedSavings": {"type": ["number", "null"], "description": "Total estimated savings achieved by Smart Charging for this session. If null, no part of this session was impacted by Smart Charging. This is expressed in NOK unless different price data has been provided via the Tariffs endpoints", "example": 1.07}}, "required": ["kw", "kwhSum", "price", "costSum", "id", "locationId", "from", "to", "nonSmartPrice", "estimatedSavings"], "additionalProperties": false}}, "productionStatisticsParams": {"type": "object", "properties": {"userId": {"type": "string", "description": "The ID of the User"}}, "required": ["userId"], "additionalProperties": false}, "productionStatisticsTimeseries": {"type": "array", "items": {"type": "object", "properties": {"kw": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate statistics for production rate in kW"}]}, "kwhSum": {"type": "number", "description": "Total power production in kWh", "example": 120}, "price": {"allOf": [{"$ref": "#/components/schemas/Aggregates"}, {"description": "Aggregate per kWh price statistics for the time bucket. This is expressed in NOK unless different price data has been provided via the Tariffs endpoints"}]}, "earningsSum": {"type": "number", "description": "Uses the local day ahead market price to determine the total value of the energy produced during the time bucket. Requires the user to have a [Location](/api/reference#locations) at the same geo coordinates as the inverter itself. This is expressed in NOK unless different price data has been provided via the Tariffs endpoints", "example": 3.14}, "date": {"type": "string", "format": "date-time", "description": "The start timestamp of this sample. If `utcOffset` has been defined, this value will be offset accordingly.", "example": "2020-04-07T17:04:26Z"}}, "required": ["kw", "kwhSum", "price", "earningsSum", "date"], "additionalProperties": false}, "example": [{"kw": {"min": 0, "max": 78, "mean": 61}, "kwhSum": 120, "price": {"min": 13.8, "max": 14.4, "mean": 14.1}, "earningsSum": 3.14, "date": "2021-01-19T09:37:36.845Z"}]}, "InverterStatisticsTimeseriesBucket": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time", "description": "Date (UTC) at the start of the data point.", "example": "2023-01-19T09:00:00.000Z"}, "value": {"type": "number", "description": "The energy produced (kWh) in the specified interval.", "example": 1.1}}, "required": ["date", "value"], "additionalProperties": false}, "InverterStatisticsBuckets": {"type": "object", "properties": {"unit": {"type": "string", "enum": ["kWh"], "description": "The unit of measurement for power production data. Always kWh.", "example": "kWh"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/InverterStatisticsTimeseriesBucket"}, "description": "An array of data points representing the power production statistics.", "example": [{"date": "2021-01-19T09:37:36.845Z", "value": 1.1}]}}, "required": ["unit", "data"], "additionalProperties": false}, "InverterStatistics": {"type": "object", "properties": {"production": {"allOf": [{"$ref": "#/components/schemas/InverterStatisticsBuckets"}, {"description": "Contains information about power production statistics."}]}}, "required": ["production"], "additionalProperties": false, "example": {"production": {"unit": "kWh", "data": [{"date": "2024-01-19T09:00:00.000Z", "value": 11.1}, {"date": "2024-01-19T10:00:00.000Z", "value": 15.4}]}}}, "TariffRateName": {"type": "string", "description": "Per-tariff unique interval name", "example": "OFF-PEAK"}, "TariffIntervalCost": {"type": "string", "pattern": "^[+-]?(\\d{1,9}([.]\\d{0,9})?|[.]\\d{1,9})$", "description": "Rate cost (decimal string)", "example": "12.34"}, "TariffRate": {"type": "object", "properties": {"name": {"$ref": "#/components/schemas/TariffRateName"}, "cost": {"$ref": "#/components/schemas/TariffIntervalCost"}}, "required": ["name", "cost"], "additionalProperties": false}, "Tariff": {"type": "array", "items": {"$ref": "#/components/schemas/TariffRate"}, "example": [{"name": "PEAK", "cost": "13.37"}, {"name": "OFF-PEAK", "cost": "12.34"}]}, "LocationTariffSchedule": {"type": "array", "items": {"type": "object", "properties": {"weekday": {"type": "integer", "enum": [0, 1, 2, 3, 4, 5, 6], "description": "Weekday to apply the named tariff. A weekday starts with 0 for Monday, and ends with 6 for Sunday. If not specified, named tariff is applied for entire week"}, "fromHourMinute": {"type": "string", "description": "Interval from time (inclusive, UTC)", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}, "toHourMinute": {"type": "string", "description": "Interval to time (exclusive, UTC)", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}, "tariffId": {"type": "string", "description": "Energy Provider Tariff ID"}, "tariffName": {"type": "string", "description": "Rate name"}}, "required": ["weekday", "fromHourMinute", "toHourMinute", "tariffId", "tariffName"], "additionalProperties": false}, "example": [{"weekday": 0, "fromHourMinute": "00:00", "toHourMinute": "18:00", "tariffId": "FLEX-TARIFF-A", "tariffName": "OFF-PEAK"}, {"weekday": 0, "fromHourMinute": "18:00", "toHourMinute": "24:00", "tariffId": "FLEX-TARIFF-A", "tariffName": "PEAK"}]}, "TariffRateInterval": {"type": "object", "properties": {"name": {"type": "string", "description": "Rate name"}, "weekdays": {"type": "array", "items": {"type": "integer", "enum": [0, 1, 2, 3, 4, 5, 6]}, "description": "An array of weekdays to apply the named tariff. A weekday starts with 0 for Monday, and ends with 6 for Sunday. If not specified, named tariff is applied for entire week", "example": [0, 1, 2, 3, 4, 5]}, "from": {"type": "string", "description": "Interval from time (inclusive), format `HH:mm`. Interpreted in the timezone specified in the `timezoneName` field of the Location.", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}, "to": {"type": "string", "description": "Interval to time (exclusive), format `HH:mm`. Interpreted in the timezone specified in the `timezoneName` field of the Location.", "pattern": "^\\d{2}:\\d{2}$", "example": "09:00"}}, "required": ["name", "from", "to"], "additionalProperties": false}, "LocationTariffInterval": {"type": "object", "properties": {"tariffId": {"type": "string", "description": "The Tariff ID"}, "tariffIntervals": {"type": "array", "items": {"$ref": "#/components/schemas/TariffRateInterval"}, "description": "List of time intervals at which to apply the specified tariff rates.", "example": [{"name": "OFF-PEAK-WEEKDAY", "from": "00:00", "to": "12:00", "weekdays": [0, 1, 2, 3, 4]}, {"name": "PEAK-WEEKDAY", "from": "12:00", "to": "24:00", "weekdays": [0, 1, 2, 3, 4]}, {"name": "OFF-PEAK-WEEKEND", "weekdays": [5, 6], "from": "00:00", "to": "06:00"}, {"name": "PEAK-WEEKEND", "weekdays": [5, 6], "from": "06:00", "to": "24:00"}]}}, "required": ["tariffId", "tariffIntervals"], "additionalProperties": false}, "UsersListEntry": {"type": "object", "properties": {"id": {"type": "string", "minLength": 1, "description": "ID of the user.", "example": "ad84e742-0f46-4cf4-b0db-7d890f8f23f5"}, "createdAt": {"type": ["string", "null"], "format": "date-time", "description": "ISO8601 UTC timestamp", "example": "2020-04-07T17:04:26Z"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes for the user.", "deprecated": true, "example": ["all"]}}, "required": ["id", "createdAt", "scopes"]}, "PaginatedUsersListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/UsersListEntry"}, "description": "Paginated list of users."}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of users."}, "UserResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "The user's ID.", "example": "123456789-<PERSON>c"}, "linkedVendors": {"type": "array", "items": {"type": "object", "properties": {"vendor": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "TESLA"}, "vendorType": {"$ref": "#/components/schemas/VendorType"}, "isValid": {"type": "boolean", "description": "Whether the provided credentials are currently valid. A `false` value here indicates that a credential has become invalidated, and [Link User](/api/reference#postUsersUseridLink) should be used to get new credentials from this user."}}, "required": ["vendor", "vendorType", "<PERSON><PERSON><PERSON><PERSON>"], "additionalProperties": false}}}, "required": ["id", "linkedVendors"], "additionalProperties": false}, "LinkUserPayload": {"type": "object", "properties": {"vendor": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "By specifying a vendor, the [brand selection step](/docs/link-ui#step-2-brand-and-service-selection) in Link UI will be skipped. Instead, your user will go directly to the [service selection view](/docs/link-ui#step-2-brand-and-service-selection) (if applicable for the specified vendor), or to the [review data access](/docs/link-ui#step-3-review-data-access) step.", "example": "TESLA"}, "vendorType": {"allOf": [{"$ref": "#/components/schemas/VendorType"}, {"description": "Sets the asset type your user can link through Link UI within the linking session. The [brand selection step](/docs/link-ui#step-2-brand-and-service-selection) selection step will show vendors available for the specified asset type."}]}, "language": {"type": "string", "enum": ["browser", "da-DK", "de-DE", "en-US", "en-GB", "es-ES", "fi-FI", "fr-FR", "it-IT", "nb-NO", "nl-NL", "nl-BE", "pt-PT", "ro-RO", "sv-SE"], "description": "Sets the language for Link UI. We recommend matching this to the language of your application. If your language is not yet supported, you can specify `browser` which will assess your user's browser language via the `Accept-Language` header ([learn more](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language)). Passing in `browser` will fall back to `en-US` if no match is found."}, "scopes": {"type": "array", "items": {"$ref": "#/components/schemas/Scopes"}, "minItems": 1, "description": "The [scope(s)](#scopes) you wish to request access to when linking an asset ([learn more](#scopes)). The scopes will be presented for user consent in Link UI in the [review data access](/docs/link-ui#step-3-review-data-access) step. The `<vendorType>:read:data` scope is required for any asset type (e.g. if you wish to link a `battery`, you must request `battery:read:data`).", "example": ["vehicle:read:data", "vehicle:control:charging"]}, "redirectUri": {"type": "string", "maxLength": 500, "format": "uri", "description": "The URI your user will be redirected to after completing the linking flow (from the [last step in the Link UI flow](/docs/link-ui#step-6-success)). If your user exits the flow before completing, they will be redirected to this URI with additional `error` and `error_message` query params describing the reason for exiting.", "example": "myapp://integrations/enode"}, "colorScheme": {"type": "string", "enum": ["system", "light", "dark"], "default": "system", "description": "Sets the color scheme (light or dark) of Link UI. If your UI is only available in one of the color schemes, and does not follow the operating system color scheme, we recommend setting either `light` or `dark`. Defaults to  `system`, which follows the users operating system settings.", "example": "light"}}, "required": ["vendorType", "language", "scopes", "redirectUri"], "additionalProperties": false, "example": {"vendor": "VOLKSWAGEN", "vendorType": "vehicle", "language": "en-GB", "scopes": ["vehicle:read:data", "vehicle:control:charging"], "redirectUri": "myapp://integrations/enode", "colorScheme": "light"}}, "LinkUserResponse": {"type": "object", "properties": {"linkUrl": {"type": "string", "description": "Use this URL to present Link UI to your user via [mobile in-app browsers](/docs/link-ui#mobile-in-app-browsers) or [web redirects](/docs/link-ui#web-redirects).", "example": "https://link.enode.com/YzIwZThhYjYtMjMzMi00ZTAyLTg0OTYtYzdjOTlhZTY3Zjc3QDI2YzI1MDExLTdhYTctNGE2NS1iNjBmLTZmMzc5NmRhODUyMDowNDViYjFiYmE0M2Y5NDU5YTc5OTgxZmEyYTg1NmI4YzhkOGU4YjgyNmNmMzQzZmFmMGNhZTlmNDBjMmZmOTgy"}, "linkToken": {"type": "string", "description": "Use this token to initialize the [Link SDKs](/docs/link-sdks) if you wish to present Link UI to your users via our [mobile SDKs](/docs/link-ui#mobile-sd-ks).", "example": "U2FtcGxlIFNESyB0b2tlbgpTYW1wbGUgU0RLIHRva2VuClNhbXBsZSBTREsgdG9rZW4KU2FtcGxlIFNESyB0b2tlbg=="}}, "required": ["linkUrl", "linkToken"], "additionalProperties": false}, "ConnectionsLimitReachedProblem": {"type": "object", "properties": {"type": {"type": "string", "description": "A URI reference that identifies the problem type."}, "title": {"type": "string", "description": "A short, human-readable summary of the problem type."}, "detail": {"type": "string", "description": "A human-readable explanation specific to this occurrence of the problem."}}, "required": ["type", "title", "detail"], "additionalProperties": false, "description": "Webhook already exists problem error", "example": {"type": "https://docs.enode.io/problems/forbidden", "title": "Connections limit reached.", "detail": "Unable to create more connections for ClientID: a7bedf14-c3eb-4c2b-a08f-b34a1f70808d"}}, "VehicleInformation": {"type": "object", "properties": {"vin": {"type": ["string", "null"], "description": "Vehicle VIN", "example": "2HGFB2F5XEH542858"}, "brand": {"allOf": [{"$ref": "#/components/schemas/VehicleBrand"}, {"type": ["string", "null"], "description": "Vehicle brand"}]}, "model": {"type": ["string", "null"], "description": "Vehicle model", "example": "Model S P85"}, "year": {"type": ["number", "null"], "description": "Vehicle production year", "example": 2020}, "displayName": {"type": ["string", "null"], "description": "User-defined vehicle nickname.", "example": "Batmobile"}}, "required": ["vin", "brand", "model", "year", "displayName"], "additionalProperties": false, "description": "Descriptive information about the Vehicle."}, "VehiclePowerDeliveryState": {"type": "string", "enum": ["UNKNOWN", "UNPLUGGED", "PLUGGED_IN:INITIALIZING", "PLUGGED_IN:CHARGING", "PLUGGED_IN:STOPPED", "PLUGGED_IN:COMPLETE", "PLUGGED_IN:NO_POWER", "PLUGGED_IN:FAULT"], "description": "The current state of power delivery between the vehicle and charger.\n\n * `UNKNOWN`: The state of power delivery is currently unknown. \n * `UNPLUGGED`: The vehicle is not connected to the charger. \n * `PLUGGED_IN:INITIALIZING`: The charging station is preparing to deliver power to the vehicle. It is expected for this state to shortly transition into PLUGGED_IN:CHARGING. \n * `PLUGGED_IN:CHARGING`: The vehicle is actively receiving power from the charger, causing the battery level to increase. \n * `PLUGGED_IN:COMPLETE`: The charging process has finished and the vehicle's battery has reached the target charge limit. \n * `PLUGGED_IN:STOPPED`: The charging process has been intentionally stopped by either the vehicle or the charger. The vehicle is still plugged in and waiting for the charging process to resume. \n * `PLUGGED_IN:NO_POWER`: The vehicle attempted to initialize charging, however no external power was available. It is not possible to transition into a charging state with a remote command until there is some user intervention to resolve the issue. \n * `PLUGGED_IN:FAULT`: A malfunction in the charging process is preventing power from being delivered. Possible causes include a charging cable not being properly locked, extreme temperatures, or malfunctions in either the charging station or the vehicle's internal system. It is not possible to transition into a charging state with a remote command until there is some user intervention to resolve the issue.", "example": "PLUGGED_IN:CHARGING"}, "VehicleChargeState": {"type": "object", "properties": {"batteryLevel": {"type": ["number", "null"], "description": "Remaining battery in percent", "example": 66}, "range": {"type": ["number", "null"], "description": "Estimated remaining kilometers", "example": 228}, "isPluggedIn": {"type": ["boolean", "null"], "description": "Indicates whether the vehicle is connected to a charging box (regardless of whether it is actually charging)", "example": true}, "isCharging": {"type": ["boolean", "null"], "description": "Indicates whether the vehicle is currently receiving power from the vehicle and actively charging its battery.", "example": true}, "isFullyCharged": {"type": ["boolean", "null"], "description": "Indicates whether the vehicle is fully charged. 'Fully charged' may be a `batteryLevel` of less than 100 if a `chargeLimit` has been configured in the vehicle.", "example": false}, "batteryCapacity": {"type": ["number", "null"], "description": "Vehicle's usable battery capacity in kWh.", "example": 48.1}, "chargeLimit": {"type": ["number", "null"], "description": "Charge limit, as a percent of `batteryCapacity`.", "example": 90}, "chargeRate": {"type": ["number", "null"], "description": "The current charge rate in kW.\n\nThis property is only available when the vehicle is charging, and is `null` any other time.", "example": 2}, "chargeTimeRemaining": {"type": ["number", "null"], "description": "Estimated time until the current charging intent is completed, in minutes.\n\nThis property is only available when the vehicle is charging, and is `null` any other time.", "example": 285}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received charge state update", "example": "2020-04-07T17:04:26Z"}, "maxCurrent": {"type": ["number", "null"], "description": "The maximum current that the vehicle can draw from the charger, in amps.", "example": 16}, "powerDeliveryState": {"$ref": "#/components/schemas/VehiclePowerDeliveryState"}}, "required": ["batteryLevel", "range", "isPluggedIn", "isCharging", "isFullyCharged", "batteryCapacity", "chargeLimit", "chargeRate", "chargeTimeRemaining", "lastUpdated", "max<PERSON><PERSON><PERSON>", "powerDeliveryState"], "additionalProperties": false, "description": "Latest information about the electric or hybrid battery. `null` values indicate we are unable to determine a value for the field based on the information coming from the vendor."}, "PartialVehicleSmartChargingPolicy": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "default": false, "description": "When enabled, this vehicle's charging status may be controlled by [Smart Charging](/docs/smart-charging/introduction)"}, "deadline": {"type": "string", "description": "The hour-minute deadline for fully charging the vehicle. Smart charging does not work without setting a deadline. If a timezone is set on the charging location at which the smart charging occurs, the deadline is interpreted in that timezone, otherwise UTC is used.", "pattern": "^\\d{2}:\\d{2}$", "example": "07:00"}, "minimumChargeLimit": {"type": "number", "default": 0, "description": "Proceed to charge promptly, disregarding energy prices, until the vehicle achieves this preset minimum limit. Please note that this limit cannot exceed the vehicle's maximum charge limit. The default setting is zero. The unit of measure is percentage."}}, "additionalProperties": false, "title": "Partial Smart Charging Policy"}, "VehicleSmartChargingPolicy": {"type": "object", "title": "Partial Smart Charging Policy", "allOf": [{"$ref": "#/components/schemas/PartialVehicleSmartChargingPolicy"}, {"type": "object", "title": "Smart Charging Policy", "description": "[Smart Charging](/docs/smart-charging/introduction) configuration properties. Configured via [Update Vehicle Smart Charging Policy](/api/reference#updateVehicleSmartChargingPolicy).", "required": ["isEnabled", "deadline", "minimumChargeLimit"]}]}, "VehicleLocation": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees", "example": 10.7197486}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees", "example": 59.9173985}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of last received location", "example": "2024-01-07T17:04:26.000Z"}}, "required": ["longitude", "latitude", "lastUpdated"], "additionalProperties": false, "description": "Vehicle's GPS coordinates with timestamp"}, "VehicleOdometer": {"type": "object", "properties": {"distance": {"type": ["number", "null"], "description": "Odometer in kilometers", "example": 65393}, "lastUpdated": {"type": ["string", "null"], "format": "date-time", "description": "Time of the last odometer update, in kilometers", "example": "2024-01-07T17:04:26.000Z"}}, "required": ["distance", "lastUpdated"], "additionalProperties": false, "description": "Vehicle's odometer reading in kilometers with timestamp"}, "VehicleCapabilities": {"type": "object", "properties": {"information": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of information data."}]}, "chargeState": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of chargeState data."}]}, "location": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of location data."}]}, "odometer": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Full availability of odometer data."}]}, "setMaxCurrent": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports setting the maximum charge current."}]}, "startCharging": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports START charging command."}]}, "stopCharging": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Supports STOP charging command."}]}, "smartCharging": {"allOf": [{"$ref": "#/components/schemas/Capability"}, {"description": "Support for smartCharging. This value is dynamic and may change over time."}]}}, "required": ["information", "chargeState", "location", "odometer", "setMaxCurrent", "startCharging", "stopCharging", "smartCharging"], "additionalProperties": false, "description": "A collection of descriptors that describe the capabilities of this specific vehicle"}, "Vehicle": {"type": "object", "properties": {"id": {"type": "string", "description": "Vehicle ID", "example": "05ee9df5-d072-4ee8-b773-399dec8f5693"}, "userId": {"type": "string", "description": "The ID of the user that linked this vehicle.", "example": "bb28471e-cde6-4eff-ace4-9a7f4f50882a"}, "vendor": {"$ref": "#/components/schemas/VehicleVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode received live data or when the vehicle was initially linked.", "example": "2024-01-07T17:04:26.000Z"}, "isReachable": {"type": ["boolean", "null"], "description": "Whether live data from the vehicle is currently available from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the vehicle's cloud service if that service has determined that its cache is valid.", "example": true}, "information": {"$ref": "#/components/schemas/VehicleInformation"}, "chargeState": {"$ref": "#/components/schemas/VehicleChargeState"}, "smartChargingPolicy": {"$ref": "#/components/schemas/VehicleSmartChargingPolicy"}, "location": {"$ref": "#/components/schemas/VehicleLocation"}, "odometer": {"$ref": "#/components/schemas/VehicleOdometer"}, "capabilities": {"$ref": "#/components/schemas/VehicleCapabilities"}, "scopes": {"type": "array", "items": {"$ref": "#/components/schemas/Scopes"}, "description": "Scopes that the user has granted for this vehicle.", "example": ["vehicle:control:charging", "vehicle:read:data", "vehicle:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "chargeState", "smartChargingPolicy", "location", "odometer", "capabilities", "scopes"], "additionalProperties": false}, "PaginatedVehicleList": {"type": "object", "properties": {"data": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Vehicle"}, {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the vehicle is currently positioned at (if any).", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}]}}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false}, "VehicleAction": {"anyOf": [{"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the target which this action is controlling.", "example": "bfbccded-8a1c-45a8-bbda-dcaeef29977a"}, "kind": {"allOf": [{"$ref": "#/components/schemas/ChargingAction"}, {"description": "The charging action to perform"}]}, "failureReason": {"allOf": [{"$ref": "#/components/schemas/ChargingActionFailureReason"}, {"type": ["object", "null"]}]}, "targetType": {"type": "string", "enum": ["vehicle"]}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "kind", "failureReason", "targetType"], "additionalProperties": false, "title": "Charge Action"}, {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "state": {"$ref": "#/components/schemas/ActionState"}, "targetId": {"type": "string", "description": "ID of the entity which this action is controlling."}, "targetState": {"$ref": "#/components/schemas/TargetMaxCurrent"}, "failureReason": {"allOf": [{"$ref": "#/components/schemas/ChargingActionFailureReason"}, {"type": ["object", "null"]}]}, "targetType": {"type": "string", "enum": ["vehicle"]}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "state", "targetId", "targetState", "failureReason", "targetType"], "additionalProperties": false, "title": "Max Current Action"}]}, "CurrencyCode": {"type": "string", "description": "Currency in which monetary amounts are denominated (ISO 4217 3-character Alpha code)", "example": "USD"}, "MonetaryAmount": {"type": "number"}, "FailureCondition": {"type": "string", "enum": ["STOP_FAILED", "START_FAILED", "FINISHED_LATE", "UNKNOWN", "CHARGE_INTERRUPTED"], "description": "An enum value that describes the current SmartCharging state of the vehicle. Every vehicle is in exactly one state, at all times"}, "VehicleSmartChargingPlan": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the Smart Charging Plan", "example": "53559d39-019a-443b-a2a7-a2ca29f54d4b"}, "vehicleId": {"type": "string", "description": "ID of the Vehicle to which the Smart Charging Plan belongs", "example": "4b5ada14-ea87-4ca1-aab9-3c979c34cf4a"}, "userId": {"type": "string", "description": "ID of the User to which the Smart Charging Plan belongs", "example": "3b568b76-e30a-426e-aacd-609db4d7be81"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the charging location at which the Smart Charging Plan is being executed. Null for Smart Charging Plans started before March 2022.", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}, "vendor": {"allOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"description": "Vendor of the Vehicle to which the Smart Charging Plan belongs"}]}, "currency": {"$ref": "#/components/schemas/CurrencyCode"}, "nonSmartCost": {"allOf": [{"$ref": "#/components/schemas/MonetaryAmount"}, {"description": "Estimated cost of charging, if smart charging were not on for the vehicle", "example": 17.2}]}, "smartCost": {"allOf": [{"$ref": "#/components/schemas/MonetaryAmount"}, {"type": ["number", "null"], "description": "Estimated cost of charging, achieved by this Smart Charging Plan", "example": 12.8}]}, "stopAt": {"type": "string", "format": "date-time", "description": "Time at which the current Smart Charging Plan will stop the vehicle's charging. The charging will be resumed when prices are optimal.", "example": "2020-04-07T17:04:26Z"}, "startAt": {"type": ["string", "null"], "format": "date-time", "description": "Time at which the current Smart Charging Plan will resume the vehicle's charging, when prices are optimal.", "example": "2020-04-07T17:04:26Z"}, "estimatedFinishAt": {"type": "string", "format": "date-time", "description": "Time at which charging is estimated to be complete, due to the current Smart Charging Plan", "example": "2020-04-07T17:04:26Z"}, "stopConfirmedAt": {"type": ["string", "null"], "format": "date-time", "description": "During a Smart Charging Plan, the first time at which the vehicle's charging was confirmed as stopped. If a vehicle stops charging outside of smart charging's control (e.g., via a 3rd party app), this will be populated with that timestamp.", "example": "2020-04-07T17:04:26Z"}, "startConfirmedAt": {"type": ["string", "null"], "format": "date-time", "description": "During a Smart Charging Plan, the first time at which the vehicle's charging was confirmed as started. If a vehicle starts charging outside of smart charging's control (e.g., via a 3rd party app), this will be populated with that timestamp.", "example": "2020-04-07T17:04:26Z"}, "endedAt": {"type": ["string", "null"], "format": "date-time", "description": "The time at which the Smart Charging Plan ended", "example": "2020-04-07T17:04:26Z"}, "finalState": {"type": ["string", "null"], "enum": ["PLAN:ENDED:FINISHED", "PLAN:ENDED:UNPLUGGED", "PLAN:ENDED:FAILED", "PLAN:ENDED:DISABLED", "PLAN:ENDED:DEADLINE_CHANGED"], "description": "The final state of the Smart Charging Plan when it ended"}, "failureCondition": {"allOf": [{"$ref": "#/components/schemas/FailureCondition"}, {"type": ["string", "null"], "description": "If the finalState was `PLAN:ENDED:FAILED`, failureCondition is populated with the condition in which the Smart Charging Plan failed. If the finalState was `PLAN:ENDED:FINISHED`, failureCondition is populated with `FINISHED_LATE` if the plan was more than 15% late.", "example": "CHARGE_INTERRUPTED"}]}, "externalStart": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": ["object", "null"], "description": "Deprecated, replaced by `smartOverride` from [Get Vehicle Smart Charging Status](/api/reference#getVehiclesVehicleidSmartchargingstatus)", "deprecated": true}]}, "chargingLocationId": {"type": ["string", "null"], "format": "uuid", "description": "Deprecated. Replaced by `locationId`. ID of the charging location at which the Smart Charging Plan is being executed. Null for Smart Charging Plans started before March 2022.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}, "deprecated": true}}, "required": ["id", "vehicleId", "userId", "locationId", "vendor", "currency", "nonSmartCost", "smartCost", "stopAt", "startAt", "estimatedFinishAt", "stopConfirmedAt", "startConfirmedAt", "endedAt", "finalState", "failureCondition", "externalStart", "chargingLocationId"], "additionalProperties": false}, "SmartChargeState": {"type": "string", "enum": ["DISABLED", "CONSIDERING", "UNKNOWN", "PLAN:EXECUTING:STOPPING", "PLAN:EXECUTING:STOP_FAILED", "PLAN:EXECUTING:STOPPED", "PLAN:EXECUTING:STOPPED:AWAITING_PRICES", "PLAN:EXECUTING:STARTING", "PLAN:EXECUTING:START_FAILED", "PLAN:EXECUTING:STARTED", "PLAN:EXECUTING:CHARGE_INTERRUPTED", "PLAN:EXECUTING:OVERRIDDEN", "PLAN:ENDED:FINISHED", "PLAN:ENDED:UNPLUGGED", "PLAN:ENDED:FAILED", "PLAN:ENDED:DISABLED", "PLAN:ENDED:DEADLINE_CHANGED", "FULLY_CHARGED"], "description": "An enum value that describes the current Smart Charging state of the vehicle. Every vehicle is in exactly one state, at all times. If the returned state is `UNKNOWN`, this might indicate that the vehicle is no longer capable of smart charging. In this case we recommend that you check the capabilities of the vehicle to determine what should be communicated to users. If we don't have pricing data or tariffs for the location of the vehicle, the state will also be `UNKNOWN`. In this case, we recommend that you contact Enode staff.", "example": "PLAN:EXECUTING:STARTING"}, "Consideration": {"type": ["object", "null"], "properties": {"isPluggedIn": {"type": "boolean", "description": "The vehicle is plugged in"}, "isCharging": {"type": "boolean", "description": "The vehicle is charging"}, "atChargingLocation": {"type": "boolean", "description": "The vehicle is located at a configured Charging Location"}, "hasTimeEstimate": {"type": "boolean", "description": "A good estimate is available of how much time it will take to charge."}}, "required": ["isPluggedIn", "isCharging", "atChargingLocation", "hasTimeEstimate"], "additionalProperties": false}, "UpdateFirehoseWebhookPayload": {"type": "object", "properties": {"secret": {"type": "string", "minLength": 6, "maxLength": 256, "description": "A cryptographically secure secret, generated and provided by your client. Refer to [Generating a secret](/docs/webhooks#generating-a-secret) for details on how to do this.", "example": "0Kvs1tAUQ69FOMBiWlt5XJSrruXMhWDiVbyrWaNm"}, "url": {"type": "string", "description": "The HTTPS URL where webhook payloads should be sent to.", "example": "https://example.com/enode-webhooks/firehose", "x-format": {"uri": true}}}, "required": ["secret", "url"], "additionalProperties": false}, "DeleteFirehoseWebhook": {"type": "object", "properties": {"url": {"type": "string", "description": "The HTTPS url of the webhook to be deleted", "example": "https://example.com/enode-webhooks/firehose", "x-format": {"uri": true}}}, "required": ["url"], "additionalProperties": false}, "WebhookNotFoundProblem": {"type": "object", "properties": {"type": {"type": "string", "description": "A URI reference that identifies the problem type."}, "title": {"type": "string", "description": "A short, human-readable summary of the problem type."}, "detail": {"type": "string", "description": "A human-readable explanation specific to this occurrence of the problem."}}, "required": ["type", "title", "detail"], "additionalProperties": false, "description": "Webhook not found problem error", "example": {"type": "https://docs.enode.io/problems/not-found", "title": "Webhook not found", "detail": "Could not find webhook 0ec3d927-0100-44ff-bb53-edfe3ce64a57"}}, "TestFirehoseWebhook": {"type": "object", "properties": {"status": {"type": "string", "enum": ["SUCCESS", "FAILURE"], "description": "Indicates if the test webhook event was delivered successfully to your configured webhook endpoint."}, "description": {"type": "string", "description": "Human readable description of the test outcome."}, "response": {"type": ["object", "null"], "properties": {"code": {"type": "number", "description": "Status code returned by your configured webhook endpoint", "example": 200}, "body": {"type": "string", "description": "Body returned by your configured webhook endpoint", "example": "{}"}, "headers": {"type": ["array", "null"], "items": {"type": "string"}, "description": "Headers returned by your configured webhook endpoint", "example": ["content-type: application/json; } charset=utf-8"]}}, "required": ["code", "body", "headers"], "additionalProperties": false, "description": "Null if status is `FAILED`."}}, "required": ["status", "description", "response"], "additionalProperties": false}, "WebhookCreatePayload": {"type": "object", "properties": {"url": {"type": "string", "minLength": 8, "maxLength": 200, "description": "The HTTPS URL that webhook payloads should be sent to.", "example": "https://example.com/enode-webhooks/firehose", "x-format": {"uri": true}}, "secret": {"type": "string", "minLength": 6, "maxLength": 256, "description": "A cryptographically secure secret, generated and provided by your client. Refer to [Generating a secret](/docs/webhooks#generating-a-secret) for details on how to do this.", "example": "0Kvs1tAUQ69FOMBiWlt5XJSrruXMhWDiVbyrWaNm"}, "events": {"type": "array", "items": {"type": "string", "enum": ["*", "user:vehicle:discovered", "user:vehicle:updated", "user:vehicle:deleted", "user:vehicle:smart-charging-status-updated", "user:charge-action:updated", "user:vendor-action:updated", "user:schedule:execution-updated", "user:charger:discovered", "user:charger:updated", "user:charger:deleted", "user:hvac:discovered", "user:hvac:updated", "user:hvac:deleted", "user:inverter:discovered", "user:inverter:updated", "user:inverter:deleted", "user:credentials:invalidated", "user:battery:discovered", "user:battery:updated", "user:battery:deleted", "enode:webhook:test", "user:meter:discovered", "user:meter:updated", "user:meter:deleted"]}, "default": ["*"], "description": "The events which will trigger the webhook. Available events: `*`, `user:vehicle:discovered`, `user:vehicle:updated`, `user:vehicle:deleted`, `user:vehicle:smart-charging-status-updated`, `user:charge-action:updated`, `user:vendor-action:updated`, `user:schedule:execution-updated`, `user:charger:discovered`, `user:charger:updated`, `user:charger:deleted`, `user:hvac:discovered`, `user:hvac:updated`, `user:hvac:deleted`, `user:inverter:discovered`, `user:inverter:updated`, `user:inverter:deleted`, `user:credentials:invalidated`, `user:battery:discovered`, `user:battery:updated`, `user:battery:deleted`, `enode:webhook:test`, `user:meter:discovered`, `user:meter:updated`, `user:meter:deleted`", "example": ["user:vehicle:discovered", "user:vehicle:updated", "user:vehicle:deleted"]}, "apiVersion": {"type": ["string", "null"], "description": "If provided, webhook payloads will be generated for this version. If not provided or `null`, the default API version of the client will be used when generating webhook payloads. Valid versions: `2024-01-01`, `2023-08-01`, `2023-05-01`, `2023-04-15`, `2023-04-01`, `2023-03-01`, `2023-02-01`", "example": "2023-08-01"}, "authentication": {"type": "object", "properties": {"headerName": {"type": "string", "minLength": 1, "maxLength": 128, "pattern": "^[a-z0-9-_]+$", "description": "Authentication header name.", "example": "x-api-key"}, "headerValue": {"type": "string", "minLength": 1, "maxLength": 256, "pattern": "^[a-zA-Z0-9_:;.,/\\\\\"'?!(){}[\\]@<>=\\-+*#$&`|~^% ]+$", "description": "Authentication header value.", "example": "AOIxcVLKxR2VpzlN5HIk14oPu80"}}, "required": ["headerName", "headerValue"], "additionalProperties": false, "description": "If provided, the authentication header will be sent with each webhook request."}}, "required": ["url", "secret"], "additionalProperties": false, "description": "Fields to update the webhook with."}, "WebhookResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the webhook.", "example": "1de1as-dsa12wed-15sa"}, "url": {"type": "string", "minLength": 8, "maxLength": 200, "description": "The HTTPS URL that webhook payloads should be sent to.", "example": "https://example.com/enode-webhooks/firehose", "x-format": {"uri": true}}, "events": {"type": "array", "items": {"type": "string"}, "default": ["*"], "description": "The events which will trigger the webhook.", "example": ["user:vehicle:discovered", "user:vehicle:updated", "user:vehicle:deleted"]}, "lastSuccess": {"type": "string", "description": "Time when a delivery to this webhook was last successful.", "example": "14-08-2023T13:01:00.000Z"}, "isActive": {"type": "boolean", "description": "Indicates whether the webhook is active or not.", "example": true}, "createdAt": {"type": "string", "description": "Time when the webhook was created.", "example": "14-08-2023T13:01:00.000Z"}, "apiVersion": {"type": ["string", "null"], "description": "API version of the payload. If null, the default API version of the client will be used when generating webhook payloads.", "example": "2023-08-01"}, "authentication": {"type": ["object", "null"], "properties": {"headerName": {"type": "string"}}, "required": ["headerName"], "additionalProperties": false}}, "required": ["id", "url", "lastSuccess", "isActive", "createdAt", "apiVersion", "authentication"], "additionalProperties": false, "description": "Webhook entity."}, "WebhookAlreadyExistsProblem": {"type": "object", "properties": {"type": {"type": "string", "description": "A URI reference that identifies the problem type."}, "title": {"type": "string", "description": "A short, human-readable summary of the problem type."}, "detail": {"type": "string", "description": "A human-readable explanation specific to this occurrence of the problem."}}, "required": ["type", "title", "detail"], "additionalProperties": false, "description": "Webhook already exists problem error", "example": {"type": "https://docs.enode.io/problems/bad-request", "title": "Webhook already exists.", "detail": "A webhook with the specified URL and API version already exists."}}, "WebhookUpdatePayload": {"type": "object", "properties": {"url": {"type": "string", "minLength": 8, "maxLength": 200, "description": "The HTTPS URL that webhook payloads should be sent to.", "example": "https://example.com/enode-webhooks/firehose", "x-format": {"uri": true}}, "secret": {"type": "string", "minLength": 6, "maxLength": 256, "description": "A cryptographically secure secret, generated and provided by your client. Refer to [Generating a secret](/docs/webhooks#generating-a-secret) for details on how to do this.", "example": "0Kvs1tAUQ69FOMBiWlt5XJSrruXMhWDiVbyrWaNm"}, "events": {"type": "array", "items": {"type": "string", "enum": ["*", "user:vehicle:discovered", "user:vehicle:updated", "user:vehicle:deleted", "user:vehicle:smart-charging-status-updated", "user:charge-action:updated", "user:vendor-action:updated", "user:schedule:execution-updated", "user:charger:discovered", "user:charger:updated", "user:charger:deleted", "user:hvac:discovered", "user:hvac:updated", "user:hvac:deleted", "user:inverter:discovered", "user:inverter:updated", "user:inverter:deleted", "user:credentials:invalidated", "user:battery:discovered", "user:battery:updated", "user:battery:deleted", "enode:webhook:test", "user:meter:discovered", "user:meter:updated", "user:meter:deleted"]}, "default": ["*"], "description": "The events which will trigger the webhook. Available events: `*`, `user:vehicle:discovered`, `user:vehicle:updated`, `user:vehicle:deleted`, `user:vehicle:smart-charging-status-updated`, `user:charge-action:updated`, `user:vendor-action:updated`, `user:schedule:execution-updated`, `user:charger:discovered`, `user:charger:updated`, `user:charger:deleted`, `user:hvac:discovered`, `user:hvac:updated`, `user:hvac:deleted`, `user:inverter:discovered`, `user:inverter:updated`, `user:inverter:deleted`, `user:credentials:invalidated`, `user:battery:discovered`, `user:battery:updated`, `user:battery:deleted`, `enode:webhook:test`, `user:meter:discovered`, `user:meter:updated`, `user:meter:deleted`", "example": ["user:vehicle:discovered", "user:vehicle:updated", "user:vehicle:deleted"]}, "apiVersion": {"type": ["string", "null"], "description": "If provided, webhook payloads will be generated for this version. If not provided or `null`, the default API version of the client will be used when generating webhook payloads. Valid versions: `2024-01-01`, `2023-08-01`, `2023-05-01`, `2023-04-15`, `2023-04-01`, `2023-03-01`, `2023-02-01`", "example": "2023-08-01"}, "authentication": {"type": ["object", "null"], "properties": {"headerName": {"type": "string", "minLength": 1, "maxLength": 128, "pattern": "^[a-z0-9-_]+$", "description": "Authentication header name.", "example": "x-api-key"}, "headerValue": {"type": "string", "minLength": 1, "maxLength": 256, "pattern": "^[a-zA-Z0-9_:;.,/\\\\\"'?!(){}[\\]@<>=\\-+*#$&`|~^% ]+$", "description": "Authentication header value.", "example": "AOIxcVLKxR2VpzlN5HIk14oPu80"}}, "required": ["headerName", "headerValue"], "additionalProperties": false, "description": "If provided, the authentication header will be sent with each webhook request."}}, "additionalProperties": false, "description": "Fields to update the webhook with."}, "PaginatedWebhookList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/WebhookResponse"}, "description": "Paginated list of webhooks."}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false, "description": "Paginated response of webhooks."}, "ChargerHealth": {"type": "object", "properties": {"vendor": {"$ref": "#/components/schemas/ChargerVendor"}, "displayName": {"$ref": "#/components/schemas/ChargerBrand"}, "status": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state of the Vendor. Currently always `READY`.", "example": "READY"}, "portalName": {"type": "string", "description": "The name of the first party service or app that the user normally logs into."}, "linkingStatus": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state for linking with the Vendor.\n\n  * `READY`: The Vendor is ready to be linked with.\n  * `ELEVATED_ERROR_RATE`: Multiple attempts may be needed to link successfully. We are showing a warning in Link UI.\n  * `OUTAGE`: The Vendor is currently not available for linking. We will show an error in Link UI and not let the user attempt to link with the Vendor.", "example": "READY"}}, "required": ["vendor", "displayName", "status", "portalName", "linkingStatus"], "additionalProperties": false}, "VehicleHealth": {"type": "object", "properties": {"vendor": {"$ref": "#/components/schemas/VehicleVendor"}, "displayName": {"$ref": "#/components/schemas/VehicleBrand"}, "status": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state of the Vendor. Currently always `READY`.", "example": "READY"}, "portalName": {"type": "string", "description": "The name of the first party service or app that the user normally logs into."}, "linkingStatus": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state for linking with the Vendor.\n\n  * `READY`: The Vendor is ready to be linked with.\n  * `ELEVATED_ERROR_RATE`: Multiple attempts may be needed to link successfully. We are showing a warning in Link UI.\n  * `OUTAGE`: The Vendor is currently not available for linking. We will show an error in Link UI and not let the user attempt to link with the Vendor.", "example": "READY"}}, "required": ["vendor", "displayName", "status", "portalName", "linkingStatus"], "additionalProperties": false}, "InverterHealth": {"type": "object", "properties": {"vendor": {"$ref": "#/components/schemas/InverterVendor"}, "displayName": {"$ref": "#/components/schemas/InverterBrand"}, "status": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state of the Vendor. Currently always `READY`.", "example": "READY"}, "portalName": {"type": "string", "description": "The name of the first party service or app that the user normally logs into."}, "linkingStatus": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state for linking with the Vendor.\n\n  * `READY`: The Vendor is ready to be linked with.\n  * `ELEVATED_ERROR_RATE`: Multiple attempts may be needed to link successfully. We are showing a warning in Link UI.\n  * `OUTAGE`: The Vendor is currently not available for linking. We will show an error in Link UI and not let the user attempt to link with the Vendor.", "example": "READY"}}, "required": ["vendor", "displayName", "status", "portalName", "linkingStatus"], "additionalProperties": false}, "HvacHealth": {"type": "object", "properties": {"vendor": {"$ref": "#/components/schemas/HvacVendor"}, "displayName": {"$ref": "#/components/schemas/HvacBrand"}, "status": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state of the Vendor. Currently always `READY`.", "example": "READY"}, "portalName": {"type": "string", "description": "The name of the first party service or app that the user normally logs into."}, "linkingStatus": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state for linking with the Vendor.\n\n  * `READY`: The Vendor is ready to be linked with.\n  * `ELEVATED_ERROR_RATE`: Multiple attempts may be needed to link successfully. We are showing a warning in Link UI.\n  * `OUTAGE`: The Vendor is currently not available for linking. We will show an error in Link UI and not let the user attempt to link with the Vendor.", "example": "READY"}}, "required": ["vendor", "displayName", "status", "portalName", "linkingStatus"], "additionalProperties": false}, "MeterHealth": {"type": "object", "properties": {"vendor": {"$ref": "#/components/schemas/MeterVendor"}, "displayName": {"$ref": "#/components/schemas/MeterBrand"}, "status": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state of the Vendor. Currently always `READY`.", "example": "READY"}, "portalName": {"type": "string", "description": "The name of the first party service or app that the user normally logs into."}, "linkingStatus": {"type": "string", "enum": ["READY", "ELEVATED_ERROR_RATE", "OUTAGE"], "description": "Ready-state for linking with the Vendor.\n\n  * `READY`: The Vendor is ready to be linked with.\n  * `ELEVATED_ERROR_RATE`: Multiple attempts may be needed to link successfully. We are showing a warning in Link UI.\n  * `OUTAGE`: The Vendor is currently not available for linking. We will show an error in Link UI and not let the user attempt to link with the Vendor.", "example": "READY"}}, "required": ["vendor", "displayName", "status", "portalName", "linkingStatus"], "additionalProperties": false}, "CreateSimulatedVehiclePayload": {"type": "object", "properties": {"vendor": {"allOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"description": "Vendor of the simulated vehicle"}]}, "username": {"type": "string", "description": "Username for linking the simulated vehicle"}, "password": {"type": "string", "description": "Password for linking the simulated vehicle"}, "name": {"type": "string", "description": "Name of the simulated vehicle", "example": "Test vehicle"}, "model": {"type": "string", "description": "Simulated vehicle model", "example": "Model S"}, "year": {"type": "number", "description": "Simulated vehicle production year", "example": 2020}, "userId": {"type": "string", "description": "Specifying a user ID automatically links a vehicle with the simulated vehicle.", "example": "user123"}}, "required": ["vendor", "username", "password", "name", "model", "year"], "additionalProperties": false}, "SimulatedVehicleChargeState": {"type": "object", "properties": {"batteryLevel": {"type": ["number", "null"], "description": "Remaining battery in percent"}, "range": {"type": ["number", "null"], "description": "Estimated remaining kilometers"}, "batteryCapacity": {"type": ["number", "null"], "description": "Vehicle's maximum physical battery capacity in kWh."}, "chargeLimit": {"type": ["number", "null"], "description": "Charge limit, as a percent of `batteryCapacity`."}, "chargeRate": {"type": ["number", "null"], "description": "The current charge rate in kW.\n\nThis property is only available when the vehicle is charging, and is `null` any other time."}, "chargeTimeRemaining": {"type": ["number", "null"], "description": "Estimated time until the current charging intent is completed, in minutes.\n\nThis property is only available when the vehicle is charging, and is `null` any other time."}, "powerDeliveryState": {"$ref": "#/components/schemas/VehiclePowerDeliveryState"}}, "required": ["batteryLevel", "range", "batteryCapacity", "chargeLimit", "chargeRate", "chargeTimeRemaining", "powerDeliveryState"], "additionalProperties": false}, "SimulatedVehicle": {"type": "object", "properties": {"id": {"type": "string", "description": "Simulated vehicle ID"}, "name": {"type": "string", "description": "Name of the simulated vehicle", "example": "Test vehicle 1"}, "username": {"type": "string", "description": "Username for linking the simulated vehicle", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for linking the simulated vehicle", "example": "password"}, "vin": {"type": "string", "description": "VIN (random value) of the simulated vehicle", "example": "5YJ3F7EA1LF700671"}, "vendor": {"allOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"description": "Vendor of the simulated vehicle"}]}, "model": {"type": "string", "description": "Simulated vehicle model", "example": "Model S P85"}, "year": {"type": "number", "description": "Simulated vehicle production year", "example": 2020}, "isReachable": {"type": "boolean", "description": "Whether live data from the simulated vehicle is currently available from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the vehicle's cloud service if that service has determined that its cache is valid.", "example": true}, "chargeState": {"allOf": [{"$ref": "#/components/schemas/SimulatedVehicleChargeState"}, {"type": "object", "properties": {"isPluggedIn": {"type": ["boolean", "null"], "description": "Indicates whether the simulated vehicle is connected to a charging box (regardless of whether it is actually charging)"}, "isCharging": {"type": ["boolean", "null"], "description": "Indicates whether the simulated vehicle is currently receiving power and actively charging its battery."}}, "required": ["isPluggedIn", "isCharging"], "additionalProperties": false}]}, "location": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees"}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees"}}, "required": ["longitude", "latitude"], "additionalProperties": false}, "userId": {"type": ["string", "null"], "description": "The ID of the user that linked this vehicle.", "example": "bb28471e-cde6-4eff-ace4-9a7f4f50882a"}, "linkedVehicleId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the linked vehicle.", "example": "05ee9df5-d072-4ee8-b773-399dec8f5693", "x-format": {"guid": true}}}, "required": ["id", "name", "username", "password", "vin", "vendor", "model", "year", "isReachable", "chargeState", "location", "userId", "linkedVehicleId"], "additionalProperties": false}, "UpdateSimulatedVehiclePayload": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for linking the simulated vehicle", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for linking the simulated vehicle", "example": "password"}, "name": {"type": "string", "description": "Name of the simulated vehicle", "example": "Test vehicle 1"}, "isReachable": {"type": "boolean", "description": "Whether live data from the simulated vehicle is currently available from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the vehicle's cloud service if that service has determined that its cache is valid.", "example": true}, "chargeState": {"$ref": "#/components/schemas/SimulatedVehicleChargeState"}, "location": {"type": "object", "properties": {"longitude": {"type": ["number", "null"], "description": "Longitude in degrees"}, "latitude": {"type": ["number", "null"], "description": "Latitude in degrees"}}, "required": ["longitude", "latitude"], "additionalProperties": false}}, "additionalProperties": false}, "PaginatedVehiclesList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/SimulatedVehicle"}}, "pagination": {"$ref": "#/components/schemas/PaginationCursors"}}, "required": ["data", "pagination"], "additionalProperties": false}, "SystemHeartbeat": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["system:heartbeat"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "createdAt": {"type": "string"}, "pendingEvents": {"type": "number", "minimum": 0}}, "required": ["version", "event", "user", "createdAt", "pendingEvents"]}, "UserVehicleDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:vehicle:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "vehicle": {"allOf": [{"$ref": "#/components/schemas/Vehicle"}, {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the vehicle is currently positioned at (if any).", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}]}}, "required": ["version", "event", "user", "vehicle"], "additionalProperties": false}, "UserVehicleUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:vehicle:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "vehicle": {"allOf": [{"$ref": "#/components/schemas/Vehicle"}, {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the vehicle is currently positioned at (if any).", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}]}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "vehicle", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserVehicleDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:vehicle:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "vehicle": {"allOf": [{"$ref": "#/components/schemas/Vehicle"}, {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the vehicle is currently positioned at (if any).", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}]}}, "required": ["version", "event", "user", "vehicle"], "additionalProperties": false}, "UserVehicleSmartChargingStatusUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:vehicle:smart-charging-status-updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "smartChargingStatus": {"type": "object", "properties": {"updatedAt": {"type": "string", "format": "date-time", "description": "Time when the Smart Charging Status was recalculated, irrespective of whether or not any values changed", "example": "2020-04-07T17:04:26Z"}, "vehicleId": {"type": "string", "description": "ID of the vehicle to which this Smart Charging Status represents", "example": "4b5ada14-ea87-4ca1-aab9-3c979c34cf4a"}, "userId": {"type": "string", "description": "ID of the user that owns this vehicle", "example": "3b568b76-e30a-426e-aacd-609db4d7be81"}, "vendor": {"allOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"description": "Vendor of the Vehicle"}]}, "state": {"$ref": "#/components/schemas/SmartChargeState"}, "stateChangedAt": {"type": "string", "format": "date-time", "description": "Time when the `state` property transitioned to its current value", "example": "2020-04-07T17:04:26Z"}, "consideration": {"$ref": "#/components/schemas/Consideration"}, "plan": {"allOf": [{"$ref": "#/components/schemas/VehicleSmartChargingPlan"}, {"type": ["object", "null"], "description": "`null` by default, but during all of the `PLAN:*` states,  the value is populated with a description of the active plan"}]}, "smartOverride": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": ["object", "null"]}]}}, "required": ["updatedAt", "vehicleId", "userId", "vendor", "state", "stateChangedAt", "consideration", "plan", "smartOverride"], "additionalProperties": false}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "smartChargingStatus", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserActionUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:vendor-action:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "vendorAction": {"anyOf": [{"$ref": "#/components/schemas/HvacActionPermanentHold"}, {"$ref": "#/components/schemas/HvacActionFollowSchedule"}, {"$ref": "#/components/schemas/MaxCurrentAction"}, {"$ref": "#/components/schemas/ChargeAction"}, {"$ref": "#/components/schemas/OperationModeAction"}]}, "updatedFields": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "array", "items": {"type": "string"}}, {"type": "array", "items": {"type": "string"}}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["version", "event", "user", "vendorAction", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserTemperatureScheduleStatusUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:schedule:execution-updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "status": {"$ref": "#/components/schemas/TemperatureScheduleStatus"}, "schedule": {"$ref": "#/components/schemas/TemperatureScheduleResponse"}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "status", "schedule", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserChargeScheduleStatusUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:schedule:execution-updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "status": {"$ref": "#/components/schemas/ChargeScheduleStatus"}, "schedule": {"$ref": "#/components/schemas/ChargeScheduleResponse"}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "status", "schedule", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserChargerUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:charger:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "charger": {"$ref": "#/components/schemas/Charger"}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "charger", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserChargerDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:charger:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "charger": {"$ref": "#/components/schemas/Charger"}}, "required": ["version", "event", "user", "charger"], "additionalProperties": false}, "UserChargerDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:charger:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "charger": {"$ref": "#/components/schemas/Charger"}}, "required": ["version", "event", "user", "charger"], "additionalProperties": false}, "UserCredentialsInvalidated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:credentials:invalidated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "vendor": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Machine-friendly name of the vendor. Use this in API requests.", "example": "TESLA"}}, "required": ["version", "event", "user", "vendor"], "additionalProperties": false}, "UserHvacDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:hvac:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "hvac": {"$ref": "#/components/schemas/Hvac"}}, "required": ["version", "event", "user", "hvac"], "additionalProperties": false}, "UserHvacUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:hvac:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "hvac": {"$ref": "#/components/schemas/Hvac"}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "hvac", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserHvacDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:hvac:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "hvac": {"$ref": "#/components/schemas/Hvac"}}, "required": ["version", "event", "user", "hvac"], "additionalProperties": false}, "UserInverterDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:inverter:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "inverter": {"$ref": "#/components/schemas/Inverter"}}, "required": ["version", "event", "user", "inverter"], "additionalProperties": false}, "UserInverterUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:inverter:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "inverter": {"$ref": "#/components/schemas/Inverter"}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "inverter", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserInverterDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:inverter:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "inverter": {"$ref": "#/components/schemas/Inverter"}}, "required": ["version", "event", "user", "inverter"], "additionalProperties": false}, "UserBatteryDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:battery:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "battery": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the battery object", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "userId": {"type": "string", "description": "The ID of the user that linked this battery.", "example": "4f6fecd0-bdae-49be-b6e8-ee442e1e3da9"}, "vendor": {"$ref": "#/components/schemas/BatteryVendor"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the battery is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the battery was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the battery is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the battery's cloud service if that service has determined that its cache is valid."}, "chargeState": {"$ref": "#/components/schemas/BatteryChargeState"}, "config": {"$ref": "#/components/schemas/BatteryConfig"}, "information": {"$ref": "#/components/schemas/BatteryInformation"}, "location": {"$ref": "#/components/schemas/BatteryLocation"}, "capabilities": {"$ref": "#/components/schemas/BatteryCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this battery."}}, "required": ["id", "userId", "vendor", "locationId", "lastSeen", "isReachable", "chargeState", "config", "information", "location", "capabilities", "scopes"], "additionalProperties": false}}, "required": ["version", "event", "user", "battery"], "additionalProperties": false}, "UserBatteryUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:battery:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "battery": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the battery object", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "userId": {"type": "string", "description": "The ID of the user that linked this battery.", "example": "4f6fecd0-bdae-49be-b6e8-ee442e1e3da9"}, "vendor": {"$ref": "#/components/schemas/BatteryVendor"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the battery is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the battery was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the battery is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the battery's cloud service if that service has determined that its cache is valid."}, "chargeState": {"$ref": "#/components/schemas/BatteryChargeState"}, "config": {"$ref": "#/components/schemas/BatteryConfig"}, "information": {"$ref": "#/components/schemas/BatteryInformation"}, "location": {"$ref": "#/components/schemas/BatteryLocation"}, "capabilities": {"$ref": "#/components/schemas/BatteryCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this battery."}}, "required": ["id", "userId", "vendor", "locationId", "lastSeen", "isReachable", "chargeState", "config", "information", "location", "capabilities", "scopes"], "additionalProperties": false}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "battery", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserBatteryDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:battery:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "battery": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the battery object", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "userId": {"type": "string", "description": "The ID of the user that linked this battery.", "example": "4f6fecd0-bdae-49be-b6e8-ee442e1e3da9"}, "vendor": {"$ref": "#/components/schemas/BatteryVendor"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the battery is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the battery was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the battery is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the battery's cloud service if that service has determined that its cache is valid."}, "chargeState": {"$ref": "#/components/schemas/BatteryChargeState"}, "config": {"$ref": "#/components/schemas/BatteryConfig"}, "information": {"$ref": "#/components/schemas/BatteryInformation"}, "location": {"$ref": "#/components/schemas/BatteryLocation"}, "capabilities": {"$ref": "#/components/schemas/BatteryCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this battery."}}, "required": ["id", "userId", "vendor", "locationId", "lastSeen", "isReachable", "chargeState", "config", "information", "location", "capabilities", "scopes"], "additionalProperties": false}}, "required": ["version", "event", "user", "battery"], "additionalProperties": false}, "UserMeterDiscovered": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:meter:discovered"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "meter": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the meter object"}, "userId": {"type": "string", "description": "The ID of the user that linked this meter."}, "vendor": {"$ref": "#/components/schemas/MeterVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the meter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the meter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the meter's cloud service if that service has determined that its cache is valid."}, "information": {"$ref": "#/components/schemas/MeterInformation"}, "energyState": {"$ref": "#/components/schemas/MeterEnergyState"}, "location": {"$ref": "#/components/schemas/MeterLocation"}, "capabilities": {"$ref": "#/components/schemas/MeterCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this meter. ", "example": ["meter:read:data", "meter:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "energyState", "location", "capabilities", "scopes"], "additionalProperties": false}}, "required": ["version", "event", "user", "meter"], "additionalProperties": false}, "UserMeterUpdated": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:meter:updated"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "meter": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the meter object"}, "userId": {"type": "string", "description": "The ID of the user that linked this meter."}, "vendor": {"$ref": "#/components/schemas/MeterVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the meter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the meter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the meter's cloud service if that service has determined that its cache is valid."}, "information": {"$ref": "#/components/schemas/MeterInformation"}, "energyState": {"$ref": "#/components/schemas/MeterEnergyState"}, "location": {"$ref": "#/components/schemas/MeterLocation"}, "capabilities": {"$ref": "#/components/schemas/MeterCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this meter. ", "example": ["meter:read:data", "meter:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "energyState", "location", "capabilities", "scopes"], "additionalProperties": false}, "updatedFields": {"type": "array", "items": {"type": "string"}}}, "required": ["version", "event", "user", "meter", "<PERSON><PERSON><PERSON>s"], "additionalProperties": false}, "UserMeterDeleted": {"type": "object", "properties": {"version": {"type": "string", "description": "API version of the payload. Example: `2024-01-01`", "example": "2024-01-01"}, "event": {"type": "string", "enum": ["user:meter:deleted"]}, "user": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}, "meter": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the meter object"}, "userId": {"type": "string", "description": "The ID of the user that linked this meter."}, "vendor": {"$ref": "#/components/schemas/MeterVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the meter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the meter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the meter's cloud service if that service has determined that its cache is valid."}, "information": {"$ref": "#/components/schemas/MeterInformation"}, "energyState": {"$ref": "#/components/schemas/MeterEnergyState"}, "location": {"$ref": "#/components/schemas/MeterLocation"}, "capabilities": {"$ref": "#/components/schemas/MeterCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this meter. ", "example": ["meter:read:data", "meter:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "energyState", "location", "capabilities", "scopes"], "additionalProperties": false}}, "required": ["version", "event", "user", "meter"], "additionalProperties": false}, "PaginationQueryAfter": {"type": "string"}, "PaginationQueryBefore": {"type": "string"}, "PaginationQueryPageSize": {"type": "integer", "exclusiveMinimum": 0, "default": 50}, "UserPathId": {"type": "string", "description": "A unique identifier of your choice representing your user, e.g. a stable UUID you keep in your datastore. If a user entity matching the provided `userId` does not exist in your client, it will be created before the link session is created."}, "PathBatteryId": {"type": "string", "format": "uuid", "description": "The ID of the battery to look up.", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "PathBatteryTargetId": {"type": "string", "format": "uuid", "description": "The ID of the battery being targeted.", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "ActionPathId": {"type": "string", "format": "uuid", "description": "ID of the Action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "ChargerPathId": {"type": "string", "description": "ID of the Charger.", "example": "ad84e742-0f46-4cf4-b0db-7d890f8f23f5"}, "HvacPathId": {"type": "string", "description": "ID of the HVAC unit.", "example": "8f39fa8d-8f10-4984-a319-741dc23848c0"}, "InterventionPathId": {"type": "string", "format": "uuid", "description": "ID of the intervention.", "example": "ad84e742-0f46-4cf4-b0db-7d890f8f23f5"}, "PathInverterId": {"type": "string", "format": "uuid", "description": "ID of the solar inverter", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "LocationPathId": {"type": "string", "format": "uuid", "description": "ID of the Location.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "PathMeterId": {"type": "string", "format": "uuid", "description": "The ID of the meter you are looking up", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "PathScheduleId": {"type": "string", "format": "uuid", "description": "ID of the Schedule.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "Vendor": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Vendor to be unlinked.", "example": "TESLA"}, "VehiclePathId": {"type": "string", "description": "ID of the Vehicle.", "example": "ad84e742-0f46-4cf4-b0db-7d890f8f23f5"}, "SmartChargingPlanPathId": {"anyOf": [{"type": "string", "format": "uuid", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, {"type": "string", "enum": ["latest"]}], "description": "ID of the Smart Charging Plan", "example": "latest"}, "WebhookPathId": {"type": "string", "format": "uuid", "description": "ID of the Webhook.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "SimulatedVehiclePathId": {"type": "string", "format": "uuid", "description": "ID of the simulated vehicle.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}}, "parameters": {"PaginationQueryAfter": {"schema": {"$ref": "#/components/schemas/PaginationQueryAfter"}, "required": false, "description": "Opaque cursor used to fetch next page. Cannot be set together with `before`. Refer to [Pagination](/api/reference#pagination) for more information.", "name": "after", "in": "query", "example": "MjAyMy0wNy0xOFQxMDowODowMi4zNzNa"}, "PaginationQueryBefore": {"schema": {"$ref": "#/components/schemas/PaginationQueryBefore"}, "required": false, "description": "Opaque cursor used to fetch previous page. Cannot be used together with `after`. Refer to [Pagination](/api/reference#pagination) for more information.", "name": "before", "in": "query", "example": "MjAyMy0wNy0xOFQxMDowODowMi4zNzNa"}, "PaginationQueryPageSize": {"schema": {"$ref": "#/components/schemas/PaginationQueryPageSize"}, "required": false, "description": "Number of records to return per page", "name": "pageSize", "in": "query", "example": 50}, "UserPathId": {"schema": {"$ref": "#/components/schemas/UserPathId"}, "required": true, "name": "userId", "in": "path"}, "PathBatteryId": {"schema": {"$ref": "#/components/schemas/PathBatteryId"}, "required": true, "name": "batteryId", "in": "path"}, "PathBatteryTargetId": {"schema": {"$ref": "#/components/schemas/PathBatteryTargetId"}, "required": true, "name": "batteryId", "in": "path"}, "ActionPathId": {"schema": {"$ref": "#/components/schemas/ActionPathId"}, "required": true, "name": "actionId", "in": "path"}, "ChargerPathId": {"schema": {"$ref": "#/components/schemas/ChargerPathId"}, "required": true, "name": "chargerId", "in": "path"}, "HvacPathId": {"schema": {"$ref": "#/components/schemas/HvacPathId"}, "required": true, "name": "hvacId", "in": "path"}, "InterventionPathId": {"schema": {"$ref": "#/components/schemas/InterventionPathId"}, "required": true, "name": "interventionId", "in": "path"}, "PathInverterId": {"schema": {"$ref": "#/components/schemas/PathInverterId"}, "required": true, "name": "inverterId", "in": "path"}, "LocationPathId": {"schema": {"$ref": "#/components/schemas/LocationPathId"}, "required": true, "name": "locationId", "in": "path"}, "PathMeterId": {"schema": {"$ref": "#/components/schemas/PathMeterId"}, "required": true, "name": "meterId", "in": "path"}, "PathScheduleId": {"schema": {"$ref": "#/components/schemas/PathScheduleId"}, "required": true, "name": "scheduleId", "in": "path"}, "Vendor": {"schema": {"$ref": "#/components/schemas/Vendor"}, "required": true, "name": "vendor", "in": "path"}, "VehiclePathId": {"schema": {"$ref": "#/components/schemas/VehiclePathId"}, "required": true, "name": "vehicleId", "in": "path"}, "SmartChargingPlanPathId": {"schema": {"$ref": "#/components/schemas/SmartChargingPlanPathId"}, "required": true, "name": "smartChargingPlanId", "in": "path"}, "WebhookPathId": {"schema": {"$ref": "#/components/schemas/WebhookPathId"}, "required": true, "name": "webhookId", "in": "path"}, "SimulatedVehiclePathId": {"schema": {"$ref": "#/components/schemas/SimulatedVehiclePathId"}, "required": true, "name": "simulatedVehicleId", "in": "path"}}}, "paths": {"/batteries": {"get": {"summary": "List Batteries", "operationId": "listBatteries", "description": "Returns a paginated list of all Batteries.", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedBatteryList"}}}}}}}, "/users/{userId}/batteries": {"get": {"summary": "List User Batteries", "description": "Returns a paginated list of batteries for the given userId.", "operationId": "listUserBatteries", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedBatteryList"}}}}}}}, "/batteries/{batteryId}": {"get": {"summary": "Get Battery", "operationId": "getBattery", "tags": ["Batteries", "Beta"], "parameters": [{"$ref": "#/components/parameters/PathBatteryId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the battery object", "example": "54d827e1-8355-4fed-97b5-55940d1d09ba"}, "userId": {"type": "string", "description": "The ID of the user that linked this battery.", "example": "4f6fecd0-bdae-49be-b6e8-ee442e1e3da9"}, "vendor": {"$ref": "#/components/schemas/BatteryVendor"}, "locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the battery is currently positioned at (if any).", "example": "8d90101b-3f2f-462a-bbb4-1ed320d33bbe", "x-format": {"guid": true}}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the battery was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the battery is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the battery's cloud service if that service has determined that its cache is valid."}, "chargeState": {"$ref": "#/components/schemas/BatteryChargeState"}, "config": {"$ref": "#/components/schemas/BatteryConfig"}, "information": {"$ref": "#/components/schemas/BatteryInformation"}, "location": {"$ref": "#/components/schemas/BatteryLocation"}, "capabilities": {"$ref": "#/components/schemas/BatteryCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this battery."}}, "required": ["id", "userId", "vendor", "locationId", "lastSeen", "isReachable", "chargeState", "config", "information", "location", "capabilities", "scopes"], "additionalProperties": false}}}}}}}, "/batteries/{batteryId}/operation-mode": {"post": {"summary": "Set Operation Mode for Battery", "operationId": "postUserSetOperationMode", "description": "Request an `operationMode` change for a battery. This request creates an Action that will retry until the battery's `operationMode` matches the expected value. The Action must complete before any further commands can be sent to the battery. Only one Action can be active for a specific battery at a time. If a new Action is created, the previous Action will be automatically cancelled and transitioned to the `CANCELLED` state. Regardless of operation mode, the battery's charge limit will not fall below `dischargeLimit` except in emergency power situations. Transitions can be tracked via the `user:vendor-action:updated` webhook event or [Get Operation Mode Action](/api/reference#getBatteriesAction).", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PathBatteryTargetId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/setBatteryOperationModePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationModeAction"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the battery cannot perform the action.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/batteries/actions/{actionId}": {"get": {"summary": "Get Operation Mode Action", "description": "Returns the current state of the requested Action.", "operationId": "getBatteriesAction", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationModeAction"}}}}, "404": {"description": "Action not found."}}}}, "/batteries/actions/{actionId}/cancel": {"post": {"summary": "Cancel Battery Action", "description": "Cancels a pending battery Action, halting any further attempts by Enode to execute it.\n\nNote: This only updates the Action's status to `CANCELLED` within Enode and does not reflect a change in the vendor's cloud. Thus any pending Action in the vendor's cloud might still be executed.", "operationId": "cancelBatteryAction", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationModeAction"}, "example": {"id": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "createdAt": "2023-08-01T01:00:00Z", "updatedAt": "2023-08-01T01:00:00Z", "completedAt": "2023-08-01T01:00:00Z", "state": "CANCELLED", "targetId": "f723fed5-73aa-4004-b7f0-89e5173ee15e", "targetType": "battery", "targetState": {"operationMode": "IMPORT_FOCUS"}}}}}, "404": {"description": "Action not found."}, "409": {"description": "Action already in a resolved state and can therefore not be cancelled.", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the action.", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "userId": {"type": "string", "description": "The ID of the user that owns the target of this action.", "example": "d5bd4771-864e-4ae5-b913-dfb5cdcd5772"}, "createdAt": {"type": "string", "format": "date-time", "description": "Time when this action was created", "example": "2020-04-07T17:04:26Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Time when this action was last updated", "example": "2020-04-07T17:04:26Z"}, "completedAt": {"type": ["string", "null"], "format": "date-time", "description": "Time when the action transitioned to a non-pending state.", "example": "2020-04-07T17:04:26Z"}, "targetId": {"type": "string", "description": "ID of the battery which this action is controlling.", "example": "ac03a513-5494-4e1c-9dd7-2a29dc024312"}, "targetType": {"type": "string", "enum": ["battery"]}, "targetState": {"$ref": "#/components/schemas/setBatteryOperationModePayload"}, "state": {"type": "string", "enum": ["CONFIRMED", "FAILED", "CANCELLED"], "description": "The real-time status of an action executed on a target. \n\n * `CONFIRMED`: Successful transition of the target to the desired state. \n * `FAILED`: The target did not respond to the action before timing out. Enode has ceased sending additional commands. \n * `CANCELLED`: A required precondition was not met during the action's timeout window or another action has been sent to the target, overriding this one."}}, "required": ["id", "userId", "createdAt", "updatedAt", "completedAt", "targetId", "targetType", "targetState", "state"], "additionalProperties": false}}}}}}}, "/batteries/{batteryId}/refresh-hint": {"post": {"summary": "Refresh Battery Data", "description": "Use this endpoint to initiate an expedited data refresh for the specified battery.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "batteriesRefreshHint", "tags": ["Batteries", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PathBatteryTargetId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified battery was not found."}}}}, "/chargers": {"get": {"summary": "List Chargers", "operationId": "listChargers", "description": "Returns a paginated list of all Chargers.", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedChargerList"}}}}}}}, "/users/{userId}/chargers": {"get": {"summary": "List User Chargers", "description": "Returns a paginated list of chargers for the given userId.", "operationId": "listUserChargers", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedChargerList"}}}}}}}, "/chargers/{chargerId}": {"get": {"summary": "Get Charger", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Charger"}}}}}}, "put": {"summary": "Update Charger", "operationId": "update<PERSON><PERSON>ger", "description": "Update the `locationId` field on a Charger.", "tags": ["Chargers", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerUpdatePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Charger"}}}}}}}, "/chargers/{chargerId}/charging": {"post": {"summary": "Control Charging", "operationId": "controlChargerCharging", "description": "Request for a charger to start or stop charging. This request creates an Action that will retry until the charger's `powerDeliveryState` matches the expected value. The Action must complete before any further commands are sent to the charger. Only one Action can be active for a specific charger at a time. If a new Action is created, the previous Action will be automatically cancelled and transitioned to the `CANCELLED` state. Transitions can be tracked via the `user:vendor-action:updated` webhook event or [Get Charger Action](/api/reference#getChargersAction).\n\n This endpoint returns an error with status code 422 if the charger is controlled by a schedule. To restore user control, either disable the schedule or use [Create Smart Override](/api/reference#chargerCreateSmartOverride) to temporarily enable charging.", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"allOf": [{"$ref": "#/components/schemas/ChargingAction"}, {"description": "Charging action to perform"}]}}, "required": ["action"], "additionalProperties": false}}}}, "responses": {"200": {"description": "Resulting charge action", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeAction"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the charger cannot perform the action, is fully charged, or is already in the desired state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "422": {"description": "Charger controlled by a Schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/chargers/{chargerId}/max-current": {"post": {"summary": "Set Max Current", "operationId": "postSetChargerMaxCurrent", "description": "Register for a change of the `maxCurrent` field on a charger. This request creates an Action that will retry until the charger's `maxCurrent` matches the expected value. The Action must complete before any further commands are sent to the charger. Only one Action can be active for a specific charger at a time. If a new Action is created, the previous Action will be automatically cancelled and transitioned to the `CANCELLED` state. Transitions can be tracked via the `user:vendor-action:updated` webhook event or [Get Charger Action](/api/reference#getChargersAction).", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TargetMaxCurrent"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaxCurrentAction"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the charger cannot perform the action.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/chargers/actions/{actionId}": {"get": {"summary": "Get Charger Action", "description": "Returns the current state of the requested Action.", "operationId": "getChargersAction", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerAction"}}}}, "404": {"description": "Action not found."}}}}, "/chargers/actions/{actionId}/cancel": {"post": {"summary": "Cancel Charger Action", "description": "Cancels a pending Action, halting any further attempts by Enode to execute it.\n\nNote: This only updates the Action's status to `CANCELLED` within Enode and does not reflect a change in the vendor's cloud. Thus any pending Action in the vendor's cloud might still be executed.", "operationId": "cancelChargerAction", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerAction"}, "example": {"id": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "createdAt": "2023-08-01T01:00:00Z", "updatedAt": "2023-08-01T01:00:00Z", "completedAt": "2023-08-01T01:00:00Z", "state": "CONFIRMED", "targetId": "f723fed5-73aa-4004-b7f0-89e5173ee15e", "targetType": "charger", "kind": "START", "failureReason": null}}}}, "404": {"description": "Action not found."}, "409": {"description": "Action already in a resolved state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerAction"}, "example": {"id": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "createdAt": "2023-08-01T01:00:00Z", "updatedAt": "2023-08-01T01:00:00Z", "completedAt": "2023-08-01T01:00:00Z", "state": "CONFIRMED", "targetId": "f723fed5-73aa-4004-b7f0-89e5173ee15e", "targetType": "charger", "kind": "START", "failureReason": null}}}}}}}, "/chargers/{chargerId}/refresh-hint": {"post": {"summary": "Refresh Charger Data", "description": "Use this endpoint to initiate an expedited data refresh for the specified charger.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "chargersRefreshHint", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified charger was not found."}}}}, "/chargers/{chargerId}/smart-override": {"post": {"summary": "Create Smart Override", "operationId": "chargerCreateSmartOverride", "description": "Overrides an active smart feature by forcing the charger to start charging. This feature is meant to be used in situations where the user wants to charge immediately without disabling other smart features. The override remains active until the charger stops charging, or until the [End Smart Override](/api/reference#chargerEndSmartOverride) endpoint is called. When the override ends, the overridden smart feature will regain control of the charger. This endpoint should not be used for standard charge control, use the [Control Charging](/api/reference#postVehiclesVehicleidCharging) endpoint instead.", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerOnlySmartOverrideResponse"}}}}}}, "delete": {"summary": "End Smart Override", "operationId": "chargerEndSmartOverride", "description": "Ends any active Smart Override for the charger specified by `chargerId`. If previously configured, Schedules or Smart Charging will resume control over the target charger. Note that this does not mean the charger will stop charging, only that it will return to the state expected by the active Schedule or Smart Charging Plan.", "tags": ["Chargers"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerOnlySmartOverrideResponse"}}}}, "404": {"description": "No Smart Override Exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/chargers/{chargerId}/smart-charging-policy": {"get": {"summary": "Get Smart Charging Policy", "operationId": "chargerSmartPolicy", "description": "Get the configured smart charging policy for this charger.", "tags": ["Chargers", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerSmartChargingPolicy"}}}}}}, "put": {"summary": "Update Smart Charging Policy", "operationId": "updateChargerSmartPolicy", "description": "Update the configured smart charging policy for this charger.", "tags": ["Chargers", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartialChargerSmartChargingPolicy"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargerSmartChargingPolicy"}}}}}}}, "/chargers/{chargerId}/smart-charging-status": {"get": {"summary": "Get Smart Charging Status", "operationId": "chargerSmartChargingStatus", "description": "Get the current smart charging status for this charger", "tags": ["Chargers", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ChargerPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"chargerId": {"type": "string", "description": "Charger ID"}, "userId": {"type": "string", "description": "User ID"}, "state": {"$ref": "#/components/schemas/ChargerState"}, "chargingIntervals": {"oneOf": [{"$ref": "#/components/schemas/ChargerSmartChargingIntervals"}]}}, "required": ["chargerId", "userId", "state", "chargingIntervals"], "additionalProperties": false}}}}}}}, "/hvacs": {"get": {"summary": "List HVAC units", "operationId": "listHVACs", "description": "Paginated list of HVAC units", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedHVACList"}}}}}}}, "/users/{userId}/hvacs": {"get": {"summary": "List User HVAC units", "description": "Paginated list of HVAC units for the given User", "operationId": "listUserHVACs", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedHVACList"}}}}}}}, "/hvacs/{hvacId}/smart-policy": {"get": {"summary": "Get HVAC unit Smart Policy", "description": "Get HVAC unit smart policy", "operationId": "getHvacsHvacIdSmartPolicy", "tags": ["HVAC", "Experimental"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacSmartPolicy"}}}}}}, "put": {"summary": "Update HVAC unit Smart Policy", "operationId": "updateHvacSmartPolicy", "description": "Updates the smart policy for an HVAC unit", "tags": ["HVAC", "Experimental"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartialHvacSmartPolicy"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacSmartPolicy"}}}}}}}, "/hvacs/{hvacId}/smart-status": {"get": {"summary": "Get Smart HVAC unit Status", "operationId": "getHvacSmartStatus", "description": "Get the status of a smart HVAC unit", "tags": ["HVAC", "Experimental"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacSmartStatus"}}}}}}}, "/hvacs/{hvacId}": {"put": {"summary": "Set Location for an HVAC unit", "operationId": "updateHVAC", "description": "Update the `locationId` field on an HVAC unit.", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/hvacUpdatePayload"}}}}, "responses": {"200": {"description": "Successfully updated HVAC unit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Hvac"}}}}}}, "get": {"summary": "Get HVAC Unit", "operationId": "getHVAC", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Hvac"}}}}}}}, "/hvacs/actions/{actionId}": {"get": {"summary": "Get Action", "description": "Returns the current state of the requested action.", "operationId": "getHvacsAction", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacAction"}}}}, "404": {"description": "Action not found."}}}}, "/hvacs/actions/{actionId}/cancel": {"post": {"summary": "Cancel HVAC Action", "description": "Cancels a pending HVAC action, halting any further attempts by Enode to execute it.\n\nNote: This only updates the action's status to `CANCELLED` within Enode and does not reflect a change in the vendor's cloud. Thus any pending action in the vendor's cloud might still be executed.", "operationId": "cancelHvacAction", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacAction"}, "example": {"id": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "createdAt": "2023-08-01T01:00:00Z", "updatedAt": "2023-08-01T01:00:00Z", "completedAt": "2023-08-01T01:00:00Z", "state": "CANCELLED", "targetId": "f723fed5-73aa-4004-b7f0-89e5173ee15e", "targetType": "hvac", "target": {"coolSetpoint": 21, "mode": "COOL", "holdType": "PERMANENT"}}}}}, "404": {"description": "Action not found."}, "409": {"description": "Action already in a resolved state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacAction"}, "example": {"id": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "createdAt": "2023-08-01T01:00:00Z", "updatedAt": "2023-08-01T01:00:00Z", "completedAt": "2023-08-01T01:00:00Z", "state": "CONFIRMED", "targetId": "f723fed5-73aa-4004-b7f0-89e5173ee15e", "targetType": "hvac", "target": {"coolSetpoint": 21, "mode": "COOL", "holdType": "PERMANENT"}}}}}}}}, "/hvacs/{hvacId}/follow-schedule": {"post": {"summary": "Set HVAC unit to follow device schedule", "operationId": "postSetHvacFollowSchedule", "description": "Tell an HVAC unit to follow the schedule set on the device. Only available if the target's `capabilities.setFollowSchedule.isCapable` is set to `true`. This endpoint can be used to cancel permanent holds. We retry sending the command until the HVAC unit's fields transition to the expected values. Note that this request will complete before any commands are sent to the HVAC unit. You may react to transitions by listening for the `user:vendor-action:updated` webhook event or polling the [HVAC action endpoint](/api/reference#getHvacsAction).", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "responses": {"200": {"description": "Resulting HVAC action", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacActionFollowSchedule"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the HVAC unit cannot perform the action.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "422": {"description": "HVAC unit controlled by an Enode Schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/hvacs/{hvacId}/refresh-hint": {"post": {"summary": "Refresh HVAC unit data", "description": "Use this endpoint to initiate an expedited data refresh for the specified HVAC unit.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "hvacsRefreshHint", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified hvac was not found."}}}}, "/hvacs/{hvacId}/permanent-hold": {"post": {"summary": "Set HVAC unit Mode as Permanent Hold", "operationId": "postSetHvacPermanentHold", "description": "Tell an HVAC unit to enter a permanent hold. Only available if the target's `capabilities.setPermanentHold.isCapable` is set to `true`. We retry sending the command until the HVAC unit's `target` field transition to the expected value. Note that this request will complete before any commands are sent to the HVAC unit. You may react to transitions by listening for the `user:vendor-action:updated` webhook event or polling the [HVAC action endpoint](/api/reference#getHvacsAction).", "tags": ["HVAC"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/HvacPathId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HVACSetPermanentHoldPayload"}}}}, "responses": {"200": {"description": "Resulting action", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HvacActionPermanentHold"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the HVAC unit cannot perform the action or the setpoints are invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "422": {"description": "HVAC unit controlled by an Enode Schedule", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/interventions": {"get": {"summary": "List Interventions", "operationId": "listInterventions", "description": "Returns a list of all supported interventions.\n\nThe `language` parameter can be used to specify the language of the resolution title and description.", "tags": ["Interventions"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "enum": ["en-US", "en-GB", "de-DE", "fr-FR", "es-ES", "pt-PT", "nl-NL", "nl-BE", "nb-NO", "sv-SE", "da-DK", "fi-FI", "ro-RO"], "description": "Preferred BCP47 language code - Request translation for the specified language. Falls back to `en-US` if not provided or provided language code is unsupported."}, "required": false, "name": "language", "in": "query"}, {"schema": {"allOf": [{"$ref": "#/components/schemas/VendorType"}, {"description": "Only return interventions for the specified vendor type."}]}, "required": false, "name": "vendorType", "in": "query"}, {"schema": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Only return interventions for the specified vendor.", "example": "TESLA"}, "required": false, "name": "vendor", "in": "query"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterventionsList"}}}}}}}, "/interventions/{interventionId}": {"get": {"summary": "Get Intervention", "operationId": "getIntervention", "description": "Returns a single intervention.\n\nThe `language` parameter can be used to specify the language of the resolution title and description.", "tags": ["Interventions"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/InterventionPathId"}, {"schema": {"type": "string", "enum": ["en-US", "en-GB", "de-DE", "fr-FR", "es-ES", "pt-PT", "nl-NL", "nl-BE", "nb-NO", "sv-SE", "da-DK", "fi-FI", "ro-RO"], "description": "Preferred BCP47 language code - Request translation for the specified language. Falls back to `en-US` if not provided or provided language code is unsupported."}, "required": false, "name": "language", "in": "query"}, {"schema": {"allOf": [{"$ref": "#/components/schemas/VendorType"}, {"description": "Only return interventions for the specified vendor type."}]}, "required": false, "name": "vendorType", "in": "query"}, {"schema": {"anyOf": [{"$ref": "#/components/schemas/InverterVendor"}, {"$ref": "#/components/schemas/VehicleVendor"}, {"$ref": "#/components/schemas/HvacVendor"}, {"$ref": "#/components/schemas/ChargerVendor"}, {"$ref": "#/components/schemas/BatteryVendor"}, {"$ref": "#/components/schemas/MeterVendor"}], "description": "Only return interventions for the specified vendor.", "example": "TESLA"}, "required": false, "name": "vendor", "in": "query"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Intervention"}}}}, "404": {"description": "Intervention not found."}}}}, "/inverters": {"get": {"summary": "List Solar Inverters", "operationId": "listInverters", "description": "Returns a paginated list of all available Solar Inverters", "tags": ["Solar inverters"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedInverterList"}}}}}}}, "/users/{userId}/inverters": {"get": {"summary": "List User Solar Inverters", "operationId": "listUserInverters", "tags": ["Solar inverters"], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedInverterList"}}}}}}}, "/inverters/{inverterId}": {"get": {"summary": "Get Solar Inverter", "operationId": "getInverter", "tags": ["Solar inverters"], "parameters": [{"$ref": "#/components/parameters/PathInverterId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Inverter"}}}}}}}, "/inverters/{inverterId}/refresh-hint": {"post": {"summary": "Refresh Inverter data", "description": "Use this endpoint to initiate an expedited data refresh for the specified inverter.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "invertersRefreshHint", "tags": ["Solar inverters"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PathInverterId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified inverter was not found."}}}}, "/locations": {"get": {"summary": "List Locations", "operationId": "getLocations", "description": "Returns a paginated list of all Locations.", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedLocationList"}}}}}}}, "/users/{userId}/locations": {"get": {"summary": "List User Locations", "operationId": "getUserlocations", "description": "Returns a paginated list of Locations for the given user.", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedLocationList"}}}}}}, "post": {"summary": "Create Location", "description": "Create a Location for a User.", "operationId": "createLocation", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationPayload"}}}}, "responses": {"200": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}}}}, "/locations/{locationId}": {"get": {"summary": "Get Location", "description": "Fetch a Location.", "operationId": "getLocation", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/LocationPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}}}, "delete": {"summary": "Delete Location", "operationId": "deleteLocation", "description": "Delete a Location.", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/LocationPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}}}, "put": {"summary": "Update Location", "operationId": "updateLocation", "description": "Updates a location.", "tags": ["Locations"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/LocationPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationUpdatePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}}}}, "/meters/{meterId}": {"get": {"summary": "<PERSON>er", "operationId": "getMeter", "tags": ["Meters", "Beta"], "parameters": [{"$ref": "#/components/parameters/PathMeterId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the meter object"}, "userId": {"type": "string", "description": "The ID of the user that linked this meter."}, "vendor": {"$ref": "#/components/schemas/MeterVendor"}, "lastSeen": {"type": "string", "format": "date-time", "description": "The last time Enode successfully communicated with the vendor or when the meter was initially linked.", "example": "2020-04-07T17:04:26Z"}, "isReachable": {"type": "boolean", "description": "Whether live data from the meter is currently reachable from Enode's perspective. This 'reachability' may refer to reading from a cache operated by the meter's cloud service if that service has determined that its cache is valid."}, "information": {"$ref": "#/components/schemas/MeterInformation"}, "energyState": {"$ref": "#/components/schemas/MeterEnergyState"}, "location": {"$ref": "#/components/schemas/MeterLocation"}, "capabilities": {"$ref": "#/components/schemas/MeterCapabilities"}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Scopes that the user has granted for this meter. ", "example": ["meter:read:data", "meter:read:location"]}}, "required": ["id", "userId", "vendor", "lastSeen", "isReachable", "information", "energyState", "location", "capabilities", "scopes"], "additionalProperties": false}}}}}}}, "/meters/{meterId}/refresh-hint": {"post": {"summary": "Refresh meter data", "description": "Use this endpoint to initiate an expedited data refresh for the specified meter.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "metersRefreshHint", "tags": ["Meters", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PathMeterId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified meter was not found."}}}}, "/users/{userId}/meters": {"get": {"summary": "List User Meters", "description": "Returns a paginated list of meters for the given userId.", "operationId": "listUserMeters", "tags": ["Meters", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedMeterList"}}}}}}}, "/meters": {"get": {"summary": "List Meters", "operationId": "listMeters", "description": "Returns a paginated list of all Meters.", "tags": ["Meters", "Beta"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedMeterList"}}}}}}}, "/users/{userId}/schedules": {"get": {"summary": "List Schedules", "description": "Returns a list of Schedules registered to the User.", "operationId": "getSchedules", "security": [{"ClientAccessToken": []}], "tags": ["Schedules"], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedScheduleList"}}}}}}, "post": {"summary": "Create Schedule", "operationId": "createSchedule", "security": [{"ClientAccessToken": []}], "tags": ["Schedules"], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Schedule"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}}}}, "/schedules/{scheduleId}": {"get": {"summary": "Get Schedule", "operationId": "getSchedule", "security": [{"ClientAccessToken": []}], "tags": ["Schedules"], "parameters": [{"$ref": "#/components/parameters/PathScheduleId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}}}, "put": {"summary": "Update Schedule", "description": "Does a partial update of a schedule.", "operationId": "updateSchedule", "security": [{"ClientAccessToken": []}], "tags": ["Schedules"], "parameters": [{"$ref": "#/components/parameters/PathScheduleId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartialSchedule"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleResponse"}}}}}}, "delete": {"summary": "Delete Schedule", "description": "Delete a Schedule", "operationId": "deleteSchedule", "security": [{"ClientAccessToken": []}], "tags": ["Schedules"], "parameters": [{"$ref": "#/components/parameters/PathScheduleId"}], "responses": {"204": {"description": "No Content", "content": {}}}}}, "/schedules/{scheduleId}/status": {"get": {"summary": "Get Schedule Status", "operationId": "getScheduleStatus", "tags": ["Schedules"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PathScheduleId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleStatus"}}}}}}}, "/users/{userId}/statistics/charging": {"get": {"summary": "Get User Charging Statistics", "operationId": "getChargingStatistics", "description": "Get statistics about power consumption and price in the form of a time series for a single device.\n\nIf Smart Charging has shifted the consumption, the `nonSmartPrice` fields will show what the consumption would have cost if it had happened at the default time. If Smart Charging has resulted in savings, the value is reported in the `estimatedSavings` field.", "tags": ["Statistics"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the User"}, "required": true, "name": "userId", "in": "path"}, {"schema": {"type": "string", "format": "date-time", "description": "Earliest date to include in the response. Example: `2021-03-21T00:15:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": true, "name": "startDate", "in": "query"}, {"schema": {"type": "string", "format": "date-time", "description": "Latest date to include in the response. Example: `2021-03-21T00:30:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": false, "name": "endDate", "in": "query"}, {"schema": {"type": "string", "format": "uuid", "description": "The ID of the location for which to fetch statistics", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "required": false, "name": "locationId", "in": "query"}, {"schema": {"type": "string", "description": "The ID of the device for which to fetch statistics. The type of the device must match the `type` parameter."}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "enum": ["charger", "vehicle", "hvac"], "description": "The device types for which statistics can be obtained."}, "required": true, "name": "type", "in": "query"}, {"schema": {"type": "string", "format": "float", "description": "Offset (in hours) from UTC to adjust the timezone for viewing statistics. By default, all timestamps are in UTC, and period boundaries (day, week, month, year) are calculated in UTC. Providing `utcOffset` aligns these to the viewer's timezone. Accepts positive, negative, and fractional values. Note: `utcOffset` has no effect with resolutions `QUARTER_HOUR`, `HALF_HOUR`, or `HOUR`. \n\nDeprecated: The use of `utcOffset` is discouraged as it doesn't handle time zones adequately. All data will be bucketed in UTC if utcOffset is not provided.", "example": "-8", "x-unit": "hours", "deprecated": true}, "required": false, "name": "utcOffset", "in": "query"}, {"schema": {"type": "string", "enum": ["QUARTER_HOUR", "HALF_HOUR", "HOUR", "DAY", "WEEK", "MONTH", "YEAR"], "default": "DAY", "description": "The unit of time the data will be bucketed into. Each entry in the response array corresponds to aggregated data of the time range specified."}, "required": false, "name": "resolution", "in": "query"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/chargingStatisticsTimeseries"}}}}, "404": {"description": "Asset not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/users/{userId}/statistics/charging/sessions": {"get": {"summary": "Get User Statistics on Charging Sessions", "operationId": "getChargingSessionsStatistics", "description": "Returns statistics about power consumption and price, binned by sessions for a single user and device, at a location. A session is defined by consecutive readings from the device showing consumption. It can typically take up to 15 minutes between a session ending in reality, and being reflected by this endpoint.", "tags": ["Statistics"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the user"}, "required": true, "name": "userId", "in": "path"}, {"schema": {"type": "string", "format": "date-time", "description": "Earliest date to include in the response. Example: `2021-03-21T00:15:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": true, "name": "startDate", "in": "query"}, {"schema": {"type": "string", "format": "date-time", "description": "Latest date to include in the response. Example: `2021-03-21T00:30:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": false, "name": "endDate", "in": "query"}, {"schema": {"type": "string", "format": "uuid", "description": "The ID of the location for which to fetch statistics", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "required": false, "name": "locationId", "in": "query"}, {"schema": {"type": "string", "description": "The ID of the device for which to fetch statistics. The type of the device must match the `type` parameter."}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "enum": ["charger", "vehicle", "hvac"], "description": "The device types for which statistics can be obtained."}, "required": true, "name": "type", "in": "query"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/sessionsStatisticsTimeseries"}}}}, "404": {"description": "Asset not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/users/{userId}/statistics/production": {"get": {"summary": "Get User Production Statistics", "operationId": "getProductionStatistics", "description": "Returns statistics about power production and price for a single device and location, in the form of a time series.\n\nData is only available from after the asset was linked. To view inverter production data from before an asset was linked, consider using our [Vendor Statistics for Inverters](/api/reference#getInverterVendorStatistics) endpoint.", "tags": ["Statistics"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the User"}, "required": true, "name": "userId", "in": "path"}, {"schema": {"type": "string", "format": "date-time", "description": "Earliest date to include in the response. Example: `2021-03-21T00:15:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": true, "name": "startDate", "in": "query"}, {"schema": {"type": "string", "format": "date-time", "description": "Latest date to include in the response. Example: `2021-03-21T00:30:00.000Z`", "example": "2020-04-07T17:04:26Z"}, "required": false, "name": "endDate", "in": "query"}, {"schema": {"type": "string", "format": "uuid", "description": "The ID of the location for which to fetch statistics", "example": "4eaeb363-296d-4ccc-a973-7805e6f400bd", "x-format": {"guid": true}}, "required": false, "name": "locationId", "in": "query"}, {"schema": {"type": "string", "description": "The ID of the device for which to fetch statistics. The type of the device must match the `type` parameter."}, "required": false, "name": "id", "in": "query"}, {"schema": {"type": "string", "enum": ["inverter"], "description": "Get statistics for this hardware type."}, "required": true, "name": "type", "in": "query"}, {"schema": {"type": "string", "enum": ["QUARTER_HOUR", "HALF_HOUR", "HOUR", "DAY", "WEEK", "MONTH", "YEAR"], "default": "DAY", "description": "The unit of time the data will be cut into before aggregate statistics are applied. Each entry in the response array corresponds to aggregated data of the time range specified."}, "required": false, "name": "resolution", "in": "query"}, {"schema": {"type": "string", "format": "float", "description": "Offset (in hours) from UTC to adjust the timezone for viewing statistics. By default, all timestamps are in UTC, and period boundaries (day, week, month, year) are calculated in UTC. Providing `utcOffset` aligns these to the viewer's timezone. Accepts positive, negative, and fractional values. Note: `utcOffset` has no effect with resolutions `QUARTER_HOUR`, `HALF_HOUR`, or `HOUR`. \n\nDeprecated: The use of `utcOffset` is discouraged as it doesn't handle time zones adequately. All data will be bucketed in UTC if utcOffset is not provided.", "example": "-8", "x-unit": "hours", "deprecated": true}, "required": false, "name": "utcOffset", "in": "query"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/productionStatisticsTimeseries"}}}}, "404": {"description": "Asset not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/tariffs/{tariffId}": {"get": {"summary": "Get Tariff", "operationId": "getTariffInformation", "description": "Get a tariff using its ID.", "tags": ["Tariffs"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "pattern": "[\\w-]+", "maxLength": 36}, "required": true, "name": "tariffId", "in": "path"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tariff"}}}}}}, "put": {"summary": "Create a Tariff", "operationId": "sendTariffInformation", "description": "Create a tariff using a list of names and prices. Before the tariff can be used in Enode products, it must be [linked to a location](https://developers.enode.com/api/reference#associateUserLocationWithTariff).", "tags": ["Tariffs"], "security": [{"ClientAccessToken": []}], "parameters": [{"schema": {"type": "string", "pattern": "[\\w-]+", "maxLength": 36}, "required": true, "name": "tariffId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tariff"}}}}, "responses": {"204": {"description": "Successfully updated.", "content": {}}}}}, "/locations/{locationId}/tariff": {"put": {"summary": "Link Tariff to Location", "operationId": "associateUserLocationWithTariff", "description": "Link a tariff to a [Location](/api/reference#locations) using a daily schedule. Each new schedule overwrites any previous requests. Devices positioned at this location will use the schedule to calculate electricity costs.The schedule must ensure that there are no overlapping intervals, although gaps are permissible. In case of gaps, the system will default to market prices. ", "tags": ["Tariffs"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/LocationPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationTariffInterval"}}}}, "responses": {"204": {"description": "Successful", "content": {}}, "400": {"description": "Overlapping tariff schedule. Ensure that the schedule has no overlaps.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "404": {"description": "Location or tariff not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}, "get": {"summary": "Get Tariff Schedule", "operationId": "getUserLocationTariff", "description": "Get the tariff schedule linked to a location.", "tags": ["Tariffs"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/LocationPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationTariffSchedule"}}}}, "404": {"description": "Location not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/users": {"get": {"summary": "List Users", "operationId": "listUsers", "description": "Returns a paginated list of all users.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Paginated list of users. Use query parameters to retrieve more results.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedUsersListResponse"}}}}}}}, "/users/{userId}": {"get": {"summary": "Get User", "operationId": "getUser", "description": "Returns metadata about the given User ID, including a list of vendors for which the User has provided credentials.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}, "example": {"id": "123456789-<PERSON>c", "linkedVendors": [{"vendor": "TESLA", "vendorType": "vehicle", "isValid": true}]}}}}}}, "delete": {"summary": "Unlink User", "operationId": "deleteUsersUserid", "description": "Deletes a User and all of their data permanently and invalidates any associated sessions, authorization codes, and access/refresh tokens.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "responses": {"204": {"description": "No Content", "content": {}}}}}, "/users/{userId}/vendors/{vendor}": {"delete": {"summary": "Disconnect Vendor", "operationId": "disconnectUserVendor", "description": "Disconnect a single Vendor from the User's account.\n\nAll stored data about their Vendor account will be deleted, and any assets that were provided by that Vendor will disappear from the system.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/Vendor"}], "responses": {"204": {"description": "No Content", "content": {}}}}}, "/users/{userId}/vendors/{vendor}/{vendorType}": {"delete": {"summary": "Disconnect Vendor Type", "operationId": "disconnectUserVendorVendorType", "description": "Disconnect a specific vendor type from the User's account. Assets of this type from that Vendor will be removed. If no other types from that vendor remain, all its stored data will be deleted.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/Vendor"}, {"schema": {"$ref": "#/components/schemas/VendorType"}, "required": true, "name": "vendorType", "in": "path"}], "responses": {"204": {"description": "No Content", "content": {}}}}}, "/users/{userId}/link": {"post": {"summary": "Link User", "operationId": "postUsersUseridLink", "description": "Creates a short-lived (24 hours), single-use device linking session. Use the returned `linkUrl` to present Link UI to your user via [mobile in-app browsers](/docs/link-ui#mobile-in-app-browsers) or [web redirects](/docs/link-ui#web-redirects), or use the `linkToken` to present Link UI via the [Link SDKs](/docs/link-ui#mobile-sd-ks).", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkUserPayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkUserResponse"}}}}, "403": {"description": "Connections limit reached for this client", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectionsLimitReachedProblem"}}}}}}}, "/users/{userId}/authorization": {"delete": {"summary": "Deauthorize User", "operationId": "deleteUsersUseridAuthorization", "description": "Deletes the User's stored vendor authorizations and credentials, invalidates any associated sessions, authorization codes, and access/refresh tokens.\n\nAll other User data is retained, and if the User is sent through the Link User flow in the future, their account will be just as they left it.\n\nNo webhook events will be generated for a deauthorized user.", "tags": ["User Management"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}], "responses": {"204": {"description": "No Content", "content": {}}}}}, "/vehicles": {"get": {"summary": "List Vehicles", "operationId": "getVehicles", "description": "Returns a paginated list of all available Vehicles", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedVehicleList"}}}}}}}, "/users/{userId}/vehicles": {"get": {"summary": "List User Vehicles", "operationId": "listUserVehicles", "description": "Paginated list of user vehicles.", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/UserPathId"}, {"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedVehicleList"}}}}}}}, "/vehicles/{vehicleId}": {"get": {"summary": "Get Vehicle", "operationId": "getVehicle", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Vehicle"}, {"type": "object", "properties": {"locationId": {"type": ["string", "null"], "format": "uuid", "description": "ID of the location the vehicle is currently positioned at (if any).", "example": "07f8368d-be7e-4dbd-8cf0-94d00dd67ad3", "x-format": {"guid": true}}}, "required": ["locationId"], "additionalProperties": false}]}}}}}}}, "/vehicles/{vehicleId}/charging": {"post": {"summary": "Control Charging", "operationId": "postVehiclesVehicleidCharging", "description": "Request for a vehicle to start or stop charging. This request creates an Action that will retry until the vehicle's `powerDeliveryState` matches the expected value. The Action must complete before any further commands are sent to the vehicle. If a `PENDING` action for the same target and type already exists, it will be reused and a new action will not be created. If the new Action has a different target or type, the existing Action will be automatically transitioned to the `CANCELLED` state and a new Action will be created. Transitions can be tracked via the `user:vendor-action:updated` webhook event or [Get Vehicle Action](/api/reference#getVehiclesAction).\n\nThis endpoint returns an error with status code 422 if the vehicle is controlled by a schedule or has an active smart charging plan. To restore user control, either disable the schedule, disable smart charging, or use [Create Smart Override](/api/reference#vehicleCreateSmartOverride) to temporarily enable charging.", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"allOf": [{"$ref": "#/components/schemas/ChargingAction"}, {"description": "Charging action to perform"}]}}, "required": ["action"], "additionalProperties": false}}}}, "responses": {"200": {"description": "Resulting charge action", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeAction"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the vehicle cannot perform the action, is fully charged, or is already in the desired state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "422": {"description": "Vehicle controlled by a Schedule or has active Smart Charging Plan", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/vehicles/{vehicleId}/max-current": {"post": {"summary": "Set Max Current", "operationId": "postVehiclesVehicleidMaxCurrent", "description": "Experimental: Set the max current the vehicle is allowed to consume during charging. Currently only supported for Tesla vehicles and behind a feature flag. Reach out to Customer Success to get access. \n\nThis request creates an Action that will retry until the vehicles's `maxCurrent` matches the expected value. The Action must complete before any further commands are sent to the vehicle. Only one Action can be active for a specific vehicle at a time. If a new Action is created, the previous Action will be automatically cancelled and transitioned to the `CANCELLED` state. Transitions can be tracked via the `user:vendor-action:updated` webhook event or [Get Vehicle Action](/api/reference#getVehiclesAction). ", "tags": ["Vehicles", "Experimental"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TargetMaxCurrent"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaxCurrentAction"}}}}, "400": {"description": "A precondition check failed that is unlikely to change within the action's timeout window. This occurs if the vehicle cannot perform the action.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/vehicles/actions/{actionId}": {"get": {"summary": "Get Vehicle Action", "description": "Returns the current state of the requested Action.", "operationId": "getVehiclesAction", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleAction"}}}}, "404": {"description": "Action not found."}}}}, "/vehicles/actions/{actionId}/cancel": {"post": {"summary": "Cancel Vehicle Action", "description": "Cancels a pending vehicle action, halting any further attempts by Enode to execute it.\n\nNote: This only updates the action's status to `CANCELLED` within Enode and does not reflect a change in the vendor's cloud. Thus any pending action in the vendor's cloud might still be executed.", "operationId": "cancelVehicleAction", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/ActionPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeAction"}}}}, "404": {"description": "Action not found."}, "409": {"description": "Action already in a resolved state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChargeAction"}}}}}}}, "/vehicles/{vehicleId}/refresh-hint": {"post": {"summary": "Refresh Vehicle Data", "description": "Use this endpoint to initiate an expedited data refresh for the specified vehicle.\n\nNote: The Enode platform keeps data automatically up-to-date and detects changes in the OEM APIs within seconds to a few minutes. We change the refresh interval dynamically based on a number of heuristics. This ensures we find the best trade-off between the stability of the connection to the OEM and freshness of the data.  \nThis method overrides most of our heuristics and should therefore be used with caution. You may use it when you have a strong reason to believe the data might be stale.", "operationId": "vehiclesRefreshHint", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"204": {"description": "Refresh hint registered successfully.", "content": {}}, "404": {"description": "The specified vehicle was not found."}}}}, "/vehicles/{vehicleId}/smart-charging-plans/{smartChargingPlanId}": {"get": {"summary": "Get Smart Charging Plan", "description": "Check status of current or historical Smart Charging Plans for a Vehicle. Returns a [Smart Charging](/docs/smart-charging/introduction) Plan for this vehicle.\n\nTo fetch the most recently created Plan, call the endpoint with `smartChargingPlanId` set to `latest`.", "operationId": "getVehiclesVehicleidSmartchargingplans", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}, {"$ref": "#/components/parameters/SmartChargingPlanPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleSmartChargingPlan"}}}}}}}, "/vehicles/{vehicleId}/smart-charging-policy": {"get": {"summary": "Get Vehicle Smart Charging Policy", "description": "Get a vehicle's [Smart Charging](/docs/smart-charging/introduction) policy", "operationId": "getVehiclesVehicleidSmartchargingpolicy", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleSmartChargingPolicy"}}}}}}, "put": {"summary": "Update Vehicle Smart Charging Policy", "operationId": "updateVehicleSmartChargingPolicy", "description": "Updates the [Smart Charging](/docs/smart-charging/introduction) policy for a vehicle", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartialVehicleSmartChargingPolicy"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VehicleSmartChargingPolicy"}}}}}}}, "/vehicles/{vehicleId}/smart-override": {"post": {"summary": "Create Smart Override", "operationId": "vehicleCreateSmartOverride", "description": "Overrides an active smart feature by forcing the vehicle to start charging. This feature is meant to be used in situations where the user wants to charge immediately without disabling other smart features. The override remains active until the vehicle stops charging, or until [End Smart Override](/api/reference#vehicleEndSmartOverride) is called. When the override ends, the overriden smart feature will regain control of the vehicle. This endpoint should not be used for standard charge control, use [Control Charging](/api/reference#postVehiclesVehicleidCharging) instead.", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": "object", "properties": {"userId": {"type": "string", "description": "ID of the User", "example": "0bec82e0-0d54-4f2f-83b1-5b248604de0b"}, "vendor": {"$ref": "#/components/schemas/ChargeableVendor"}}, "required": ["userId", "vendor"], "additionalProperties": false}], "description": "This field populates after using Vehicle [Create Smart Override](/api/reference#vehicleCreateSmartOverride) or Charger [Create Smart Override](/api/reference#chargerCreateSmartOverride). Once populated, the target enters an overridden state, stopping Enode from sending Smart Charging actions. The Smart Override persists until the target ceases charging for any reason or is manually ended via Vehicle [End Smart Override](/api/reference#vehicleEndSmartOverride) or Charger [End Smart Override](/api/reference#chargerEndSmartOverride)."}}}}}}, "delete": {"summary": "End Smart Override", "operationId": "vehicleEndSmartOverride", "description": "Ends any active Smart Override for the vehicle specified by `vehicleId`. If previously configured, Schedules or Smart Charging will resume control over the target vehicle. Note that this does not mean the vehicle will stop charging, only that it will return to the state expected by the active Schedule or Smart Charging Plan.", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": "object", "properties": {"userId": {"type": "string", "description": "ID of the User", "example": "0bec82e0-0d54-4f2f-83b1-5b248604de0b"}, "vendor": {"$ref": "#/components/schemas/ChargeableVendor"}}, "required": ["userId", "vendor"], "additionalProperties": false}], "description": "This field populates after using Vehicle [Create Smart Override](/api/reference#vehicleCreateSmartOverride) or Charger [Create Smart Override](/api/reference#chargerCreateSmartOverride). Once populated, the target enters an overridden state, stopping Enode from sending Smart Charging actions. The Smart Override persists until the target ceases charging for any reason or is manually ended via Vehicle [End Smart Override](/api/reference#vehicleEndSmartOverride) or Charger [End Smart Override](/api/reference#chargerEndSmartOverride)."}}}}, "404": {"description": "No Smart Override Exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/vehicles/{vehicleId}/smart-charging-status": {"get": {"summary": "Get Vehicle Smart Charging Status", "description": "Every vehicle in Enode has a [Smart Charging Status](/docs/smart-charging/introduction) that describes the vehicle in terms of smart charging.", "operationId": "getVehiclesVehicleidSmartchargingstatus", "tags": ["Vehicles"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/VehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"updatedAt": {"type": "string", "format": "date-time", "description": "Time when the Smart Charging Status was recalculated, irrespective of whether or not any values changed", "example": "2020-04-07T17:04:26Z"}, "vehicleId": {"type": "string", "description": "ID of the vehicle to which this Smart Charging Status represents", "example": "4b5ada14-ea87-4ca1-aab9-3c979c34cf4a"}, "userId": {"type": "string", "description": "ID of the user that owns this vehicle", "example": "3b568b76-e30a-426e-aacd-609db4d7be81"}, "vendor": {"allOf": [{"$ref": "#/components/schemas/VehicleVendor"}, {"description": "Vendor of the Vehicle"}]}, "state": {"$ref": "#/components/schemas/SmartChargeState"}, "stateChangedAt": {"type": "string", "format": "date-time", "description": "Time when the `state` property transitioned to its current value", "example": "2020-04-07T17:04:26Z"}, "consideration": {"$ref": "#/components/schemas/Consideration"}, "smartOverride": {"allOf": [{"$ref": "#/components/schemas/SmartOverrideStatus"}, {"type": ["object", "null"]}]}}, "required": ["updatedAt", "vehicleId", "userId", "vendor", "state", "stateChangedAt", "consideration", "plan", "smartOverride"], "additionalProperties": false}}}}}}}, "/webhooks/firehose": {"put": {"summary": "Update Firehose Webhook", "operationId": "putWebhooksFirehose", "tags": ["Webhooks"], "deprecated": true, "security": [{"ClientAccessToken": []}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFirehoseWebhookPayload"}}}}, "responses": {"204": {"description": "No Content", "content": {}}}}, "delete": {"summary": "Delete Firehose Webhook", "operationId": "deleteWebhooksFirehose", "tags": ["Webhooks"], "deprecated": true, "security": [{"ClientAccessToken": []}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFirehoseWebhook"}}}}, "responses": {"204": {"description": "No Content", "content": {}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookNotFoundProblem"}}}}}}}, "/webhooks/firehose/test": {"post": {"summary": "Test Firehose Webhook", "operationId": "testFirehose", "description": "Trigger a `enode:firehose:test` payload to be sent to your configured Webhook URL. This will reset your webhook to a healthy state on success.", "tags": ["Webhooks"], "deprecated": true, "security": [{"ClientAccessToken": []}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestFirehoseWebhook"}}}}}}}, "/webhooks": {"post": {"summary": "Create Webhook", "operationId": "createWebhook", "description": "Create a webhook.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookCreatePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookResponse"}}}}, "400": {"description": "Webhook already exists.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookAlreadyExistsProblem"}}}}}}, "get": {"summary": "List Webhooks", "operationId": "listWebhooks", "description": "Returns a paginated list of webhooks.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedWebhookList"}}}}}}}, "/webhooks/{webhookId}": {"patch": {"summary": "Update Webhook", "operationId": "updateWebhook", "description": "Update a webhook.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/WebhookPathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookUpdatePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookResponse"}}}}, "404": {"description": "Webhook not found or already deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookNotFoundProblem"}}}}}}, "get": {"summary": "Get Webhook", "operationId": "getWebhook", "description": "Returns webhook with the given ID, if it exists.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/WebhookPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookResponse"}}}}, "404": {"description": "Webhook not found or already deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookNotFoundProblem"}}}}}}, "delete": {"summary": "Delete Webhook", "operationId": "deleteWebhook", "description": "Deletes webhook with the given ID, if it exists.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/WebhookPathId"}], "responses": {"204": {"description": "Successfully deleted", "content": {}}, "404": {"description": "Webhook not found or already deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookNotFoundProblem"}}}}}}}, "/webhooks/{webhookId}/test": {"post": {"summary": "Test Webhook", "operationId": "testWebhook", "description": "Send a `enode:webhook:test` payload to the webhook. If the webhook is inactive, it'll be reactivated on success.", "tags": ["Webhooks"], "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/WebhookPathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestFirehoseWebhook"}}}}, "404": {"description": "Webhook not found or already deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookNotFoundProblem"}}}}}}}, "/health/chargers": {"get": {"summary": "Check Available Charger Vendors", "operationId": "getHealthChargerVendors", "description": "Lists the available charger vendors, including the activated vendors that your client has access to. Learn more about [vendors requiring activation](https://developers.enode.io/api/capabilities/vehicles?dialog=glossary&value=activation-required).", "tags": ["Service Health"], "security": [], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChargerHealth"}, "example": [{"vendor": "EASEE", "displayName": "<PERSON><PERSON><PERSON>", "portalName": "<PERSON><PERSON><PERSON>", "status": "READY", "linkingStatus": "READY"}, {"vendor": "WALLBOX", "displayName": "Wallbox", "portalName": "Wallbox", "status": "READY", "linkingStatus": "READY"}, {"vendor": "ZAPTEC", "displayName": "Zaptec", "portalName": "Zaptec", "status": "READY", "linkingStatus": "READY"}, {"vendor": "EO", "displayName": "EO", "portalName": "EO", "status": "READY", "linkingStatus": "READY"}]}}}}}}}, "/health/vehicles": {"get": {"summary": "Check Available Vehicle Vendors", "operationId": "getHealthVehicleVendors", "description": "Lists the available vehicle vendors, including the activated vendors that your client has access to. Learn more about [vendors requiring activation](https://developers.enode.io/api/capabilities/vehicles?dialog=glossary&value=activation-required).", "tags": ["Service Health"], "security": [], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VehicleHealth"}, "example": [{"vendor": "TESLA", "displayName": "Tesla", "portalName": "Tesla", "status": "READY", "linkingStatus": "READY"}, {"vendor": "BMW", "displayName": "BMW", "portalName": "My BMW", "status": "READY", "linkingStatus": "READY"}, {"vendor": "AUDI", "displayName": "Audi", "portalName": "myAudi", "status": "READY", "linkingStatus": "READY"}]}}}}}}}, "/health/inverter": {"get": {"summary": "Check Available Inverter Vendors", "operationId": "getHealthInverterVendors", "description": "Lists the available inverter vendors, including the activated vendors that your client has access to. Learn more about [vendors requiring activation](https://developers.enode.io/api/capabilities/vehicles?dialog=glossary&value=activation-required).", "tags": ["Service Health"], "security": [], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InverterHealth"}, "example": [{"vendor": "SOLAREDGE", "displayName": "SolarEdge", "portalName": "Solar Edge", "status": "READY", "linkingStatus": "READY"}, {"vendor": "SMA", "displayName": "SMA", "portalName": "SMA Energy", "status": "READY", "linkingStatus": "READY"}, {"vendor": "SOLIS", "displayName": "Solis", "portalName": "Solis", "status": "READY", "linkingStatus": "READY"}, {"vendor": "FRONIUS", "displayName": "<PERSON><PERSON><PERSON>", "status": "READY", "linkingStatus": "READY", "portalName": "<PERSON><PERSON><PERSON>"}]}}}}}}}, "/health/hvacs": {"get": {"summary": "Check Available Hvac Vendors", "operationId": "getHealthHvacVendors", "description": "Lists the available HVAC vendors, including the activated vendors that your client has access to. Learn more about [vendors requiring activation](https://developers.enode.io/api/capabilities/vehicles?dialog=glossary&value=activation-required).", "tags": ["Service Health"], "security": [], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HvacHealth"}, "example": [{"vendor": "MILL", "displayName": "Mill", "portalName": "Mill", "status": "READY", "linkingStatus": "READY"}]}}}}}}}, "/health/meters": {"get": {"summary": "Check Available Meter Vendors", "operationId": "getHealthMeterVendors", "description": "Lists the available Meter vendors, including the activated vendors that your client has access to. Learn more about [vendors requiring activation](https://developers.enode.io/api/capabilities/vehicles?dialog=glossary&value=activation-required).", "tags": ["Service Health", "Experimental"], "security": [], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MeterHealth"}, "example": [{"vendor": "TESLA", "displayName": "Tesla", "portalName": "Tesla", "status": "READY", "linkingStatus": "READY"}]}}}}}}}, "/health/ready": {"get": {"summary": "Check Service Readiness", "operationId": "getHealthReady", "security": [], "description": "Gets the combined health status of the service and all functionalities and dependencies.", "tags": ["Service Health"], "responses": {"204": {"description": "All functionalities are operating nominally.", "content": {}}, "503": {"description": "At least one functionality of the system is not operating nominally.", "content": {}}}}}, "/simulated/vehicles": {"post": {"summary": "Create Simulated Vehicle", "operationId": "createSimulatedVehicle", "tags": ["Simulated Vehicles", "Experimental"], "deprecated": true, "security": [{"ClientAccessToken": []}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSimulatedVehiclePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulatedVehicle"}}}}}}, "get": {"summary": "List Simulated Vehicle", "operationId": "listSimulatedVehicle", "tags": ["Simulated Vehicles", "Experimental"], "deprecated": true, "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/PaginationQueryAfter"}, {"$ref": "#/components/parameters/PaginationQueryBefore"}, {"$ref": "#/components/parameters/PaginationQueryPageSize"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedVehiclesList"}}}}}}}, "/simulated/vehicles/{simulatedVehicleId}": {"get": {"summary": "Get Simulated Vehicle", "operationId": "getSimulatedVehicle", "tags": ["Simulated Vehicles", "Experimental"], "deprecated": true, "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/SimulatedVehiclePathId"}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulatedVehicle"}}}}}}, "patch": {"summary": "Update Simulated Vehicle", "operationId": "updateSimulatedVehicle", "tags": ["Simulated Vehicles", "Experimental"], "deprecated": true, "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/SimulatedVehiclePathId"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSimulatedVehiclePayload"}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulatedVehicle"}}}}}}, "delete": {"summary": "Delete Simulated Vehicle", "operationId": "deleteSimulatedVehicle", "tags": ["Simulated Vehicles", "Experimental"], "deprecated": true, "security": [{"ClientAccessToken": []}], "parameters": [{"$ref": "#/components/parameters/SimulatedVehiclePathId"}], "responses": {"200": {"description": "Successful"}}}}}, "webhooks": {"systemHeartbeat": {"post": {"summary": "systemHeartbeat", "operationId": "systemHeartbeat", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemHeartbeat"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userVehicleDiscovered": {"post": {"summary": "userVehicleDiscovered", "operationId": "userVehicleDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVehicleDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userVehicleUpdated": {"post": {"summary": "userVehicleUpdated", "operationId": "userVehicleUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVehicleUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userVehicleDeleted": {"post": {"summary": "userVehicleDeleted", "operationId": "userVehicleDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVehicleDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userVehicleSmartChargingStatusUpdated": {"post": {"summary": "userVehicleSmartChargingStatusUpdated", "operationId": "userVehicleSmartChargingStatusUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVehicleSmartChargingStatusUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userActionUpdated": {"post": {"summary": "userActionUpdated", "operationId": "userActionUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserActionUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userTemperatureScheduleStatusUpdated": {"post": {"summary": "userTemperatureScheduleStatusUpdated", "operationId": "userTemperatureScheduleStatusUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserTemperatureScheduleStatusUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userChargeScheduleStatusUpdated": {"post": {"summary": "userChargeScheduleStatusUpdated", "operationId": "userChargeScheduleStatusUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChargeScheduleStatusUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userChargerUpdated": {"post": {"summary": "userChargerUpdated", "operationId": "userChargerUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChargerUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userChargerDeleted": {"post": {"summary": "userChargerDeleted", "operationId": "userChargerDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChargerDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userChargerDiscovered": {"post": {"summary": "userChargerDiscovered", "operationId": "userChargerDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChargerDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userCredentialsInvalidated": {"post": {"summary": "userCredentialsInvalidated", "operationId": "userCredentialsInvalidated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCredentialsInvalidated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userHvacDiscovered": {"post": {"summary": "userHvacDiscovered", "operationId": "userHvacDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHvacDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userHvacUpdated": {"post": {"summary": "userHvacUpdated", "operationId": "userHvacUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHvacUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userHvacDeleted": {"post": {"summary": "userHvacDeleted", "operationId": "userHvacDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserHvacDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userInverterDiscovered": {"post": {"summary": "userInverterDiscovered", "operationId": "userInverterDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInverterDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userInverterUpdated": {"post": {"summary": "userInverterUpdated", "operationId": "userInverterUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInverterUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userInverterDeleted": {"post": {"summary": "userInverterDeleted", "operationId": "userInverterDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInverterDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userBatteryDiscovered": {"post": {"summary": "userBatteryDiscovered", "operationId": "userBatteryDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBatteryDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userBatteryUpdated": {"post": {"summary": "userBatteryUpdated", "operationId": "userBatteryUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBatteryUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userBatteryDeleted": {"post": {"summary": "userBatteryDeleted", "operationId": "userBatteryDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBatteryDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userMeterDiscovered": {"post": {"summary": "userMeterDiscovered", "operationId": "userMeterDiscovered", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMeterDiscovered"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userMeterUpdated": {"post": {"summary": "userMeterUpdated", "operationId": "userMeterUpdated", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMeterUpdated"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}, "userMeterDeleted": {"post": {"summary": "userMeterDeleted", "operationId": "userMeterDeleted", "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMeterDeleted"}}}}, "responses": {"200": {"description": "Return a 200 status to indicate that the data was received successfully"}}}}}}