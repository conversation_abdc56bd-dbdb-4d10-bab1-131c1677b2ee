/* tslint:disable */
/* eslint-disable */
/**
 * Data Platform API
 * Collection of APIs fulfilling needs in the experience domain.
 *
 * The version of the OpenAPI document: 0.0.1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AggregateCostCorrectedResponse
 */
export interface AggregateCostCorrectedResponse {
  /**
   * New cost of the charge in lowest denomination of its currency (pence, euro cents).
   * @type {number}
   * @memberof AggregateCostCorrectedResponse
   */
  cost: number;
  /**
   * UUID of the charge.
   * @type {string}
   * @memberof AggregateCostCorrectedResponse
   */
  id: string;
  /**
   * Who has submitted the correction request.
   * @type {string}
   * @memberof AggregateCostCorrectedResponse
   */
  submittedBy?: string;
}
/**
 *
 * @export
 * @interface AggregateSettlementAmountCorrectedResponse
 */
export interface AggregateSettlementAmountCorrectedResponse {
  /**
   * UUID of the charge.
   * @type {string}
   * @memberof AggregateSettlementAmountCorrectedResponse
   */
  id: string;
  /**
   * New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).
   * @type {number}
   * @memberof AggregateSettlementAmountCorrectedResponse
   */
  settlementAmount: number;
  /**
   * Who has submitted the correction request.
   * @type {string}
   * @memberof AggregateSettlementAmountCorrectedResponse
   */
  submittedBy?: string;
}
/**
 *
 * @export
 * @interface AuthoriseChargeRequestBody
 */
export interface AuthoriseChargeRequestBody {
  /**
   * PPID (PSL number) of a charger
   * @type {string}
   * @memberof AuthoriseChargeRequestBody
   */
  chargerId: string;
  /**
   * Charger door. A, B or C
   * @type {string}
   * @memberof AuthoriseChargeRequestBody
   */
  door: AuthoriseChargeRequestBodyDoorEnum;
  /**
   * RFID card token, OCPI token, or Billing event ID for guest authorisation.
   * @type {string}
   * @memberof AuthoriseChargeRequestBody
   */
  token: string;
}

export const AuthoriseChargeRequestBodyDoorEnum = {
  A: 'A',
  B: 'B',
  C: 'C',
} as const;

export type AuthoriseChargeRequestBodyDoorEnum =
  (typeof AuthoriseChargeRequestBodyDoorEnum)[keyof typeof AuthoriseChargeRequestBodyDoorEnum];

/**
 * No authoriser can be found for the given identifier (rfid/user/other).
 * @export
 * @interface AuthoriserNotFound
 */
export interface AuthoriserNotFound {
  /**
   *
   * @type {string}
   * @memberof AuthoriserNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof AuthoriserNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface Balance
 */
export interface Balance {
  /**
   * Actual user balance (or guest pre-authorised amount) in pence/cents/øre
   * @type {number}
   * @memberof Balance
   */
  actual?: number;
  /**
   * Balance currency code
   * @type {string}
   * @memberof Balance
   */
  currency?: BalanceCurrencyEnum;
  /**
   * Minimum balance required to start charge in pence/cents/øre
   * @type {number}
   * @memberof Balance
   */
  minimum?: number;
}

export const BalanceCurrencyEnum = {
  Gbp: 'GBP',
  Eur: 'EUR',
  Nok: 'NOK',
} as const;

export type BalanceCurrencyEnum =
  (typeof BalanceCurrencyEnum)[keyof typeof BalanceCurrencyEnum];

/**
 *
 * @export
 * @interface Breakdown
 */
export interface Breakdown {
  /**
   * Generation energy in kWh
   * @type {number}
   * @memberof Breakdown
   */
  generation?: number;
  /**
   * Grid energy in kWh
   * @type {number}
   * @memberof Breakdown
   */
  grid?: number;
  /**
   * Total energy in kWh
   * @type {number}
   * @memberof Breakdown
   */
  total: number;
}
/**
 *
 * @export
 * @interface Charge
 */
export interface Charge {
  /**
   *
   * @type {Charger}
   * @memberof Charge
   */
  charger: Charger;
  /**
   *
   * @type {Money}
   * @memberof Charge
   */
  cost?: Money;
  /**
   * Duration of charging in seconds.
   * @type {number}
   * @memberof Charge
   */
  duration?: number;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof Charge
   */
  endedAt?: string;
  /**
   * Energy used in kWh.
   * @type {number}
   * @memberof Charge
   */
  energyTotal?: number;
  /**
   *
   * @type {ExpensedTo}
   * @memberof Charge
   */
  expensedTo?: ExpensedTo;
  /**
   * Energy used in kWh that was generated e.g. Solar.
   * @type {number}
   * @memberof Charge
   */
  generationEnergyTotal?: number;
  /**
   * Energy used in kWh that is imported from the grid.
   * @type {number}
   * @memberof Charge
   */
  gridEnergyTotal?: number;
  /**
   * Charges unique identifier.
   * @type {string}
   * @memberof Charge
   */
  id: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof Charge
   */
  startedAt?: string;
}
/**
 * Charge aggregate cannot be found by its uuid.
 * @export
 * @interface ChargeAggregateNotFound
 */
export interface ChargeAggregateNotFound {
  /**
   *
   * @type {string}
   * @memberof ChargeAggregateNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof ChargeAggregateNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface ChargeAuthorisationResponse
 */
export interface ChargeAuthorisationResponse {
  /**
   * Whether the charge is authorised.
   * @type {boolean}
   * @memberof ChargeAuthorisationResponse
   */
  authorised: boolean;
  /**
   * UUID representing the charge authorisation, present if authorisation was successful.
   * @type {string}
   * @memberof ChargeAuthorisationResponse
   */
  id?: string;
  /**
   *
   * @type {Meta}
   * @memberof ChargeAuthorisationResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface ChargeEnergySummary
 */
export interface ChargeEnergySummary {
  /**
   * Energy used this period in kWh, filtering out any unclaimed charges.
   * @type {number}
   * @memberof ChargeEnergySummary
   */
  claimedUsage: number;
  /**
   * Energy cost in pence.
   * @type {number}
   * @memberof ChargeEnergySummary
   */
  cost: number;
  /**
   * Energy used this period in kWh by claimed charges with a positive revenue.
   * @type {number}
   * @memberof ChargeEnergySummary
   */
  revenueGeneratingClaimedUsage: number;
  /**
   * Energy used this period in kWh.
   * @type {number}
   * @memberof ChargeEnergySummary
   */
  totalUsage: number;
  /**
   * Energy used this period in kWh, filtering out any claimed charges.
   * @type {number}
   * @memberof ChargeEnergySummary
   */
  unclaimedUsage: number;
}
/**
 * The cost of an already expensed charge cannot be updated.
 * @export
 * @interface ChargeExpensed
 */
export interface ChargeExpensed {
  /**
   *
   * @type {string}
   * @memberof ChargeExpensed
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof ChargeExpensed
   */
  status: number;
}
/**
 * ChargeNotFound is the error returned when there is no charge found for the given charge ID.
 * @export
 * @interface ChargeNotFound
 */
export interface ChargeNotFound {
  /**
   *
   * @type {string}
   * @memberof ChargeNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof ChargeNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface Charger
 */
export interface Charger {
  /**
   * Charger door used.
   * @type {string}
   * @memberof Charger
   */
  door: string;
  /**
   * Chargers unique identifier.
   * @type {string}
   * @memberof Charger
   */
  id: string;
  /**
   * Chargers user-friendly name.
   * @type {string}
   * @memberof Charger
   */
  name?: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof Charger
   */
  pluggedInAt?: string;
  /**
   * Duration a charger has been in use in seconds.
   * @type {number}
   * @memberof Charger
   */
  pluggedInDuration?: number;
  /**
   * Name of the site where the charger is located.
   * @type {string}
   * @memberof Charger
   */
  siteName?: string;
  /**
   * Type of charger.
   * @type {string}
   * @memberof Charger
   */
  type: ChargerTypeEnum;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof Charger
   */
  unpluggedAt?: string;
}

export const ChargerTypeEnum = {
  Public: 'public',
  Private: 'private',
  Home: 'home',
} as const;

export type ChargerTypeEnum =
  (typeof ChargerTypeEnum)[keyof typeof ChargerTypeEnum];

/**
 *
 * @export
 * @interface ChargerChargeStatistics
 */
export interface ChargerChargeStatistics {
  /**
   * sum of charging duration in seconds
   * @type {number}
   * @memberof ChargerChargeStatistics
   */
  chargingDuration: number;
  /**
   * CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
   * @type {number}
   * @memberof ChargerChargeStatistics
   */
  co2Savings: number;
  /**
   *
   * @type {EnergyStatistics}
   * @memberof ChargerChargeStatistics
   */
  energy: EnergyStatistics;
  /**
   * count of distinct charge uuids
   * @type {number}
   * @memberof ChargerChargeStatistics
   */
  numberOfCharges: number;
  /**
   * count of distinct user ids
   * @type {number}
   * @memberof ChargerChargeStatistics
   */
  numberOfUsers: number;
  /**
   * sum of settlement amount in pence
   * @type {number}
   * @memberof ChargerChargeStatistics
   */
  revenueGenerated: number;
}
/**
 * ChargerPpidNotFound is the error returned when a charger location cannot be found by its ppid.
 * @export
 * @interface ChargerPpidNotFound
 */
export interface ChargerPpidNotFound {
  /**
   *
   * @type {string}
   * @memberof ChargerPpidNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof ChargerPpidNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface Chargerchargessummary
 */
export interface Chargerchargessummary {
  /**
   * Total duration of charging in seconds.
   * @type {number}
   * @memberof Chargerchargessummary
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Chargerchargessummary
   */
  co2Savings: number;
  /**
   *
   * @type {ChargeEnergySummary}
   * @memberof Chargerchargessummary
   */
  energy?: ChargeEnergySummary;
  /**
   * Total number of charges.
   * @type {number}
   * @memberof Chargerchargessummary
   */
  numberOfCharges: number;
  /**
   * Total number of unique drivers.
   * @type {number}
   * @memberof Chargerchargessummary
   */
  numberOfDrivers: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof Chargerchargessummary
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface Charges
 */
export interface Charges {
  /**
   * Charge data for driver.
   * @type {Array<Charge>}
   * @memberof Charges
   */
  charges: Array<Charge>;
  /**
   * Count of charges returned.
   * @type {number}
   * @memberof Charges
   */
  count: number;
}
/**
 *
 * @export
 * @interface Charges2
 */
export interface Charges2 {
  /**
   * Duration of charging in seconds.
   * @type {number}
   * @memberof Charges2
   */
  chargeDurationTotal: number;
  /**
   * Charger ID
   * @type {string}
   * @memberof Charges2
   */
  chargerId: string;
  /**
   * Charger name
   * @type {string}
   * @memberof Charges2
   */
  chargerName: string;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Charges2
   */
  co2Avoided: number;
  /**
   * Charge is confirmed or not. Identified by whether the authoriserID is present or not.
   * @type {boolean}
   * @memberof Charges2
   */
  confirmed: boolean;
  /**
   * Charger door used.
   * @type {string}
   * @memberof Charges2
   */
  door: string;
  /**
   * Driver ID
   * @type {Array<string>}
   * @memberof Charges2
   */
  driverIDs: Array<string>;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges2
   */
  endedAt: string;
  /**
   * Cost of energy used in pence
   * @type {number}
   * @memberof Charges2
   */
  energyCost: number;
  /**
   * Energy used in kWh.
   * @type {number}
   * @memberof Charges2
   */
  energyTotal: number;
  /**
   * Generation energy used in kWh.
   * @type {number}
   * @memberof Charges2
   */
  generationEnergyTotal: number;
  /**
   * Grid energy used in kWh.
   * @type {number}
   * @memberof Charges2
   */
  gridEnergyTotal: number;
  /**
   * UUID of the charge
   * @type {string}
   * @memberof Charges2
   */
  id: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges2
   */
  pluggedInAt: string;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof Charges2
   */
  revenueGenerated: number;
  /**
   * Site name
   * @type {string}
   * @memberof Charges2
   */
  siteName: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges2
   */
  startedAt: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges2
   */
  unpluggedAt: string;
}
/**
 *
 * @export
 * @interface Charges3
 */
export interface Charges3 {
  /**
   * Duration of charging in seconds.
   * @type {number}
   * @memberof Charges3
   */
  chargeDurationTotal: number;
  /**
   * Charger ID
   * @type {string}
   * @memberof Charges3
   */
  chargerId: string;
  /**
   * Charger name
   * @type {string}
   * @memberof Charges3
   */
  chargerName: string;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Charges3
   */
  co2Avoided: number;
  /**
   * Charge is confirmed or not. Identified by whether the authoriserID is present or not.
   * @type {boolean}
   * @memberof Charges3
   */
  confirmed: boolean;
  /**
   * Charger door used.
   * @type {string}
   * @memberof Charges3
   */
  door: string;
  /**
   * Driver ID
   * @type {Array<string>}
   * @memberof Charges3
   */
  driverIDs: Array<string>;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges3
   */
  endedAt: string;
  /**
   * Cost of energy used in pence
   * @type {number}
   * @memberof Charges3
   */
  energyCost: number;
  /**
   * Energy used in kWh.
   * @type {number}
   * @memberof Charges3
   */
  energyTotal: number;
  /**
   * Generation energy used in kWh.
   * @type {number}
   * @memberof Charges3
   */
  generationEnergyTotal: number;
  /**
   * Grid energy used in kWh.
   * @type {number}
   * @memberof Charges3
   */
  gridEnergyTotal: number;
  /**
   * UUID of the charge
   * @type {string}
   * @memberof Charges3
   */
  id: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges3
   */
  pluggedInAt: string;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof Charges3
   */
  revenueGenerated: number;
  /**
   * Site name
   * @type {string}
   * @memberof Charges3
   */
  siteName: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges3
   */
  startedAt: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format.
   * @type {string}
   * @memberof Charges3
   */
  unpluggedAt: string;
}
/**
 * Information about charge allowance and related limits
 * @export
 * @interface ChargesLimitResponse
 */
export interface ChargesLimitResponse {
  /**
   * Indicator of whether charge is allowed
   * @type {boolean}
   * @memberof ChargesLimitResponse
   */
  allowed: boolean;
  /**
   *
   * @type {Balance}
   * @memberof ChargesLimitResponse
   */
  balance: Balance;
  /**
   *
   * @type {Array<Limit>}
   * @memberof ChargesLimitResponse
   */
  limits?: Array<Limit>;
}
/**
 *
 * @export
 * @interface CorrectEnergyCostRequestBody
 */
export interface CorrectEnergyCostRequestBody {
  /**
   * New cost of the charge in lowest denomination of its currency (pence, euro cents).
   * @type {number}
   * @memberof CorrectEnergyCostRequestBody
   */
  cost: number;
  /**
   * Who has submitted the correction request.
   * @type {string}
   * @memberof CorrectEnergyCostRequestBody
   */
  submittedBy?: string;
}
/**
 *
 * @export
 * @interface CorrectSettlementAmountRequestBody
 */
export interface CorrectSettlementAmountRequestBody {
  /**
   * New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).
   * @type {number}
   * @memberof CorrectSettlementAmountRequestBody
   */
  settlementAmount: number;
  /**
   * Who has submitted the correction request.
   * @type {string}
   * @memberof CorrectSettlementAmountRequestBody
   */
  submittedBy?: string;
}
/**
 *
 * @export
 * @interface Cost
 */
export interface Cost {
  /**
   * Total cost of all charges at home chargers in pence/cent/ore
   * @type {Array<MoneyInt64>}
   * @memberof Cost
   */
  home: Array<MoneyInt64>;
  /**
   * Total cost of all chargers at private chargers in pence/cent/ore
   * @type {Array<MoneyInt64>}
   * @memberof Cost
   */
  private: Array<MoneyInt64>;
  /**
   * Total cost of all chargers at public chargers in pence/cent/ore
   * @type {Array<MoneyInt64>}
   * @memberof Cost
   */
  public: Array<MoneyInt64>;
  /**
   * Total cost of all charges in pence/cent/ore
   * @type {Array<MoneyInt64>}
   * @memberof Cost
   */
  total: Array<MoneyInt64>;
}
/**
 *
 * @export
 * @interface CreateDriverExpensesRequestBody
 */
export interface CreateDriverExpensesRequestBody {
  /**
   *
   * @type {Array<CreateExpenseRequest>}
   * @memberof CreateDriverExpensesRequestBody
   */
  expenses?: Array<CreateExpenseRequest>;
}
/**
 *
 * @export
 * @interface CreateExpenseRequest
 */
export interface CreateExpenseRequest {
  /**
   * Primary key of the submitted charge.
   * @type {number}
   * @memberof CreateExpenseRequest
   */
  chargeId: number;
}
/**
 *
 * @export
 * @interface CreatedExpense
 */
export interface CreatedExpense {
  /**
   * ID of the charge associated with the expense.
   * @type {number}
   * @memberof CreatedExpense
   */
  chargeId: number;
  /**
   * Primary key of the expense.
   * @type {number}
   * @memberof CreatedExpense
   */
  id: number;
}
/**
 *
 * @export
 * @interface CreatedExpenseResponse
 */
export interface CreatedExpenseResponse {
  /**
   *
   * @type {Array<CreatedExpense>}
   * @memberof CreatedExpenseResponse
   */
  expenses?: Array<CreatedExpense>;
}
/**
 *
 * @export
 * @interface Dnoregion
 */
export interface Dnoregion {
  /**
   * DNO region full name.
   * @type {string}
   * @memberof Dnoregion
   */
  dnoregion: string;
  /**
   * National grid DNO region id.
   * @type {number}
   * @memberof Dnoregion
   */
  regionid: number;
  /**
   * DNO region short name.
   * @type {string}
   * @memberof Dnoregion
   */
  shortname: string;
}
/**
 *
 * @export
 * @interface Driver
 */
export interface Driver {
  /**
   * Email address of the driver.
   * @type {string}
   * @memberof Driver
   */
  email: string;
  /**
   * First name of the driver.
   * @type {string}
   * @memberof Driver
   */
  firstName: string;
  /**
   * Full name of the driver.
   * @type {string}
   * @memberof Driver
   */
  fullName: string;
  /**
   * Primary key of the driver.
   * @type {number}
   * @memberof Driver
   */
  id: number;
  /**
   * Last name of the driver.
   * @type {string}
   * @memberof Driver
   */
  lastName: string;
}
/**
 *
 * @export
 * @interface DriverCharge
 */
export interface DriverCharge {
  /**
   * Business to whom this charge is associated
   * @type {string}
   * @memberof DriverCharge
   */
  businessName: string;
  /**
   * Charge cost in pence
   * @type {number}
   * @memberof DriverCharge
   */
  chargeCost: number;
  /**
   * Name of charger associated with charge
   * @type {string}
   * @memberof DriverCharge
   */
  chargerName: string;
  /**
   * Charge duration in seconds
   * @type {number}
   * @memberof DriverCharge
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof DriverCharge
   */
  co2Savings: number;
  /**
   * Charge end time UTC
   * @type {string}
   * @memberof DriverCharge
   */
  endTime: string;
  /**
   * Energy used in kWh
   * @type {number}
   * @memberof DriverCharge
   */
  energyUsage: number;
  /**
   * Plugged-in duration in seconds
   * @type {number}
   * @memberof DriverCharge
   */
  pluggedInDuration: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof DriverCharge
   */
  revenueGenerated: number;
  /**
   * Charge start time UTC
   * @type {string}
   * @memberof DriverCharge
   */
  startTime: string;
}
/**
 * List of driver\'s charges
 * @export
 * @interface DriverChargesResponse
 */
export interface DriverChargesResponse {
  /**
   *
   * @type {Array<DriverCharge>}
   * @memberof DriverChargesResponse
   */
  charges?: Array<DriverCharge>;
}
/**
 * No driver has been found for a given driverID.
 * @export
 * @interface DriverNotFound
 */
export interface DriverNotFound {
  /**
   *
   * @type {string}
   * @memberof DriverNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof DriverNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface DriverStatsData
 */
export interface DriverStatsData {
  /**
   * Driver statistics broken down by the requested interval type.
   * @type {Array<Interval>}
   * @memberof DriverStatsData
   */
  intervals?: Array<Interval>;
  /**
   *
   * @type {StatsSummary}
   * @memberof DriverStatsData
   */
  summary: StatsSummary;
}
/**
 *
 * @export
 * @interface DriverStatsResponse
 */
export interface DriverStatsResponse {
  /**
   *
   * @type {DriverStatsData}
   * @memberof DriverStatsResponse
   */
  data: DriverStatsData;
  /**
   *
   * @type {Meta}
   * @memberof DriverStatsResponse
   */
  meta: Meta;
}
/**
 * No charge can be found for the given user and charge IDs.
 * @export
 * @interface DriversChargeNotFound
 */
export interface DriversChargeNotFound {
  /**
   *
   * @type {string}
   * @memberof DriversChargeNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof DriversChargeNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface DriversChargeResponse
 */
export interface DriversChargeResponse {
  /**
   *
   * @type {Charge}
   * @memberof DriversChargeResponse
   */
  data: Charge;
  /**
   *
   * @type {Meta}
   * @memberof DriversChargeResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface DriversChargesResponse
 */
export interface DriversChargesResponse {
  /**
   *
   * @type {Charges}
   * @memberof DriversChargesResponse
   */
  data: Charges;
  /**
   *
   * @type {Meta}
   * @memberof DriversChargesResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface Duration
 */
export interface Duration {
  /**
   * Total duration of all charges at home chargers in secs
   * @type {number}
   * @memberof Duration
   */
  home: number;
  /**
   * Total duration of all charges at private chargers in secs
   * @type {number}
   * @memberof Duration
   */
  private: number;
  /**
   * Total duration of all charges at public chargers in secs
   * @type {number}
   * @memberof Duration
   */
  public: number;
  /**
   * Total duration of all charges in secs
   * @type {number}
   * @memberof Duration
   */
  total: number;
}
/**
 *
 * @export
 * @interface Energy
 */
export interface Energy {
  /**
   *
   * @type {Breakdown}
   * @memberof Energy
   */
  home: Breakdown;
  /**
   *
   * @type {Breakdown}
   * @memberof Energy
   */
  private: Breakdown;
  /**
   *
   * @type {Breakdown}
   * @memberof Energy
   */
  public: Breakdown;
  /**
   *
   * @type {Breakdown}
   * @memberof Energy
   */
  total: Breakdown;
}
/**
 *
 * @export
 * @interface EnergyStatistics
 */
export interface EnergyStatistics {
  /**
   * Energy used this period in kWh, filtering out any unclaimed charges.
   * @type {number}
   * @memberof EnergyStatistics
   */
  claimedUsage: number;
  /**
   * Energy cost in pence.
   * @type {number}
   * @memberof EnergyStatistics
   */
  cost: number;
  /**
   * Energy used this period in kWh by claimed charges with a positive revenue.
   * @type {number}
   * @memberof EnergyStatistics
   */
  revenueGeneratingClaimedUsage: number;
  /**
   * Energy used this period in kWh.
   * @type {number}
   * @memberof EnergyStatistics
   */
  totalUsage: number;
  /**
   * Energy used this period in kWh, filtering out any claimed charges.
   * @type {number}
   * @memberof EnergyStatistics
   */
  unclaimedUsage: number;
}
/**
 * EventOutOfDate is the error returned when there is another update on the same charge, resulting in a unique constraint on the database.
 * @export
 * @interface EventOutOfDate
 */
export interface EventOutOfDate {
  /**
   *
   * @type {string}
   * @memberof EventOutOfDate
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof EventOutOfDate
   */
  status: number;
}
/**
 * Expensable charge
 * @export
 * @interface ExpensableCharge
 */
export interface ExpensableCharge {
  /**
   * Charge cost in pence
   * @type {number}
   * @memberof ExpensableCharge
   */
  chargeCost: number;
  /**
   * Public charger name or home charger PSL
   * @type {string}
   * @memberof ExpensableCharge
   */
  chargerName: string;
  /**
   *
   * @type {Driver}
   * @memberof ExpensableCharge
   */
  driver: Driver;
  /**
   * Charge end time UTC
   * @type {string}
   * @memberof ExpensableCharge
   */
  endTime: string;
  /**
   * Energy used in KWh
   * @type {number}
   * @memberof ExpensableCharge
   */
  energyUsage: number;
  /**
   * Primary key of the charge
   * @type {number}
   * @memberof ExpensableCharge
   */
  id: number;
  /**
   *
   * @type {SubmittedChargeLocation}
   * @memberof ExpensableCharge
   */
  location: SubmittedChargeLocation;
  /**
   * Plugged in at time UTC
   * @type {string}
   * @memberof ExpensableCharge
   */
  pluggedInAt?: string;
  /**
   * Who processed this expense (if processed)
   * @type {string}
   * @memberof ExpensableCharge
   */
  processedByFullName?: string;
  /**
   * Processed time UTC (if processed)
   * @type {string}
   * @memberof ExpensableCharge
   */
  processedTime?: string;
  /**
   * Charge start time UTC
   * @type {string}
   * @memberof ExpensableCharge
   */
  startTime: string;
  /**
   * Submitted for approval time UTC
   * @type {string}
   * @memberof ExpensableCharge
   */
  submittedTime: string;
  /**
   * Unplugged at time UTC
   * @type {string}
   * @memberof ExpensableCharge
   */
  unpluggedAt?: string;
}
/**
 *
 * @export
 * @interface ExpensableChargeDriverSummary
 */
export interface ExpensableChargeDriverSummary {
  /**
   *
   * @type {Driver}
   * @memberof ExpensableChargeDriverSummary
   */
  driver: Driver;
  /**
   * List of submitted charge IDs.
   * @type {Array<number>}
   * @memberof ExpensableChargeDriverSummary
   */
  submittedChargeIds?: Array<number>;
  /**
   * Number of charges.
   * @type {number}
   * @memberof ExpensableChargeDriverSummary
   */
  totalCharges: number;
  /**
   *
   * @type {TotalCost}
   * @memberof ExpensableChargeDriverSummary
   */
  totalCost: TotalCost;
  /**
   *
   * @type {TotalUsage}
   * @memberof ExpensableChargeDriverSummary
   */
  totalUsage: TotalUsage;
}
/**
 * Group the charge is to be expensed to.
 * @export
 * @interface ExpensedTo
 */
export interface ExpensedTo {
  /**
   * Groups unique identifier.
   * @type {string}
   * @memberof ExpensedTo
   */
  id: string;
  /**
   * Groups name.
   * @type {string}
   * @memberof ExpensedTo
   */
  name: string;
}
/**
 *
 * @export
 * @interface FleetUsageResponse
 */
export interface FleetUsageResponse {
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof FleetUsageResponse
   */
  co2Savings: number;
  /**
   * Number of drivers.
   * @type {number}
   * @memberof FleetUsageResponse
   */
  numberOfDrivers: number;
  /**
   *
   * @type {TotalCharges}
   * @memberof FleetUsageResponse
   */
  totalCharges: TotalCharges;
  /**
   *
   * @type {TotalUsage}
   * @memberof FleetUsageResponse
   */
  totalUsage: TotalUsage;
}
/**
 *
 * @export
 * @interface Forecast
 */
export interface Forecast {
  /**
   *
   * @type {Forecastdata}
   * @memberof Forecast
   */
  data: Forecastdata;
}
/**
 *
 * @export
 * @interface Forecastdata
 */
export interface Forecastdata {
  /**
   *
   * @type {Array<Period>}
   * @memberof Forecastdata
   */
  data: Array<Period>;
  /**
   *
   * @type {string}
   * @memberof Forecastdata
   */
  dnoregion: string;
  /**
   *
   * @type {number}
   * @memberof Forecastdata
   */
  regionid: number;
  /**
   *
   * @type {string}
   * @memberof Forecastdata
   */
  shortname: string;
}
/**
 *
 * @export
 * @interface GroupChargeStatistics
 */
export interface GroupChargeStatistics {
  /**
   * sum of charging duration in seconds
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  chargingDuration: number;
  /**
   * CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  co2Savings: number;
  /**
   *
   * @type {EnergyStatistics}
   * @memberof GroupChargeStatistics
   */
  energy: EnergyStatistics;
  /**
   * count of distinct charger ids
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  numberOfChargers: number;
  /**
   * count of distinct charge uuids
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  numberOfCharges: number;
  /**
   * count of distinct sites
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  numberOfSites: number;
  /**
   * count of distinct user ids
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  numberOfUsers: number;
  /**
   * sum of settlement amount in pence
   * @type {number}
   * @memberof GroupChargeStatistics
   */
  revenueGenerated: number;
}
/**
 * GroupNotFound is the error returned when there is no group for a given {groupId}.
 * @export
 * @interface GroupNotFound
 */
export interface GroupNotFound {
  /**
   *
   * @type {string}
   * @memberof GroupNotFound
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof GroupNotFound
   */
  status: number;
}
/**
 *
 * @export
 * @interface GroupSitesStats
 */
export interface GroupSitesStats {
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof GroupSitesStats
   */
  co2AvoidedKg: number;
  /**
   * Total energy cost in pence.
   * @type {number}
   * @memberof GroupSitesStats
   */
  energyCost: number;
  /**
   * Total energy usage in kWh.
   * @type {number}
   * @memberof GroupSitesStats
   */
  energyUsageKwh: number;
  /**
   * Total number of charges.
   * @type {number}
   * @memberof GroupSitesStats
   */
  numberOfCharges: number;
  /**
   * Total number of unique drivers.
   * @type {number}
   * @memberof GroupSitesStats
   */
  numberOfDrivers: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof GroupSitesStats
   */
  revenueGenerated: number;
  /**
   * Site ID
   * @type {string}
   * @memberof GroupSitesStats
   */
  siteId: string;
  /**
   * Total duration of charging in seconds.
   * @type {number}
   * @memberof GroupSitesStats
   */
  totalDuration: number;
}
/**
 *
 * @export
 * @interface GroupSitesStatsResponse
 */
export interface GroupSitesStatsResponse {
  /**
   *
   * @type {Array<GroupSitesStats>}
   * @memberof GroupSitesStatsResponse
   */
  data: Array<GroupSitesStats>;
  /**
   *
   * @type {Meta}
   * @memberof GroupSitesStatsResponse
   */
  meta: Meta;
}
/**
 * IdentifierNotProvided is the error returned when an identifier is not provided.
 * @export
 * @interface IdentifierNotProvided
 */
export interface IdentifierNotProvided {
  /**
   *
   * @type {string}
   * @memberof IdentifierNotProvided
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof IdentifierNotProvided
   */
  status: number;
}
/**
 *
 * @export
 * @interface Intensity
 */
export interface Intensity {
  /**
   *
   * @type {number}
   * @memberof Intensity
   */
  forecast: number;
  /**
   * Intensity index with values: very low, low, moderate, high, very high
   * @type {string}
   * @memberof Intensity
   */
  index: string;
}
/**
 *
 * @export
 * @interface Interval
 */
export interface Interval {
  /**
   * Statistics date time range start eg: 2022-01-01T00:00:00Z
   * @type {string}
   * @memberof Interval
   */
  from: string;
  /**
   *
   * @type {StatsSummary}
   * @memberof Interval
   */
  stats: StatsSummary;
  /**
   * Statistics report inclusive end date eg: 2022-01-31T00:00:00Z
   * @type {string}
   * @memberof Interval
   */
  to: string;
}
/**
 *
 * @export
 * @interface Limit
 */
export interface Limit {
  /**
   * Amount for a limit
   * @type {number}
   * @memberof Limit
   */
  amount?: number;
  /**
   * Type of a limit
   * @type {string}
   * @memberof Limit
   */
  type?: LimitTypeEnum;
  /**
   * Unit for a limit - currently only kWh, but could be time interval in the future
   * @type {string}
   * @memberof Limit
   */
  unit?: string;
}

export const LimitTypeEnum = {
  Energy: 'energy',
  Duration: 'duration',
} as const;

export type LimitTypeEnum = (typeof LimitTypeEnum)[keyof typeof LimitTypeEnum];

/**
 *
 * @export
 * @interface MarkSubmittedChargesAsProcessedRequestBody
 */
export interface MarkSubmittedChargesAsProcessedRequestBody {
  /**
   *
   * @type {number}
   * @memberof MarkSubmittedChargesAsProcessedRequestBody
   */
  approverId: number;
  /**
   *
   * @type {Array<number>}
   * @memberof MarkSubmittedChargesAsProcessedRequestBody
   */
  submittedChargeIds: Array<number>;
}
/**
 *
 * @export
 * @interface Meta
 */
export interface Meta {
  /**
   * Passed parameters
   * @type {{ [key: string]: any; }}
   * @memberof Meta
   */
  params: { [key: string]: any };
}
/**
 *
 * @export
 * @interface Mix
 */
export interface Mix {
  /**
   * Fuel type with values: gas, coal, biomass, nuclear, hydro, storage, imports, other, wind, solar
   * @type {string}
   * @memberof Mix
   */
  fuel: string;
  /**
   *
   * @type {number}
   * @memberof Mix
   */
  perc: number;
}
/**
 *
 * @export
 * @interface ModelError
 */
export interface ModelError {
  /**
   * Is the error a server-side fault?
   * @type {boolean}
   * @memberof ModelError
   */
  fault: boolean;
  /**
   * ID is a unique identifier for this particular occurrence of the problem.
   * @type {string}
   * @memberof ModelError
   */
  id: string;
  /**
   * Message is a human-readable explanation specific to this occurrence of the problem.
   * @type {string}
   * @memberof ModelError
   */
  message: string;
  /**
   * Name is the name of this class of errors.
   * @type {string}
   * @memberof ModelError
   */
  name: string;
  /**
   * Is the error temporary?
   * @type {boolean}
   * @memberof ModelError
   */
  temporary: boolean;
  /**
   * Is the error a timeout?
   * @type {boolean}
   * @memberof ModelError
   */
  timeout: boolean;
}
/**
 * An amount of money with its currency type.
 * @export
 * @interface Money
 */
export interface Money {
  /**
   * Amount in smallest denomination of the associated currency
   * @type {number}
   * @memberof Money
   */
  amount: number;
  /**
   * ISO currency code
   * @type {string}
   * @memberof Money
   */
  currency: string;
}
/**
 * An amount of money with its currency type.
 * @export
 * @interface MoneyInt64
 */
export interface MoneyInt64 {
  /**
   * Amount in smallest denomination of the associated currency
   * @type {number}
   * @memberof MoneyInt64
   */
  amount: number;
  /**
   * ISO currency code
   * @type {string}
   * @memberof MoneyInt64
   */
  currency: string;
}
/**
 * Expensable charges summary grouped per driver
 * @export
 * @interface OrganisationChargesDriverSummaryResponse
 */
export interface OrganisationChargesDriverSummaryResponse {
  /**
   *
   * @type {Array<ExpensableChargeDriverSummary>}
   * @memberof OrganisationChargesDriverSummaryResponse
   */
  driverExpensableChargeSummaries: Array<ExpensableChargeDriverSummary>;
}
/**
 * Expensable charges
 * @export
 * @interface OrganisationChargesResponse
 */
export interface OrganisationChargesResponse {
  /**
   *
   * @type {Array<ExpensableCharge>}
   * @memberof OrganisationChargesResponse
   */
  expensableCharges: Array<ExpensableCharge>;
}
/**
 *
 * @export
 * @interface OrganisationDriversChargeStatistics
 */
export interface OrganisationDriversChargeStatistics {
  /**
   * Aggregate charge duration in seconds
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  co2Savings: number;
  /**
   *
   * @type {Driver}
   * @memberof OrganisationDriversChargeStatistics
   */
  driver: Driver;
  /**
   * Total number of charges.
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  numberOfCharges: number;
  /**
   * Aggregate plugged-in duration in seconds
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  pluggedInDuration: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  revenueGenerated: number;
  /**
   * Aggregate charge cost in pence
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  totalCost: number;
  /**
   * Energy used this period in kWh.
   * @type {number}
   * @memberof OrganisationDriversChargeStatistics
   */
  totalUsage: number;
}
/**
 * Aggregate statistics for each organisation driver
 * @export
 * @interface OrganisationDriversChargeStatisticsResponse
 */
export interface OrganisationDriversChargeStatisticsResponse {
  /**
   *
   * @type {Array<OrganisationDriversChargeStatistics>}
   * @memberof OrganisationDriversChargeStatisticsResponse
   */
  charges?: Array<OrganisationDriversChargeStatistics>;
}
/**
 *
 * @export
 * @interface Organisationchargessummary
 */
export interface Organisationchargessummary {
  /**
   * Total duration of charging in seconds.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Organisationchargessummary
   */
  co2Savings: number;
  /**
   *
   * @type {ChargeEnergySummary}
   * @memberof Organisationchargessummary
   */
  energy?: ChargeEnergySummary;
  /**
   * Total number of Chargers.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  numberOfChargers: number;
  /**
   * Total number of charges.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  numberOfCharges: number;
  /**
   * Total number of unique drivers.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  numberOfDrivers: number;
  /**
   * Total number of Sites.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  numberOfSites: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof Organisationchargessummary
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface Period
 */
export interface Period {
  /**
   *
   * @type {string}
   * @memberof Period
   */
  from: string;
  /**
   *
   * @type {Array<Mix>}
   * @memberof Period
   */
  generationmix: Array<Mix>;
  /**
   *
   * @type {Intensity}
   * @memberof Period
   */
  intensity: Intensity;
  /**
   *
   * @type {string}
   * @memberof Period
   */
  to: string;
}
/**
 *
 * @export
 * @interface ProjectionChargesResponse
 */
export interface ProjectionChargesResponse {
  /**
   *
   * @type {Array<Charges3>}
   * @memberof ProjectionChargesResponse
   */
  data: Array<Charges3>;
  /**
   *
   * @type {Meta}
   * @memberof ProjectionChargesResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface ProjectionGroupAndUserChargesResponse
 */
export interface ProjectionGroupAndUserChargesResponse {
  /**
   *
   * @type {UserChargesSchema}
   * @memberof ProjectionGroupAndUserChargesResponse
   */
  data: UserChargesSchema;
  /**
   *
   * @type {Meta}
   * @memberof ProjectionGroupAndUserChargesResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface ProjectionchargerChargeStatisticsResponse
 */
export interface ProjectionchargerChargeStatisticsResponse {
  /**
   *
   * @type {ChargerChargeStatistics}
   * @memberof ProjectionchargerChargeStatisticsResponse
   */
  data: ChargerChargeStatistics;
  /**
   *
   * @type {Meta}
   * @memberof ProjectionchargerChargeStatisticsResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface ProjectiongroupChargeStatisticsResponse
 */
export interface ProjectiongroupChargeStatisticsResponse {
  /**
   *
   * @type {GroupChargeStatistics}
   * @memberof ProjectiongroupChargeStatisticsResponse
   */
  data: GroupChargeStatistics;
  /**
   *
   * @type {Meta}
   * @memberof ProjectiongroupChargeStatisticsResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface ProjectionsiteChargeStatisticsResponse
 */
export interface ProjectionsiteChargeStatisticsResponse {
  /**
   *
   * @type {SiteChargeStatistics}
   * @memberof ProjectionsiteChargeStatisticsResponse
   */
  data: SiteChargeStatistics;
  /**
   *
   * @type {Meta}
   * @memberof ProjectionsiteChargeStatisticsResponse
   */
  meta: Meta;
}
/**
 *
 * @export
 * @interface Region
 */
export interface Region {
  /**
   * National grid DNO region id.
   * @type {number}
   * @memberof Region
   */
  regionid: number;
}
/**
 *
 * @export
 * @interface Regions
 */
export interface Regions {
  /**
   *
   * @type {Array<Dnoregion>}
   * @memberof Regions
   */
  data: Array<Dnoregion>;
}
/**
 *
 * @export
 * @interface RetrieveOrganisationDriversStatisticsRequestBody
 */
export interface RetrieveOrganisationDriversStatisticsRequestBody {
  /**
   *
   * @type {Array<string>}
   * @memberof RetrieveOrganisationDriversStatisticsRequestBody
   */
  driverIds: Array<string>;
}
/**
 *
 * @export
 * @interface SiteChargeStatistics
 */
export interface SiteChargeStatistics {
  /**
   * sum of charging duration in seconds
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  chargingDuration: number;
  /**
   * CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  co2Savings: number;
  /**
   *
   * @type {EnergyStatistics}
   * @memberof SiteChargeStatistics
   */
  energy: EnergyStatistics;
  /**
   * count of distinct charger ids
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  numberOfChargers: number;
  /**
   * count of distinct charge uuids
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  numberOfCharges: number;
  /**
   * count of distinct user ids
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  numberOfUsers: number;
  /**
   * sum of settlement amount in pence
   * @type {number}
   * @memberof SiteChargeStatistics
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface SiteStats
 */
export interface SiteStats {
  /**
   * Group unique identifier.
   * @type {string}
   * @memberof SiteStats
   */
  groupId?: string;
  /**
   * Group Name.
   * @type {string}
   * @memberof SiteStats
   */
  groupName?: string;
  /**
   * Site unique identifier.
   * @type {string}
   * @memberof SiteStats
   */
  id: string;
  /**
   * Site name.
   * @type {string}
   * @memberof SiteStats
   */
  name?: string;
  /**
   * Revenue generated in pence
   * @type {number}
   * @memberof SiteStats
   */
  revenueGenerated: number;
  /**
   * Energy used in kWh.
   * @type {number}
   * @memberof SiteStats
   */
  totalEnergy: number;
}
/**
 *
 * @export
 * @interface SiteStatsResponse
 */
export interface SiteStatsResponse {
  /**
   * Count of sites returned.
   * @type {number}
   * @memberof SiteStatsResponse
   */
  count: number;
  /**
   * Charge data for site.
   * @type {Array<SiteStats>}
   * @memberof SiteStatsResponse
   */
  data: Array<SiteStats>;
  /**
   * Statistics report inclusive start date eg: 2022-01-01
   * @type {string}
   * @memberof SiteStatsResponse
   */
  from: string;
  /**
   * Statistics report inclusive end date eg: 2022-01-31
   * @type {string}
   * @memberof SiteStatsResponse
   */
  to: string;
}
/**
 *
 * @export
 * @interface Sitechargessummary
 */
export interface Sitechargessummary {
  /**
   * Total duration of charging in seconds.
   * @type {number}
   * @memberof Sitechargessummary
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Sitechargessummary
   */
  co2Savings: number;
  /**
   *
   * @type {ChargeEnergySummary}
   * @memberof Sitechargessummary
   */
  energy: ChargeEnergySummary;
  /**
   * Total number of Chargers.
   * @type {number}
   * @memberof Sitechargessummary
   */
  numberOfChargers: number;
  /**
   * Total number of charges.
   * @type {number}
   * @memberof Sitechargessummary
   */
  numberOfCharges: number;
  /**
   * Total number of unique drivers.
   * @type {number}
   * @memberof Sitechargessummary
   */
  numberOfDrivers: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof Sitechargessummary
   */
  revenueGenerated: number;
}
/**
 *
 * @export
 * @interface StatsSummary
 */
export interface StatsSummary {
  /**
   *
   * @type {Cost}
   * @memberof StatsSummary
   */
  cost: Cost;
  /**
   *
   * @type {Duration}
   * @memberof StatsSummary
   */
  duration: Duration;
  /**
   *
   * @type {Energy}
   * @memberof StatsSummary
   */
  energy: Energy;
}
/**
 *
 * @export
 * @interface SubmitDriverExpensesRequestBody
 */
export interface SubmitDriverExpensesRequestBody {
  /**
   *
   * @type {Array<SubmitExpenseRequest>}
   * @memberof SubmitDriverExpensesRequestBody
   */
  expenses?: Array<SubmitExpenseRequest>;
}
/**
 *
 * @export
 * @interface SubmitExpenseRequest
 */
export interface SubmitExpenseRequest {
  /**
   * UUID of the submitted charge.
   * @type {string}
   * @memberof SubmitExpenseRequest
   */
  chargeId: string;
}
/**
 *
 * @export
 * @interface SubmittedCharge
 */
export interface SubmittedCharge {
  /**
   * Charge cost in pence
   * @type {number}
   * @memberof SubmittedCharge
   */
  chargeCost: number;
  /**
   * Public charger name or home charger PSL
   * @type {string}
   * @memberof SubmittedCharge
   */
  chargerName: string;
  /**
   * Charge duration in seconds
   * @type {number}
   * @memberof SubmittedCharge
   */
  duration: number;
  /**
   * Charge end time UTC
   * @type {string}
   * @memberof SubmittedCharge
   */
  endTime: string;
  /**
   * Energy used in KWh
   * @type {number}
   * @memberof SubmittedCharge
   */
  energyUsage: number;
  /**
   * Primary key of the charge
   * @type {number}
   * @memberof SubmittedCharge
   */
  id: number;
  /**
   *
   * @type {SubmittedChargeLocation}
   * @memberof SubmittedCharge
   */
  location: SubmittedChargeLocation;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof SubmittedCharge
   */
  pluggedInAt?: string;
  /**
   * Who processed this expense (if processed)
   * @type {string}
   * @memberof SubmittedCharge
   */
  processedByFullName?: string;
  /**
   * Processed time UTC (if processed)
   * @type {string}
   * @memberof SubmittedCharge
   */
  processedTime?: string;
  /**
   * Charge start time UTC
   * @type {string}
   * @memberof SubmittedCharge
   */
  startTime: string;
  /**
   * Whether the charge is NEW or PROCESSED
   * @type {string}
   * @memberof SubmittedCharge
   */
  status: SubmittedChargeStatusEnum;
  /**
   * Submitted for approval time UTC
   * @type {string}
   * @memberof SubmittedCharge
   */
  submittedTime: string;
  /**
   * Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
   * @type {string}
   * @memberof SubmittedCharge
   */
  unpluggedAt?: string;
}

export const SubmittedChargeStatusEnum = {
  New: 'NEW',
  Processed: 'PROCESSED',
} as const;

export type SubmittedChargeStatusEnum =
  (typeof SubmittedChargeStatusEnum)[keyof typeof SubmittedChargeStatusEnum];

/**
 *
 * @export
 * @interface SubmittedChargeAddress
 */
export interface SubmittedChargeAddress {
  /**
   * Country full name
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  country?: string;
  /**
   * Address line 1
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  line1: string;
  /**
   * Address line 2
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  line2?: string;
  /**
   * Postcode
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  postcode?: string;
  /**
   * User-friendly string representation of address
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  prettyPrint: string;
  /**
   * Town
   * @type {string}
   * @memberof SubmittedChargeAddress
   */
  town: string;
}
/**
 *
 * @export
 * @interface SubmittedChargeLocation
 */
export interface SubmittedChargeLocation {
  /**
   *
   * @type {SubmittedChargeAddress}
   * @memberof SubmittedChargeLocation
   */
  address: SubmittedChargeAddress;
  /**
   * ID of the charge location
   * @type {number}
   * @memberof SubmittedChargeLocation
   */
  id: number;
  /**
   * Type of the location: home or public
   * @type {string}
   * @memberof SubmittedChargeLocation
   */
  locationType: SubmittedChargeLocationLocationTypeEnum;
}

export const SubmittedChargeLocationLocationTypeEnum = {
  Home: 'home',
  Public: 'public',
} as const;

export type SubmittedChargeLocationLocationTypeEnum =
  (typeof SubmittedChargeLocationLocationTypeEnum)[keyof typeof SubmittedChargeLocationLocationTypeEnum];

/**
 * List of submitted charges and driver data
 * @export
 * @interface SubmittedChargesResponse
 */
export interface SubmittedChargesResponse {
  /**
   *
   * @type {Driver}
   * @memberof SubmittedChargesResponse
   */
  driver: Driver;
  /**
   *
   * @type {Array<SubmittedCharge>}
   * @memberof SubmittedChargesResponse
   */
  submittedCharges: Array<SubmittedCharge>;
  /**
   *
   * @type {TotalCost}
   * @memberof SubmittedChargesResponse
   */
  totalCost: TotalCost;
  /**
   *
   * @type {TotalUsage}
   * @memberof SubmittedChargesResponse
   */
  totalUsage: TotalUsage;
}
/**
 *
 * @export
 * @interface SubmittedExpense
 */
export interface SubmittedExpense {
  /**
   * UUID of the charge associated with the expense.
   * @type {string}
   * @memberof SubmittedExpense
   */
  chargeId: string;
  /**
   * Primary key of the expense.
   * @type {number}
   * @memberof SubmittedExpense
   */
  id: number;
}
/**
 *
 * @export
 * @interface SubmittedExpenseResponse
 */
export interface SubmittedExpenseResponse {
  /**
   *
   * @type {Array<SubmittedExpense>}
   * @memberof SubmittedExpenseResponse
   */
  expenses?: Array<SubmittedExpense>;
}
/**
 * TimeRangeOutOfBounds is the error returned when the requested time window exceeds the maximum range.
 * @export
 * @interface TimeRangeOutOfBounds
 */
export interface TimeRangeOutOfBounds {
  /**
   *
   * @type {string}
   * @memberof TimeRangeOutOfBounds
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof TimeRangeOutOfBounds
   */
  status: number;
}
/**
 *
 * @export
 * @interface TotalCharges
 */
export interface TotalCharges {
  /**
   * Number of charges attributed to Home chargers.
   * @type {number}
   * @memberof TotalCharges
   */
  home: number;
  /**
   * Number of charges attributed to Public chargers.
   * @type {number}
   * @memberof TotalCharges
   */
  public: number;
  /**
   * Number of charges.
   * @type {number}
   * @memberof TotalCharges
   */
  total: number;
}
/**
 *
 * @export
 * @interface TotalCost
 */
export interface TotalCost {
  /**
   * Amount expensed in pence for home charges.
   * @type {number}
   * @memberof TotalCost
   */
  home: number;
  /**
   * Amount expensed in pence for public charges.
   * @type {number}
   * @memberof TotalCost
   */
  public: number;
}
/**
 *
 * @export
 * @interface TotalUsage
 */
export interface TotalUsage {
  /**
   * Total number of kWh used for home charges.
   * @type {number}
   * @memberof TotalUsage
   */
  home: number;
  /**
   * Total number of kWh used for public charges.
   * @type {number}
   * @memberof TotalUsage
   */
  public: number;
  /**
   * Total number of kWh used.
   * @type {number}
   * @memberof TotalUsage
   */
  total?: number;
}
/**
 * TransactionNotStarted is the error returned when a charge cycle transaction was not created as part of OCPI charge authorisation.
 * @export
 * @interface TransactionNotStarted
 */
export interface TransactionNotStarted {
  /**
   *
   * @type {string}
   * @memberof TransactionNotStarted
   */
  reason: string;
  /**
   *
   * @type {number}
   * @memberof TransactionNotStarted
   */
  status: number;
}
/**
 *
 * @export
 * @interface Usage
 */
export interface Usage {
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof Usage
   */
  co2Savings: number;
  /**
   * Energy cost in pence
   * @type {number}
   * @memberof Usage
   */
  cost: number;
  /**
   * The start date of this usage interval
   * @type {string}
   * @memberof Usage
   */
  intervalStartDate: string;
  /**
   * Revenue generated in pence
   * @type {number}
   * @memberof Usage
   */
  revenueGenerated: number;
  /**
   * Energy used this period in kWh
   * @type {number}
   * @memberof Usage
   */
  totalUsage: number;
}
/**
 * Basic usage by time periods
 * @export
 * @interface UsageResponse
 */
export interface UsageResponse {
  /**
   *
   * @type {Meta}
   * @memberof UsageResponse
   */
  meta: Meta;
  /**
   *
   * @type {Array<Usage>}
   * @memberof UsageResponse
   */
  usage?: Array<Usage>;
}
/**
 *
 * @export
 * @interface UserCharge
 */
export interface UserCharge {
  /**
   * Business to whom this charge is associated
   * @type {string}
   * @memberof UserCharge
   */
  businessName: string;
  /**
   * Charge cost in pence
   * @type {number}
   * @memberof UserCharge
   */
  chargeCost: number;
  /**
   * Name of charger associated with charge
   * @type {string}
   * @memberof UserCharge
   */
  chargerName: string;
  /**
   * Charge duration in seconds
   * @type {number}
   * @memberof UserCharge
   */
  chargingDuration: number;
  /**
   * CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
   * @type {number}
   * @memberof UserCharge
   */
  co2Savings: number;
  /**
   * Charge end time UTC
   * @type {string}
   * @memberof UserCharge
   */
  endTime: string;
  /**
   * Energy used in kWh
   * @type {number}
   * @memberof UserCharge
   */
  energyUsage: number;
  /**
   * Plugged-in duration in seconds
   * @type {number}
   * @memberof UserCharge
   */
  pluggedInDuration: number;
  /**
   * Revenue generated in pence.
   * @type {number}
   * @memberof UserCharge
   */
  revenueGenerated: number;
  /**
   * Charge start time UTC
   * @type {string}
   * @memberof UserCharge
   */
  startTime: string;
}
/**
 * List of user\'s charges
 * @export
 * @interface UserChargesSchema
 */
export interface UserChargesSchema {
  /**
   *
   * @type {Array<UserCharge>}
   * @memberof UserChargesSchema
   */
  charges?: Array<UserCharge>;
}

/**
 * CarbonIntensityApi - axios parameter creator
 * @export
 */
export const CarbonIntensityApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve half-hourly forecast data 48 hours from provided date.
     * @summary Retrieve regional forecast 48 hours from date Carbon intensity
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonIntensityRetrieveRegionalForecast48HoursFromDate: async (
      from: string,
      regionId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'carbonIntensityRetrieveRegionalForecast48HoursFromDate',
        'from',
        from
      );
      // verify required parameter 'regionId' is not null or undefined
      assertParamExists(
        'carbonIntensityRetrieveRegionalForecast48HoursFromDate',
        'regionId',
        regionId
      );
      const localVarPath =
        `/regional/intensity/{from}/fw48h/regionid/{regionId}`
          .replace(`{${'from'}}`, encodeURIComponent(String(from)))
          .replace(`{${'regionId'}}`, encodeURIComponent(String(regionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CarbonIntensityApi - functional programming interface
 * @export
 */
export const CarbonIntensityApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CarbonIntensityApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve half-hourly forecast data 48 hours from provided date.
     * @summary Retrieve regional forecast 48 hours from date Carbon intensity
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async carbonIntensityRetrieveRegionalForecast48HoursFromDate(
      from: string,
      regionId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Forecast>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.carbonIntensityRetrieveRegionalForecast48HoursFromDate(
          from,
          regionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CarbonIntensityApi.carbonIntensityRetrieveRegionalForecast48HoursFromDate'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CarbonIntensityApi - factory interface
 * @export
 */
export const CarbonIntensityApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CarbonIntensityApiFp(configuration);
  return {
    /**
     * Retrieve half-hourly forecast data 48 hours from provided date.
     * @summary Retrieve regional forecast 48 hours from date Carbon intensity
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonIntensityRetrieveRegionalForecast48HoursFromDate(
      from: string,
      regionId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Forecast> {
      return localVarFp
        .carbonIntensityRetrieveRegionalForecast48HoursFromDate(
          from,
          regionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CarbonIntensityApi - object-oriented interface
 * @export
 * @class CarbonIntensityApi
 * @extends {BaseAPI}
 */
export class CarbonIntensityApi extends BaseAPI {
  /**
   * Retrieve half-hourly forecast data 48 hours from provided date.
   * @summary Retrieve regional forecast 48 hours from date Carbon intensity
   * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format.
   * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CarbonIntensityApi
   */
  public carbonIntensityRetrieveRegionalForecast48HoursFromDate(
    from: string,
    regionId: number,
    options?: RawAxiosRequestConfig
  ) {
    return CarbonIntensityApiFp(this.configuration)
      .carbonIntensityRetrieveRegionalForecast48HoursFromDate(
        from,
        regionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargeAuthorisationApi - axios parameter creator
 * @export
 */
export const ChargeAuthorisationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger\'s group.
     * @summary authoriseCharge Charge Authorisation
     * @param {string} authorisationMethod One of rfid or ocpi.
     * @param {AuthoriseChargeRequestBody} authoriseChargeRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeAuthorisationAuthoriseCharge: async (
      authorisationMethod: string,
      authoriseChargeRequestBody: AuthoriseChargeRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'authorisationMethod' is not null or undefined
      assertParamExists(
        'chargeAuthorisationAuthoriseCharge',
        'authorisationMethod',
        authorisationMethod
      );
      // verify required parameter 'authoriseChargeRequestBody' is not null or undefined
      assertParamExists(
        'chargeAuthorisationAuthoriseCharge',
        'authoriseChargeRequestBody',
        authoriseChargeRequestBody
      );
      const localVarPath =
        `/charge-authorisations/{authorisationMethod}`.replace(
          `{${'authorisationMethod'}}`,
          encodeURIComponent(String(authorisationMethod))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        authoriseChargeRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeAuthorisationApi - functional programming interface
 * @export
 */
export const ChargeAuthorisationApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ChargeAuthorisationApiAxiosParamCreator(configuration);
  return {
    /**
     * Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger\'s group.
     * @summary authoriseCharge Charge Authorisation
     * @param {string} authorisationMethod One of rfid or ocpi.
     * @param {AuthoriseChargeRequestBody} authoriseChargeRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeAuthorisationAuthoriseCharge(
      authorisationMethod: string,
      authoriseChargeRequestBody: AuthoriseChargeRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargeAuthorisationResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeAuthorisationAuthoriseCharge(
          authorisationMethod,
          authoriseChargeRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeAuthorisationApi.chargeAuthorisationAuthoriseCharge'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeAuthorisationApi - factory interface
 * @export
 */
export const ChargeAuthorisationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeAuthorisationApiFp(configuration);
  return {
    /**
     * Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger\'s group.
     * @summary authoriseCharge Charge Authorisation
     * @param {string} authorisationMethod One of rfid or ocpi.
     * @param {AuthoriseChargeRequestBody} authoriseChargeRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeAuthorisationAuthoriseCharge(
      authorisationMethod: string,
      authoriseChargeRequestBody: AuthoriseChargeRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargeAuthorisationResponse> {
      return localVarFp
        .chargeAuthorisationAuthoriseCharge(
          authorisationMethod,
          authoriseChargeRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeAuthorisationApi - object-oriented interface
 * @export
 * @class ChargeAuthorisationApi
 * @extends {BaseAPI}
 */
export class ChargeAuthorisationApi extends BaseAPI {
  /**
   * Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger\'s group.
   * @summary authoriseCharge Charge Authorisation
   * @param {string} authorisationMethod One of rfid or ocpi.
   * @param {AuthoriseChargeRequestBody} authoriseChargeRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeAuthorisationApi
   */
  public chargeAuthorisationAuthoriseCharge(
    authorisationMethod: string,
    authoriseChargeRequestBody: AuthoriseChargeRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeAuthorisationApiFp(this.configuration)
      .chargeAuthorisationAuthoriseCharge(
        authorisationMethod,
        authoriseChargeRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargeCommandsApi - axios parameter creator
 * @export
 */
export const ChargeCommandsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Update the energy cost of a charge.
     * @summary Correct energy cost Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectEnergyCostRequestBody} correctEnergyCostRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeCommandsCorrectEnergyCost: async (
      chargeID: string,
      correctEnergyCostRequestBody: CorrectEnergyCostRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'chargeID' is not null or undefined
      assertParamExists(
        'chargeCommandsCorrectEnergyCost',
        'chargeID',
        chargeID
      );
      // verify required parameter 'correctEnergyCostRequestBody' is not null or undefined
      assertParamExists(
        'chargeCommandsCorrectEnergyCost',
        'correctEnergyCostRequestBody',
        correctEnergyCostRequestBody
      );
      const localVarPath =
        `/commands/charges/{chargeID}/correct-energy-cost`.replace(
          `{${'chargeID'}}`,
          encodeURIComponent(String(chargeID))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        correctEnergyCostRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update the settlement amount of a charge.
     * @summary Correct settlement amount Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectSettlementAmountRequestBody} correctSettlementAmountRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeCommandsCorrectSettlementAmount: async (
      chargeID: string,
      correctSettlementAmountRequestBody: CorrectSettlementAmountRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'chargeID' is not null or undefined
      assertParamExists(
        'chargeCommandsCorrectSettlementAmount',
        'chargeID',
        chargeID
      );
      // verify required parameter 'correctSettlementAmountRequestBody' is not null or undefined
      assertParamExists(
        'chargeCommandsCorrectSettlementAmount',
        'correctSettlementAmountRequestBody',
        correctSettlementAmountRequestBody
      );
      const localVarPath =
        `/commands/charges/{chargeID}/correct-settlement-amount`.replace(
          `{${'chargeID'}}`,
          encodeURIComponent(String(chargeID))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        correctSettlementAmountRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeCommandsApi - functional programming interface
 * @export
 */
export const ChargeCommandsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargeCommandsApiAxiosParamCreator(configuration);
  return {
    /**
     * Update the energy cost of a charge.
     * @summary Correct energy cost Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectEnergyCostRequestBody} correctEnergyCostRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeCommandsCorrectEnergyCost(
      chargeID: string,
      correctEnergyCostRequestBody: CorrectEnergyCostRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AggregateCostCorrectedResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeCommandsCorrectEnergyCost(
          chargeID,
          correctEnergyCostRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeCommandsApi.chargeCommandsCorrectEnergyCost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update the settlement amount of a charge.
     * @summary Correct settlement amount Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectSettlementAmountRequestBody} correctSettlementAmountRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeCommandsCorrectSettlementAmount(
      chargeID: string,
      correctSettlementAmountRequestBody: CorrectSettlementAmountRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AggregateSettlementAmountCorrectedResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeCommandsCorrectSettlementAmount(
          chargeID,
          correctSettlementAmountRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeCommandsApi.chargeCommandsCorrectSettlementAmount'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeCommandsApi - factory interface
 * @export
 */
export const ChargeCommandsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeCommandsApiFp(configuration);
  return {
    /**
     * Update the energy cost of a charge.
     * @summary Correct energy cost Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectEnergyCostRequestBody} correctEnergyCostRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeCommandsCorrectEnergyCost(
      chargeID: string,
      correctEnergyCostRequestBody: CorrectEnergyCostRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AggregateCostCorrectedResponse> {
      return localVarFp
        .chargeCommandsCorrectEnergyCost(
          chargeID,
          correctEnergyCostRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update the settlement amount of a charge.
     * @summary Correct settlement amount Charge commands
     * @param {string} chargeID UUID of the charge to correct.
     * @param {CorrectSettlementAmountRequestBody} correctSettlementAmountRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeCommandsCorrectSettlementAmount(
      chargeID: string,
      correctSettlementAmountRequestBody: CorrectSettlementAmountRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AggregateSettlementAmountCorrectedResponse> {
      return localVarFp
        .chargeCommandsCorrectSettlementAmount(
          chargeID,
          correctSettlementAmountRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeCommandsApi - object-oriented interface
 * @export
 * @class ChargeCommandsApi
 * @extends {BaseAPI}
 */
export class ChargeCommandsApi extends BaseAPI {
  /**
   * Update the energy cost of a charge.
   * @summary Correct energy cost Charge commands
   * @param {string} chargeID UUID of the charge to correct.
   * @param {CorrectEnergyCostRequestBody} correctEnergyCostRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeCommandsApi
   */
  public chargeCommandsCorrectEnergyCost(
    chargeID: string,
    correctEnergyCostRequestBody: CorrectEnergyCostRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeCommandsApiFp(this.configuration)
      .chargeCommandsCorrectEnergyCost(
        chargeID,
        correctEnergyCostRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update the settlement amount of a charge.
   * @summary Correct settlement amount Charge commands
   * @param {string} chargeID UUID of the charge to correct.
   * @param {CorrectSettlementAmountRequestBody} correctSettlementAmountRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeCommandsApi
   */
  public chargeCommandsCorrectSettlementAmount(
    chargeID: string,
    correctSettlementAmountRequestBody: CorrectSettlementAmountRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeCommandsApiFp(this.configuration)
      .chargeCommandsCorrectSettlementAmount(
        chargeID,
        correctSettlementAmountRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargeStatisticsApi - axios parameter creator
 * @export
 */
export const ChargeStatisticsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}
     * @summary Charger Charge Statistics Charge Statistics
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsChargerChargeStatistics: async (
      chargerId: string,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'chargerId' is not null or undefined
      assertParamExists(
        'chargeStatisticsChargerChargeStatistics',
        'chargerId',
        chargerId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'chargeStatisticsChargerChargeStatistics',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargeStatisticsChargerChargeStatistics', 'to', to);
      const localVarPath = `/chargers/{chargerId}/charge-statistics`.replace(
        `{${'chargerId'}}`,
        encodeURIComponent(String(chargerId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats
     * @summary Group and Charger Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupAndChargerUsageSummaries: async (
      groupId: string,
      chargerId: string,
      from: string,
      to: string,
      interval: ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndChargerUsageSummaries',
        'groupId',
        groupId
      );
      // verify required parameter 'chargerId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndChargerUsageSummaries',
        'chargerId',
        chargerId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndChargerUsageSummaries',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndChargerUsageSummaries',
        'to',
        to
      );
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndChargerUsageSummaries',
        'interval',
        interval
      );
      const localVarPath =
        `/groups/{groupId}/chargers/{chargerId}/charge-statistics/{interval}`
          .replace(`{${'groupId'}}`, encodeURIComponent(String(groupId)))
          .replace(`{${'chargerId'}}`, encodeURIComponent(String(chargerId)))
          .replace(`{${'interval'}}`, encodeURIComponent(String(interval)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats
     * @summary Group and Site Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} siteId UUID of the site.
     * @param {ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupAndSiteUsageSummaries: async (
      groupId: string,
      siteId: string,
      interval: ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndSiteUsageSummaries',
        'groupId',
        groupId
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndSiteUsageSummaries',
        'siteId',
        siteId
      );
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndSiteUsageSummaries',
        'interval',
        interval
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupAndSiteUsageSummaries',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargeStatisticsGroupAndSiteUsageSummaries', 'to', to);
      const localVarPath =
        `/groups/{groupId}/sites/{siteId}/charge-statistics/{interval}`
          .replace(`{${'groupId'}}`, encodeURIComponent(String(groupId)))
          .replace(`{${'siteId'}}`, encodeURIComponent(String(siteId)))
          .replace(`{${'interval'}}`, encodeURIComponent(String(interval)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}
     * @summary Group Charge Statistics Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupChargeStatistics: async (
      groupId: string,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupChargeStatistics',
        'groupId',
        groupId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargeStatisticsGroupChargeStatistics', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargeStatisticsGroupChargeStatistics', 'to', to);
      const localVarPath = `/groups/{groupId}/charge-statistics`.replace(
        `{${'groupId'}}`,
        encodeURIComponent(String(groupId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats
     * @summary Group Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {ChargeStatisticsGroupUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupUsageSummaries: async (
      groupId: string,
      interval: ChargeStatisticsGroupUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupUsageSummaries',
        'groupId',
        groupId
      );
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'chargeStatisticsGroupUsageSummaries',
        'interval',
        interval
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargeStatisticsGroupUsageSummaries', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargeStatisticsGroupUsageSummaries', 'to', to);
      const localVarPath = `/groups/{groupId}/charge-statistics/{interval}`
        .replace(`{${'groupId'}}`, encodeURIComponent(String(groupId)))
        .replace(`{${'interval'}}`, encodeURIComponent(String(interval)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}
     * @summary Site Charge Statistics Charge Statistics
     * @param {string} siteId UUID of the site.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsSiteChargeStatistics: async (
      siteId: string,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists(
        'chargeStatisticsSiteChargeStatistics',
        'siteId',
        siteId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargeStatisticsSiteChargeStatistics', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargeStatisticsSiteChargeStatistics', 'to', to);
      const localVarPath = `/sites/{siteId}/charge-statistics`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargeStatisticsApi - functional programming interface
 * @export
 */
export const ChargeStatisticsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ChargeStatisticsApiAxiosParamCreator(configuration);
  return {
    /**
     * Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}
     * @summary Charger Charge Statistics Charge Statistics
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsChargerChargeStatistics(
      chargerId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProjectionchargerChargeStatisticsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsChargerChargeStatistics(
          chargerId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsChargerChargeStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats
     * @summary Group and Charger Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsGroupAndChargerUsageSummaries(
      groupId: string,
      chargerId: string,
      from: string,
      to: string,
      interval: ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsGroupAndChargerUsageSummaries(
          groupId,
          chargerId,
          from,
          to,
          interval,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsGroupAndChargerUsageSummaries'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats
     * @summary Group and Site Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} siteId UUID of the site.
     * @param {ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsGroupAndSiteUsageSummaries(
      groupId: string,
      siteId: string,
      interval: ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsGroupAndSiteUsageSummaries(
          groupId,
          siteId,
          interval,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsGroupAndSiteUsageSummaries'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}
     * @summary Group Charge Statistics Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsGroupChargeStatistics(
      groupId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProjectiongroupChargeStatisticsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsGroupChargeStatistics(
          groupId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsGroupChargeStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats
     * @summary Group Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {ChargeStatisticsGroupUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsGroupUsageSummaries(
      groupId: string,
      interval: ChargeStatisticsGroupUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsGroupUsageSummaries(
          groupId,
          interval,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsGroupUsageSummaries'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}
     * @summary Site Charge Statistics Charge Statistics
     * @param {string} siteId UUID of the site.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargeStatisticsSiteChargeStatistics(
      siteId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProjectionsiteChargeStatisticsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargeStatisticsSiteChargeStatistics(
          siteId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargeStatisticsApi.chargeStatisticsSiteChargeStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargeStatisticsApi - factory interface
 * @export
 */
export const ChargeStatisticsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargeStatisticsApiFp(configuration);
  return {
    /**
     * Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}
     * @summary Charger Charge Statistics Charge Statistics
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsChargerChargeStatistics(
      chargerId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProjectionchargerChargeStatisticsResponse> {
      return localVarFp
        .chargeStatisticsChargerChargeStatistics(chargerId, from, to, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats
     * @summary Group and Charger Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} chargerId ID of the charger (equivalent to PPID).
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupAndChargerUsageSummaries(
      groupId: string,
      chargerId: string,
      from: string,
      to: string,
      interval: ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .chargeStatisticsGroupAndChargerUsageSummaries(
          groupId,
          chargerId,
          from,
          to,
          interval,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats
     * @summary Group and Site Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} siteId UUID of the site.
     * @param {ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupAndSiteUsageSummaries(
      groupId: string,
      siteId: string,
      interval: ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .chargeStatisticsGroupAndSiteUsageSummaries(
          groupId,
          siteId,
          interval,
          from,
          to,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}
     * @summary Group Charge Statistics Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupChargeStatistics(
      groupId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProjectiongroupChargeStatisticsResponse> {
      return localVarFp
        .chargeStatisticsGroupChargeStatistics(groupId, from, to, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats
     * @summary Group Usage Summaries Charge Statistics
     * @param {string} groupId UUID of the group.
     * @param {ChargeStatisticsGroupUsageSummariesIntervalEnum} interval Reporting interval
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsGroupUsageSummaries(
      groupId: string,
      interval: ChargeStatisticsGroupUsageSummariesIntervalEnum,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .chargeStatisticsGroupUsageSummaries(
          groupId,
          interval,
          from,
          to,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}
     * @summary Site Charge Statistics Charge Statistics
     * @param {string} siteId UUID of the site.
     * @param {string} from Inclusive from date used for filtering on unpluggedAt
     * @param {string} to Inclusive to date used for filtering on unpluggedAt
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargeStatisticsSiteChargeStatistics(
      siteId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProjectionsiteChargeStatisticsResponse> {
      return localVarFp
        .chargeStatisticsSiteChargeStatistics(siteId, from, to, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargeStatisticsApi - object-oriented interface
 * @export
 * @class ChargeStatisticsApi
 * @extends {BaseAPI}
 */
export class ChargeStatisticsApi extends BaseAPI {
  /**
   * Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}
   * @summary Charger Charge Statistics Charge Statistics
   * @param {string} chargerId ID of the charger (equivalent to PPID).
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsChargerChargeStatistics(
    chargerId: string,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsChargerChargeStatistics(chargerId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats
   * @summary Group and Charger Usage Summaries Charge Statistics
   * @param {string} groupId UUID of the group.
   * @param {string} chargerId ID of the charger (equivalent to PPID).
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum} interval Reporting interval
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsGroupAndChargerUsageSummaries(
    groupId: string,
    chargerId: string,
    from: string,
    to: string,
    interval: ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsGroupAndChargerUsageSummaries(
        groupId,
        chargerId,
        from,
        to,
        interval,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats
   * @summary Group and Site Usage Summaries Charge Statistics
   * @param {string} groupId UUID of the group.
   * @param {string} siteId UUID of the site.
   * @param {ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum} interval Reporting interval
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsGroupAndSiteUsageSummaries(
    groupId: string,
    siteId: string,
    interval: ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsGroupAndSiteUsageSummaries(
        groupId,
        siteId,
        interval,
        from,
        to,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}
   * @summary Group Charge Statistics Charge Statistics
   * @param {string} groupId UUID of the group.
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsGroupChargeStatistics(
    groupId: string,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsGroupChargeStatistics(groupId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats
   * @summary Group Usage Summaries Charge Statistics
   * @param {string} groupId UUID of the group.
   * @param {ChargeStatisticsGroupUsageSummariesIntervalEnum} interval Reporting interval
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsGroupUsageSummaries(
    groupId: string,
    interval: ChargeStatisticsGroupUsageSummariesIntervalEnum,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsGroupUsageSummaries(groupId, interval, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}
   * @summary Site Charge Statistics Charge Statistics
   * @param {string} siteId UUID of the site.
   * @param {string} from Inclusive from date used for filtering on unpluggedAt
   * @param {string} to Inclusive to date used for filtering on unpluggedAt
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargeStatisticsApi
   */
  public chargeStatisticsSiteChargeStatistics(
    siteId: string,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargeStatisticsApiFp(this.configuration)
      .chargeStatisticsSiteChargeStatistics(siteId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum =
  (typeof ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum)[keyof typeof ChargeStatisticsGroupAndChargerUsageSummariesIntervalEnum];
/**
 * @export
 */
export const ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum =
  (typeof ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum)[keyof typeof ChargeStatisticsGroupAndSiteUsageSummariesIntervalEnum];
/**
 * @export
 */
export const ChargeStatisticsGroupUsageSummariesIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type ChargeStatisticsGroupUsageSummariesIntervalEnum =
  (typeof ChargeStatisticsGroupUsageSummariesIntervalEnum)[keyof typeof ChargeStatisticsGroupUsageSummariesIntervalEnum];

/**
 * ChargersApi - axios parameter creator
 * @export
 */
export const ChargersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary Retrieve charger region Chargers
     * @param {string} ppId PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveChargerRegion: async (
      ppId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppId' is not null or undefined
      assertParamExists('chargersRetrieveChargerRegion', 'ppId', ppId);
      const localVarPath = `/chargers/{ppId}/dnoregion`.replace(
        `{${'ppId'}}`,
        encodeURIComponent(String(ppId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve limit for charging
     * @summary Retrieve charging limit Chargers
     * @param {string} ppID PPID (PSL number) of a charger
     * @param {string} authoriserUUID UUID of charge authoriser - can be user\&#39;s UUID or app name, like &#x60;dcs&#x60; or RFID key
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveChargingLimit: async (
      ppID: string,
      authoriserUUID: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppID' is not null or undefined
      assertParamExists('chargersRetrieveChargingLimit', 'ppID', ppID);
      // verify required parameter 'authoriserUUID' is not null or undefined
      assertParamExists(
        'chargersRetrieveChargingLimit',
        'authoriserUUID',
        authoriserUUID
      );
      const localVarPath = `/chargers/{ppID}/limit`.replace(
        `{${'ppID'}}`,
        encodeURIComponent(String(ppID))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (authoriserUUID !== undefined) {
        localVarQueryParameter['authoriserUUID'] = authoriserUUID;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve the complete collection of DNO region objects.
     * @summary Retrieve DNO regions Chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveDNORegions: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/dno-regions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargersApi - functional programming interface
 * @export
 */
export const ChargersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ChargersApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary Retrieve charger region Chargers
     * @param {string} ppId PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersRetrieveChargerRegion(
      ppId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Region>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersRetrieveChargerRegion(
          ppId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersRetrieveChargerRegion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve limit for charging
     * @summary Retrieve charging limit Chargers
     * @param {string} ppID PPID (PSL number) of a charger
     * @param {string} authoriserUUID UUID of charge authoriser - can be user\&#39;s UUID or app name, like &#x60;dcs&#x60; or RFID key
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersRetrieveChargingLimit(
      ppID: string,
      authoriserUUID: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargesLimitResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersRetrieveChargingLimit(
          ppID,
          authoriserUUID,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersRetrieveChargingLimit']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve the complete collection of DNO region objects.
     * @summary Retrieve DNO regions Chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersRetrieveDNORegions(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Regions>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersRetrieveDNORegions(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersRetrieveDNORegions']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargersApi - factory interface
 * @export
 */
export const ChargersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargersApiFp(configuration);
  return {
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary Retrieve charger region Chargers
     * @param {string} ppId PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveChargerRegion(
      ppId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Region> {
      return localVarFp
        .chargersRetrieveChargerRegion(ppId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve limit for charging
     * @summary Retrieve charging limit Chargers
     * @param {string} ppID PPID (PSL number) of a charger
     * @param {string} authoriserUUID UUID of charge authoriser - can be user\&#39;s UUID or app name, like &#x60;dcs&#x60; or RFID key
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveChargingLimit(
      ppID: string,
      authoriserUUID: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargesLimitResponse> {
      return localVarFp
        .chargersRetrieveChargingLimit(ppID, authoriserUUID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve the complete collection of DNO region objects.
     * @summary Retrieve DNO regions Chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersRetrieveDNORegions(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Regions> {
      return localVarFp
        .chargersRetrieveDNORegions(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargersApi - object-oriented interface
 * @export
 * @class ChargersApi
 * @extends {BaseAPI}
 */
export class ChargersApi extends BaseAPI {
  /**
   * For a given PSL / PPID, return the associated DNO region id.
   * @summary Retrieve charger region Chargers
   * @param {string} ppId PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersRetrieveChargerRegion(
    ppId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersRetrieveChargerRegion(ppId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve limit for charging
   * @summary Retrieve charging limit Chargers
   * @param {string} ppID PPID (PSL number) of a charger
   * @param {string} authoriserUUID UUID of charge authoriser - can be user\&#39;s UUID or app name, like &#x60;dcs&#x60; or RFID key
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersRetrieveChargingLimit(
    ppID: string,
    authoriserUUID: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersRetrieveChargingLimit(ppID, authoriserUUID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve the complete collection of DNO region objects.
   * @summary Retrieve DNO regions Chargers
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersRetrieveDNORegions(options?: RawAxiosRequestConfig) {
    return ChargersApiFp(this.configuration)
      .chargersRetrieveDNORegions(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargingStatisticsApi - axios parameter creator
 * @export
 */
export const ChargingStatisticsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Charge statistics for a single charger between two inclusive dates.
     * @summary Charger statistics Charging statistics
     * @param {number} locationId Primary key of the charger location.
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsChargerStatistics: async (
      locationId: number,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'chargingStatisticsChargerStatistics',
        'locationId',
        locationId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargingStatisticsChargerStatistics', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargingStatisticsChargerStatistics', 'to', to);
      const localVarPath = `/charges/charger/{locationId}`.replace(
        `{${'locationId'}}`,
        encodeURIComponent(String(locationId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the \'from\' and \'to\' date parameters constitute a valid date range. Where the \'to\' date is  prior to  the \'from\' date no results will be returned.
     * @summary Organisation statistics Charging statistics
     * @param {string} organisationId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsOrganisationStatistics: async (
      organisationId: string,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'chargingStatisticsOrganisationStatistics',
        'organisationId',
        organisationId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'chargingStatisticsOrganisationStatistics',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargingStatisticsOrganisationStatistics', 'to', to);
      const localVarPath = `/charges/group/{organisationId}`.replace(
        `{${'organisationId'}}`,
        encodeURIComponent(String(organisationId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Charge statistics for a site between two inclusive dates.
     * @summary Site statistics Charging statistics
     * @param {number} siteId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsSiteStatistics: async (
      siteId: number,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('chargingStatisticsSiteStatistics', 'siteId', siteId);
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargingStatisticsSiteStatistics', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargingStatisticsSiteStatistics', 'to', to);
      const localVarPath = `/charges/site/{siteId}`.replace(
        `{${'siteId'}}`,
        encodeURIComponent(String(siteId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargingStatisticsApi - functional programming interface
 * @export
 */
export const ChargingStatisticsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ChargingStatisticsApiAxiosParamCreator(configuration);
  return {
    /**
     * Charge statistics for a single charger between two inclusive dates.
     * @summary Charger statistics Charging statistics
     * @param {number} locationId Primary key of the charger location.
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargingStatisticsChargerStatistics(
      locationId: number,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Chargerchargessummary>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargingStatisticsChargerStatistics(
          locationId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStatisticsApi.chargingStatisticsChargerStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the \'from\' and \'to\' date parameters constitute a valid date range. Where the \'to\' date is  prior to  the \'from\' date no results will be returned.
     * @summary Organisation statistics Charging statistics
     * @param {string} organisationId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargingStatisticsOrganisationStatistics(
      organisationId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Organisationchargessummary>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargingStatisticsOrganisationStatistics(
          organisationId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStatisticsApi.chargingStatisticsOrganisationStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Charge statistics for a site between two inclusive dates.
     * @summary Site statistics Charging statistics
     * @param {number} siteId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargingStatisticsSiteStatistics(
      siteId: number,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Sitechargessummary>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargingStatisticsSiteStatistics(
          siteId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargingStatisticsApi.chargingStatisticsSiteStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargingStatisticsApi - factory interface
 * @export
 */
export const ChargingStatisticsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargingStatisticsApiFp(configuration);
  return {
    /**
     * Charge statistics for a single charger between two inclusive dates.
     * @summary Charger statistics Charging statistics
     * @param {number} locationId Primary key of the charger location.
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsChargerStatistics(
      locationId: number,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Chargerchargessummary> {
      return localVarFp
        .chargingStatisticsChargerStatistics(locationId, from, to, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the \'from\' and \'to\' date parameters constitute a valid date range. Where the \'to\' date is  prior to  the \'from\' date no results will be returned.
     * @summary Organisation statistics Charging statistics
     * @param {string} organisationId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsOrganisationStatistics(
      organisationId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Organisationchargessummary> {
      return localVarFp
        .chargingStatisticsOrganisationStatistics(
          organisationId,
          from,
          to,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Charge statistics for a site between two inclusive dates.
     * @summary Site statistics Charging statistics
     * @param {number} siteId
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargingStatisticsSiteStatistics(
      siteId: number,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Sitechargessummary> {
      return localVarFp
        .chargingStatisticsSiteStatistics(siteId, from, to, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargingStatisticsApi - object-oriented interface
 * @export
 * @class ChargingStatisticsApi
 * @extends {BaseAPI}
 */
export class ChargingStatisticsApi extends BaseAPI {
  /**
   * Charge statistics for a single charger between two inclusive dates.
   * @summary Charger statistics Charging statistics
   * @param {number} locationId Primary key of the charger location.
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStatisticsApi
   */
  public chargingStatisticsChargerStatistics(
    locationId: number,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStatisticsApiFp(this.configuration)
      .chargingStatisticsChargerStatistics(locationId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the \'from\' and \'to\' date parameters constitute a valid date range. Where the \'to\' date is  prior to  the \'from\' date no results will be returned.
   * @summary Organisation statistics Charging statistics
   * @param {string} organisationId
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStatisticsApi
   */
  public chargingStatisticsOrganisationStatistics(
    organisationId: string,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStatisticsApiFp(this.configuration)
      .chargingStatisticsOrganisationStatistics(
        organisationId,
        from,
        to,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Charge statistics for a site between two inclusive dates.
   * @summary Site statistics Charging statistics
   * @param {number} siteId
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargingStatisticsApi
   */
  public chargingStatisticsSiteStatistics(
    siteId: number,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargingStatisticsApiFp(this.configuration)
      .chargingStatisticsSiteStatistics(siteId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DriverChargesApi - axios parameter creator
 * @export
 */
export const DriverChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Submitting a list of charges as expenses for a driver within an organisation.
     * @summary Create driver expenses Driver charges
     * @param {number} driverId
     * @param {number} organisationId
     * @param {CreateDriverExpensesRequestBody} createDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesCreateDriverExpenses: async (
      driverId: number,
      organisationId: number,
      createDriverExpensesRequestBody: CreateDriverExpensesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverChargesCreateDriverExpenses',
        'driverId',
        driverId
      );
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'driverChargesCreateDriverExpenses',
        'organisationId',
        organisationId
      );
      // verify required parameter 'createDriverExpensesRequestBody' is not null or undefined
      assertParamExists(
        'driverChargesCreateDriverExpenses',
        'createDriverExpensesRequestBody',
        createDriverExpensesRequestBody
      );
      const localVarPath =
        `/drivers/{driverId}/organisations/{organisationId}/expenses`
          .replace(`{${'driverId'}}`, encodeURIComponent(String(driverId)))
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createDriverExpensesRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve charges for a driver
     * @summary Retrieve many Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesRetrieveMany: async (
      from: string,
      to: string,
      organisationId: string,
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('driverChargesRetrieveMany', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('driverChargesRetrieveMany', 'to', to);
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'driverChargesRetrieveMany',
        'organisationId',
        organisationId
      );
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('driverChargesRetrieveMany', 'userId', userId);
      const localVarPath =
        `/organisations/{organisationId}/drivers/{userId}/charges`
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          )
          .replace(`{${'userId'}}`, encodeURIComponent(String(userId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve charge statistics for given organisation drivers
     * @summary Retrieve organisation drivers statistics Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated drivers have taken place
     * @param {RetrieveOrganisationDriversStatisticsRequestBody} retrieveOrganisationDriversStatisticsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesRetrieveOrganisationDriversStatistics: async (
      from: string,
      to: string,
      organisationId: string,
      retrieveOrganisationDriversStatisticsRequestBody: RetrieveOrganisationDriversStatisticsRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'driverChargesRetrieveOrganisationDriversStatistics',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists(
        'driverChargesRetrieveOrganisationDriversStatistics',
        'to',
        to
      );
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'driverChargesRetrieveOrganisationDriversStatistics',
        'organisationId',
        organisationId
      );
      // verify required parameter 'retrieveOrganisationDriversStatisticsRequestBody' is not null or undefined
      assertParamExists(
        'driverChargesRetrieveOrganisationDriversStatistics',
        'retrieveOrganisationDriversStatisticsRequestBody',
        retrieveOrganisationDriversStatisticsRequestBody
      );
      const localVarPath =
        `/organisations/{organisationId}/drivers/stats`.replace(
          `{${'organisationId'}}`,
          encodeURIComponent(String(organisationId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        retrieveOrganisationDriversStatisticsRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submit driver expenses Driver charges
     * @param {string} driverId UUID of the driver.
     * @param {number} organisationId
     * @param {SubmitDriverExpensesRequestBody} submitDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesSubmitDriverExpenses: async (
      driverId: string,
      organisationId: number,
      submitDriverExpensesRequestBody: SubmitDriverExpensesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'driverChargesSubmitDriverExpenses',
        'driverId',
        driverId
      );
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'driverChargesSubmitDriverExpenses',
        'organisationId',
        organisationId
      );
      // verify required parameter 'submitDriverExpensesRequestBody' is not null or undefined
      assertParamExists(
        'driverChargesSubmitDriverExpenses',
        'submitDriverExpensesRequestBody',
        submitDriverExpensesRequestBody
      );
      const localVarPath =
        `/drivers/{driverId}/groups/{organisationId}/expenses`
          .replace(`{${'driverId'}}`, encodeURIComponent(String(driverId)))
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        submitDriverExpensesRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DriverChargesApi - functional programming interface
 * @export
 */
export const DriverChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    DriverChargesApiAxiosParamCreator(configuration);
  return {
    /**
     * Submitting a list of charges as expenses for a driver within an organisation.
     * @summary Create driver expenses Driver charges
     * @param {number} driverId
     * @param {number} organisationId
     * @param {CreateDriverExpensesRequestBody} createDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesCreateDriverExpenses(
      driverId: number,
      organisationId: number,
      createDriverExpensesRequestBody: CreateDriverExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreatedExpenseResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesCreateDriverExpenses(
          driverId,
          organisationId,
          createDriverExpensesRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverChargesApi.driverChargesCreateDriverExpenses'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve charges for a driver
     * @summary Retrieve many Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesRetrieveMany(
      from: string,
      to: string,
      organisationId: string,
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DriverChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesRetrieveMany(
          from,
          to,
          organisationId,
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriverChargesApi.driverChargesRetrieveMany']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve charge statistics for given organisation drivers
     * @summary Retrieve organisation drivers statistics Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated drivers have taken place
     * @param {RetrieveOrganisationDriversStatisticsRequestBody} retrieveOrganisationDriversStatisticsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesRetrieveOrganisationDriversStatistics(
      from: string,
      to: string,
      organisationId: string,
      retrieveOrganisationDriversStatisticsRequestBody: RetrieveOrganisationDriversStatisticsRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<OrganisationDriversChargeStatisticsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesRetrieveOrganisationDriversStatistics(
          from,
          to,
          organisationId,
          retrieveOrganisationDriversStatisticsRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverChargesApi.driverChargesRetrieveOrganisationDriversStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submit driver expenses Driver charges
     * @param {string} driverId UUID of the driver.
     * @param {number} organisationId
     * @param {SubmitDriverExpensesRequestBody} submitDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driverChargesSubmitDriverExpenses(
      driverId: string,
      organisationId: number,
      submitDriverExpensesRequestBody: SubmitDriverExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubmittedExpenseResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driverChargesSubmitDriverExpenses(
          driverId,
          organisationId,
          submitDriverExpensesRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverChargesApi.driverChargesSubmitDriverExpenses'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DriverChargesApi - factory interface
 * @export
 */
export const DriverChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DriverChargesApiFp(configuration);
  return {
    /**
     * Submitting a list of charges as expenses for a driver within an organisation.
     * @summary Create driver expenses Driver charges
     * @param {number} driverId
     * @param {number} organisationId
     * @param {CreateDriverExpensesRequestBody} createDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesCreateDriverExpenses(
      driverId: number,
      organisationId: number,
      createDriverExpensesRequestBody: CreateDriverExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreatedExpenseResponse> {
      return localVarFp
        .driverChargesCreateDriverExpenses(
          driverId,
          organisationId,
          createDriverExpensesRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve charges for a driver
     * @summary Retrieve many Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesRetrieveMany(
      from: string,
      to: string,
      organisationId: string,
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DriverChargesResponse> {
      return localVarFp
        .driverChargesRetrieveMany(from, to, organisationId, userId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve charge statistics for given organisation drivers
     * @summary Retrieve organisation drivers statistics Driver charges
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {string} organisationId The UUID of the group within which charges for the associated drivers have taken place
     * @param {RetrieveOrganisationDriversStatisticsRequestBody} retrieveOrganisationDriversStatisticsRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesRetrieveOrganisationDriversStatistics(
      from: string,
      to: string,
      organisationId: string,
      retrieveOrganisationDriversStatisticsRequestBody: RetrieveOrganisationDriversStatisticsRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OrganisationDriversChargeStatisticsResponse> {
      return localVarFp
        .driverChargesRetrieveOrganisationDriversStatistics(
          from,
          to,
          organisationId,
          retrieveOrganisationDriversStatisticsRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submit driver expenses Driver charges
     * @param {string} driverId UUID of the driver.
     * @param {number} organisationId
     * @param {SubmitDriverExpensesRequestBody} submitDriverExpensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driverChargesSubmitDriverExpenses(
      driverId: string,
      organisationId: number,
      submitDriverExpensesRequestBody: SubmitDriverExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubmittedExpenseResponse> {
      return localVarFp
        .driverChargesSubmitDriverExpenses(
          driverId,
          organisationId,
          submitDriverExpensesRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DriverChargesApi - object-oriented interface
 * @export
 * @class DriverChargesApi
 * @extends {BaseAPI}
 */
export class DriverChargesApi extends BaseAPI {
  /**
   * Submitting a list of charges as expenses for a driver within an organisation.
   * @summary Create driver expenses Driver charges
   * @param {number} driverId
   * @param {number} organisationId
   * @param {CreateDriverExpensesRequestBody} createDriverExpensesRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesCreateDriverExpenses(
    driverId: number,
    organisationId: number,
    createDriverExpensesRequestBody: CreateDriverExpensesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesCreateDriverExpenses(
        driverId,
        organisationId,
        createDriverExpensesRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve charges for a driver
   * @summary Retrieve many Driver charges
   * @param {string} from Query param filter from charge endsAt datetime
   * @param {string} to Query param filter to charge endsAt datetime
   * @param {string} organisationId The UUID of the group within which charges for the associated user have taken place
   * @param {string} userId The UUID of the user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesRetrieveMany(
    from: string,
    to: string,
    organisationId: string,
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesRetrieveMany(from, to, organisationId, userId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve charge statistics for given organisation drivers
   * @summary Retrieve organisation drivers statistics Driver charges
   * @param {string} from Query param filter from charge endsAt datetime
   * @param {string} to Query param filter to charge endsAt datetime
   * @param {string} organisationId The UUID of the group within which charges for the associated drivers have taken place
   * @param {RetrieveOrganisationDriversStatisticsRequestBody} retrieveOrganisationDriversStatisticsRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesRetrieveOrganisationDriversStatistics(
    from: string,
    to: string,
    organisationId: string,
    retrieveOrganisationDriversStatisticsRequestBody: RetrieveOrganisationDriversStatisticsRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesRetrieveOrganisationDriversStatistics(
        from,
        to,
        organisationId,
        retrieveOrganisationDriversStatisticsRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Submitting a list of charges as expenses for a driver within a group.
   * @summary Submit driver expenses Driver charges
   * @param {string} driverId UUID of the driver.
   * @param {number} organisationId
   * @param {SubmitDriverExpensesRequestBody} submitDriverExpensesRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverChargesApi
   */
  public driverChargesSubmitDriverExpenses(
    driverId: string,
    organisationId: number,
    submitDriverExpensesRequestBody: SubmitDriverExpensesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return DriverChargesApiFp(this.configuration)
      .driverChargesSubmitDriverExpenses(
        driverId,
        organisationId,
        submitDriverExpensesRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DriversApi - axios parameter creator
 * @export
 */
export const DriversApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve charge details.
     * @summary retrieveCharge drivers
     * @param {string} driverId ID for the driver
     * @param {string} chargeId Charge ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveCharge: async (
      driverId: string,
      chargeId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists('driversRetrieveCharge', 'driverId', driverId);
      // verify required parameter 'chargeId' is not null or undefined
      assertParamExists('driversRetrieveCharge', 'chargeId', chargeId);
      const localVarPath = `/drivers/{driverId}/charges/{chargeId}`
        .replace(`{${'driverId'}}`, encodeURIComponent(String(driverId)))
        .replace(`{${'chargeId'}}`, encodeURIComponent(String(chargeId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveCharges drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveCharges: async (
      from: string,
      to: string,
      driverId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('driversRetrieveCharges', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('driversRetrieveCharges', 'to', to);
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists('driversRetrieveCharges', 'driverId', driverId);
      const localVarPath = `/drivers/{driverId}/charges`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveStats drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {DriversRetrieveStatsIntervalEnum} [interval] Time duration interval data should be provided in.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveStats: async (
      from: string,
      to: string,
      driverId: string,
      interval?: DriversRetrieveStatsIntervalEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('driversRetrieveStats', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('driversRetrieveStats', 'to', to);
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists('driversRetrieveStats', 'driverId', driverId);
      const localVarPath = `/drivers/{driverId}/stats`.replace(
        `{${'driverId'}}`,
        encodeURIComponent(String(driverId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      if (interval !== undefined) {
        localVarQueryParameter['interval'] = interval;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DriversApi - functional programming interface
 * @export
 */
export const DriversApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DriversApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve charge details.
     * @summary retrieveCharge drivers
     * @param {string} driverId ID for the driver
     * @param {string} chargeId Charge ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driversRetrieveCharge(
      driverId: string,
      chargeId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DriversChargeResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driversRetrieveCharge(
          driverId,
          chargeId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriversApi.driversRetrieveCharge']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveCharges drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driversRetrieveCharges(
      from: string,
      to: string,
      driverId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DriversChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driversRetrieveCharges(
          from,
          to,
          driverId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriversApi.driversRetrieveCharges']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveStats drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {DriversRetrieveStatsIntervalEnum} [interval] Time duration interval data should be provided in.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async driversRetrieveStats(
      from: string,
      to: string,
      driverId: string,
      interval?: DriversRetrieveStatsIntervalEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DriverStatsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.driversRetrieveStats(
          from,
          to,
          driverId,
          interval,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DriversApi.driversRetrieveStats']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DriversApi - factory interface
 * @export
 */
export const DriversApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DriversApiFp(configuration);
  return {
    /**
     * Retrieve charge details.
     * @summary retrieveCharge drivers
     * @param {string} driverId ID for the driver
     * @param {string} chargeId Charge ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveCharge(
      driverId: string,
      chargeId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DriversChargeResponse> {
      return localVarFp
        .driversRetrieveCharge(driverId, chargeId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveCharges drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveCharges(
      from: string,
      to: string,
      driverId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DriversChargesResponse> {
      return localVarFp
        .driversRetrieveCharges(from, to, driverId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
     * @summary retrieveStats drivers
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {string} driverId UUID of the driver
     * @param {DriversRetrieveStatsIntervalEnum} [interval] Time duration interval data should be provided in.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    driversRetrieveStats(
      from: string,
      to: string,
      driverId: string,
      interval?: DriversRetrieveStatsIntervalEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DriverStatsResponse> {
      return localVarFp
        .driversRetrieveStats(from, to, driverId, interval, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DriversApi - object-oriented interface
 * @export
 * @class DriversApi
 * @extends {BaseAPI}
 */
export class DriversApi extends BaseAPI {
  /**
   * Retrieve charge details.
   * @summary retrieveCharge drivers
   * @param {string} driverId ID for the driver
   * @param {string} chargeId Charge ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriversApi
   */
  public driversRetrieveCharge(
    driverId: string,
    chargeId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriversApiFp(this.configuration)
      .driversRetrieveCharge(driverId, chargeId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
   * @summary retrieveCharges drivers
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {string} driverId UUID of the driver
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriversApi
   */
  public driversRetrieveCharges(
    from: string,
    to: string,
    driverId: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriversApiFp(this.configuration)
      .driversRetrieveCharges(from, to, driverId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn\'t present.
   * @summary retrieveStats drivers
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {string} driverId UUID of the driver
   * @param {DriversRetrieveStatsIntervalEnum} [interval] Time duration interval data should be provided in.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriversApi
   */
  public driversRetrieveStats(
    from: string,
    to: string,
    driverId: string,
    interval?: DriversRetrieveStatsIntervalEnum,
    options?: RawAxiosRequestConfig
  ) {
    return DriversApiFp(this.configuration)
      .driversRetrieveStats(from, to, driverId, interval, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const DriversRetrieveStatsIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type DriversRetrieveStatsIntervalEnum =
  (typeof DriversRetrieveStatsIntervalEnum)[keyof typeof DriversRetrieveStatsIntervalEnum];

/**
 * LinkUserApi - axios parameter creator
 * @export
 */
export const LinkUserApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Link user to home charger Link user
     * @param {string} ppId PPID (PSL number) of a charger
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkUserLinkUserToHomeCharger: async (
      ppId: string,
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppId' is not null or undefined
      assertParamExists('linkUserLinkUserToHomeCharger', 'ppId', ppId);
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('linkUserLinkUserToHomeCharger', 'userId', userId);
      const localVarPath = `/link-user/{userId}/charger/{ppId}`
        .replace(`{${'ppId'}}`, encodeURIComponent(String(ppId)))
        .replace(`{${'userId'}}`, encodeURIComponent(String(userId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * LinkUserApi - functional programming interface
 * @export
 */
export const LinkUserApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = LinkUserApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Link user to home charger Link user
     * @param {string} ppId PPID (PSL number) of a charger
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async linkUserLinkUserToHomeCharger(
      ppId: string,
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.linkUserLinkUserToHomeCharger(
          ppId,
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['LinkUserApi.linkUserLinkUserToHomeCharger']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * LinkUserApi - factory interface
 * @export
 */
export const LinkUserApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = LinkUserApiFp(configuration);
  return {
    /**
     *
     * @summary Link user to home charger Link user
     * @param {string} ppId PPID (PSL number) of a charger
     * @param {string} userId The UUID of the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkUserLinkUserToHomeCharger(
      ppId: string,
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .linkUserLinkUserToHomeCharger(ppId, userId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * LinkUserApi - object-oriented interface
 * @export
 * @class LinkUserApi
 * @extends {BaseAPI}
 */
export class LinkUserApi extends BaseAPI {
  /**
   *
   * @summary Link user to home charger Link user
   * @param {string} ppId PPID (PSL number) of a charger
   * @param {string} userId The UUID of the user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LinkUserApi
   */
  public linkUserLinkUserToHomeCharger(
    ppId: string,
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return LinkUserApiFp(this.configuration)
      .linkUserLinkUserToHomeCharger(ppId, userId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * OrganisationChargesApi - axios parameter creator
 * @export
 */
export const OrganisationChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month).
     * @summary Expenses by organisation Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesExpensesByOrganisation: async (
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationStatusEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'organisationChargesExpensesByOrganisation',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists('organisationChargesExpensesByOrganisation', 'to', to);
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'organisationChargesExpensesByOrganisation',
        'organisationId',
        organisationId
      );
      const localVarPath =
        `/organisations/{organisationId}/submitted-charges`.replace(
          `{${'organisationId'}}`,
          encodeURIComponent(String(organisationId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (status !== undefined) {
        localVarQueryParameter['status'] = status;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.
     * @summary Expenses by organisation, grouped by driver Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesExpensesByOrganisationGroupedByDriver: async (
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists(
        'organisationChargesExpensesByOrganisationGroupedByDriver',
        'from',
        from
      );
      // verify required parameter 'to' is not null or undefined
      assertParamExists(
        'organisationChargesExpensesByOrganisationGroupedByDriver',
        'to',
        to
      );
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'organisationChargesExpensesByOrganisationGroupedByDriver',
        'organisationId',
        organisationId
      );
      const localVarPath =
        `/organisations/{organisationId}/submitted-charges/drivers`.replace(
          `{${'organisationId'}}`,
          encodeURIComponent(String(organisationId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (status !== undefined) {
        localVarQueryParameter['status'] = status;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.
     * @summary Fleet usage by organisation Organisation charges
     * @param {string} organisationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesFleetUsageByOrganisation: async (
      organisationId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'organisationChargesFleetUsageByOrganisation',
        'organisationId',
        organisationId
      );
      const localVarPath =
        `/organisations/{organisationId}/fleet-usage`.replace(
          `{${'organisationId'}}`,
          encodeURIComponent(String(organisationId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Mark submitted charges as processed
     * @summary Mark submitted charges as processed Organisation charges
     * @param {string} organisationId
     * @param {MarkSubmittedChargesAsProcessedRequestBody} markSubmittedChargesAsProcessedRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesMarkSubmittedChargesAsProcessed: async (
      organisationId: string,
      markSubmittedChargesAsProcessedRequestBody: MarkSubmittedChargesAsProcessedRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'organisationChargesMarkSubmittedChargesAsProcessed',
        'organisationId',
        organisationId
      );
      // verify required parameter 'markSubmittedChargesAsProcessedRequestBody' is not null or undefined
      assertParamExists(
        'organisationChargesMarkSubmittedChargesAsProcessed',
        'markSubmittedChargesAsProcessedRequestBody',
        markSubmittedChargesAsProcessedRequestBody
      );
      const localVarPath =
        `/organisations/{organisationId}/process-charges`.replace(
          `{${'organisationId'}}`,
          encodeURIComponent(String(organisationId))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        markSubmittedChargesAsProcessedRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve submitted charges for a driver
     * @summary Submitted charges for driver Organisation charges
     * @param {string} organisationId
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesSubmittedChargesForDriver: async (
      organisationId: string,
      driverId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'organisationChargesSubmittedChargesForDriver',
        'organisationId',
        organisationId
      );
      // verify required parameter 'driverId' is not null or undefined
      assertParamExists(
        'organisationChargesSubmittedChargesForDriver',
        'driverId',
        driverId
      );
      const localVarPath =
        `/organisations/{organisationId}/submitted-charges/{driverId}`
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          )
          .replace(`{${'driverId'}}`, encodeURIComponent(String(driverId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * OrganisationChargesApi - functional programming interface
 * @export
 */
export const OrganisationChargesApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    OrganisationChargesApiAxiosParamCreator(configuration);
  return {
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month).
     * @summary Expenses by organisation Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async organisationChargesExpensesByOrganisation(
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationStatusEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<OrganisationChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.organisationChargesExpensesByOrganisation(
          from,
          to,
          organisationId,
          status,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OrganisationChargesApi.organisationChargesExpensesByOrganisation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.
     * @summary Expenses by organisation, grouped by driver Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async organisationChargesExpensesByOrganisationGroupedByDriver(
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<OrganisationChargesDriverSummaryResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.organisationChargesExpensesByOrganisationGroupedByDriver(
          from,
          to,
          organisationId,
          status,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OrganisationChargesApi.organisationChargesExpensesByOrganisationGroupedByDriver'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.
     * @summary Fleet usage by organisation Organisation charges
     * @param {string} organisationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async organisationChargesFleetUsageByOrganisation(
      organisationId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FleetUsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.organisationChargesFleetUsageByOrganisation(
          organisationId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OrganisationChargesApi.organisationChargesFleetUsageByOrganisation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Mark submitted charges as processed
     * @summary Mark submitted charges as processed Organisation charges
     * @param {string} organisationId
     * @param {MarkSubmittedChargesAsProcessedRequestBody} markSubmittedChargesAsProcessedRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async organisationChargesMarkSubmittedChargesAsProcessed(
      organisationId: string,
      markSubmittedChargesAsProcessedRequestBody: MarkSubmittedChargesAsProcessedRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.organisationChargesMarkSubmittedChargesAsProcessed(
          organisationId,
          markSubmittedChargesAsProcessedRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OrganisationChargesApi.organisationChargesMarkSubmittedChargesAsProcessed'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve submitted charges for a driver
     * @summary Submitted charges for driver Organisation charges
     * @param {string} organisationId
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async organisationChargesSubmittedChargesForDriver(
      organisationId: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubmittedChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.organisationChargesSubmittedChargesForDriver(
          organisationId,
          driverId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'OrganisationChargesApi.organisationChargesSubmittedChargesForDriver'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * OrganisationChargesApi - factory interface
 * @export
 */
export const OrganisationChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = OrganisationChargesApiFp(configuration);
  return {
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month).
     * @summary Expenses by organisation Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesExpensesByOrganisation(
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationStatusEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OrganisationChargesResponse> {
      return localVarFp
        .organisationChargesExpensesByOrganisation(
          from,
          to,
          organisationId,
          status,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.
     * @summary Expenses by organisation, grouped by driver Organisation charges
     * @param {string} from Charges expensed from and including this date will be returned.
     * @param {string} to Charges expensed up to and not including this date will be returned.
     * @param {string} organisationId
     * @param {OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesExpensesByOrganisationGroupedByDriver(
      from: string,
      to: string,
      organisationId: string,
      status?: OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OrganisationChargesDriverSummaryResponse> {
      return localVarFp
        .organisationChargesExpensesByOrganisationGroupedByDriver(
          from,
          to,
          organisationId,
          status,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.
     * @summary Fleet usage by organisation Organisation charges
     * @param {string} organisationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesFleetUsageByOrganisation(
      organisationId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FleetUsageResponse> {
      return localVarFp
        .organisationChargesFleetUsageByOrganisation(organisationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Mark submitted charges as processed
     * @summary Mark submitted charges as processed Organisation charges
     * @param {string} organisationId
     * @param {MarkSubmittedChargesAsProcessedRequestBody} markSubmittedChargesAsProcessedRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesMarkSubmittedChargesAsProcessed(
      organisationId: string,
      markSubmittedChargesAsProcessedRequestBody: MarkSubmittedChargesAsProcessedRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .organisationChargesMarkSubmittedChargesAsProcessed(
          organisationId,
          markSubmittedChargesAsProcessedRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve submitted charges for a driver
     * @summary Submitted charges for driver Organisation charges
     * @param {string} organisationId
     * @param {number} driverId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    organisationChargesSubmittedChargesForDriver(
      organisationId: string,
      driverId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubmittedChargesResponse> {
      return localVarFp
        .organisationChargesSubmittedChargesForDriver(
          organisationId,
          driverId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * OrganisationChargesApi - object-oriented interface
 * @export
 * @class OrganisationChargesApi
 * @extends {BaseAPI}
 */
export class OrganisationChargesApi extends BaseAPI {
  /**
   * Get all expensable charges per organisation between two dates (maximum 1 month).
   * @summary Expenses by organisation Organisation charges
   * @param {string} from Charges expensed from and including this date will be returned.
   * @param {string} to Charges expensed up to and not including this date will be returned.
   * @param {string} organisationId
   * @param {OrganisationChargesExpensesByOrganisationStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OrganisationChargesApi
   */
  public organisationChargesExpensesByOrganisation(
    from: string,
    to: string,
    organisationId: string,
    status?: OrganisationChargesExpensesByOrganisationStatusEnum,
    options?: RawAxiosRequestConfig
  ) {
    return OrganisationChargesApiFp(this.configuration)
      .organisationChargesExpensesByOrganisation(
        from,
        to,
        organisationId,
        status,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.
   * @summary Expenses by organisation, grouped by driver Organisation charges
   * @param {string} from Charges expensed from and including this date will be returned.
   * @param {string} to Charges expensed up to and not including this date will be returned.
   * @param {string} organisationId
   * @param {OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum} [status] Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OrganisationChargesApi
   */
  public organisationChargesExpensesByOrganisationGroupedByDriver(
    from: string,
    to: string,
    organisationId: string,
    status?: OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum,
    options?: RawAxiosRequestConfig
  ) {
    return OrganisationChargesApiFp(this.configuration)
      .organisationChargesExpensesByOrganisationGroupedByDriver(
        from,
        to,
        organisationId,
        status,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.
   * @summary Fleet usage by organisation Organisation charges
   * @param {string} organisationId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OrganisationChargesApi
   */
  public organisationChargesFleetUsageByOrganisation(
    organisationId: string,
    options?: RawAxiosRequestConfig
  ) {
    return OrganisationChargesApiFp(this.configuration)
      .organisationChargesFleetUsageByOrganisation(organisationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Mark submitted charges as processed
   * @summary Mark submitted charges as processed Organisation charges
   * @param {string} organisationId
   * @param {MarkSubmittedChargesAsProcessedRequestBody} markSubmittedChargesAsProcessedRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OrganisationChargesApi
   */
  public organisationChargesMarkSubmittedChargesAsProcessed(
    organisationId: string,
    markSubmittedChargesAsProcessedRequestBody: MarkSubmittedChargesAsProcessedRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return OrganisationChargesApiFp(this.configuration)
      .organisationChargesMarkSubmittedChargesAsProcessed(
        organisationId,
        markSubmittedChargesAsProcessedRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve submitted charges for a driver
   * @summary Submitted charges for driver Organisation charges
   * @param {string} organisationId
   * @param {number} driverId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof OrganisationChargesApi
   */
  public organisationChargesSubmittedChargesForDriver(
    organisationId: string,
    driverId: number,
    options?: RawAxiosRequestConfig
  ) {
    return OrganisationChargesApiFp(this.configuration)
      .organisationChargesSubmittedChargesForDriver(
        organisationId,
        driverId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const OrganisationChargesExpensesByOrganisationStatusEnum = {
  New: 'NEW',
  Processed: 'PROCESSED',
} as const;
export type OrganisationChargesExpensesByOrganisationStatusEnum =
  (typeof OrganisationChargesExpensesByOrganisationStatusEnum)[keyof typeof OrganisationChargesExpensesByOrganisationStatusEnum];
/**
 * @export
 */
export const OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum =
  {
    New: 'NEW',
    Processed: 'PROCESSED',
  } as const;
export type OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum =
  (typeof OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum)[keyof typeof OrganisationChargesExpensesByOrganisationGroupedByDriverStatusEnum];

/**
 * ProjectionChargesApi - axios parameter creator
 * @export
 */
export const ProjectionChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.
     * @summary Projection Charge Data Projection Charges
     * @param {string} from Query param filter from charge unpluggedAt datetime. The from field is inclusive.
     * @param {string} to Query param filter to charge unpluggedAt datetime. The to field is exclusive.
     * @param {string} [groupId] UUID of the group.
     * @param {string} [siteId] UUID of the site.
     * @param {string} [chargerId] Id of the charger.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    projectionChargesProjectionChargeData: async (
      from: string,
      to: string,
      groupId?: string,
      siteId?: string,
      chargerId?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('projectionChargesProjectionChargeData', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('projectionChargesProjectionChargeData', 'to', to);
      const localVarPath = `/charges`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (groupId !== undefined) {
        localVarQueryParameter['groupId'] = groupId;
      }

      if (siteId !== undefined) {
        localVarQueryParameter['siteId'] = siteId;
      }

      if (chargerId !== undefined) {
        localVarQueryParameter['chargerId'] = chargerId;
      }

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ProjectionChargesApi - functional programming interface
 * @export
 */
export const ProjectionChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ProjectionChargesApiAxiosParamCreator(configuration);
  return {
    /**
     * All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.
     * @summary Projection Charge Data Projection Charges
     * @param {string} from Query param filter from charge unpluggedAt datetime. The from field is inclusive.
     * @param {string} to Query param filter to charge unpluggedAt datetime. The to field is exclusive.
     * @param {string} [groupId] UUID of the group.
     * @param {string} [siteId] UUID of the site.
     * @param {string} [chargerId] Id of the charger.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async projectionChargesProjectionChargeData(
      from: string,
      to: string,
      groupId?: string,
      siteId?: string,
      chargerId?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProjectionChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.projectionChargesProjectionChargeData(
          from,
          to,
          groupId,
          siteId,
          chargerId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ProjectionChargesApi.projectionChargesProjectionChargeData'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ProjectionChargesApi - factory interface
 * @export
 */
export const ProjectionChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ProjectionChargesApiFp(configuration);
  return {
    /**
     * All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.
     * @summary Projection Charge Data Projection Charges
     * @param {string} from Query param filter from charge unpluggedAt datetime. The from field is inclusive.
     * @param {string} to Query param filter to charge unpluggedAt datetime. The to field is exclusive.
     * @param {string} [groupId] UUID of the group.
     * @param {string} [siteId] UUID of the site.
     * @param {string} [chargerId] Id of the charger.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    projectionChargesProjectionChargeData(
      from: string,
      to: string,
      groupId?: string,
      siteId?: string,
      chargerId?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProjectionChargesResponse> {
      return localVarFp
        .projectionChargesProjectionChargeData(
          from,
          to,
          groupId,
          siteId,
          chargerId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ProjectionChargesApi - object-oriented interface
 * @export
 * @class ProjectionChargesApi
 * @extends {BaseAPI}
 */
export class ProjectionChargesApi extends BaseAPI {
  /**
   * All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.
   * @summary Projection Charge Data Projection Charges
   * @param {string} from Query param filter from charge unpluggedAt datetime. The from field is inclusive.
   * @param {string} to Query param filter to charge unpluggedAt datetime. The to field is exclusive.
   * @param {string} [groupId] UUID of the group.
   * @param {string} [siteId] UUID of the site.
   * @param {string} [chargerId] Id of the charger.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProjectionChargesApi
   */
  public projectionChargesProjectionChargeData(
    from: string,
    to: string,
    groupId?: string,
    siteId?: string,
    chargerId?: string,
    options?: RawAxiosRequestConfig
  ) {
    return ProjectionChargesApiFp(this.configuration)
      .projectionChargesProjectionChargeData(
        from,
        to,
        groupId,
        siteId,
        chargerId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ProjectionGroupStatisticsApi - axios parameter creator
 * @export
 */
export const ProjectionGroupStatisticsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Charge statistics for all sites within a group for the given month.
     * @summary Group site statistics Projection Group Statistics
     * @param {string} groupId UUID of the group.
     * @param {number} year Year to be queried.
     * @param {number} month Month to be queried where Jan &#x3D; 1, Feb &#x3D; 2 etc...
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    projectionGroupStatisticsGroupSiteStatistics: async (
      groupId: string,
      year: number,
      month: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists(
        'projectionGroupStatisticsGroupSiteStatistics',
        'groupId',
        groupId
      );
      // verify required parameter 'year' is not null or undefined
      assertParamExists(
        'projectionGroupStatisticsGroupSiteStatistics',
        'year',
        year
      );
      // verify required parameter 'month' is not null or undefined
      assertParamExists(
        'projectionGroupStatisticsGroupSiteStatistics',
        'month',
        month
      );
      const localVarPath = `/charges/groups/{groupId}/sites`.replace(
        `{${'groupId'}}`,
        encodeURIComponent(String(groupId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (year !== undefined) {
        localVarQueryParameter['year'] = year;
      }

      if (month !== undefined) {
        localVarQueryParameter['month'] = month;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ProjectionGroupStatisticsApi - functional programming interface
 * @export
 */
export const ProjectionGroupStatisticsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    ProjectionGroupStatisticsApiAxiosParamCreator(configuration);
  return {
    /**
     * Charge statistics for all sites within a group for the given month.
     * @summary Group site statistics Projection Group Statistics
     * @param {string} groupId UUID of the group.
     * @param {number} year Year to be queried.
     * @param {number} month Month to be queried where Jan &#x3D; 1, Feb &#x3D; 2 etc...
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async projectionGroupStatisticsGroupSiteStatistics(
      groupId: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GroupSitesStatsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.projectionGroupStatisticsGroupSiteStatistics(
          groupId,
          year,
          month,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ProjectionGroupStatisticsApi.projectionGroupStatisticsGroupSiteStatistics'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ProjectionGroupStatisticsApi - factory interface
 * @export
 */
export const ProjectionGroupStatisticsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ProjectionGroupStatisticsApiFp(configuration);
  return {
    /**
     * Charge statistics for all sites within a group for the given month.
     * @summary Group site statistics Projection Group Statistics
     * @param {string} groupId UUID of the group.
     * @param {number} year Year to be queried.
     * @param {number} month Month to be queried where Jan &#x3D; 1, Feb &#x3D; 2 etc...
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    projectionGroupStatisticsGroupSiteStatistics(
      groupId: string,
      year: number,
      month: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GroupSitesStatsResponse> {
      return localVarFp
        .projectionGroupStatisticsGroupSiteStatistics(
          groupId,
          year,
          month,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ProjectionGroupStatisticsApi - object-oriented interface
 * @export
 * @class ProjectionGroupStatisticsApi
 * @extends {BaseAPI}
 */
export class ProjectionGroupStatisticsApi extends BaseAPI {
  /**
   * Charge statistics for all sites within a group for the given month.
   * @summary Group site statistics Projection Group Statistics
   * @param {string} groupId UUID of the group.
   * @param {number} year Year to be queried.
   * @param {number} month Month to be queried where Jan &#x3D; 1, Feb &#x3D; 2 etc...
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ProjectionGroupStatisticsApi
   */
  public projectionGroupStatisticsGroupSiteStatistics(
    groupId: string,
    year: number,
    month: number,
    options?: RawAxiosRequestConfig
  ) {
    return ProjectionGroupStatisticsApiFp(this.configuration)
      .projectionGroupStatisticsGroupSiteStatistics(
        groupId,
        year,
        month,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SitesApi - axios parameter creator
 * @export
 */
export const SitesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve charge information by site.
     * @summary retrieveChargeStatsGroupedBySite sites
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesRetrieveChargeStatsGroupedBySite: async (
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('sitesRetrieveChargeStatsGroupedBySite', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('sitesRetrieveChargeStatsGroupedBySite', 'to', to);
      const localVarPath = `/sites`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SitesApi - functional programming interface
 * @export
 */
export const SitesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = SitesApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve charge information by site.
     * @summary retrieveChargeStatsGroupedBySite sites
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async sitesRetrieveChargeStatsGroupedBySite(
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SiteStatsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.sitesRetrieveChargeStatsGroupedBySite(
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SitesApi.sitesRetrieveChargeStatsGroupedBySite']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SitesApi - factory interface
 * @export
 */
export const SitesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SitesApiFp(configuration);
  return {
    /**
     * Retrieve charge information by site.
     * @summary retrieveChargeStatsGroupedBySite sites
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-31
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    sitesRetrieveChargeStatsGroupedBySite(
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SiteStatsResponse> {
      return localVarFp
        .sitesRetrieveChargeStatsGroupedBySite(from, to, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SitesApi - object-oriented interface
 * @export
 * @class SitesApi
 * @extends {BaseAPI}
 */
export class SitesApi extends BaseAPI {
  /**
   * Retrieve charge information by site.
   * @summary retrieveChargeStatsGroupedBySite sites
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-31
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SitesApi
   */
  public sitesRetrieveChargeStatsGroupedBySite(
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return SitesApiFp(this.configuration)
      .sitesRetrieveChargeStatsGroupedBySite(from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UsageApi - axios parameter creator
 * @export
 */
export const UsageApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Organisation usage between two inclusive dates
     * @summary Usage by organisation usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisation: async (
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationIntervalEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisation',
        'organisationId',
        organisationId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('usageUsageByOrganisation', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('usageUsageByOrganisation', 'to', to);
      // verify required parameter 'interval' is not null or undefined
      assertParamExists('usageUsageByOrganisation', 'interval', interval);
      const localVarPath = `/organisations/{organisationId}/stats`.replace(
        `{${'organisationId'}}`,
        encodeURIComponent(String(organisationId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      if (interval !== undefined) {
        localVarQueryParameter['interval'] = interval;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Charger usage between two inclusive dates
     * @summary Usage by organisation and charger usage
     * @param {string} organisationId UUID of the organisation
     * @param {number} locationId Primary key of the charger location from podadmin
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndChargerIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisationAndCharger: async (
      organisationId: string,
      locationId: number,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndChargerIntervalEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisationAndCharger',
        'organisationId',
        organisationId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisationAndCharger',
        'locationId',
        locationId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('usageUsageByOrganisationAndCharger', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('usageUsageByOrganisationAndCharger', 'to', to);
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisationAndCharger',
        'interval',
        interval
      );
      const localVarPath =
        `/organisations/{organisationId}/chargers/{locationId}/stats`
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          )
          .replace(`{${'locationId'}}`, encodeURIComponent(String(locationId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      if (interval !== undefined) {
        localVarQueryParameter['interval'] = interval;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Site usage between two inclusive dates
     * @summary Usage by organisation and site usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndSiteIntervalEnum} interval Reporting interval
     * @param {number} siteId Site ID - primary key of the podadmin pod_addresses table
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisationAndSite: async (
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndSiteIntervalEnum,
      siteId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisationAndSite',
        'organisationId',
        organisationId
      );
      // verify required parameter 'from' is not null or undefined
      assertParamExists('usageUsageByOrganisationAndSite', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('usageUsageByOrganisationAndSite', 'to', to);
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'usageUsageByOrganisationAndSite',
        'interval',
        interval
      );
      // verify required parameter 'siteId' is not null or undefined
      assertParamExists('usageUsageByOrganisationAndSite', 'siteId', siteId);
      const localVarPath =
        `/organisations/{organisationId}/sites/{siteId}/stats`
          .replace(
            `{${'organisationId'}}`,
            encodeURIComponent(String(organisationId))
          )
          .replace(`{${'siteId'}}`, encodeURIComponent(String(siteId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      if (interval !== undefined) {
        localVarQueryParameter['interval'] = interval;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UsageApi - functional programming interface
 * @export
 */
export const UsageApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = UsageApiAxiosParamCreator(configuration);
  return {
    /**
     * Organisation usage between two inclusive dates
     * @summary Usage by organisation usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usageUsageByOrganisation(
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationIntervalEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usageUsageByOrganisation(
          organisationId,
          from,
          to,
          interval,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsageApi.usageUsageByOrganisation']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Charger usage between two inclusive dates
     * @summary Usage by organisation and charger usage
     * @param {string} organisationId UUID of the organisation
     * @param {number} locationId Primary key of the charger location from podadmin
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndChargerIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usageUsageByOrganisationAndCharger(
      organisationId: string,
      locationId: number,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndChargerIntervalEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usageUsageByOrganisationAndCharger(
          organisationId,
          locationId,
          from,
          to,
          interval,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsageApi.usageUsageByOrganisationAndCharger']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Site usage between two inclusive dates
     * @summary Usage by organisation and site usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndSiteIntervalEnum} interval Reporting interval
     * @param {number} siteId Site ID - primary key of the podadmin pod_addresses table
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usageUsageByOrganisationAndSite(
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndSiteIntervalEnum,
      siteId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UsageResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usageUsageByOrganisationAndSite(
          organisationId,
          from,
          to,
          interval,
          siteId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsageApi.usageUsageByOrganisationAndSite']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UsageApi - factory interface
 * @export
 */
export const UsageApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UsageApiFp(configuration);
  return {
    /**
     * Organisation usage between two inclusive dates
     * @summary Usage by organisation usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisation(
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationIntervalEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .usageUsageByOrganisation(organisationId, from, to, interval, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Charger usage between two inclusive dates
     * @summary Usage by organisation and charger usage
     * @param {string} organisationId UUID of the organisation
     * @param {number} locationId Primary key of the charger location from podadmin
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndChargerIntervalEnum} interval Reporting interval
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisationAndCharger(
      organisationId: string,
      locationId: number,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndChargerIntervalEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .usageUsageByOrganisationAndCharger(
          organisationId,
          locationId,
          from,
          to,
          interval,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Site usage between two inclusive dates
     * @summary Usage by organisation and site usage
     * @param {string} organisationId UUID of the organisation
     * @param {string} from
     * @param {string} to
     * @param {UsageUsageByOrganisationAndSiteIntervalEnum} interval Reporting interval
     * @param {number} siteId Site ID - primary key of the podadmin pod_addresses table
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usageUsageByOrganisationAndSite(
      organisationId: string,
      from: string,
      to: string,
      interval: UsageUsageByOrganisationAndSiteIntervalEnum,
      siteId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UsageResponse> {
      return localVarFp
        .usageUsageByOrganisationAndSite(
          organisationId,
          from,
          to,
          interval,
          siteId,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UsageApi - object-oriented interface
 * @export
 * @class UsageApi
 * @extends {BaseAPI}
 */
export class UsageApi extends BaseAPI {
  /**
   * Organisation usage between two inclusive dates
   * @summary Usage by organisation usage
   * @param {string} organisationId UUID of the organisation
   * @param {string} from
   * @param {string} to
   * @param {UsageUsageByOrganisationIntervalEnum} interval Reporting interval
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsageApi
   */
  public usageUsageByOrganisation(
    organisationId: string,
    from: string,
    to: string,
    interval: UsageUsageByOrganisationIntervalEnum,
    options?: RawAxiosRequestConfig
  ) {
    return UsageApiFp(this.configuration)
      .usageUsageByOrganisation(organisationId, from, to, interval, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Charger usage between two inclusive dates
   * @summary Usage by organisation and charger usage
   * @param {string} organisationId UUID of the organisation
   * @param {number} locationId Primary key of the charger location from podadmin
   * @param {string} from
   * @param {string} to
   * @param {UsageUsageByOrganisationAndChargerIntervalEnum} interval Reporting interval
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsageApi
   */
  public usageUsageByOrganisationAndCharger(
    organisationId: string,
    locationId: number,
    from: string,
    to: string,
    interval: UsageUsageByOrganisationAndChargerIntervalEnum,
    options?: RawAxiosRequestConfig
  ) {
    return UsageApiFp(this.configuration)
      .usageUsageByOrganisationAndCharger(
        organisationId,
        locationId,
        from,
        to,
        interval,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Site usage between two inclusive dates
   * @summary Usage by organisation and site usage
   * @param {string} organisationId UUID of the organisation
   * @param {string} from
   * @param {string} to
   * @param {UsageUsageByOrganisationAndSiteIntervalEnum} interval Reporting interval
   * @param {number} siteId Site ID - primary key of the podadmin pod_addresses table
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsageApi
   */
  public usageUsageByOrganisationAndSite(
    organisationId: string,
    from: string,
    to: string,
    interval: UsageUsageByOrganisationAndSiteIntervalEnum,
    siteId: number,
    options?: RawAxiosRequestConfig
  ) {
    return UsageApiFp(this.configuration)
      .usageUsageByOrganisationAndSite(
        organisationId,
        from,
        to,
        interval,
        siteId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const UsageUsageByOrganisationIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type UsageUsageByOrganisationIntervalEnum =
  (typeof UsageUsageByOrganisationIntervalEnum)[keyof typeof UsageUsageByOrganisationIntervalEnum];
/**
 * @export
 */
export const UsageUsageByOrganisationAndChargerIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type UsageUsageByOrganisationAndChargerIntervalEnum =
  (typeof UsageUsageByOrganisationAndChargerIntervalEnum)[keyof typeof UsageUsageByOrganisationAndChargerIntervalEnum];
/**
 * @export
 */
export const UsageUsageByOrganisationAndSiteIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type UsageUsageByOrganisationAndSiteIntervalEnum =
  (typeof UsageUsageByOrganisationAndSiteIntervalEnum)[keyof typeof UsageUsageByOrganisationAndSiteIntervalEnum];

/**
 * UserChargesApi - axios parameter creator
 * @export
 */
export const UserChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges
     * @summary Group And User Charges User Charges
     * @param {string} groupId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userChargesGroupAndUserCharges: async (
      groupId: string,
      userId: string,
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'groupId' is not null or undefined
      assertParamExists('userChargesGroupAndUserCharges', 'groupId', groupId);
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('userChargesGroupAndUserCharges', 'userId', userId);
      // verify required parameter 'from' is not null or undefined
      assertParamExists('userChargesGroupAndUserCharges', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('userChargesGroupAndUserCharges', 'to', to);
      const localVarPath = `/groups/{groupId}/users/{userId}/charges`
        .replace(`{${'groupId'}}`, encodeURIComponent(String(groupId)))
        .replace(`{${'userId'}}`, encodeURIComponent(String(userId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (from !== undefined) {
        localVarQueryParameter['from'] =
          (from as any) instanceof Date
            ? (from as any).toISOString().substring(0, 10)
            : from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] =
          (to as any) instanceof Date
            ? (to as any).toISOString().substring(0, 10)
            : to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UserChargesApi - functional programming interface
 * @export
 */
export const UserChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    UserChargesApiAxiosParamCreator(configuration);
  return {
    /**
     * Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges
     * @summary Group And User Charges User Charges
     * @param {string} groupId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async userChargesGroupAndUserCharges(
      groupId: string,
      userId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ProjectionGroupAndUserChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.userChargesGroupAndUserCharges(
          groupId,
          userId,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UserChargesApi.userChargesGroupAndUserCharges']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UserChargesApi - factory interface
 * @export
 */
export const UserChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UserChargesApiFp(configuration);
  return {
    /**
     * Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges
     * @summary Group And User Charges User Charges
     * @param {string} groupId The UUID of the group within which charges for the associated user have taken place
     * @param {string} userId The UUID of the user
     * @param {string} from Query param filter from charge endsAt datetime
     * @param {string} to Query param filter to charge endsAt datetime
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    userChargesGroupAndUserCharges(
      groupId: string,
      userId: string,
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ProjectionGroupAndUserChargesResponse> {
      return localVarFp
        .userChargesGroupAndUserCharges(groupId, userId, from, to, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UserChargesApi - object-oriented interface
 * @export
 * @class UserChargesApi
 * @extends {BaseAPI}
 */
export class UserChargesApi extends BaseAPI {
  /**
   * Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges
   * @summary Group And User Charges User Charges
   * @param {string} groupId The UUID of the group within which charges for the associated user have taken place
   * @param {string} userId The UUID of the user
   * @param {string} from Query param filter from charge endsAt datetime
   * @param {string} to Query param filter to charge endsAt datetime
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserChargesApi
   */
  public userChargesGroupAndUserCharges(
    groupId: string,
    userId: string,
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return UserChargesApiFp(this.configuration)
      .userChargesGroupAndUserCharges(groupId, userId, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
