import {
  SubmittedCharge,
  SubmittedChargeStatusEnum,
} from '@experience/shared/axios/data-platform-api-client';
import {
  TEST_HOME_LOCATION,
  TEST_LOCATION,
  TEST_PUBLIC_LOCATION,
} from './submitted-charge-location.interface';

export const TEST_SUBMITTED_PUBLIC_CHARGE: SubmittedCharge = {
  id: 1,
  startTime: '2022-09-16T14:58:00Z',
  endTime: '2022-09-16T15:23:00Z',
  submittedTime: '2022-09-16T16:58:00Z',
  chargeCost: 7012,
  energyUsage: 12.5,
  chargerName: 'Kate-Jane',
  location: TEST_PUBLIC_LOCATION,
  processedTime: '2022-09-16T18:58:00Z',
  processedByFullName: '<PERSON> Wick',
  duration: 1500,
  status: SubmittedChargeStatusEnum.Processed,
};

export const TEST_SUBMITTED_HOME_CHARGE: SubmittedCharge = {
  id: 2,
  startTime: '2022-08-09T01:58:00Z',
  endTime: '2022-08-09T02:56:00Z',
  submittedTime: '2022-10-09T08:01:00Z',
  chargeCost: 12332,
  energyUsage: 43.5,
  chargerName: 'PSL123456',
  location: TEST_HOME_LOCATION,
  processedTime: '2022-10-20T08:01:00Z',
  processedByFullName: 'Alan Smith',
  duration: 3480,
  status: SubmittedChargeStatusEnum.Processed,
};

export const TEST_NOT_PROCESSED_HOME_CHARGE: SubmittedCharge = {
  id: 3,
  startTime: '2022-11-09T01:58:00Z',
  endTime: '2022-11-09T02:56:00Z',
  submittedTime: '2022-11-09T08:01:00Z',
  chargeCost: 14654,
  energyUsage: 89.5,
  chargerName: 'PSL123456',
  location: TEST_HOME_LOCATION,
  duration: 3480,
  status: SubmittedChargeStatusEnum.New,
};

export const TEST_SUBMITTED_CHARGE: SubmittedCharge = {
  id: 1,
  startTime: '2022-09-16T14:58:00Z',
  endTime: '2022-09-16T15:23:00Z',
  submittedTime: '2022-09-16T16:58:00Z',
  chargeCost: 7012,
  energyUsage: 12.5,
  chargerName: 'Kate-Jane',
  location: TEST_LOCATION,
  processedTime: '2022-09-16T18:58:00Z',
  processedByFullName: 'John Wick',
  duration: 123,
  status: SubmittedChargeStatusEnum.Processed,
};
