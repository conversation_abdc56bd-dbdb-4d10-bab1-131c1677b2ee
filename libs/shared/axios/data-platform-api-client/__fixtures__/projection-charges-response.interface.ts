import { Charges2, ProjectionChargesResponse } from '../src';
import { Map } from 'immutable';

export const TEST_PROJECTION_CHARGES_RESPONSES: ProjectionChargesResponse = {
  data: [
    {
      chargeDurationTotal: 3300,
      chargerId: 'PSL-0001',
      chargerName: 'MART-SHOR',
      co2Avoided: 4325.62,
      confirmed: true,
      door: 'A',
      driverIDs: ['f3c55848-f8a0-4f70-8802-f46d5957b0ae'],
      endedAt: '2022-09-27 19:00:00.000',
      energyCost: 32,
      energyTotal: 567.89,
      generationEnergyTotal: 35.44,
      gridEnergyTotal: 532.45,
      id: 'a2de3e18-9aaa-4fbf-a39a-82b8f67098c7',
      pluggedInAt: '2022-09-27 18:00:00.000',
      revenueGenerated: 457600,
      siteName: 'Big site',
      startedAt: '2022-09-27 18:05:00.000',
      unpluggedAt: '2022-09-27 18:55:00.000',
    },
    {
      chargeDurationTotal: 3300,
      chargerId: 'PSL-0002',
      chargerName: 'JANE-LYNC',
      co2Avoided: 4325.62,
      confirmed: true,
      door: 'A',
      driverIDs: ['0357be96-1495-4951-8046-c2d59ba76c31'],
      endedAt: '2022-09-27 19:00:00.000',
      energyCost: 32,
      energyTotal: 567.89,
      generationEnergyTotal: 35.44,
      gridEnergyTotal: 532.45,
      id: '60dfc816-f2f8-4690-a3b2-c7511211d0dd',
      pluggedInAt: '2022-09-27 18:00:00.000',
      revenueGenerated: 457600,
      siteName: 'Big site',
      startedAt: '2022-09-27 18:05:00.000',
      unpluggedAt: '2022-09-27 18:55:00.000',
    },
    {
      chargeDurationTotal: 3300,
      chargerId: 'PSL-0003',
      chargerName: 'BOB-SEBASTIAN',
      co2Avoided: 4325.62,
      confirmed: true,
      door: 'A',
      driverIDs: ['0357be96-1495-4951-8046-c2d59ba76c31'],
      endedAt: '2022-09-27 19:00:00.000',
      energyCost: 32,
      energyTotal: 567.89,
      generationEnergyTotal: 35.44,
      gridEnergyTotal: 532.45,
      id: '9ac265c3-ef72-4b5c-98a1-cdd46d107416',
      pluggedInAt: '2022-09-27 18:00:00.000',
      revenueGenerated: 457600,
      siteName: 'Little site',
      startedAt: '2022-09-27 18:05:00.000',
      unpluggedAt: '2022-09-27 18:55:00.000',
    },
    {
      chargeDurationTotal: 3300,
      chargerId: 'PSL-0003',
      chargerName: 'BOB-SEBASTIAN',
      co2Avoided: 4325.62,
      confirmed: true,
      door: 'A',
      driverIDs: ['0357be96-1495-4951-8046-c2d59ba76c31'],
      endedAt: '2022-10-27 19:00:00.000',
      energyCost: 32,
      energyTotal: 567.89,
      generationEnergyTotal: 35.44,
      gridEnergyTotal: 532.45,
      id: 'de419cc6-3a9a-4cb2-a6c9-bce6d2dd6397',
      pluggedInAt: '2022-10-27 18:00:00.000',
      revenueGenerated: 457600,
      siteName: 'Little site',
      startedAt: '2022-10-27 18:05:00.000',
      unpluggedAt: '2022-10-27 18:55:00.000',
    },
  ],
  meta: { params: {} },
};

export const TEST_PROJECTION_CHARGES_RESPONSES_WITH_MATCHING_CHARGER_IDS: ProjectionChargesResponse =
  {
    data: [
      Map(TEST_PROJECTION_CHARGES_RESPONSES.data[0])
        .set('chargerId', 'PG-80435')
        .set('chargerName', 'Hank-Neil')
        .toObject() as unknown as Charges2,
      Map(TEST_PROJECTION_CHARGES_RESPONSES.data[1])
        .set('chargerId', 't53_it1_3717_004')
        .set('chargerName', 'Bill-Tara')
        .toObject() as unknown as Charges2,
      Map(TEST_PROJECTION_CHARGES_RESPONSES.data[2])
        .set('chargerId', 'PG-70500')
        .set('chargerName', 'Kyle-Dion')
        .toObject() as unknown as Charges2,
      Map(TEST_PROJECTION_CHARGES_RESPONSES.data[3])
        .set('chargerId', 'PG-86983')
        .set('chargerName', 'Zach-Jade')
        .toObject() as unknown as Charges2,
    ],
    meta: { params: {} },
  };
