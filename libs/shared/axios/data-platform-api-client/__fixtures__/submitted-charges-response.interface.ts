import { SubmittedChargesResponse } from '@experience/shared/axios/data-platform-api-client';
import { TEST_DRIVER, TEST_MEMBER } from './driver.interface';
import {
  TEST_DRIVER_TOTAL_COST,
  TEST_MEMBER_TOTAL_COST,
} from './total-cost.interface';
import { TEST_DRIVER_TOTAL_USAGE } from './total-usage.interface';
import {
  TEST_NOT_PROCESSED_HOME_CHARGE,
  TEST_SUBMITTED_HOME_CHARGE,
  TEST_SUBMITTED_PUBLIC_CHARGE,
} from './submitted-charge.interface';

export const TEST_DRIVER_EXPENSE: SubmittedChargesResponse = {
  driver: TEST_DRIVER,
  submittedCharges: [TEST_SUBMITTED_HOME_CHARGE, TEST_SUBMITTED_PUBLIC_CHARGE],
  totalCost: TEST_DRIVER_TOTAL_COST,
  totalUsage: TEST_DRIVER_TOTAL_USAGE,
};

export const TEST_MEMBER_EXPENSE: SubmittedChargesResponse = {
  driver: TEST_MEMBER,
  submittedCharges: [TEST_NOT_PROCESSED_HOME_CHARGE],
  totalCost: TEST_MEMBER_TOTAL_COST,
  totalUsage: TEST_MEMBER_TOTAL_COST,
};
