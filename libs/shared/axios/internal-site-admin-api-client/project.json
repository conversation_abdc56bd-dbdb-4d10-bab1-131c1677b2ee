{"name": "shared-axios-internal-site-admin-api-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/axios/internal-site-admin-api-client/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i openapi3.json -o src", "rm openapitools.json", "npx prettier . --write"]}}}, "tags": ["shared"]}