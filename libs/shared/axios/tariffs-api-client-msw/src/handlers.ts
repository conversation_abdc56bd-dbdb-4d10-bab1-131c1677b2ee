/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [await getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/suppliers`, async () => {
    const resultArray = [[await getGetSuppliers200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/charging-stations/:ppid/tariffs`, async () => {
    const resultArray = [
      [await getCreateTariffForChargingStation201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/charging-stations/:ppid/tariffs`, async () => {
    const resultArray = [
      [await getGetTariffsByPpid200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/charging-stations/:ppid/tariffs`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(
    `${baseURL}/charging-stations/:ppid/tariffs/:tariffId`,
    async () => {
      const resultArray = [[undefined, { status: 204 }]];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.put(`${baseURL}/charging-stations/:ppid/tariffs/:tariffId`, async () => {
    const resultArray = [[await getUpdateTariff200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getGetSuppliers200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.lorem.words(),
    name: faker.person.fullName(),
    timeZone: 'Europe/London',
    icon: faker.lorem.words(),
    defaultTariffInfo: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      start: '00:00:00',
      end: '00:00:00',
      price: 0.3,
      days: [
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY',
        'SUNDAY',
      ],
    })),
    defaultMaxChargePrice: 0.2,
  }));
}

export function getCreateTariffForChargingStation201Response() {
  return {
    ppid: faker.lorem.words(),
    supplierId: faker.lorem.words(),
    tariffInfo: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      start: '00:00:00',
      end: '00:00:00',
      price: 0.3,
      days: [
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY',
        'SUNDAY',
      ],
    })),
    timezone: 'Europe/London',
    maxChargePrice: 0.2,
    effectiveFrom: '2021-01-01',
    id: faker.string.uuid(),
    cheapestUnitPrice: 0.2,
  };
}

export function getGetTariffsByPpid200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ppid: faker.lorem.words(),
      supplierId: faker.lorem.words(),
      tariffInfo: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        start: '00:00:00',
        end: '00:00:00',
        price: 0.3,
        days: [
          'MONDAY',
          'TUESDAY',
          'WEDNESDAY',
          'THURSDAY',
          'FRIDAY',
          'SATURDAY',
          'SUNDAY',
        ],
      })),
      timezone: 'Europe/London',
      maxChargePrice: 0.2,
      effectiveFrom: '2021-01-01',
      id: faker.string.uuid(),
      cheapestUnitPrice: 0.2,
    })),
    metadata: {
      criteria: {
        ppid: faker.lorem.words(),
        effectiveFrom: '2024-08-25T20:05:46.646Z',
        effectiveTo: '2024-08-25T20:05:46.646Z',
      },
    },
  };
}

export function getUpdateTariff200Response() {
  return {
    ppid: faker.lorem.words(),
    supplierId: faker.lorem.words(),
    tariffInfo: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      start: '00:00:00',
      end: '00:00:00',
      price: 0.3,
      days: [
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY',
        'SUNDAY',
      ],
    })),
    timezone: 'Europe/London',
    maxChargePrice: 0.2,
    effectiveFrom: '2021-01-01',
    id: faker.string.uuid(),
    cheapestUnitPrice: 0.2,
  };
}
