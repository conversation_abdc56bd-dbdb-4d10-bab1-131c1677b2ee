/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.get(`${baseURL}/firmware/device`, async () => {
    const resultArray = [
      [await getGetCurrentFirmware200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/firmware/device/:serialNumber`, async () => {
    const resultArray = [
      [await getGetCurrentFirmwareSerialNumber200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/firmware/version/:pcbArchitecture`, async () => {
    const resultArray = [
      [await getFindFirmwareVersion201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/evses/:serialNumber/firmware/updates`, async () => {
    const resultArray = [
      [await getGetFirmwareUpdates200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/evses/:serialNumber/firmware/updates`, async () => {
    const resultArray = [
      [await getCreateFirmwareUpdate202Response(), { status: 202 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(
    `${baseURL}/evses/:serialNumber/firmware/updates/:updateId`,
    async () => {
      const resultArray = [
        [await getGetFirmwareUpdate200Response(), { status: 200 }],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.get(`${baseURL}/firmware/manifests`, async () => {
    const resultArray = [[await getGetManifests200Response(), { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/firmware/manifests`, async () => {
    const resultArray = [
      [await getCreateManifest201Response(), { status: 201 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/firmware/update-batches`, async () => {
    const resultArray = [
      [await getGetUpdateBatch200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/firmware/update-batches`, async () => {
    const resultArray = [
      [await getCreateUpdateBatch202Response(), { status: 202 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/firmware/update-batches/:batchId`, async () => {
    const resultArray = [
      [await getGetUpdateBatchById200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/serial-number-groups`, async () => {
    const resultArray = [
      [await getListSerialNumberGroups200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/serial-number-groups`, async () => {
    const resultArray = [
      [await getPostSerialNumberGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/serial-number-groups/:id`, async () => {
    const resultArray = [
      [await getGetSerialNumberGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/serial-number-groups/:id`, async () => {
    const resultArray = [
      [await getPatchSerialNumberGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(`${baseURL}/serial-number-groups/:id`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/serial-number-groups/:id/serial-numbers`, async () => {
    const resultArray = [
      [await getGetSerialNumbers200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/serial-number-groups/:id/serial-numbers`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.patch(`${baseURL}/serial-number-groups/:id/serial-numbers`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.delete(
    `${baseURL}/serial-number-groups/:id/serial-numbers`,
    async () => {
      const resultArray = [[undefined, { status: 200 }]];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/serial-number-groups/:id/trigger-update`, async () => {
    const resultArray = [
      [await getTriggerFirmwareUpdate200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.get(`${baseURL}/serial-number-groups/serial-numbers`, async () => {
    const resultArray = [
      [await getGetSerialNumbersByString200Response(), { status: 200 }],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getGetCurrentFirmware200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    serialNumber: '30303030',
    versionInfo: {
      manifestId: 'A50P-X.Y.Z-0000P',
      architecture: faker.helpers.arrayElement([
        'arch1',
        'arch2',
        'arch2.4',
        'arch3',
        'arch5',
      ]),
      details: faker.helpers.arrayElement([
        {
          dspVersion: '2.37.0-00005',
          wifiVersion: '1.14',
        },
        {
          rfidVersion: '1.1.1',
        },
        {},
      ]),
    },
    updateStatus: {
      isUpdateAvailable: true,
      updateVersion: {
        manifestId: 'A50P-X.Y.Z-0000P',
        architecture: faker.helpers.arrayElement([
          'arch1',
          'arch2',
          'arch2.4',
          'arch3',
          'arch5',
        ]),
        details: faker.helpers.arrayElement([
          {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          {
            rfidVersion: '1.1.1',
          },
          {},
        ]),
      },
      updateId: '5',
      status: 'IN_PROGRESS',
    },
  }));
}

export function getGetCurrentFirmwareSerialNumber200Response() {
  return {
    serialNumber: '30303030',
    versionInfo: {
      manifestId: 'A50P-X.Y.Z-0000P',
      architecture: faker.helpers.arrayElement([
        'arch1',
        'arch2',
        'arch2.4',
        'arch3',
        'arch5',
      ]),
      details: faker.helpers.arrayElement([
        {
          dspVersion: '2.37.0-00005',
          wifiVersion: '1.14',
        },
        {
          rfidVersion: '1.1.1',
        },
        {},
      ]),
    },
    updateStatus: {
      isUpdateAvailable: true,
      updateVersion: {
        manifestId: 'A50P-X.Y.Z-0000P',
        architecture: faker.helpers.arrayElement([
          'arch1',
          'arch2',
          'arch2.4',
          'arch3',
          'arch5',
        ]),
        details: faker.helpers.arrayElement([
          {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          {
            rfidVersion: '1.1.1',
          },
          {},
        ]),
      },
      updateId: '5',
      status: 'IN_PROGRESS',
    },
  };
}

export function getFindFirmwareVersion201Response() {
  return {
    manufacturing: {
      manifest: {
        manifestId: 'A50P-X.Y.Z-0000P',
      },
      url: faker.internet.url(),
    },
    ota: {
      manifest: {
        manifestId: 'A50P-X.Y.Z-0000P',
      },
      url: faker.internet.url(),
    },
  };
}

export function getGetFirmwareUpdates200Response() {
  return {
    updates: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      targetVersionInfo: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement([
              'arch1',
              'arch2',
              'arch2.4',
              'arch3',
            ]),
            details: {
              dspVersion: '2.37.0-00005',
              wifiVersion: '1.14',
            },
            createdDate: '2022-01-01T00:00:00.000Z',
          },
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement(['arch5']),
            createdDate: '2022-01-01T00:00:00.000Z',
            path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
          },
        ]),
      ]),
      sourceVersionInfo: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement([
              'arch1',
              'arch2',
              'arch2.4',
              'arch3',
            ]),
            details: {
              dspVersion: '2.37.0-00005',
              wifiVersion: '1.14',
            },
            createdDate: '2022-01-01T00:00:00.000Z',
          },
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement(['arch5']),
            createdDate: '2022-01-01T00:00:00.000Z',
            path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
          },
        ]),
      ]),
      currentStatus: faker.helpers.arrayElement([
        'QUEUED',
        'SCHEDULED',
        'IN_PROGRESS',
        'COMPLETED',
        'FAILED',
      ]),
      attempts: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.string.uuid(),
        pcbType: faker.helpers.arrayElement(['dsp', 'wifi']),
        status: faker.helpers.arrayElement([
          'AWAITING_PILOT_STATE_A',
          'PENDING',
          'STARTED',
          'INSTALLED',
          'FAILED',
          'SKIPPED',
        ]),
        sequence: faker.number.int(),
        startedClientTimestamp: faker.date.past(),
      })),
      requestedAt: faker.date.past(),
      scheduledDate: faker.date.past(),
      completedAt: faker.date.past(),
    })),
  };
}

export function getCreateFirmwareUpdate202Response() {
  return {
    id: faker.string.uuid(),
    targetVersionInfo: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ]),
    ]),
    sourceVersionInfo: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ]),
    ]),
    currentStatus: faker.helpers.arrayElement([
      'QUEUED',
      'SCHEDULED',
      'IN_PROGRESS',
      'COMPLETED',
      'FAILED',
    ]),
    attempts: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      pcbType: faker.helpers.arrayElement(['dsp', 'wifi']),
      status: faker.helpers.arrayElement([
        'AWAITING_PILOT_STATE_A',
        'PENDING',
        'STARTED',
        'INSTALLED',
        'FAILED',
        'SKIPPED',
      ]),
      sequence: faker.number.int(),
      startedClientTimestamp: faker.date.past(),
    })),
    requestedAt: faker.date.past(),
    scheduledDate: faker.date.past(),
    completedAt: faker.date.past(),
  };
}

export function getGetFirmwareUpdate200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      targetVersionInfo: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement([
              'arch1',
              'arch2',
              'arch2.4',
              'arch3',
            ]),
            details: {
              dspVersion: '2.37.0-00005',
              wifiVersion: '1.14',
            },
            createdDate: '2022-01-01T00:00:00.000Z',
          },
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement(['arch5']),
            createdDate: '2022-01-01T00:00:00.000Z',
            path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
          },
        ]),
      ]),
      sourceVersionInfo: faker.helpers.arrayElement([
        faker.helpers.arrayElement([
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement([
              'arch1',
              'arch2',
              'arch2.4',
              'arch3',
            ]),
            details: {
              dspVersion: '2.37.0-00005',
              wifiVersion: '1.14',
            },
            createdDate: '2022-01-01T00:00:00.000Z',
          },
          {
            manifestId: 'A22P-2.37.0-00001',
            architecture: faker.helpers.arrayElement(['arch5']),
            createdDate: '2022-01-01T00:00:00.000Z',
            path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
          },
        ]),
      ]),
      currentStatus: faker.helpers.arrayElement([
        'QUEUED',
        'SCHEDULED',
        'IN_PROGRESS',
        'COMPLETED',
        'FAILED',
      ]),
      attempts: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.string.uuid(),
        pcbType: faker.helpers.arrayElement(['dsp', 'wifi']),
        status: faker.helpers.arrayElement([
          'AWAITING_PILOT_STATE_A',
          'PENDING',
          'STARTED',
          'INSTALLED',
          'FAILED',
          'SKIPPED',
        ]),
        sequence: faker.number.int(),
        startedClientTimestamp: faker.date.past(),
      })),
      requestedAt: faker.date.past(),
      scheduledDate: faker.date.past(),
      completedAt: faker.date.past(),
    },
    {
      id: faker.string.uuid(),
      targetManifestId: 'A50P-1.9.1',
      sourceManifestId: 'A50P-1.9.1',
      currentStatus: faker.helpers.arrayElement([
        'NOT_ACCEPTED',
        'IN_PROGRESS',
        'COMPLETED',
        'FAILED',
      ]),
      requestedAt: faker.date.past(),
      completedAt: faker.date.past(),
    },
  ]);
}

export function getGetManifests200Response() {
  return {
    data: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ])
    ),
  };
}

export function getCreateManifest201Response() {
  return faker.helpers.arrayElement([
    {
      manifestId: 'A22P-2.37.0-00001',
      architecture: faker.helpers.arrayElement([
        'arch1',
        'arch2',
        'arch2.4',
        'arch3',
      ]),
      details: {
        dspVersion: '2.37.0-00005',
        wifiVersion: '1.14',
      },
      createdDate: '2022-01-01T00:00:00.000Z',
    },
    {
      manifestId: 'A22P-2.37.0-00001',
      architecture: faker.helpers.arrayElement(['arch5']),
      createdDate: '2022-01-01T00:00:00.000Z',
      path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
    },
  ]);
}

export function getGetUpdateBatch200Response() {
  return {
    id: faker.string.uuid(),
    batchName: faker.person.fullName(),
    manifest: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ]),
    ]),
    requestedAt: faker.date.past(),
    totalSerialNumbers: faker.number.int(),
  };
}

export function getCreateUpdateBatch202Response() {
  return {
    id: faker.string.uuid(),
    batchName: faker.person.fullName(),
    manifest: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ]),
    ]),
    requestedAt: faker.date.past(),
    totalSerialNumbers: faker.number.int(),
  };
}

export function getGetUpdateBatchById200Response() {
  return {
    id: faker.string.uuid(),
    batchName: faker.person.fullName(),
    manifest: faker.helpers.arrayElement([
      faker.helpers.arrayElement([
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement([
            'arch1',
            'arch2',
            'arch2.4',
            'arch3',
          ]),
          details: {
            dspVersion: '2.37.0-00005',
            wifiVersion: '1.14',
          },
          createdDate: '2022-01-01T00:00:00.000Z',
        },
        {
          manifestId: 'A22P-2.37.0-00001',
          architecture: faker.helpers.arrayElement(['arch5']),
          createdDate: '2022-01-01T00:00:00.000Z',
          path: 'pt2/2024031234234133802/v0.99.5/arch5-podpoint-bundle-arch5-podpoint-imx6ul-pt2-234234234.raucb',
        },
      ]),
    ]),
    requestedAt: faker.date.past(),
    totalSerialNumbers: faker.number.int(),
  };
}

export function getListSerialNumberGroups200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    targetManifestId: faker.lorem.words(),
    architecture: faker.helpers.arrayElement([
      'arch2',
      'arch2.4',
      'arch3',
      'arch5',
      'rfid',
    ]),
    createdAt: faker.date.past(),
    updatedAt: faker.date.past(),
  }));
}

export function getPostSerialNumberGroup200Response() {
  return {
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    targetManifestId: faker.lorem.words(),
    architecture: faker.helpers.arrayElement([
      'arch2',
      'arch2.4',
      'arch3',
      'arch5',
      'rfid',
    ]),
    createdAt: faker.date.past(),
    updatedAt: faker.date.past(),
  };
}

export function getGetSerialNumberGroup200Response() {
  return {
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    targetManifestId: faker.lorem.words(),
    architecture: faker.helpers.arrayElement([
      'arch2',
      'arch2.4',
      'arch3',
      'arch5',
      'rfid',
    ]),
    createdAt: faker.date.past(),
    updatedAt: faker.date.past(),
  };
}

export function getPatchSerialNumberGroup200Response() {
  return {
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    targetManifestId: faker.lorem.words(),
    architecture: faker.helpers.arrayElement([
      'arch2',
      'arch2.4',
      'arch3',
      'arch5',
      'rfid',
    ]),
    createdAt: faker.date.past(),
    updatedAt: faker.date.past(),
  };
}

export function getGetSerialNumbers200Response() {
  return {
    serialNumber: '30303030',
    currentManifestId: faker.lorem.words(),
    isOnTargetManifest: faker.datatype.boolean(),
    createdAt: faker.date.past(),
  };
}

export function getTriggerFirmwareUpdate200Response() {
  return {
    status: faker.helpers.arrayElement(['allAccepted', 'someAccepted']),
  };
}

export function getGetSerialNumbersByString200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    serialNumber: '30303030',
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    targetManifestId: faker.lorem.words(),
    currentManifestId: faker.lorem.words(),
    isOnTargetManifest: faker.datatype.boolean(),
    architecture: faker.helpers.arrayElement([
      'arch2',
      'arch2.4',
      'arch3',
      'arch5',
      'rfid',
    ]),
  }));
}
