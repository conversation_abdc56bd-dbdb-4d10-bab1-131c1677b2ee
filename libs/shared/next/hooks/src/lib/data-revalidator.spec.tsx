import { render, screen, waitFor } from '@testing-library/react';
import { useDataRevalidator } from './data-revalidator';
import React from 'react';

const mockRouterRefresh = jest.fn();
const mockPathname = jest.fn();
const mockSearchParams = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    refresh: () => mockRouterRefresh(),
  }),
  usePathname: () => mockPathname(),
  useSearchParams: () => mockSearchParams(),
}));

interface DataRevalidatorProviderProps {
  children: React.ReactNode;
  debounceTime?: number;
  revalidateOnFocus?: boolean;
}

const DataRevalidatorProvider = ({
  children,
  debounceTime = 2000,
  revalidateOnFocus = false,
}: DataRevalidatorProviderProps) => {
  useDataRevalidator({ debounceTime, revalidateOnFocus });
  return children;
};

describe('data revalidator', () => {
  beforeEach(() => {
    process.env.NODE_ENV = 'production';
  });

  it('should revalidate on route changes', async () => {
    mockPathname.mockReturnValueOnce('/test');

    const component = (
      <DataRevalidatorProvider debounceTime={1000}>
        <p>Hello</p>
      </DataRevalidatorProvider>
    );

    mockPathname.mockReturnValueOnce('/test');

    const { rerender } = render(component);

    expect(mockRouterRefresh).not.toHaveBeenCalled();

    rerender(component);

    await waitFor(
      () => {
        expect(mockRouterRefresh).toHaveBeenCalledTimes(1);
      },
      { timeout: 1100 }
    );
  });

  it('should revalidate on search params change', async () => {
    mockSearchParams.mockReturnValueOnce(new URLSearchParams('foo=bar'));

    render(
      <DataRevalidatorProvider debounceTime={500}>
        <p>Hello</p>
      </DataRevalidatorProvider>
    );

    expect(mockRouterRefresh).not.toHaveBeenCalled();

    mockSearchParams.mockReturnValueOnce(new URLSearchParams('eggs=spam'));

    await waitFor(
      () => {
        expect(mockRouterRefresh).toHaveBeenCalledTimes(1);
      },
      { timeout: 600 }
    );
  });

  it('should not run in development mode', async () => {
    process.env.NODE_ENV = 'development';

    mockPathname.mockReturnValueOnce('/test');

    const component = (
      <DataRevalidatorProvider>
        <p>Hello</p>
      </DataRevalidatorProvider>
    );

    mockPathname.mockReturnValueOnce('/test');

    const { rerender } = render(component);

    expect(mockRouterRefresh).not.toHaveBeenCalled();

    rerender(component);

    await waitFor(
      () => {
        expect(mockRouterRefresh).not.toHaveBeenCalled();
      },
      { timeout: 1100 }
    );
  });

  it('should revalidate on focus if revalidate on focus param is true', async () => {
    render(
      <DataRevalidatorProvider revalidateOnFocus={true} debounceTime={600}>
        <p>Hello</p>
        <label htmlFor="hello-world">Hello world</label>
        <input type="text" id="hello-world" />
      </DataRevalidatorProvider>
    );

    const input = screen.getByLabelText('Hello world');
    input.focus();

    await waitFor(
      () => {
        expect(mockRouterRefresh).toHaveBeenCalledTimes(1);
      },
      { timeout: 700 }
    );
  });
});
