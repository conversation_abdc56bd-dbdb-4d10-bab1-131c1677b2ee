import * as getConfig from 'next/config';
import { render, screen } from '@testing-library/react';
import { useNextEnvVariable } from './next-env-variable';

const mockUseEnvContext = jest.fn().mockReturnValue({});

jest.mock('next-runtime-env', () => ({
  useEnvContext: () => mockUseEnvContext(),
}));
jest.mock('next/config');

const TestComponent = ({ envKey }: { envKey: string }) => {
  const envVariable = useNextEnvVariable(envKey);
  return <div>{envVariable}</div>;
};

describe('use next env variable', () => {
  it('should return the correct value from runtime env', () => {
    mockUseEnvContext.mockReturnValueOnce({
      NEXT_PUBLIC_TEST_ENV: 'test value 1',
    });
    render(<TestComponent envKey="NEXT_PUBLIC_TEST_ENV" />);

    expect(screen.getByText('test value 1')).toBeInTheDocument();
  });

  it.each([
    ['with next public', 'NEXT_PUBLIC_TEST_ENV'],
    ['without next public', 'TEST_ENV'],
  ])(
    'should return the correct value from public runtime config %s',
    (_, env) => {
      jest.spyOn(getConfig, 'default').mockReturnValue({
        publicRuntimeConfig: {
          testEnv: 'test value 2',
        },
      });
      render(<TestComponent envKey={env} />);
      expect(screen.getByText('test value 2')).toBeInTheDocument();
    }
  );

  it('should return the correct value from process.env if no runtime values exist', () => {
    mockUseEnvContext.mockReturnValueOnce({});
    jest.spyOn(getConfig, 'default').mockReturnValue({
      publicRuntimeConfig: {},
    });
    process.env.NEXT_PUBLIC_TEST_ENV = 'test value 3';
    render(<TestComponent envKey="NEXT_PUBLIC_TEST_ENV" />);
    expect(screen.getByText('test value 3')).toBeInTheDocument();
  });

  it('should return undefined if no value is found', () => {
    delete process.env.NEXT_PUBLIC_TEST_ENV;
    render(<TestComponent envKey="NEXT_PUBLIC_TEST_ENV" />);
    expect(screen.queryByText('test value 3')).not.toBeInTheDocument();
  });
});
