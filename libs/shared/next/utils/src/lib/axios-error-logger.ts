import { AxiosError } from 'axios';
import { Logger } from 'pino';

export const logAxiosError = (
  message: string,
  error: AxiosError,
  logger: Logger,
  context: object = {}
) => {
  if (error.response) {
    logger.error(
      {
        ...context,
        error: {
          config: error.config,
          cause: error.cause,
          message: error.message,
          name: error.name,
          response: {
            data: error.response.data,
            headers: error.response.headers,
            status: error.response.status,
          },
          status: error.status,
        },
      },
      message
    );
  } else {
    logger.error(
      {
        ...context,
        error: {
          config: error.config,
          cause: error.cause,
          message: error.message,
          name: error.name,
          status: error.status,
        },
      },
      message
    );
  }
};
