import {
  Alert,
  AlertType,
  Paragraph,
  TextSize,
} from '@experience/shared/react/design-system';
import { ReactElement, ReactNode } from 'react';
import { useNextEnvVariable } from '@experience/shared/next/hooks';

interface InformationBannerProps {
  message: ReactNode;
  bannerType: AlertType;
}

export const InformationBanner = ({
  message,
  bannerType,
}: InformationBannerProps) => {
  const reactMessage =
    typeof message === 'string' ? undefined : (message as ReactElement);
  return (
    <div className="flex py-1">
      <div className="flex-1">
        <Alert type={bannerType}>
          {reactMessage ?? (
            <Paragraph textSize={TextSize.Large}>{message}</Paragraph>
          )}
        </Alert>
      </div>
    </div>
  );
};

const Info = () => {
  const message = useNextEnvVariable('NEXT_PUBLIC_INFO_BANNER_MESSAGE');
  return message ? (
    <InformationBanner message={message} bannerType={AlertType.INFO} />
  ) : null;
};

const Maintenance = () => {
  const message = useNextEnvVariable('NEXT_PUBLIC_MAINTENANCE_BANNER_MESSAGE');
  return message ? (
    <InformationBanner message={message} bannerType={AlertType.WARNING} />
  ) : null;
};

InformationBanner.Maintenance = Maintenance;
InformationBanner.Info = Info;

export const InformationBanners = () => (
  <>
    {InformationBanner.Info() ? <InformationBanner.Info /> : null}
    {InformationBanner.Maintenance() ? <InformationBanner.Maintenance /> : null}
  </>
);
