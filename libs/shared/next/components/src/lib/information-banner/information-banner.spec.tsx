import * as getConfig from 'next/config';
import { InformationBanner, InformationBanners } from './information-banner';
import { Paragraph } from '@experience/shared/react/design-system';
import { PublicEnvProvider } from 'next-runtime-env';
import { render, screen } from '@testing-library/react';

jest.mock('next/config');

describe('information banner', () => {
  beforeEach(() => {
    jest.spyOn(getConfig, 'default').mockReturnValue({
      publicRuntimeConfig: {
        infoBannerMessage: 'testing 456',
        maintenanceBannerMessage: 'testing 123',
      },
    });
  });

  it.each([
    ['info banner', InformationBanner.Info],
    ['maintenance banner', InformationBanner.Maintenance],
    ['all banners', InformationBanners],
  ])('should render correctly %s', (_, Component) => {
    const { baseElement } = render(
      <PublicEnvProvider>
        <Component />
      </PublicEnvProvider>
    );
    expect(baseElement).toBeInTheDocument();
  });

  it.each([
    ['info banner', InformationBanner.Info],
    ['maintenance banner', InformationBanner.Maintenance],
    ['all banners', InformationBanners],
  ])('should match snapshot for %s', (_, Component) => {
    const { baseElement } = render(
      <PublicEnvProvider>
        <Component />
      </PublicEnvProvider>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    ['render info banner if info env variable is provided', true],
    ['render no banner if message env variable is not provided', false],
  ])('%s', (_, shouldRender) => {
    const message = 'Exciting news coming soon - stay tuned!';
    jest.spyOn(getConfig, 'default').mockReturnValue({
      publicRuntimeConfig: {
        infoBannerMessage: shouldRender ? message : undefined,
      },
    });

    render(
      <PublicEnvProvider>
        <InformationBanner.Info />
      </PublicEnvProvider>
    );

    shouldRender
      ? expect(screen.getByText(message)).toBeInTheDocument()
      : expect(screen.queryByText(message)).not.toBeInTheDocument();
  });

  it.each([
    ['render maintenance banner if maintenance env variable is provided', true],
    ['render no banner if maintenance env variable is not provided', false],
  ])('%s', (_, shouldRender) => {
    const message = 'We are currently undergoing maintenance';
    jest.spyOn(getConfig, 'default').mockReturnValue({
      publicRuntimeConfig: {
        maintenanceBannerMessage: shouldRender ? message : undefined,
      },
    });

    render(
      <PublicEnvProvider>
        <InformationBanner.Maintenance />
      </PublicEnvProvider>
    );

    shouldRender
      ? expect(screen.getByText(message)).toBeInTheDocument()
      : expect(screen.queryByText(message)).not.toBeInTheDocument();
  });

  it('should match snapshot when the message is a react element', () => {
    jest.spyOn(getConfig, 'default').mockReturnValue({
      publicRuntimeConfig: {
        infoBannerMessage: <Paragraph>testing 456</Paragraph>,
      },
    });

    render(
      <PublicEnvProvider>
        <InformationBanner.Info />
      </PublicEnvProvider>
    );
    expect(screen.queryByText('testing 456')).toMatchSnapshot();
  });
});
