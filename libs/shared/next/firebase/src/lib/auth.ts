import {
  connectAuthEmulator,
  getAuth as getFirebaseAuth,
  inMemoryPersistence,
} from 'firebase/auth';
import { getApp } from './app';
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();
const { emulatorHost } = publicRuntimeConfig.firebase;

export const getAuth = async () => {
  const auth = getFirebaseAuth(getApp());
  if (emulatorHost) {
    connectAuthEmulator(auth, `http://${emulatorHost}`);
  }
  await auth.setPersistence(inMemoryPersistence);
  return auth;
};
