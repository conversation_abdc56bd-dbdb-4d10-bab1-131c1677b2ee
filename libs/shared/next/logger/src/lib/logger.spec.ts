import { createMock } from '@golevelup/ts-jest';
import { logger, pinoInstance } from './logger';

describe('logger', () => {
  const request: Request = createMock<Request>({
    method: 'GET',
    url: 'https://identity.pod-point.com/en/request-reset',
    headers: {
      get: () => 'Safari 15 / macOS 15.0.1',
    },
  });

  let pinoError: jest.SpyInstance;
  let pinoInfo: jest.SpyInstance;

  beforeEach(() => {
    pinoError = jest.spyOn(pinoInstance, 'error').mockReturnValue(undefined);
    pinoInfo = jest.spyOn(pinoInstance, 'info').mockReturnValue(undefined);
  });

  describe('error()', () => {
    it('using pino to log an error with the default params', () => {
      const error = new Error();

      logger.error({ request, error }, 'something went wrong');

      expect(pinoError).toHaveBeenCalledTimes(1);
      expect(pinoError).toHaveBeenCalledWith(
        {
          userAgent: 'Safari 15 / macOS 15.0.1',
          method: 'GET',
          path: '/en/request-reset',
          error,
        },
        'something went wrong'
      );
    });

    it('using pino to log info with the default params', () => {
      logger.info({ request }, 'something went wrong');

      expect(pinoInfo).toHaveBeenCalledTimes(1);
      expect(pinoInfo).toHaveBeenCalledWith(
        {
          userAgent: 'Safari 15 / macOS 15.0.1',
          method: 'GET',
          path: '/en/request-reset',
        },
        'something went wrong'
      );
    });
  });
});
