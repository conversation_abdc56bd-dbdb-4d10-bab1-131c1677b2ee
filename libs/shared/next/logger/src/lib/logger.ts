import { Level, pino } from 'pino';

type LoggerParams = {
  request: Request;
} & Record<string, unknown>;

export const pinoInstance = pino();

const log = (
  level: Level,
  { request, ...params }: LoggerParams,
  message: string
) => {
  pinoInstance[level](
    {
      userAgent: request.headers.get('user-agent'),
      method: request.method,
      path: new URL(request.url).pathname,
      ...params,
    },
    message
  );
};

export const logger = {
  error: (params: LoggerParams, message: string) => {
    log('error', params, message);
  },
  info: (params: LoggerParams, message: string) => {
    log('info', params, message);
  },
};
