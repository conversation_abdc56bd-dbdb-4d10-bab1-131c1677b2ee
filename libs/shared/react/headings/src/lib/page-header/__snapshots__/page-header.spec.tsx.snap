// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PageHeader should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Header heading
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          Header sub-heading
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <button>
          Action button
        </button>
      </div>
    </header>
  </div>
</body>
`;

exports[`PageHeader should match snapshot with no action 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Header heading
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          Header sub-heading
        </p>
      </div>
    </header>
  </div>
</body>
`;
