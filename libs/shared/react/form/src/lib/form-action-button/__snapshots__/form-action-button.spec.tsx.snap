// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormActionButton should match snapshot 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      type="submit"
    >
      Action
    </button>
  </div>
</body>
`;

exports[`FormActionButton should match snapshot when action is pending 1`] = `
<body>
  <div>
    <button
      class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
      disabled=""
      type="submit"
    >
      Action
    </button>
  </div>
</body>
`;
