// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`KeyValueTable should match snapshot with string value 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Test Table
          </caption>
          <tbody
            class="divide-y"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
              >
                String Row
              </td>
              <td
                class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
              >
                String Value
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
              >
                Element Row
              </td>
              <td
                class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
              >
                <span>
                  Element Value
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
