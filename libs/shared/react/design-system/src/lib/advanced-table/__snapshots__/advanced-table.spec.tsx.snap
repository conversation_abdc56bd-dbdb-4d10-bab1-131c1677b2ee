// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AdvancedTable Client side filters should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div>
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="deleted"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="deleted"
              id="headlessui-label-:test-id-1"
            >
              Deleted
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 deleted"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="deleted"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Yes
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="deleted"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="yes"
              />
            </span>
          </div>
        </div>
        <div>
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="status"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="status"
              id="headlessui-label-:test-id-9"
            >
              Status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-9 status"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="status"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            advanced table component
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  ID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                1
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                C Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                3
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                B Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                available
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`AdvancedTable Server side filters should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div>
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="deleted"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="deleted"
              id="headlessui-label-:test-id-1"
            >
              Deleted
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 deleted"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="deleted"
                type="button"
              >
                <span
                  class="block truncate"
                >
                  Yes
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            >
              <input
                hidden=""
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
              />
              <input
                hidden=""
                name="deleted"
                readonly=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                type="hidden"
                value="yes"
              />
            </span>
          </div>
        </div>
        <div>
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="status"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="status"
              id="headlessui-label-:test-id-9"
            >
              Status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-9 status"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="status"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            advanced table component
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  ID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                1
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                C Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                3
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                B Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                available
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`AdvancedTable should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            advanced table component
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  ID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                1
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                C Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                unavailable
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                3
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                B Bloggs
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                available
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Filters should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex items-center mb-4 space-x-4"
    >
      <p
        class="text-md font-normal py-2 self-end break-words"
      >
        Filters: 
      </p>
      <div>
        <div
          data-headlessui-state=""
        >
          <label
            aria-labelledby="deleted"
            class="block mb-2 text-md font-bold"
            data-headlessui-state=""
            for="deleted"
            id="headlessui-label-:test-id-1"
          >
            Deleted
          </label>
          <div
            class="relative mt-1"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-labelledby="headlessui-label-:test-id-1 deleted"
              class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
              data-headlessui-state=""
              id="deleted"
              type="button"
            >
              <span
                class="block truncate"
              >
                Yes
              </span>
              <span
                class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
              >
                <svg
                  aria-hidden="true"
                  class="fill-current w-4 h-2.5 text-neutral"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    drop down
                  </title>
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </span>
            </button>
          </div>
          <span
            hidden=""
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          >
            <input
              hidden=""
              readonly=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              type="hidden"
            />
            <input
              hidden=""
              name="deleted"
              readonly=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              type="hidden"
              value="yes"
            />
          </span>
        </div>
      </div>
      <div>
        <div
          data-headlessui-state=""
        >
          <label
            aria-labelledby="status"
            class="block mb-2 text-md font-bold"
            data-headlessui-state=""
            for="status"
            id="headlessui-label-:test-id-9"
          >
            Status
          </label>
          <div
            class="relative mt-1"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-labelledby="headlessui-label-:test-id-9 status"
              class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
              data-headlessui-state=""
              id="status"
              type="button"
            >
              <span
                class="text-neutral block truncate"
              >
                All
              </span>
              <span
                class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
              >
                <svg
                  aria-hidden="true"
                  class="fill-current w-4 h-2.5 text-neutral"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    drop down
                  </title>
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </span>
            </button>
          </div>
          <span
            hidden=""
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          />
        </div>
      </div>
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
        type="button"
      >
        Clear
      </button>
    </div>
  </div>
</body>
`;
