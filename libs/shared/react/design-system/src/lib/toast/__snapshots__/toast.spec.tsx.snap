// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Toast should match the default toast snapshot 1`] = `
<body>
  <div>
    <div
      class="w-[18rem] flex items-start p-3 rounded-lg shadow-lg ring-1 ring-black/5 bg-white"
    >
      <span
        aria-hidden="true"
        class="shrink-0 flex items-center justify-center w-5 h-5 rounded-sm bg-primary/20 text-primary"
      >
        <svg
          class="fill-current w-3 h-3"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M14.13,30.18a1.52,1.52,0,0,1-1.51-1.51v-8H7.36a1.47,1.47,0,0,1-.81-.24,1.52,1.52,0,0,1-.46-2.09L16.32,2.53a1.43,1.43,0,0,1,1.33-.7,1.53,1.53,0,0,1,1.06.49,1.5,1.5,0,0,1,.39,1.09L19,11.15h5.61a1.54,1.54,0,0,1,.83.24,1.5,1.5,0,0,1,.65,1,1.48,1.48,0,0,1-.21,1.14l-10.5,16A1.53,1.53,0,0,1,14.13,30.18ZM18.31,3.8h0Zm-1.57-.46v0Zm.57-.18h0Z"
            />
          </g>
        </svg>
      </span>
      <div
        class="flex-grow text-left px-2.5"
      >
        <div
          class="-my-1 text-xs"
        >
          Message
        </div>
      </div>
      <button
        class="rounded-full focus:ring-2 focus:ring-neutral outline-hidden hover:opacity-60"
        type="button"
      >
        <svg
          class="h-3.5 w-3.5 stroke-1 stroke-current fill-current"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>
            Close
          </title>
          <g>
            <g>
              <line
                x1="5.16"
                x2="18.94"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
              />
            </g>
            <g>
              <line
                x1="18.94"
                x2="5.16"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
              />
            </g>
          </g>
        </svg>
      </button>
    </div>
  </div>
</body>
`;

exports[`Toast should match the default toast snapshot with closeable false 1`] = `
<body>
  <div>
    <div
      class="w-[18rem] flex items-start p-3 rounded-lg shadow-lg ring-1 ring-black/5 bg-white"
    >
      <span
        aria-hidden="true"
        class="shrink-0 flex items-center justify-center w-5 h-5 rounded-sm bg-primary/20 text-primary"
      >
        <svg
          class="fill-current w-3 h-3"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M14.13,30.18a1.52,1.52,0,0,1-1.51-1.51v-8H7.36a1.47,1.47,0,0,1-.81-.24,1.52,1.52,0,0,1-.46-2.09L16.32,2.53a1.43,1.43,0,0,1,1.33-.7,1.53,1.53,0,0,1,1.06.49,1.5,1.5,0,0,1,.39,1.09L19,11.15h5.61a1.54,1.54,0,0,1,.83.24,1.5,1.5,0,0,1,.65,1,1.48,1.48,0,0,1-.21,1.14l-10.5,16A1.53,1.53,0,0,1,14.13,30.18ZM18.31,3.8h0Zm-1.57-.46v0Zm.57-.18h0Z"
            />
          </g>
        </svg>
      </span>
      <div
        class="flex-grow text-left px-2.5"
      >
        <div
          class="-my-1 text-xs"
        >
          Message
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`Toast should match the default toast with heading snapshot 1`] = `
<body>
  <div>
    <div
      class="w-[18rem] flex items-start p-3 rounded-lg shadow-lg ring-1 ring-black/5 bg-white"
    >
      <span
        aria-hidden="true"
        class="shrink-0 flex items-center justify-center w-5 h-5 rounded-sm bg-primary/20 text-primary"
      >
        <svg
          class="fill-current w-3 h-3"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M14.13,30.18a1.52,1.52,0,0,1-1.51-1.51v-8H7.36a1.47,1.47,0,0,1-.81-.24,1.52,1.52,0,0,1-.46-2.09L16.32,2.53a1.43,1.43,0,0,1,1.33-.7,1.53,1.53,0,0,1,1.06.49,1.5,1.5,0,0,1,.39,1.09L19,11.15h5.61a1.54,1.54,0,0,1,.83.24,1.5,1.5,0,0,1,.65,1,1.48,1.48,0,0,1-.21,1.14l-10.5,16A1.53,1.53,0,0,1,14.13,30.18ZM18.31,3.8h0Zm-1.57-.46v0Zm.57-.18h0Z"
            />
          </g>
        </svg>
      </span>
      <div
        class="flex-grow text-left px-2.5"
      >
        <div
          class="-my-1 text-xs"
        >
          <div>
            <p
              class="mb-2 text-xs font-bold leading-3"
            >
              Heading
            </p>
            <p
              class="text-xs"
            >
              Message
            </p>
          </div>
        </div>
      </div>
      <button
        class="rounded-full focus:ring-2 focus:ring-neutral outline-hidden hover:opacity-60"
        type="button"
      >
        <svg
          class="h-3.5 w-3.5 stroke-1 stroke-current fill-current"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>
            Close
          </title>
          <g>
            <g>
              <line
                x1="5.16"
                x2="18.94"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
              />
            </g>
            <g>
              <line
                x1="18.94"
                x2="5.16"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
              />
            </g>
          </g>
        </svg>
      </button>
    </div>
  </div>
</body>
`;

exports[`Toast should match the error toast snapshot 1`] = `
<body>
  <div>
    <div
      class="w-[18rem] flex items-start p-3 rounded-lg shadow-lg ring-1 ring-black/5 bg-error/10 text-error"
    >
      <span
        aria-hidden="true"
        class="shrink-0 flex items-center justify-center w-5 h-5 rounded-sm bg-error/20 text-error"
      >
        <svg
          class="fill-current w-3 h-3"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M14.13,30.18a1.52,1.52,0,0,1-1.51-1.51v-8H7.36a1.47,1.47,0,0,1-.81-.24,1.52,1.52,0,0,1-.46-2.09L16.32,2.53a1.43,1.43,0,0,1,1.33-.7,1.53,1.53,0,0,1,1.06.49,1.5,1.5,0,0,1,.39,1.09L19,11.15h5.61a1.54,1.54,0,0,1,.83.24,1.5,1.5,0,0,1,.65,1,1.48,1.48,0,0,1-.21,1.14l-10.5,16A1.53,1.53,0,0,1,14.13,30.18ZM18.31,3.8h0Zm-1.57-.46v0Zm.57-.18h0Z"
            />
          </g>
        </svg>
      </span>
      <div
        class="flex-grow text-left px-2.5"
      >
        <div
          class="-my-1 text-xs"
        >
          Message
        </div>
      </div>
      <button
        class="rounded-full focus:ring-2 focus:ring-neutral outline-hidden hover:opacity-60"
        type="button"
      >
        <svg
          class="h-3.5 w-3.5 stroke-1 stroke-current fill-current"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <title>
            Close
          </title>
          <g>
            <g>
              <line
                x1="5.16"
                x2="18.94"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
              />
            </g>
            <g>
              <line
                x1="18.94"
                x2="5.16"
                y1="5.11"
                y2="18.89"
              />
              <path
                d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
              />
            </g>
          </g>
        </svg>
      </button>
    </div>
  </div>
</body>
`;
