import { AdvancedTableProps } from '../advanced-table/advanced-table';
import { ColumnDef } from '@tanstack/react-table';
import { InfiniteTable } from './infinite-table';
import { Meta, StoryFn } from '@storybook/react';
import { faker } from '@faker-js/faker';
import Button, { ButtonTypes } from '../button/button';

interface SampleData {
  id: number;
  name: string;
  date?: string;
  isActive: boolean;
}

export default {
  component: InfiniteTable,
  title: 'Infinite Table',
} as Meta<typeof InfiniteTable>;

const columns: ColumnDef<SampleData>[] = [
  {
    accessorKey: 'id',
    header: () => 'ID',
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'name',
    header: () => 'Full name',
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'date',
    header: () => 'Added at',
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'isActive',
    accessorFn: (data) => (data.isActive ? 'true' : 'false'),
    header: () => 'Active',
    sortingFn: 'basic',
  },
  {
    enableSorting: false,
    header: () => 'Actions',
    id: 'actions',
    cell: () => <Button buttonType={ButtonTypes.LINK}>Click me</Button>,
  },
];

const data: SampleData[] = [...new Array(5000).keys()].map((i) => ({
  id: i,
  name: faker.person.fullName(),
  date: faker.date.past().toString(),
  isActive: faker.datatype.boolean(),
}));

const defaultProps: AdvancedTableProps<SampleData> = {
  columns,
  data,
  caption: 'Example table',
  id: 'example-table',
  filters: {
    clientSide: [
      {
        columnId: 'isActive',
        label: 'Is active?',
        options: [
          { id: 'true', name: 'Yes' },
          { id: 'false', name: 'No' },
        ],
        searchable: true,
      },
    ],
  },
  showSearchField: true,
};

const Template: StoryFn<typeof InfiniteTable> = () => (
  <InfiniteTable {...defaultProps} />
);

export const Playground = Template.bind({});
Playground.args = {};
