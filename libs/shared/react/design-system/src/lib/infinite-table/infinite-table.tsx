'use client';

import { AdvancedTableProps, MetaType } from '../advanced-table/advanced-table';
import { ArrowDownIcon, ArrowUpIcon } from '../icons';
import { Table } from '../table/table';
import { flexRender } from '@tanstack/react-table';
import { twMerge } from 'tailwind-merge';
import { useAdvancedTable } from '../advanced-table/hooks/use-advanced-table';
import { useCallback, useEffect, useState } from 'react';
import Filters from '../advanced-table/filters/filters';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import debounce from 'debounce';

export const InfiniteTable = <T,>({
  caption,
  clearFilters,
  columns,
  data,
  filters,
  hiddenColumns,
  highlightAlternativeRows = false,
  id,
  initialSort,
  manualFiltering = false,
  meta,
  showSearchField,
  serverSideResultsCount,
}: AdvancedTableProps<T>) => {
  const { globalFilter, results, table } = useAdvancedTable({
    caption,
    columns,
    data,
    filters,
    hiddenColumns,
    id,
    initialSort,
    manualFiltering,
    meta,
    showSearchField,
    serverSideResultsCount,
  });

  const initialOffset = 100;

  const [offset, setOffset] = useState(initialOffset);
  const [hasMore, setHasMore] = useState(true);

  const { rows } = table.getRowModel();

  useEffect(() => {
    setHasMore(rows.length > offset);
  }, [rows.length, offset]);

  const fetchMoreData = debounce(() => {
    setOffset(offset + initialOffset);
  }, 1000);

  const handleSetFilter = useCallback(() => {
    setOffset(initialOffset);
  }, [initialOffset]);

  return (
    <>
      {filters || showSearchField ? (
        <div className="flex">
          <Filters
            clearFilters={clearFilters}
            filters={filters ?? {}}
            columns={table.getAllColumns()}
            handleSetFilter={handleSetFilter}
            globalFilter={globalFilter}
            showSearchField={showSearchField}
            setGlobalFilter={table.setGlobalFilter}
          />
          {results}
        </div>
      ) : null}

      <InfiniteScroll
        next={fetchMoreData}
        hasMore={hasMore}
        loader={<p>Loading...</p>}
        dataLength={offset}
      >
        <Table caption={caption}>
          <Table.Header>
            <Table.Row>
              {table.getHeaderGroups().map((headerGroup) =>
                headerGroup.headers.map((header) => (
                  <Table.Heading
                    key={header.id}
                    aria-sort={header.column.getIsSorted() ?? 'none'}
                    className={twMerge(
                      classNames(
                        'align-top',
                        (header.column.columnDef.meta as MetaType)
                          ?.headerCellClassName
                      )
                    )}
                  >
                    {header.column.getCanSort() ? (
                      <button
                        {...{
                          type: 'button',
                          className: twMerge(
                            classNames(
                              'flex items-center hover:underline text-left cursor-pointer',
                              (header.column.columnDef.meta as MetaType)
                                ?.sortButtonClassName
                            )
                          ),
                          onClick: header.column.getToggleSortingHandler(),
                        }}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        <span className="w-4 h-4 ml-1 text-neutral shrink-0">
                          {{
                            asc: (
                              <ArrowUpIcon.SOLID className="stroke-current stroke-2" />
                            ),
                            desc: (
                              <ArrowDownIcon.SOLID className="stroke-current stroke-2" />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </span>
                      </button>
                    ) : (
                      flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )
                    )}
                  </Table.Heading>
                ))
              )}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {rows.slice(0, offset).map((row) => (
              <Table.Row
                key={row.id}
                className={classNames({
                  'odd:bg-gray-50': highlightAlternativeRows,
                })}
              >
                {row.getVisibleCells().map((cell) => (
                  <Table.Data
                    key={cell.id}
                    className={
                      (cell.column.columnDef.meta as MetaType)?.cellClassName
                    }
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Table.Data>
                ))}
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </InfiniteScroll>
    </>
  );
};
