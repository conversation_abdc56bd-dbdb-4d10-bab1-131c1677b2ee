// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Table should match the snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            test table
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Name
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                PPId
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Bob
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                1234
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                John
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4321
              </td>
            </tr>
          </tbody>
          <tfoot
            class=""
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                5555
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Table should update table based on props 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            test table
          </caption>
          <thead
            class=""
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left sr-only"
                scope="col"
              >
                Name
              </th>
              <th
                class="text-left sr-only"
                scope="col"
              >
                PPId
              </th>
              <th
                class="text-left"
                colspan="2"
                scope="col"
              >
                Duration
              </th>
            </tr>
          </thead>
          <tbody
            class=""
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal"
              >
                Bob
              </td>
              <td
                class="whitespace-normal"
              >
                1234
              </td>
              <td
                class="whitespace-normal"
              >
                00:00
              </td>
              <td
                class="whitespace-normal"
              >
                01:00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal"
              >
                John
              </td>
              <td
                class="whitespace-normal"
              >
                4321
              </td>
              <td
                class="whitespace-normal"
              >
                01:00
              </td>
              <td
                class="whitespace-normal"
              >
                02:00
              </td>
            </tr>
          </tbody>
          <tfoot
            class=""
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal"
              >
                2
              </td>
              <td
                class="whitespace-normal"
              >
                5555
              </td>
              <td
                class="whitespace-normal"
              >
                03:00
              </td>
              <td
                class="whitespace-normal"
              >
                04:00
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>
</body>
`;
