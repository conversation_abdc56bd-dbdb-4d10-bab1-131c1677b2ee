import { twMerge } from 'tailwind-merge';
import React from 'react';
import classNames from 'classnames';

interface TableComponentProps {
  children?: React.ReactNode;
  className?: string;
  dividers?: boolean;
  center?: boolean;
  padding?: boolean;
  visuallyHidden?: boolean;
}

export interface TableBodyProps extends TableComponentProps {
  showLastDivider?: boolean;
}

export interface TableHeadingProps extends TableComponentProps {
  colSpan?: number;
}

export interface TableProps extends TableComponentProps {
  caption: string;
}

type Table = React.FC<TableProps> & {
  Body: React.FC<TableBodyProps>;
  Data: React.FC<TableComponentProps>;
  Header: React.FC<TableComponentProps>;
  Heading: React.FC<TableHeadingProps>;
  Footer: React.FC<TableComponentProps>;
  Row: React.FC<TableComponentProps>;
};

export const Table: Table = ({
  caption,
  children,
  className,
  dividers = false,
}: TableProps): React.JSX.Element => (
  <div className="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto">
    <div className="inline-block min-w-full -my-2 py-2 align-middle">
      <table
        className={classNames(
          'min-w-full',
          { 'divide-y': dividers },
          className
        )}
        tabIndex={0}
      >
        <caption className="sr-only">{caption}</caption>
        {children}
      </table>
    </div>
  </div>
);

const Body: React.FC<TableBodyProps> = ({
  children,
  className,
  dividers = true,
  showLastDivider = true,
}: TableBodyProps): React.JSX.Element => (
  <tbody
    className={classNames(
      {
        'divide-y': dividers,
        'border-b border-b-neutral/20': dividers && showLastDivider,
      },
      className
    )}
  >
    {children}
  </tbody>
);

const Data: React.FC<TableComponentProps> = ({
  children,
  className,
  padding = true,
  center = false,
}: TableComponentProps): React.JSX.Element => (
  <td
    className={classNames(
      'whitespace-normal',
      { 'px-3 py-4': padding },
      { 'text-center': center },
      className
    )}
  >
    {children}
  </td>
);

const Footer: React.FC<TableComponentProps> = ({
  children,
  className,
}: TableComponentProps): React.JSX.Element => (
  <tfoot className={classNames(className)}>{children}</tfoot>
);

const Header: React.FC<TableComponentProps> = ({
  children,
  className,
  dividers = true,
}: TableComponentProps): React.JSX.Element => (
  <thead
    className={classNames(
      { 'border-b border-b-neutral/20': dividers },
      className
    )}
  >
    {children}
  </thead>
);

const Heading: React.FC<TableHeadingProps> = ({
  center = false,
  children,
  className,
  colSpan = 1,
  padding = true,
  visuallyHidden = false,
}: TableHeadingProps): React.JSX.Element => (
  <th
    colSpan={colSpan > 1 ? colSpan : undefined}
    scope="col"
    className={classNames(
      'text-left',
      { 'text-center': center },
      { 'p-3': padding },
      { 'sr-only': visuallyHidden },
      className
    )}
  >
    {children}
  </th>
);

const Row: React.FC<TableComponentProps> = ({
  children,
  className,
}: TableComponentProps): React.JSX.Element => (
  <tr className={twMerge(classNames('border-b-neutral/20', className))}>
    {children}
  </tr>
);

Table.Body = Body;
Table.Data = Data;
Table.Footer = Footer;
Table.Header = Header;
Table.Heading = Heading;
Table.Row = Row;
