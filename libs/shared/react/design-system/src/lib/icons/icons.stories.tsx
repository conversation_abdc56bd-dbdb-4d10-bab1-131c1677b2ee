import { HomeIcon } from './home-icon/home-icon';
import { StoryFn } from '@storybook/react';
import ArrowDownIcon from './arrow-down-icon/arrow-down-icon';
import ArrowHeadDownIcon from './arrow-head-down-icon/arrow-head-down-icon';
import ArrowHeadUpIcon from './arrow-head-up-icon/arrow-head-up-icon';
import ArrowLeftIcon from './arrow-left-icon/arrow-left-icon';
import ArrowRightIcon from './arrow-right-icon/arrow-right-icon';
import ArrowUpIcon from './arrow-up-icon/arrow-up-icon';
import Badge from '../badge/badge';
import BarChartIcon from './bar-chart-icon/bar-chart-icon';
import BoltIcon from './bolt-icon/bolt-icon';
import Button from '../button/button';
import CaretDownIcon from './caret-down-icon/caret-down-icon';
import CaretUpIcon from './caret-up-icon/caret-up-icon';
import ChecklistIcon from './checklist-icon/checklist-icon';
import CheckmarkCircleIcon from './checkmark-circle-icon/checkmark-circle-icon';
import CheckmarkIcon from './checkmark-icon/checkmark-icon';
import ChevronIcon from './chevron-icon/chevron-icon';
import CompanyIcon from './company-icon/company-icon';
import CopyIcon from './copy-icon/copy-icon';
import CrossCircleIcon from './cross-circle-icon/cross-circle-icon';
import CrossIcon from './cross-icon/cross-icon';
import CurrencyPoundIcon from './currency-pound-icon/currency-pound-icon';
import DeleteIcon from './delete-icon/delete-icon';
import DotIcon from './dot-icon/dot-icon';
import DownloadIcon from './download-icon/download-icon';
import EllipsisIcon from './ellipsis-icon/ellipsis-icon';
import EmailIcon from './email-icon/email-icon';
import ExpandIcon from './expand-icon/expand-icon';
import ExportIcon from './export-icon/export-icon';
import FeedbackIcon from './feedback-icon/feedback-icon';
import HomePlugIcon from './home-plug-icon/home-plug-icon';
import InfoIcon from './info-icon/info-icon';
import LocationBoltIcon from './location-bolt-icon/location-bolt-icon';
import LoginIcon from './login-icon/login-icon';
import LogoutIcon from './logout-icon/logout-icon';
import MoneyIcon from './money-icon/money-icon';
import NoDriversIcon from './no-drivers-icon/no-drivers-icon';
import NoPodsIcon from './no-pods-icon/no-pods-icon';
import NoSitesIcon from './no-sites-icon/no-sites-icon';
import NoTariffsIcon from './no-tariffs-icon/no-tariffs-icon';
import OrganisationsIcon from './organisation-icon/organisation-icon';
import PadlockLockedIcon from './padlock-locked-icon/padlock-locked-icon';
import PadlockUnlockedIcon from './padlock-unlocked-icon/padlock-unlocked-icon';
import PencilIcon from './pencil-icon/pencil-icon';
import ProfileIcon from './profile-icon/profile-icon';
import ReceiptIcon from './receipt-icon/receipt-icon';
import RefreshIcon from './refresh-icon/refresh-icon';
import RemoveUserIcon from './remove-user-icon/remove-user-icon';
import ScrollIcon from './scroll-icon/scroll-icon';
import SearchIcon from './search-icon/search-icon';
import TwinChargerIcon from './twin-charger-icon/twin-charger-icon';
import UnlinkWithChargerIcon from './unlink-with-charger-icon/unlink-with-charger-icon';
import UnlinkWithPoundIcon from './unlink-with-pound-icon/unlink-with-pound-icon';
import UsersIcon from './users-icon/users-icon';
import VehicleIcon from './vehicle-icon/vehicle-icon';
import WarningIcon from './warning-icon/warning-icon';

export default {
  title: 'Icons',
};

const Template: StoryFn<typeof HTMLDivElement> = () => {
  const className = 'w-12 h-12 m-2';

  return (
    <div className="flex flex-wrap">
      <ArrowDownIcon.SOLID className={className} />
      <ArrowLeftIcon.SOLID className={className} />
      <ArrowRightIcon.SOLID className={className} />
      <ArrowUpIcon.SOLID className={className} />
      <ArrowHeadDownIcon.SOLID className={className} />
      <ArrowHeadUpIcon.SOLID className={className} />
      <BarChartIcon.LIGHT className={className} />
      <BoltIcon.LIGHT className={className} />
      <BoltIcon.SOLID className={className} />
      <CaretDownIcon.SOLID className={className} />
      <CaretUpIcon.SOLID className={className} />
      <ChecklistIcon.LIGHT className={className} />
      <CheckmarkCircleIcon.LIGHT className={className} />
      <CheckmarkCircleIcon.SMALL className={className} />
      <CheckmarkIcon.SOLID className={className} />
      <ChevronIcon.SOLID className={className} />
      <CompanyIcon.LIGHT className={className} />
      <CopyIcon.LIGHT className={className} />
      <CrossCircleIcon.LIGHT className={className} />
      <CrossCircleIcon.SMALL className={className} />
      <CrossIcon.LIGHT className={className} />
      <CurrencyPoundIcon.LIGHT className={className} />
      <DeleteIcon.LIGHT className={className} />
      <DotIcon.SOLID className={className} />
      <DownloadIcon.LIGHT className={className} />
      <EllipsisIcon.SOLID className={className} />
      <EmailIcon.LIGHT className={className} />
      <ExpandIcon.SOLID className={className} />
      <ExportIcon.LIGHT className={className} />
      <FeedbackIcon.LIGHT className={className} />
      <HomeIcon.LIGHT className={className} />
      <HomeIcon.SOLID className={className} />
      <HomePlugIcon.LIGHT className={className} />
      <InfoIcon.LIGHT className={className} />
      <InfoIcon.SOLID className={className} />
      <LocationBoltIcon.LIGHT className={className} />
      <LocationBoltIcon.SOLID className={className} />
      <LoginIcon.LIGHT className={className} />
      <LogoutIcon.LIGHT className={className} />
      <MoneyIcon.LIGHT className={className} />
      <OrganisationsIcon.LIGHT className={className} />
      <PadlockLockedIcon.LIGHT className={className} />
      <PadlockUnlockedIcon.LIGHT className={className} />
      <PencilIcon.LIGHT className={className} />
      <ProfileIcon.LIGHT className={className} />
      <ReceiptIcon.LIGHT className={className} />
      <RefreshIcon.LIGHT className={className} />
      <RemoveUserIcon.LIGHT className={className} />
      <ScrollIcon.LIGHT className={className} />
      <SearchIcon.LIGHT className={className} />
      <UnlinkWithChargerIcon.LIGHT className={className} />
      <UnlinkWithPoundIcon.LIGHT className={className} />
      <TwinChargerIcon.SOLID className={className} />
      <UsersIcon.LIGHT className={className} />
      <VehicleIcon.LIGHT className={className} />
      <WarningIcon.LIGHT className={className} />
      <WarningIcon.SOLID className={className} />
    </div>
  );
};

export const Gallery = Template.bind({});

export const Styled = () => <HomeIcon.LIGHT className="w-12 text-primary" />;

export const InsideButton = () => (
  <Button className="inline-flex items-center space-x-4">
    <HomeIcon.LIGHT className="h-4 w-4 mr-2" />
    Button with icon
  </Button>
);

export const InsideBadge = () => (
  <Badge.Neutral
    icon={<CheckmarkIcon.SOLID />}
    label="Badge with icon"
  ></Badge.Neutral>
);

export const LargeIcons = () => (
  <div className="flex flex-wrap space-x-4">
    <NoDriversIcon.SOLID />
    <NoPodsIcon.SOLID />
    <NoSitesIcon.SOLID />
    <NoTariffsIcon.SOLID />
  </div>
);
