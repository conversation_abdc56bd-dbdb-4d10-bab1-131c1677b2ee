// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Select should match snapshot when anchor positioning props are passed 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-hidden="true"
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state="open"
        data-open=""
      >
        <button
          aria-controls="headlessui-listbox-options-:test-id-5"
          aria-expanded="true"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
          data-active=""
          data-headlessui-state="open active"
          data-open=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        aria-hidden="true"
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <div
        aria-activedescendant="headlessui-listbox-option-:test-id-8"
        aria-labelledby="select-field"
        aria-orientation="vertical"
        class="absolute z-10 max-h-60 overflow-auto rounded-md bg-white mt-1 text-base transition duration-100 ease-in data-[leave]:opacity-0 data-[enter]:opacity-100 shadow-lg ring-1 ring-black/5 focus:outline-hidden [--anchor-max-height:15rem]"
        data-anchor="bottom center"
        data-headlessui-state="open"
        data-open=""
        id="headlessui-listbox-options-:test-id-5"
        role="listbox"
        style="width: 25px; position: absolute; left: 0px; top: 0px; --button-width: 0px;"
        tabindex="0"
      >
        <div
          aria-selected="true"
          class="bg-neutral/10 relative cursor-pointer select-none py-2 pl-1.5 pr-9"
          data-active=""
          data-focus=""
          data-headlessui-state="active focus selected"
          data-selected=""
          id="headlessui-listbox-option-:test-id-8"
          role="option"
          tabindex="-1"
        >
          <span
            class="block truncate text-xs"
          >
            Item one
          </span>
          <span
            class="absolute inset-y-0 right-0 flex items-center pr-1.5"
          >
            <svg
              class="fill-current h-4 w-4 text-primary"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                Checkmark
              </title>
              <path
                d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
                id="prod"
              />
            </svg>
          </span>
        </div>
        <div
          aria-selected="false"
          class="relative cursor-pointer select-none py-2 pl-1.5 pr-9"
          data-headlessui-state=""
          id="headlessui-listbox-option-:test-id-9"
          role="option"
          tabindex="-1"
        >
          <span
            class="block truncate text-xs"
          >
            Item two
          </span>
        </div>
        <div
          aria-disabled="true"
          aria-selected="false"
          class="relative cursor-pointer select-none py-2 pl-1.5 pr-9 bg-neutral/20 pointer-events-none"
          data-disabled=""
          data-headlessui-state="disabled"
          id="headlessui-listbox-option-:test-id-10"
          role="option"
        >
          <span
            class="block truncate text-xs placeholder-neutral-500"
          >
            Item three
          </span>
        </div>
        <div
          aria-selected="false"
          class="relative cursor-pointer select-none py-2 pl-1.5 pr-9"
          data-headlessui-state=""
          id="headlessui-listbox-option-:test-id-11"
          role="option"
          tabindex="-1"
        >
          <span
            class="block truncate text-xs"
          >
            Item four
          </span>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when an error occurs 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="border-2 border-solid border-red-500 relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
    <p
      class="text-xs font-normal text-error break-words"
    >
      There has been an error
    </p>
  </div>
</body>
`;

exports[`Select should match the snapshot when background colour is bg-neutral/10 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when background colour is bg-white 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when dropdown is shown 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-hidden="true"
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state="open"
        data-open=""
      >
        <button
          aria-controls="headlessui-listbox-options-:test-id-5"
          aria-expanded="true"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
          data-active=""
          data-headlessui-state="open active"
          data-open=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
        <div
          aria-activedescendant="headlessui-listbox-option-:test-id-8"
          aria-labelledby="select-field"
          aria-orientation="vertical"
          class="absolute z-10 max-h-60 overflow-auto rounded-md bg-white mt-1 text-base transition duration-100 ease-in data-[leave]:opacity-0 data-[enter]:opacity-100 shadow-lg ring-1 ring-black/5 focus:outline-hidden"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-listbox-options-:test-id-5"
          role="listbox"
          style="width: 100%; --button-width: 0px;"
          tabindex="0"
        >
          <div
            aria-selected="true"
            class="bg-neutral/10 relative cursor-pointer select-none py-2 pl-1.5 pr-9"
            data-active=""
            data-focus=""
            data-headlessui-state="active focus selected"
            data-selected=""
            id="headlessui-listbox-option-:test-id-8"
            role="option"
            tabindex="-1"
          >
            <span
              class="block truncate text-xs"
            >
              Item one
            </span>
            <span
              class="absolute inset-y-0 right-0 flex items-center pr-1.5"
            >
              <svg
                class="fill-current h-4 w-4 text-primary"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Checkmark
                </title>
                <path
                  d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
                  id="prod"
                />
              </svg>
            </span>
          </div>
          <div
            aria-selected="false"
            class="relative cursor-pointer select-none py-2 pl-1.5 pr-9"
            data-headlessui-state=""
            id="headlessui-listbox-option-:test-id-9"
            role="option"
            tabindex="-1"
          >
            <span
              class="block truncate text-xs"
            >
              Item two
            </span>
          </div>
          <div
            aria-disabled="true"
            aria-selected="false"
            class="relative cursor-pointer select-none py-2 pl-1.5 pr-9 bg-neutral/20 pointer-events-none"
            data-disabled=""
            data-headlessui-state="disabled"
            id="headlessui-listbox-option-:test-id-10"
            role="option"
          >
            <span
              class="block truncate text-xs placeholder-neutral-500"
            >
              Item three
            </span>
          </div>
          <div
            aria-selected="false"
            class="relative cursor-pointer select-none py-2 pl-1.5 pr-9"
            data-headlessui-state=""
            id="headlessui-listbox-option-:test-id-11"
            role="option"
            tabindex="-1"
          >
            <span
              class="block truncate text-xs"
            >
              Item four
            </span>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when input width is w-1/2 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-1/2"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when input width is w-1/3 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-1/3"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when input width is w-2/3 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;

exports[`Select should match the snapshot when input width is w-full 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-field"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-field"
        id="headlessui-label-:test-id-1"
      >
        Select field
      </label>
      <div
        class="relative mt-1"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-labelledby="headlessui-label-:test-id-1 select-field"
          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-field"
          type="button"
        >
          <span
            class="block truncate"
          >
            Item one
          </span>
          <span
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
          >
            <svg
              aria-hidden="true"
              class="fill-current w-4 h-2.5 text-neutral"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title>
                drop down
              </title>
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </span>
        </button>
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      >
        <input
          hidden=""
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
        />
        <input
          hidden=""
          name="selectField"
          readonly=""
          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
          type="hidden"
          value="1"
        />
      </span>
    </div>
  </div>
</body>
`;
