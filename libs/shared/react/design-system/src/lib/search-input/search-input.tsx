import { Input, InputBackgroundColour, InputProps } from '../input/input';
import { SearchIcon } from '../icons';
import CrossIcon from '../icons/cross-icon/cross-icon';
import React, { FC } from 'react';
import classNames from 'classnames';

interface SearchInputProps extends Omit<InputProps, 'label'> {
  handleClear: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

export const SearchInput: FC<SearchInputProps> = ({
  className,
  value,
  onChange,
  handleClear,
  ...props
}) => (
  <div className="relative">
    <span className="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral">
      <SearchIcon.LIGHT height="h-4" width="w-4" />
    </span>
    <Input
      backgroundColour={InputBackgroundColour.WHITE}
      className={classNames('px-8', className)}
      srOnlyLabel={true}
      label="Search"
      role="search"
      placeholder="Search"
      value={value}
      onChange={onChange}
      {...props}
    />
    <button
      aria-label="Clear search"
      className={classNames(
        [value ? 'inline-flex' : 'invisible'],
        'w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden'
      )}
      name="Clear search"
      onClick={handleClear}
    >
      <CrossIcon.LIGHT height="h-3" width="w-3" />
    </button>
  </div>
);

export default SearchInput;
