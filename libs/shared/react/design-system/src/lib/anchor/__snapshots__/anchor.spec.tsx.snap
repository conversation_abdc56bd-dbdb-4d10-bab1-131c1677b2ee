// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Anchor should match the snapshot 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
      href="https://pod-point.com"
    >
      Pod Point
    </a>
  </div>
</body>
`;

exports[`Anchor should match the snapshot if is a native html link 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
      href="https://pod-point.com"
      target="_blank"
    >
      Pod Point
    </a>
  </div>
</body>
`;

exports[`Anchor should match the snapshot with custom class overrides 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline active:text-info cursor-pointer hover:text-error"
      href="https://pod-point.com"
      target="_blank"
    >
      Pod Point
    </a>
  </div>
</body>
`;
