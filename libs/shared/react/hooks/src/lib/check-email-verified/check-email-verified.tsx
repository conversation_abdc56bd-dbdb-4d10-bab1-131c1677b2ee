import { Session } from 'next-auth';
import { ToastMessageWithHeading } from '@experience/shared/react/design-system';
import { jsonFetcher } from '@experience/shared/swr/utils';
import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import useSWR from 'swr';

export const useEmailVerifiedCheck = (): void => {
  const { data: session } = useSWR<Session>('/api/auth/session', jsonFetcher);
  const { data: user } = useSWR<{ emailVerified: boolean }>(
    session?.user ? '/api/user' : null,
    jsonFetcher
  );
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!user) {
      return;
    }

    const toastId = 'check-email-verified-toast';

    if (user.emailVerified) {
      toast.dismiss(toastId);
      return;
    }

    if (!user.emailVerified && pathname === '/') {
      toast.error(
        <ToastMessageWithHeading
          heading="Verify your account"
          message="The email address associated with this account needs to be verified before the 24th January 2024. Please visit the profile page to do this."
        />,
        { duration: Infinity, id: toastId }
      );
      router.push('/profile');
    }
  }, [pathname, router, user]);
};

export default useEmailVerifiedCheck;
