import { detect } from 'detect-browser';
import { useEffect } from 'react';
import toast from 'react-hot-toast';

// https://browser-update.org/browsers.html (Insecure below)
const minimumBrowserVersions = new Map<string, number>([
  ['chrome', 119],
  ['edge', 119],
  ['edge-chromium', 119],
  ['firefox', 115],
  ['safari', 16.1],
]);

export const useDetectBrowser = (): void => {
  useEffect(() => {
    const info = detect();
    if (info && info.type === 'browser') {
      const browserName = info.name;
      const browserVersion = info.version;
      const minimumBrowserVersion = minimumBrowserVersions.get(browserName);
      if (
        minimumBrowserVersion &&
        Number.parseInt(browserVersion) < minimumBrowserVersion
      ) {
        toast.error(
          `Your web browser is out of date. Update your browser for more security, speed and the best experience on this site.`,
          { duration: Infinity, id: 'browser-update-toast' }
        );
      }
    }
  }, []);
};

export default useDetectBrowser;
