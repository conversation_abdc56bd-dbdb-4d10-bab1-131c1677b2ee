import { SelectMonthModal } from './select-month-modal';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import MockDate from 'mockdate';
import dayjs from 'dayjs';
import userEvent from '@testing-library/user-event';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

describe('SelectMonthModal', () => {
  const mockSetOpen = jest.fn();
  const mockWindowOpen = jest.fn();

  const defaultProps = {
    apiEndpoint: '/api/sites/stats/csv',
    open: true,
    setOpen: mockSetOpen,
  };

  beforeEach(() => {
    MockDate.set(new Date(2023, 3, 15));
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should render correctly', () => {
    const { baseElement } = render(<SelectMonthModal {...defaultProps} />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<SelectMonthModal {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should display a description', () => {
    render(
      <SelectMonthModal {...defaultProps} description="Test description" />
    );
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('should not show years before the start year', async () => {
    render(<SelectMonthModal {...defaultProps} startYear={2022} />);

    fireEvent.click(screen.getByRole('button', { name: 'Year' }));

    const option = screen.queryByRole('option', {
      name: /2021/i,
    });
    expect(option).not.toBeInTheDocument();
  });

  it('should not show future month options', async () => {
    render(<SelectMonthModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Year' }));

    const option = screen.getByRole('option', {
      name: /2023/i,
    });
    expect(option).toBeInTheDocument();
    await userEvent.click(option);

    await userEvent.click(screen.getByRole('button', { name: 'Month' }));
    const futureMonthOption = screen.queryByRole('option', {
      name: /May/i,
    });
    expect(futureMonthOption).not.toBeInTheDocument();
  });

  it('should download a csv with the drivers charge stats for the selected month', async () => {
    jest.spyOn(window, 'open').mockImplementation(mockWindowOpen);

    render(<SelectMonthModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Year' }));

    await waitFor(() => {
      const option = screen.getByRole('option', {
        name: /2022/i,
      });
      expect(option).toBeInTheDocument();
      fireEvent.click(option);
    });

    fireEvent.click(screen.getByRole('button', { name: 'Month' }));

    await waitFor(() => {
      const option = screen.getByRole('option', {
        name: /May/i,
      });
      expect(option).toBeInTheDocument();
      fireEvent.click(option);
    });

    fireEvent.click(screen.getByRole('button', { name: 'Download' }));

    await waitFor(() => {
      expect(mockWindowOpen).toHaveBeenCalledWith(
        `/api/sites/stats/csv?date=2022-05-01`,
        '_blank'
      );
      expect(mockSetOpen).toHaveBeenCalledWith(false);
    });
  });
});
