// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: carbon_intensity.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"
)

const deletePeriodByRegionAndStartTime = `-- name: DeletePeriodByRegionAndStartTime :exec
DELETE FROM carbon_intensity.regional_forecast_periods WHERE dno_region_id = $1 AND start_time = $2
`

type DeletePeriodByRegionAndStartTimeParams struct {
	DnoRegionID int16     `json:"dno_region_id"`
	StartTime   time.Time `json:"start_time"`
}

func (q *Queries) DeletePeriodByRegionAndStartTime(ctx context.Context, arg DeletePeriodByRegionAndStartTimeParams) error {
	_, err := q.db.ExecContext(ctx, deletePeriodByRegionAndStartTime, arg.DnoRegionID, arg.StartTime)
	return err
}

const seedRegionalForecastPeriod = `-- name: SeedRegionalForecastPeriod :exec
INSERT INTO carbon_intensity.regional_forecast_periods (dno_region_id, start_time, updated_at, data) VALUES ($1, $2, NOW(), $3)
`

type SeedRegionalForecastPeriodParams struct {
	DnoRegionID int16           `json:"dno_region_id"`
	StartTime   time.Time       `json:"start_time"`
	Data        json.RawMessage `json:"data"`
}

func (q *Queries) SeedRegionalForecastPeriod(ctx context.Context, arg SeedRegionalForecastPeriodParams) error {
	_, err := q.db.ExecContext(ctx, seedRegionalForecastPeriod, arg.DnoRegionID, arg.StartTime, arg.Data)
	return err
}
