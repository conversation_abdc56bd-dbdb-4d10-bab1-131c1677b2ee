// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: projections.sql

package sqlc

import (
	"context"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

const deleteAuditedChargeProjectionByChargeUUID = `-- name: DeleteAuditedChargeProjectionByChargeUUID :exec
DELETE
FROM projections.audited_charges
WHERE aggregate_id = $1
`

func (q *Queries) DeleteAuditedChargeProjectionByChargeUUID(ctx context.Context, aggregateID uuid.UUID) error {
	_, err := q.db.ExecContext(ctx, deleteAuditedChargeProjectionByChargeUUID, aggregateID)
	return err
}

const deleteChargeProjectionByChargeUUID = `-- name: DeleteChargeProjectionByChargeUUID :exec
DELETE
FROM projections.charges
WHERE charge_uuid = $1
`

func (q *Queries) DeleteChargeProjectionByChargeUUID(ctx context.Context, chargeUuid uuid.UUID) error {
	_, err := q.db.ExecContext(ctx, deleteChargeProjectionByChargeUUID, chargeUuid)
	return err
}

const deleteChargeProjectionByChargeUUIDs = `-- name: DeleteChargeProjectionByChargeUUIDs :exec
DELETE
FROM projections.charges
WHERE charge_uuid = ANY ($1::uuid[])
`

func (q *Queries) DeleteChargeProjectionByChargeUUIDs(ctx context.Context, dollar_1 []uuid.UUID) error {
	_, err := q.db.ExecContext(ctx, deleteChargeProjectionByChargeUUIDs, pq.Array(dollar_1))
	return err
}

const deleteSiteStatsMonthlyProjectionBySiteName = `-- name: DeleteSiteStatsMonthlyProjectionBySiteName :exec
DELETE
FROM projections.site_stats_monthly
WHERE site_name = $1
`

func (q *Queries) DeleteSiteStatsMonthlyProjectionBySiteName(ctx context.Context, siteName string) error {
	_, err := q.db.ExecContext(ctx, deleteSiteStatsMonthlyProjectionBySiteName, siteName)
	return err
}

const deleteSiteStatsMonthlyProjectionBySiteUUID = `-- name: DeleteSiteStatsMonthlyProjectionBySiteUUID :exec
DELETE
FROM projections.site_stats_monthly
WHERE site_id = $1
`

func (q *Queries) DeleteSiteStatsMonthlyProjectionBySiteUUID(ctx context.Context, siteID uuid.UUID) error {
	_, err := q.db.ExecContext(ctx, deleteSiteStatsMonthlyProjectionBySiteUUID, siteID)
	return err
}

const findAuditedChargeProjectionByChargeUUID = `-- name: FindAuditedChargeProjectionByChargeUUID :one
SELECT id, aggregate_id, location_id, charging_duration, plugged_in_duration, claim_charge_id, started_at, plugged_in_at, billed, energy_kwh, group_name, authoriser_id, authoriser_type, ppid, door, charge_cycle_id, supports_ocpp, group_id
FROM projections.audited_charges
WHERE aggregate_id = $1
`

func (q *Queries) FindAuditedChargeProjectionByChargeUUID(ctx context.Context, aggregateID uuid.UUID) (ProjectionsAuditedCharge, error) {
	row := q.db.QueryRowContext(ctx, findAuditedChargeProjectionByChargeUUID, aggregateID)
	var i ProjectionsAuditedCharge
	err := row.Scan(
		&i.ID,
		&i.AggregateID,
		&i.LocationID,
		&i.ChargingDuration,
		&i.PluggedInDuration,
		&i.ClaimChargeID,
		&i.StartedAt,
		&i.PluggedInAt,
		&i.Billed,
		&i.EnergyKwh,
		&i.GroupName,
		&i.AuthoriserID,
		&i.AuthoriserType,
		&i.Ppid,
		&i.Door,
		&i.ChargeCycleID,
		&i.SupportsOcpp,
		&i.GroupID,
	)
	return i, err
}

const findByChargeID = `-- name: FindByChargeID :one
SELECT id, charge_uuid, started_at, ended_at, energy_total, charge_duration_total, charger_id, door, plugged_in_at, unplugged_at, settlement_amount, settlement_currency, expensed_to_group, expensed_to, charger_name, charger_type, site_name, group_id, site_id, group_name, energy_cost, energy_cost_currency, charger_timezone, user_ids, generation_energy_total, grid_energy_total, confirmed, reward_eligible_energy, vehicle_id
FROM projections.charges
WHERE charge_uuid = $1
`

func (q *Queries) FindByChargeID(ctx context.Context, chargeUuid uuid.UUID) (ProjectionsCharge, error) {
	row := q.db.QueryRowContext(ctx, findByChargeID, chargeUuid)
	var i ProjectionsCharge
	err := row.Scan(
		&i.ID,
		&i.ChargeUuid,
		&i.StartedAt,
		&i.EndedAt,
		&i.EnergyTotal,
		&i.ChargeDurationTotal,
		&i.ChargerID,
		&i.Door,
		&i.PluggedInAt,
		&i.UnpluggedAt,
		&i.SettlementAmount,
		&i.SettlementCurrency,
		&i.ExpensedToGroup,
		&i.ExpensedTo,
		&i.ChargerName,
		&i.ChargerType,
		&i.SiteName,
		&i.GroupID,
		&i.SiteID,
		&i.GroupName,
		&i.EnergyCost,
		&i.EnergyCostCurrency,
		&i.ChargerTimezone,
		pq.Array(&i.UserIds),
		&i.GenerationEnergyTotal,
		&i.GridEnergyTotal,
		&i.Confirmed,
		&i.RewardEligibleEnergy,
		&i.VehicleID,
	)
	return i, err
}
