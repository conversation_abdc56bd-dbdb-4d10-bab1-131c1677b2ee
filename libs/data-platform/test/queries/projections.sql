-- name: DeleteChargeProjectionByChargeUUID :exec
DELETE
FROM projections.charges
WHERE charge_uuid = $1;

-- name: DeleteChargeProjectionByChargeUUIDs :exec
DELETE
FROM projections.charges
WHERE charge_uuid = ANY ($1::uuid[]);

-- name: DeleteSiteStatsMonthlyProjectionBySiteUUID :exec
DELETE
FROM projections.site_stats_monthly
WHERE site_id = $1;


-- name: DeleteSiteStatsMonthlyProjectionBySiteName :exec
DELETE
FROM projections.site_stats_monthly
WHERE site_name = $1;

-- name: FindByChargeID :one
SELECT *
FROM projections.charges
WHERE charge_uuid = $1;

-- name: FindAuditedChargeProjectionByChargeUUID :one
SELECT *
FROM projections.audited_charges
WHERE aggregate_id = $1;

-- name: DeleteAuditedChargeProjectionByChargeUUID :exec
DELETE
FROM projections.audited_charges
WHERE aggregate_id = $1;
