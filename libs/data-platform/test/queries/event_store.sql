-- name: DeleteEventByAggregateID :exec
DELETE
FROM event_store.events
WHERE aggregate_id = $1;

-- name: DeleteEventByAggregateIDs :exec
DELETE
FROM event_store.events
WHERE aggregate_id = ANY ($1::uuid[]);

-- name: RetrieveEventByAggregateIDAndEventType :one
SELECT data
FROM event_store.events
WHERE aggregate_id = $1
  AND data ->> 'event'::text = $2::text;

-- name: StartProcessingSubscription :exec
INSERT INTO event_store.event_subscriptions (subscription_name, last_transaction_id, last_event_id)
SELECT $1,
       MAX(event_store.event_subscriptions.last_transaction_id),
       MAX(event_store.event_subscriptions.last_event_id)
FROM event_store.event_subscriptions
ON CONFLICT DO NOTHING;

-- name: StopProcessingSubscription :exec
DELETE
FROM event_store.event_subscriptions
WHERE subscription_name = $1;
