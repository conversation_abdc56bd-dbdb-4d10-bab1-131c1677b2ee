-- name: DeleteSubmittedChargesInIDRange :exec
DELETE
FROM commercial.submitted_charges
WHERE id BETWEEN $1 AND $2;

-- name: DeleteSubmittedChargesInOrganisationIDRange :exec
DELETE
FROM commercial.submitted_charges
WHERE organisation_id BETWEEN $1 AND $2;

-- name: DeleteSubmittedChargesInDriverIDRange :exec
DELETE
FROM commercial.submitted_charges
WHERE driver_id BETWEEN $1 AND $2;

-- name: DeleteSubmittedCharge :exec
DELETE
FROM commercial.submitted_charges
WHERE id = $1;

-- name: CreateSubmittedCharge :one
INSERT INTO commercial.submitted_charges (id, organisation_id, driver_id, charge_id, submitted_at, created_by,
                                          created_at, status)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: CreateSubmittedChargeProcessed :one
INSERT INTO commercial.submitted_charges (id, organisation_id, driver_id, charge_id, submitted_at, created_by,
                                          created_at, status, processed_by, processed_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
ON CONFLICT DO NOTHING
RETURNING *;

-- name: DeleteSubmittedChargesForChargeId :exec
DELETE
FROM commercial.submitted_charges
WHERE charge_id = $1;

-- name: DeleteSubmittedChargeForOrganisationId :exec
DELETE
FROM commercial.submitted_charges
WHERE organisation_id = $1;

-- duplicated production query
-- name: SetStatusOnSubmittedChargesById :many
UPDATE commercial.submitted_charges
SET status       = $1,
    processed_by = $2,
    processed_at = $3
WHERE id = ANY (@id::bigint[])
  AND status != $1
RETURNING *;
