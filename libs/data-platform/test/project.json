{"name": "data-platform-test", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-platform/test", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["go run libs/shared/go/sqlc/generate.go -config libs/data-platform/test/sqlc.yaml"], "parallel": false}}, "test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}}, "tags": ["data-platform"]}