package seeding

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/random"
	"experience/libs/data-platform/test/fixtures"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/numbers"
	"testing"

	"github.com/stretchr/testify/assert"
)

type PodadminBillingEventOption func(p *sqlcpodadmin.CreatePodadminBillingEventDefaultParams)

func WithSettlementAmount(settlementAmount int) PodadminBillingEventOption {
	return func(p *sqlcpodadmin.CreatePodadminBillingEventDefaultParams) {
		convertedSettlementAmount := int32(settlementAmount)
		p.SettlementAmount = convertedSettlementAmount
	}
}

func WithPresentmentAmount(presentmentAmount int) PodadminBillingEventOption {
	return func(p *sqlcpodadmin.CreatePodadminBillingEventDefaultParams) {
		convertedPresentmentAmount := int32(presentmentAmount)
		p.PresentmentAmount = convertedPresentmentAmount
	}
}

func PodadminCharge(t *testing.T, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, billingEventOptions ...PodadminBillingEventOption) (chargeID int64, cleanup func()) {
	t.Helper()
	ctx := context.Background()

	podadminPodUnitID, err := podadminQueries.InsertPodadminUnitReturning(ctx, random.ChargerID())
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Units, podadminPodUnitID)
	podUnitID, err := numbers.Convert[int64, uint32](podadminPodUnitID)
	assert.NoError(t, err)

	billingEventParams := &sqlcpodadmin.CreatePodadminBillingEventDefaultParams{}
	for _, billingEventOption := range billingEventOptions {
		billingEventOption(billingEventParams)
	}
	podadminBillingEventID, err := podadminQueries.InsertPodadminBillingEventReturning(ctx, billingEventParams)
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.BillingEvents, podadminBillingEventID)
	billingEventID, err := numbers.Convert[int64, int32](podadminBillingEventID)
	assert.NoError(t, err)

	chargeID, err = podadminQueries.InsertPodadminChargeReturning(ctx, &sqlcpodadmin.CreatePodadminChargeDefaultParams{
		BillingEventID: sql.NullInt32{
			Int32: billingEventID,
			Valid: true,
		},
		UnitID: podUnitID,
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Charges, chargeID)

	return chargeID, func() {
		t.Helper()
		podadminDBState.Cleanup(ctx)
	}
}
