package seeding

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/sqlc"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

//nolint:thelper // if the seeding fails, we want to know which seeding failed
func WalletBilling(ctx context.Context, t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, isLocal bool, config *PodadminHelperConfig) (cleanup func(), chargeID, billingAccountID, authoriserID int64) {
	podadminHelper := NewPodadminHelper(podadminQueries, podadminDBState, isLocal, config, nil)
	podadminHelper.CreateCharge(ctx, t)
	podadminData := podadminHelper.ReturnSeededIDs()

	if isLocal {
		startsAt := time.Now().UTC().Add(-time.Hour * 7 * 24)
		endsAt := startsAt.Add(time.Hour * 1)
		_, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(
			*podadminData.ChargeID,
			sqlc.PodpointPodLocation{},
			sqlc.PodpointPodUnit{ID: *podadminData.UnitID},
			sqlc.PodpointBillingEvent{},
			0,
			"0",
			&startsAt,
			&endsAt,
			0,
			0,
			1,
			*podadminData.BillingAccountID,
		))
		assert.NoError(t, err)

		_, err = queries.CreateAuthoriser(ctx, fixtures.CreateAuthoriserParams(*podadminData.AuthoriserID, *podadminData.AuthoriserUID, "user", nil))
		assert.NoError(t, err)

		_, err = queries.CreateUser(ctx, fixtures.CreateUserParams(*podadminData.UserID, *podadminData.AuthoriserUID, sqlc.PodpointGroup{}, "<EMAIL>", "John", "Doe"))
		assert.NoError(t, err)

		_, err = queries.CreateBillingAccount(ctx, sqlc.CreateBillingAccountParams{
			ID:       *podadminData.BillingAccountID,
			UserID:   sql.NullInt64{Int64: *podadminData.UserID, Valid: true},
			Balance:  *config.BillingAccountBalance,
			Currency: "GBP",
		})
		assert.NoError(t, err)
	}

	return func() {
		t.Helper()
		podadminDBState.Cleanup(ctx)
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)

		// the below postgres data is aligned with podadmin ID's so have to delete separately as the IDs are out of our set postgres range
		if isLocal {
			assert.NoError(t, queries.DeleteCharge(ctx, *podadminData.ChargeID))
			assert.NoError(t, queries.DeleteAuthoriser(ctx, *podadminData.AuthoriserID))
			assert.NoError(t, queries.DeleteUser(ctx, *podadminData.UserID))
			assert.NoError(t, queries.DeleteBillingAccount(ctx, *podadminData.BillingAccountID))
		}
	}, *podadminData.ChargeID, *podadminData.BillingAccountID, *podadminData.AuthoriserID
}
