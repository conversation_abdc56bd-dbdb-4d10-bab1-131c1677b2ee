package seeding

import (
	"context"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/sqlc"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/service/utils"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/assert"

	"github.com/google/uuid"
)

func createCommonParts(t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState) (podadminChargeOne, podadminChargeTwo int64, unit sqlc.PodpointPodUnit, location sqlc.PodpointPodLocation, groupUUID uuid.UUID) {
	t.Helper()
	ctx := context.Background()
	groupUUID = uuid.New()

	// podadmin seeding
	podadminGroupID, err := podadminQueries.InsertPodadminGroupReturning(ctx, groupUUID)
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Groups, podadminGroupID)

	podadminPodUnitID, err := podadminQueries.InsertPodadminUnitReturning(ctx, "PP-10001")
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Units, podadminPodUnitID)

	podadminChargeOne, err = podadminQueries.InsertPodadminChargeReturning(ctx, fixtures.CreatePodadminChargeDefaultParams(int(podadminPodUnitID)))
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Charges, podadminChargeOne)

	podadminChargeTwo, err = podadminQueries.InsertPodadminChargeReturning(ctx, fixtures.CreatePodadminChargeDefaultParams(int(podadminPodUnitID)))
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Charges, podadminChargeTwo)

	// ---------------------------------

	unit, err = queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	address, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	assert.NoError(t, err)

	location, err = queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address, &unit, true, false))
	assert.NoError(t, err)

	return podadminChargeOne, podadminChargeTwo, unit, location, groupUUID
}

func ChargesForExpenseSubmitting(t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, driverID int) func() error {
	t.Helper()
	ctx := context.Background()
	driverUUID := utils.FabricateUUIDFromNumericID(driverID)

	podadminChargeOne, podadminChargeTwo, unit, location, groupUUID := createCommonParts(t, queries, podadminQueries, podadminDBState)

	organisation, err := queries.CreateOrganisation(ctx,
		fixtures.CreateOrganisationParams(0, groupUUID.String(), "Dunder Mifflin"))
	assert.NoError(t, err)

	user, err := queries.CreateUser(ctx,
		fixtures.CreateUserParams(fixtures.Sequence.Get(), driverUUID.String(), organisation, "<EMAIL>", "John", "Smith"))
	assert.NoError(t, err)

	billingAccount, err := queries.CreateBillingAccount(ctx,
		fixtures.CreateBillingAccountParams(user.ID, 0, "GBP"))
	assert.NoError(t, err)

	billingEventOne, err := queries.CreateBillingEvent(ctx,
		fixtures.CreateBillingEventParams(100, user.ID))
	assert.NoError(t, err)

	_, err = queries.CreateCharge(ctx, fixtures.CreateChargeParams(podadminChargeOne, location, unit, billingEventOne, 150, "1.5", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 3, 1, 1, billingAccount.ID))
	assert.NoError(t, err)

	billingEventTwo, err := queries.CreateBillingEvent(ctx,
		fixtures.CreateBillingEventParams(100, user.ID))
	assert.NoError(t, err)

	_, err = queries.CreateCharge(ctx, fixtures.CreateChargeParams(podadminChargeTwo, location, unit, billingEventTwo, 140, "1.4", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 3, 1, 1, billingAccount.ID))
	assert.NoError(t, err)

	return func() error {
		t.Helper()
		podadminDBState.Cleanup(ctx)
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}
}

type DriverChargeInput struct {
	StartsAt time.Time
	EndsAt   time.Time
}

// DriverCharges seeds the database with a new user, organisation and billing
// account. For each of the passed in `charges`, a charge with a separate
// billing event is created.
func DriverCharges(t *testing.T, queries *sqlc.Queries, driverID, organisationID uuid.UUID, charges []DriverChargeInput) (func() error, error) {
	t.Helper()
	ctx := context.Background()

	organisation, err := queries.CreateOrganisation(ctx,
		fixtures.CreateOrganisationParams(0, organisationID.String(), "Dunder Mifflin"))
	assert.NoError(t, err)

	unit, _, location, _ := fixtures.PrepareChargeData(ctx, t, queries, true, false, organisation.ID)

	user, err := queries.CreateUser(ctx,
		fixtures.CreateUserParams(fixtures.Sequence.Get(), driverID.String(), organisation, "<EMAIL>", "John", "Smith"))
	assert.NoError(t, err)

	billingAccount, err := queries.CreateBillingAccount(ctx,
		fixtures.CreateBillingAccountParams(user.ID, 0, "GBP"))
	assert.NoError(t, err)

	for _, charge := range charges {
		billingEvent, err := queries.CreateBillingEvent(ctx,
			fixtures.CreateBillingEventParams(100, user.ID))
		assert.NoError(t, err)
		_, err = queries.CreateCharge(ctx,
			fixtures.CreateChargeParams(0, location, unit, billingEvent, 15, "3.4", &charge.StartsAt, &charge.EndsAt, 3, organisation.ID, 1, billingAccount.ID))
		assert.NoError(t, err)
	}

	return func() error {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}, nil
}

func OrganisationDriverCharges(t *testing.T, queries *sqlc.Queries, driverIDs []uuid.UUID, organisationID uuid.UUID) (func() error, error) {
	t.Helper()
	ctx := context.Background()
	startsAt := time.Date(2023, time.April, 8, 21, 34, 0, 0, time.UTC)
	endsAt := time.Date(2023, time.April, 8, 22, 34, 0, 0, time.UTC)

	organisation, err := queries.CreateOrganisation(ctx,
		fixtures.CreateOrganisationParams(0, organisationID.String(), "Dunder Mifflin"))
	assert.NoError(t, err)

	unit, _, location, _ := fixtures.PrepareChargeData(ctx, t, queries, true, false, organisation.ID)

	for _, u := range driverIDs {
		user, err := queries.CreateUser(ctx,
			fixtures.CreateUserParams(fixtures.Sequence.Get(), u.String(), organisation, "<EMAIL>", "John", "Smith"))
		assert.NoError(t, err)
		billingAccount, err := queries.CreateBillingAccount(ctx,
			fixtures.CreateBillingAccountParams(user.ID, 0, "GBP"))
		assert.NoError(t, err)

		billingEvent, err := queries.CreateBillingEvent(ctx,
			fixtures.CreateBillingEventParams(100, user.ID))
		assert.NoError(t, err)

		_, err = queries.CreateCharge(ctx,
			fixtures.CreateChargeParams(0, location, unit, billingEvent, 15, "3.4", &startsAt, &endsAt, 3, organisation.ID, 1, billingAccount.ID))
		assert.NoError(t, err)
	}

	return func() error {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}, nil
}
