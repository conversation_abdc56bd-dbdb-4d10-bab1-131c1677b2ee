package seeding

import (
	"context"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/sqlc"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func UsageData(t *testing.T, queries *sqlc.Queries, groupUID string, siteID int64, fromDate, toDate time.Time, locationID int64) (func() error, error) {
	t.Helper()
	ctx := context.Background()

	group, err := queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, groupUID, "Test Organisation"))
	assert.NoError(t, err)

	unit1, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	unit2, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	unit3, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	address1, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(siteID, group.ID))
	assert.NoError(t, err)

	address2, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0, group.ID))
	assert.NoError(t, err)

	location1, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address1, &unit1, false, false, locationID))
	assert.NoError(t, err)

	location2, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address1, &unit2, false, false))
	assert.NoError(t, err)

	location3, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address2, &unit3, false, false))
	assert.NoError(t, err)

	billingEvent1, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 1))
	assert.NoError(t, err)

	billingEvent2, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 1))
	assert.NoError(t, err)

	billingEvent3, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 2))
	assert.NoError(t, err)

	billingEvent4, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 3))
	assert.NoError(t, err)

	startsAt := fromDate.Add(time.Hour * 1)
	charge1, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location1, unit1, billingEvent1, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 1))
	assert.NoError(t, err)
	// Updates the charges lookup table with a charge
	err = queries.CloseCharge(ctx, charge1.ID)
	assert.NoError(t, err)

	charge2, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location2, unit2, billingEvent2, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 1))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge2.ID)
	assert.NoError(t, err)

	charge3, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location1, unit1, billingEvent3, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 2))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge3.ID)
	assert.NoError(t, err)

	charge4, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location3, unit3, billingEvent4, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 3))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge4.ID)
	assert.NoError(t, err)

	return func() error {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}, nil
}
