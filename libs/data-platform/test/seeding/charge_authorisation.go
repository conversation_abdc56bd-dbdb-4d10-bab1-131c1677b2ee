//nolint:thelper // if the seeding fails, we want to know which seeding failed
package seeding

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/random"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/sqlc"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/numbers"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func ChargeAuthorisationRFID(ctx context.Context, t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, groupUUID uuid.UUID, charger *random.Charger, isLocal bool, rfidTestTag string) (cleanup func(), authoriserID int64) {
	podadminGroupID, err := podadminQueries.InsertPodadminGroupReturning(ctx, groupUUID)
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Groups, podadminGroupID)

	podadminBillingAccountID, err := podadminQueries.InsertPodadminBillingAccountReturning(ctx, &sqlcpodadmin.CreatePodadminBillingAccountDefaultParams{
		Uid:          groupUUID.String(),
		BusinessName: t.Name(),
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.BillingAccounts, podadminBillingAccountID)

	groupID, err := numbers.Convert[int64, uint32](podadminGroupID)
	assert.NoError(t, err)
	billingAccountID, err := numbers.Convert[int64, uint32](podadminBillingAccountID)
	assert.NoError(t, err)
	podadminGroupBillingAccountID, err := podadminQueries.InsertPodadminGroupBillingAccountReturning(ctx, &sqlcpodadmin.CreatePodadminGroupBillingAccountDefaultParams{
		GroupID:          groupID,
		BillingAccountID: billingAccountID,
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.GroupBillingAccounts, podadminGroupBillingAccountID)

	podadminAuthoriserID, err := podadminQueries.InsertPodadminAuthoriserWithGroupDefaultReturning(ctx, &sqlcpodadmin.CreatePodadminAuthoriserWithGroupDefaultParams{
		Uid:      rfidTestTag,
		Type:     "rfid",
		GroupUid: sql.NullString{String: groupUUID.String(), Valid: true},
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Authorisers, podadminAuthoriserID)

	// we have doors and address types on podadmin staging but don't have any when running locally
	if isLocal {
		podadminDoorID := 1
		err = podadminQueries.CreatePodadminDoor(ctx, fixtures.CreatePodadminDoorParams(uint32(podadminDoorID), "A"))
		assert.NoError(t, err)
		podadminDBState.AppendID(fixtures.Doors, int64(podadminDoorID))

		podadminAddressTypeID := 2
		err = podadminQueries.CreatePodadminAddressType(ctx, uint32(podadminAddressTypeID))
		assert.NoError(t, err)
		podadminDBState.AppendID(fixtures.AddressTypes, int64(podadminAddressTypeID))
	}

	groupIDInt32, err := numbers.Convert[int64, int32](podadminGroupID)
	assert.NoError(t, err)
	podadminAddressID, err := podadminQueries.InsertPodadminAddressReturning(ctx, &sqlcpodadmin.CreatePodadminAddressDefaultParams{
		GroupID:      sql.NullInt32{Int32: groupIDInt32, Valid: true},
		BusinessName: t.Name(),
		Line1:        "",
		Line2:        "",
		PostalTown:   "",
		Postcode:     "",
		Country:      "",
		Description:  "",
		TypeID:       2,
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Addresses, podadminAddressID)
	addressID, err := numbers.Convert[int64, uint32](podadminAddressID)
	assert.NoError(t, err)

	podadminUnit, err := podadminQueries.InsertPodadminUnitReturning(ctx, charger.ID)
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Units, podadminUnit)
	unitID, err := numbers.Convert[int64, int32](podadminUnit)
	assert.NoError(t, err)

	podadminLocationID, err := podadminQueries.InsertPodadminLocationReturning(ctx, &sqlcpodadmin.CreatePodadminLocationDefaultParams{
		Uuid:               uuid.NewString(),
		AddressID:          addressID,
		Longitude:          "",
		Latitude:           "",
		Description:        "",
		PaygEnabled:        false,
		ContactlessEnabled: false,
		MidmeterEnabled:    false,
		IsPublic:           false,
		IsHome:             false,
		IsEvZone:           false,
		UnitID:             sql.NullInt32{Int32: unitID, Valid: true},
	})
	assert.NoError(t, err)
	podadminDBState.AppendID(fixtures.Locations, podadminLocationID)

	// if local seed to postgres otherwise rely on DMS to replicate the data over from podadmin
	var ppLocation *sqlc.PodpointPodLocation
	if isLocal {
		_, err = queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(podadminGroupID, groupUUID.String(), "GroupName"))
		assert.NoError(t, err)

		_, err = queries.CreateBillingAccount(ctx, sqlc.CreateBillingAccountParams{
			ID: podadminBillingAccountID,
		})
		assert.NoError(t, err)

		_, err = queries.CreateGroupBillingAccount(ctx, sqlc.CreateGroupBillingAccountParams{
			ID:               podadminGroupBillingAccountID,
			GroupID:          podadminGroupID,
			BillingAccountID: podadminBillingAccountID,
		})
		assert.NoError(t, err)

		_, err = queries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{
			ID:       podadminAuthoriserID,
			Uid:      rfidTestTag,
			Type:     "rfid",
			GroupUid: sql.NullString{String: groupUUID.String(), Valid: true},
		})
		assert.NoError(t, err)

		address, err := queries.CreateAddress(ctx, sqlc.CreateAddressParams{
			ID:           podadminAddressID,
			BusinessName: t.Name(),
			GroupID:      nullableGroupID(&podadminGroupID),
		})
		assert.NoError(t, err)

		unit, err := queries.CreateUnit(ctx, sqlc.CreateUnitParams{ID: podadminUnit, Ppid: charger.ID, Name: sql.NullString{String: *charger.Name, Valid: true}})
		assert.NoError(t, err)

		location, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address, &unit, true, false, podadminLocationID))
		ppLocation = &location
		assert.NoError(t, err)
	}

	return func() {
		t.Helper()
		podadminDBState.Cleanup(ctx)
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)

		// the below postgres data is aligned with podadmin ID's so have to delete separately as the ID's are out of our set postgres range
		if isLocal {
			assert.NoError(t, queries.DeleteGroupBillingAccount(ctx, podadminGroupBillingAccountID))
			assert.NoError(t, queries.DeleteBillingAccount(ctx, podadminBillingAccountID))
			assert.NoError(t, queries.DeleteGroup(ctx, podadminGroupID))
			assert.NoError(t, queries.DeleteAuthoriser(ctx, podadminAuthoriserID))
			assert.NoError(t, queries.DeleteUnit(ctx, podadminUnit))
			if ppLocation != nil {
				assert.NoError(t, queries.DeleteLocation(ctx, ppLocation.ID))
			}
			assert.NoError(t, queries.DeleteAddress(ctx, podadminAddressID))
		}
	}, podadminAuthoriserID
}

func ChargeAuthorisationOCPI(ctx context.Context, t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, isLocal bool, config *PodadminHelperConfig) (cleanup func(), authoriserID int64, chargerID *string) {
	podadminHelper := NewPodadminHelper(podadminQueries, podadminDBState, isLocal, config, queries)
	podadminHelper.CreateLocation(ctx, t)
	podadminHelper.CreateDoor(ctx, t)
	podadminHelper.CreateAuthoriser(ctx, t)

	podadminData := podadminHelper.ReturnSeededIDs()

	return func() {
		t.Helper()
		podadminDBState.Cleanup(ctx)
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)

		// the below postgres data is aligned with podadmin ID's so have to delete separately as the ID's are out of our set postgres range
		if isLocal {
			assert.NoError(t, queries.DeleteAuthoriser(ctx, *podadminData.AuthoriserID))
			assert.NoError(t, queries.DeleteUnit(ctx, *podadminData.UnitID))
			assert.NoError(t, queries.DeleteLocation(ctx, *podadminData.LocationID))
		}
	}, *podadminData.AuthoriserID, &podadminData.PPID
}

func ChargeAuthorisationGuest(ctx context.Context, t *testing.T, queries *sqlc.Queries, podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, isLocal bool, config *PodadminHelperConfig) (cleanup func(), billingEventID int64, chargerID string) {
	podadminHelper := NewPodadminHelper(podadminQueries, podadminDBState, isLocal, config, queries)
	podadminHelper.CreateLocation(ctx, t)
	podadminHelper.CreateDoor(ctx, t)
	podadminHelper.CreateBillingEvent(ctx, t)

	_, err := queries.RetrieveGuestAuthoriser(ctx)
	if errors.Is(err, sql.ErrNoRows) {
		podadminHelper.CreateAuthoriser(ctx, t)
	} else {
		assert.NoError(t, err)
	}

	podadminData := podadminHelper.ReturnSeededIDs()

	return func() {
		t.Helper()
		podadminDBState.Cleanup(ctx)
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)

		// the below postgres data is aligned with podadmin ID's so have to delete separately as the ID's are out of our set postgres range
		if isLocal {
			assert.NoError(t, queries.DeleteAuthoriser(ctx, *podadminData.AuthoriserID))
			assert.NoError(t, queries.DeleteUnit(ctx, *podadminData.UnitID))
			assert.NoError(t, queries.DeleteLocation(ctx, *podadminData.LocationID))
			assert.NoError(t, queries.DeleteBillingEvent(ctx, *podadminData.BillingEventID))
		}
	}, *podadminData.BillingEventID, podadminData.PPID
}
