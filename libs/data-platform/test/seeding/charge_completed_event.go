package seeding

import (
	"context"
	"encoding/json"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/sqs"
	"fmt"
	"testing"

	"github.com/google/uuid"
)

func Event(ctx context.Context, t *testing.T, chargeNotificationSQSClient *sqs.Client, evt eventstore.Event) error {
	t.Helper()

	messageBody, err := json.Marshal(evt)
	if err != nil {
		return err
	}

	var eventType struct {
		Event eventstore.EventType `json:"event"`
	}
	err = json.Unmarshal(messageBody, &eventType)
	if err != nil {
		return err
	}

	snsWrappedPayload := fmt.Sprintf("{ \"Type\" : \"Notification\", \"MessageId\" : %q,  \"Message\" : %q}", uuid.New().String(), messageBody)

	fmt.Printf("sending %q event for aggregate %s; test %s\n", eventType.Event, evt.GetAggregateID().String(), t.Name())
	_, err = chargeNotificationSQSClient.SendMessage(ctx, snsWrappedPayload)
	return err
}

func Command(ctx context.Context, t *testing.T, chargeNotificationSQSClient *sqs.Client, cmd eventstore.Command) error {
	t.Helper()

	messageBody, err := json.Marshal(cmd)
	if err != nil {
		return err
	}

	_, err = chargeNotificationSQSClient.SendMessage(ctx, string(messageBody))
	return err
}
