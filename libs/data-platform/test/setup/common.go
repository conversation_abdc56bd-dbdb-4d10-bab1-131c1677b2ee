package setup

import (
	"github.com/stretchr/testify/require"

	"testing"
)

var hostToAddress = map[string]string{
	"local": "http://0.0.0.0:2830",
	"ecs":   "http://data-platform-api.destination.cluster.com:2830",
}

func BaseURL(t *testing.T, host string) string {
	t.Helper()
	require.Containsf(t, []string{"local", "ecs"}, host, "invalid host argument: %q (valid hosts: local|ecs)", host)
	return hostToAddress[host]
}
