package energycost

import (
	costcalculation "experience/libs/data-platform/cost-calculation"
	"experience/libs/shared/go/service"
	"math"
	"time"

	"k8s.io/utils/ptr"

	"github.com/biter777/countries"
)

var noCost = uint32(0)
var tariffPrecision = 10000
var defaultUKTariff = 14 * tariffPrecision

func NewEnergyCostCalculator() Calculator {
	return &energyCostCalculator{}
}

type energyCostCalculator struct {
}

func (e *energyCostCalculator) EnergyCost(address *Address, charge *costcalculation.Charge) uint32 {
	if address == nil || charge == nil || charge.DateRange.To == nil {
		return noCost
	}

	if charge.DateRange.DurationSeconds() < 10 {
		return noCost
	}

	if !hasTariffTiers(address.Tariff) && address.Country != countries.UnitedKingdom.Alpha2() {
		return noCost
	}

	if !hasTariffTiers(address.Tariff) && address.Country == countries.UnitedKingdom.Alpha2() {
		return uint32(math.Round(float64(defaultUKTariff) * charge.Energy / float64(tariffPrecision)))
	}

	if hasSingleTariffTier(address.Tariff) {
		tariffTier := address.Tariff.Tiers[0]
		return uint32(math.Round(float64(tariffTier.Rate) * charge.Energy / float64(tariffPrecision)))
	}
	return costForMultipleTariffTiers(address.Tariff, charge)
}

func hasTariffTiers(tariff Tariff) bool {
	return len(tariff.Tiers) > 0
}

func hasSingleTariffTier(tariff Tariff) bool {
	return len(tariff.Tiers) == 1
}

func costForMultipleTariffTiers(tariff Tariff, charge *costcalculation.Charge) uint32 {
	var totalCost float64
	for _, tier := range tariff.Tiers {
		tierRanges := getTariffTierRanges(charge, tier, tariff.Timezone)

		if len(charge.ChargingStates()) == 0 {
			for _, tierRange := range tierRanges {
				if !thisTierRangeHasApplicableDay(charge.DateRange.From.Weekday(), tier.Days) || !charge.DateRange.Intersects(tierRange.DateRange) {
					continue
				}

				duration := charge.DateRange.DurationSeconds()

				intersection := charge.DateRange.IntersectionTime(tierRange.DateRange)
				energyIntersection := charge.Energy * intersection / float64(duration)

				totalCost += costOfEnergy(energyIntersection, tierRange.Rate)
			}
		} else {
			// Get the cost for this tariff tier by checking any charging states it overlaps with
			for _, tierRange := range tierRanges {
				states := calculateTierRangeCostForChargingStates(*tierRange, charge)
				totalCost += states
			}
		}
	}
	return uint32(math.Round(totalCost / float64(tariffPrecision)))
}

func calculateTierRangeCostForChargingStates(tariffTierRange TariffTierRange, charge *costcalculation.Charge) float64 {
	var costForTierRange float64
	var cumulativeEnergy float64

	states := charge.ChargingStates()
	for i, chargingState := range states {
		// Calculate energy delivered during this charging state
		var currentEnergy float64
		if i == len(states)-1 {
			currentEnergy = charge.Energy
		} else {
			currentEnergy = chargingState.Energy
		}

		stateEnergy := currentEnergy - cumulativeEnergy
		cumulativeEnergy = currentEnergy

		stateRange, err := chargingState.DateRange()
		// Charging state start/end time is invalid, ignore
		if err != nil {
			continue
		}

		if thisTierRangeHasApplicableDay(stateRange.From.Weekday(), tariffTierRange.Days) && stateRange.Intersects(tariffTierRange.DateRange) {
			costForTierRange += calculateStateCostForTariffRange(tariffTierRange, stateRange, stateEnergy)
		}
	}
	return costForTierRange
}

func calculateStateCostForTariffRange(tariffTier TariffTierRange, stateRange *service.DateRange, stateEnergy float64) float64 {
	// Charging state intersects the tariff tier range by some amount, calculate how much
	overlapDuration := stateRange.IntersectionTime(tariffTier.DateRange)
	stateDuration := stateRange.DurationSeconds()

	if float64(stateDuration) == overlapDuration {
		// Entirety of charging state is within this tariff tier range
		return costOfEnergy(stateEnergy, tariffTier.Rate)
	}
	// Apportion fraction of charging state's energy to this tariff tier range, based on overlap duration
	fractionOfChargeWithinTariffRange := overlapDuration / float64(stateDuration)
	energyApportionedToTariffRange := fractionOfChargeWithinTariffRange * stateEnergy
	return costOfEnergy(energyApportionedToTariffRange, tariffTier.Rate)
}

// costOfEnergy consistently handles cost of: energy (kWh) at a given rate (per kWh), stored as 10^4
func costOfEnergy(energy float64, rate int64) float64 {
	return energy * float64(rate)
}

func getTariffTierRanges(charge *costcalculation.Charge, tariffTier TariffTier, tariffTimezone *time.Location) []*TariffTierRange {
	var tierRanges []*TariffTierRange

	// If no tier begin and end, the tier range is the full length of the charge
	if tariffTier.BeginTime == nil && tariffTier.EndTime == nil {
		return append(tierRanges, &TariffTierRange{
			Rate:      tariffTier.Rate,
			DateRange: charge.DateRange,
			Days:      tariffTier.Days,
		})
	}

	tierDateRange := buildTierRange(charge, tariffTier, tariffTimezone)
	if tierDateRange.To.Equal(*tierDateRange.From) || tierDateRange.To.Before(*tierDateRange.From) {
		// Add a day to the end time if it's a tariff tier that spills over to the next day (e.g. 2am end, need to add a day)
		tierDateRange.To = ptr.To(tierDateRange.To.Add(time.Hour * 24))
	}

	checkShouldStartOnPreviousDay(charge, &tierDateRange)
	checkShouldStartOnNextDay(charge, &tierDateRange)

	// Loop through, adding a day each time and seeing if it overlaps the charge
	for charge.DateRange.Intersects(tierDateRange) {
		// Add tier range and its rate to the slice
		from := *tierDateRange.From
		to := *tierDateRange.To

		tierRanges = append(tierRanges, &TariffTierRange{
			Rate: tariffTier.Rate,
			DateRange: service.DateRange{
				From: &from,
				To:   &to,
			},
			Days: tariffTier.Days,
		})

		tierDateRange.From = ptr.To(tierDateRange.From.Add(time.Hour * 24))
		tierDateRange.To = ptr.To(tierDateRange.To.Add(time.Hour * 24))
	}

	return tierRanges
}

func thisTierRangeHasApplicableDay(weekday time.Weekday, days []time.Weekday) bool {
	if len(days) == 0 || len(days) == 7 {
		return true
	}
	for _, day := range days {
		if day == weekday {
			return true
		}
	}
	return false
}

// checkShouldStartOnPreviousDay will see if the charge intersects the tariff tier on the previous day, if so
// then we must start our iterations from that day.
//
// Example:
// - A charge runs from 04:00-14:00 today.
// - The tariff tier runs from 23:00-05:00.
// - We would currently be looking from 23:00 tonight, until 05:00 tomorrow, which won't overlap.
// - When we subtract a day, we will see it overlaps from 04:00-05:00 this morning.
func checkShouldStartOnPreviousDay(charge *costcalculation.Charge, tierDateRange *service.DateRange) {
	previousDayStart := tierDateRange.From.Add(-time.Hour * 24)
	previousDayEnd := tierDateRange.To.Add(-time.Hour * 24)
	previousDayRange := service.DateRange{
		From: &previousDayStart,
		To:   &previousDayEnd,
	}

	if charge.DateRange.Intersects(previousDayRange) {
		tierDateRange.From = &previousDayStart
		tierDateRange.To = &previousDayEnd
	}
}

// checkShouldStartOnNextDay will see if the charge doesn't intersect on current day, but does on the next day.
//
// Example:
// - A charge runs from 22:00 today - 09:00 tomorrow.
// - The tariff tier runs from 08:00-16:00.
// - We would currently be looking from 08:00 this morning, until 16:00 this afternoon, which won't overlap.
// - When we add a day, we will see it overlaps from 08:00-09:00 tomorrow morning.
func checkShouldStartOnNextDay(charge *costcalculation.Charge, tierDateRange *service.DateRange) {
	if !charge.DateRange.Intersects(*tierDateRange) {
		nextDayStart := tierDateRange.From.Add(time.Hour * 24)
		nextDayEnd := tierDateRange.To.Add(time.Hour * 24)

		tierDateRange.From = &nextDayStart
		tierDateRange.To = &nextDayEnd
	}
}

func buildTierRange(charge *costcalculation.Charge, tariffTier TariffTier, tariffTimezone *time.Location) service.DateRange {
	rangeStart := time.Date(
		charge.DateRange.From.Year(),
		charge.DateRange.From.Month(),
		charge.DateRange.From.Day(),
		tariffTier.BeginTime.Hour(),
		tariffTier.BeginTime.Minute(),
		tariffTier.BeginTime.Second(),
		0,
		tariffTimezone,
	).UTC()

	rangeEnd := time.Date(
		charge.DateRange.From.Year(),
		charge.DateRange.From.Month(),
		charge.DateRange.From.Day(),
		tariffTier.EndTime.Hour(),
		tariffTier.EndTime.Minute(),
		tariffTier.EndTime.Second(),
		0,
		tariffTimezone,
	).UTC()

	return service.DateRange{
		From: &rangeStart,
		To:   &rangeEnd,
	}
}
