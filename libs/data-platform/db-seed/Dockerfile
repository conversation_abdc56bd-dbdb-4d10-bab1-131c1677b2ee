FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/golang:1.24.4-alpine AS builder

# Create app directory
WORKDIR /workspace

## Add wait script for verifying postgres is ready
ADD https://github.com/ufoscout/docker-compose-wait/releases/download/2.9.0/wait /wait
RUN chmod +x /wait

# Copy source files
COPY go.mod go.mod
COPY go.sum go.sum
RUN go mod download
COPY apps/data-platform apps/data-platform
COPY libs/data-platform/ libs/data-platform/
COPY libs/shared/go/ libs/shared/go/

# Build app
RUN go build -o dist/apps/data-platform/db-seed libs/data-platform/db-seed/main.go

FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/alpine:latest@sha256:8a1f59ffb675680d47db6337b49d22281a139e9d709335b492be023728e11715
WORKDIR /app
COPY --from=builder /wait /wait
COPY --from=builder /workspace/dist/apps/data-platform/db-seed ./
COPY --from=builder /workspace/libs/data-platform/db-seed/data ./data
CMD /wait; /app/db-seed
