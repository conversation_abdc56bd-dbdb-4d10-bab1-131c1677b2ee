INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(1, '45c9ef22-b44b-4edd-bb66-392e923950af', 1, 6846, 'Pod Point - Software Team', '<PERSON><PERSON>', '+44(0)3188 203136', 'Pod Point - Software Team', 'Floor 4, Cityside House', '40 Adler Street, Aldgate East', 'London', 'E1 1EE', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(2, 'c9c5d39c-23ba-4a21-ac45-75968fdc055f', 2, 15552, 'Tesco Stores Ltd', '<PERSON><PERSON>', '***********', 'Tesco Stores Ltd', 'Tesco House', 'Shire Park, Kestrel Way', 'Welwyn Garden City', 'AL7 1GA', 'GB', 0.00, '2010-07-29 13:00:00', '2020-01-20 14:29:36', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(30, 'c7668cd2-**************-fcdbf6461e0c', 2, NULL, 'Lex Autolease', 'Ada Nolan', '0727 9904857', 'Lex Autolease', 'Latimer House', 'Anglo Office Park, White Lion Road', 'Amersham', 'HP7 9JQ', 'GB', 0.00, '2011-06-24 16:09:45', '2020-01-20 14:29:37', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(211, '914081a8-c513-417c-8282-8fa1cb1569e0', 4, 7420, 'iPro Solutions Ltd (Manufacturing)', 'Emilia Ryan', '0055750347', 'iPro Solutions Ltd (Manufacturing)', 'Haddenham Business Park ', 'Pegasus Way', 'Aylesbury', 'HP17 8LJ', 'GB', 0.00, '2015-04-23 16:17:30', '2020-01-20 14:29:40', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(188, '780d9f10-47e7-472a-8367-88a852458efe', 3, NULL, 'Savills UK', 'Cora Boyle', '+44(0)026802495', 'Savills UK', '25 Finsbury Circus', 'Moorgate', 'London', 'EC2M 7EE', 'GB', 0.00, '2014-12-17 17:07:44', '2020-01-20 14:29:39', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(451, '087e9b6b-1d60-47a0-89e9-87ff0d54f719', 2, NULL, 'Gallagher Shopping', 'Aidan', '************', 'Gallagher Shopping', 'Tewkesbury Road', 'Cheltenham', 'Gloucestershire', 'GL51 9RR', 'GB', 0.00, '2016-10-24 13:51:37', '2020-01-20 14:29:34', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(206, '6e205495-9f57-474f-a3f5-84351ef02cbb', 1, 6239, 'Pod Point - Operations Team', 'William O’Connell', '(0271) 6833297', 'Pod Point - Operations Team', '145 -157', 'St John Street', 'London', 'EC1V 4PV', 'GB', 0.00, '2015-04-07 16:00:25', '2021-03-23 17:19:38', NULL);
INSERT INTO podpoint.groups(id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES(336, 'b2de66af-834c-4dd6-b119-99d94ffc2abe', 2, NULL, 'Coliseum Shopping Park', 'Ella O''Reilly', '************', 'Coliseum Shopping Park', '30 Coliseum Way', '', 'Ellesmere Port', 'CH65 9HD', 'GB', 0.00, '2016-04-27 15:22:15', '2020-01-20 10:06:52', NULL);
