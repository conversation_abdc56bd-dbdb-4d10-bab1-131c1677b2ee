package walletbilling_test

import (
	"bytes"
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	mockbilling "experience/libs/data-platform/event-sourcing/domain/events/charges/billing/mock"
	"experience/libs/data-platform/event-sourcing/walletbilling"
	mockwalletbilling "experience/libs/data-platform/event-sourcing/walletbilling/mock"
	"experience/libs/shared/go/numbers"
	"fmt"
	"log"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

func TestBill(t *testing.T) {
	testData := createBillTestData()

	tests := []struct {
		name                             string
		billingAccountID                 *int32
		getBillingDataFromChargeError    error
		billingData                      billing.Data
		createdBillingEventID            int64
		createBillingEventError          error
		updateChargeBillingEventIDError  error
		newBillingAccountBalance         int32
		updateBillingAccountBalanceError error
		expectedError                    error
		expectedLog                      string
		chargeID                         uint32
	}{
		{
			name:     "returns no error if all internal methods work as expected",
			chargeID: 123,
			billingData: billing.Data{
				BillingAccountID: ptr.To(uint32(456)),
				LocationID:       ptr.To(uint32(789)),
			},
			createdBillingEventID: int64(123),
		},
		{
			name:                          "returns error if we cannot get billing data",
			getBillingDataFromChargeError: fmt.Errorf("not found for charge ID"),
			expectedError:                 fmt.Errorf("error when getting billing data from charge: not found for charge ID"),
		},
		{
			name:        "logs error if charge has already been billed",
			chargeID:    uint32(456),
			billingData: billing.Data{BillingEventID: ptr.To(uint32(123))}, // non nil value
			expectedLog: fmt.Sprintf("charge ID %d has already been billed (billing_event ID %d)", 456, 123),
		},
		{
			name:        "logs error if no accountID is present",
			chargeID:    uint32(456),
			billingData: billing.Data{}, // non nil value
			expectedLog: "accountID needs to be present in order to bill",
		},
		{
			name: "returns error if CreateBillingEvent method returns an error",
			billingData: billing.Data{
				BillingAccountID: ptr.To(uint32(456)),
				LocationID:       ptr.To(uint32(789)),
			},
			createBillingEventError: fmt.Errorf("create_billing_event"),
			expectedError:           fmt.Errorf("creating billing event: create_billing_event"),
		},
		{
			name: "returns error if SetEventIDAndAccountIDOnCharge method returns an error",
			billingData: billing.Data{
				BillingAccountID: ptr.To(uint32(456)),
				LocationID:       ptr.To(uint32(789)),
			},
			updateChargeBillingEventIDError: fmt.Errorf("update_charge_billing_event_id"),
			expectedError:                   fmt.Errorf("updating charge billing_event_id: update_charge_billing_event_id"),
		},
		{
			name: "returns error if UpdateBillingAccountBalance method returns an error",
			billingData: billing.Data{
				BillingAccountID: ptr.To(uint32(456)),
				LocationID:       ptr.To(uint32(789)),
			},
			updateBillingAccountBalanceError: fmt.Errorf("update_billing_account_balance"),
			expectedError:                    fmt.Errorf("updating billing_account balance: update_billing_account_balance"),
		},
		{
			name:     "logs information when billing account balance is negative",
			chargeID: uint32(456),
			billingData: billing.Data{
				BillingAccountID: ptr.To(uint32(456)),
				LocationID:       ptr.To(uint32(789)),
			},
			createdBillingEventID:    int64(123),
			newBillingAccountBalance: -100,
			expectedLog:              fmt.Sprintf("billing resulted in a negative balance: Charge ID: 456, Billing Account ID: 456, Billing Event ID: 123, Presentment Amount: %d %s, Settlement Amount: %d %s, New balance: -100", testData.presentmentAmount, testData.presentmentCurrency, testData.settlementAmount, testData.settlementCurrency),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockBillingRepository := mockbilling.NewMockRepository(ctrl)

			ctx := t.Context()

			mockBillingRepository.EXPECT().GetBillingDataFromCharge(ctx, tt.chargeID).Return(tt.billingData, tt.getBillingDataFromChargeError).AnyTimes()

			convertedBillingAccount, err := numbers.Convert[uint32, int32](ptr.Deref(tt.billingData.BillingAccountID, 0))
			require.NoError(t, err)

			mockBillingRepository.
				EXPECT().
				CreateBillingEvent(
					ctx,
					testData.presentmentAmount,
					testData.presentmentCurrency,
					testData.settlementAmount,
					testData.settlementCurrency,
					testData.exchangeRate,
					fmt.Sprintf("Charged at location %d", ptr.Deref(tt.billingData.LocationID, 0)),
					gomock.AssignableToTypeOf(time.Time{}),
					ptr.To(convertedBillingAccount),
					ptr.To(int32(0)),
				).
				Return(tt.createdBillingEventID, tt.createBillingEventError).AnyTimes()

			convertedBillingEvent, err := numbers.Convert[int64, int32](tt.createdBillingEventID)
			require.NoError(t, err)

			convertedBillingAccountID, err := numbers.Convert[uint32, int32](ptr.Deref(tt.billingData.BillingAccountID, 0))
			require.NoError(t, err)

			convertedChargeID, err := numbers.Convert[uint32, int32](tt.chargeID)
			require.NoError(t, err)

			mockBillingRepository.
				EXPECT().
				SetEventIDAndAccountIDOnCharge(
					ctx,
					convertedChargeID,
					convertedBillingEvent,
					convertedBillingAccountID,
				).
				Return(tt.updateChargeBillingEventIDError).AnyTimes()

			mockBillingRepository.
				EXPECT().
				UpdateBillingAccountBalance(
					ctx,
					convertedBillingAccountID,
					testData.settlementAmount,
					gomock.AssignableToTypeOf(time.Time{}),
				).
				Return(tt.newBillingAccountBalance, tt.updateBillingAccountBalanceError).AnyTimes()

			mockTransactionsWrapper := mockwalletbilling.NewMockTransactionWrapper(ctrl)

			mockTransactionsWrapper.
				EXPECT().
				BillingRepository().
				Return(mockBillingRepository).
				AnyTimes()

			mockTransactionsWrapper.
				EXPECT().
				Atomic(ctx, gomock.Any()).
				DoAndReturn(
					func(_ context.Context, callback func(t walletbilling.TransactionWrapper) error) error {
						err = callback(mockTransactionsWrapper)
						if err != nil {
							return err
						}
						return nil
					},
				).AnyTimes()

			// Do
			var buff bytes.Buffer
			mockLogger := log.New(&buff, "[Wallet Billing Test]", log.Lmsgprefix)
			service := walletbilling.NewService(mockLogger, mockTransactionsWrapper)

			err = service.Bill(
				ctx,
				tt.chargeID,
				testData.presentmentAmount,
				testData.presentmentCurrency,
				testData.settlementAmount,
				testData.settlementCurrency,
				testData.exchangeRate,
			)

			// Assert
			if tt.expectedError != nil {
				require.Error(t, tt.expectedError, err)
			} else {
				require.NoError(t, err)
			}

			require.Contains(t, buff.String(), tt.expectedLog)
		})
	}
}

type BillTestData struct {
	presentmentAmount   int32
	presentmentCurrency string
	settlementAmount    int32
	settlementCurrency  string
	exchangeRate        string
}

func createBillTestData() BillTestData {
	return BillTestData{
		presentmentAmount:   gofakeit.Int32(),
		presentmentCurrency: gofakeit.Currency().Short,
		settlementAmount:    gofakeit.Int32(),
		settlementCurrency:  gofakeit.Currency().Short,
		exchangeRate:        "1.0",
	}
}
