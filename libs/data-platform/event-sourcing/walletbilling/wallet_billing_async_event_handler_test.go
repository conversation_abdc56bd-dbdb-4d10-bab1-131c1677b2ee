package walletbilling_test

import (
	"bytes"
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	mockcost "experience/libs/data-platform/event-sourcing/domain/events/charges/cost/mock"
	mockcharges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	"experience/libs/data-platform/event-sourcing/walletbilling"
	mockwalletbilling "experience/libs/data-platform/event-sourcing/walletbilling/mock"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/numbers"
	exchangerates "experience/libs/shared/go/service/exchange-rates"
	mockexchangerates "experience/libs/shared/go/service/exchange-rates/mock"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"log"
	"math"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func Test_walletBillingAsyncEventHandler_execute(t *testing.T) {
	chargeID := gofakeit.IntRange(1, math.MaxInt32)
	aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

	mockBillingAccountID := uint32(85555)
	mockLocationID := uint32(35555)
	mockAuthoriserID := 1234

	oneHundredAndTwentyDaysAgo := time.Now().AddDate(0, 0, -walletbilling.MaxDaysWithinWhichToBill).UTC()
	twoDaysAgo := time.Now().AddDate(0, 0, -2).UTC()

	tests := []struct {
		name                    string
		event                   charges.Billed
		mockLoadedAggregate     *charges.Aggregate
		chargeTooOld            bool
		mockGetBillingData      billing.Data
		mockGetBillingDataError error
		mockGetCurrency         string
		mockGetCurrencyError    error
		mockGetHistorical       *exchangerates.ExchangeRate
		mockGetHistoricalError  error
		mockGetBillingEventByID billing.Event
		mockBillError           error
		expectedLogMessage      string
		expectedError           error
		expectedMismatches      []string
	}{
		{
			name: "returns error if there was no completed event on the aggregate",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &twoDaysAgo,
				Events:      []eventstore.Event{},
			},
			mockGetBillingData: billing.Data{
				BillingEventID:   nil,
				BillingAccountID: ptr.To(mockBillingAccountID),
				LocationID:       ptr.To(mockLocationID),
			},
			mockGetBillingEventByID: billing.Event{},
			mockGetHistorical:       &exchangerates.ExchangeRate{},
			expectedError:           fmt.Errorf("finding completed event %s: cannot find a completed event", aggregateID.String()),
		},
		{
			name: "returns error if getting currency by account ID fails",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &twoDaysAgo,
				Events: []eventstore.Event{
					&charges.Completed{
						Payload: charges.CompletedPayload{
							Authorisation: charges.Authorisation{
								AuthoriserID: ptr.To(mockAuthoriserID),
							},
							Charge: charges.Payload{
								ID: chargeID,
							},
						},
					},
				},
			},
			mockGetCurrencyError: fmt.Errorf("mock_get_currency_error"),
			mockGetBillingData: billing.Data{
				BillingEventID:   nil,
				BillingAccountID: ptr.To(mockBillingAccountID),
				LocationID:       ptr.To(mockLocationID),
			},
			mockGetBillingEventByID: billing.Event{},
			expectedError:           fmt.Errorf("failed to retrieve billing account for authoriser %d on aggregate %s: mock_get_currency_error", mockAuthoriserID, aggregateID.String()),
		},
		{
			name: "returns error if getting historical exchange rate fails",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &twoDaysAgo,
				Events: []eventstore.Event{
					&charges.Completed{
						Payload: charges.CompletedPayload{
							Authorisation: charges.Authorisation{
								AuthoriserID: ptr.To(mockAuthoriserID),
							},
							Charge: charges.Payload{
								ID: chargeID,
							},
						},
					},
				},
			},
			mockGetCurrency:        "EUR",
			mockGetHistoricalError: fmt.Errorf("mock_get_historical_error"),
			mockGetBillingData: billing.Data{
				BillingEventID:   nil,
				BillingAccountID: ptr.To(mockBillingAccountID),
				LocationID:       ptr.To(mockLocationID),
			},
			mockGetBillingEventByID: billing.Event{},
			expectedError:           fmt.Errorf("unable to get exchange rate: retrieving exchange rate: mock_get_historical_error"),
		},
		{
			name: "does not process event if billing type is not wallet",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					BillingType: "not_wallet",
				},
			},
			mockLoadedAggregate: &charges.Aggregate{},
		},
		{
			name: "doesn't process event if charge is older than maximum number of days",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					BillingType: billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				UnpluggedAt: &oneHundredAndTwentyDaysAgo,
			},
			chargeTooOld: true,
		},
		{
			name: "prints error if billing via wallet billing service fails",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				UnpluggedAt: &twoDaysAgo,
				Events: []eventstore.Event{
					&charges.Completed{
						Payload: charges.CompletedPayload{
							Authorisation: charges.Authorisation{
								AuthoriserID: ptr.To(mockAuthoriserID),
							},
							Charge: charges.Payload{
								ID: chargeID,
							},
						},
					},
				},
			},
			mockGetCurrency: "EUR",
			mockGetBillingData: billing.Data{
				BillingEventID:   nil,
				BillingAccountID: ptr.To(mockBillingAccountID),
				LocationID:       ptr.To(mockLocationID),
			},
			mockGetHistorical: &exchangerates.ExchangeRate{
				Value: 1.0,
			},
			mockGetBillingEventByID: billing.Event{},
			mockBillError:           fmt.Errorf("mock_bill_error"),
			expectedLogMessage:      "error when billing via wallet billing service: mock_bill_error",
		},
		{
			name: "logs message when authoriser present in aggregate",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeWallet.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &twoDaysAgo,
				Events: []eventstore.Event{
					&charges.Completed{
						Payload: charges.CompletedPayload{
							Authorisation: charges.Authorisation{
								AuthoriserID: ptr.To(mockAuthoriserID),
							},
							Charge: charges.Payload{
								ID: chargeID,
							},
						},
					},
				},
			},
			mockGetCurrency: "EUR",
			mockGetBillingData: billing.Data{
				BillingEventID:   nil,
				BillingAccountID: ptr.To(mockBillingAccountID),
				LocationID:       ptr.To(mockLocationID),
			},
			mockGetHistorical: &exchangerates.ExchangeRate{
				Value: 1.0,
			},
			mockGetBillingEventByID: billing.Event{
				AccountID:           ptr.To(mockBillingAccountID),
				PresentmentAmount:   10,
				PresentmentCurrency: "GBP",
				ExchangeRate:        1,
				SettlementAmount:    10,
				SettlementCurrency:  "GBP",
				Description:         "Test",
				RefundedAmount:      nil,
			},
			expectedLogMessage: fmt.Sprintf("Authoriser ID %d present for aggregate %s", mockAuthoriserID, aggregateID.String()),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockAggregateLoader := mockcharges.NewMockAggregateLoader(ctrl)

			if tt.event.Payload.BillingType == billing.TypeWallet.String() {
				mockAggregateLoader.
					EXPECT().
					LoadCharge(context.Background(), gomock.Any()).
					AnyTimes().
					Return(tt.mockLoadedAggregate, nil)
			}

			mockWalletBillingService := mockwalletbilling.NewMockService(ctrl)

			if tt.mockGetBillingData.BillingEventID == nil &&
				tt.mockGetBillingDataError == nil &&
				completedEventPresent(tt.mockLoadedAggregate.Events) &&
				tt.event.Payload.BillingType == billing.TypeWallet.String() &&
				!tt.chargeTooOld &&
				tt.mockGetCurrencyError == nil &&
				tt.mockGetHistoricalError == nil {
				chargeIDUInt32, err := numbers.Convert[int, uint32](chargeID)
				require.NoError(t, err)
				require.NoError(t, err)
				mockWalletBillingService.
					EXPECT().
					Bill(
						gomock.AssignableToTypeOf(context.Background()),
						chargeIDUInt32,
						-tt.event.Payload.SettlementAmount,
						tt.event.Payload.SettlementCurrency,
						gomock.Any(),
						tt.mockGetCurrency,
						fmt.Sprintf("%.6f", tt.mockGetHistorical.Value),
					).
					Return(tt.mockBillError)
			}

			mockCostCalcRepository := mockcost.NewMockRepository(ctrl)

			if tt.mockGetBillingDataError == nil &&
				tt.event.Payload.BillingType == billing.TypeWallet.String() &&
				completedEventPresent(tt.mockLoadedAggregate.Events) &&
				!tt.chargeTooOld {
				mockCostCalcRepository.
					EXPECT().
					GetBillingAccountCurrencyByAuthoriserID(
						gomock.AssignableToTypeOf(context.Background()),
						mockAuthoriserID,
					).
					Times(1).
					Return(
						tt.mockGetCurrency,
						tt.mockGetCurrencyError,
					)
			}

			mockExchangeRatesAPI := mockexchangerates.NewMockAPI(ctrl)

			if tt.mockGetBillingDataError == nil &&
				tt.mockGetBillingData.BillingEventID == nil &&
				completedEventPresent(tt.mockLoadedAggregate.Events) &&
				tt.event.Payload.BillingType == billing.TypeWallet.String() &&
				!tt.chargeTooOld &&
				tt.mockGetCurrencyError == nil {
				mockExchangeRatesAPI.
					EXPECT().
					GetHistorical(
						tt.event.Payload.SettlementCurrency,
						tt.mockGetCurrency,
						ptr.Deref(tt.mockLoadedAggregate.UnpluggedAt, time.Time{}),
					).
					Times(1).
					Return(
						tt.mockGetHistorical,
						tt.mockGetHistoricalError,
					)
			}

			writer := log.Writer()
			var buf bytes.Buffer
			log.SetOutput(&buf)
			defer func() {
				// restore log output to its default writer after the test
				log.SetOutput(writer)
			}()
			u := walletbilling.NewWalletBillingAsyncEventHandler(
				log.Default(),
				mockWalletBillingService,
				mockAggregateLoader,
				mockCostCalcRepository,
				mockExchangeRatesAPI,
			)

			err := u.Execute(context.Background(), nil, &tt.event, 1)

			if tt.expectedError != nil {
				require.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}

			if tt.event.Payload.BillingType == billing.TypeWallet.String() &&
				!tt.chargeTooOld &&
				tt.mockGetBillingDataError == nil &&
				tt.mockGetBillingData.BillingEventID == nil &&
				completedEventPresent(tt.mockLoadedAggregate.Events) &&
				tt.mockBillError == nil {
				require.Contains(t, buf.String(), "processed event type "+tt.event.GetType().String()+" for aggregate ID "+tt.event.GetAggregateID().String())
			}

			require.Contains(t, buf.String(), tt.expectedLogMessage)

			for _, mismatch := range tt.expectedMismatches {
				require.Contains(t, buf.String(), mismatch)
			}
		})
	}
}

func completedEventPresent(events []eventstore.Event) bool {
	for _, event := range events {
		if _, ok := event.(*charges.Completed); ok {
			return true
		}
	}
	return false
}
