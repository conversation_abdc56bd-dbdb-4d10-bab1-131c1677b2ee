package infra_test

import (
	"context"
	"errors"
	"experience/libs/data-platform/event-sourcing/walletbilling"
	"fmt"
	"log"
	"testing"

	"experience/libs/data-platform/event-sourcing/walletbilling/infra"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/require"
)

func TestAtomicNew(t *testing.T) {
	beginError := errors.New("begin error")
	callbackError := errors.New("callback error")
	rollbackError := errors.New("rollback error")
	combinedError := fmt.Errorf("tx err: %v, rb err: %v", callbackError, rollbackError)
	commitError := errors.New("commit error")

	tests := []struct {
		name          string
		setupMock     func(mockSQL sqlmock.Sqlmock)
		callbackError error
		expectError   bool
		expectedError error
	}{
		{
			name: "No error from callback - commit",
			setupMock: func(mockSQL sqlmock.Sqlmock) {
				mockSQL.ExpectBegin()
				mockSQL.ExpectCommit()
			},
			callbackError: nil,
			expectError:   false,
		},
		{
			name: "Error from callback - rollback",
			setupMock: func(mockSQL sqlmock.Sqlmock) {
				mockSQL.ExpectBegin()
				mockSQL.ExpectRollback()
			},
			callbackError: callbackError,
			expectError:   true,
			expectedError: callbackError,
		},
		{
			name: "Error with Begin",
			setupMock: func(mockSQL sqlmock.Sqlmock) {
				mockSQL.ExpectBegin().WillReturnError(beginError)
			},
			expectError:   true,
			expectedError: fmt.Errorf("beginning transaction: %w", beginError),
		},
		{
			name: "Error with Rollback",
			setupMock: func(mockSQL sqlmock.Sqlmock) {
				mockSQL.ExpectBegin()
				mockSQL.ExpectRollback().WillReturnError(rollbackError)
			},
			callbackError: callbackError,
			expectError:   true,
			expectedError: combinedError,
		},
		{
			name: "Error with Commit",
			setupMock: func(mockSQL sqlmock.Sqlmock) {
				mockSQL.ExpectBegin()
				mockSQL.ExpectCommit().WillReturnError(commitError)
			},
			expectError:   true,
			expectedError: commitError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB, mockSQL, _ := sqlmock.New()
			db := sqlx.NewDb(mockDB, "sqlmock")
			transactionWrapper := infra.NewTransactionWrapper(log.Default(), db)

			tt.setupMock(mockSQL)

			err := transactionWrapper.Atomic(context.Background(), func(_ walletbilling.TransactionWrapper) error {
				return tt.callbackError
			})

			if tt.expectError {
				require.Error(t, err)
				require.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}

			err = mockSQL.ExpectationsWereMet()
			require.NoError(t, err)
		})
	}
}
