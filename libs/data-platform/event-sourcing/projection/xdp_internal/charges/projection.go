package charges

import (
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"time"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

type Projection struct {
	ChargeUUID            uuid.UUID
	UserIDs               []uuid.UUID
	StartedAt             *time.Time
	EndedAt               *time.Time
	PluggedInAt           *time.Time
	UnpluggedAt           *time.Time
	ChargerID             *string
	ChargerName           *string
	Door                  *string
	ChargerTimezone       *string
	EnergyTotal           *float64
	GenerationEnergyTotal *float64
	GridEnergyTotal       *float64
	ChargeDurationTotal   *int32
	EnergyCost            *int32
	EnergyCostCurrency    *string
	SettlementAmount      *int32
	SettlementCurrency    *string
	ExpensedToGroup       *uuid.UUID
	ExpensedTo            *string
	ChargerType           *string
	SiteName              *string
	GroupID               *uuid.UUID
	GroupName             *string
	SiteID                *uuid.UUID
	Confirmed             *bool
	RewardsEligibleEnergy *float32
	VehicleID             *uuid.UUID
}

func NewProjection(aggregate *charges.Aggregate) *Projection {
	return &Projection{
		ChargeUUID:            aggregate.AggregateID,
		UserIDs:               aggregate.UserIDs.Members(),
		StartedAt:             aggregate.StartedAt,
		EndedAt:               aggregate.EndedAt,
		PluggedInAt:           aggregate.PluggedInAt,
		UnpluggedAt:           aggregate.UnpluggedAt,
		ChargerID:             toStringPtr(aggregate.ChargerID),
		ChargerType:           aggregate.ChargerType,
		ChargerName:           aggregate.ChargerName,
		Door:                  aggregate.Door,
		ChargerTimezone:       aggregate.ChargerTimezone,
		EnergyTotal:           aggregate.EnergyTotal,
		GenerationEnergyTotal: aggregate.GenerationEnergyTotal,
		GridEnergyTotal:       aggregate.GridEnergyTotal,
		ChargeDurationTotal:   aggregate.ChargeDurationTotal,
		EnergyCost:            aggregate.EnergyCost,
		EnergyCostCurrency:    aggregate.EnergyCostCurrency,
		SettlementAmount:      aggregate.SettlementAmount,
		SettlementCurrency:    aggregate.SettlementCurrency,
		ExpensedToGroup:       aggregate.ExpensedTo,
		ExpensedTo:            aggregate.ExpensedToName,
		SiteName:              aggregate.SiteName,
		GroupID:               aggregate.GroupID,
		GroupName:             aggregate.GroupName,
		SiteID:                aggregate.SiteID,
		Confirmed:             aggregate.Confirmed,
		RewardsEligibleEnergy: aggregate.RewardableEnergy,
		VehicleID:             aggregate.VehicleID,
	}
}

func toStringPtr(chargerID *chargers.StationID) *string {
	if chargerID != nil {
		return ptr.To(string(*chargerID))
	}
	return nil
}
