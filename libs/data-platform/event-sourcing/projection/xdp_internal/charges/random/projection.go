package random

import (
	"context"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"testing"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
)

func TimesCreateProjectionsCharges(ctx context.Context, t *testing.T, times uint, queries *sqlc.Queries) []*sqlc.ProjectionsCharge {
	t.Helper()

	ccpp := TimesCreateChargeProjectionParams(times)
	pcs := make([]*sqlc.ProjectionsCharge, times)
	for i := 0; i < len(ccpp); i++ {
		pcs[i] = CreateProjectionsCharge(ctx, t, queries, ptr.To(ccpp[i]))
	}
	return pcs
}

func CreateProjectionsCharge(ctx context.Context, t *testing.T, queries *sqlc.Queries, params *sqlc.CreateChargeProjectionParams) *sqlc.ProjectionsCharge {
	t.Helper()

	chargeProjection, err := queries.CreateChargeProjection(ctx, *params)
	require.NoError(t, err)

	return &chargeProjection
}
