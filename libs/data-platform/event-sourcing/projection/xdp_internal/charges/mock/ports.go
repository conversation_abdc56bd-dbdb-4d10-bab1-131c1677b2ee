// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/projection/xdp_internal/charges/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/projection/xdp_internal/charges/mock/ports.go -source=libs/data-platform/event-sourcing/projection/xdp_internal/charges/ports.go
//
// Package mock_charges is a generated GoMock package.
package mock_charges

import (
	context "context"
	charges "experience/libs/data-platform/event-sourcing/domain/events/charges"
	charges0 "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// SaveCharge mocks base method.
func (m *MockService) SaveCharge(ctx context.Context, chargeAggr *charges.Aggregate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveCharge", ctx, chargeAggr)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveCharge indicates an expected call of SaveCharge.
func (mr *MockServiceMockRecorder) SaveCharge(ctx, chargeAggr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCharge", reflect.TypeOf((*MockService)(nil).SaveCharge), ctx, chargeAggr)
}

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Save mocks base method.
func (m *MockRepository) Save(ctx context.Context, chargeProjection *charges0.Projection) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, chargeProjection)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockRepositoryMockRecorder) Save(ctx, chargeProjection any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockRepository)(nil).Save), ctx, chargeProjection)
}
