package charges

import (
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
)

type serviceImpl struct {
	repository Repository
}

func NewService(repository Repository) Service {
	return &serviceImpl{repository: repository}
}

func (s serviceImpl) SaveCharge(ctx context.Context, chargeAggr *charges.Aggregate) error {
	chargeProjection := NewProjection(chargeAggr)

	err := s.repository.Save(ctx, chargeProjection)
	if err != nil {
		return err
	}

	return nil
}
