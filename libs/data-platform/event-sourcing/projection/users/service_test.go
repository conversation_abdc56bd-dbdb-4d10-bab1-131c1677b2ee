package users_test

import (
	"context"
	mockusers "experience/libs/data-platform/event-sourcing/projection/users/mock"

	"github.com/stretchr/testify/require"

	"errors"
	"experience/libs/data-platform/event-sourcing/projection/users"
	"testing"
	"time"

	"github.com/google/uuid"
	"go.uber.org/mock/gomock"
)

var (
	errFake = errors.New("fake error")
	groupID = uuid.New()
	userID  = uuid.New()
	from    = time.Now()
	to      = from.AddDate(0, 0, -1)
)

func Test_serviceImpl_RetrieveUserCharges(t *testing.T) {
	tests := []struct {
		name          string
		repositoryErr error
		want          []users.UserCharge
		wantErr       error
	}{
		{
			name: "responds with repository output",
			want: []users.UserCharge{
				{},
			},
		},
		{
			name:          "responds with repository error",
			repositoryErr: errFake,
			wantErr:       errFake,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockRepository := mockusers.NewMockRepository(gomock.NewController(t))
			mockRepository.EXPECT().RetrieveUserCharges(ctx, groupID, userID, from, to).Return(tt.want, tt.repositoryErr)

			s := users.NewService(mockRepository)
			got, err := s.RetrieveUserCharges(ctx, groupID, userID, from, to)
			require.Equal(t, tt.want, got)
			require.Equal(t, tt.wantErr, err)
		})
	}
}
