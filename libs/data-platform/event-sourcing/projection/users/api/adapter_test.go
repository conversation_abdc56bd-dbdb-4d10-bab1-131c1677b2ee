package api

import (
	contract "experience/libs/data-platform/api/contract/gen/user_charges"
	"experience/libs/data-platform/event-sourcing/projection/users"
	carbonsavings "experience/libs/shared/go/carbon-savings"
	"testing"

	"github.com/jinzhu/now"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestToProjectionGroupAndUserChargesResponse(t *testing.T) {
	energyUsage := 32.45

	tests := []struct {
		name  string
		input []users.UserCharge
		want  *contract.ProjectionGroupAndUserChargesResponse
	}{
		{
			name: "maps model to contract",
			input: []users.UserCharge{
				{
					ChargerName:       "Charger-Name",
					BusinessName:      "Business Name",
					StartTime:         ptr.To(now.MustParse("2024-01-01 06:00:00")),
					EndTime:           ptr.To(now.MustParse("2024-01-01 16:00:00")),
					ChargingDuration:  12345,
					PluggedInDuration: 23456,
					EnergyUsage:       energyUsage,
					ChargeCost:        56789,
					RevenueGenerated:  67890,
				},
			},
			want: &contract.ProjectionGroupAndUserChargesResponse{
				Data: &contract.UserChargesSchema{
					Charges: []*contract.UserCharge{
						{
							ChargerName:       "Charger-Name",
							BusinessName:      "Business Name",
							StartTime:         "2024-01-01T06:00:00Z",
							EndTime:           "2024-01-01T16:00:00Z",
							ChargingDuration:  12345,
							PluggedInDuration: 23456,
							EnergyUsage:       energyUsage,
							ChargeCost:        56789,
							RevenueGenerated:  67890,
							Co2Savings:        carbonsavings.Co2Savings(energyUsage),
						},
					},
				},
			},
		},
		{
			name:  "maps empty slice",
			input: []users.UserCharge{},
			want: &contract.ProjectionGroupAndUserChargesResponse{
				Data: &contract.UserChargesSchema{
					Charges: []*contract.UserCharge{},
				},
			},
		},
		{
			name:  "maps nil slice",
			input: nil,
			want: &contract.ProjectionGroupAndUserChargesResponse{
				Data: &contract.UserChargesSchema{
					Charges: []*contract.UserCharge{},
				},
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := ToProjectionGroupAndUserChargesResponse(test.input)
			require.NoError(t, err)
			require.Equal(t, test.want, got)
		})
	}
}
