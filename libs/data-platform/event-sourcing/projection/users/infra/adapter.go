package infra

import (
	"experience/libs/data-platform/event-sourcing/projection/users"
	"experience/libs/data-platform/event-sourcing/projection/users/infra/sqlc"
	"experience/libs/shared/go/db"

	"github.com/jinzhu/copier"
)

func ToUserCharges(rows []sqlc.RetrieveUserChargesRow) ([]users.UserCharge, error) {
	var userCharges []users.UserCharge
	err := copier.CopyWithOption(&userCharges, rows, copier.Option{
		Converters: []copier.TypeConverter{
			db.NullStringToFloatConverter,
		},
	})
	if err != nil {
		return nil, err
	}
	return userCharges, nil
}
