package users

import (
	carbonsavings "experience/libs/shared/go/carbon-savings"
	"time"
)

type UserCharge struct {
	ChargerName       string
	BusinessName      string
	StartTime         *time.Time
	EndTime           *time.Time
	ChargingDuration  int
	PluggedInDuration int
	EnergyUsage       float64
	ChargeCost        int
	RevenueGenerated  int
}

func (u *UserCharge) Co2Savings() float64 {
	return carbonsavings.Co2Savings(u.EnergyUsage)
}
