// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/projection/users/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/projection/users/mock/ports.go -source=libs/data-platform/event-sourcing/projection/users/ports.go
//
// Package mock_users is a generated GoMock package.
package mock_users

import (
	context "context"
	users "experience/libs/data-platform/event-sourcing/projection/users"
	reflect "reflect"
	time "time"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// RetrieveUserCharges mocks base method.
func (m *MockService) RetrieveUserCharges(ctx context.Context, groupID, userID uuid.UUID, from, to time.Time) ([]users.UserCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveUserCharges", ctx, groupID, userID, from, to)
	ret0, _ := ret[0].([]users.UserCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveUserCharges indicates an expected call of RetrieveUserCharges.
func (mr *MockServiceMockRecorder) RetrieveUserCharges(ctx, groupID, userID, from, to any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveUserCharges", reflect.TypeOf((*MockService)(nil).RetrieveUserCharges), ctx, groupID, userID, from, to)
}

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// RetrieveUserCharges mocks base method.
func (m *MockRepository) RetrieveUserCharges(ctx context.Context, groupID, userID uuid.UUID, from, to time.Time) ([]users.UserCharge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveUserCharges", ctx, groupID, userID, from, to)
	ret0, _ := ret[0].([]users.UserCharge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveUserCharges indicates an expected call of RetrieveUserCharges.
func (mr *MockRepositoryMockRecorder) RetrieveUserCharges(ctx, groupID, userID, from, to any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveUserCharges", reflect.TypeOf((*MockRepository)(nil).RetrieveUserCharges), ctx, groupID, userID, from, to)
}
