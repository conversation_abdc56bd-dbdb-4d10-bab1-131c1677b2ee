-- name: RetrieveChargeStatisticsByCharger :one
WITH filtered_charges AS (
  SELECT *
  FROM projections.charges
  WHERE charger_id = sqlc.arg(charger_id)
    AND unplugged_at >= sqlc.arg(from_date)::timestamptz
  AND unplugged_at < sqlc.arg(to_date)::timestamptz
),
confirmed_charges AS (
  SELECT COALESCE(SUM(energy_total), 0)::numeric(18, 2) AS claimed_usage
  FROM filtered_charges
  WHERE confirmed IS TRUE
),
unconfirmed_charges AS (
  SELECT COALESCE(SUM(energy_total), 0)::numeric(18, 2) AS unclaimed_usage
  FROM filtered_charges
  WHERE confirmed IS NOT TRUE
),
revenue_generating_charges AS (
  SELECT COALESCE(SUM(energy_total), 0)::numeric(18, 2) AS revenue_generating_claimed_usage
  FROM filtered_charges
  WHERE confirmed IS TRUE
    AND settlement_amount > 0
),
unnested_user_ids AS (
  SELECT unnest(user_ids) AS user_id
  FROM filtered_charges
),
distinct_user_count AS (
  SELECT COUNT(DISTINCT user_id) AS number_of_users
  FROM unnested_user_ids
)
SELECT
  COUNT(DISTINCT charge_uuid) AS number_of_charges,
  (SELECT number_of_users FROM distinct_user_count),
  COALESCE(SUM(energy_total), 0)::numeric(18, 2) AS total_usage,
  (SELECT claimed_usage FROM confirmed_charges),
  (SELECT revenue_generating_claimed_usage FROM revenue_generating_charges),
  (SELECT unclaimed_usage FROM unconfirmed_charges),
  COALESCE(SUM(charge_duration_total), 0) AS charging_duration,
  COALESCE(SUM(settlement_amount), 0) AS revenue_generated,
  COALESCE(SUM(energy_cost), 0) AS cost
FROM filtered_charges;
