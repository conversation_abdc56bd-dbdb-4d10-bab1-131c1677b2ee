package drivers_test

import (
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	"experience/libs/shared/go/service/domain"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"

	"github.com/stretchr/testify/require"
)

func newChargeCostInfoFixture(name, chargerType string) chargeCostInfoFixture {
	var want *domain.Money
	energyCost := domain.Money{
		Amount:   int(gofakeit.Int32()),
		Currency: gofakeit.CurrencyShort(),
	}
	settlement := domain.Money{
		Amount:   int(gofakeit.Int32()),
		Currency: gofakeit.CurrencyShort(),
	}

	switch chargerType {
	case "home":
		want = &energyCost
	case "private", "public":
		want = &settlement
	}

	return chargeCostInfoFixture{
		name: name,
		charge: drivers.NewCharge(uuid.Nil, nil, nil, nil, nil, nil, nil, drivers.NewCharger(
			chargerType,
			"",
			nil,
			"",
			nil,
			nil,
			nil,
			nil,
		), &energyCost, &settlement, nil),
		want: want,
	}
}

type chargeCostInfoFixture struct {
	name   string
	charge drivers.Charge
	want   *domain.Money
}

func TestCharge_ChargeCost(t *testing.T) {
	tests := []chargeCostInfoFixture{
		newChargeCostInfoFixture("home charge - cost taken from energy cost", "home"),
		newChargeCostInfoFixture("private charge - cost taken from settlement amount", "private"),
		newChargeCostInfoFixture("public charge - cost taken from settlement amount", "public"),
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.want, tt.charge.Cost())
		})
	}
}

func TestCharger_PluggedInDuration(t *testing.T) {
	pluggedInAt := time.Now().UTC()
	unpluggedAt := pluggedInAt.Add(time.Minute * 5).UTC()
	pluggedInDuration := uint32(unpluggedAt.Sub(pluggedInAt).Seconds())

	tests := []struct {
		name            string
		pluggedInAt     *time.Time
		unpluggedAt     *time.Time
		expectedSeconds uint32
	}{
		{
			name:            "valid duration returned for plugged in duration",
			pluggedInAt:     &pluggedInAt,
			unpluggedAt:     &unpluggedAt,
			expectedSeconds: 300, // 5 minutes
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.expectedSeconds, pluggedInDuration)
		})
	}
}
