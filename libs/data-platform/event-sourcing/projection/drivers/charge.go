package drivers

import (
	"experience/libs/shared/go/service"
	"experience/libs/shared/go/service/domain"
	"time"

	"github.com/google/uuid"
)

func NewCharge(id uuid.UUID, start, end *time.Time, duration *int32, energyTotal, generationEnergyTotal, gridEnergyTotal *float64, charger Charger, energyCost, settlement *domain.Money, expensedTo *ExpensedTo) Charge { //nolint:gocritic // making pointers not feasible as will break adapter
	return Charge{
		id:                    id,
		startedAt:             start,
		endedAt:               end,
		duration:              duration,
		energyTotal:           energyTotal,
		generationEnergyTotal: generationEnergyTotal,
		gridEnergyTotal:       gridEnergyTotal,
		Charger:               charger,
		energyCost:            energyCost,
		settlement:            settlement,
		ExpensedTo:            expensedTo,
	}
}

// Charge driver charge details.
type Charge struct {
	// Charges unique identifier.
	id        uuid.UUID
	startedAt *time.Time
	endedAt   *time.Time
	// Duration of charging in seconds.
	duration *int32
	// Energy used in kWh.
	energyTotal           *float64
	generationEnergyTotal *float64
	gridEnergyTotal       *float64
	Charger               Charger
	// EnergyCost amount details.
	energyCost *domain.Money
	// Settlement amount details.
	settlement       *domain.Money
	ExpensedTo       *ExpensedTo
	Points           float32
	PointsRedeemedAt *time.Time
	PointsExpiryDate *time.Time
}

type Rewards struct {
	Points           float32
	PointsRedeemedAt *time.Time
	PointsExpiryDate *time.Time
}

func (c *Charge) Rewards() *Rewards {
	return &Rewards{
		Points:           c.Points,
		PointsRedeemedAt: c.PointsRedeemedAt,
		PointsExpiryDate: c.PointsExpiryDate,
	}
}

func (c *Charge) Cost() *domain.Money {
	switch c.Charger.chargerType {
	case "home":
		return c.energyCost
	case "private", "public":
		return c.settlement
	}
	return nil
}

func (c *Charge) Duration() *int32 {
	return c.duration
}

// EndedAt The time relating to when power last provided via charge. This value is not always present for all types of chargers.
// Where a timezone value is provided for the charger, the time provided will include this.
func (c *Charge) EndedAt() *time.Time {
	return service.ToLocationTime(c.endedAt, c.Charger.timezone)
}

func (c *Charge) EnergyCost() *domain.Money {
	return c.energyCost
}

func (c *Charge) EnergyTotal() *float64 {
	return c.energyTotal
}

func (c *Charge) GenerationEnergyTotal() *float64 {
	return c.generationEnergyTotal
}

func (c *Charge) GridEnergyTotal() *float64 {
	return c.gridEnergyTotal
}

func (c *Charge) ID() uuid.UUID {
	return c.id
}

func (c *Charge) Settlement() *domain.Money {
	return c.settlement
}

// StartedAt The time relating to when power first provided via charge. This value is not always present for all types of chargers.
// Where a timezone value is provided for the charger, the time provided will include this.
func (c *Charge) StartedAt() *time.Time {
	return service.ToLocationTime(c.startedAt, c.Charger.timezone)
}

func NewCharger(chargerType, id string, name *string, door string, siteName *string, pluggedIn, unplugged *time.Time, timezone *string) Charger {
	return Charger{
		chargerType: chargerType,
		id:          id,
		door:        door,
		name:        name,
		siteName:    siteName,
		pluggedInAt: pluggedIn,
		unpluggedAt: unplugged,
		timezone:    timezone,
	}
}

type Charger struct {
	chargerType       string
	id                string
	name              *string
	door              string
	siteName          *string
	pluggedInAt       *time.Time
	unpluggedAt       *time.Time
	timezone          *string
	pluggedInDuration *uint32
}

// Type The type of charge. Possible values: home, public, private.
func (c Charger) Type() string { //nolint:gocritic // making pointers not feasible as will break adapter
	return c.chargerType
}

// ID Chargers unique identifier.
func (c Charger) ID() string { //nolint:gocritic //making pointers not feasible as will break adapter
	return c.id
}

// Name Chargers user-friendly name. Should only be present for commercial chargers.
func (c Charger) Name() *string { //nolint:gocritic // making pointers not feasible as will break adapter
	return c.name
}

// Door Charger door used.
func (c Charger) Door() string { //nolint:gocritic // making pointers not feasible as will break adapter
	return c.door
}

// SiteName Name of the site where the charger is located. Should only be present for commercial chargers.
func (c Charger) SiteName() *string { //nolint:gocritic // making pointers not feasible as will break adapter
	return c.siteName
}

// PluggedInAt The time relating to when charger first plugged into as part of a charging session.
// Where a timezone value is provided for the charger, the time provided will include this.
func (c Charger) PluggedInAt() *time.Time { //nolint:gocritic // making pointers not feasible as will break adapter
	return service.ToLocationTime(c.pluggedInAt, c.timezone)
}

// UnpluggedAt The time relating to when charger unplugged from as part of a charging session.
// Where a timezone value is provided for the charger, the time provided will include this.
func (c Charger) UnpluggedAt() *time.Time { //nolint:gocritic // making pointers not feasible as will break adapter
	return service.ToLocationTime(c.unpluggedAt, c.timezone)
}

// Timezone Timezone associated with charger location.
func (c Charger) Timezone() *string { //nolint:gocritic // making pointers not feasible as will break adapter
	return c.timezone
}

// PluggedInDuration is the duration a charger has been in use in seconds.
func (c Charger) PluggedInDuration() uint32 { //nolint:gocritic // making pointers not feasible as will break adapter
	return uint32(c.unpluggedAt.Sub(*c.pluggedInAt).Seconds())
}

// ExpensedTo group the charge is to be expensed to.
type ExpensedTo struct {
	// Groups unique identifier.
	ID uuid.UUID
	// Groups name.
	Name string
}
