package cmd

import (
	contract "experience/libs/data-platform/api/contract/gen/drivers"
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	"experience/libs/data-platform/event-sourcing/projection/drivers/api"
	"experience/libs/data-platform/event-sourcing/projection/drivers/infra"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/http"
)

func NewService(db *postgres.ReadWriteDB) contract.Service {
	repo := infra.NewRepository(db.ReadDB)
	driversService := drivers.NewService(repo)
	return api.NewService(driversService, http.NewErrorMapper(api.ErrorMap))
}
