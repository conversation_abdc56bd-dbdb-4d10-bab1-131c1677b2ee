package test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestIntervalStart(t *testing.T) {
	tests := []struct {
		name          string
		inputTime     time.Time
		interval      string
		expected      time.Time
		expectedPanic bool
	}{
		{
			name:      "day start",
			inputTime: time.Date(2023, 11, 11, 23, 22, 1, 0, time.UTC),
			interval:  "day",
			expected:  time.Date(2023, 11, 11, 0, 0, 0, 0, time.UTC),
		},
		{
			name:      "week start",
			inputTime: time.Date(2023, 11, 11, 23, 22, 1, 0, time.UTC),
			interval:  "week",
			expected:  time.Date(2023, 11, 6, 0, 0, 0, 0, time.UTC),
		},
		{
			name:      "month start",
			inputTime: time.Date(2023, 11, 11, 23, 22, 1, 0, time.UTC),
			interval:  "month",
			expected:  time.Date(2023, 11, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			name:          "biweekly start will panic",
			inputTime:     time.Date(2023, 11, 11, 23, 22, 1, 0, time.UTC),
			interval:      "biweekly",
			expectedPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectedPanic {
				defer func() {
					p := recover()
					require.Equal(t, "unsupported date truncation", p)
				}()
				IntervalStart(t, tt.inputTime, tt.interval, nil)
			} else {
				require.Equal(t, tt.expected, IntervalStart(t, tt.inputTime, tt.interval, nil))
			}
		})
	}
}
