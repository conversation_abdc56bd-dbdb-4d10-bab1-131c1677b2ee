package api_test

import (
	groupstats "experience/libs/data-platform/api/contract/gen/projection_group_statistics"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	"experience/libs/data-platform/event-sourcing/projection/site-stats-monthly/api"
	"experience/libs/shared/go/service/utils"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestToGroupSiteStatsData(t *testing.T) {
	groupID := uuid.New()

	tests := []struct {
		name  string
		input sitestatsmonthly.Projection
		want  *groupstats.GroupSitesStatsResponse
	}{
		{
			name: "valid values mapped to group site stats data",
			input: sitestatsmonthly.Projection{
				Month:            time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				SiteID:           utils.FabricateUUIDFromNumericID(123),
				SiteName:         "site name",
				EnergyUsageKwh:   100.0,
				EnergyCost:       1000,
				RevenueGenerated: 2000,
				NumberOfCharges:  10,
				NumberOfDrivers:  5,
				TotalDuration:    10000,
				GroupID:          groupID,
				GroupName:        "group name",
			},
			want: &groupstats.GroupSitesStatsResponse{
				Data: []*groupstats.GroupSitesStats{
					{
						SiteID:           utils.FabricateUUIDFromNumericID(123).String(),
						EnergyUsageKwh:   100.0,
						EnergyCost:       1000,
						Co2AvoidedKg:     56,
						RevenueGenerated: 2000,
						NumberOfCharges:  10,
						NumberOfDrivers:  5,
						TotalDuration:    10000,
					},
				},
			},
		},
		{
			name: "nil values mapped to group site stats data",
			input: sitestatsmonthly.Projection{
				Month:  time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				SiteID: utils.FabricateUUIDFromNumericID(123),
			},
			want: &groupstats.GroupSitesStatsResponse{
				Data: []*groupstats.GroupSitesStats{
					{
						SiteID: utils.FabricateUUIDFromNumericID(123).String(),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := api.ToGroupSiteStatsData([]sitestatsmonthly.Projection{tt.input})

			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}
