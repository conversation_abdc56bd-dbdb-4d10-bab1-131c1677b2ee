package api

import (
	groupstats "experience/libs/data-platform/api/contract/gen/projection_group_statistics"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	"experience/libs/shared/go/converters"

	"github.com/jinzhu/copier"
)

func ToGroupSiteStatsData(results []sitestatsmonthly.Projection) (*groupstats.GroupSitesStatsResponse, error) {
	var resultsData groupstats.GroupSitesStatsResponse
	err := copier.CopyWithOption(&resultsData.Data, &results, copier.Option{
		Converters: []copier.TypeConverter{
			converters.TimestampToStringConverter,
		},
	})
	if err != nil {
		return nil, err
	}
	return &resultsData, nil
}
