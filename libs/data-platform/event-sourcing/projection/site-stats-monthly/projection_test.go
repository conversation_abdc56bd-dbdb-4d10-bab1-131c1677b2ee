package sitestatsmonthly

import (
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/service/collections"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

func TestProjection_Apply(t *testing.T) {
	tests := []struct {
		name                string
		projectionUnchanged bool
		chargeAggregate     *charges.Aggregate
		initialProjection   *Projection
		expectedProjection  *Projection
		event               eventstore.Event
	}{
		{
			name:                "ignores nil charge aggregates",
			chargeAggregate:     nil,
			projectionUnchanged: true,
			initialProjection:   &Projection{},
			expectedProjection:  &Projection{},
		},
		{
			name: "applies all expected fields to an empty projection",
			chargeAggregate: &charges.Aggregate{
				AggregateID:         uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550"),
				EnergyTotal:         ptr.To(12.14),
				ChargeDurationTotal: ptr.To(int32(150)),
				UserIDs:             collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
				SettlementAmount:    ptr.To(int32(654)),
				SettlementCurrency:  ptr.To("GBP"),
				SiteName:            ptr.To("LGMs Fleet Garage"),
				GroupID:             ptr.To(uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485")),
				GroupName:           ptr.To("PodPoint"),
				SiteID:              ptr.To(uuid.MustParse("fb6cef30-990f-432c-865c-379af062e1a9")),
				EnergyCost:          ptr.To(int32(502)),
				EnergyCostCurrency:  ptr.To("GBP"),
			},
			initialProjection: &Projection{},
			expectedProjection: &Projection{
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  1,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
		},
		{
			name: "increments all fields appropriately for existing projection",
			chargeAggregate: &charges.Aggregate{
				AggregateID:         uuid.MustParse("175c5832-836a-4b69-af64-6b9d7edc5cf5"),
				EnergyTotal:         ptr.To(32.17),
				ChargeDurationTotal: ptr.To(int32(670)),
				UserIDs:             collections.NewSet[uuid.UUID](uuid.MustParse("99521c91-9db7-4dbf-8c4f-f76e240be02e")),
				SettlementAmount:    ptr.To(int32(1765)),
				SettlementCurrency:  ptr.To("GBP"),
				SiteName:            ptr.To("LGMs Fleet Garage"),
				GroupID:             ptr.To(uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485")),
				GroupName:           ptr.To("PodPoint"),
				SiteID:              ptr.To(uuid.MustParse("fb6cef30-990f-432c-865c-379af062e1a9")),
				EnergyCost:          ptr.To(int32(943)),
				EnergyCostCurrency:  ptr.To("GBP"),
			},
			initialProjection: &Projection{
				SiteID:           uuid.MustParse("fb6cef30-990f-432c-865c-379af062e1a9"),
				Month:            time.Date(2024, 5, 0, 0, 0, 0, 0, time.UTC),
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  1,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
			expectedProjection: &Projection{
				SiteID:           uuid.MustParse("fb6cef30-990f-432c-865c-379af062e1a9"),
				Month:            time.Date(2024, 5, 0, 0, 0, 0, 0, time.UTC),
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   44.31,
				EnergyCost:       1445,
				RevenueGenerated: 2419,
				NumberOfCharges:  2,
				NumberOfDrivers:  2,
				TotalDuration:    820,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550"), uuid.MustParse("175c5832-836a-4b69-af64-6b9d7edc5cf5")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b"), uuid.MustParse("99521c91-9db7-4dbf-8c4f-f76e240be02e")),
			},
		},
		{
			name: "skips nullable fields from aggregate, but will still add charge id to list",
			chargeAggregate: &charges.Aggregate{
				AggregateID: uuid.MustParse("f336ccb8-4ce9-4555-b6e5-a6cc991299ee"),
			},
			initialProjection: &Projection{
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  1,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
			expectedProjection: &Projection{
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  2,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550"), uuid.MustParse("f336ccb8-4ce9-4555-b6e5-a6cc991299ee")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
		},
		{
			name:                "doesn't add a charge or driver id if it already exists",
			projectionUnchanged: true,
			chargeAggregate: &charges.Aggregate{
				AggregateID: uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550"),
				UserIDs:     collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
			initialProjection: &Projection{
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  1,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
			expectedProjection: &Projection{
				SiteName:         "LGMs Fleet Garage",
				EnergyUsageKwh:   12.14,
				EnergyCost:       502,
				RevenueGenerated: 654,
				NumberOfCharges:  1,
				NumberOfDrivers:  1,
				TotalDuration:    150,
				GroupID:          uuid.MustParse("3b0ea09c-d7c7-4712-bb80-9bf9fc034485"),
				GroupName:        "PodPoint",
				ChargeIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("788231c1-a0df-454a-ac63-94da20c48550")),
				DriverIDs:        collections.NewSet[uuid.UUID](uuid.MustParse("84cab708-f1a3-4380-8333-530d9601ab3b")),
			},
		},
		{
			name: "applies EnergyCostCorrected event correctly",
			chargeAggregate: &charges.Aggregate{
				AggregateID: uuid.MustParse("175c5832-836a-4b69-af64-6b9d7edc5cf5"),
				EnergyCost:  ptr.To(int32(652)),
				Events: []eventstore.Event{
					&charges.Costed{
						Payload: charges.CostedPayload{
							EnergyCost:         ptr.To(int32(502)),
							EnergyCostCurrency: "",
						},
					},
					&charges.EnergyCostCorrected{
						Payload: charges.EnergyCostCorrectedPayload{
							Cost:   652,
							Change: 150,
						},
					},
				},
			},
			initialProjection: &Projection{
				EnergyCost: 502,
			},
			expectedProjection: &Projection{
				EnergyCost: 652,
			},
			event: &charges.EnergyCostCorrected{
				Payload: charges.EnergyCostCorrectedPayload{
					Cost:   652,
					Change: 150,
				},
			},
		},
		{
			name: "applies SettlementAmountCorrected event correctly",
			chargeAggregate: &charges.Aggregate{
				AggregateID:      uuid.MustParse("53aab9ce-de46-4dd5-9859-8dc2c8004b62"),
				SettlementAmount: ptr.To(int32(579)),
				Events: []eventstore.Event{
					&charges.Billed{
						Payload: charges.BilledPayload{
							SettlementAmount:   123,
							SettlementCurrency: "",
						},
					},
					&charges.SettlementAmountCorrected{
						Payload: charges.SettlementAmountCorrectedPayload{
							SettlementAmount: 123,
							Change:           456,
						},
					},
				},
			},
			initialProjection: &Projection{
				RevenueGenerated: 123,
			},
			expectedProjection: &Projection{
				RevenueGenerated: 579,
			},
			event: &charges.SettlementAmountCorrected{
				Payload: charges.SettlementAmountCorrectedPayload{
					SettlementAmount: 123,
					Change:           456,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := tt.initialProjection.ApplyCharge(tt.chargeAggregate, tt.event)

			if !tt.projectionUnchanged {
				// Make sure the initial projection values aren't updated by reference
				require.NotEqual(t, *tt.expectedProjection, *tt.initialProjection)
			}
			// Make sure we have a new struct/address returned
			require.False(t, tt.initialProjection == got)
			require.Equal(t, tt.expectedProjection, got)
		})
	}
}
