package infra

import (
	"context"
	"database/sql"
	"errors"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	"experience/libs/data-platform/event-sourcing/projection/site-stats-monthly/infra/sqlc"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/jmoiron/sqlx"
)

type repository struct {
	dbWrite      *sqlx.DB
	queriesWrite *sqlc.Queries
	dbRead       *sqlx.DB
	queriesRead  *sqlc.Queries
}

func NewRepository(dbRead, dbWrite *sqlx.DB) sitestatsmonthly.Repository {
	return repository{dbWrite: dbWrite, queriesWrite: sqlc.New(dbWrite), dbRead: dbRead, queriesRead: sqlc.New(dbRead)}
}

func (r repository) Save(ctx context.Context, tx *sql.Tx, projection *sitestatsmonthly.Projection) error {
	params, err := ToUpsertSiteStatisticsParams(projection)
	if err != nil {
		return fmt.Errorf("copy to projection save params for site %s and month %s: %w", projection.SiteID, projection.Month, err)
	}

	_, err = r.queriesWrite.WithTx(tx).InsertOrUpdateSiteStatistics(ctx, *params)
	if err != nil {
		return fmt.Errorf("save projection for site %s and month %s: %w", projection.SiteID, projection.Month, err)
	}

	return nil
}

func (r repository) RetrieveSingleForUpdate(ctx context.Context, tx *sql.Tx, siteID uuid.UUID, month time.Time) (*sitestatsmonthly.Projection, error) {
	row, err := r.queriesWrite.WithTx(tx).RetrieveStatsBySiteAndMonthForUpdate(ctx, sqlc.RetrieveStatsBySiteAndMonthForUpdateParams{
		SiteID: siteID,
		Month:  month,
	})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sitestatsmonthly.ErrMonthlySiteStatisticsNotFound
		}
		return nil, err
	}
	projection, err := ToSingleMonthSiteStatsProjection(&row)
	return projection, err
}

func (r repository) RetrieveStatsForAllSitesByMonth(ctx context.Context, groupID uuid.UUID, month time.Time) ([]sitestatsmonthly.Projection, error) {
	rows, err := r.queriesRead.RetrieveStatsForAllSitesByMonth(ctx, sqlc.RetrieveStatsForAllSitesByMonthParams{
		GroupID: groupID,
		Month:   month,
	})

	if err != nil {
		return nil, err
	}
	return ToGroupSiteStatsProjection(rows)
}

func (r repository) GroupExists(ctx context.Context, groupID uuid.UUID) (bool, error) {
	return r.queriesRead.GroupExists(ctx, groupID.String())
}
