package infra

import (
	"context"
	"database/sql"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	"experience/libs/data-platform/event-sourcing/projection/site-stats-monthly/infra/sqlc"
	cfg "experience/libs/shared/go/db"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"experience/libs/shared/go/service/collections"
	"experience/libs/shared/go/service/test/assertions"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type TestSiteStatsMonthlyRepositorySuite struct {
	suite.Suite
	testDB      *test.Database
	underTest   sitestatsmonthly.Repository
	db          *sql.DB
	sqlcQueries *sqlc.Queries
}

func TestSiteStatsMonthlyRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping TestSiteStatsMonthlyRepositorySuite integration test")
	}
	suite.Run(t, new(TestSiteStatsMonthlyRepositorySuite))
}

func (s *TestSiteStatsMonthlyRepositorySuite) SetupSuite() {
	t := s.T()
	s.testDB = test.NewDatabase(t, test.WithUser("xdp_async_processor"))
	passwordConfig := s.testDB.PasswordConfig(t)
	dbh, err := postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(t, err)

	_, err = migrate.MigrateUp("file://../../../../golang-migrate/migration", s.testDB.PasswordConfig(t), nil, true)
	require.NoError(t, err)

	config := s.testDB.IamConfig(t)

	rwDB := postgres.NewReadWriteDB(&cfg.ServiceDatasource{
		ReadConfig:  cfg.ReadConfig{IAMConfig: config},
		WriteConfig: cfg.WriteConfig{IAMConfig: config},
	}, true)

	s.db = dbh
	s.sqlcQueries = sqlc.New(s.db)
	s.underTest = NewRepository(rwDB.WriteDB, rwDB.ReadDB)
}

func (s *TestSiteStatsMonthlyRepositorySuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *TestSiteStatsMonthlyRepositorySuite) AfterTest(_, _ string) {
	prepare, _ := s.db.Prepare("TRUNCATE TABLE projections.site_stats_monthly")
	_, _ = prepare.Exec()
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestSave() {
	t := s.T()
	ctx := context.Background()
	siteID := uuid.New()
	groupID := uuid.New()
	chargeID1 := uuid.New()
	chargeID2 := uuid.New()
	driverID1 := uuid.New()

	initialProjection := sitestatsmonthly.Projection{
		Month:            time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
		SiteID:           siteID,
		SiteName:         "Post Office 1",
		EnergyUsageKwh:   12.43,
		EnergyCost:       654,
		RevenueGenerated: 776,
		NumberOfCharges:  1,
		NumberOfDrivers:  1,
		TotalDuration:    975,
		GroupID:          groupID,
		GroupName:        "Royal Mail",
		ChargeIDs:        collections.NewSet[uuid.UUID](chargeID1),
		DriverIDs:        collections.NewSet[uuid.UUID](driverID1),
	}

	updatedProjection := sitestatsmonthly.Projection{
		Month:            time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
		SiteID:           siteID,
		SiteName:         "Post Office 1",
		EnergyUsageKwh:   25.6,
		EnergyCost:       999,
		RevenueGenerated: 999,
		NumberOfCharges:  2,
		NumberOfDrivers:  1,
		TotalDuration:    975,
		GroupID:          groupID,
		GroupName:        "Royal Mail",
		ChargeIDs:        collections.NewSet[uuid.UUID](chargeID1, chargeID2),
		DriverIDs:        collections.NewSet[uuid.UUID](driverID1),
	}

	// Given a projection was initially saved
	saveTx, err := s.db.Begin()
	require.NoError(t, err)
	err = s.underTest.Save(ctx, saveTx, &initialProjection)
	require.NoError(t, err)
	err = saveTx.Commit()
	require.NoError(t, err)

	// And has been retrieved for update
	tx1, err := s.db.BeginTx(ctx, &sql.TxOptions{})
	require.NoError(t, err)
	gotAfterFirstSave, err := s.underTest.RetrieveSingleForUpdate(ctx, tx1, initialProjection.SiteID, initialProjection.Month)
	require.NoError(t, err)
	assertions.AssertEqualsExclude(t, initialProjection, *gotAfterFirstSave, []string{"Month"})
	require.Equal(t, initialProjection.Month, gotAfterFirstSave.Month.UTC())

	// When we save a second time with the same transaction as the first retrieve
	err = s.underTest.Save(ctx, tx1, &updatedProjection)
	require.NoError(t, err)
	err = tx1.Commit()
	require.NoError(t, err)

	// Then the projection is successfully updated
	tx2, err := s.db.BeginTx(ctx, &sql.TxOptions{})
	require.NoError(t, err)
	gotAfterSecondSave, err := s.underTest.RetrieveSingleForUpdate(ctx, tx2, initialProjection.SiteID, initialProjection.Month)
	require.NoError(t, err)
	err = tx2.Commit()
	require.NoError(t, err)
	assertions.AssertEqualsExclude(t, updatedProjection, *gotAfterSecondSave, []string{"Month"})
	require.Equal(t, updatedProjection.Month, gotAfterSecondSave.Month.UTC())
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestRetrieveSingleForUpdate() {
	t := s.T()
	ctx := context.Background()
	siteID := uuid.New()
	groupID := uuid.New()
	chargeID1 := uuid.New()
	chargeID2 := uuid.New()
	driverID1 := uuid.New()

	inserted, err := s.sqlcQueries.InsertSiteStatistic(ctx, sqlc.InsertSiteStatisticParams{
		Month:            time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
		SiteID:           siteID,
		SiteName:         "Post Office 1",
		EnergyUsageKwh:   "12.43",
		EnergyCost:       654,
		RevenueGenerated: 776,
		NumberOfCharges:  2,
		NumberOfDrivers:  1,
		TotalDuration:    975,
		GroupID:          groupID,
		GroupName:        "Royal Mail",
		ChargeIds:        []uuid.UUID{chargeID1, chargeID2},
		DriverIds:        []uuid.UUID{driverID1},
	})
	require.NoError(t, err)

	expected := sitestatsmonthly.Projection{
		Month:            inserted.Month,
		SiteID:           siteID,
		SiteName:         "Post Office 1",
		EnergyUsageKwh:   12.43,
		EnergyCost:       654,
		RevenueGenerated: 776,
		NumberOfCharges:  2,
		NumberOfDrivers:  1,
		TotalDuration:    975,
		GroupID:          groupID,
		GroupName:        "Royal Mail",
		ChargeIDs:        collections.NewSet[uuid.UUID](chargeID1, chargeID2),
		DriverIDs:        collections.NewSet[uuid.UUID](driverID1),
	}

	tx, err := s.db.BeginTx(ctx, &sql.TxOptions{})
	require.NoError(t, err)
	gotProj, err := s.underTest.RetrieveSingleForUpdate(ctx, tx, siteID, time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC))
	require.NoError(t, err)
	require.Equal(t, expected, *gotProj)

	gotProjDateMiddleOfMonth, err := s.underTest.RetrieveSingleForUpdate(ctx, tx, siteID, time.Date(2024, 2, 23, 0, 0, 0, 0, time.UTC))
	require.NoError(t, err)
	err = tx.Commit()
	require.NoError(t, err)
	require.Equal(t, expected, *gotProjDateMiddleOfMonth)
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestRetrieveSingleForUpdateNoRow() {
	t := s.T()
	ctx := context.Background()

	tx, err := s.db.BeginTx(ctx, &sql.TxOptions{})
	require.NoError(t, err)
	gotProj, err := s.underTest.RetrieveSingleForUpdate(ctx, tx, uuid.New(), time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC))
	require.Nil(t, gotProj)
	require.ErrorIs(t, err, sitestatsmonthly.ErrMonthlySiteStatisticsNotFound)
	require.NoError(t, tx.Rollback())
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestRetrieveStatsForAllSitesByMonth() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.New()
	chargeID1 := uuid.New()
	chargeID2 := uuid.New()
	driverID1 := uuid.New()
	month := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	_, err := s.sqlcQueries.InsertSiteStatistic(ctx, sqlc.InsertSiteStatisticParams{
		Month:            time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
		SiteID:           siteID,
		SiteName:         "Post Office 1",
		EnergyUsageKwh:   "12.43",
		EnergyCost:       654,
		RevenueGenerated: 776,
		NumberOfCharges:  2,
		NumberOfDrivers:  1,
		TotalDuration:    975,
		GroupID:          groupID,
		GroupName:        "Royal Mail",
		ChargeIds:        []uuid.UUID{chargeID1, chargeID2},
		DriverIds:        []uuid.UUID{driverID1},
	})
	require.NoError(t, err)

	projections, err := s.underTest.RetrieveStatsForAllSitesByMonth(ctx, groupID, month)
	require.NoError(t, err)

	// Assert that the returned data is correct
	require.Len(t, projections, 1)
	require.Equal(t, groupID, projections[0].GroupID)
	require.Equal(t, collections.NewSet[uuid.UUID](chargeID1, chargeID2), projections[0].ChargeIDs)
	require.Equal(t, collections.NewSet[uuid.UUID](driverID1), projections[0].DriverIDs)
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestRetrieveStatsForAllSitesByMonthNoRows() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	month := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	projections, err := s.underTest.RetrieveStatsForAllSitesByMonth(ctx, groupID, month)
	require.NoError(t, err)

	// Assert that the returned data is empty
	require.Len(t, projections, 0)
}

func (s *TestSiteStatsMonthlyRepositorySuite) TestGroupExists() {
	t := s.T()
	ctx := context.Background()
	group, err := s.sqlcQueries.CreateGroup(ctx, uuid.New().String())
	require.NoError(t, err)

	result, err := s.underTest.GroupExists(ctx, uuid.MustParse(group.Uid))
	require.NoError(t, err)
	require.True(t, result)

	result, err = s.underTest.GroupExists(ctx, uuid.New())
	require.NoError(t, err)
	require.False(t, result)
}
