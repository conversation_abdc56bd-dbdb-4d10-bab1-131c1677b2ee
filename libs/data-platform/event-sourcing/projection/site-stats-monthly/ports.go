package sitestatsmonthly

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
)

type Service interface {
	RetrieveStatsForAllSitesByMonth(ctx context.Context, groupID uuid.UUID, month time.Time) ([]Projection, error)
}

type Repository interface {
	Save(ctx context.Context, tx *sql.Tx, projection *Projection) error
	RetrieveSingleForUpdate(ctx context.Context, tx *sql.Tx, siteID uuid.UUID, month time.Time) (*Projection, error)
	RetrieveStatsForAllSitesByMonth(ctx context.Context, groupID uuid.UUID, month time.Time) ([]Projection, error)
	GroupExists(ctx context.Context, groupID uuid.UUID) (bool, error)
}
