package api_test

import (
	contractsites "experience/libs/data-platform/api/contract/gen/sites"
	"experience/libs/data-platform/event-sourcing/projection/sites"
	"experience/libs/data-platform/event-sourcing/projection/sites/api"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/random"
	"experience/libs/shared/go/service/utils"
	"testing"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestModelsToSiteChargeInformation(t *testing.T) {
	groupID := uuid.New()
	groupName := random.SiteName(t)

	tests := []struct {
		name  string
		input []sites.SiteStats
		want  []*contractsites.SiteStats
	}{
		{
			name: "valid values mapped to site charge information",
			input: []sites.SiteStats{
				{
					SiteID:           utils.FabricateUUIDFromNumericID(123),
					SiteName:         ptr.To("Site 1"),
					GroupID:          &groupID,
					GroupName:        &groupName,
					EnergyTotal:      4.5,
					RevenueGenerated: 20,
				},
				{
					SiteID:           utils.FabricateUUIDFromNumericID(456),
					SiteName:         ptr.To("Site 2"),
					GroupID:          &groupID,
					GroupName:        &groupName,
					EnergyTotal:      19.27,
					RevenueGenerated: 500,
				},
			},
			want: []*contractsites.SiteStats{
				{
					ID:               "123",
					Name:             ptr.To("Site 1"),
					GroupID:          ptr.To(groupID.String()),
					GroupName:        ptr.To(groupName),
					TotalEnergy:      4.5,
					RevenueGenerated: 20,
				},
				{
					ID:               "456",
					Name:             ptr.To("Site 2"),
					GroupID:          ptr.To(groupID.String()),
					GroupName:        ptr.To(groupName),
					TotalEnergy:      19.27,
					RevenueGenerated: 500,
				},
			},
		},
		{
			name: "nil values mapped to nil in site with energy delivered",
			input: []sites.SiteStats{
				{
					SiteID: utils.FabricateUUIDFromNumericID(123),
				},
			},
			want: []*contractsites.SiteStats{
				{
					ID: "123",
				},
			},
		},
		{
			name:  "empty model slice gives empty slice of site charge info",
			input: []sites.SiteStats{},
			want:  []*contractsites.SiteStats{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := api.ModelsToSiteChargeInformation(tt.input)

			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}
