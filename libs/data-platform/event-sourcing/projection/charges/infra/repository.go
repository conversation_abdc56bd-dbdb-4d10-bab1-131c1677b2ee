package infra

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/projection/charges"
	"experience/libs/data-platform/event-sourcing/projection/charges/infra/sqlc"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type repository struct {
	dbRead      *sqlx.DB
	queriesRead *sqlc.Queries
}

func NewRepository(dbRead *sqlx.DB) charges.Repository {
	return repository{dbRead: dbRead, queriesRead: sqlc.New(dbRead)}
}

func (r repository) RetrieveProjectionCharges(ctx context.Context, params charges.RetrieveProjectionChargesParams) ([]charges.Projection, error) {
	var groupIDNull, siteIDNull uuid.NullUUID
	var chargerIDNull sql.NullString

	if params.GroupID != nil {
		groupIDNull = uuid.NullUUID{UUID: *params.GroupID, Valid: true}
	}
	if params.SiteID != nil {
		siteIDNull = uuid.NullUUID{UUID: *params.SiteID, Valid: true}
	}
	if params.ChargerID != nil {
		chargerIDNull = sql.NullString{String: *params.ChargerID, Valid: true}
	}

	rows, err := r.queriesRead.RetrieveProjectionCharges(ctx, sqlc.RetrieveProjectionChargesParams{
		FromDate:  params.FromDate,
		ToDate:    params.ToDate,
		Groupid:   groupIDNull,
		Siteid:    siteIDNull,
		Chargerid: chargerIDNull,
	})

	if err != nil {
		return nil, err
	}

	return ToProjectionChargesRow(rows)
}
