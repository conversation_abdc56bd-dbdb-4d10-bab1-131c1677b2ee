package random

import (
	"database/sql"
	chargeprojection "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"fmt"
	"time"

	"github.com/google/uuid"
)

func TimesCreateChargeProjections(times uint, seed *ChargeParamsSeed) []chargeprojection.Projection {
	projections := make([]chargeprojection.Projection, times)
	for i := uint(0); i < times; i++ {
		projections[i], _ = CreateChargeProjection(seed)
	}
	return projections
}

func CreateChargeProjection(seed *ChargeParamsSeed) (chargeprojection.Projection, sqlc.CreateChargeProjectionParams) {
	ccpp := CreateChargeProjectionParams(seed)
	cp, _ := infra.ToChargeProjection(&ccpp)
	return *cp, ccpp
}

func CreateChargeProjectionParams(seed *ChargeParamsSeed) sqlc.CreateChargeProjectionParams {
	var siteID uuid.UUID
	var groupID uuid.UUID
	var chargerID string
	if seed.GroupID != nil {
		groupID = *seed.GroupID
	}
	if seed.SiteID != nil {
		siteID = *seed.SiteID
	}
	if seed.ChargerID != nil {
		chargerID = *seed.ChargerID
	}

	ccpp := sqlc.CreateChargeProjectionParams{
		ChargeUUID:            uuid.New(),
		SiteName:              sql.NullString{String: *seed.SiteName, Valid: seed.SiteName != nil},
		ChargerID:             sql.NullString{String: chargerID, Valid: seed.ChargerID != nil},
		ChargerName:           sql.NullString{String: *seed.ChargerName, Valid: seed.ChargerName != nil},
		PluggedInAt:           sql.NullTime{Time: *seed.PluggedInAt, Valid: seed.PluggedInAt != nil},
		StartedAt:             sql.NullTime{Time: *seed.StartedAt, Valid: seed.StartedAt != nil},
		EndedAt:               sql.NullTime{Time: *seed.EndedAt, Valid: seed.EndedAt != nil},
		UnpluggedAt:           sql.NullTime{Time: *seed.UnpluggedAt, Valid: seed.UnpluggedAt != nil},
		EnergyTotal:           sql.NullString{String: fmt.Sprintf("%f", *seed.EnergyTotal), Valid: seed.EnergyTotal != nil},
		GenerationEnergyTotal: sql.NullString{String: fmt.Sprintf("%f", *seed.GenerationEnergyTotal), Valid: seed.GenerationEnergyTotal != nil},
		GridEnergyTotal:       sql.NullString{String: fmt.Sprintf("%f", *seed.GridEnergyTotal), Valid: seed.GridEnergyTotal != nil},
		Door:                  sql.NullString{String: *seed.Door, Valid: seed.Door != nil},
		UserIds:               []uuid.UUID{seed.DriverID},
		SettlementAmount:      sql.NullInt32{Int32: *seed.RevenueGenerated, Valid: seed.RevenueGenerated != nil},
		ChargeDurationTotal:   sql.NullInt32{Int32: *seed.ChargeDurationTotal, Valid: seed.ChargeDurationTotal != nil},
		EnergyCost:            sql.NullInt32{Int32: *seed.EnergyCost, Valid: seed.EnergyCost != nil},
		GroupID:               uuid.NullUUID{UUID: groupID, Valid: seed.GroupID != nil},
		SiteID:                uuid.NullUUID{UUID: siteID, Valid: seed.SiteID != nil},
		Confirmed:             sql.NullBool{Bool: *seed.Confirmed, Valid: seed.Confirmed != nil},
	}

	return ccpp
}

type ChargeParamsSeed struct {
	PluggedInAt           *time.Time
	StartedAt             *time.Time
	EndedAt               *time.Time
	UnpluggedAt           *time.Time
	EnergyTotal           *float32
	GenerationEnergyTotal *float32
	GridEnergyTotal       *float32
	ChargeDurationTotal   *int32
	RevenueGenerated      *int32
	SettlementCurrency    *string
	EnergyCost            *int32
	ChargerType           *sqlc.Access
	ChargerTimezone       *string
	ChargerName           *string
	SiteName              *string
	ChargerID             *string
	GroupID               *uuid.UUID
	Door                  *string
	DriverID              uuid.UUID
	SiteID                *uuid.UUID
	Confirmed             *bool
	Points                *float32
	PointsExpiryDate      *time.Time
	PointsRedeemedAt      *time.Time
}
