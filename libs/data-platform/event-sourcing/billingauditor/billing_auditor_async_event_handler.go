package billingauditor

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	"fmt"
	"log"
)

type billingAuditorAsyncEventHandler struct {
	logger                *log.Logger
	billingAuditorService Service
	aggregateLoader       charges.AggregateLoader
}

func NewBillingAuditorAsyncEventHandler(logger *log.Logger, billingAuditorService Service, aggregateLoader charges.AggregateLoader) handlers.AsyncEventHandler {
	return &billingAuditorAsyncEventHandler{
		logger,
		billingAuditorService,
		aggregateLoader,
	}
}

func (u *billingAuditorAsyncEventHandler) Execute(ctx context.Context, _ *sql.Tx, event eventstore.Event, _ uint64) error {
	switch event := event.(type) {
	case *charges.Completed:
		return u.onCompleted(ctx, event)
	case *charges.Claimed:
		return u.onClaimed(ctx, event)
	case *charges.Billed:
		return u.onBilled(ctx, event)
	default:
		return nil
	}
}

func (u *billingAuditorAsyncEventHandler) onCompleted(ctx context.Context, event *charges.Completed) error {
	aggregate, err := u.aggregateLoader.LoadCharge(ctx, event.GetAggregateID())
	if err != nil {
		return fmt.Errorf("loading charge aggregate %s: %w", event.GetAggregateID(), err)
	}
	err = u.billingAuditorService.Audit(ctx, aggregate, event)
	if err != nil {
		return fmt.Errorf("auditing charge %s: %w", event.GetAggregateID(), err)
	}

	u.logger.Printf("billing auditor processed event type %v for aggregate ID %v \n", event.GetType(), event.AggregateID)
	return nil
}

func (u *billingAuditorAsyncEventHandler) onClaimed(ctx context.Context, event *charges.Claimed) error {
	aggregate, err := u.aggregateLoader.LoadCharge(ctx, event.GetAggregateID())
	if err != nil {
		return fmt.Errorf("loading charge aggregate %s: %w", event.GetAggregateID(), err)
	}
	err = u.billingAuditorService.UpdateAuditWithAuthoriser(ctx, aggregate)
	if err != nil {
		u.logger.Printf("error updating audit with authoriser: %v", err)
		return nil
	}

	u.logger.Printf("billing auditor processed event type %v for aggregate ID %v \n", event.GetType(), event.AggregateID)
	return err
}

func (u *billingAuditorAsyncEventHandler) onBilled(ctx context.Context, event *charges.Billed) error {
	aggregate, err := u.aggregateLoader.LoadCharge(ctx, event.GetAggregateID())
	if err != nil {
		return fmt.Errorf("loading charge aggregate %s: %w", event.GetAggregateID(), err)
	}
	err = u.billingAuditorService.MarkAsBilled(ctx, aggregate)
	if err != nil {
		u.logger.Printf("error marking audited charge as billed: %v", err)
		return nil
	}

	u.logger.Printf("billing auditor processed event type %v for aggregate ID %v \n", event.GetType(), event.AggregateID)
	return err
}
