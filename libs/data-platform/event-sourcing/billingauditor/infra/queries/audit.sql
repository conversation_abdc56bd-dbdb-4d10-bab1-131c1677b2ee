-- name: FindAuditableCharge :one
WITH charge AS (SELECT CASE
                         WHEN
                           EXISTS(SELECT 1
                                  FROM podpoint.charges c
                                  WHERE c.id = $1
                                    AND billing_event_id IS NOT NULL)
                           THEN TRUE
                         ELSE FALSE
                         END                       AS has_billing_event,
                       CASE
                         WHEN
                           EXISTS (SELECT 1
                                   FROM podpoint.charges c
                                          INNER JOIN podpoint.pod_locations pl
                                                     ON c.location_id = pl.id
                                                       AND pl.is_home = 1
                                   WHERE c.id = $1)
                           THEN TRUE
                         ELSE FALSE
                         END                       AS is_home,
                       CASE
                         WHEN
                           EXISTS (SELECT 1
                                   FROM podpoint.charges c
                                          INNER JOIN podpoint.pod_locations pl
                                                     ON c.location_id = pl.id
                                          LEFT JOIN podpoint.revenue_profiles rp
                                                    ON rp.id = pl.revenue_profile_id
                                          LEFT JOIN podpoint.revenue_profile_tiers rpt
                                                    ON rpt.revenue_profile_id = rp.id
                                   WHERE c.id = $1
                                     AND pl.payg_enabled = 0
                                     AND rp.deleted_at IS NULL
                                     AND rpt.deleted_at IS NULL
                                   GROUP BY c.id, pl.id, rp.id, rpt.id
                                   HAVING SUM(COALESCE(rpt.rate, 0)) = 0)
                           THEN TRUE
                         ELSE FALSE
                         END                       AS is_free_vend,
                       pm.supports_ocpp::int::bool AS supports_ocpp
                FROM podpoint.charges c
                       INNER JOIN podpoint.pod_units pu
                                  ON c.unit_id = pu.id
                       INNER JOIN podpoint.pod_models pm
                                  ON pm.id = pu.model_id
                WHERE c.id = $1)
SELECT *
FROM charge;

-- name: FindAuditableChargeByAggregateID :one
SELECT
  id,
  aggregate_id,
  location_id,
  charging_duration,
  plugged_in_duration,
  claim_charge_id,
  started_at,
  plugged_in_at,
  billed,
  energy_kwh,
  group_id,
  group_name,
  authoriser_id,
  authoriser_type,
  ppid,
  door,
  charge_cycle_id,
  supports_ocpp
FROM projections.audited_charges
WHERE aggregate_id = $1;

-- name: SaveUnbilledCharge :one
INSERT INTO projections.audited_charges (aggregate_id,
                                         location_id,
                                         charging_duration,
                                         plugged_in_duration,
                                         claim_charge_id,
                                         started_at,
                                         plugged_in_at,
                                         billed,
                                         energy_kwh,
                                         group_id,
                                         group_name,
                                         authoriser_id,
                                         authoriser_type,
                                         ppid,
                                         door,
                                         charge_cycle_id,
                                         supports_ocpp)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
ON CONFLICT (aggregate_id)
  DO UPDATE SET location_id         = $2,
                charging_duration   = $3,
                plugged_in_duration = $4,
                claim_charge_id     = $5,
                started_at          = $6,
                plugged_in_at       = $7,
                billed              = $8,
                energy_kwh          = $9,
                group_id            = $10,
                group_name          = $11,
                authoriser_id       = $12,
                authoriser_type     = $13,
                ppid                = $14,
                door                = $15,
                charge_cycle_id     = $16,
                supports_ocpp       = $17
RETURNING *;
