package billingauditor

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/event-sourcing/billingauditor"
	"experience/libs/data-platform/event-sourcing/billingauditor/infra/sqlc"
	"experience/libs/shared/go/db"
	"fmt"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"k8s.io/utils/ptr"
)

type auditableChargeRepository struct {
	readQueries  *sqlc.Queries
	writeQueries *sqlc.Queries
}

// NewAuditableChargeRepository NewRepository creates a new repository instance.
func NewAuditableChargeRepository(reader, writer sqlc.DBTX) billingauditor.AuditRepository {
	return &auditableChargeRepository{
		readQueries:  sqlc.New(reader), // could split these up if needed
		writeQueries: sqlc.New(writer),
	}
}

func (a auditableChargeRepository) FindByID(ctx context.Context, chargeID int64) (*billingauditor.Charge, error) {
	charge, err := a.readQueries.FindAuditableCharge(ctx, chargeID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, billingauditor.ErrChargeNotFound
		}
		return nil, err
	}
	return billingauditor.NewCharge(charge.HasBillingEvent, charge.IsHome, charge.IsFreeVend, charge.SupportsOcpp), nil
}

func (a auditableChargeRepository) FindAuditableChargeByAggregateID(ctx context.Context, aggregateID uuid.UUID) (*billingauditor.UnbilledCharge, error) {
	aggregate, err := a.writeQueries.FindAuditableChargeByAggregateID(ctx, aggregateID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, billingauditor.ErrChargeNotFound
		}
		return nil, err
	}

	unbilledCharge := &billingauditor.UnbilledCharge{}
	copyErr := copier.CopyWithOption(unbilledCharge, aggregate, copier.Option{
		Converters: []copier.TypeConverter{
			db.NullUUIDToUUIDPointerConverter},
	})
	if copyErr != nil {
		return nil, fmt.Errorf("copy unbilled charge: %w", copyErr)
	}

	return unbilledCharge, nil
}

func (a auditableChargeRepository) SaveUnbilledCharge(ctx context.Context, charge *billingauditor.UnbilledCharge) (*billingauditor.UnbilledCharge, error) {
	result, err := a.writeQueries.SaveUnbilledCharge(ctx, sqlc.SaveUnbilledChargeParams{
		AggregateID:       charge.AggregateID,
		LocationID:        db.ToNullInt32(&charge.LocationID),
		ClaimChargeID:     db.ToNullInt32(&charge.ClaimChargeID),
		ChargingDuration:  db.ToNullInt32(&charge.ChargingDuration),
		PluggedInDuration: db.ToNullInt32(&charge.PluggedInDuration),
		StartedAt:         db.ToNullTime(charge.StartedAt),
		PluggedInAt:       db.ToNullTime(&charge.PluggedInAt),
		Billed:            charge.Billed,
		EnergyKwh:         db.ToNullString(&charge.EnergyKWh),
		ChargeCycleID:     db.ToNullString(&charge.ChargeCycleID),
		Door:              db.ToNullString(ptr.To(charge.Door)),
		SupportsOcpp:      charge.SupportsOCPP,
		GroupID:           db.ToNullUUID(charge.GroupID),
		GroupName:         db.ToNullString(&charge.GroupName),
		AuthoriserID:      db.ToNullInt32(&charge.AuthoriserID),
		AuthoriserType:    db.ToNullString(&charge.AuthoriserType),
		Ppid:              db.ToNullString(&charge.PPID),
	})
	if err != nil {
		return nil, fmt.Errorf("save unbilled charge: %w", err)
	}
	unbilledCharge := billingauditor.UnbilledCharge{}
	copyErr := copier.CopyWithOption(&unbilledCharge, result, copier.Option{
		Converters: []copier.TypeConverter{
			db.NullUUIDToUUIDPointerConverter,
		},
	})
	if copyErr != nil {
		return nil, fmt.Errorf("copy unbilled charge: %w", copyErr)
	}

	return &unbilledCharge, nil
}
