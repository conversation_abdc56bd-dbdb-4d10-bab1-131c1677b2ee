{"name": "data-platform-event-sourcing-guestbilling", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-platform/event-sourcing/guestbilling", "implicitDependencies": ["shared-go-test", "data-platform-event-sourcing-domain"], "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["go run libs/shared/go/sqlc/generate.go -config libs/data-platform/event-sourcing/guestbilling/sqlc.yaml", "mockgen -destination libs/data-platform/event-sourcing/guestbilling/mock/ports.go -source=libs/data-platform/event-sourcing/guestbilling/ports.go"], "parallel": false}}, "test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}}, "tags": ["data-platform"]}