package guestbilling_test

import (
	"bytes"
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	mockcharges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	"experience/libs/data-platform/event-sourcing/guestbilling"
	mockguestbilling "experience/libs/data-platform/event-sourcing/guestbilling/mock"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"log"
	"math"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func Test_guestBillingAsyncEventHandler_execute(t *testing.T) {
	chargeID := gofakeit.IntRange(1, math.MaxInt32)
	aggregateID := utils.FabricateUUIDFromNumericID(chargeID)
	unpluggedAt := time.Date(2025, 4, 3, 2, 1, 0, 0, time.UTC)

	tests := []struct {
		name                string
		event               charges.Billed
		mockLoadedAggregate *charges.Aggregate
		mockBillError       error
		expectedError       error
		expectedLogMessage  string
	}{
		{
			name: "returns error if there was no completed event on the aggregate",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeGuest.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &unpluggedAt,
				Events:      []eventstore.Event{},
			},
			expectedError: fmt.Errorf("finding completed event %s: cannot find a completed event", aggregateID.String()),
		},
		{
			name: "prints error if billing via guest billing service fails",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					SettlementAmount:   123,
					SettlementCurrency: "GBP",
					BillingType:        billing.TypeGuest.String(),
				},
			},
			mockLoadedAggregate: &charges.Aggregate{
				AggregateID: aggregateID,
				UnpluggedAt: &unpluggedAt,
				Events: []eventstore.Event{
					&charges.Completed{},
				},
			},
			mockBillError:      fmt.Errorf("mock_bill_error"),
			expectedLogMessage: "error when billing via guest billing service: mock_bill_error",
		},
		{
			name: "does not process event if billing type is not guest",
			event: charges.Billed{
				Base: charges.Base{
					AggregateID: aggregateID,
					Event:       charges.TypeBilled,
					EventID:     uuid.NewString(),
					PublishedAt: time.Time{},
					Historic:    false,
				},
				Payload: charges.BilledPayload{
					BillingType: "not_guest",
				},
			},
			mockLoadedAggregate: &charges.Aggregate{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockAggregateLoader := mockcharges.NewMockAggregateLoader(ctrl)
			mockAggregateLoader.
				EXPECT().
				LoadCharge(context.Background(), gomock.Any()).
				AnyTimes().
				Return(tt.mockLoadedAggregate, nil)

			mockGuestBillingService := mockguestbilling.NewMockService(ctrl)
			mockGuestBillingService.
				EXPECT().
				Bill(gomock.Any(), gomock.Any(), tt.event.Payload.SettlementAmount).
				Return(tt.mockBillError).
				AnyTimes()

			writer := log.Writer()
			var buf bytes.Buffer
			log.SetOutput(&buf)
			defer func() {
				log.SetOutput(writer)
			}()

			u := guestbilling.NewGuestBillingAsyncEventHandler(
				log.Default(),
				mockGuestBillingService,
				mockAggregateLoader,
			)

			err := u.Execute(context.Background(), nil, &tt.event, 1)

			if tt.expectedError != nil {
				require.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}

			require.Contains(t, buf.String(), tt.expectedLogMessage)
		})
	}
}
