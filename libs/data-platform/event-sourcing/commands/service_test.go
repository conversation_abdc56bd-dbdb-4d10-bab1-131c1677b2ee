package commands_test

import (
	"bytes"
	"context"
	"experience/libs/data-platform/event-sourcing/commands"
	mockcommands "experience/libs/data-platform/event-sourcing/commands/mock"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	mockcharges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	mockchargeprojection "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/mock"
	eventstore "experience/libs/shared/go/event-store"
	mockeventstore "experience/libs/shared/go/event-store/test/mock"
	"fmt"
	"log"
	"testing"

	"github.com/jinzhu/copier"

	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

func TestRecordEnergyCostCorrectedEvent(t *testing.T) {
	validUUID := uuid.MustParse("7da678e9-e484-4b61-afd1-0f6840616e63")

	tests := []struct {
		name                              string
		chargeID                          uuid.UUID
		submittedBy                       string
		expectedLogMessage                string
		mockExpensedToFromLoadedAggregate *uuid.UUID
		mockEnergyCostFromLoadedAggregate *int32
		mockErrorLoadingAggregate         error
		mockErrorSavingToEventStore       error
		mockChargeProjectionSaveError     error
		mockErrorCommittingTransaction    error
		expectedError                     error
		mockErrorUpdatingPodadminCharge   error
		converter                         *copier.TypeConverter
	}{
		{
			name:                              "returns no error if event can be correctly recorded",
			chargeID:                          validUUID,
			submittedBy:                       "example-submitter",
			mockEnergyCostFromLoadedAggregate: ptr.To(int32(42)),
		},
		{
			name:                              "returns no error even if submittedBy is an empty string",
			chargeID:                          validUUID,
			submittedBy:                       "",
			mockEnergyCostFromLoadedAggregate: ptr.To(int32(42)),
		},
		{
			name:        "returns no error even if the loaded aggregate has a nil pointer EnergyCost",
			chargeID:    validUUID,
			submittedBy: "example-submitter",
		},
		{
			name:                              "returns error when charge has already been expensed",
			chargeID:                          validUUID,
			submittedBy:                       "example-submitter",
			mockExpensedToFromLoadedAggregate: ptr.To(uuid.New()),
			expectedError:                     charges.ErrChargeAlreadyExpensed,
		},
		{
			name:                      "returns error when aggregate loading failed",
			chargeID:                  validUUID,
			submittedBy:               "example-submitter",
			mockErrorLoadingAggregate: fmt.Errorf("mock_error_aggregate_loading"),
			expectedError:             commands.ErrChargeNotFound,
		},
		{
			name:                              "returns error when saving to event store failed",
			chargeID:                          validUUID,
			submittedBy:                       "example-submitter",
			mockEnergyCostFromLoadedAggregate: ptr.To(int32(42)),
			mockErrorSavingToEventStore:       fmt.Errorf("mock_error_saving_to_event_store"),
			expectedError:                     fmt.Errorf("unable to save event to event_store: mock_error_saving_to_event_store"),
		},
		{
			name:                              "returns event out of date error directly",
			chargeID:                          validUUID,
			submittedBy:                       "example-submitter",
			mockEnergyCostFromLoadedAggregate: ptr.To(int32(42)),
			mockErrorSavingToEventStore:       eventstore.ErrEventIsOutOfDate,
			expectedError:                     eventstore.ErrEventIsOutOfDate,
		},
		{
			name:                              "returns error when saving charge projection failed",
			chargeID:                          validUUID,
			submittedBy:                       "example-submitter",
			mockEnergyCostFromLoadedAggregate: ptr.To(int32(42)),
			mockChargeProjectionSaveError:     fmt.Errorf("mock_chargeprojection_save_error"),
			expectedError:                     fmt.Errorf("saving charge projection: mock_chargeprojection_save_error"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			newCost := int32(1337)

			ctrl := gomock.NewController(t)
			mockDataStore := mockcommands.NewMockRepository(ctrl)

			mockAggregateLoader := mockcharges.NewMockAggregateLoader(ctrl)
			mockEventStore := mockeventstore.NewMockStore(ctrl)
			mockProjection := mockchargeprojection.NewMockRepository(ctrl)

			var buff bytes.Buffer
			mockLogger := log.New(&buff, "[commands_test]", log.Lmsgprefix)

			svc := commands.NewService(mockLogger, mockDataStore)

			mockDataStore.EXPECT().Atomic(gomock.Any(), gomock.Any()).DoAndReturn(
				func(_ context.Context, callback func(r commands.Repository) (charges.Aggregate, error)) (charges.Aggregate, error) {
					result, err := callback(mockDataStore)
					if err != nil {
						return charges.Aggregate{}, err
					}
					return result, nil
				}).AnyTimes()

			mockDataStore.EXPECT().AggregateLoader().Return(mockAggregateLoader).AnyTimes()
			mockDataStore.EXPECT().EventStore().Return(mockEventStore).AnyTimes()
			mockDataStore.EXPECT().Projection().Return(mockProjection).AnyTimes()

			mockAggregateLoader.EXPECT().LoadCharge(gomock.Any(), gomock.Any()).Return(&charges.Aggregate{
				EnergyCost: test.mockEnergyCostFromLoadedAggregate,
				ExpensedTo: test.mockExpensedToFromLoadedAggregate,
			}, test.mockErrorLoadingAggregate).AnyTimes()

			mockEventStore.EXPECT().Save(gomock.Any(), gomock.Eq(validUUID), gomock.Any(), gomock.Any()).AnyTimes().Return(test.mockErrorSavingToEventStore)
			mockProjection.EXPECT().Save(gomock.Any(), gomock.Any()).AnyTimes().Return(test.mockChargeProjectionSaveError)

			newAggregate, err := svc.RecordEnergyCostCorrectedEvent(context.Background(), test.chargeID, newCost, test.submittedBy)

			logMessages := buff.String()
			if test.expectedLogMessage != "" {
				require.Contains(t, logMessages, test.expectedLogMessage)
			} else {
				require.Empty(t, buff.String())
			}

			if test.expectedError != nil {
				require.Error(t, err)
				require.Equal(t, test.expectedError.Error(), err.Error())
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, ptr.To(newCost), newAggregate.EnergyCost)
		})
	}
}

func TestRecordSettlementAmountCorrectedEvent(t *testing.T) {
	validUUID := uuid.MustParse("7da678e9-e484-4b61-afd1-0f6840616e63")

	tests := []struct {
		name                                    string
		chargeID                                uuid.UUID
		submittedBy                             string
		expectedLogMessage                      string
		mockSettlementAmountFromLoadedAggregate *int32
		mockErrorLoadingAggregate               error
		mockErrorSavingToEventStore             error
		mockChargeProjectionSaveError           error
		mockErrorCommittingTransaction          error
		txError                                 error
		rollbackError                           error
		expectedError                           error
		mockErrorUpdatingPodadminCharge         error
		converter                               *copier.TypeConverter
	}{
		{
			name:                                    "returns no error if event can be correctly recorded",
			chargeID:                                validUUID,
			submittedBy:                             "example-submitter",
			mockSettlementAmountFromLoadedAggregate: ptr.To(int32(42)),
		},
		{
			name:                                    "returns no error even if submittedBy is an empty string",
			chargeID:                                validUUID,
			submittedBy:                             "",
			mockSettlementAmountFromLoadedAggregate: ptr.To(int32(42)),
		},
		{
			name:        "returns no error even if the loaded aggregate has a nil pointer EnergyCost",
			chargeID:    validUUID,
			submittedBy: "example-submitter",
		},
		{
			name:                      "returns error when aggregate loading failed",
			chargeID:                  validUUID,
			submittedBy:               "example-submitter",
			mockErrorLoadingAggregate: fmt.Errorf("mock_error_aggregate_loading"),
			expectedError:             commands.ErrChargeNotFound,
		},
		{
			name:                                    "returns error when saving to event store failed",
			chargeID:                                validUUID,
			submittedBy:                             "example-submitter",
			mockSettlementAmountFromLoadedAggregate: ptr.To(int32(42)),
			mockErrorSavingToEventStore:             fmt.Errorf("mock_error_saving_to_event_store"),
			expectedError:                           fmt.Errorf("unable to save event to event_store: mock_error_saving_to_event_store"),
		},
		{
			name:                                    "returns event out of date error directly",
			chargeID:                                validUUID,
			submittedBy:                             "example-submitter",
			mockSettlementAmountFromLoadedAggregate: ptr.To(int32(42)),
			mockErrorSavingToEventStore:             eventstore.ErrEventIsOutOfDate,
			expectedError:                           eventstore.ErrEventIsOutOfDate,
		},
		{
			name:                                    "returns error when saving charge projection failed",
			chargeID:                                validUUID,
			submittedBy:                             "example-submitter",
			mockSettlementAmountFromLoadedAggregate: ptr.To(int32(42)),
			mockChargeProjectionSaveError:           fmt.Errorf("mock_chargeprojection_save_error"),
			expectedError:                           fmt.Errorf("saving charge projection: mock_chargeprojection_save_error"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			newSettlementAmount := int32(1337)

			ctrl := gomock.NewController(t)
			mockDataStore := mockcommands.NewMockRepository(ctrl)

			mockAggregateLoader := mockcharges.NewMockAggregateLoader(ctrl)
			mockEventStore := mockeventstore.NewMockStore(ctrl)
			mockProjection := mockchargeprojection.NewMockRepository(ctrl)

			var buff bytes.Buffer
			mockLogger := log.New(&buff, "[commands_test]", log.Lmsgprefix)

			svc := commands.NewService(mockLogger, mockDataStore)

			mockDataStore.EXPECT().Atomic(gomock.Any(), gomock.Any()).DoAndReturn(
				func(_ context.Context, callback func(r commands.Repository) (charges.Aggregate, error)) (charges.Aggregate, error) {
					result, err := callback(mockDataStore)
					if err != nil {
						return charges.Aggregate{}, err
					}
					return result, nil
				}).AnyTimes()

			mockDataStore.EXPECT().AggregateLoader().Return(mockAggregateLoader).AnyTimes()
			mockDataStore.EXPECT().EventStore().Return(mockEventStore).AnyTimes()
			mockDataStore.EXPECT().Projection().Return(mockProjection).AnyTimes()

			mockAggregateLoader.EXPECT().LoadCharge(gomock.Any(), gomock.Any()).Return(&charges.Aggregate{
				EnergyCost: test.mockSettlementAmountFromLoadedAggregate,
			}, test.mockErrorLoadingAggregate).AnyTimes()

			mockEventStore.EXPECT().Save(gomock.Any(), gomock.Eq(validUUID), gomock.Any(), gomock.Any()).AnyTimes().Return(test.mockErrorSavingToEventStore)
			mockProjection.EXPECT().Save(gomock.Any(), gomock.Any()).AnyTimes().Return(test.mockChargeProjectionSaveError)

			newAggregate, err := svc.RecordSettlementAmountCorrectedEvent(context.Background(), test.chargeID, newSettlementAmount, test.submittedBy)

			logMessages := buff.String()
			if test.expectedLogMessage != "" {
				require.Contains(t, logMessages, test.expectedLogMessage)
			} else {
				require.Empty(t, buff.String())
			}

			if test.expectedError != nil {
				require.Error(t, err)
				require.Equal(t, test.expectedError.Error(), err.Error())
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, ptr.To(newSettlementAmount), newAggregate.SettlementAmount)
		})
	}
}
