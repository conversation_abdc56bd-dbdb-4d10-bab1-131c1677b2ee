package commands

import (
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	projectionCharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	eventstore "experience/libs/shared/go/event-store"

	"github.com/google/uuid"
)

type Service interface {
	RecordEnergyCostCorrectedEvent(ctx context.Context, aggregateID uuid.UUID, cost int32, submittedBy string) (charges.Aggregate, error)
	RecordSettlementAmountCorrectedEvent(ctx context.Context, aggregateID uuid.UUID, settlementAmount int32, submittedBy string) (charges.Aggregate, error)
}

type PodadminRepository interface {
	UpdateEnergyCostOfCharge(ctx context.Context, chargeID int64, energyCost int32) error
	UpdateSettlementAmountOfCharge(ctx context.Context, chargeID int64, settlementAmount int32) error
}

type Repository interface {
	Atomic(ctx context.Context, cb AtomicCallback) (charges.Aggregate, error)
	Projection() projectionCharges.Repository
	EventStore() eventstore.Store
	AggregateLoader() charges.AggregateLoader
}

type AtomicCallback = func(r Repository) (charges.Aggregate, error)
