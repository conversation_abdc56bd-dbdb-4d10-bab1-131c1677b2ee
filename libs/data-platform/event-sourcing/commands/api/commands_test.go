package api

import (
	"encoding/json"
	contract "experience/libs/data-platform/api/contract/gen/charge_commands"
	"experience/libs/data-platform/api/contract/gen/http/charge_commands/server"
	"experience/libs/data-platform/event-sourcing/commands"
	mockcommands "experience/libs/data-platform/event-sourcing/commands/mock"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	errormapper "experience/libs/shared/go/http"
	"fmt"
	"log"
	"net/http"
	"testing"

	"github.com/ikawaha/goahttpcheck"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

func Test_commandsSvc_CorrectEnergyCost(t *testing.T) {
	tests := []struct {
		name                                        string
		path                                        string
		requestBody                                 *contract.CorrectEnergyCostPayload
		mockRecordEnergyCostCorrectedEventAggregate charges.Aggregate
		mockRecordEnergyCostCorrectedEventError     error
		statusCode                                  int
	}{
		{
			name: "status ok",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost:        16,
				SubmittedBy: ptr.To("John Doe"),
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusOK,
		},
		{
			name: "status ok - no submitted by",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 16,
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusOK,
		},
		{
			name: "status bad request - invalid charge id",
			path: "/commands/charges/invalid/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 16,
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name: "status bad request - charge already expensed",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 16,
			},
			mockRecordEnergyCostCorrectedEventError: charges.ErrChargeAlreadyExpensed,
			statusCode:                              http.StatusBadRequest,
		},
		{
			name: "status conflict - charge has already been updated",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 16,
			},
			mockRecordEnergyCostCorrectedEventError: eventstore.ErrEventIsOutOfDate,
			statusCode:                              http.StatusConflict,
		},
		{
			name: "status not found - charge not found",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 16,
			},
			mockRecordEnergyCostCorrectedEventError: commands.ErrChargeNotFound,
			statusCode:                              http.StatusNotFound,
		},
		{
			name: "status bad request - zero cost",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: 0,
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name: "status bad request - negative cost",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost: -1,
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name:        "status bad request - missing cost",
			path:        "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{
				EnergyCost: ptr.To(int32(42)),
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name: "status internal server error - no EnergyCost on returned aggregate",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost:        16,
				SubmittedBy: ptr.To("John Doe"),
			},
			mockRecordEnergyCostCorrectedEventAggregate: charges.Aggregate{},
			statusCode: http.StatusInternalServerError,
		},
		{
			name: "status internal server error - error received from RecordEnergyCostCorrectedEvent",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-energy-cost",
			requestBody: &contract.CorrectEnergyCostPayload{
				Cost:        16,
				SubmittedBy: ptr.To("John Doe"),
			},
			mockRecordEnergyCostCorrectedEventError: fmt.Errorf("mock_record_energy_error"),
			statusCode:                              http.StatusInternalServerError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			serviceMock := mockcommands.NewMockService(ctrl)
			serviceMock.EXPECT().RecordEnergyCostCorrectedEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.mockRecordEnergyCostCorrectedEventAggregate, tt.mockRecordEnergyCostCorrectedEventError)

			var checker = goahttpcheck.New()
			checker.Mount(
				server.NewCorrectEnergyCostHandler,
				server.MountCorrectEnergyCostHandler,
				contract.NewCorrectEnergyCostEndpoint(NewService(log.Default(), serviceMock, errormapper.NewErrorMapper(ErrorMap))),
			)

			requestJSON, err := json.Marshal(tt.requestBody)
			require.NoError(t, err)

			checker.Test(t, http.MethodPost, tt.path).WithBody(requestJSON).Check().HasStatus(tt.statusCode)
		})
	}
}

func Test_commandsSvc_CorrectSettlementAmount(t *testing.T) {
	tests := []struct {
		name                                              string
		path                                              string
		requestBody                                       *contract.CorrectSettlementAmountPayload
		mockRecordSettlementAmountCorrectedEventAggregate charges.Aggregate
		mockRecordSettlementAmountCorrectedEventError     error
		statusCode                                        int
	}{
		{
			name: "status ok",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
				SubmittedBy:      ptr.To("John Doe"),
			},
			mockRecordSettlementAmountCorrectedEventAggregate: charges.Aggregate{
				SettlementAmount: ptr.To(int32(42)),
			},
			statusCode: http.StatusOK,
		},
		{
			name: "status ok - no submitted by",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
			},
			mockRecordSettlementAmountCorrectedEventAggregate: charges.Aggregate{
				SettlementAmount: ptr.To(int32(42)),
			},
			statusCode: http.StatusOK,
		},
		{
			name: "status bad request - invalid charge id",
			path: "/commands/charges/invalid/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name: "status bad request - zero settlement amount",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 0,
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name: "status bad request - negative settlement amount",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: -1,
			},
			statusCode: http.StatusBadRequest,
		},
		{
			name:        "status bad request - missing settlement amount",
			path:        "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{},
			statusCode:  http.StatusBadRequest,
		},
		{
			name: "status not found - charge not found",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
			},
			mockRecordSettlementAmountCorrectedEventError: commands.ErrChargeNotFound,
			statusCode: http.StatusNotFound,
		},
		{
			name: "status conflict - charge has already been updated",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
			},
			mockRecordSettlementAmountCorrectedEventError: eventstore.ErrEventIsOutOfDate,
			statusCode: http.StatusConflict,
		},
		{
			name: "status internal server error - error received from RecordSettlementAmountCorrectedEvent",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
				SubmittedBy:      ptr.To("John Doe"),
			},
			mockRecordSettlementAmountCorrectedEventError: fmt.Errorf("mock_record_settlement_amount_error"),
			statusCode: http.StatusInternalServerError,
		},
		{
			name: "status internal server error - no settlement amount on returned aggregate",
			path: "/commands/charges/677cbd48-efd1-41f2-bbe5-4f9a20cf8b26/correct-settlement-amount",
			requestBody: &contract.CorrectSettlementAmountPayload{
				SettlementAmount: 16,
				SubmittedBy:      ptr.To("John Doe"),
			},
			mockRecordSettlementAmountCorrectedEventAggregate: charges.Aggregate{},
			statusCode: http.StatusInternalServerError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			serviceMock := mockcommands.NewMockService(ctrl)
			serviceMock.EXPECT().RecordSettlementAmountCorrectedEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.mockRecordSettlementAmountCorrectedEventAggregate, tt.mockRecordSettlementAmountCorrectedEventError)

			var checker = goahttpcheck.New()
			checker.Mount(
				server.NewCorrectSettlementAmountHandler,
				server.MountCorrectSettlementAmountHandler,
				contract.NewCorrectSettlementAmountEndpoint(NewService(log.Default(), serviceMock, errormapper.NewErrorMapper(ErrorMap))),
			)

			requestJSON, err := json.Marshal(tt.requestBody)
			require.NoError(t, err)

			checker.Test(t, http.MethodPost, tt.path).WithBody(requestJSON).Check().HasStatus(tt.statusCode)
		})
	}
}
