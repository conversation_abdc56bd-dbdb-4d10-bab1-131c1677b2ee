package api

import (
	contract "experience/libs/data-platform/api/contract/gen/charge_commands"
	"experience/libs/data-platform/event-sourcing/commands"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"net/http"
)

var ChargeExpensedGoaError = &contract.ChargeExpensed{
	Reason: "charge_expensed",
	Status: http.StatusBadRequest,
}

var ChargeNotFoundGoaError = &contract.ChargeNotFound{
	Reason: "charge_not_found",
	Status: http.StatusNotFound,
}

var DuplicateRequestsGoaError = &contract.EventOutOfDate{
	Reason: "duplicate_requests",
	Status: http.StatusConflict,
}

var ErrorMap = map[error]error{
	charges.ErrChargeAlreadyExpensed: ChargeExpensedGoaError,
	commands.ErrChargeNotFound:       ChargeNotFoundGoaError,
	eventstore.ErrEventIsOutOfDate:   DuplicateRequestsGoaError,
}
