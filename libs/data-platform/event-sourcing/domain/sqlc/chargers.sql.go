// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: chargers.sql

package sqlc

import (
	"context"
	"database/sql"
)

const createAddress = `-- name: CreateAddress :one
INSERT INTO podpoint.pod_addresses (id, business_name, line_1, line_2,
                                    postal_town, postcode, country, description, type_id, tariff_id, deleted_at,
                                    group_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 2, $9, $10, $11)
ON CONFLICT DO NOTHING
RETURNING id, group_id, contact_name, email, telephone, business_name, line_1, line_2, postal_town, postcode, country, description, type_id, tariff_id, cost_per_kwh, created_at, updated_at, deleted_at
`

type CreateAddressParams struct {
	ID           int64         `json:"id"`
	BusinessName string        `json:"business_name"`
	Line1        string        `json:"line_1"`
	Line2        string        `json:"line_2"`
	PostalTown   string        `json:"postal_town"`
	Postcode     string        `json:"postcode"`
	Country      string        `json:"country"`
	Description  string        `json:"description"`
	TariffID     sql.NullInt64 `json:"tariff_id"`
	DeletedAt    sql.NullTime  `json:"deleted_at"`
	GroupID      sql.NullInt64 `json:"group_id"`
}

func (q *Queries) CreateAddress(ctx context.Context, arg CreateAddressParams) (PodpointPodAddress, error) {
	row := q.db.QueryRowContext(ctx, createAddress,
		arg.ID,
		arg.BusinessName,
		arg.Line1,
		arg.Line2,
		arg.PostalTown,
		arg.Postcode,
		arg.Country,
		arg.Description,
		arg.TariffID,
		arg.DeletedAt,
		arg.GroupID,
	)
	var i PodpointPodAddress
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.ContactName,
		&i.Email,
		&i.Telephone,
		&i.BusinessName,
		&i.Line1,
		&i.Line2,
		&i.PostalTown,
		&i.Postcode,
		&i.Country,
		&i.Description,
		&i.TypeID,
		&i.TariffID,
		&i.CostPerKwh,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const createCharge = `-- name: CreateCharge :one
INSERT INTO podpoint.charges (id, location_id, unit_id, door, energy_cost, billing_event_id, starts_at, ends_at,
                              kwh_used, duration,
                              is_closed, group_id, claimed_charge_id, billing_account_id, deleted_at)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
ON CONFLICT DO NOTHING
RETURNING id, location_id, unit_id, job_id, door, energy_cost, tag_id, billing_account_id, billing_event_id, group_id, claimed_charge_id, charge_cycle_id, starts_at, ends_at, start_event_processed_at, end_event_processed_at, kwh_used, duration, is_closed, created_at, updated_at, deleted_at, generation_kwh_used
`

type CreateChargeParams struct {
	ID               int64         `json:"id"`
	LocationID       sql.NullInt64 `json:"location_id"`
	UnitID           int64         `json:"unit_id"`
	Door             int64         `json:"door"`
	EnergyCost       sql.NullInt32 `json:"energy_cost"`
	BillingEventID   sql.NullInt64 `json:"billing_event_id"`
	StartsAt         sql.NullTime  `json:"starts_at"`
	EndsAt           sql.NullTime  `json:"ends_at"`
	KwhUsed          string        `json:"kwh_used"`
	Duration         sql.NullInt64 `json:"duration"`
	IsClosed         int16         `json:"is_closed"`
	GroupID          sql.NullInt64 `json:"group_id"`
	ClaimedChargeID  sql.NullInt64 `json:"claimed_charge_id"`
	BillingAccountID sql.NullInt64 `json:"billing_account_id"`
	DeletedAt        sql.NullTime  `json:"deleted_at"`
}

func (q *Queries) CreateCharge(ctx context.Context, arg CreateChargeParams) (PodpointCharge, error) {
	row := q.db.QueryRowContext(ctx, createCharge,
		arg.ID,
		arg.LocationID,
		arg.UnitID,
		arg.Door,
		arg.EnergyCost,
		arg.BillingEventID,
		arg.StartsAt,
		arg.EndsAt,
		arg.KwhUsed,
		arg.Duration,
		arg.IsClosed,
		arg.GroupID,
		arg.ClaimedChargeID,
		arg.BillingAccountID,
		arg.DeletedAt,
	)
	var i PodpointCharge
	err := row.Scan(
		&i.ID,
		&i.LocationID,
		&i.UnitID,
		&i.JobID,
		&i.Door,
		&i.EnergyCost,
		&i.TagID,
		&i.BillingAccountID,
		&i.BillingEventID,
		&i.GroupID,
		&i.ClaimedChargeID,
		&i.ChargeCycleID,
		&i.StartsAt,
		&i.EndsAt,
		&i.StartEventProcessedAt,
		&i.EndEventProcessedAt,
		&i.KwhUsed,
		&i.Duration,
		&i.IsClosed,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.GenerationKwhUsed,
	)
	return i, err
}

const createCharger = `-- name: CreateCharger :one
INSERT INTO podpoint.pod_units (id, ppid, name, model_id, status_id, relay_weld_flag, deleted_at)
VALUES ($1, $2, $3, $4, 1, 0, $5)
ON CONFLICT DO NOTHING
RETURNING id, ppid, name, model_id, status_id, config_id, ocpp_endpoint_id, installation_id, date_commissioned, last_contact, relay_weld_flag, created_at, updated_at, deleted_at
`

type CreateChargerParams struct {
	ID        int64          `json:"id"`
	Ppid      string         `json:"ppid"`
	Name      sql.NullString `json:"name"`
	ModelID   int64          `json:"model_id"`
	DeletedAt sql.NullTime   `json:"deleted_at"`
}

func (q *Queries) CreateCharger(ctx context.Context, arg CreateChargerParams) (PodpointPodUnit, error) {
	row := q.db.QueryRowContext(ctx, createCharger,
		arg.ID,
		arg.Ppid,
		arg.Name,
		arg.ModelID,
		arg.DeletedAt,
	)
	var i PodpointPodUnit
	err := row.Scan(
		&i.ID,
		&i.Ppid,
		&i.Name,
		&i.ModelID,
		&i.StatusID,
		&i.ConfigID,
		&i.OcppEndpointID,
		&i.InstallationID,
		&i.DateCommissioned,
		&i.LastContact,
		&i.RelayWeldFlag,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getChargerByID = `-- name: GetChargerByID :one
SELECT pu.name, pl.is_home, pl.is_public, pl.timezone, pa.business_name
FROM podpoint.pod_units pu
       JOIN podpoint.pod_locations pl ON pu.id = pl.unit_id
       JOIN podpoint.pod_addresses pa ON pa.id = pl.address_id
WHERE pu.ppid = $1
`

type GetChargerByIDRow struct {
	Name         sql.NullString `json:"name"`
	IsHome       int16          `json:"is_home"`
	IsPublic     int16          `json:"is_public"`
	Timezone     sql.NullString `json:"timezone"`
	BusinessName string         `json:"business_name"`
}

func (q *Queries) GetChargerByID(ctx context.Context, ppid string) (GetChargerByIDRow, error) {
	row := q.db.QueryRowContext(ctx, getChargerByID, ppid)
	var i GetChargerByIDRow
	err := row.Scan(
		&i.Name,
		&i.IsHome,
		&i.IsPublic,
		&i.Timezone,
		&i.BusinessName,
	)
	return i, err
}

const getChargesByChargerID = `-- name: GetChargesByChargerID :many
SELECT c.id
FROM podpoint.charges c
       INNER JOIN podpoint.pod_units pu ON pu.id = c.unit_id
WHERE pu.ppid = $1
  AND c.deleted_at IS NULL
  AND pu.deleted_at IS NULL
`

func (q *Queries) GetChargesByChargerID(ctx context.Context, ppid string) ([]int64, error) {
	rows, err := q.db.QueryContext(ctx, getChargesByChargerID, ppid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getLinkedUsersByID = `-- name: GetLinkedUsersByID :many
SELECT u.auth_id
FROM podpoint.users u
       INNER JOIN podpoint.pod_unit_user puu ON puu.user_id = u.id
       INNER JOIN podpoint.pod_units pu ON pu.id = puu.unit_id
WHERE pu.ppid = $1
  AND u.deleted_at IS NULL
`

func (q *Queries) GetLinkedUsersByID(ctx context.Context, ppid string) ([]string, error) {
	rows, err := q.db.QueryContext(ctx, getLinkedUsersByID, ppid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var auth_id string
		if err := rows.Scan(&auth_id); err != nil {
			return nil, err
		}
		items = append(items, auth_id)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
