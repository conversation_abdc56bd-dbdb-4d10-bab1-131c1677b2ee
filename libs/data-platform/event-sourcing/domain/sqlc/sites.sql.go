// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: sites.sql

package sqlc

import (
	"context"
	"database/sql"
)

const getSiteByLocationID = `-- name: GetSiteByLocationID :one
SELECT pa.id address_id, g.uid group_uuid, g.name group_name
FROM podpoint.pod_locations pl
       INNER JOIN podpoint.pod_addresses pa
                  ON pl.address_id = pa.id
       LEFT JOIN podpoint.groups g
                 ON g.id = pa.group_id
WHERE pl.id = $1
  AND pl.deleted_at IS NULL
  AND pa.deleted_at IS NULL
  AND g.deleted_at IS NULL
`

type GetSiteByLocationIDRow struct {
	AddressID int64          `json:"address_id"`
	GroupUuid sql.NullString `json:"group_uuid"`
	GroupName sql.NullString `json:"group_name"`
}

func (q *Queries) GetSiteByLocationID(ctx context.Context, id int64) (GetSiteByLocationIDRow, error) {
	row := q.db.QueryRowContext(ctx, getSiteByLocationID, id)
	var i GetSiteByLocationIDRow
	err := row.Scan(&i.AddressID, &i.GroupUuid, &i.GroupName)
	return i, err
}
