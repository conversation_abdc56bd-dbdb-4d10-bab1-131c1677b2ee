-- name: GetRevenueProfileTiersByLocationAndUserType :many
SELECT rpt.id,
       rpt.revenue_profile_id,
       rpt.type AS tier_type,
       rpt.user_type,
       rpt.begin_time,
       rpt.begin_day,
       rpt.end_time,
       rpt.end_day,
       rpt.start_second,
       rpt.rate
FROM podpoint.pod_locations pl
       INNER JOIN podpoint.revenue_profile_tiers rpt ON rpt.revenue_profile_id = pl.revenue_profile_id
WHERE rpt.deleted_at IS NULL
  AND pl.id = $1
  AND rpt.user_type = $2;

-- name: GetUserTypeByAuthPKAndLocationID :one
WITH grp AS
       (SELECT rp.group_id AS id
        FROM podpoint.revenue_profiles rp
               INNER JOIN podpoint.pod_locations pl ON pl.revenue_profile_id = rp.id
        WHERE pl.deleted_at IS NULL
          AND rp.deleted_at IS NULL
          AND pl.id = sqlc.arg(location_id))
SELECT COALESCE(
         CASE
           WHEN EXISTS(SELECT id
                       FROM podpoint.members
                       WHERE email = u.email
                         AND deleted_at IS NULL
                         AND group_id = (SELECT id FROM grp)) THEN 'member'
           WHEN EXISTS(SELECT id
                       FROM podpoint.ev_drivers
                       WHERE email = u.email
                         AND deleted_at IS NULL
                         AND group_id = (SELECT id FROM grp)) THEN 'driver'
           WHEN EXISTS(SELECT id
                       FROM podpoint.ev_driver_domains
                       WHERE domain_name = SUBSTRING(u.email from '@(.*)$')
                         AND deleted_at IS NULL
                         AND group_id = (SELECT id FROM grp)) THEN 'driver'
           ELSE 'public'
           END,
         'public'
       )::varchar(7) AS authoriser_level
FROM podpoint.authorisers a
       LEFT JOIN podpoint.users u ON u.auth_id = a.uid
WHERE a.id = sqlc.arg(auth_pk);

-- name: GetNextDurationRevenueProfileTier :one
SELECT id,
       type AS tier_type,
       user_type,
       begin_time,
       begin_day,
       end_time,
       end_day,
       start_second,
       rate
FROM podpoint.revenue_profile_tiers
WHERE type = 'duration'
  AND user_type = $1
  AND begin_day = $2
  AND begin_time = $3
  AND end_day = $4
  AND end_time = $5
  AND start_second > $6
  AND revenue_profile_id = $7
  AND deleted_at IS NULL
ORDER BY start_second
LIMIT 1;

-- name: GetRevenueProfileCurrencyAndTimezoneByLocation :one
SELECT rp.currency, pl.timezone
FROM podpoint.revenue_profiles rp
       INNER JOIN podpoint.pod_locations pl ON pl.revenue_profile_id = rp.id
WHERE rp.deleted_at IS NULL
  AND pl.id = $1;
