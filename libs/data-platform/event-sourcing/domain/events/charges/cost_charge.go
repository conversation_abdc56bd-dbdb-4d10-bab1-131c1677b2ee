package charges

import (
	"context"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/cost"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/sqs"
	"fmt"
	"time"

	"github.com/jinzhu/copier"

	"github.com/biter777/countries"

	"github.com/zlasd/tzloc"

	"log"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

type costChargeServiceImpl struct {
	logger                       *log.Logger
	sqsClient                    sqs.ClientOperations
	aggregateLoader              AggregateLoader
	msgHandler                   sqs.MessageHandler
	energyCostCalculator         energycost.Calculator
	costCalcRepository           cost.Repository
	podadminRepository           PodadminRepository
	aggregateToChargeIDConverter copier.TypeConverter
}

func NewCostChargeService(logger *log.Logger, sqsClient sqs.ClientOperations, aggregateLoader AggregateLoader, msgHandler sqs.MessageH<PERSON>ler, energyCostCalculator energycost.Calculator, costCalcRepository cost.Repository, aggregateToChargeIDConverter copier.TypeConverter) CostChargeService {
	return &costChargeServiceImpl{
		logger:                       logger,
		sqsClient:                    sqsClient,
		aggregateLoader:              aggregateLoader,
		msgHandler:                   msgHandler,
		energyCostCalculator:         energyCostCalculator,
		costCalcRepository:           costCalcRepository,
		aggregateToChargeIDConverter: aggregateToChargeIDConverter,
	}
}

func (s *costChargeServiceImpl) CostCharge(ctx context.Context, aggregateID uuid.UUID) error {
	charge, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("failed to load charge: %s, %w", aggregateID, err)
	}
	completedEvent, err := charge.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("failed to find completed event: %s, %w", aggregateID, err)
	}

	if completedEvent.Historic {
		event := NewCosted(aggregateID, completedEvent.Payload.Energy.EnergyCost, completedEvent.Payload.Energy.EnergyCostCurrency)
		return processDirectWithSQSFallback(ctx, &event, s.msgHandler, s.sqsClient)
	}

	calculatedCost := s.calculateCost(ctx, completedEvent)
	energyCostCurrency, err := s.costCalcRepository.GetEnergyTariffCurrencyByLocationID(ctx, completedEvent.Payload.Location.ID)
	if err != nil {
		energyCostCurrency = countries.CurrencyGBP.Alpha()
	}

	event := NewCosted(aggregateID, &calculatedCost, energyCostCurrency)
	return processDirectWithSQSFallback(ctx, &event, s.msgHandler, s.sqsClient)
}

func (s *costChargeServiceImpl) RecalculateEnergyCost(ctx context.Context, aggregateID uuid.UUID, submittedBy string) error {
	aggregate, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("failed to load charge: %s, %w", aggregateID, err)
	}

	completedEvent, err := aggregate.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("failed to find completed event: %s, %w", aggregateID, err)
	}

	energyCost := s.calculateCost(ctx, completedEvent)
	event := NewEnergyCostCorrected(aggregateID, energyCost, energyCost-ptr.Deref(aggregate.EnergyCost, 0), ptr.To(submittedBy))

	return processDirectWithSQSFallback(ctx, event, s.msgHandler, s.sqsClient)
}

func (s *costChargeServiceImpl) calculateCost(ctx context.Context, completedEvent *Completed) int32 {
	var (
		locationTariffs []energycost.TariffTier
		country         countries.CountryCode
		err             error
	)

	if completedEvent.Payload.Location.ID != 0 {
		locationTariffs, err = s.costCalcRepository.GetEnergyTariffTiersByID(ctx, completedEvent.Payload.Location.ID)
		if err != nil {
			s.logger.Printf("Cost calculation - error retrieving tariff tiers for locationID %d eventID %s : %v", completedEvent.Payload.Location.ID, completedEvent.EventID, err)
		}
		country, err = s.costCalcRepository.GetCountryByID(ctx, completedEvent.Payload.Location.ID)
		if err != nil {
			s.logger.Printf("Cost calculation - couldn't retrieve country for location id %d: %v", completedEvent.Payload.Location.ID, err)
		}
	}

	address := energycost.Address{
		Tariff: energycost.Tariff{
			Tiers:    locationTariffs,
			Timezone: s.getTariffTimezone(),
		},
		Country: country.Alpha2(),
	}
	charge := toCostCalcCharge(&completedEvent.Payload)

	ec, err := numbers.Convert[uint32, int32](s.energyCostCalculator.EnergyCost(&address, charge))
	if err != nil {
		s.logger.Printf("Cost calculation - error converting energy cost: %v", err)
		// todo: reinstate the line below once connectivity fix for PAR-1285 is in place.
		// return 0, err
	}
	return ec
}

func (s *costChargeServiceImpl) getTariffTimezone() *time.Location {
	// todo: reset this with the one provided by tariffs api
	tariffTimezoneFromTariffsAPI := "Europe/London"

	// prepare a default location - this should never fail
	location, _ := time.LoadLocation(tzloc.Europe_London)

	if tzloc.ValidLocation(tariffTimezoneFromTariffsAPI) {
		// once location is established as valid this should never fail either
		location, _ = time.LoadLocation(tariffTimezoneFromTariffsAPI)
	}
	return location
}
