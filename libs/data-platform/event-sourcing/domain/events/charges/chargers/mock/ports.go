// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/domain/events/charges/chargers/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/domain/events/charges/chargers/mock/ports.go -source=libs/data-platform/event-sourcing/domain/events/charges/chargers/ports.go
//
// Package mock_chargers is a generated GoMock package.
package mock_chargers

import (
	context "context"
	chargers "experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	reflect "reflect"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetChargesByID mocks base method.
func (m *MockRepository) GetChargesByID(ctx context.Context, chargerID chargers.StationID) ([]uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChargesByID", ctx, chargerID)
	ret0, _ := ret[0].([]uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChargesByID indicates an expected call of GetChargesByID.
func (mr *MockRepositoryMockRecorder) GetChargesByID(ctx, chargerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChargesByID", reflect.TypeOf((*MockRepository)(nil).GetChargesByID), ctx, chargerID)
}

// GetLinkedChargerByID mocks base method.
func (m *MockRepository) GetLinkedChargerByID(ctx context.Context, chargerID chargers.StationID) (*chargers.Charger, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkedChargerByID", ctx, chargerID)
	ret0, _ := ret[0].(*chargers.Charger)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkedChargerByID indicates an expected call of GetLinkedChargerByID.
func (mr *MockRepositoryMockRecorder) GetLinkedChargerByID(ctx, chargerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkedChargerByID", reflect.TypeOf((*MockRepository)(nil).GetLinkedChargerByID), ctx, chargerID)
}

// GetLinkedUsersByID mocks base method.
func (m *MockRepository) GetLinkedUsersByID(ctx context.Context, chargerID chargers.StationID) ([]uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkedUsersByID", ctx, chargerID)
	ret0, _ := ret[0].([]uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkedUsersByID indicates an expected call of GetLinkedUsersByID.
func (mr *MockRepositoryMockRecorder) GetLinkedUsersByID(ctx, chargerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkedUsersByID", reflect.TypeOf((*MockRepository)(nil).GetLinkedUsersByID), ctx, chargerID)
}
