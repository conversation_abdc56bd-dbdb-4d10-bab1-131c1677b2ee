package charges

import (
	"context"
	chargecommands "experience/libs/data-platform/event-sourcing/domain/commands/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/rewards"
	"experience/libs/shared/go/sqs"
	"fmt"
	"log"
)

type rewardsServiceImpl struct {
	logger        *log.Logger
	msgHandler    sqs.MessageHandler
	sqsClient     sqs.ClientOperations
	rewardsClient rewards.Client
}

func NewAttributeRewardableEnergyService(logger *log.Logger, msgHandler sqs.MessageHandler, sqsClient sqs.ClientOperations, rewardsClient rewards.Client) AttributeRewardableEnergyService {
	return &rewardsServiceImpl{
		logger:        logger,
		msgHandler:    msgHandler,
		sqsClient:     sqsClient,
		rewardsClient: rewardsClient,
	}
}

func (s *rewardsServiceImpl) AttributeRewardableEnergy(ctx context.Context, payload *chargecommands.AttributeRewardableEnergyPayload) error {
	if payload.ChargerID == "" {
		return fmt.Errorf("chargerID is nil on aggregate %s", payload.ChargeID)
	}

	if payload.PluggedInAt.IsZero() {
		return fmt.Errorf("pluggedInAt is nil on aggregate %s", payload.ChargeID)
	}

	if payload.UnpluggedAt.IsZero() {
		return fmt.Errorf("unpluggedAt is nil on aggregate %s", payload.ChargeID)
	}

	statusCode, response, err := s.rewardsClient.ConvertChargeToRewardPoints(ctx, &rewards.GetChargingSessionRewardInfoRequest{
		Ppid:       string(payload.ChargerID),
		Start:      payload.PluggedInAt,
		End:        payload.UnpluggedAt,
		Grid:       float32(payload.GridEnergyTotal),
		Generation: float32(payload.GenerationEnergyTotal),
		Total:      float32(payload.EnergyTotal),
	})
	if err != nil {
		if statusCode >= 500 && statusCode < 600 {
			return fmt.Errorf("server error when converting charge %s to reward points: %v", payload.ChargeID, err)
		}

		// only log non-404
		if statusCode != 404 {
			s.logger.Printf("client error when converting charge %s to reward points: %v", payload.ChargeID, err)
		}
		return nil
	}

	if response.RewardableEnergyKwh <= 0 {
		return nil
	}

	pointsAttributed := NewRewardableEnergyAttributed(
		payload.ChargeID,
		response.RewardableEnergyKwh,
		response.VehicleID,
	)

	return processDirectWithSQSFallback(ctx, &pointsAttributed, s.msgHandler, s.sqsClient)
}
