package billing

type Event struct {
	AccountID           *uint32
	PresentmentAmount   int32
	PresentmentCurrency string
	ExchangeRate        float64
	SettlementAmount    int32
	SettlementCurrency  string
	Description         string
	RefundedAmount      *uint32
}

type Data struct {
	LocationID       *uint32
	BillingAccountID *uint32
	BillingEventID   *uint32
}

func (d *Data) HasAlreadyBeenBilled() bool {
	return d.BillingEventID != nil
}
