// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/domain/events/charges/billing/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/domain/events/charges/billing/mock/ports.go -source=libs/data-platform/event-sourcing/domain/events/charges/billing/ports.go
//
// Package mock_billing is a generated GoMock package.
package mock_billing

import (
	context "context"
	billing "experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// CreateBillingEvent mocks base method.
func (m *MockRepository) CreateBillingEvent(ctx context.Context, presentmentAmount int32, presentmentCurrency string, settlementAmount int32, settlementCurrency, exchangeRate, description string, updateTime time.Time, accountID, refundedAmount *int32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBillingEvent", ctx, presentmentAmount, presentmentCurrency, settlementAmount, settlementCurrency, exchangeRate, description, updateTime, accountID, refundedAmount)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBillingEvent indicates an expected call of CreateBillingEvent.
func (mr *MockRepositoryMockRecorder) CreateBillingEvent(ctx, presentmentAmount, presentmentCurrency, settlementAmount, settlementCurrency, exchangeRate, description, updateTime, accountID, refundedAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBillingEvent", reflect.TypeOf((*MockRepository)(nil).CreateBillingEvent), ctx, presentmentAmount, presentmentCurrency, settlementAmount, settlementCurrency, exchangeRate, description, updateTime, accountID, refundedAmount)
}

// GetBillingDataFromCharge mocks base method.
func (m *MockRepository) GetBillingDataFromCharge(ctx context.Context, chargeID uint32) (billing.Data, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillingDataFromCharge", ctx, chargeID)
	ret0, _ := ret[0].(billing.Data)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillingDataFromCharge indicates an expected call of GetBillingDataFromCharge.
func (mr *MockRepositoryMockRecorder) GetBillingDataFromCharge(ctx, chargeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingDataFromCharge", reflect.TypeOf((*MockRepository)(nil).GetBillingDataFromCharge), ctx, chargeID)
}

// GetBillingEventByID mocks base method.
func (m *MockRepository) GetBillingEventByID(ctx context.Context, billingEventID uint32) (billing.Event, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillingEventByID", ctx, billingEventID)
	ret0, _ := ret[0].(billing.Event)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillingEventByID indicates an expected call of GetBillingEventByID.
func (mr *MockRepositoryMockRecorder) GetBillingEventByID(ctx, billingEventID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingEventByID", reflect.TypeOf((*MockRepository)(nil).GetBillingEventByID), ctx, billingEventID)
}

// SetEventIDAndAccountIDOnCharge mocks base method.
func (m *MockRepository) SetEventIDAndAccountIDOnCharge(ctx context.Context, chargeID, billingEventID, billingAccountID int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEventIDAndAccountIDOnCharge", ctx, chargeID, billingEventID, billingAccountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEventIDAndAccountIDOnCharge indicates an expected call of SetEventIDAndAccountIDOnCharge.
func (mr *MockRepositoryMockRecorder) SetEventIDAndAccountIDOnCharge(ctx, chargeID, billingEventID, billingAccountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEventIDAndAccountIDOnCharge", reflect.TypeOf((*MockRepository)(nil).SetEventIDAndAccountIDOnCharge), ctx, chargeID, billingEventID, billingAccountID)
}

// UpdateBillingAccountBalance mocks base method.
func (m *MockRepository) UpdateBillingAccountBalance(ctx context.Context, billingAccountID, balance int32, updateTime time.Time) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBillingAccountBalance", ctx, billingAccountID, balance, updateTime)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBillingAccountBalance indicates an expected call of UpdateBillingAccountBalance.
func (mr *MockRepositoryMockRecorder) UpdateBillingAccountBalance(ctx, billingAccountID, balance, updateTime any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBillingAccountBalance", reflect.TypeOf((*MockRepository)(nil).UpdateBillingAccountBalance), ctx, billingAccountID, balance, updateTime)
}
