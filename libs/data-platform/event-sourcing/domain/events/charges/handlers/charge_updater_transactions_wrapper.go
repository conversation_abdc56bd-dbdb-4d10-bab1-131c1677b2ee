package handlers

import (
	"context"
	"database/sql"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	chargeprojection "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra"
	eventstore "experience/libs/shared/go/event-store"
	"fmt"

	"github.com/jmoiron/sqlx"
)

type DataStore struct {
	db                      *sqlx.DB
	eventStore              eventstore.Store
	aggregateLoader         chargeevents.AggregateLoader
	chargeProjectionService chargeevents.ChargeProjectionUpdater
}

func NewDataStore(db *sqlx.DB) *DataStore {
	eventStore := eventstore.NewPGEventStore(db)
	return &DataStore{
		db:                      db,
		eventStore:              eventStore,
		aggregateLoader:         chargeevents.NewAggregateLoader(eventStore),
		chargeProjectionService: chargeprojection.NewService(infra.NewRepository(db)),
	}
}

// withTx creates new repository instances with *sql.Tx to
// commit and rollback the operations based on the return of the atomic callback.
func (ds DataStore) withTx(tx *sql.Tx) *DataStore {
	newDataStore := NewDataStore(ds.db)
	newDataStore.chargeProjectionService = chargeprojection.NewService(infra.NewRepository(tx))
	newDataStore.eventStore = eventstore.NewPGEventStore(tx)
	newDataStore.aggregateLoader = chargeevents.NewAggregateLoader(newDataStore.eventStore)
	return newDataStore
}

// Atomic rolls back the operations if the callback returns an error
// The transaction only affects the writer instance.
func (ds DataStore) Atomic(ctx context.Context, cb chargeevents.AtomicChargeUpdaterCallback) (err error) {
	tx, err := ds.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(); rbErr != nil {
				err = fmt.Errorf("tx err: %v, rb err: %v", err, rbErr)
			}
		} else {
			err = tx.Commit()
		}
	}()

	dataStoreTx := ds.withTx(tx)
	return cb(*dataStoreTx)
}

func (ds DataStore) EventStore() eventstore.Store {
	return ds.eventStore
}

func (ds DataStore) AggregateLoader() chargeevents.AggregateLoader {
	return ds.aggregateLoader
}

func (ds DataStore) ChargeProjectionService() chargeevents.ChargeProjectionUpdater {
	return ds.chargeProjectionService
}
