package handlers

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	sitestatsmonthly "experience/libs/data-platform/event-sourcing/projection/site-stats-monthly"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	"fmt"
	"time"
)

type siteStatProjectionUpdater struct {
	aggregateLoader               charges.AggregateLoader
	siteStatsProjectionRepository sitestatsmonthly.Repository
}

func NewSiteStatProjectionUpdater(aggregateLoader charges.AggregateLoader, siteStatsProjectionRepository sitestatsmonthly.Repository) handlers.AsyncEventHandler {
	return siteStatProjectionUpdater{aggregateLoader, siteStatsProjectionRepository}
}

func (u siteStatProjectionUpdater) Execute(ctx context.Context, tx *sql.Tx, event eventstore.Event, currentTransactionID uint64) error {
	switch event.(type) {
	case *charges.Completed, *charges.Claimed, *charges.Costed, *charges.Billed, *charges.EnergyCostCorrected, *charges.SettlementAmountCorrected:
		return u.handle(ctx, tx, event, currentTransactionID)
	default:
		return nil
	}
}

func (u siteStatProjectionUpdater) handle(ctx context.Context, tx *sql.Tx, event eventstore.Event, currentTransactionID uint64) error {
	// Get aggregate state at the time of the current transaction id
	chargeAggregate, err := u.aggregateLoader.LoadChargeTo(ctx, event.GetAggregateID(), currentTransactionID)
	if err != nil {
		return fmt.Errorf("loading aggregate %s up to transactionID %d: %w", event.GetAggregateID(), currentTransactionID, err)
	}

	// Check that the charge has been completed, claimed, costed and billed
	if !hasRequiredEvents(chargeAggregate.Events) {
		return nil
	}

	if chargeAggregate.SiteID == nil || chargeAggregate.UnpluggedAt == nil {
		return nil
	}

	siteProjection, err := u.getInitialProjection(ctx, tx, chargeAggregate)
	if err != nil {
		return fmt.Errorf("loading current site stats projection for aggregate %s: %w", chargeAggregate.AggregateID, err)
	}
	if !isUpdateEvent(event) && hasBeenProcessedBefore(siteProjection, event) {
		return nil
	}

	// Update the projection values
	updatedProjection, _ := siteProjection.ApplyCharge(chargeAggregate, event)

	// Upsert the db projection with new values
	err = u.siteStatsProjectionRepository.Save(
		ctx,
		tx,
		updatedProjection,
	)
	if err != nil {
		return fmt.Errorf("saving latest site statistic projection for %s: %w", event.GetAggregateID(), err)
	}
	return nil
}

func (u siteStatProjectionUpdater) getInitialProjection(ctx context.Context, tx *sql.Tx, chargeAggregate *charges.Aggregate) (*sitestatsmonthly.Projection, error) {
	// Load any existing statistic projection for this site/month
	siteProjection, err := u.siteStatsProjectionRepository.RetrieveSingleForUpdate(ctx, tx, *chargeAggregate.SiteID, *chargeAggregate.UnpluggedAt)
	if err != nil {
		if errors.Is(err, sitestatsmonthly.ErrMonthlySiteStatisticsNotFound) {
			// Start a new one if it doesn't exist yet
			siteProjection = &sitestatsmonthly.Projection{
				SiteID: *chargeAggregate.SiteID,
				Month:  time.Date(chargeAggregate.UnpluggedAt.Year(), chargeAggregate.UnpluggedAt.Month(), 1, 0, 0, 0, 0, time.UTC),
			}
		} else {
			return nil, err
		}
	}
	return siteProjection, nil
}

func hasRequiredEvents(events []eventstore.Event) bool {
	var hasCompleted, hasClaimed, hasCosted, hasBilled bool

	for _, event := range events {
		if _, ok := event.(*charges.Completed); ok {
			hasCompleted = true
		}
		if _, ok := event.(*charges.Claimed); ok {
			hasClaimed = true
		}
		if _, ok := event.(*charges.Costed); ok {
			hasCosted = true
		}
		if _, ok := event.(*charges.Billed); ok {
			hasBilled = true
		}
	}

	return hasCompleted && hasCosted && hasClaimed && hasBilled
}

func hasBeenProcessedBefore(siteProjection *sitestatsmonthly.Projection, event eventstore.Event) bool {
	// Charge has already been included in site statistics
	return siteProjection.ChargeIDs.Contains(event.GetAggregateID())
}

func isUpdateEvent(event eventstore.Event) bool {
	switch event.(type) {
	case *charges.EnergyCostCorrected, *charges.SettlementAmountCorrected:
		return true
	default:
		return false
	}
}
