package handlers

import (
	"context"
	"encoding/json"
	"errors"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	"fmt"
)

type chargeProjectionsUpdater struct {
	dataStore chargeevents.ChargeUpdaterScope
}

func NewChargeProjectionsUpdater(dataStore chargeevents.ChargeUpdaterScope) handlers.SyncEventHandler {
	return chargeProjectionsUpdater{
		dataStore: dataStore,
	}
}

func (u chargeProjectionsUpdater) Execute(ctx context.Context, event eventstore.Event, eventBody json.RawMessage) error {
	aggregateID := event.GetAggregateID()

	callback := func(s chargeevents.ChargeUpdaterScope) error {
		version, err := s.EventStore().GetNextAggregateVersion(ctx, aggregateID)
		if err != nil {
			return fmt.Errorf("error getting next aggregate version: %w", err)
		}

		chargeAggregate, err := s.AggregateLoader().LoadCharge(ctx, aggregateID)
		if err != nil {
			return fmt.Errorf("error loading charge aggregate %s: %w", aggregateID, err)
		}

		if applyErr := chargeAggregate.ApplyEvent(event); applyErr != nil {
			return applyErr
		}

		if err = s.EventStore().Save(ctx, aggregateID, version, eventBody); err != nil {
			return fmt.Errorf("error storing event for aggregate %s and version %d: %w", aggregateID, version, err)
		}

		if err = s.ChargeProjectionService().SaveCharge(ctx, chargeAggregate); err != nil {
			return fmt.Errorf("error saving charge projection: %w", err)
		}

		return nil
	}

	if err := u.dataStore.Atomic(ctx, callback); err != nil {
		return fmt.Errorf("error executing charge projection handler: %w", err)
	}

	return nil
}

func (u chargeProjectionsUpdater) ErrorAllowed(err error) bool {
	return err == nil || errors.Is(err, chargeevents.ErrChargeAlreadyBilled) || errors.Is(err, chargeevents.ErrChargeHasRewardableEnergyAttributed)
}
