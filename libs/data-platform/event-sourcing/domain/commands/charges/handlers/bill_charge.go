package handlers

import (
	"context"
	chargecommands "experience/libs/data-platform/event-sourcing/domain/commands/charges"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	eventhandler "experience/libs/shared/go/event-store/handlers"
	"fmt"
)

type billChargeCommand struct {
	chargeEventsSvc chargeevents.BillChargeService
}

func NewBillChargeCommandHandler(service chargeevents.BillChargeService) eventhandler.CommandHandler {
	return billChargeCommand{
		chargeEventsSvc: service,
	}
}

func (b billChargeCommand) Execute(ctx context.Context, command eventstore.Command) error {
	if billChargeCommand, ok := command.(*chargecommands.Bill); ok {
		err := b.chargeEventsSvc.BillCharge(ctx, billChargeCommand.Payload.ChargeID)
		if err != nil {
			return fmt.Errorf("bill charge: %w", err)
		}
		return err
	}
	return nil
}
