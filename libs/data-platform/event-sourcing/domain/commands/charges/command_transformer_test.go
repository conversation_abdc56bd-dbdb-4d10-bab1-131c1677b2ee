package charges

import (
	"encoding/json"
	"errors"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	eventstore "experience/libs/shared/go/event-store"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
)

func TestCommandTransformer_Transform(t *testing.T) {
	tests := []struct {
		name        string
		input       messageInput
		expectedCmd eventstore.Command
		expectedErr error
	}{
		{
			name: "successfully transforms into Claim Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.Claim",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedCmd: &Claim{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeClaim,
				},
				Payload: ClaimPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
		},
		{
			name: "successfully transforms into Confirm Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.Confirm",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedCmd: &Confirm{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeConfirm,
				},
				Payload: ConfirmPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
		},
		{
			name: "successfully transforms into Cost Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.Cost",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedCmd: &Cost{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeCost,
				},
				Payload: CostPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
		},
		{
			name: "successfully transforms into RecalculateEnergyCost Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2024, 10, 7, 11, 22, 1, 0, time.UTC),
				Type:     "Charge.RecalculateEnergyCost",
				Payload: RecalculateEnergyCostPayload{
					ChargeUID:   uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330"),
					SubmittedBy: "sneaky Lee",
				},
			},
			expectedCmd: &RecalculateEnergyCost{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2024, 10, 7, 11, 22, 1, 0, time.UTC),
					Type:     TypeRecalculateEnergyCost,
				},
				Payload: RecalculateEnergyCostPayload{
					ChargeUID:   uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330"),
					SubmittedBy: "sneaky Lee",
				},
			},
		},
		{
			name: "successfully transforms into AddUser Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.AddUser",
				Payload:  AddUserPayload{UserID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330"), Ppid: chargers.StationID("Test")},
			},
			expectedCmd: &AddUser{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeAddUser,
				},
				Payload: AddUserPayload{UserID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330"), Ppid: chargers.StationID("Test")},
			},
		},
		{
			name: "successfully transforms into Bill Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.Bill",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedCmd: &Bill{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeBill,
				},
				Payload: BillPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
		},
		{
			name: "returns err for unexpected 'delete' command",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.Delete",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedErr: errors.New(`unexpected command type received: {"id":"b42bf2c3-236a-496f-96b5-d3b1f97fd940","issuedAt":"2023-06-14T09:28:16Z","type":"Charge.Delete","payload":{"chargeID":"db2cc665-d271-4d9a-8506-1bfc90fe2330"}}`),
		},
		{
			name: "successfully transforms into AttributeRewardableEnergy Command struct",
			input: messageInput{
				ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
				IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
				Type:     "Charge.AttributeRewardableEnergy",
				Payload:  chargeInputPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
			expectedCmd: &AttributeRewardableEnergy{
				BaseCommand: BaseCommand{
					ID:       uuid.MustParse("b42bf2c3-236a-496f-96b5-d3b1f97fd940"),
					IssuedAt: time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC),
					Type:     TypeAttributeRewardableEnergy,
				},
				Payload: AttributeRewardableEnergyPayload{ChargeID: uuid.MustParse("db2cc665-d271-4d9a-8506-1bfc90fe2330")},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			transformer := CommandTransformer{}

			bodyInput, err := json.Marshal(tt.input)
			require.NoError(t, err)

			got, gotErr := transformer.Transform(ptr.To(string(bodyInput)))

			require.Equal(t, tt.expectedCmd, got)
			require.Equal(t, tt.expectedErr, gotErr)
		})
	}
}

func TestCommandTransformer_TransformTwo(t *testing.T) {
	transformer := CommandTransformer{}
	testString := `{
  "id" : "6730a55a-553e-4a81-8c47-da890f19fc5b",
  "issuedAt" : "2024-10-10T00:55:33.910103+01:00",
  "type" : "Charge.RecalculateEnergyCost",
  "Payload" : {
    "chargeID" : "570b2ccf-824d-4076-9da5-000032b674d7",
    "submittedBy" : "TestRecalculateChargeEnergyCost"
  }
}`
	cmd, err := transformer.Transform(ptr.To(testString))
	require.NoError(t, err)
	require.NotNil(t, cmd)
}

type messageInput struct {
	ID       uuid.UUID `json:"id"`
	IssuedAt time.Time `json:"issuedAt"`
	Type     string    `json:"type"`
	Payload  any       `json:"payload"`
}

type chargeInputPayload struct {
	ChargeID uuid.UUID `json:"chargeID"`
}
