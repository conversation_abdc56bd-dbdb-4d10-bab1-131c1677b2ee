package charges

import (
	"context"
	errorfmt "experience/libs/shared/go/error-fmt"
	"experience/libs/shared/go/event-store/handlers"
	"experience/libs/shared/go/sqs"
)

type CommandManager struct {
	transformer     CommandTransformer
	commandHandlers []handlers.CommandHandler
}

func NewCommandManager(transformer CommandTransformer, commandHandlers []handlers.CommandHandler) *CommandManager {
	return &CommandManager{
		transformer:     transformer,
		commandHandlers: commandHandlers,
	}
}

func (e *CommandManager) Process(ctx context.Context, message sqs.Message) error {
	command, err := e.transformer.Transform(message.Body)
	if err != nil {
		return err
	}

	var errWrapper errorfmt.ErrorWrapper

	for _, handler := range e.commandHandlers {
		err = handler.Execute(ctx, command)
		if err != nil {
			errWrapper.AddError(err)
		}
	}
	return errWrapper.SetPrefix("command manager: ").Wrap()
}
