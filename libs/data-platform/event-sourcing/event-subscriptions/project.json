{"name": "data-platform-event-sourcing-event-subscriptions", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/data-platform/event-sourcing/event-subscriptions", "implicitDependencies": ["shared-go-test", "data-platform-event-sourcing-walletbilling", "data-platform-event-sourcing-billingauditor"], "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"commands": ["mockgen -destination libs/data-platform/event-sourcing/event-subscriptions/mock/ports.go -source=libs/data-platform/event-sourcing/event-subscriptions/ports.go"], "parallel": false}}, "test": {"executor": "@nx-go/nx-go:test", "options": {"race": true}}}, "tags": ["data-platform"]}