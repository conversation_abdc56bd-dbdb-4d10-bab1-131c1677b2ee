package infra_test

import (
	"encoding/json"
	publishhistoricevents "experience/libs/data-platform/tasks"
	"experience/libs/data-platform/tasks/publish-historic-confirmed-events/infra"
	"experience/libs/data-platform/tasks/publish-historic-confirmed-events/sqlc"
	testhelpers "experience/libs/data-platform/tasks/test-helpers"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestToConfirmedHistoricEvents(t *testing.T) {
	var from = time.Now().Add(-24 * time.Hour)
	var to = time.Now()
	event1, _ := json.Marshal(testhelpers.Event(from, to))
	event2, _ := json.Marshal(testhelpers.Event(from, to))

	tests := []struct {
		name  string
		input []sqlc.RetrieveConfirmedHistoricEventsInDateRangeRow
		want  []publishhistoricevents.HistoricEvent
	}{
		{
			name: "rows mapped to historic events",
			input: []sqlc.RetrieveConfirmedHistoricEventsInDateRangeRow{
				{
					ID:   1,
					Data: event1,
				},
				{
					ID:   2,
					Data: event2,
				},
			},
			want: []publishhistoricevents.HistoricEvent{
				{
					ID:          1,
					Data:        string(event1),
					ProcessedAt: nil,
				},
				{
					ID:          2,
					Data:        string(event2),
					ProcessedAt: nil,
				},
			},
		},
		{
			name:  "empty row slice gives empty slice of historic events",
			input: []sqlc.RetrieveConfirmedHistoricEventsInDateRangeRow{},
			want:  []publishhistoricevents.HistoricEvent{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := infra.ToEvents(tt.input)

			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}
