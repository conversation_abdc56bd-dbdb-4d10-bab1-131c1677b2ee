-- name: InsertConfirmedHistoricEvent :one
INSERT INTO event_store.claimed_historic_events(data, processed_at, charge_ends_at) VALUES (sqlc.arg(data), sqlc.arg(processed_at), sqlc.arg(charge_ends_at)::timestamp)
  RETURNING id;

-- name: RetrieveConfirmedHistoricEventByID :one
SELECT *
FROM event_store.claimed_historic_events
WHERE id = sqlc.arg(id);

-- name: RetrieveConfirmedHistoricEventsInDateRange :many
SELECT id, data
FROM event_store.claimed_historic_events
WHERE processed_at is null
AND charge_ends_at between sqlc.arg(from_date)::timestamp and sqlc.arg(to_date)::timestamp
ORDER BY charge_ends_at DESC
LIMIT sqlc.arg(chunk_size);

-- name: MarkConfirmedHistoricEventAsProcessed :exec
UPDATE event_store.claimed_historic_events
SET processed_at = sqlc.arg(processed_at)::timestamp
WHERE id = $1;

-- name: MarkConfirmedHistoricEventsAsProcessed :exec
UPDATE event_store.claimed_historic_events
SET processed_at = sqlc.arg(processed_at)::timestamp
WHERE ID = ANY (@ids::bigint[]);
