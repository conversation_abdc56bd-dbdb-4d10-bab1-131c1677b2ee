package tasks_test

import (
	"context"
	"encoding/json"
	"errors"
	"experience/libs/data-platform/tasks"
	mockSqs "experience/libs/shared/go/sqs/test/mock"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

const eventJSON = "{\"Event\": \"Charge.Completed\"}"

func TestPublishSingle(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessage(gomock.Any(), eventJSON).Return(nil, nil).Times(1)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 10)
	err := publisher.PublishSingle(context.Background(), json.RawMessage(eventJSON))
	require.NoError(t, err)
}

func TestPublishSingle_SQSErrorReturnedToCaller(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessage(gomock.Any(), eventJSON).Return(nil, errors.New("send failed")).Times(1)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 10)
	err := publisher.PublishSingle(context.Background(), json.RawMessage(eventJSON))
	require.Error(t, err)
}

func TestPublishSingle_ExceedingMaxQueueDepthDoesSend(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessage(gomock.Any(), eventJSON).Return(nil, nil).Times(1)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 3)
	err := publisher.PublishSingle(context.Background(), json.RawMessage(eventJSON))
	require.NoError(t, err)
}

func TestPublishBatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessageBatch(gomock.Any(), []string{eventJSON}).Return(nil).Times(1)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 10)
	err := publisher.PublishBatch(context.Background(), []string{eventJSON})
	require.NoError(t, err)
}

func TestPublishBatch_SQSErrorReturnedToCaller(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessageBatch(gomock.Any(), []string{eventJSON}).Return(errors.New("send failed")).Times(1)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 10)
	err := publisher.PublishBatch(context.Background(), []string{eventJSON})
	require.Error(t, err)
}

func TestPublishBatch_ExceedingMaxQueueDepthDoesNotSend(t *testing.T) {
	ctrl := gomock.NewController(t)
	clientMock := mockSqs.NewMockClientOperations(ctrl)

	clientMock.EXPECT().SendMessageBatch(gomock.Any(), []string{eventJSON}).Return(nil).Times(0)
	clientMock.EXPECT().GetQueueDepth(gomock.Any()).Return(int64(5), nil)

	publisher := tasks.NewEventPublisherSqs(clientMock, 4)
	err := publisher.PublishBatch(context.Background(), []string{eventJSON})
	require.NoError(t, err)
}
