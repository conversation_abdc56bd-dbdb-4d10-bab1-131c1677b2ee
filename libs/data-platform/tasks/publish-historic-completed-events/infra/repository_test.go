package infra_test

import (
	"database/sql"
	"encoding/json"
	"experience/libs/data-platform/tasks"
	"experience/libs/data-platform/tasks/publish-historic-completed-events/infra"
	"experience/libs/data-platform/tasks/publish-historic-completed-events/sqlc"
	testhelpers "experience/libs/data-platform/tasks/test-helpers"
	"experience/libs/shared/go/db/postgres/test"
	"log"

	"github.com/google/uuid"

	"github.com/stretchr/testify/require"

	"experience/libs/data-platform/test/setup"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
)

const chunkSize = 2

type PublishHistoricEventsRepositoryTestSuite struct {
	suite.Suite
	testDB    *test.Database
	dbh       *sql.DB
	queries   *sqlc.Queries
	underTest tasks.Repository
}

func TestPublishHistoricEventsRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping PublishHistoricEventsRepository integration test suite")
	}
	suite.Run(t, new(PublishHistoricEventsRepositoryTestSuite))
}

func (p *PublishHistoricEventsRepositoryTestSuite) SetupSuite() {
	db := setup.NewPostgresTestDB(p.T(), "file://../../../golang-migrate/migration")
	p.testDB = db.TestDB
	p.dbh = db.DBHandle
	p.queries = sqlc.New(db.DBHandle)
	p.underTest = infra.NewRepository(db.ReadWriteDB)
}

func (p *PublishHistoricEventsRepositoryTestSuite) TearDownSuite() {
	err := p.testDB.Container.Terminate(p.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (p *PublishHistoricEventsRepositoryTestSuite) AfterTest(_, _ string) {
	prepare, _ := p.dbh.Prepare("TRUNCATE TABLE event_store.historic_events")
	_, _ = prepare.Exec()
}

func (p *PublishHistoricEventsRepositoryTestSuite) TestRetrieveHistoricEvents() {
	t := p.T()
	ctx := p.T().Context()
	now := time.Now()

	// events included in the date range and not processed
	eventInclRangeNotProc := testhelpers.Event(now.Add(-16*time.Hour), now.Add(-15*time.Hour))
	eventInclRangeNotProcJSON, _ := json.Marshal(eventInclRangeNotProc)
	historicEventInclRangeNotProcID := createHistoricChargeCompletedEvent(t, p.queries, &eventInclRangeNotProc, false)

	event2InclRangeNotProc := testhelpers.Event(now.Add(-10*time.Hour), now.Add(-5*time.Hour))
	event2InclRangeNotProcJSON, _ := json.Marshal(event2InclRangeNotProc)
	historicEvent2InclRangeNotProcID := createHistoricChargeCompletedEvent(t, p.queries, &event2InclRangeNotProc, false)

	// event included in the date range and not processed, but outside the chunk
	eventInclRangeNotProcOutsideChunk := testhelpers.Event(now.Add(-20*time.Hour), now.Add(-18*time.Hour))
	_ = createHistoricChargeCompletedEvent(t, p.queries, &eventInclRangeNotProcOutsideChunk, false)

	// event not included in the date range and not processed
	eventExclRangeNotProc := testhelpers.Event(now.Add(-72*time.Hour), now.Add(-80*time.Hour))
	_ = createHistoricChargeCompletedEvent(t, p.queries, &eventExclRangeNotProc, false)

	// event included in the date range and processed
	eventInclRangeProc := testhelpers.Event(now.Add(-15*time.Hour), now.Add(-10*time.Hour))
	_ = createHistoricChargeCompletedEvent(t, p.queries, &eventInclRangeProc, true)

	type args struct {
		name             string
		fromDate         time.Time
		toDate           time.Time
		want             []tasks.HistoricEvent
		wantAggregateIDs []uuid.UUID
	}

	tests := []args{
		{
			name:     "returns non-processed historic events from given time range - 1st chunk",
			fromDate: now.Add(-24 * time.Hour),
			toDate:   now.Add(24 * time.Hour),
			want: []tasks.HistoricEvent{
				{
					ID:          historicEvent2InclRangeNotProcID,
					Data:        string(event2InclRangeNotProcJSON),
					ProcessedAt: nil,
				},
				{
					ID:          historicEventInclRangeNotProcID,
					Data:        string(eventInclRangeNotProcJSON),
					ProcessedAt: nil,
				},
			},
			wantAggregateIDs: []uuid.UUID{event2InclRangeNotProc.AggregateID, eventInclRangeNotProc.AggregateID},
		},
		{
			name:     "returns an empty slice if no events matching criteria have been found",
			fromDate: now.Add(-48 * time.Hour),
			toDate:   now.Add(-25 * time.Hour),
			want:     []tasks.HistoricEvent{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := p.underTest.RetrieveHistoricEvents(ctx, tt.fromDate, tt.toDate, chunkSize)

			require.NoError(t, err)
			require.Len(t, got, len(tt.want))

			for i, wantEvent := range tt.want {
				var gotData testhelpers.TestEvent
				err = json.Unmarshal([]byte(wantEvent.Data), &gotData)

				require.NoError(t, err)
				require.Equal(t, wantEvent.ID, (got)[i].ID)
				require.Equal(t, tt.wantAggregateIDs[i], gotData.AggregateID)
			}
		})
	}
}

func (p *PublishHistoricEventsRepositoryTestSuite) TestMarkHistoricEventAsProcessed() {
	t := p.T()
	ctx := p.T().Context()
	var now = time.Now()

	event := testhelpers.Event(now.Add(-2*time.Hour), now.Add(-1*time.Hour))
	historicEventID := createHistoricChargeCompletedEvent(t, p.queries, &event, false)

	historicEvent, err := p.queries.RetrieveHistoricEventByID(ctx, historicEventID)
	require.NoError(t, err)
	require.False(t, historicEvent.ProcessedAt.Valid)

	tx, err := p.underTest.StartTransaction()
	require.NoError(t, err)
	err = p.underTest.MarkHistoricEventAsProcessed(ctx, tx, historicEventID)
	require.NoError(t, err)
	err = tx.Commit()
	require.NoError(t, err)

	historicEvent, err = p.queries.RetrieveHistoricEventByID(ctx, historicEventID)
	require.NoError(t, err)
	require.True(t, historicEvent.ProcessedAt.Valid)
}

func (p *PublishHistoricEventsRepositoryTestSuite) TestMarkHistoricEventsAsProcessed() {
	t := p.T()
	ctx := p.T().Context()
	var now = time.Now()

	event := testhelpers.Event(now.Add(-2*time.Hour), now.Add(-1*time.Hour))

	historicEventID1 := createHistoricChargeCompletedEvent(t, p.queries, &event, false)
	historicEvent1, err := p.queries.RetrieveHistoricEventByID(ctx, historicEventID1)
	require.NoError(t, err)
	require.False(t, historicEvent1.ProcessedAt.Valid)

	historicEventID2 := createHistoricChargeCompletedEvent(t, p.queries, &event, false)
	historicEvent2, err := p.queries.RetrieveHistoricEventByID(ctx, historicEventID2)
	require.NoError(t, err)
	require.False(t, historicEvent2.ProcessedAt.Valid)

	tx, err := p.underTest.StartTransaction()
	require.NoError(t, err)
	err = p.underTest.MarkHistoricEventsAsProcessed(ctx, tx, []int64{historicEventID1, historicEventID2})
	require.NoError(t, err)
	err = tx.Commit()
	require.NoError(t, err)

	historicEvent1, err = p.queries.RetrieveHistoricEventByID(ctx, historicEventID1)
	require.NoError(t, err)
	historicEvent2, err = p.queries.RetrieveHistoricEventByID(ctx, historicEventID2)
	require.NoError(t, err)
	require.True(t, historicEvent1.ProcessedAt.Valid)
	require.True(t, historicEvent2.ProcessedAt.Valid)
}

func createHistoricChargeCompletedEvent(t *testing.T, queries *sqlc.Queries, event *testhelpers.TestEvent, isProcessed bool) int64 {
	t.Helper()

	eventJSON, _ := json.Marshal(event)
	params := sqlc.CreateHistoricEventParams{Data: eventJSON}

	if isProcessed {
		params.ProcessedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}

	id, err := queries.CreateHistoricEvent(t.Context(), params)
	require.NoError(t, err)

	return id
}
