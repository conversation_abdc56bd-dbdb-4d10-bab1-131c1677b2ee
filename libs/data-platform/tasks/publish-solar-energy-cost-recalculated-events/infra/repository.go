package infra

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/tasks"
	"experience/libs/data-platform/tasks/publish-solar-energy-cost-recalculated-events/sqlc"
	"experience/libs/shared/go/db/postgres"
	"time"
)

type repository struct {
	rwDB *postgres.ReadWriteDB
}

func NewRepository(rwDB *postgres.ReadWriteDB) tasks.Repository {
	return &repository{
		rwDB: rwDB,
	}
}

func (r *repository) StartTransaction() (*sql.Tx, error) {
	var err error

	tx, err := r.rwDB.WriteDB.Begin()
	if err != nil {
		return nil, err
	}

	return tx, nil
}

func (r *repository) RetrieveHistoricEvents(ctx context.Context, from, to time.Time, chunkSize int) ([]tasks.HistoricEvent, error) {
	queries := sqlc.New(r.rwDB.WriteDB)
	rows, err := queries.RetrieveSolarEnergyCostRecalculatedEventsInDateRange(ctx, sqlc.RetrieveSolarEnergyCostRecalculatedEventsInDateRangeParams{
		FromDate:  from,
		ToDate:    to,
		ChunkSize: int32(chunkSize),
	})
	if err != nil {
		return nil, err
	}

	return ToEvents(rows)
}

func (r *repository) MarkHistoricEventAsProcessed(ctx context.Context, tx *sql.Tx, eventID int64) error {
	queriesTx := sqlc.New(r.rwDB.WriteDB).WithTx(tx)
	err := queriesTx.MarkSolarEnergyCostRecalculatedEventAsProcessed(ctx, sqlc.MarkSolarEnergyCostRecalculatedEventAsProcessedParams{
		ID:          eventID,
		ProcessedAt: time.Time{},
	})
	if err != nil {
		return err
	}

	return nil
}

func (r *repository) MarkHistoricEventsAsProcessed(ctx context.Context, tx *sql.Tx, eventIDs []int64) error {
	queriesTx := sqlc.New(r.rwDB.WriteDB).WithTx(tx)
	err := queriesTx.MarkSolarEnergyCostRecalculatedEventsAsProcessed(ctx, sqlc.MarkSolarEnergyCostRecalculatedEventsAsProcessedParams{
		ProcessedAt: time.Time{},
		Ids:         eventIDs,
	})
	if err != nil {
		return err
	}

	return nil
}
