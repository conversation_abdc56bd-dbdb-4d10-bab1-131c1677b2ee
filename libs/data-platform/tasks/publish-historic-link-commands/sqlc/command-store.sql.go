// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: command-store.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/lib/pq"
)

const insertLinkHistoricCommand = `-- name: InsertLinkHistoricCommand :one
INSERT INTO event_store.linked_historic_commands(data, processed_at, linked_at) VALUES ($1, $2, $3::timestamp)
RETURNING id
`

type InsertLinkHistoricCommandParams struct {
	Data        json.RawMessage `json:"data"`
	ProcessedAt sql.NullTime    `json:"processed_at"`
	LinkedAt    time.Time       `json:"linked_at"`
}

func (q *Queries) InsertLinkHistoricCommand(ctx context.Context, arg InsertLinkHistoricCommandParams) (int64, error) {
	row := q.db.QueryRowContext(ctx, insertLinkHistoricCommand, arg.Data, arg.ProcessedAt, arg.LinkedAt)
	var id int64
	err := row.Scan(&id)
	return id, err
}

const markLinkHistoricCommandAsProcessed = `-- name: MarkLinkHistoricCommandAsProcessed :exec
UPDATE event_store.linked_historic_commands
SET processed_at = $2::timestamp
WHERE id = $1
`

type MarkLinkHistoricCommandAsProcessedParams struct {
	ID          int64     `json:"id"`
	ProcessedAt time.Time `json:"processed_at"`
}

func (q *Queries) MarkLinkHistoricCommandAsProcessed(ctx context.Context, arg MarkLinkHistoricCommandAsProcessedParams) error {
	_, err := q.db.ExecContext(ctx, markLinkHistoricCommandAsProcessed, arg.ID, arg.ProcessedAt)
	return err
}

const markLinkHistoricCommandsAsProcessed = `-- name: MarkLinkHistoricCommandsAsProcessed :exec
UPDATE event_store.linked_historic_commands
SET processed_at = $1::timestamp
WHERE ID = ANY ($2::bigint[])
`

type MarkLinkHistoricCommandsAsProcessedParams struct {
	ProcessedAt time.Time `json:"processed_at"`
	Ids         []int64   `json:"ids"`
}

func (q *Queries) MarkLinkHistoricCommandsAsProcessed(ctx context.Context, arg MarkLinkHistoricCommandsAsProcessedParams) error {
	_, err := q.db.ExecContext(ctx, markLinkHistoricCommandsAsProcessed, arg.ProcessedAt, pq.Array(arg.Ids))
	return err
}

const retrieveLinkHistoricCommandByID = `-- name: RetrieveLinkHistoricCommandByID :one
SELECT id, data, processed_at, linked_at
FROM event_store.linked_historic_commands
WHERE id = $1
`

func (q *Queries) RetrieveLinkHistoricCommandByID(ctx context.Context, id int64) (EventStoreLinkedHistoricCommand, error) {
	row := q.db.QueryRowContext(ctx, retrieveLinkHistoricCommandByID, id)
	var i EventStoreLinkedHistoricCommand
	err := row.Scan(
		&i.ID,
		&i.Data,
		&i.ProcessedAt,
		&i.LinkedAt,
	)
	return i, err
}

const retrieveLinkHistoricCommandsInDateRange = `-- name: RetrieveLinkHistoricCommandsInDateRange :many
SELECT id, data
FROM event_store.linked_historic_commands
WHERE processed_at is null
AND linked_at between $1::timestamp and $2::timestamp
ORDER BY linked_at DESC
LIMIT $3
`

type RetrieveLinkHistoricCommandsInDateRangeParams struct {
	FromDate  time.Time `json:"from_date"`
	ToDate    time.Time `json:"to_date"`
	ChunkSize int32     `json:"chunk_size"`
}

type RetrieveLinkHistoricCommandsInDateRangeRow struct {
	ID   int64           `json:"id"`
	Data json.RawMessage `json:"data"`
}

func (q *Queries) RetrieveLinkHistoricCommandsInDateRange(ctx context.Context, arg RetrieveLinkHistoricCommandsInDateRangeParams) ([]RetrieveLinkHistoricCommandsInDateRangeRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveLinkHistoricCommandsInDateRange, arg.FromDate, arg.ToDate, arg.ChunkSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveLinkHistoricCommandsInDateRangeRow
	for rows.Next() {
		var i RetrieveLinkHistoricCommandsInDateRangeRow
		if err := rows.Scan(&i.ID, &i.Data); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
