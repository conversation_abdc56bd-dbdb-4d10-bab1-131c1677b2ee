package infra

import (
	"experience/libs/data-platform/tasks"
	"experience/libs/data-platform/tasks/publish-historic-link-commands/sqlc"

	"github.com/jinzhu/copier"
)

func ToLinkHistoricCommands(rows []sqlc.RetrieveLinkHistoricCommandsInDateRangeRow) ([]tasks.HistoricEvent, error) {
	historicEvents := make([]tasks.HistoricEvent, 0)

	err := copier.Copy(&historicEvents, rows)
	if err != nil {
		return nil, err
	}

	return historicEvents, nil
}
