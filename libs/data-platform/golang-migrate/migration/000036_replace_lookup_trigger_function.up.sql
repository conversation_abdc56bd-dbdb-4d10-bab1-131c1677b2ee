CREATE OR REPLACE FUNCTION POPULATE_CHARGES_LK_UP()
  RETURNS TRIGGER
  LANGUAGE plpgsql
AS
$$
BEGIN
  INSERT INTO aggregate_statistics.charges_lk_up(charge_id, group_uid, location_id, address_id, presentment_amount)
  SELECT c.id, g.uid, pl.id, a.id, be.presentment_amount
  FROM podpoint.pod_addresses a
         INNER JOIN podpoint.groups g ON g.id = a.group_id
         INNER JOIN podpoint.pod_locations pl ON pl.address_id = a.id
         INNER JOIN podpoint.pod_units pu ON pl.unit_id = pu.id
         INNER JOIN podpoint.charges c ON c.unit_id = pu.id
         LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id AND be.deleted_at IS NULL
  WHERE c.id = old.id;
  RETURN
    new;
END;
$$;

ALTER TABLE aggregate_statistics.charges_lk_up DROP COLUMN user_id;
