-- see https://github.com/Pod-Point/experience/blob/main/docs/data-platform/docs/support/data-fixes/change-group.md
CREATE OR REPLACE FUNCTION aggregate_statistics.move_existing_charges_to_current_group(group_name varchar(255))
  RETURNS text
  LANGUAGE plpgsql
AS
$$
BEGIN
with group_cte as (select uid, id from podpoint.groups where name = $1 and deleted_at is null)
update aggregate_statistics.charges_lk_up set group_uid = (select uid from group_cte)
where address_id in (
  select id from podpoint.pod_addresses where group_id = (select id from group_cte)
)
  and group_uid != (select uid from group_cte);
RETURN
  'SUCCESS';
END;
$$;
