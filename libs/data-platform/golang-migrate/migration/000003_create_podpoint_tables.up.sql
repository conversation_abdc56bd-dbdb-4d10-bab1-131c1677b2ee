CREATE TABLE IF NOT EXISTS podpoint.billing_events
(
  id                   bigint                                              NOT NULL,
  account_id           bigint,
  presentment_amount   integer                                             NOT NULL,
  presentment_currency character varying(3) COLLATE pg_catalog."default"   NOT NULL,
  exchange_rate        character varying(255) COLLATE pg_catalog."default" NOT NULL,
  settlement_amount    integer                                             NOT NULL,
  settlement_currency  character varying(3) COLLATE pg_catalog."default"   NOT NULL,
  amount               integer,
  transaction_provider character varying(7) COLLATE pg_catalog."default",
  transaction_id       character varying(191) COLLATE pg_catalog."default",
  description          character varying(255) COLLATE pg_catalog."default" NOT NULL,
  refunded_amount      bigint,
  created_at           timestamp WITHOUT TIME ZONE,
  processed_at         timestamp WITHOUT TIME ZONE,
  updated_at           timestamp WITHOUT TIME ZONE,
  deleted_at           timestamp WITHOUT TIME ZONE,
  user_id              bigint,
  CONSTRAINT billing_events_pkey PRIMARY KEY (id)
)
  TABLESPACE pg_default;

ALTER TABLE IF EXISTS podpoint.billing_events
  OWNER TO postgres;

CREATE TABLE IF NOT EXISTS podpoint.charges
(
  id                       bigint         NOT NULL,
  location_id              bigint,
  unit_id                  bigint         NOT NULL,
  job_id                   character varying(255) COLLATE pg_catalog."default",
  door                     bigint         NOT NULL,
  energy_cost              integer,
  tag_id                   bigint,
  billing_account_id       bigint,
  billing_event_id         bigint,
  group_id                 bigint,
  claimed_charge_id        bigint,
  charge_cycle_id          character varying(255) COLLATE pg_catalog."default",
  starts_at                timestamp WITHOUT TIME ZONE,
  ends_at                  timestamp WITHOUT TIME ZONE,
  start_event_processed_at timestamp WITHOUT TIME ZONE,
  end_event_processed_at   timestamp WITHOUT TIME ZONE,
  kwh_used                 numeric(18, 2) NOT NULL,
  duration                 bigint,
  is_closed                smallint       NOT NULL,
  created_at               timestamp WITHOUT TIME ZONE,
  updated_at               timestamp WITHOUT TIME ZONE,
  deleted_at               timestamp WITHOUT TIME ZONE,
  CONSTRAINT charges_pkey PRIMARY KEY (id)
)
  TABLESPACE pg_default;

ALTER TABLE IF EXISTS podpoint.charges
  OWNER TO postgres;

CREATE TABLE IF NOT EXISTS podpoint.pod_addresses
(
  id            bigint                                              NOT NULL,
  group_id      bigint,
  contact_name  character varying(255) COLLATE pg_catalog."default",
  email         character varying(255) COLLATE pg_catalog."default",
  telephone     character varying(255) COLLATE pg_catalog."default",
  business_name character varying(255) COLLATE pg_catalog."default" NOT NULL,
  line_1        character varying(255) COLLATE pg_catalog."default" NOT NULL,
  line_2        character varying(255) COLLATE pg_catalog."default" NOT NULL,
  postal_town   character varying(255) COLLATE pg_catalog."default" NOT NULL,
  postcode      character varying(255) COLLATE pg_catalog."default" NOT NULL,
  country       character varying(255) COLLATE pg_catalog."default" NOT NULL,
  description   character varying(255) COLLATE pg_catalog."default" NOT NULL,
  type_id       bigint                                              NOT NULL,
  tariff_id     bigint,
  cost_per_kwh  bigint,
  created_at    timestamp WITHOUT TIME ZONE,
  updated_at    timestamp WITHOUT TIME ZONE,
  deleted_at    timestamp WITHOUT TIME ZONE,
  CONSTRAINT pod_addresses_pkey PRIMARY KEY (id)
)
  TABLESPACE pg_default;

ALTER TABLE IF EXISTS podpoint.pod_addresses
  OWNER TO postgres;

CREATE TABLE IF NOT EXISTS podpoint.pod_locations
(
  id                  bigint                                              NOT NULL,
  address_id          bigint                                              NOT NULL,
  revenue_profile_id  bigint,
  advert_id           bigint,
  longitude           numeric(11, 8)                                      NOT NULL,
  latitude            numeric(11, 8)                                      NOT NULL,
  geohash             bigint,
  name                character varying(255) COLLATE pg_catalog."default",
  description         character varying(255) COLLATE pg_catalog."default" NOT NULL,
  payg_enabled        smallint                                            NOT NULL,
  contactless_enabled smallint                                            NOT NULL,
  midmeter_enabled    smallint                                            NOT NULL,
  is_public           smallint                                            NOT NULL,
  is_home             smallint                                            NOT NULL,
  is_ev_zone          smallint                                            NOT NULL,
  unit_id             bigint,
  created_at          timestamp WITHOUT TIME ZONE,
  updated_at          timestamp WITHOUT TIME ZONE,
  deleted_at          timestamp WITHOUT TIME ZONE,
  timezone            character varying(32) COLLATE pg_catalog."default",
  CONSTRAINT pod_locations_pkey PRIMARY KEY (id)
)
  TABLESPACE pg_default;

ALTER TABLE IF EXISTS podpoint.pod_locations
  OWNER TO postgres;

CREATE TABLE IF NOT EXISTS podpoint.pod_units
(
  id                bigint                                              NOT NULL,
  ppid              character varying(191) COLLATE pg_catalog."default" NOT NULL,
  name              character varying(191) COLLATE pg_catalog."default",
  model_id          bigint                                              NOT NULL,
  status_id         bigint,
  config_id         integer,
  ocpp_endpoint_id  bigint,
  installation_id   character varying(255) COLLATE pg_catalog."default",
  date_commissioned date,
  last_contact      timestamp WITHOUT TIME ZONE,
  relay_weld_flag   smallint                                            NOT NULL,
  created_at        timestamp WITHOUT TIME ZONE,
  updated_at        timestamp WITHOUT TIME ZONE,
  deleted_at        timestamp WITHOUT TIME ZONE,
  CONSTRAINT pod_units_pkey PRIMARY KEY (id)
)
  TABLESPACE pg_default;

ALTER TABLE IF EXISTS podpoint.pod_units
  OWNER TO postgres;

GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.billing_events, podpoint.charges, podpoint.pod_addresses, podpoint.pod_locations, podpoint.pod_units TO xdp_api;
