REVOKE ALL PRIVILEGES ON aggregate_statistics.monthly_site_statistics_view FROM xdp_api;
DROP VIEW IF EXISTS aggregate_statistics.monthly_site_statistics_view;

CREATE OR REPLACE VIEW aggregate_statistics.monthly_site_statistics_view AS
  SELECT pa.id                                                                   AS site_id,
         COUNT(c.id)                                                             AS number_of_charges,
         COALESCE(SUM(be.presentment_amount * -1), 0)::bigint                    AS revenue_generated,
         COALESCE(ROUND(SUM(c.kwh_used), 2), 0)::numeric(18, 2)                  AS energy_usage,
         COALESCE(SUM(EXTRACT(EPOCH FROM (c.ends_at - c.starts_at))), 0)::bigint AS plugged_in_duration,
         COALESCE(SUM(c.duration), 0)::bigint                                    AS charging_duration,
         COALESCE(SUM(c.energy_cost), 0)::bigint                                 AS energy_cost,
         TO_CHAR(c.starts_at, 'yyyy-mm')                                         AS reporting_period
  FROM podpoint.pod_addresses pa
         INNER JOIN podpoint.pod_locations pl ON pl.address_id = pa.id
         INNER JOIN podpoint.pod_units pu ON pl.unit_id = pu.id
         LEFT JOIN podpoint.charges c
                   ON c.unit_id = pu.id AND c.ends_at > (DATE_TRUNC('month', NOW()) - INTERVAL '12 month')
         LEFT JOIN podpoint.billing_events be ON be.id = c.billing_event_id AND be.deleted_at IS NULL
  WHERE pa.deleted_at IS NULL
    AND pl.deleted_at IS NULL
    AND pu.deleted_at IS NULL
    AND c.deleted_at IS NULL
    AND c.is_closed = 1
  GROUP BY pa.id, TO_CHAR(c.starts_at, 'yyyy-mm')
  ORDER BY reporting_period DESC;

GRANT SELECT ON aggregate_statistics.monthly_site_statistics_view TO xdp_api;
