CREATE USER grafana_ro;

GRANT USAGE ON SCHEMA podpoint TO grafana_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA podpoint GRANT SELECT ON TABLES TO grafana_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA podpoint TO grafana_ro;

GRANT USAGE ON SCHEMA commercial TO grafana_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA commercial GRANT SELECT ON TABLES TO grafana_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA commercial TO grafana_ro;

GRANT USAGE ON SCHEMA aggregate_statistics TO grafana_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA aggregate_statistics GRANT SELECT ON TABLES TO grafana_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA aggregate_statistics TO grafana_ro;

GRANT USAGE ON SCHEMA carbon_intensity TO grafana_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA carbon_intensity GRANT SELECT ON TABLES TO grafana_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA carbon_intensity TO grafana_ro;
