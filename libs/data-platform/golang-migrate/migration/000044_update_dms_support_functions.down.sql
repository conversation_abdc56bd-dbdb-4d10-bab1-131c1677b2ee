DROP FUNCTION podpoint.repopulate_charges_lk_up;

CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION podpoint.drop_constraints()
  RETURNS text
  LANGUAGE plpgsql
AS
$$
DECLARE
BEGIN

  ALTER TABLE IF EXISTS commercial.submitted_charges
    DROP CONSTRAINT IF EXISTS charge_id_fk;

  ALTER TABLE IF EXISTS commercial.submitted_charges
    DROP CONSTRAINT IF EXISTS processed_by_fk;

  RETURN 'SUCCESS';
END;
$$;

CREATE OR REPLACE FUNCTION podpoint.recreate_constraints()
  RETURNS text
  LANGUAGE plpgsql
AS
$$
DECLARE
BEGIN

  ALTER TABLE IF EXISTS commercial.submitted_charges
    DROP CONSTRAINT IF EXISTS charge_id_fk,
    ADD CONSTRAINT charge_id_fk FOREIGN KEY (charge_id) REFERENCES podpoint.charges (id) MATCH FULL;

  ALTER TABLE IF EXISTS commercial.submitted_charges
    DROP CONSTRAINT IF EXISTS processed_by_fk,
    ADD CONSTRAINT processed_by_fk FOREIGN KEY (processed_by) REFERENCES podpoint.users(id);

  RETURN 'SUCCESS';
END;
$$;
