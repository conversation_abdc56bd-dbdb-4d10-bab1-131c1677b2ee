CREATE SCHEMA IF NOT EXISTS event_store;

CREATE TABLE IF NOT EXISTS event_store.events
(
  id                  BIGSERIAL PRIMARY KEY,      -- unique id
  transaction_id      XID8 NOT NULL,              -- reliable event polling marker
  aggregate_id        UUID NOT NULL,              -- unique identifier for stream of all events related to one entity, aggregate, or workflow
  version             INTEGER NOT NULL,           -- optimistic concurrency control
  data                JSONB NOT NULL,             -- event payload
  UNIQUE (aggregate_id, version)
);

CREATE INDEX IF NOT EXISTS events_transaction_id_id_idx ON event_store.events (transaction_id, id);
CREATE INDEX IF NOT EXISTS events_aggregate_id_idx ON event_store.events (aggregate_id);

GRANT USAGE ON SCHEMA event_store TO xdp_api;
GRANT SELECT, INSERT ON event_store.events TO xdp_api;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA event_store TO xdp_api;
