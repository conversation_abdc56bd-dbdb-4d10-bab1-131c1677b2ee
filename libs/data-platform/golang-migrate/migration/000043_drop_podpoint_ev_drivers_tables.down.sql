CREATE TABLE IF NOT EXISTS podpoint.ev_drivers
(
  id          bigint       not null primary key,
  email       varchar(255) not null,
  first_name  varchar(255) not null,
  last_name   varchar(255) not null,
  group_id    bigint       not null,
  can_expense smallint     not null,
  created_at  timestamp,
  updated_at  timestamp,
  deleted_at  timestamp
  );

ALTER TABLE IF EXISTS podpoint.ev_drivers
  OWNER TO postgres;

GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.ev_drivers TO xdp_api;
