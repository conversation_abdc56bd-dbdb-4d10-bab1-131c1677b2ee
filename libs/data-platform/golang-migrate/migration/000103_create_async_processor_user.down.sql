REVOKE SELECT ON event_store.events FROM xdp_async_processor;
<PERSON><PERSON><PERSON><PERSON> SELECT, INSERT, UPDATE ON event_store.event_subscriptions FROM xdp_async_processor;

REVOKE USAGE, SELECT ON ALL SEQUENCES IN SCHEMA projections FROM xdp_async_processor;
R<PERSON><PERSON><PERSON> SELECT, INSERT, UPDATE ON projections.site_stats_monthly FROM xdp_async_processor;

REVOKE USAGE ON SCHEMA event_store FROM xdp_async_processor;
REVOKE USAGE ON SCHEMA projections FROM xdp_async_processor;

DROP ROLE IF EXISTS xdp_async_processor;
