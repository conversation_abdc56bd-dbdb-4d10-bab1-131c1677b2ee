CREATE TABLE IF NOT EXISTS podpoint.claimed_charges
(
    id bigint NOT NULL PRIMARY KEY,
    pod_location_id bigint NOT NULL,
    pod_door_id bigint NOT NULL,
    user_id bigint,
    billing_event_id bigint,
    created_at timestamp WITHOUT TIME ZONE NOT NULL,
    updated_at timestamp WITHOUT TIME ZONE NOT NULL,
    deleted_at timestamp WITHOUT TIME ZONE,
    claimed_by bigint,
    authoriser_id bigint NOT NULL,
    billing_account_id bigint
);

CREATE INDEX IF NOT EXISTS claimed_charges_authoriser_id_index
    ON podpoint.claimed_charges (authoriser_id);

ALTER TABLE IF EXISTS podpoint.claimed_charges
    OWNER TO postgres;
GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.claimed_charges TO xdp_api;
