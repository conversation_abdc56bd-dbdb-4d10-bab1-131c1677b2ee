CREATE TABLE IF NOT EXISTS podpoint.pod_unit_user
(
  unit_id    bigint NOT NULL,
  user_id    bigint NOT NULL,
  created_at timestamp WITHOUT TIME ZONE,
  updated_at timestamp WITHOUT TIME ZONE,
  PRIMARY KEY (unit_id, user_id)
);

CREATE INDEX pod_unit_user_unit_id_index
  ON podpoint.pod_unit_user (unit_id);


ALTER TABLE podpoint.pod_unit_user
  OWNER TO postgres;

GRANT SELECT ON podpoint.pod_unit_user TO grafana_ro;
GRANT SELECT, INSERT, DELETE, UPDATE ON podpoint.pod_unit_user TO xdp_api;
