<PERSON><PERSON><PERSON><PERSON> SELECT, INSERT, DELE<PERSON>, UPDATE ON ALL TABLES IN SCHEMA podpoint FROM xdp_test;
ALTER DEFAULT PRIVILEGES IN SCHEMA podpoint REVOKE SELECT, INSERT, DELETE, UPDATE ON TABLES FROM xdp_test;
REVO<PERSON> USAGE ON SCHEMA podpoint FROM xdp_test;

R<PERSON>VO<PERSON> SELECT, INSERT, DELETE, UPDATE ON ALL TABLES IN SCHEMA commercial FROM xdp_test;
ALTER DEFAULT PRIVILEGES IN SCHEMA commercial REVOKE SELECT, INSERT, DELETE, UPDATE ON TABLES FROM xdp_test;
REVO<PERSON> USAGE ON SCHEMA commercial FROM xdp_test;

<PERSON><PERSON><PERSON><PERSON> SELECT, INSERT, DELETE, UPDATE ON ALL TABLES IN SCHEMA carbon_intensity FROM xdp_test;
ALTER DEFAULT PRIVILEGES IN SCHEMA carbon_intensity REVOKE SELECT, INSERT, DELETE, UPDATE ON TABLES FROM xdp_test;
R<PERSON>VO<PERSON> USAGE ON SCHEMA carbon_intensity FROM xdp_test;

<PERSON><PERSON>VO<PERSON> SELECT, INSERT, DELETE, UPDATE ON ALL TABLES IN SCHEMA aggregate_statistics FROM xdp_test;
ALTER DEFAULT PRIVILEGES IN SCHEMA aggregate_statistics REVOKE SELECT, INSERT, DELETE, UPDATE ON TABLES FROM xdp_test;
REVOKE USAGE ON SCHEMA aggregate_statistics FROM xdp_test;

DROP ROLE IF EXISTS xdp_test;
