// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Projection Charges HTTP server encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"context"
	"errors"
	projectioncharges "experience/libs/data-platform/api/contract/gen/projection_charges"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// EncodeProjectionChargeDataResponse returns an encoder for responses returned
// by the Projection Charges Projection Charge Data endpoint.
func EncodeProjectionChargeDataResponse(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder) func(context.Context, http.ResponseWriter, any) error {
	return func(ctx context.Context, w http.ResponseWriter, v any) error {
		res, _ := v.(*projectioncharges.ProjectionChargesResponse)
		enc := encoder(ctx, w)
		body := NewProjectionChargeDataResponseBody(res)
		w.WriteHeader(http.StatusOK)
		return enc.Encode(body)
	}
}

// DecodeProjectionChargeDataRequest returns a decoder for requests sent to the
// Projection Charges Projection Charge Data endpoint.
func DecodeProjectionChargeDataRequest(mux goahttp.Muxer, decoder func(*http.Request) goahttp.Decoder) func(*http.Request) (any, error) {
	return func(r *http.Request) (any, error) {
		var (
			groupID   *string
			siteID    *string
			chargerID *string
			from      string
			to        string
			err       error
		)
		qp := r.URL.Query()
		groupIDRaw := qp.Get("groupId")
		if groupIDRaw != "" {
			groupID = &groupIDRaw
		}
		if groupID != nil {
			err = goa.MergeErrors(err, goa.ValidateFormat("groupId", *groupID, goa.FormatUUID))
		}
		siteIDRaw := qp.Get("siteId")
		if siteIDRaw != "" {
			siteID = &siteIDRaw
		}
		if siteID != nil {
			err = goa.MergeErrors(err, goa.ValidateFormat("siteId", *siteID, goa.FormatUUID))
		}
		chargerIDRaw := qp.Get("chargerId")
		if chargerIDRaw != "" {
			chargerID = &chargerIDRaw
		}
		from = qp.Get("from")
		if from == "" {
			err = goa.MergeErrors(err, goa.MissingFieldError("from", "query string"))
		}
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		to = qp.Get("to")
		if to == "" {
			err = goa.MergeErrors(err, goa.MissingFieldError("to", "query string"))
		}
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
		payload := NewProjectionChargeDataPayload(groupID, siteID, chargerID, from, to)

		return payload, nil
	}
}

// EncodeProjectionChargeDataError returns an encoder for errors returned by
// the Projection Charge Data Projection Charges endpoint.
func EncodeProjectionChargeDataError(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder, formatter func(ctx context.Context, err error) goahttp.Statuser) func(context.Context, http.ResponseWriter, error) error {
	encodeError := goahttp.ErrorEncoder(encoder, formatter)
	return func(ctx context.Context, w http.ResponseWriter, v error) error {
		var en goa.GoaErrorNamer
		if !errors.As(v, &en) {
			return encodeError(ctx, w, v)
		}
		switch en.GoaErrorName() {
		case "bad_request":
			var res *goa.ServiceError
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewProjectionChargeDataBadRequestResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		case "identifier_not_provided":
			var res *projectioncharges.IdentifierNotProvided
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewProjectionChargeDataIdentifierNotProvidedResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		default:
			return encodeError(ctx, w, v)
		}
	}
}

// marshalProjectionchargesChargesToChargesResponseBody builds a value of type
// *ChargesResponseBody from a value of type *projectioncharges.Charges.
func marshalProjectionchargesChargesToChargesResponseBody(v *projectioncharges.Charges) *ChargesResponseBody {
	res := &ChargesResponseBody{
		ID:                    v.ID,
		SiteName:              v.SiteName,
		ChargerID:             v.ChargerID,
		ChargerName:           v.ChargerName,
		PluggedInAt:           v.PluggedInAt,
		UnpluggedAt:           v.UnpluggedAt,
		StartedAt:             v.StartedAt,
		EndedAt:               v.EndedAt,
		EnergyTotal:           v.EnergyTotal,
		GenerationEnergyTotal: v.GenerationEnergyTotal,
		GridEnergyTotal:       v.GridEnergyTotal,
		Door:                  v.Door,
		RevenueGenerated:      v.RevenueGenerated,
		ChargeDurationTotal:   v.ChargeDurationTotal,
		EnergyCost:            v.EnergyCost,
		Co2Avoided:            v.Co2Avoided,
		Confirmed:             v.Confirmed,
	}
	if v.DriverIDs != nil {
		res.DriverIDs = make([]string, len(v.DriverIDs))
		for i, val := range v.DriverIDs {
			res.DriverIDs[i] = val
		}
	} else {
		res.DriverIDs = []string{}
	}

	return res
}

// marshalProjectionchargesMetaToMetaResponseBody builds a value of type
// *MetaResponseBody from a value of type *projectioncharges.Meta.
func marshalProjectionchargesMetaToMetaResponseBody(v *projectioncharges.Meta) *MetaResponseBody {
	res := &MetaResponseBody{}
	if v.Params != nil {
		res.Params = make(map[string]any, len(v.Params))
		for key, val := range v.Params {
			tk := key
			tv := val
			res.Params[tk] = tv
		}
	}

	return res
}
