// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charging statistics HTTP client CLI support package
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	chargingstatistics "experience/libs/data-platform/api/contract/gen/charging_statistics"
	"fmt"
	"strconv"

	goa "goa.design/goa/v3/pkg"
)

// BuildSiteStatisticsPayload builds the payload for the Charging statistics
// Site statistics endpoint from CLI flags.
func BuildSiteStatisticsPayload(chargingStatisticsSiteStatisticsSiteID string, chargingStatisticsSiteStatisticsFrom string, chargingStatisticsSiteStatisticsTo string) (*chargingstatistics.SiteStatisticsPayload, error) {
	var err error
	var siteID int
	{
		var v int64
		v, err = strconv.ParseInt(chargingStatisticsSiteStatisticsSiteID, 10, strconv.IntSize)
		siteID = int(v)
		if err != nil {
			return nil, fmt.Errorf("invalid value for siteID, must be INT")
		}
		if siteID < 1 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("siteId", siteID, 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargingStatisticsSiteStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargingStatisticsSiteStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargingstatistics.SiteStatisticsPayload{}
	v.SiteID = siteID
	v.From = from
	v.To = to

	return v, nil
}

// BuildOrganisationStatisticsPayload builds the payload for the Charging
// statistics Organisation statistics endpoint from CLI flags.
func BuildOrganisationStatisticsPayload(chargingStatisticsOrganisationStatisticsOrganisationID string, chargingStatisticsOrganisationStatisticsFrom string, chargingStatisticsOrganisationStatisticsTo string) (*chargingstatistics.OrganisationStatisticsPayload, error) {
	var err error
	var organisationID string
	{
		organisationID = chargingStatisticsOrganisationStatisticsOrganisationID
		err = goa.MergeErrors(err, goa.ValidateFormat("organisationId", organisationID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargingStatisticsOrganisationStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargingStatisticsOrganisationStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargingstatistics.OrganisationStatisticsPayload{}
	v.OrganisationID = organisationID
	v.From = from
	v.To = to

	return v, nil
}

// BuildChargerStatisticsPayload builds the payload for the Charging statistics
// Charger statistics endpoint from CLI flags.
func BuildChargerStatisticsPayload(chargingStatisticsChargerStatisticsLocationID string, chargingStatisticsChargerStatisticsFrom string, chargingStatisticsChargerStatisticsTo string) (*chargingstatistics.ChargerStatisticsPayload, error) {
	var err error
	var locationID int
	{
		var v int64
		v, err = strconv.ParseInt(chargingStatisticsChargerStatisticsLocationID, 10, strconv.IntSize)
		locationID = int(v)
		if err != nil {
			return nil, fmt.Errorf("invalid value for locationID, must be INT")
		}
	}
	var from string
	{
		from = chargingStatisticsChargerStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargingStatisticsChargerStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargingstatistics.ChargerStatisticsPayload{}
	v.LocationID = locationID
	v.From = from
	v.To = to

	return v, nil
}
