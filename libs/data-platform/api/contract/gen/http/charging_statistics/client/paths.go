// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the Charging statistics service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// SiteStatisticsChargingStatisticsPath returns the URL path to the Charging statistics service Site statistics HTTP endpoint.
func SiteStatisticsChargingStatisticsPath(siteID int) string {
	return fmt.Sprintf("/charges/site/%v", siteID)
}

// OrganisationStatisticsChargingStatisticsPath returns the URL path to the Charging statistics service Organisation statistics HTTP endpoint.
func OrganisationStatisticsChargingStatisticsPath(organisationID string) string {
	return fmt.Sprintf("/charges/group/%v", organisationID)
}

// ChargerStatisticsChargingStatisticsPath returns the URL path to the Charging statistics service Charger statistics HTTP endpoint.
func ChargerStatisticsChargingStatisticsPath(locationID int) string {
	return fmt.Sprintf("/charges/charger/%v", locationID)
}
