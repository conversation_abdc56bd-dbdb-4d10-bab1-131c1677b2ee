// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Organisation charges client HTTP transport
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"context"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Client lists the Organisation charges service endpoint HTTP clients.
type Client struct {
	// ExpensesByOrganisation Doer is the HTTP client used to make requests to the
	// Expenses by organisation endpoint.
	ExpensesByOrganisationDoer goahttp.Doer

	// ExpensesByOrganisationGroupedByDriver Doer is the HTTP client used to make
	// requests to the Expenses by organisation, grouped by driver endpoint.
	ExpensesByOrganisationGroupedByDriverDoer goahttp.Doer

	// SubmittedChargesForDriver Doer is the HTTP client used to make requests to
	// the Submitted charges for driver endpoint.
	SubmittedChargesForDriverDoer goahttp.Doer

	// MarkSubmittedChargesAsProcessed Doer is the HTTP client used to make
	// requests to the Mark submitted charges as processed endpoint.
	MarkSubmittedChargesAsProcessedDoer goahttp.Doer

	// FleetUsageByOrganisation Doer is the HTTP client used to make requests to
	// the Fleet usage by organisation endpoint.
	FleetUsageByOrganisationDoer goahttp.Doer

	// RestoreResponseBody controls whether the response bodies are reset after
	// decoding so they can be read again.
	RestoreResponseBody bool

	scheme  string
	host    string
	encoder func(*http.Request) goahttp.Encoder
	decoder func(*http.Response) goahttp.Decoder
}

// NewClient instantiates HTTP clients for all the Organisation charges service
// servers.
func NewClient(
	scheme string,
	host string,
	doer goahttp.Doer,
	enc func(*http.Request) goahttp.Encoder,
	dec func(*http.Response) goahttp.Decoder,
	restoreBody bool,
) *Client {
	return &Client{
		ExpensesByOrganisationDoer:                doer,
		ExpensesByOrganisationGroupedByDriverDoer: doer,
		SubmittedChargesForDriverDoer:             doer,
		MarkSubmittedChargesAsProcessedDoer:       doer,
		FleetUsageByOrganisationDoer:              doer,
		RestoreResponseBody:                       restoreBody,
		scheme:                                    scheme,
		host:                                      host,
		decoder:                                   dec,
		encoder:                                   enc,
	}
}

// ExpensesByOrganisation returns an endpoint that makes HTTP requests to the
// Organisation charges service Expenses by organisation server.
func (c *Client) ExpensesByOrganisation() goa.Endpoint {
	var (
		encodeRequest  = EncodeExpensesByOrganisationRequest(c.encoder)
		decodeResponse = DecodeExpensesByOrganisationResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildExpensesByOrganisationRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.ExpensesByOrganisationDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Organisation charges", "Expenses by organisation", err)
		}
		return decodeResponse(resp)
	}
}

// ExpensesByOrganisationGroupedByDriver returns an endpoint that makes HTTP
// requests to the Organisation charges service Expenses by organisation,
// grouped by driver server.
func (c *Client) ExpensesByOrganisationGroupedByDriver() goa.Endpoint {
	var (
		encodeRequest  = EncodeExpensesByOrganisationGroupedByDriverRequest(c.encoder)
		decodeResponse = DecodeExpensesByOrganisationGroupedByDriverResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildExpensesByOrganisationGroupedByDriverRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.ExpensesByOrganisationGroupedByDriverDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Organisation charges", "Expenses by organisation, grouped by driver", err)
		}
		return decodeResponse(resp)
	}
}

// SubmittedChargesForDriver returns an endpoint that makes HTTP requests to
// the Organisation charges service Submitted charges for driver server.
func (c *Client) SubmittedChargesForDriver() goa.Endpoint {
	var (
		decodeResponse = DecodeSubmittedChargesForDriverResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildSubmittedChargesForDriverRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.SubmittedChargesForDriverDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Organisation charges", "Submitted charges for driver", err)
		}
		return decodeResponse(resp)
	}
}

// MarkSubmittedChargesAsProcessed returns an endpoint that makes HTTP requests
// to the Organisation charges service Mark submitted charges as processed
// server.
func (c *Client) MarkSubmittedChargesAsProcessed() goa.Endpoint {
	var (
		encodeRequest  = EncodeMarkSubmittedChargesAsProcessedRequest(c.encoder)
		decodeResponse = DecodeMarkSubmittedChargesAsProcessedResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildMarkSubmittedChargesAsProcessedRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.MarkSubmittedChargesAsProcessedDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Organisation charges", "Mark submitted charges as processed", err)
		}
		return decodeResponse(resp)
	}
}

// FleetUsageByOrganisation returns an endpoint that makes HTTP requests to the
// Organisation charges service Fleet usage by organisation server.
func (c *Client) FleetUsageByOrganisation() goa.Endpoint {
	var (
		decodeResponse = DecodeFleetUsageByOrganisationResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildFleetUsageByOrganisationRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.FleetUsageByOrganisationDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Organisation charges", "Fleet usage by organisation", err)
		}
		return decodeResponse(resp)
	}
}
