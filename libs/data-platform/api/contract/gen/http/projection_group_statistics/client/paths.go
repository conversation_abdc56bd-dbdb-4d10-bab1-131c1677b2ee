// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the Projection Group Statistics service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// GroupSiteStatisticsProjectionGroupStatisticsPath returns the URL path to the Projection Group Statistics service Group site statistics HTTP endpoint.
func GroupSiteStatisticsProjectionGroupStatisticsPath(groupID string) string {
	return fmt.Sprintf("/charges/groups/%v/sites", groupID)
}
