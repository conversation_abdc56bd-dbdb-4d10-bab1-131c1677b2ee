// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Projection Group Statistics HTTP client CLI support package
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	projectiongroupstatistics "experience/libs/data-platform/api/contract/gen/projection_group_statistics"
	"fmt"
	"strconv"

	goa "goa.design/goa/v3/pkg"
)

// BuildGroupSiteStatisticsPayload builds the payload for the Projection Group
// Statistics Group site statistics endpoint from CLI flags.
func BuildGroupSiteStatisticsPayload(projectionGroupStatisticsGroupSiteStatisticsGroupID string, projectionGroupStatisticsGroupSiteStatisticsYear string, projectionGroupStatisticsGroupSiteStatisticsMonth string) (*projectiongroupstatistics.GroupSiteStatisticsPayload, error) {
	var err error
	var groupID string
	{
		groupID = projectionGroupStatisticsGroupSiteStatisticsGroupID
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var year int
	{
		var v int64
		v, err = strconv.ParseInt(projectionGroupStatisticsGroupSiteStatisticsYear, 10, strconv.IntSize)
		year = int(v)
		if err != nil {
			return nil, fmt.Errorf("invalid value for year, must be INT")
		}
		if year < 1 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("year", year, 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var month int
	{
		var v int64
		v, err = strconv.ParseInt(projectionGroupStatisticsGroupSiteStatisticsMonth, 10, strconv.IntSize)
		month = int(v)
		if err != nil {
			return nil, fmt.Errorf("invalid value for month, must be INT")
		}
		if month < 1 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("month", month, 1, true))
		}
		if month > 12 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("month", month, 12, false))
		}
		if err != nil {
			return nil, err
		}
	}
	v := &projectiongroupstatistics.GroupSiteStatisticsPayload{}
	v.GroupID = groupID
	v.Year = year
	v.Month = month

	return v, nil
}
