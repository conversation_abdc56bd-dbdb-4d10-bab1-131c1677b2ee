// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Projection Group Statistics HTTP server types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	projectiongroupstatistics "experience/libs/data-platform/api/contract/gen/projection_group_statistics"

	goa "goa.design/goa/v3/pkg"
)

// GroupSiteStatisticsResponseBody is the type of the "Projection Group
// Statistics" service "Group site statistics" endpoint HTTP response body.
type GroupSiteStatisticsResponseBody struct {
	Data []*GroupSitesStatsResponseBody `form:"data" json:"data" xml:"data"`
	Meta *MetaResponseBody              `form:"meta" json:"meta" xml:"meta"`
}

// GroupSiteStatisticsBadRequestResponseBody is the type of the "Projection
// Group Statistics" service "Group site statistics" endpoint HTTP response
// body for the "bad_request" error.
type GroupSiteStatisticsBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// GroupSiteStatisticsGroupNotFoundResponseBody is the type of the "Projection
// Group Statistics" service "Group site statistics" endpoint HTTP response
// body for the "group_not_found" error.
type GroupSiteStatisticsGroupNotFoundResponseBody struct {
	Reason string `form:"reason" json:"reason" xml:"reason"`
	Status int    `form:"status" json:"status" xml:"status"`
}

// GroupSitesStatsResponseBody is used to define fields on response body types.
type GroupSitesStatsResponseBody struct {
	// Site ID
	SiteID string `form:"siteId" json:"siteId" xml:"siteId"`
	// Total energy usage in kWh.
	EnergyUsageKwh float64 `form:"energyUsageKwh" json:"energyUsageKwh" xml:"energyUsageKwh"`
	// Total energy cost in pence.
	EnergyCost int `form:"energyCost" json:"energyCost" xml:"energyCost"`
	// CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per
	// mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy
	// saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
	Co2AvoidedKg float64 `form:"co2AvoidedKg" json:"co2AvoidedKg" xml:"co2AvoidedKg"`
	// Revenue generated in pence.
	RevenueGenerated int `form:"revenueGenerated" json:"revenueGenerated" xml:"revenueGenerated"`
	// Total number of charges.
	NumberOfCharges int `form:"numberOfCharges" json:"numberOfCharges" xml:"numberOfCharges"`
	// Total number of unique drivers.
	NumberOfDrivers int `form:"numberOfDrivers" json:"numberOfDrivers" xml:"numberOfDrivers"`
	// Total duration of charging in seconds.
	TotalDuration int `form:"totalDuration" json:"totalDuration" xml:"totalDuration"`
}

// MetaResponseBody is used to define fields on response body types.
type MetaResponseBody struct {
	// Passed parameters
	Params map[string]any `form:"params" json:"params" xml:"params"`
}

// NewGroupSiteStatisticsResponseBody builds the HTTP response body from the
// result of the "Group site statistics" endpoint of the "Projection Group
// Statistics" service.
func NewGroupSiteStatisticsResponseBody(res *projectiongroupstatistics.GroupSitesStatsResponse) *GroupSiteStatisticsResponseBody {
	body := &GroupSiteStatisticsResponseBody{}
	if res.Data != nil {
		body.Data = make([]*GroupSitesStatsResponseBody, len(res.Data))
		for i, val := range res.Data {
			body.Data[i] = marshalProjectiongroupstatisticsGroupSitesStatsToGroupSitesStatsResponseBody(val)
		}
	} else {
		body.Data = []*GroupSitesStatsResponseBody{}
	}
	if res.Meta != nil {
		body.Meta = marshalProjectiongroupstatisticsMetaToMetaResponseBody(res.Meta)
	}
	return body
}

// NewGroupSiteStatisticsBadRequestResponseBody builds the HTTP response body
// from the result of the "Group site statistics" endpoint of the "Projection
// Group Statistics" service.
func NewGroupSiteStatisticsBadRequestResponseBody(res *goa.ServiceError) *GroupSiteStatisticsBadRequestResponseBody {
	body := &GroupSiteStatisticsBadRequestResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewGroupSiteStatisticsGroupNotFoundResponseBody builds the HTTP response
// body from the result of the "Group site statistics" endpoint of the
// "Projection Group Statistics" service.
func NewGroupSiteStatisticsGroupNotFoundResponseBody(res *projectiongroupstatistics.GroupNotFound) *GroupSiteStatisticsGroupNotFoundResponseBody {
	body := &GroupSiteStatisticsGroupNotFoundResponseBody{
		Reason: res.Reason,
		Status: res.Status,
	}
	return body
}

// NewGroupSiteStatisticsPayload builds a Projection Group Statistics service
// Group site statistics endpoint payload.
func NewGroupSiteStatisticsPayload(groupID string, year int, month int) *projectiongroupstatistics.GroupSiteStatisticsPayload {
	v := &projectiongroupstatistics.GroupSiteStatisticsPayload{}
	v.GroupID = groupID
	v.Year = year
	v.Month = month

	return v
}
