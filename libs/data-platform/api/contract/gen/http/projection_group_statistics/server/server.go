// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Projection Group Statistics HTTP server
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"context"
	projectiongroupstatistics "experience/libs/data-platform/api/contract/gen/projection_group_statistics"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Server lists the Projection Group Statistics service endpoint HTTP handlers.
type Server struct {
	Mounts              []*MountPoint
	GroupSiteStatistics http.Handler
}

// MountPoint holds information about the mounted endpoints.
type MountPoint struct {
	// Method is the name of the service method served by the mounted HTTP handler.
	Method string
	// Verb is the HTTP method used to match requests to the mounted handler.
	Verb string
	// Pattern is the HTTP request path pattern used to match requests to the
	// mounted handler.
	Pattern string
}

// New instantiates HTTP handlers for all the Projection Group Statistics
// service endpoints using the provided encoder and decoder. The handlers are
// mounted on the given mux using the HTTP verb and path defined in the design.
// errhandler is called whenever a response fails to be encoded. formatter is
// used to format errors returned by the service methods prior to encoding.
// Both errhandler and formatter are optional and can be nil.
func New(
	e *projectiongroupstatistics.Endpoints,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) *Server {
	return &Server{
		Mounts: []*MountPoint{
			{"GroupSiteStatistics", "GET", "/charges/groups/{groupId}/sites"},
		},
		GroupSiteStatistics: NewGroupSiteStatisticsHandler(e.GroupSiteStatistics, mux, decoder, encoder, errhandler, formatter),
	}
}

// Service returns the name of the service served.
func (s *Server) Service() string { return "Projection Group Statistics" }

// Use wraps the server handlers with the given middleware.
func (s *Server) Use(m func(http.Handler) http.Handler) {
	s.GroupSiteStatistics = m(s.GroupSiteStatistics)
}

// MethodNames returns the methods served.
func (s *Server) MethodNames() []string { return projectiongroupstatistics.MethodNames[:] }

// Mount configures the mux to serve the Projection Group Statistics endpoints.
func Mount(mux goahttp.Muxer, h *Server) {
	MountGroupSiteStatisticsHandler(mux, h.GroupSiteStatistics)
}

// Mount configures the mux to serve the Projection Group Statistics endpoints.
func (s *Server) Mount(mux goahttp.Muxer) {
	Mount(mux, s)
}

// MountGroupSiteStatisticsHandler configures the mux to serve the "Projection
// Group Statistics" service "Group site statistics" endpoint.
func MountGroupSiteStatisticsHandler(mux goahttp.Muxer, h http.Handler) {
	f, ok := h.(http.HandlerFunc)
	if !ok {
		f = func(w http.ResponseWriter, r *http.Request) {
			h.ServeHTTP(w, r)
		}
	}
	mux.Handle("GET", "/charges/groups/{groupId}/sites", f)
}

// NewGroupSiteStatisticsHandler creates a HTTP handler which loads the HTTP
// request and calls the "Projection Group Statistics" service "Group site
// statistics" endpoint.
func NewGroupSiteStatisticsHandler(
	endpoint goa.Endpoint,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) http.Handler {
	var (
		decodeRequest  = DecodeGroupSiteStatisticsRequest(mux, decoder)
		encodeResponse = EncodeGroupSiteStatisticsResponse(encoder)
		encodeError    = EncodeGroupSiteStatisticsError(encoder, formatter)
	)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := context.WithValue(r.Context(), goahttp.AcceptTypeKey, r.Header.Get("Accept"))
		ctx = context.WithValue(ctx, goa.MethodKey, "Group site statistics")
		ctx = context.WithValue(ctx, goa.ServiceKey, "Projection Group Statistics")
		payload, err := decodeRequest(r)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		res, err := endpoint(ctx, payload)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		if err := encodeResponse(ctx, w, res); err != nil {
			errhandler(ctx, w, err)
		}
	})
}
