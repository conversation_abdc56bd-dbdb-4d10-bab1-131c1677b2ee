// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Carbon intensity HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	carbonintensity "experience/libs/data-platform/api/contract/gen/carbon_intensity"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildRetrieveRegionalForecast48HoursFromDateRequest instantiates a HTTP
// request object with method and path set to call the "Carbon intensity"
// service "Retrieve regional forecast 48 hours from date" endpoint
func (c *Client) BuildRetrieveRegionalForecast48HoursFromDateRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		from     string
		regionID int
	)
	{
		p, ok := v.(*carbonintensity.RetrieveRegionalForecast48HoursFromDatePayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Carbon intensity", "Retrieve regional forecast 48 hours from date", "*carbonintensity.RetrieveRegionalForecast48HoursFromDatePayload", v)
		}
		from = p.From
		regionID = p.RegionID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveRegionalForecast48HoursFromDateCarbonIntensityPath(from, regionID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Carbon intensity", "Retrieve regional forecast 48 hours from date", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// DecodeRetrieveRegionalForecast48HoursFromDateResponse returns a decoder for
// responses returned by the Carbon intensity Retrieve regional forecast 48
// hours from date endpoint. restoreBody controls whether the response body
// should be restored after having been read.
// DecodeRetrieveRegionalForecast48HoursFromDateResponse may return the
// following errors:
//   - "bad_request" (type *carbonintensity.InvalidTimestamp): http.StatusBadRequest
//   - "internal_server_error" (type *goa.ServiceError): http.StatusInternalServerError
//   - error: internal error
func DecodeRetrieveRegionalForecast48HoursFromDateResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveRegionalForecast48HoursFromDateResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Carbon intensity", "Retrieve regional forecast 48 hours from date", err)
			}
			err = ValidateRetrieveRegionalForecast48HoursFromDateResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Carbon intensity", "Retrieve regional forecast 48 hours from date", err)
			}
			res := NewRetrieveRegionalForecast48HoursFromDateForecastOK(&body)
			return res, nil
		case http.StatusBadRequest:
			return nil, NewRetrieveRegionalForecast48HoursFromDateBadRequest()
		case http.StatusInternalServerError:
			var (
				body RetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Carbon intensity", "Retrieve regional forecast 48 hours from date", err)
			}
			err = ValidateRetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Carbon intensity", "Retrieve regional forecast 48 hours from date", err)
			}
			return nil, NewRetrieveRegionalForecast48HoursFromDateInternalServerError(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Carbon intensity", "Retrieve regional forecast 48 hours from date", resp.StatusCode, string(body))
		}
	}
}

// unmarshalForecastdataResponseBodyToCarbonintensityForecastdata builds a
// value of type *carbonintensity.Forecastdata from a value of type
// *ForecastdataResponseBody.
func unmarshalForecastdataResponseBodyToCarbonintensityForecastdata(v *ForecastdataResponseBody) *carbonintensity.Forecastdata {
	res := &carbonintensity.Forecastdata{
		Regionid:  *v.Regionid,
		Dnoregion: *v.Dnoregion,
		Shortname: *v.Shortname,
	}
	res.Data = make([]*carbonintensity.Period, len(v.Data))
	for i, val := range v.Data {
		res.Data[i] = unmarshalPeriodResponseBodyToCarbonintensityPeriod(val)
	}

	return res
}

// unmarshalPeriodResponseBodyToCarbonintensityPeriod builds a value of type
// *carbonintensity.Period from a value of type *PeriodResponseBody.
func unmarshalPeriodResponseBodyToCarbonintensityPeriod(v *PeriodResponseBody) *carbonintensity.Period {
	res := &carbonintensity.Period{
		From: *v.From,
		To:   *v.To,
	}
	res.Intensity = unmarshalIntensityResponseBodyToCarbonintensityIntensity(v.Intensity)
	res.Generationmix = make([]*carbonintensity.Mix, len(v.Generationmix))
	for i, val := range v.Generationmix {
		res.Generationmix[i] = unmarshalMixResponseBodyToCarbonintensityMix(val)
	}

	return res
}

// unmarshalIntensityResponseBodyToCarbonintensityIntensity builds a value of
// type *carbonintensity.Intensity from a value of type *IntensityResponseBody.
func unmarshalIntensityResponseBodyToCarbonintensityIntensity(v *IntensityResponseBody) *carbonintensity.Intensity {
	res := &carbonintensity.Intensity{
		Forecast: *v.Forecast,
		Index:    *v.Index,
	}

	return res
}

// unmarshalMixResponseBodyToCarbonintensityMix builds a value of type
// *carbonintensity.Mix from a value of type *MixResponseBody.
func unmarshalMixResponseBodyToCarbonintensityMix(v *MixResponseBody) *carbonintensity.Mix {
	res := &carbonintensity.Mix{
		Fuel: *v.Fuel,
		Perc: *v.Perc,
	}

	return res
}
