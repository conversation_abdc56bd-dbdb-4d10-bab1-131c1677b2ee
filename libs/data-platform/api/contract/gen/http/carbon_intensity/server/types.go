// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Carbon intensity HTTP server types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	carbonintensity "experience/libs/data-platform/api/contract/gen/carbon_intensity"

	goa "goa.design/goa/v3/pkg"
)

// RetrieveRegionalForecast48HoursFromDateResponseBody is the type of the
// "Carbon intensity" service "Retrieve regional forecast 48 hours from date"
// endpoint HTTP response body.
type RetrieveRegionalForecast48HoursFromDateResponseBody struct {
	Data *ForecastdataResponseBody `form:"data" json:"data" xml:"data"`
}

// RetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody is
// the type of the "Carbon intensity" service "Retrieve regional forecast 48
// hours from date" endpoint HTTP response body for the "internal_server_error"
// error.
type RetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// ForecastdataResponseBody is used to define fields on response body types.
type ForecastdataResponseBody struct {
	Regionid  int                   `form:"regionid" json:"regionid" xml:"regionid"`
	Dnoregion string                `form:"dnoregion" json:"dnoregion" xml:"dnoregion"`
	Shortname string                `form:"shortname" json:"shortname" xml:"shortname"`
	Data      []*PeriodResponseBody `form:"data" json:"data" xml:"data"`
}

// PeriodResponseBody is used to define fields on response body types.
type PeriodResponseBody struct {
	From          string                 `form:"from" json:"from" xml:"from"`
	To            string                 `form:"to" json:"to" xml:"to"`
	Intensity     *IntensityResponseBody `form:"intensity" json:"intensity" xml:"intensity"`
	Generationmix []*MixResponseBody     `form:"generationmix" json:"generationmix" xml:"generationmix"`
}

// IntensityResponseBody is used to define fields on response body types.
type IntensityResponseBody struct {
	Forecast int `form:"forecast" json:"forecast" xml:"forecast"`
	// Intensity index with values: very low, low, moderate, high, very high
	Index string `form:"index" json:"index" xml:"index"`
}

// MixResponseBody is used to define fields on response body types.
type MixResponseBody struct {
	// Fuel type with values: gas, coal, biomass, nuclear, hydro, storage, imports,
	// other, wind, solar
	Fuel string  `form:"fuel" json:"fuel" xml:"fuel"`
	Perc float32 `form:"perc" json:"perc" xml:"perc"`
}

// NewRetrieveRegionalForecast48HoursFromDateResponseBody builds the HTTP
// response body from the result of the "Retrieve regional forecast 48 hours
// from date" endpoint of the "Carbon intensity" service.
func NewRetrieveRegionalForecast48HoursFromDateResponseBody(res *carbonintensity.Forecast) *RetrieveRegionalForecast48HoursFromDateResponseBody {
	body := &RetrieveRegionalForecast48HoursFromDateResponseBody{}
	if res.Data != nil {
		body.Data = marshalCarbonintensityForecastdataToForecastdataResponseBody(res.Data)
	}
	return body
}

// NewRetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody
// builds the HTTP response body from the result of the "Retrieve regional
// forecast 48 hours from date" endpoint of the "Carbon intensity" service.
func NewRetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody(res *goa.ServiceError) *RetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody {
	body := &RetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewRetrieveRegionalForecast48HoursFromDatePayload builds a Carbon intensity
// service Retrieve regional forecast 48 hours from date endpoint payload.
func NewRetrieveRegionalForecast48HoursFromDatePayload(from string, regionID int) *carbonintensity.RetrieveRegionalForecast48HoursFromDatePayload {
	v := &carbonintensity.RetrieveRegionalForecast48HoursFromDatePayload{}
	v.From = from
	v.RegionID = regionID

	return v
}
