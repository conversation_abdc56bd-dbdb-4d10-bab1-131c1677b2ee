openapi: 3.0.3
info:
  title: Data Platform API
  description: Collection of APIs fulfilling needs in the experience domain.
  version: 0.0.1
servers:
  - url: http://0.0.0.0:2830
  - url: http://data-platform-api.destination.cluster.com:2830
paths:
  /charge-authorisations/{authorisationMethod}:
    post:
      tags:
        - Charge Authorisation
      summary: authoriseCharge Charge Authorisation
      description: Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger's group.
      operationId: Charge Authorisation#authoriseCharge
      parameters:
        - name: authorisationMethod
          in: path
          description: One of rfid or ocpi.
          required: true
          schema:
            type: string
            example: Aut molestiae eum minus.
          example: rfid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthoriseChargeRequestBody'
            example:
              chargerId: PP-12345
              door: A
              token: 0DA46GKEFP3
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeAuthorisationResponse'
              example:
                authorised: true
                id: 86163097-7595-4d2e-bb3d-42aaca0ab308
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'authoriser_not_found: Not Found response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerPpidNotFound'
              example:
                reason: Quod sed aut totam et.
                status: 58492550306142
        '500':
          description: 'transaction_not_started: Internal Server Error response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                reason: Et sint numquam voluptatem doloremque.
                status: 4469253884378602624
  /chargers/{chargerId}/charge-statistics:
    get:
      tags:
        - Charge Statistics
      summary: Charger Charge Statistics Charge Statistics
      description: 'Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}'
      operationId: Charge Statistics#Charger Charge Statistics
      parameters:
        - name: chargerId
          in: path
          description: ID of the charger (equivalent to PPID).
          required: true
          schema:
            type: string
            description: ID of the charger (equivalent to PPID).
            example: PP-12003
            minLength: 1
          example: PP-12003
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2024-07-01'
            format: date
          example: '2024-07-01'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2024-07-31'
            format: date
          example: '2024-07-31'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectionchargerChargeStatisticsResponse'
              example:
                data:
                  chargingDuration: 22245
                  co2Savings: 4325.62
                  energy:
                    claimedUsage: 243.89
                    cost: 324550
                    revenueGeneratingClaimedUsage: 43.5
                    totalUsage: 567.89
                    unclaimedUsage: 324.65
                  numberOfCharges: 10
                  numberOfUsers: 2
                  revenueGenerated: 457600
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /chargers/{ppID}/limit:
    get:
      tags:
        - Chargers
      summary: Retrieve charging limit Chargers
      description: Retrieve limit for charging
      operationId: Chargers#Retrieve charging limit
      parameters:
        - name: ppID
          in: path
          description: PPID (PSL number) of a charger
          required: true
          schema:
            type: string
            description: PPID (PSL number) of a charger
            example: PP-12003
            minLength: 1
          example: PP-12003
        - name: authoriserUUID
          in: query
          description: UUID of charge authoriser - can be user's UUID or app name, like `dcs` or RFID key
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: UUID of charge authoriser - can be user's UUID or app name, like `dcs` or RFID key
            example: a82cc207-ee2a-3f38-a517-88aa6784f322
            minLength: 1
          example: a82cc207-ee2a-3f38-a517-88aa6784f322
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargesLimitResponse'
              example:
                allowed: true
                balance:
                  actual: 75
                  currency: GBP
                  minimum: 50
                limits:
                  - amount: 1.23
                    type: energy
                    unit: kWh
                  - amount: 1.23
                    type: energy
                    unit: kWh
        '400':
          description: 'bad_request: Returns 400 Bad Request when PPID or authoriser UUID is missing'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'not_found: Returns 404 Not Found, when PPID or authoriser UUID has not been found'
  /chargers/{ppId}/dnoregion:
    get:
      tags:
        - Chargers
      summary: Retrieve charger region Chargers
      description: For a given PSL / PPID, return the associated DNO region id.
      operationId: Chargers#Retrieve charger region
      parameters:
        - name: ppId
          in: path
          description: PPID of a given charger
          required: true
          schema:
            type: string
            description: PPID of a given charger
            example: PP-12003
          example: PP-12003
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Region'
              example:
                regionid: 1
        '400':
          description: 'bad_request: Returns bad request when a location does not exist for the provided charger psl/ppid.'
        '404':
          description: 'not_found: Returns not found when a DNO region cannot be determined for the provided charger psl/ppid.'
        '500':
          description: 'internal_server_error: Internal Server Error response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /charges:
    get:
      tags:
        - Projection Charges
      summary: Projection Charge Data Projection Charges
      description: All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.
      operationId: Projection Charges#Projection Charge Data
      parameters:
        - name: groupId
          in: query
          description: UUID of the group.
          allowEmptyValue: true
          schema:
            type: string
            description: UUID of the group.
            example: 55089ec6-1f61-4a35-be2a-043870726a10
            format: uuid
          example: 04648b54-ab6d-4666-bf0d-d58630afe0e5
        - name: siteId
          in: query
          description: UUID of the site.
          allowEmptyValue: true
          schema:
            type: string
            description: UUID of the site.
            example: c89bd6c5-43b5-4d48-8391-e86a3d82af01
            format: uuid
          example: ed13a747-f71b-4493-b467-bb62045b832c
        - name: chargerId
          in: query
          description: Id of the charger.
          allowEmptyValue: true
          schema:
            type: string
            description: Id of the charger.
            example: Et saepe ea quisquam maiores.
          example: Voluptas iste quo fuga nam ducimus.
        - name: from
          in: query
          description: Query param filter from charge unpluggedAt datetime. The from field is inclusive.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter from charge unpluggedAt datetime. The from field is inclusive.
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Query param filter to charge unpluggedAt datetime. The to field is exclusive.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter to charge unpluggedAt datetime. The to field is exclusive.
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectionChargesResponse'
              example:
                data:
                  - chargeDurationTotal: 22245
                    chargerId: PSL-0001
                    chargerName: Quis est perferendis repudiandae id laboriosam aliquam.
                    co2Avoided: 4325.62
                    confirmed: true
                    door: A
                    driverIDs:
                      - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                      - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
                    endedAt: '2018-05-15T12:00:00Z'
                    energyCost: 32
                    energyTotal: 567.89
                    generationEnergyTotal: 35.44
                    gridEnergyTotal: 532.45
                    id: 5588483f-7125-414a-887f-6fdc37911182
                    pluggedInAt: '2018-05-15T12:00:00Z'
                    revenueGenerated: 457600
                    siteName: Corporis corporis omnis minima.
                    startedAt: '2018-05-15T12:00:00Z'
                    unpluggedAt: '2018-05-15T12:00:00Z'
                  - chargeDurationTotal: 22245
                    chargerId: PSL-0001
                    chargerName: Quis est perferendis repudiandae id laboriosam aliquam.
                    co2Avoided: 4325.62
                    confirmed: true
                    door: A
                    driverIDs:
                      - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                      - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
                    endedAt: '2018-05-15T12:00:00Z'
                    energyCost: 32
                    energyTotal: 567.89
                    generationEnergyTotal: 35.44
                    gridEnergyTotal: 532.45
                    id: 5588483f-7125-414a-887f-6fdc37911182
                    pluggedInAt: '2018-05-15T12:00:00Z'
                    revenueGenerated: 457600
                    siteName: Corporis corporis omnis minima.
                    startedAt: '2018-05-15T12:00:00Z'
                    unpluggedAt: '2018-05-15T12:00:00Z'
                  - chargeDurationTotal: 22245
                    chargerId: PSL-0001
                    chargerName: Quis est perferendis repudiandae id laboriosam aliquam.
                    co2Avoided: 4325.62
                    confirmed: true
                    door: A
                    driverIDs:
                      - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                      - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
                    endedAt: '2018-05-15T12:00:00Z'
                    energyCost: 32
                    energyTotal: 567.89
                    generationEnergyTotal: 35.44
                    gridEnergyTotal: 532.45
                    id: 5588483f-7125-414a-887f-6fdc37911182
                    pluggedInAt: '2018-05-15T12:00:00Z'
                    revenueGenerated: 457600
                    siteName: Corporis corporis omnis minima.
                    startedAt: '2018-05-15T12:00:00Z'
                    unpluggedAt: '2018-05-15T12:00:00Z'
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/IdentifierNotProvided'
  /charges/charger/{locationId}:
    get:
      tags:
        - Charging statistics
      summary: Charger statistics Charging statistics
      description: Charge statistics for a single charger between two inclusive dates.
      operationId: Charging statistics#Charger statistics
      parameters:
        - name: locationId
          in: path
          description: Primary key of the charger location.
          required: true
          schema:
            type: integer
            description: Primary key of the charger location.
            example: 123
            format: int64
          example: 123
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chargerchargessummary'
              example:
                chargingDuration: 22245
                co2Savings: 4325.62
                energy:
                  claimedUsage: 243.89
                  cost: 324550
                  revenueGeneratingClaimedUsage: 43.5
                  totalUsage: 567.89
                  unclaimedUsage: 324.65
                numberOfCharges: 2048
                numberOfDrivers: 64
                revenueGenerated: 457600
        '400':
          description: 'bad_request: Returns bad request when date parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charger_not_found: Charger location not found'
  /charges/group/{organisationId}:
    get:
      tags:
        - Charging statistics
      summary: Organisation statistics Charging statistics
      description: Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the 'from' and 'to' date parameters constitute a valid date range. Where the 'to' date is  prior to  the 'from' date no results will be returned.
      operationId: Charging statistics#Organisation statistics
      parameters:
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: *************-4ac3-9723-ab1c1591327c
            format: uuid
          example: ffcf763b-6111-4e0f-a878-6bea066352bb
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organisationchargessummary'
              example:
                chargingDuration: 22245
                co2Savings: 4325.62
                energy:
                  claimedUsage: 243.89
                  cost: 324550
                  revenueGeneratingClaimedUsage: 43.5
                  totalUsage: 567.89
                  unclaimedUsage: 324.65
                numberOfChargers: 32
                numberOfCharges: 2048
                numberOfDrivers: 64
                numberOfSites: 16
                revenueGenerated: 457600
        '400':
          description: 'bad_request: Returns bad request when organisation ID is malformed or date parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'organisation_not_found: Organisation not found'
  /charges/groups/{groupId}/sites:
    get:
      tags:
        - Projection Group Statistics
      summary: Group site statistics Projection Group Statistics
      description: Charge statistics for all sites within a group for the given month.
      operationId: Projection Group Statistics#Group site statistics
      parameters:
        - name: groupId
          in: path
          description: UUID of the group.
          required: true
          schema:
            type: string
            description: UUID of the group.
            example: 1f063af3-d142-443d-a3a3-0ce6c6787336
            format: uuid
          example: ecc53f70-2454-49af-8440-4323d5b38478
        - name: year
          in: query
          description: Year to be queried.
          allowEmptyValue: true
          required: true
          schema:
            type: integer
            description: Year to be queried.
            example: 2021
            format: int64
            minimum: 1
          example: 2021
        - name: month
          in: query
          description: Month to be queried where Jan = 1, Feb = 2 etc...
          allowEmptyValue: true
          required: true
          schema:
            type: integer
            description: Month to be queried where Jan = 1, Feb = 2 etc...
            example: 3
            format: int64
            minimum: 1
            maximum: 12
          example: 10
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupSitesStatsResponse'
              example:
                data:
                  - co2AvoidedKg: 4325.62
                    energyCost: 3245
                    energyUsageKwh: 567.89
                    numberOfCharges: 2048
                    numberOfDrivers: 64
                    revenueGenerated: 457600
                    siteId: b2d87e7b-562e-4e7c-91cb-0841e34faf6d
                    totalDuration: 22245
                  - co2AvoidedKg: 4325.62
                    energyCost: 3245
                    energyUsageKwh: 567.89
                    numberOfCharges: 2048
                    numberOfDrivers: 64
                    revenueGenerated: 457600
                    siteId: b2d87e7b-562e-4e7c-91cb-0841e34faf6d
                    totalDuration: 22245
                  - co2AvoidedKg: 4325.62
                    energyCost: 3245
                    energyUsageKwh: 567.89
                    numberOfCharges: 2048
                    numberOfDrivers: 64
                    revenueGenerated: 457600
                    siteId: b2d87e7b-562e-4e7c-91cb-0841e34faf6d
                    totalDuration: 22245
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'group_not_found: Group not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupNotFound'
              example:
                reason: Repudiandae et.
                status: 7060964402108583820
  /charges/site/{siteId}:
    get:
      tags:
        - Charging statistics
      summary: Site statistics Charging statistics
      description: Charge statistics for a site between two inclusive dates.
      operationId: Charging statistics#Site statistics
      parameters:
        - name: siteId
          in: path
          required: true
          schema:
            type: integer
            example: 1001846446576937746
            format: int64
            minimum: 1
          example: 764905799943871649
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Sitechargessummary'
              example:
                chargingDuration: 22245
                co2Savings: 4325.62
                energy:
                  claimedUsage: 243.89
                  cost: 324550
                  revenueGeneratingClaimedUsage: 43.5
                  totalUsage: 567.89
                  unclaimedUsage: 324.65
                numberOfChargers: 32
                numberOfCharges: 2048
                numberOfDrivers: 64
                revenueGenerated: 457600
        '400':
          description: 'bad_request: Returns bad request when site ID is malformed'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'site_not_found: site not found'
  /commands/charges/{chargeID}/correct-energy-cost:
    post:
      tags:
        - Charge commands
      summary: Correct energy cost Charge commands
      description: Update the energy cost of a charge.
      operationId: Charge commands#Correct energy cost
      parameters:
        - name: chargeID
          in: path
          description: UUID of the charge to correct.
          required: true
          schema:
            type: string
            description: UUID of the charge to correct.
            example: 874eeba7-5b8a-40cd-aa18-7c4dc351c823
            format: uuid
          example: 97d0a32e-7cd9-4782-831e-52c605f06a22
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CorrectEnergyCostRequestBody'
            example:
              cost: 16
              submittedBy: John Doe
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AggregateCostCorrectedResponse'
              example:
                cost: 16
                id: 88d6a13f-6886-4009-80d7-1c3b75712d28
                submittedBy: John Doe
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/ChargeExpensed'
        '404':
          description: 'charge_not_found: Not Found response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeNotFound'
              example:
                reason: Distinctio dolores.
                status: 7658260991919733198
        '409':
          description: 'duplicate_requests: Conflict response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventOutOfDate'
              example:
                reason: Dicta tempore error qui itaque molestiae nostrum.
                status: 6287406437542363273
  /commands/charges/{chargeID}/correct-settlement-amount:
    post:
      tags:
        - Charge commands
      summary: Correct settlement amount Charge commands
      description: Update the settlement amount of a charge.
      operationId: Charge commands#Correct settlement amount
      parameters:
        - name: chargeID
          in: path
          description: UUID of the charge to correct.
          required: true
          schema:
            type: string
            description: UUID of the charge to correct.
            example: 8667c6ee-4f8e-42c7-a24d-725f27e16fb2
            format: uuid
          example: 2adfc31b-34ec-4524-88e2-5baa79738b23
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CorrectSettlementAmountRequestBody'
            example:
              settlementAmount: 16
              submittedBy: John Doe
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AggregateSettlementAmountCorrectedResponse'
              example:
                id: 2beb1246-c5ba-4dee-b8b2-97652a5fd6ea
                settlementAmount: 16
                submittedBy: John Doe
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charge_not_found: Not Found response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeNotFound'
              example:
                reason: Magni reiciendis facilis omnis dolores ut.
                status: 4907510526620846454
        '409':
          description: 'duplicate_requests: Conflict response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventOutOfDate'
              example:
                reason: Ut consectetur itaque omnis.
                status: 4914513838692508043
  /dno-regions:
    get:
      tags:
        - Chargers
      summary: Retrieve DNO regions Chargers
      description: Retrieve the complete collection of DNO region objects.
      operationId: Chargers#Retrieve DNO regions
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Regions'
              example:
                data:
                  - dnoregion: Scottish Hydro Electric Power Distribution
                    regionid: 1
                    shortname: North Scotland
                  - dnoregion: Scottish Hydro Electric Power Distribution
                    regionid: 1
                    shortname: North Scotland
        '500':
          description: 'internal_server_error: Internal Server Error response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /drivers/{driverId}/charges:
    get:
      tags:
        - drivers
      summary: retrieveCharges drivers
      description: Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn't present.
      operationId: drivers#retrieveCharges
      parameters:
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
        - name: driverId
          in: path
          description: UUID of the driver
          required: true
          schema:
            type: string
            description: UUID of the driver
            example: a06299a4-**************-b8ffb92f6b3e
            format: uuid
          example: e996b772-7ff9-4ab5-ab1c-c18326077b4d
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriversChargesResponse'
              example:
                data:
                  charges:
                    - charger:
                        door: A
                        id: veefil-602300633
                        name: Amoy-Reef
                        pluggedInAt: '2018-05-15T12:00:00+12:00'
                        pluggedInDuration: 3600
                        siteName: Ecclestone Court Car Park
                        type: home
                        unpluggedAt: '2018-05-15T12:00:00+12:00'
                      cost:
                        amount: 2356
                        currency: GBP
                      duration: 22245
                      endedAt: '2018-05-15T12:00:00+12:00'
                      energyTotal: 567.89
                      expensedTo:
                        id: 665130f3-dd26-46d8-9b50-eb1d69880539
                        name: Adept Power Solutions Ltd
                      generationEnergyTotal: 67.89
                      gridEnergyTotal: 500
                      id: a9775af6-da4f-4ef4-af71-9c7092823419
                      startedAt: '2018-05-15T12:00:00+12:00'
                    - charger:
                        door: A
                        id: veefil-602300633
                        name: Amoy-Reef
                        pluggedInAt: '2018-05-15T12:00:00+12:00'
                        pluggedInDuration: 3600
                        siteName: Ecclestone Court Car Park
                        type: home
                        unpluggedAt: '2018-05-15T12:00:00+12:00'
                      cost:
                        amount: 2356
                        currency: GBP
                      duration: 22245
                      endedAt: '2018-05-15T12:00:00+12:00'
                      energyTotal: 567.89
                      expensedTo:
                        id: 665130f3-dd26-46d8-9b50-eb1d69880539
                        name: Adept Power Solutions Ltd
                      generationEnergyTotal: 67.89
                      gridEnergyTotal: 500
                      id: a9775af6-da4f-4ef4-af71-9c7092823419
                      startedAt: '2018-05-15T12:00:00+12:00'
                    - charger:
                        door: A
                        id: veefil-602300633
                        name: Amoy-Reef
                        pluggedInAt: '2018-05-15T12:00:00+12:00'
                        pluggedInDuration: 3600
                        siteName: Ecclestone Court Car Park
                        type: home
                        unpluggedAt: '2018-05-15T12:00:00+12:00'
                      cost:
                        amount: 2356
                        currency: GBP
                      duration: 22245
                      endedAt: '2018-05-15T12:00:00+12:00'
                      energyTotal: 567.89
                      expensedTo:
                        id: 665130f3-dd26-46d8-9b50-eb1d69880539
                        name: Adept Power Solutions Ltd
                      generationEnergyTotal: 67.89
                      gridEnergyTotal: 500
                      id: a9775af6-da4f-4ef4-af71-9c7092823419
                      startedAt: '2018-05-15T12:00:00+12:00'
                  count: 22245
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /drivers/{driverId}/charges/{chargeId}:
    get:
      tags:
        - drivers
      summary: retrieveCharge drivers
      description: Retrieve charge details.
      operationId: drivers#retrieveCharge
      parameters:
        - name: driverId
          in: path
          description: ID for the driver
          required: true
          schema:
            type: string
            description: ID for the driver
            example: 6c6e0079-9d03-4986-b262-d3de63c7db3c
            format: uuid
          example: b69e8f0b-2ce4-4b4d-a3c4-0907b8e88a10
        - name: chargeId
          in: path
          description: Charge ID
          required: true
          schema:
            type: string
            description: Charge ID
            example: ebd3fd90-edd8-4c60-af62-12cf949d5ecf
            format: uuid
          example: 8d30bfc8-9171-458a-90e7-9bffb5719f21
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriversChargeResponse'
              example:
                data:
                  charger:
                    door: A
                    id: veefil-602300633
                    name: Amoy-Reef
                    pluggedInAt: '2018-05-15T12:00:00+12:00'
                    pluggedInDuration: 3600
                    siteName: Ecclestone Court Car Park
                    type: home
                    unpluggedAt: '2018-05-15T12:00:00+12:00'
                  cost:
                    amount: 2356
                    currency: GBP
                  duration: 22245
                  endedAt: '2018-05-15T12:00:00+12:00'
                  energyTotal: 567.89
                  expensedTo:
                    id: 665130f3-dd26-46d8-9b50-eb1d69880539
                    name: Adept Power Solutions Ltd
                  generationEnergyTotal: 67.89
                  gridEnergyTotal: 500
                  id: a9775af6-da4f-4ef4-af71-9c7092823419
                  startedAt: '2018-05-15T12:00:00+12:00'
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charge_not_found: Not Found response.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriversChargeNotFound'
              example:
                reason: Qui earum sint.
                status: 8658153353822577228
  /drivers/{driverId}/groups/{organisationId}/expenses:
    post:
      tags:
        - Driver charges
      summary: Submit driver expenses Driver charges
      description: Submitting a list of charges as expenses for a driver within a group.
      operationId: Driver charges#Submit driver expenses
      parameters:
        - name: driverId
          in: path
          description: UUID of the driver.
          required: true
          schema:
            type: string
            description: UUID of the driver.
            example: 7c5bb8ec-329f-4324-93fa-8f3cfb26f8c0
            format: uuid
          example: 7e97fe23-db07-42f3-a458-402d0b883316
        - name: organisationId
          in: path
          required: true
          schema:
            type: integer
            example: 234578
            format: int64
            minimum: 1
          example: 234578
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitDriverExpensesRequestBody'
            example:
              expenses:
                - chargeId: c7b9a78a-6008-47d1-843e-15dc585c30df
                - chargeId: c7b9a78a-6008-47d1-843e-15dc585c30df
      responses:
        '201':
          description: Created response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmittedExpenseResponse'
              example:
                expenses:
                  - chargeId: efc8b3e5-e8b7-4503-b077-8df02aa39ee0
                    id: 456
                  - chargeId: efc8b3e5-e8b7-4503-b077-8df02aa39ee0
                    id: 456
                  - chargeId: efc8b3e5-e8b7-4503-b077-8df02aa39ee0
                    id: 456
        '400':
          description: 'bad_request: Driver ID, Group ID or Expenses payload is malformed.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charge_not_found: charge not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriverNotFound'
              example:
                reason: Atque sequi amet debitis.
                status: 4012669829070911464
        '409':
          description: 'duplicate_charge_submission: duplicate charge submission'
  /drivers/{driverId}/organisations/{organisationId}/expenses:
    post:
      tags:
        - Driver charges
      summary: Create driver expenses Driver charges
      description: Submitting a list of charges as expenses for a driver within an organisation.
      operationId: Driver charges#Create driver expenses
      parameters:
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
            example: 978125
            format: int64
            minimum: 1
          example: 978125
        - name: organisationId
          in: path
          required: true
          schema:
            type: integer
            example: 234578
            format: int64
            minimum: 1
          example: 234578
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDriverExpensesRequestBody'
            example:
              expenses:
                - chargeId: 123
      responses:
        '201':
          description: Created response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedExpenseResponse'
              example:
                expenses:
                  - chargeId: 123
                    id: 456
                  - chargeId: 123
                    id: 456
        '400':
          description: 'bad_request: Driver ID, Organisation ID or Expenses payload is malformed.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charge_not_found: charge not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriverNotFound'
              example:
                reason: Quasi et sint assumenda enim aut porro.
                status: 6981787910613554951
        '409':
          description: 'duplicate_charge_submission: duplicate charge submission'
  /drivers/{driverId}/stats:
    get:
      tags:
        - drivers
      summary: retrieveStats drivers
      description: "Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn't present. "
      operationId: drivers#retrieveStats
      parameters:
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
        - name: interval
          in: query
          description: Time duration interval data should be provided in.
          allowEmptyValue: true
          schema:
            type: string
            description: Time duration interval data should be provided in.
            example: month
            enum:
              - day
              - week
              - month
          example: month
        - name: driverId
          in: path
          description: UUID of the driver
          required: true
          schema:
            type: string
            description: UUID of the driver
            example: 01017262-07b2-4e4b-b527-d9dc3f2c697b
            format: uuid
          example: 8eb775c5-5ce9-4ed4-9152-0f86a6948bc0
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriverStatsResponse'
              example:
                data:
                  intervals:
                    - from: '2022-10-12T00:00:00Z'
                      stats:
                        cost:
                          home:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          private:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          public:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          total:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                        duration:
                          home: 680
                          private: 170
                          public: 170
                          total: 850
                        energy:
                          home:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          private:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          public:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          total:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                      to: '2022-10-19T00:00:00Z'
                    - from: '2022-10-12T00:00:00Z'
                      stats:
                        cost:
                          home:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          private:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          public:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                          total:
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                            - amount: 2356
                              currency: GBP
                        duration:
                          home: 680
                          private: 170
                          public: 170
                          total: 850
                        energy:
                          home:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          private:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          public:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                          total:
                            generation: 12.5
                            grid: 123.4
                            total: 135.9
                      to: '2022-10-19T00:00:00Z'
                  summary:
                    cost:
                      home:
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                      private:
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                      public:
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                      total:
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                        - amount: 2356
                          currency: GBP
                    duration:
                      home: 680
                      private: 170
                      public: 170
                      total: 850
                    energy:
                      home:
                        generation: 12.5
                        grid: 123.4
                        total: 135.9
                      private:
                        generation: 12.5
                        grid: 123.4
                        total: 135.9
                      public:
                        generation: 12.5
                        grid: 123.4
                        total: 135.9
                      total:
                        generation: 12.5
                        grid: 123.4
                        total: 135.9
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /groups/{groupId}/charge-statistics:
    get:
      tags:
        - Charge Statistics
      summary: Group Charge Statistics Charge Statistics
      description: 'Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}'
      operationId: Charge Statistics#Group Charge Statistics
      parameters:
        - name: groupId
          in: path
          description: UUID of the group.
          required: true
          schema:
            type: string
            description: UUID of the group.
            example: 761cac69-035e-4939-87f3-9d29a734744a
            format: uuid
          example: 761cac69-035e-4939-87f3-9d29a734744a
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectiongroupChargeStatisticsResponse'
              example:
                data:
                  chargingDuration: 22245
                  co2Savings: 4325.62
                  energy:
                    claimedUsage: 243.89
                    cost: 324550
                    revenueGeneratingClaimedUsage: 43.5
                    totalUsage: 567.89
                    unclaimedUsage: 324.65
                  numberOfChargers: 3
                  numberOfCharges: 10
                  numberOfSites: 3
                  numberOfUsers: 2
                  revenueGenerated: 457600
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /groups/{groupId}/charge-statistics/{interval}:
    get:
      tags:
        - Charge Statistics
      summary: Group Usage Summaries Charge Statistics
      description: 'Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats'
      operationId: Charge Statistics#Group Usage Summaries
      parameters:
        - name: groupId
          in: path
          description: UUID of the group.
          required: true
          schema:
            type: string
            description: UUID of the group.
            example: acda5aaa-16de-4508-a211-ba2847d1d92e
            format: uuid
          example: 19fe91c7-e58c-4ecb-9abf-dbf17e9834e1
        - name: interval
          in: path
          description: Reporting interval
          required: true
          schema:
            type: string
            description: Reporting interval
            example: day
            enum:
              - day
              - week
              - month
          example: day
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2024-07-01'
            format: date
          example: '2024-07-01'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2024-07-30'
            format: date
          example: '2024-07-30'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'group_not_found: group not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupNotFound'
              example:
                reason: Non autem deserunt sapiente repellat.
                status: 2479935143840344746
  /groups/{groupId}/chargers/{chargerId}/charge-statistics/{interval}:
    get:
      tags:
        - Charge Statistics
      summary: Group and Charger Usage Summaries Charge Statistics
      description: 'Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats'
      operationId: Charge Statistics#Group and Charger Usage Summaries
      parameters:
        - name: groupId
          in: path
          description: UUID of the group.
          required: true
          schema:
            type: string
            description: UUID of the group.
            example: d92d869b-0f6f-4ec8-831b-7652519f21fe
            format: uuid
          example: 8ae5c8bc-3c2e-48bd-8e39-135f84edc396
        - name: chargerId
          in: path
          description: ID of the charger (equivalent to PPID).
          required: true
          schema:
            type: string
            description: ID of the charger (equivalent to PPID).
            example: PP-4567
            minLength: 1
          example: PP-4567
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2024-09-02'
            format: date
          example: '2024-09-02'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2024-09-02'
            format: date
          example: '2024-09-02'
        - name: interval
          in: path
          description: Reporting interval
          required: true
          schema:
            type: string
            description: Reporting interval
            example: day
            enum:
              - day
              - week
              - month
          example: day
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /groups/{groupId}/sites/{siteId}/charge-statistics/{interval}:
    get:
      tags:
        - Charge Statistics
      summary: Group and Site Usage Summaries Charge Statistics
      description: 'Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats'
      operationId: Charge Statistics#Group and Site Usage Summaries
      parameters:
        - name: groupId
          in: path
          description: UUID of the group.
          required: true
          schema:
            type: string
            description: UUID of the group.
            example: db2e5e19-e258-4d4f-876f-2c9bcf0dfc59
            format: uuid
          example: b57cd364-f409-4a63-96b7-529163f9353c
        - name: siteId
          in: path
          description: UUID of the site.
          required: true
          schema:
            type: string
            description: UUID of the site.
            example: 71562dbf-2c67-461b-a6d9-e0875e38b46f
            format: uuid
          example: 22080956-f04d-448e-9d7d-883f464d188c
        - name: interval
          in: path
          description: Reporting interval
          required: true
          schema:
            type: string
            description: Reporting interval
            example: day
            enum:
              - day
              - week
              - month
          example: day
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2024-07-01'
            format: date
          example: '2024-07-01'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2024-07-30'
            format: date
          example: '2024-07-30'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /groups/{groupId}/users/{userId}/charges:
    get:
      tags:
        - User Charges
      summary: Group And User Charges User Charges
      description: 'Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges'
      operationId: User Charges#Group And User Charges
      parameters:
        - name: groupId
          in: path
          description: The UUID of the group within which charges for the associated user have taken place
          required: true
          schema:
            type: string
            description: The UUID of the group within which charges for the associated user have taken place
            example: d5bafbb9-6750-4d85-951e-1af878d68409
            format: uuid
          example: 76f66a10-db6d-4713-a37a-416f0958d481
        - name: userId
          in: path
          description: The UUID of the user
          required: true
          schema:
            type: string
            description: The UUID of the user
            example: ba09ef2a-a7c7-487f-882b-************
            format: uuid
          example: 85abe587-89a7-4f74-967f-ac24e0583899
        - name: from
          in: query
          description: Query param filter from charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter from charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Query param filter to charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter to charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectionGroupAndUserChargesResponse'
              example:
                data:
                  charges:
                    - businessName: Pod Point - Software Team
                      chargeCost: 324550
                      chargerName: Nick-Gary
                      chargingDuration: 123
                      co2Savings: 4325.62
                      endTime: '2022-09-05T17:58:33Z'
                      energyUsage: 98.76
                      pluggedInDuration: 123
                      revenueGenerated: 457600
                      startTime: '2022-09-05T14:58:33Z'
                    - businessName: Pod Point - Software Team
                      chargeCost: 324550
                      chargerName: Nick-Gary
                      chargingDuration: 123
                      co2Savings: 4325.62
                      endTime: '2022-09-05T17:58:33Z'
                      energyUsage: 98.76
                      pluggedInDuration: 123
                      revenueGenerated: 457600
                      startTime: '2022-09-05T14:58:33Z'
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /link-user/{userId}/charger/{ppId}:
    post:
      tags:
        - Link user
      summary: Link user to home charger Link user
      operationId: Link user#Link user to home charger
      parameters:
        - name: ppId
          in: path
          description: PPID (PSL number) of a charger
          required: true
          schema:
            type: string
            description: PPID (PSL number) of a charger
            example: PP-12345
          example: PP-12345
        - name: userId
          in: path
          description: The UUID of the user
          required: true
          schema:
            type: string
            description: The UUID of the user
            example: f855b50d-e93c-4d55-b781-e56dca2e334f
            format: uuid
          example: 7bbfedb2-685c-4a75-b905-28d45778119c
      responses:
        '202':
          description: Accepted response.
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'user_not_found: User not found'
        '500':
          description: 'internal_server_error: Internal Server Error response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /organisations/{organisationId}/chargers/{locationId}/stats:
    get:
      tags:
        - usage
      summary: Usage by organisation and charger usage
      description: Charger usage between two inclusive dates
      operationId: usage#Usage by organisation and charger
      parameters:
        - name: organisationId
          in: path
          description: UUID of the organisation
          required: true
          schema:
            type: string
            description: UUID of the organisation
            example: e6d3040b-9aeb-46d4-8487-db4dce6d2d46
            format: uuid
          example: 7b5d86e9-d8c5-4906-89ae-a0b11d1058f9
        - name: locationId
          in: path
          description: Primary key of the charger location from podadmin
          required: true
          schema:
            type: integer
            description: Primary key of the charger location from podadmin
            example: 261671510
            format: int32
          example: 376470113
        - name: from
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-15'
            format: date
          example: '2023-06-15'
        - name: to
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-16'
            format: date
          example: '2023-06-16'
        - name: interval
          in: query
          description: Reporting interval
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Reporting interval
            example: month
            enum:
              - day
              - week
              - month
          example: month
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'charger_not_found: charger not found'
  /organisations/{organisationId}/drivers/{userId}/charges:
    get:
      tags:
        - Driver charges
      summary: Retrieve many Driver charges
      description: Retrieve charges for a driver
      operationId: Driver charges#Retrieve many
      parameters:
        - name: from
          in: query
          description: Query param filter from charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter from charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Query param filter to charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter to charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: organisationId
          in: path
          description: The UUID of the group within which charges for the associated user have taken place
          required: true
          schema:
            type: string
            description: The UUID of the group within which charges for the associated user have taken place
            example: 05bdd0fa-684a-4f99-8f65-7403e7dc02df
            format: uuid
          example: 6ee79d07-af62-4c0e-a162-52a634bdb8d0
        - name: userId
          in: path
          description: The UUID of the user
          required: true
          schema:
            type: string
            description: The UUID of the user
            example: e4d26992-0187-4c24-a4d2-27bbd240e49c
            format: uuid
          example: e684b69c-a1f3-4f3b-ab01-4b5ae9dbe429
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DriverChargesResponse'
              example:
                charges:
                  - businessName: Pod Point - Software Team
                    chargeCost: 324550
                    chargerName: Nick-Gary
                    chargingDuration: 123
                    co2Savings: 4325.62
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    pluggedInDuration: 123
                    revenueGenerated: 457600
                    startTime: '2022-09-05T14:58:33Z'
                  - businessName: Pod Point - Software Team
                    chargeCost: 324550
                    chargerName: Nick-Gary
                    chargingDuration: 123
                    co2Savings: 4325.62
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    pluggedInDuration: 123
                    revenueGenerated: 457600
                    startTime: '2022-09-05T14:58:33Z'
                  - businessName: Pod Point - Software Team
                    chargeCost: 324550
                    chargerName: Nick-Gary
                    chargingDuration: 123
                    co2Savings: 4325.62
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    pluggedInDuration: 123
                    revenueGenerated: 457600
                    startTime: '2022-09-05T14:58:33Z'
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'organisation_not_found: organisation not found'
  /organisations/{organisationId}/drivers/stats:
    post:
      tags:
        - Driver charges
      summary: Retrieve organisation drivers statistics Driver charges
      description: Retrieve charge statistics for given organisation drivers
      operationId: Driver charges#Retrieve organisation drivers statistics
      parameters:
        - name: from
          in: query
          description: Query param filter from charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter from charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Query param filter to charge endsAt datetime
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Query param filter to charge endsAt datetime
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: organisationId
          in: path
          description: The UUID of the group within which charges for the associated drivers have taken place
          required: true
          schema:
            type: string
            description: The UUID of the group within which charges for the associated drivers have taken place
            example: 63cb16d6-9c2a-4d0f-ace2-338aedbe520e
            format: uuid
          example: 04280ab0-b17f-4958-be23-69f7cef01d45
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveOrganisationDriversStatisticsRequestBody'
            example:
              driverIds:
                - 12d9423b-8a65-40b1-af24-062b41e1cedb
                - 648450d6-a670-4a60-8a1e-acb79eafe708
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganisationDriversChargeStatisticsResponse'
              example:
                charges:
                  - chargingDuration: 123
                    co2Savings: 4325.62
                    driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    numberOfCharges: 2048
                    pluggedInDuration: 123
                    revenueGenerated: 457600
                    totalCost: 324550
                    totalUsage: 567.89
                  - chargingDuration: 123
                    co2Savings: 4325.62
                    driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    numberOfCharges: 2048
                    pluggedInDuration: 123
                    revenueGenerated: 457600
                    totalCost: 324550
                    totalUsage: 567.89
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'organisation_not_found: organisation not found'
  /organisations/{organisationId}/fleet-usage:
    get:
      tags:
        - Organisation charges
      summary: Fleet usage by organisation Organisation charges
      description: This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.
      operationId: Organisation charges#Fleet usage by organisation
      parameters:
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: d4f6380d-34df-49e4-a986-18ca98a651e3
            format: uuid
          example: 6345deb4-569f-4957-8a59-************
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FleetUsageResponse'
              example:
                co2Savings: 4325.62
                numberOfDrivers: 367
                totalCharges:
                  home: 367
                  public: 367
                  total: 367
                totalUsage:
                  home: 12.34
                  public: 56.78
                  total: 12.34
        '400':
          description: 'bad_request: Returns bad request when org ID is malformed'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'organisation_not_found: organisation not found'
  /organisations/{organisationId}/process-charges:
    post:
      tags:
        - Organisation charges
      summary: Mark submitted charges as processed Organisation charges
      description: Mark submitted charges as processed
      operationId: Organisation charges#Mark submitted charges as processed
      parameters:
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: f138a84e-ac78-4f75-a378-1c0741a9eeae
            format: uuid
          example: 68d2be94-a4c0-4882-b2a2-a89b4fb89f74
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkSubmittedChargesAsProcessedRequestBody'
            example:
              approverId: 4138466440844227861
              submittedChargeIds:
                - 12375
                - 17456
      responses:
        '200':
          description: OK response.
        '400':
          description: 'charge_not_found_in_organisation: Bad Request response.'
        '404':
          description: 'organisation_not_found: Not Found response.'
  /organisations/{organisationId}/sites/{siteId}/stats:
    get:
      tags:
        - usage
      summary: Usage by organisation and site usage
      description: Site usage between two inclusive dates
      operationId: usage#Usage by organisation and site
      parameters:
        - name: organisationId
          in: path
          description: UUID of the organisation
          required: true
          schema:
            type: string
            description: UUID of the organisation
            example: af3acb45-45c5-4bcc-8c0e-4c889a73c3dc
            format: uuid
          example: bc09ce0c-469c-4266-95ee-37b623938181
        - name: from
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-07'
            format: date
          example: '2023-06-07'
        - name: to
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-08'
            format: date
          example: '2023-06-08'
        - name: interval
          in: query
          description: Reporting interval
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Reporting interval
            example: month
            enum:
              - day
              - week
              - month
          example: month
        - name: siteId
          in: path
          description: Site ID - primary key of the podadmin pod_addresses table
          required: true
          schema:
            type: integer
            description: Site ID - primary key of the podadmin pod_addresses table
            example: 2119165467
            format: int32
          example: 1712561239
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'site_not_found: site not found'
  /organisations/{organisationId}/stats:
    get:
      tags:
        - usage
      summary: Usage by organisation usage
      description: Organisation usage between two inclusive dates
      operationId: usage#Usage by organisation
      parameters:
        - name: organisationId
          in: path
          description: UUID of the organisation
          required: true
          schema:
            type: string
            description: UUID of the organisation
            example: 73d615c9-e14c-4231-8aa2-8876556d8bf4
            format: uuid
          example: bfa805ae-47ae-4af6-9ffd-949b2008a5c5
        - name: from
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-07'
            format: date
          example: '2023-06-07'
        - name: to
          in: query
          allowEmptyValue: true
          required: true
          schema:
            type: string
            example: '2023-06-08'
            format: date
          example: '2023-06-08'
        - name: interval
          in: query
          description: Reporting interval
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Reporting interval
            example: month
            enum:
              - day
              - week
              - month
          example: month
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageResponse'
              example:
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
                usage:
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
                  - co2Savings: 2469.32
                    cost: 135668
                    intervalStartDate: '2023-06-07'
                    revenueGenerated: 16
                    totalUsage: 669.2
        '400':
          description: 'bad_request: Bad Request response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'organisation_not_found: organisation not found'
  /organisations/{organisationId}/submitted-charges:
    get:
      tags:
        - Organisation charges
      summary: Expenses by organisation Organisation charges
      description: Get all expensable charges per organisation between two dates (maximum 1 month).
      operationId: Organisation charges#Expenses by organisation
      parameters:
        - name: status
          in: query
          description: Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
          allowEmptyValue: true
          schema:
            type: string
            description: Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
            example: NEW
            enum:
              - NEW
              - PROCESSED
          example: NEW
        - name: from
          in: query
          description: Charges expensed from and including this date will be returned.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Charges expensed from and including this date will be returned.
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Charges expensed up to and not including this date will be returned.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Charges expensed up to and not including this date will be returned.
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: 16eb701f-79f3-4a84-afa0-e7c2fd84e892
            format: uuid
          example: b894acb0-b309-4d43-99de-f3b995593e58
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganisationChargesResponse'
              example:
                expensableCharges:
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2022-09-30T15:35:00Z'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2022-09-30T16:46:00Z'
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2022-09-30T15:35:00Z'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2022-09-30T16:46:00Z'
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2022-09-30T15:35:00Z'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2022-09-30T16:46:00Z'
        '400':
          description: 'bad_request: Returns bad request when org ID is malformed'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/TimeRangeOutOfBounds'
        '404':
          description: 'organisation_not_found: Organisation not found'
  /organisations/{organisationId}/submitted-charges/{driverId}:
    get:
      tags:
        - Organisation charges
      summary: Submitted charges for driver Organisation charges
      description: Retrieve submitted charges for a driver
      operationId: Organisation charges#Submitted charges for driver
      parameters:
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: 79341107-43ff-4df1-a830-92ab1cb9b165
            format: uuid
          example: de2b0a77-7a49-4c19-b559-c002ea4ffe51
        - name: driverId
          in: path
          required: true
          schema:
            type: integer
            example: 9166444393796434264
            format: int64
            minimum: 1
          example: 7294682775603339443
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmittedChargesResponse'
              example:
                driver:
                  email: <EMAIL>
                  firstName: Max
                  fullName: Max Verstappen
                  id: 123
                  lastName: Verstappen
                submittedCharges:
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    duration: 123
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2018-05-15T12:00:00+12:00'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    status: NEW
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2018-05-15T12:00:00+12:00'
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    duration: 123
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2018-05-15T12:00:00+12:00'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    status: NEW
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2018-05-15T12:00:00+12:00'
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    duration: 123
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2018-05-15T12:00:00+12:00'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    status: NEW
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2018-05-15T12:00:00+12:00'
                  - chargeCost: 123
                    chargerName: Kent-Jake
                    duration: 123
                    endTime: '2022-09-05T17:58:33Z'
                    energyUsage: 98.76
                    id: 123
                    location:
                      address:
                        country: United Kingdom
                        line1: 234 Banner St
                        line2: Westminster
                        postcode: EC1Y 8QE
                        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                        town: London
                      id: 456
                      locationType: home
                    pluggedInAt: '2018-05-15T12:00:00+12:00'
                    processedByFullName: John Smith
                    processedTime: '2022-09-05T14:58:33Z'
                    startTime: '2022-09-05T14:58:33Z'
                    status: NEW
                    submittedTime: '2022-09-05T17:58:33Z'
                    unpluggedAt: '2018-05-15T12:00:00+12:00'
                totalCost:
                  home: 12300
                  public: 45670
                totalUsage:
                  home: 12.34
                  public: 56.78
                  total: 12.34
        '400':
          description: 'bad_request: Returns bad request when org ID is malformed'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: 'driver_not_found: driver not found'
          content:
            application/json:
              example:
                reason: Dolorem voluptatem enim soluta optio id expedita.
                status: 1646520909202294823
  /organisations/{organisationId}/submitted-charges/drivers:
    get:
      tags:
        - Organisation charges
      summary: Expenses by organisation, grouped by driver Organisation charges
      description: Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.
      operationId: Organisation charges#Expenses by organisation, grouped by driver
      parameters:
        - name: status
          in: query
          description: Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
          allowEmptyValue: true
          schema:
            type: string
            description: Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.
            example: NEW
            enum:
              - NEW
              - PROCESSED
          example: NEW
        - name: from
          in: query
          description: Charges expensed from and including this date will be returned.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Charges expensed from and including this date will be returned.
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: Charges expensed up to and not including this date will be returned.
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Charges expensed up to and not including this date will be returned.
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
        - name: organisationId
          in: path
          required: true
          schema:
            type: string
            example: bed7b87b-bb57-4d30-9d13-b7e563cc308e
            format: uuid
          example: 759a557f-1209-4c63-ba99-cc889c53b5b2
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganisationChargesDriverSummaryResponse'
              example:
                driverExpensableChargeSummaries:
                  - driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    submittedChargeIds:
                      - 1
                      - 2
                      - 4
                      - 42
                      - 54
                    totalCharges: 1
                    totalCost:
                      home: 12300
                      public: 45670
                    totalUsage:
                      home: 12.34
                      public: 56.78
                      total: 12.34
                  - driver:
                      email: <EMAIL>
                      firstName: Max
                      fullName: Max Verstappen
                      id: 123
                      lastName: Verstappen
                    submittedChargeIds:
                      - 1
                      - 2
                      - 4
                      - 42
                      - 54
                    totalCharges: 1
                    totalCost:
                      home: 12300
                      public: 45670
                    totalUsage:
                      home: 12.34
                      public: 56.78
                      total: 12.34
        '400':
          description: 'bad_request: Returns bad request when org ID is malformed'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/TimeRangeOutOfBounds'
        '404':
          description: 'organisation_not_found: Organisation not found'
  /regional/intensity/{from}/fw48h/regionid/{regionId}:
    get:
      tags:
        - Carbon intensity
      summary: Retrieve regional forecast 48 hours from date Carbon intensity
      description: Retrieve half-hourly forecast data 48 hours from provided date.
      operationId: Carbon intensity#Retrieve regional forecast 48 hours from date
      parameters:
        - name: from
          in: path
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          required: true
          schema:
            type: string
            description: Datetime is in ISO8601 and RFC3339 compliant format.
            example: '2018-05-15T12:00:00Z'
          example: '2018-05-15T12:00:00Z'
        - name: regionId
          in: path
          description: 'Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.'
          required: true
          schema:
            type: integer
            description: 'Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.'
            example: 3720436211076503217
            format: int64
          example: 7550539559113488883
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Forecast'
              example:
                data:
                  data:
                    - from: 2018-01-20T12:00Z
                      generationmix:
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                      intensity:
                        forecast: 991886998364910352
                        index: Non autem ex alias iste ad voluptatum.
                      to: 2018-01-20T12:00Z
                    - from: 2018-01-20T12:00Z
                      generationmix:
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                      intensity:
                        forecast: 991886998364910352
                        index: Non autem ex alias iste ad voluptatum.
                      to: 2018-01-20T12:00Z
                    - from: 2018-01-20T12:00Z
                      generationmix:
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                      intensity:
                        forecast: 991886998364910352
                        index: Non autem ex alias iste ad voluptatum.
                      to: 2018-01-20T12:00Z
                    - from: 2018-01-20T12:00Z
                      generationmix:
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                        - fuel: Totam doloremque iste.
                          perc: 0.48824632
                      intensity:
                        forecast: 991886998364910352
                        index: Non autem ex alias iste ad voluptatum.
                      to: 2018-01-20T12:00Z
                  dnoregion: Tempore rerum hic suscipit temporibus.
                  regionid: 3791299769356288913
                  shortname: Est dignissimos qui voluptatem earum.
        '400':
          description: 'bad_request: invalid timestamp'
        '500':
          description: 'internal_server_error: Internal Server Error response.'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /sites:
    get:
      tags:
        - sites
      summary: retrieveChargeStatsGroupedBySite sites
      description: Retrieve charge information by site.
      operationId: sites#retrieveChargeStatsGroupedBySite
      parameters:
        - name: from
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive start date eg: 2022-01-01'
            example: '2022-10-12'
            format: date
          example: '2022-10-12'
        - name: to
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: 'Statistics report inclusive end date eg: 2022-01-31'
            example: '2022-10-19'
            format: date
          example: '2022-10-19'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SiteStatsResponse'
              example:
                count: 650
                data:
                  - groupId: e1f2dc78-6616-4200-bed9-f61a78e32c9d
                    groupName: In nihil voluptas rerum esse necessitatibus.
                    id: fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2
                    name: Voluptatum ab est amet omnis nisi corrupti.
                    revenueGenerated: 16
                    totalEnergy: 567.89
                  - groupId: e1f2dc78-6616-4200-bed9-f61a78e32c9d
                    groupName: In nihil voluptas rerum esse necessitatibus.
                    id: fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2
                    name: Voluptatum ab est amet omnis nisi corrupti.
                    revenueGenerated: 16
                    totalEnergy: 567.89
                  - groupId: e1f2dc78-6616-4200-bed9-f61a78e32c9d
                    groupName: In nihil voluptas rerum esse necessitatibus.
                    id: fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2
                    name: Voluptatum ab est amet omnis nisi corrupti.
                    revenueGenerated: 16
                    totalEnergy: 567.89
                  - groupId: e1f2dc78-6616-4200-bed9-f61a78e32c9d
                    groupName: In nihil voluptas rerum esse necessitatibus.
                    id: fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2
                    name: Voluptatum ab est amet omnis nisi corrupti.
                    revenueGenerated: 16
                    totalEnergy: 567.89
                from: '2022-10-12'
                to: '2022-10-19'
        '400':
          description: 'bad_request: Returns bad request when date is invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
  /sites/{siteId}/charge-statistics:
    get:
      tags:
        - Charge Statistics
      summary: Site Charge Statistics Charge Statistics
      description: 'Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}'
      operationId: Charge Statistics#Site Charge Statistics
      parameters:
        - name: siteId
          in: path
          description: UUID of the site.
          required: true
          schema:
            type: string
            description: UUID of the site.
            example: 761cac69-035e-4939-87f3-9d29a734744a
            format: uuid
          example: 761cac69-035e-4939-87f3-9d29a734744a
        - name: from
          in: query
          description: Inclusive from date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive from date used for filtering on unpluggedAt
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
        - name: to
          in: query
          description: Inclusive to date used for filtering on unpluggedAt
          allowEmptyValue: true
          required: true
          schema:
            type: string
            description: Inclusive to date used for filtering on unpluggedAt
            example: '2006-01-02'
            format: date
          example: '2006-01-02'
      responses:
        '200':
          description: OK response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectionsiteChargeStatisticsResponse'
              example:
                data:
                  chargingDuration: 22245
                  co2Savings: 4325.62
                  energy:
                    claimedUsage: 243.89
                    cost: 324550
                    revenueGeneratingClaimedUsage: 43.5
                    totalUsage: 567.89
                    unclaimedUsage: 324.65
                  numberOfChargers: 3
                  numberOfCharges: 10
                  numberOfUsers: 2
                  revenueGenerated: 457600
                meta:
                  params:
                    Enim blanditiis ex omnis.: Deserunt nemo placeat.
                    Impedit facilis debitis esse sit.: Deserunt molestiae.
                    Quo itaque molestiae inventore.: Ipsum quia amet aut possimus consequatur.
        '400':
          description: 'bad_request: Returns bad request when request parameters are invalid'
          content:
            application/vnd.goa.error:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    AggregateCostCorrectedResponse:
      type: object
      properties:
        cost:
          type: integer
          description: New cost of the charge in lowest denomination of its currency (pence, euro cents).
          example: 16
          format: int64
        id:
          type: string
          description: UUID of the charge.
          example: f1687320-8efc-4233-ba0c-d85ae6b0f65d
          format: uuid
        submittedBy:
          type: string
          description: Who has submitted the correction request.
          example: John Doe
      example:
        cost: 16
        id: ebe2aada-b053-42be-bc84-b6325d427117
        submittedBy: John Doe
      required:
        - id
        - cost
    AggregateSettlementAmountCorrectedResponse:
      type: object
      properties:
        id:
          type: string
          description: UUID of the charge.
          example: c682cd91-074d-45ee-b0d5-e0daf20e28ab
          format: uuid
        settlementAmount:
          type: integer
          description: New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).
          example: 16
          format: int64
        submittedBy:
          type: string
          description: Who has submitted the correction request.
          example: John Doe
      example:
        id: 6f31378a-591b-41bc-b72e-dc75571b155b
        settlementAmount: 16
        submittedBy: John Doe
      required:
        - id
        - settlementAmount
    ApproverNotFound:
      type: object
      description: ApproverNotFound is the error returned when there is no approver found for the given approver ID.
      example: {}
    AuthoriseChargeRequestBody:
      type: object
      properties:
        chargerId:
          type: string
          description: PPID (PSL number) of a charger
          example: PP-12345
        door:
          type: string
          description: Charger door. A, B or C
          example: A
          enum:
            - A
            - B
            - C
        token:
          type: string
          description: RFID card token, OCPI token, or Billing event ID for guest authorisation.
          example: 0DA46GKEFP3
      example:
        chargerId: PP-12345
        door: A
        token: 0DA46GKEFP3
      required:
        - token
        - chargerId
        - door
    AuthoriserNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Incidunt architecto esse et.
        status:
          type: integer
          example: 6107026220835786154
          format: int64
      description: No authoriser can be found for the given identifier (rfid/user/other).
      example:
        reason: Dolorum ullam numquam beatae.
        status: ******************
      required:
        - reason
        - status
    Balance:
      type: object
      properties:
        actual:
          type: integer
          description: Actual user balance (or guest pre-authorised amount) in pence/cents/øre
          example: 75
          format: int64
        currency:
          type: string
          description: Balance currency code
          example: GBP
          enum:
            - GBP
            - EUR
            - NOK
        minimum:
          type: integer
          description: Minimum balance required to start charge in pence/cents/øre
          example: 50
          format: int64
      example:
        actual: 75
        currency: GBP
        minimum: 50
    Breakdown:
      type: object
      properties:
        generation:
          type: number
          description: Generation energy in kWh
          example: 12.5
          format: double
        grid:
          type: number
          description: Grid energy in kWh
          example: 123.4
          format: double
        total:
          type: number
          description: Total energy in kWh
          example: 135.9
          format: double
      example:
        generation: 12.5
        grid: 123.4
        total: 135.9
      required:
        - total
    Charge:
      type: object
      properties:
        charger:
          $ref: '#/components/schemas/Charger'
        cost:
          $ref: '#/components/schemas/Money'
        duration:
          type: integer
          description: Duration of charging in seconds.
          example: 22245
          format: int64
        endedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
        energyTotal:
          type: number
          description: Energy used in kWh.
          example: 567.89
          format: double
        expensedTo:
          $ref: '#/components/schemas/ExpensedTo'
        generationEnergyTotal:
          type: number
          description: Energy used in kWh that was generated e.g. Solar.
          example: 67.89
          format: double
        gridEnergyTotal:
          type: number
          description: Energy used in kWh that is imported from the grid.
          example: 500
          format: double
        id:
          type: string
          description: Charges unique identifier.
          example: 924c1a5d-5cdf-468e-bb80-a92947bd8d81
          format: uuid
        startedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
      example:
        charger:
          door: A
          id: veefil-602300633
          name: Amoy-Reef
          pluggedInAt: '2018-05-15T12:00:00+12:00'
          pluggedInDuration: 3600
          siteName: Ecclestone Court Car Park
          type: home
          unpluggedAt: '2018-05-15T12:00:00+12:00'
        cost:
          amount: 2356
          currency: GBP
        duration: 22245
        endedAt: '2018-05-15T12:00:00+12:00'
        energyTotal: 567.89
        expensedTo:
          id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
          name: Adept Power Solutions Ltd
        generationEnergyTotal: 67.89
        gridEnergyTotal: 500
        id: 731b0fe4-9ef0-443f-af6f-e7ec1feba1fa
        startedAt: '2018-05-15T12:00:00+12:00'
      required:
        - id
        - charger
    ChargeAggregateNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Excepturi nesciunt sunt nulla labore quo.
        status:
          type: integer
          example: 5314270994321633479
          format: int64
      description: Charge aggregate cannot be found by its uuid.
      example:
        reason: Possimus quis voluptatem est quia occaecati laborum.
        status: 7594904778576349979
      required:
        - reason
        - status
    ChargeAuthorisationResponse:
      type: object
      properties:
        authorised:
          type: boolean
          description: Whether the charge is authorised.
          example: false
        id:
          type: string
          description: UUID representing the charge authorisation, present if authorisation was successful.
          example: 38a7908d-01ce-49ba-9863-f057b68ac1dc
          format: uuid
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        authorised: true
        id: 04b7320d-c29e-4182-8675-1d6302f00e23
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - authorised
        - meta
    ChargeEnergySummary:
      type: object
      properties:
        claimedUsage:
          type: number
          description: Energy used this period in kWh, filtering out any unclaimed charges.
          example: 243.89
          format: double
        cost:
          type: integer
          description: Energy cost in pence.
          example: 324550
          format: int64
        revenueGeneratingClaimedUsage:
          type: number
          description: Energy used this period in kWh by claimed charges with a positive revenue.
          example: 43.5
          format: double
        totalUsage:
          type: number
          description: Energy used this period in kWh.
          example: 567.89
          format: double
        unclaimedUsage:
          type: number
          description: Energy used this period in kWh, filtering out any claimed charges.
          example: 324.65
          format: double
      example:
        claimedUsage: 243.89
        cost: 324550
        revenueGeneratingClaimedUsage: 43.5
        totalUsage: 567.89
        unclaimedUsage: 324.65
      required:
        - totalUsage
        - claimedUsage
        - revenueGeneratingClaimedUsage
        - unclaimedUsage
        - cost
    ChargeExpensed:
      type: object
      properties:
        reason:
          type: string
          example: Fugit quidem.
        status:
          type: integer
          example: 7245431305204423728
          format: int64
      description: The cost of an already expensed charge cannot be updated.
      example:
        reason: Quo vero temporibus labore culpa dolor ducimus.
        status: 739075535040998643
      required:
        - reason
        - status
    ChargeNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Et dolores voluptatem in quia sequi.
        status:
          type: integer
          example: 2090958392058155720
          format: int64
      description: ChargeNotFound is the error returned when there is no charge found for the given charge ID.
      example:
        reason: Harum voluptate ut ex voluptatem nemo ut.
        status: 3481799398561556138
      required:
        - reason
        - status
    ChargeNotFoundInOrganisation:
      type: object
      description: ChargeNotFoundInOrganisation is the error returned when there is no charge found for the given charge ID within an organisation.
      example: {}
    Charger:
      type: object
      properties:
        door:
          type: string
          description: Charger door used.
          example: A
        id:
          type: string
          description: Chargers unique identifier.
          example: veefil-602300633
        name:
          type: string
          description: Chargers user-friendly name.
          example: Amoy-Reef
        pluggedInAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
        pluggedInDuration:
          type: integer
          description: Duration a charger has been in use in seconds.
          example: 3600
          format: int64
        siteName:
          type: string
          description: Name of the site where the charger is located.
          example: Ecclestone Court Car Park
        type:
          type: string
          description: Type of charger.
          example: home
          enum:
            - public
            - private
            - home
        unpluggedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
      example:
        door: A
        id: veefil-602300633
        name: Amoy-Reef
        pluggedInAt: '2018-05-15T12:00:00+12:00'
        pluggedInDuration: 3600
        siteName: Ecclestone Court Car Park
        type: home
        unpluggedAt: '2018-05-15T12:00:00+12:00'
      required:
        - id
        - door
        - type
    ChargerChargeStatistics:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: sum of charging duration in seconds
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/EnergyStatistics'
        numberOfCharges:
          type: integer
          description: count of distinct charge uuids
          example: 10
          format: int64
        numberOfUsers:
          type: integer
          description: count of distinct user ids
          example: 2
          format: int64
        revenueGenerated:
          type: integer
          description: sum of settlement amount in pence
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfCharges: 10
        numberOfUsers: 2
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - numberOfUsers
        - chargingDuration
        - revenueGenerated
        - co2Savings
        - energy
    ChargerLimitNotFound:
      type: object
      description: ChargerLimitNotFound is the error returned when a charger location/authoriser cannot be found by its ppid/authoriser uuid.
      example: {}
    ChargerLocationNotFound:
      type: object
      description: ChargerLocationNotFound is the error returned when a charger location cannot be found by its ppid.
      example: {}
    ChargerNotFound:
      type: object
      description: ChargerNotFound is the error returned when there is no charger found for a given {locationId}.
      example: {}
    ChargerPpidNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Sapiente voluptate et non ducimus sint.
        status:
          type: integer
          example: 7222421359006113743
          format: int64
      description: ChargerPpidNotFound is the error returned when a charger location cannot be found by its ppid.
      example:
        reason: Dicta praesentium mollitia repellendus quia quibusdam dolores.
        status: 1365167895320577558
      required:
        - reason
        - status
    ChargerRegionNotFound:
      type: object
      description: ChargerRegionNotFound is the error returned when a suitable DNO region cannot be found for a charger.
      example: {}
    Chargerchargessummary:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: Total duration of charging in seconds.
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/ChargeEnergySummary'
        numberOfCharges:
          type: integer
          description: Total number of charges.
          example: 2048
          format: int64
        numberOfDrivers:
          type: integer
          description: Total number of unique drivers.
          example: 64
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfCharges: 2048
        numberOfDrivers: 64
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - numberOfDrivers
        - chargingDuration
        - revenueGenerated
        - co2Savings
    Charges:
      type: object
      properties:
        charges:
          type: array
          items:
            $ref: '#/components/schemas/Charge'
          description: Charge data for driver.
          example:
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
        count:
          type: integer
          description: Count of charges returned.
          example: 22245
          format: int64
      example:
        charges:
          - charger:
              door: A
              id: veefil-602300633
              name: Amoy-Reef
              pluggedInAt: '2018-05-15T12:00:00+12:00'
              pluggedInDuration: 3600
              siteName: Ecclestone Court Car Park
              type: home
              unpluggedAt: '2018-05-15T12:00:00+12:00'
            cost:
              amount: 2356
              currency: GBP
            duration: 22245
            endedAt: '2018-05-15T12:00:00+12:00'
            energyTotal: 567.89
            expensedTo:
              id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
              name: Adept Power Solutions Ltd
            generationEnergyTotal: 67.89
            gridEnergyTotal: 500
            id: 0d4c7438-d837-44ad-a96d-41c7fc616959
            startedAt: '2018-05-15T12:00:00+12:00'
          - charger:
              door: A
              id: veefil-602300633
              name: Amoy-Reef
              pluggedInAt: '2018-05-15T12:00:00+12:00'
              pluggedInDuration: 3600
              siteName: Ecclestone Court Car Park
              type: home
              unpluggedAt: '2018-05-15T12:00:00+12:00'
            cost:
              amount: 2356
              currency: GBP
            duration: 22245
            endedAt: '2018-05-15T12:00:00+12:00'
            energyTotal: 567.89
            expensedTo:
              id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
              name: Adept Power Solutions Ltd
            generationEnergyTotal: 67.89
            gridEnergyTotal: 500
            id: 0d4c7438-d837-44ad-a96d-41c7fc616959
            startedAt: '2018-05-15T12:00:00+12:00'
          - charger:
              door: A
              id: veefil-602300633
              name: Amoy-Reef
              pluggedInAt: '2018-05-15T12:00:00+12:00'
              pluggedInDuration: 3600
              siteName: Ecclestone Court Car Park
              type: home
              unpluggedAt: '2018-05-15T12:00:00+12:00'
            cost:
              amount: 2356
              currency: GBP
            duration: 22245
            endedAt: '2018-05-15T12:00:00+12:00'
            energyTotal: 567.89
            expensedTo:
              id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
              name: Adept Power Solutions Ltd
            generationEnergyTotal: 67.89
            gridEnergyTotal: 500
            id: 0d4c7438-d837-44ad-a96d-41c7fc616959
            startedAt: '2018-05-15T12:00:00+12:00'
        count: 22245
      required:
        - count
        - charges
    Charges2:
      type: object
      properties:
        chargeDurationTotal:
          type: integer
          description: Duration of charging in seconds.
          example: 22245
          format: int64
        chargerId:
          type: string
          description: Charger ID
          example: PSL-0001
        chargerName:
          type: string
          description: Charger name
          example: Nobis sit corporis suscipit ut.
        co2Avoided:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        confirmed:
          type: boolean
          description: Charge is confirmed or not. Identified by whether the authoriserID is present or not.
          example: true
        door:
          type: string
          description: Charger door used.
          example: A
        driverIDs:
          type: array
          items:
            type: string
            example: Provident hic occaecati.
          description: Driver ID
          example:
            - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
            - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
        endedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        energyCost:
          type: integer
          description: Cost of energy used in pence
          example: 32
          format: int64
        energyTotal:
          type: number
          description: Energy used in kWh.
          example: 567.89
          format: double
        generationEnergyTotal:
          type: number
          description: Generation energy used in kWh.
          example: 35.44
          format: double
        gridEnergyTotal:
          type: number
          description: Grid energy used in kWh.
          example: 532.45
          format: double
        id:
          type: string
          description: UUID of the charge
          example: 5588483f-7125-414a-887f-6fdc37911182
        pluggedInAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        siteName:
          type: string
          description: Site name
          example: Fugiat soluta non et.
        startedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        unpluggedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
      example:
        chargeDurationTotal: 22245
        chargerId: PSL-0001
        chargerName: Veritatis ut esse est.
        co2Avoided: 4325.62
        confirmed: true
        door: A
        driverIDs:
          - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
          - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
        endedAt: '2018-05-15T12:00:00Z'
        energyCost: 32
        energyTotal: 567.89
        generationEnergyTotal: 35.44
        gridEnergyTotal: 532.45
        id: 5588483f-7125-414a-887f-6fdc37911182
        pluggedInAt: '2018-05-15T12:00:00Z'
        revenueGenerated: 457600
        siteName: Nihil sed.
        startedAt: '2018-05-15T12:00:00Z'
        unpluggedAt: '2018-05-15T12:00:00Z'
      required:
        - id
        - siteName
        - chargerId
        - chargerName
        - pluggedInAt
        - unpluggedAt
        - startedAt
        - endedAt
        - energyTotal
        - generationEnergyTotal
        - gridEnergyTotal
        - door
        - driverIDs
        - revenueGenerated
        - chargeDurationTotal
        - energyCost
        - co2Avoided
        - confirmed
    Charges3:
      type: object
      properties:
        chargeDurationTotal:
          type: integer
          description: Duration of charging in seconds.
          example: 22245
          format: int64
        chargerId:
          type: string
          description: Charger ID
          example: PSL-0001
        chargerName:
          type: string
          description: Charger name
          example: Dolore eveniet sed.
        co2Avoided:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        confirmed:
          type: boolean
          description: Charge is confirmed or not. Identified by whether the authoriserID is present or not.
          example: true
        door:
          type: string
          description: Charger door used.
          example: A
        driverIDs:
          type: array
          items:
            type: string
            example: Amet voluptates.
          description: Driver ID
          example:
            - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
            - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
        endedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        energyCost:
          type: integer
          description: Cost of energy used in pence
          example: 32
          format: int64
        energyTotal:
          type: number
          description: Energy used in kWh.
          example: 567.89
          format: double
        generationEnergyTotal:
          type: number
          description: Generation energy used in kWh.
          example: 35.44
          format: double
        gridEnergyTotal:
          type: number
          description: Grid energy used in kWh.
          example: 532.45
          format: double
        id:
          type: string
          description: UUID of the charge
          example: 5588483f-7125-414a-887f-6fdc37911182
        pluggedInAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        siteName:
          type: string
          description: Site name
          example: Quam non rerum debitis ipsam perspiciatis nam.
        startedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
        unpluggedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format.
          example: '2018-05-15T12:00:00Z'
      example:
        chargeDurationTotal: 22245
        chargerId: PSL-0001
        chargerName: Illum quisquam omnis voluptatem omnis reprehenderit porro.
        co2Avoided: 4325.62
        confirmed: true
        door: A
        driverIDs:
          - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
          - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
        endedAt: '2018-05-15T12:00:00Z'
        energyCost: 32
        energyTotal: 567.89
        generationEnergyTotal: 35.44
        gridEnergyTotal: 532.45
        id: 5588483f-7125-414a-887f-6fdc37911182
        pluggedInAt: '2018-05-15T12:00:00Z'
        revenueGenerated: 457600
        siteName: Corporis deleniti et vitae laborum facilis culpa.
        startedAt: '2018-05-15T12:00:00Z'
        unpluggedAt: '2018-05-15T12:00:00Z'
      required:
        - id
        - siteName
        - chargerId
        - chargerName
        - pluggedInAt
        - unpluggedAt
        - startedAt
        - endedAt
        - energyTotal
        - generationEnergyTotal
        - gridEnergyTotal
        - door
        - driverIDs
        - revenueGenerated
        - chargeDurationTotal
        - energyCost
        - co2Avoided
        - confirmed
    ChargesLimitResponse:
      type: object
      properties:
        allowed:
          type: boolean
          description: Indicator of whether charge is allowed
          example: true
        balance:
          $ref: '#/components/schemas/Balance'
        limits:
          type: array
          items:
            $ref: '#/components/schemas/Limit'
          example:
            - amount: 1.23
              type: energy
              unit: kWh
            - amount: 1.23
              type: energy
              unit: kWh
      description: Information about charge allowance and related limits
      example:
        allowed: true
        balance:
          actual: 75
          currency: GBP
          minimum: 50
        limits:
          - amount: 1.23
            type: energy
            unit: kWh
          - amount: 1.23
            type: energy
            unit: kWh
      required:
        - allowed
        - balance
    CorrectEnergyCostRequestBody:
      type: object
      properties:
        cost:
          type: integer
          description: New cost of the charge in lowest denomination of its currency (pence, euro cents).
          example: 16
          format: int64
          minimum: 1
        submittedBy:
          type: string
          description: Who has submitted the correction request.
          example: John Doe
      example:
        cost: 16
        submittedBy: John Doe
      required:
        - cost
    CorrectSettlementAmountRequestBody:
      type: object
      properties:
        settlementAmount:
          type: integer
          description: New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).
          example: 16
          format: int64
          minimum: 1
        submittedBy:
          type: string
          description: Who has submitted the correction request.
          example: John Doe
      example:
        settlementAmount: 16
        submittedBy: John Doe
      required:
        - settlementAmount
    Cost:
      type: object
      properties:
        home:
          type: array
          items:
            $ref: '#/components/schemas/MoneyInt64'
          description: Total cost of all charges at home chargers in pence/cent/ore
          example:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
        private:
          type: array
          items:
            $ref: '#/components/schemas/MoneyInt64'
          description: Total cost of all chargers at private chargers in pence/cent/ore
          example:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
        public:
          type: array
          items:
            $ref: '#/components/schemas/MoneyInt64'
          description: Total cost of all chargers at public chargers in pence/cent/ore
          example:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
        total:
          type: array
          items:
            $ref: '#/components/schemas/MoneyInt64'
          description: Total cost of all charges in pence/cent/ore
          example:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
      example:
        home:
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
        private:
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
        public:
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
        total:
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
          - amount: 2356
            currency: GBP
      required:
        - home
        - private
        - public
        - total
    CreateDriverExpensesRequestBody:
      type: object
      properties:
        expenses:
          type: array
          items:
            $ref: '#/components/schemas/CreateExpenseRequest'
          example:
            - chargeId: 123
            - chargeId: 123
            - chargeId: 123
          minItems: 1
      example:
        expenses:
          - chargeId: 123
    CreateExpenseRequest:
      type: object
      properties:
        chargeId:
          type: integer
          description: Primary key of the submitted charge.
          example: 123
          format: int64
      example:
        chargeId: 123
      required:
        - chargeId
    CreatedExpense:
      type: object
      properties:
        chargeId:
          type: integer
          description: ID of the charge associated with the expense.
          example: 123
          format: int64
        id:
          type: integer
          description: Primary key of the expense.
          example: 456
          format: int64
      example:
        chargeId: 123
        id: 456
      required:
        - id
        - chargeId
    CreatedExpenseResponse:
      type: object
      properties:
        expenses:
          type: array
          items:
            $ref: '#/components/schemas/CreatedExpense'
          example:
            - chargeId: 123
              id: 456
            - chargeId: 123
              id: 456
      example:
        expenses:
          - chargeId: 123
            id: 456
          - chargeId: 123
            id: 456
          - chargeId: 123
            id: 456
    Dnoregion:
      type: object
      properties:
        dnoregion:
          type: string
          description: DNO region full name.
          example: Scottish Hydro Electric Power Distribution
        regionid:
          type: integer
          description: National grid DNO region id.
          example: 1
          format: int64
        shortname:
          type: string
          description: DNO region short name.
          example: North Scotland
      example:
        dnoregion: Scottish Hydro Electric Power Distribution
        regionid: 1
        shortname: North Scotland
      required:
        - regionid
        - dnoregion
        - shortname
    Driver:
      type: object
      properties:
        email:
          type: string
          description: Email address of the driver.
          example: <EMAIL>
        firstName:
          type: string
          description: First name of the driver.
          example: Max
        fullName:
          type: string
          description: Full name of the driver.
          example: Max Verstappen
        id:
          type: integer
          description: Primary key of the driver.
          example: 123
          format: int64
        lastName:
          type: string
          description: Last name of the driver.
          example: Verstappen
      example:
        email: <EMAIL>
        firstName: Max
        fullName: Max Verstappen
        id: 123
        lastName: Verstappen
      required:
        - id
        - firstName
        - lastName
        - fullName
        - email
    DriverCharge:
      type: object
      properties:
        businessName:
          type: string
          description: Business to whom this charge is associated
          example: Pod Point - Software Team
        chargeCost:
          type: integer
          description: Charge cost in pence
          example: 324550
          format: int64
        chargerName:
          type: string
          description: Name of charger associated with charge
          example: Nick-Gary
        chargingDuration:
          type: integer
          description: Charge duration in seconds
          example: 123
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        endTime:
          type: string
          description: Charge end time UTC
          example: '2022-09-05T17:58:33Z'
        energyUsage:
          type: number
          description: Energy used in kWh
          example: 98.76
          format: double
        pluggedInDuration:
          type: integer
          description: Plugged-in duration in seconds
          example: 123
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        startTime:
          type: string
          description: Charge start time UTC
          example: '2022-09-05T14:58:33Z'
      example:
        businessName: Pod Point - Software Team
        chargeCost: 324550
        chargerName: Nick-Gary
        chargingDuration: 123
        co2Savings: 4325.62
        endTime: '2022-09-05T17:58:33Z'
        energyUsage: 98.76
        pluggedInDuration: 123
        revenueGenerated: 457600
        startTime: '2022-09-05T14:58:33Z'
      required:
        - chargerName
        - businessName
        - startTime
        - endTime
        - chargingDuration
        - pluggedInDuration
        - energyUsage
        - chargeCost
        - revenueGenerated
        - co2Savings
    DriverChargesResponse:
      type: object
      properties:
        charges:
          type: array
          items:
            $ref: '#/components/schemas/DriverCharge'
          example:
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
      description: List of driver's charges
      example:
        charges:
          - businessName: Pod Point - Software Team
            chargeCost: 324550
            chargerName: Nick-Gary
            chargingDuration: 123
            co2Savings: 4325.62
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            pluggedInDuration: 123
            revenueGenerated: 457600
            startTime: '2022-09-05T14:58:33Z'
          - businessName: Pod Point - Software Team
            chargeCost: 324550
            chargerName: Nick-Gary
            chargingDuration: 123
            co2Savings: 4325.62
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            pluggedInDuration: 123
            revenueGenerated: 457600
            startTime: '2022-09-05T14:58:33Z'
    DriverNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Placeat debitis et rerum ratione et.
        status:
          type: integer
          example: 1993590641896640757
          format: int64
      description: No driver has been found for a given driverID.
      example:
        reason: Sed eos consectetur similique incidunt et.
        status: 2960999694147322496
      required:
        - reason
        - status
    DriverStatsData:
      type: object
      properties:
        intervals:
          type: array
          items:
            $ref: '#/components/schemas/Interval'
          description: Driver statistics broken down by the requested interval type.
          example:
            - from: '2022-10-12T00:00:00Z'
              stats:
                cost:
                  home:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  private:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  public:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  total:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                duration:
                  home: 680
                  private: 170
                  public: 170
                  total: 850
                energy:
                  home:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  private:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  public:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  total:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
              to: '2022-10-19T00:00:00Z'
            - from: '2022-10-12T00:00:00Z'
              stats:
                cost:
                  home:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  private:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  public:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  total:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                duration:
                  home: 680
                  private: 170
                  public: 170
                  total: 850
                energy:
                  home:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  private:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  public:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  total:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
              to: '2022-10-19T00:00:00Z'
        summary:
          $ref: '#/components/schemas/StatsSummary'
      example:
        intervals:
          - from: '2022-10-12T00:00:00Z'
            stats:
              cost:
                home:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                private:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                public:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                total:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
              duration:
                home: 680
                private: 170
                public: 170
                total: 850
              energy:
                home:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                private:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                public:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                total:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
            to: '2022-10-19T00:00:00Z'
          - from: '2022-10-12T00:00:00Z'
            stats:
              cost:
                home:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                private:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                public:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                total:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
              duration:
                home: 680
                private: 170
                public: 170
                total: 850
              energy:
                home:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                private:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                public:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                total:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
            to: '2022-10-19T00:00:00Z'
          - from: '2022-10-12T00:00:00Z'
            stats:
              cost:
                home:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                private:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                public:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                total:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
              duration:
                home: 680
                private: 170
                public: 170
                total: 850
              energy:
                home:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                private:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                public:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                total:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
            to: '2022-10-19T00:00:00Z'
          - from: '2022-10-12T00:00:00Z'
            stats:
              cost:
                home:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                private:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                public:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                total:
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
                  - amount: 2356
                    currency: GBP
              duration:
                home: 680
                private: 170
                public: 170
                total: 850
              energy:
                home:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                private:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                public:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
                total:
                  generation: 12.5
                  grid: 123.4
                  total: 135.9
            to: '2022-10-19T00:00:00Z'
        summary:
          cost:
            home:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            private:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            public:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            total:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
          duration:
            home: 680
            private: 170
            public: 170
            total: 850
          energy:
            home:
              generation: 12.5
              grid: 123.4
              total: 135.9
            private:
              generation: 12.5
              grid: 123.4
              total: 135.9
            public:
              generation: 12.5
              grid: 123.4
              total: 135.9
            total:
              generation: 12.5
              grid: 123.4
              total: 135.9
      required:
        - summary
    DriverStatsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/DriverStatsData'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          intervals:
            - from: '2022-10-12T00:00:00Z'
              stats:
                cost:
                  home:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  private:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  public:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  total:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                duration:
                  home: 680
                  private: 170
                  public: 170
                  total: 850
                energy:
                  home:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  private:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  public:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  total:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
              to: '2022-10-19T00:00:00Z'
            - from: '2022-10-12T00:00:00Z'
              stats:
                cost:
                  home:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  private:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  public:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                  total:
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                    - amount: 2356
                      currency: GBP
                duration:
                  home: 680
                  private: 170
                  public: 170
                  total: 850
                energy:
                  home:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  private:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  public:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
                  total:
                    generation: 12.5
                    grid: 123.4
                    total: 135.9
              to: '2022-10-19T00:00:00Z'
          summary:
            cost:
              home:
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
              private:
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
              public:
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
              total:
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
                - amount: 2356
                  currency: GBP
            duration:
              home: 680
              private: 170
              public: 170
              total: 850
            energy:
              home:
                generation: 12.5
                grid: 123.4
                total: 135.9
              private:
                generation: 12.5
                grid: 123.4
                total: 135.9
              public:
                generation: 12.5
                grid: 123.4
                total: 135.9
              total:
                generation: 12.5
                grid: 123.4
                total: 135.9
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    DriversChargeNotFound:
      type: object
      properties:
        reason:
          type: string
          example: In et dolorem deleniti molestiae explicabo aut.
        status:
          type: integer
          example: 4330075351137538910
          format: int64
      description: No charge can be found for the given user and charge IDs.
      example:
        reason: Sapiente officiis quo dolorum ut rem.
        status: 7784555528056234299
      required:
        - reason
        - status
    DriversChargeResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Charge'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          charger:
            door: A
            id: veefil-602300633
            name: Amoy-Reef
            pluggedInAt: '2018-05-15T12:00:00+12:00'
            pluggedInDuration: 3600
            siteName: Ecclestone Court Car Park
            type: home
            unpluggedAt: '2018-05-15T12:00:00+12:00'
          cost:
            amount: 2356
            currency: GBP
          duration: 22245
          endedAt: '2018-05-15T12:00:00+12:00'
          energyTotal: 567.89
          expensedTo:
            id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
            name: Adept Power Solutions Ltd
          generationEnergyTotal: 67.89
          gridEnergyTotal: 500
          id: 0d4c7438-d837-44ad-a96d-41c7fc616959
          startedAt: '2018-05-15T12:00:00+12:00'
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    DriversChargesResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Charges'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          charges:
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
            - charger:
                door: A
                id: veefil-602300633
                name: Amoy-Reef
                pluggedInAt: '2018-05-15T12:00:00+12:00'
                pluggedInDuration: 3600
                siteName: Ecclestone Court Car Park
                type: home
                unpluggedAt: '2018-05-15T12:00:00+12:00'
              cost:
                amount: 2356
                currency: GBP
              duration: 22245
              endedAt: '2018-05-15T12:00:00+12:00'
              energyTotal: 567.89
              expensedTo:
                id: 2072da84-bb3c-4c6a-afa5-e9b93b4fc682
                name: Adept Power Solutions Ltd
              generationEnergyTotal: 67.89
              gridEnergyTotal: 500
              id: 0d4c7438-d837-44ad-a96d-41c7fc616959
              startedAt: '2018-05-15T12:00:00+12:00'
          count: 22245
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    DuplicateChargeSubmission:
      type: object
      description: DuplicateChargeSubmission is the error returned when there is a subsequent submission for a charge resulting in a unique constraint on the database.
      example: {}
    Duration:
      type: object
      properties:
        home:
          type: integer
          description: Total duration of all charges at home chargers in secs
          example: 680
          format: int64
        private:
          type: integer
          description: Total duration of all charges at private chargers in secs
          example: 170
          format: int64
        public:
          type: integer
          description: Total duration of all charges at public chargers in secs
          example: 170
          format: int64
        total:
          type: integer
          description: Total duration of all charges in secs
          example: 850
          format: int64
      example:
        home: 680
        private: 170
        public: 170
        total: 850
      required:
        - home
        - private
        - public
        - total
    Energy:
      type: object
      properties:
        home:
          $ref: '#/components/schemas/Breakdown'
        private:
          $ref: '#/components/schemas/Breakdown'
        public:
          $ref: '#/components/schemas/Breakdown'
        total:
          $ref: '#/components/schemas/Breakdown'
      example:
        home:
          generation: 12.5
          grid: 123.4
          total: 135.9
        private:
          generation: 12.5
          grid: 123.4
          total: 135.9
        public:
          generation: 12.5
          grid: 123.4
          total: 135.9
        total:
          generation: 12.5
          grid: 123.4
          total: 135.9
      required:
        - home
        - private
        - public
        - total
    EnergyStatistics:
      type: object
      properties:
        claimedUsage:
          type: number
          description: Energy used this period in kWh, filtering out any unclaimed charges.
          example: 243.89
          format: double
        cost:
          type: integer
          description: Energy cost in pence.
          example: 324550
          format: int64
        revenueGeneratingClaimedUsage:
          type: number
          description: Energy used this period in kWh by claimed charges with a positive revenue.
          example: 43.5
          format: double
        totalUsage:
          type: number
          description: Energy used this period in kWh.
          example: 567.89
          format: double
        unclaimedUsage:
          type: number
          description: Energy used this period in kWh, filtering out any claimed charges.
          example: 324.65
          format: double
      example:
        claimedUsage: 243.89
        cost: 324550
        revenueGeneratingClaimedUsage: 43.5
        totalUsage: 567.89
        unclaimedUsage: 324.65
      required:
        - totalUsage
        - claimedUsage
        - revenueGeneratingClaimedUsage
        - unclaimedUsage
        - cost
    Error:
      type: object
      properties:
        fault:
          type: boolean
          description: Is the error a server-side fault?
          example: true
        id:
          type: string
          description: ID is a unique identifier for this particular occurrence of the problem.
          example: 123abc
        message:
          type: string
          description: Message is a human-readable explanation specific to this occurrence of the problem.
          example: parameter 'p' must be an integer
        name:
          type: string
          description: Name is the name of this class of errors.
          example: bad_request
        temporary:
          type: boolean
          description: Is the error temporary?
          example: true
        timeout:
          type: boolean
          description: Is the error a timeout?
          example: false
      example:
        fault: true
        id: 123abc
        message: parameter 'p' must be an integer
        name: bad_request
        temporary: false
        timeout: false
      required:
        - name
        - id
        - message
        - temporary
        - timeout
        - fault
    EventOutOfDate:
      type: object
      properties:
        reason:
          type: string
          example: Beatae aliquam et officia qui nihil aut.
        status:
          type: integer
          example: 1325769767574858650
          format: int64
      description: EventOutOfDate is the error returned when there is another update on the same charge, resulting in a unique constraint on the database.
      example:
        reason: Eos mollitia doloribus atque dolorem voluptatem et.
        status: 3176125574086649892
      required:
        - reason
        - status
    ExpensableCharge:
      type: object
      properties:
        chargeCost:
          type: integer
          description: Charge cost in pence
          example: 123
          format: int64
        chargerName:
          type: string
          description: Public charger name or home charger PSL
          example: Kent-Jake
        driver:
          $ref: '#/components/schemas/Driver'
        endTime:
          type: string
          description: Charge end time UTC
          example: '2022-09-05T17:58:33Z'
        energyUsage:
          type: number
          description: Energy used in KWh
          example: 98.76
          format: double
        id:
          type: integer
          description: Primary key of the charge
          example: 123
          format: int64
        location:
          $ref: '#/components/schemas/SubmittedChargeLocation'
        pluggedInAt:
          type: string
          description: Plugged in at time UTC
          example: '2022-09-30T15:35:00Z'
        processedByFullName:
          type: string
          description: Who processed this expense (if processed)
          example: John Smith
        processedTime:
          type: string
          description: Processed time UTC (if processed)
          example: '2022-09-05T14:58:33Z'
        startTime:
          type: string
          description: Charge start time UTC
          example: '2022-09-05T14:58:33Z'
        submittedTime:
          type: string
          description: Submitted for approval time UTC
          example: '2022-09-05T17:58:33Z'
        unpluggedAt:
          type: string
          description: Unplugged at time UTC
          example: '2022-09-30T16:46:00Z'
      description: Expensable charge
      example:
        chargeCost: 123
        chargerName: Kent-Jake
        driver:
          email: <EMAIL>
          firstName: Max
          fullName: Max Verstappen
          id: 123
          lastName: Verstappen
        endTime: '2022-09-05T17:58:33Z'
        energyUsage: 98.76
        id: 123
        location:
          address:
            country: United Kingdom
            line1: 234 Banner St
            line2: Westminster
            postcode: EC1Y 8QE
            prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
            town: London
          id: 456
          locationType: home
        pluggedInAt: '2022-09-30T15:35:00Z'
        processedByFullName: John Smith
        processedTime: '2022-09-05T14:58:33Z'
        startTime: '2022-09-05T14:58:33Z'
        submittedTime: '2022-09-05T17:58:33Z'
        unpluggedAt: '2022-09-30T16:46:00Z'
      required:
        - id
        - driver
        - startTime
        - endTime
        - submittedTime
        - chargeCost
        - energyUsage
        - location
        - chargerName
    ExpensableChargeDriverSummary:
      type: object
      properties:
        driver:
          $ref: '#/components/schemas/Driver'
        submittedChargeIds:
          type: array
          items:
            type: integer
            example: 980201913009922336
            format: int64
          description: List of submitted charge IDs.
          example:
            - 1
            - 2
            - 4
            - 42
            - 54
        totalCharges:
          type: integer
          description: Number of charges.
          example: 1
          format: int64
        totalCost:
          $ref: '#/components/schemas/TotalCost'
        totalUsage:
          $ref: '#/components/schemas/TotalUsage'
      example:
        driver:
          email: <EMAIL>
          firstName: Max
          fullName: Max Verstappen
          id: 123
          lastName: Verstappen
        submittedChargeIds:
          - 1
          - 2
          - 4
          - 42
          - 54
        totalCharges: 1
        totalCost:
          home: 12300
          public: 45670
        totalUsage:
          home: 12.34
          public: 56.78
          total: 12.34
      required:
        - driver
        - totalCharges
        - totalUsage
        - totalCost
    ExpensedTo:
      type: object
      properties:
        id:
          type: string
          description: Groups unique identifier.
          example: f804280d-5b14-493c-9755-2d286ccad898
          format: uuid
        name:
          type: string
          description: Groups name.
          example: Adept Power Solutions Ltd
      description: Group the charge is to be expensed to.
      example:
        id: 6f072ef4-8db4-4a52-928d-d68669085b31
        name: Adept Power Solutions Ltd
      required:
        - id
        - name
    FleetUsageResponse:
      type: object
      properties:
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        numberOfDrivers:
          type: integer
          description: Number of drivers.
          example: 367
          format: int64
        totalCharges:
          $ref: '#/components/schemas/TotalCharges'
        totalUsage:
          $ref: '#/components/schemas/TotalUsage'
      example:
        co2Savings: 4325.62
        numberOfDrivers: 367
        totalCharges:
          home: 367
          public: 367
          total: 367
        totalUsage:
          home: 12.34
          public: 56.78
          total: 12.34
      required:
        - totalCharges
        - totalUsage
        - co2Savings
        - numberOfDrivers
    Forecast:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Forecastdata'
      example:
        data:
          data:
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
          dnoregion: Voluptatem molestiae id sit.
          regionid: 573640858367639627
          shortname: Veniam architecto dolorum libero distinctio doloremque quae.
      required:
        - data
    Forecastdata:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Period'
          example:
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
            - from: 2018-01-20T12:00Z
              generationmix:
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
                - fuel: Dolorem molestias sed magnam consequatur.
                  perc: 0.85192853
              intensity:
                forecast: 1609991548261271046
                index: Amet autem dolorem.
              to: 2018-01-20T12:00Z
        dnoregion:
          type: string
          example: Vel quia ab recusandae pariatur alias tempore.
        regionid:
          type: integer
          example: 1592336715409948320
          format: int64
        shortname:
          type: string
          example: Eum enim.
      example:
        data:
          - from: 2018-01-20T12:00Z
            generationmix:
              - fuel: Dolorem molestias sed magnam consequatur.
                perc: 0.85192853
              - fuel: Dolorem molestias sed magnam consequatur.
                perc: 0.85192853
            intensity:
              forecast: 1609991548261271046
              index: Amet autem dolorem.
            to: 2018-01-20T12:00Z
          - from: 2018-01-20T12:00Z
            generationmix:
              - fuel: Dolorem molestias sed magnam consequatur.
                perc: 0.85192853
              - fuel: Dolorem molestias sed magnam consequatur.
                perc: 0.85192853
            intensity:
              forecast: 1609991548261271046
              index: Amet autem dolorem.
            to: 2018-01-20T12:00Z
        dnoregion: Impedit repellendus.
        regionid: 1304190336489311728
        shortname: Provident excepturi ipsum ut incidunt.
      required:
        - regionid
        - dnoregion
        - shortname
        - data
    GroupChargeStatistics:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: sum of charging duration in seconds
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/EnergyStatistics'
        numberOfChargers:
          type: integer
          description: count of distinct charger ids
          example: 3
          format: int64
        numberOfCharges:
          type: integer
          description: count of distinct charge uuids
          example: 10
          format: int64
        numberOfSites:
          type: integer
          description: count of distinct sites
          example: 3
          format: int64
        numberOfUsers:
          type: integer
          description: count of distinct user ids
          example: 2
          format: int64
        revenueGenerated:
          type: integer
          description: sum of settlement amount in pence
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfChargers: 3
        numberOfCharges: 10
        numberOfSites: 3
        numberOfUsers: 2
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - numberOfSites
        - co2Savings
        - energy
        - numberOfChargers
        - numberOfUsers
        - chargingDuration
        - revenueGenerated
    GroupNotFound:
      type: object
      properties:
        reason:
          type: string
          example: Aliquam rem.
        status:
          type: integer
          example: 137132495969302540
          format: int64
      description: GroupNotFound is the error returned when there is no group for a given {groupId}.
      example:
        reason: Et eos quia eum consequatur.
        status: 6074588362548896930
      required:
        - reason
        - status
    GroupSitesStats:
      type: object
      properties:
        co2AvoidedKg:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        energyCost:
          type: integer
          description: Total energy cost in pence.
          example: 3245
          format: int64
        energyUsageKwh:
          type: number
          description: Total energy usage in kWh.
          example: 567.89
          format: double
        numberOfCharges:
          type: integer
          description: Total number of charges.
          example: 2048
          format: int64
        numberOfDrivers:
          type: integer
          description: Total number of unique drivers.
          example: 64
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        siteId:
          type: string
          description: Site ID
          example: 74602255-bb13-45be-8118-8712ff97e600
          format: uuid
        totalDuration:
          type: integer
          description: Total duration of charging in seconds.
          example: 22245
          format: int64
      example:
        co2AvoidedKg: 4325.62
        energyCost: 3245
        energyUsageKwh: 567.89
        numberOfCharges: 2048
        numberOfDrivers: 64
        revenueGenerated: 457600
        siteId: 49cb2e4c-28eb-4cc8-962c-c20f3149af73
        totalDuration: 22245
      required:
        - siteId
        - energyUsageKwh
        - energyCost
        - co2AvoidedKg
        - revenueGenerated
        - numberOfCharges
        - numberOfDrivers
        - totalDuration
    GroupSitesStatsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/GroupSitesStats'
          example:
            - co2AvoidedKg: 4325.62
              energyCost: 3245
              energyUsageKwh: 567.89
              numberOfCharges: 2048
              numberOfDrivers: 64
              revenueGenerated: 457600
              siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
              totalDuration: 22245
            - co2AvoidedKg: 4325.62
              energyCost: 3245
              energyUsageKwh: 567.89
              numberOfCharges: 2048
              numberOfDrivers: 64
              revenueGenerated: 457600
              siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
              totalDuration: 22245
            - co2AvoidedKg: 4325.62
              energyCost: 3245
              energyUsageKwh: 567.89
              numberOfCharges: 2048
              numberOfDrivers: 64
              revenueGenerated: 457600
              siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
              totalDuration: 22245
            - co2AvoidedKg: 4325.62
              energyCost: 3245
              energyUsageKwh: 567.89
              numberOfCharges: 2048
              numberOfDrivers: 64
              revenueGenerated: 457600
              siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
              totalDuration: 22245
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          - co2AvoidedKg: 4325.62
            energyCost: 3245
            energyUsageKwh: 567.89
            numberOfCharges: 2048
            numberOfDrivers: 64
            revenueGenerated: 457600
            siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
            totalDuration: 22245
          - co2AvoidedKg: 4325.62
            energyCost: 3245
            energyUsageKwh: 567.89
            numberOfCharges: 2048
            numberOfDrivers: 64
            revenueGenerated: 457600
            siteId: 12c88855-37f1-4f05-ba8d-0ea371bd6eee
            totalDuration: 22245
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    IdentifierNotProvided:
      type: object
      properties:
        reason:
          type: string
          example: Illum eius dolorem quo.
        status:
          type: integer
          example: 718997484148012082
          format: int64
      description: IdentifierNotProvided is the error returned when an identifier is not provided.
      example:
        reason: Beatae nesciunt illo quia animi.
        status: 4487406079611466721
      required:
        - reason
        - status
    Intensity:
      type: object
      properties:
        forecast:
          type: integer
          example: 7958373975251559075
          format: int64
        index:
          type: string
          description: 'Intensity index with values: very low, low, moderate, high, very high'
          example: Nam et.
      example:
        forecast: 8997976706335188803
        index: Minima omnis suscipit magni vitae facere fugit.
      required:
        - forecast
        - index
    Interval:
      type: object
      properties:
        from:
          type: string
          description: 'Statistics date time range start eg: 2022-01-01T00:00:00Z'
          example: '2022-10-12T00:00:00Z'
          format: date-time
        stats:
          $ref: '#/components/schemas/StatsSummary'
        to:
          type: string
          description: 'Statistics report inclusive end date eg: 2022-01-31T00:00:00Z'
          example: '2022-10-19T00:00:00Z'
          format: date-time
      example:
        from: '2022-10-12T00:00:00Z'
        stats:
          cost:
            home:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            private:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            public:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
            total:
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
              - amount: 2356
                currency: GBP
          duration:
            home: 680
            private: 170
            public: 170
            total: 850
          energy:
            home:
              generation: 12.5
              grid: 123.4
              total: 135.9
            private:
              generation: 12.5
              grid: 123.4
              total: 135.9
            public:
              generation: 12.5
              grid: 123.4
              total: 135.9
            total:
              generation: 12.5
              grid: 123.4
              total: 135.9
        to: '2022-10-19T00:00:00Z'
      required:
        - from
        - to
        - stats
    InvalidTimestamp:
      type: object
      description: InvalidTimestamp is the error returned when a timestamp request parameter is invalid.
      example: {}
    Limit:
      type: object
      properties:
        amount:
          type: number
          description: Amount for a limit
          example: 1.23
          format: double
        type:
          type: string
          description: Type of a limit
          example: energy
          enum:
            - energy
            - duration
        unit:
          type: string
          description: Unit for a limit - currently only kWh, but could be time interval in the future
          example: kWh
      example:
        amount: 1.23
        type: energy
        unit: kWh
    MarkSubmittedChargesAsProcessedRequestBody:
      type: object
      properties:
        approverId:
          type: integer
          example: 3250841504753116128
          format: int64
        submittedChargeIds:
          type: array
          items:
            type: integer
            example: 4918678230612558539
            format: int64
          example:
            - 12375
            - 17456
          minItems: 1
      example:
        approverId: 2227602187141307644
        submittedChargeIds:
          - 12375
          - 17456
      required:
        - approverId
        - submittedChargeIds
    Meta:
      type: object
      properties:
        params:
          type: object
          description: Passed parameters
          example:
            Atque dolores et sit nobis et debitis.: Voluptatem voluptate aut dolorum tempore quo et.
            Officiis maiores delectus ipsum.: Et voluptatem ullam numquam repudiandae non fugiat.
          additionalProperties: true
      example:
        params:
          Quibusdam id explicabo.: Ut assumenda doloremque veritatis iste praesentium.
      required:
        - params
    Mix:
      type: object
      properties:
        fuel:
          type: string
          description: 'Fuel type with values: gas, coal, biomass, nuclear, hydro, storage, imports, other, wind, solar'
          example: Assumenda omnis vitae.
        perc:
          type: number
          example: 0.46395028
          format: float
      example:
        fuel: Iste nesciunt sit.
        perc: 0.73093307
      required:
        - fuel
        - perc
    Money:
      type: object
      properties:
        amount:
          type: integer
          description: Amount in smallest denomination of the associated currency
          example: 2356
          format: int64
        currency:
          type: string
          description: ISO currency code
          example: GBP
      description: An amount of money with its currency type.
      example:
        amount: 2356
        currency: GBP
      required:
        - amount
        - currency
    MoneyInt64:
      type: object
      properties:
        amount:
          type: integer
          description: Amount in smallest denomination of the associated currency
          example: 2356
          format: int64
        currency:
          type: string
          description: ISO currency code
          example: GBP
      description: An amount of money with its currency type.
      example:
        amount: 2356
        currency: GBP
      required:
        - amount
        - currency
    OrganisationChargesDriverSummaryResponse:
      type: object
      properties:
        driverExpensableChargeSummaries:
          type: array
          items:
            $ref: '#/components/schemas/ExpensableChargeDriverSummary'
          example:
            - driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              submittedChargeIds:
                - 1
                - 2
                - 4
                - 42
                - 54
              totalCharges: 1
              totalCost:
                home: 12300
                public: 45670
              totalUsage:
                home: 12.34
                public: 56.78
                total: 12.34
            - driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              submittedChargeIds:
                - 1
                - 2
                - 4
                - 42
                - 54
              totalCharges: 1
              totalCost:
                home: 12300
                public: 45670
              totalUsage:
                home: 12.34
                public: 56.78
                total: 12.34
            - driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              submittedChargeIds:
                - 1
                - 2
                - 4
                - 42
                - 54
              totalCharges: 1
              totalCost:
                home: 12300
                public: 45670
              totalUsage:
                home: 12.34
                public: 56.78
                total: 12.34
            - driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              submittedChargeIds:
                - 1
                - 2
                - 4
                - 42
                - 54
              totalCharges: 1
              totalCost:
                home: 12300
                public: 45670
              totalUsage:
                home: 12.34
                public: 56.78
                total: 12.34
      description: Expensable charges summary grouped per driver
      example:
        driverExpensableChargeSummaries:
          - driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            submittedChargeIds:
              - 1
              - 2
              - 4
              - 42
              - 54
            totalCharges: 1
            totalCost:
              home: 12300
              public: 45670
            totalUsage:
              home: 12.34
              public: 56.78
              total: 12.34
          - driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            submittedChargeIds:
              - 1
              - 2
              - 4
              - 42
              - 54
            totalCharges: 1
            totalCost:
              home: 12300
              public: 45670
            totalUsage:
              home: 12.34
              public: 56.78
              total: 12.34
      required:
        - driverExpensableChargeSummaries
    OrganisationChargesResponse:
      type: object
      properties:
        expensableCharges:
          type: array
          items:
            $ref: '#/components/schemas/ExpensableCharge'
          example:
            - chargeCost: 123
              chargerName: Kent-Jake
              driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              id: 123
              location:
                address:
                  country: United Kingdom
                  line1: 234 Banner St
                  line2: Westminster
                  postcode: EC1Y 8QE
                  prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                  town: London
                id: 456
                locationType: home
              pluggedInAt: '2022-09-30T15:35:00Z'
              processedByFullName: John Smith
              processedTime: '2022-09-05T14:58:33Z'
              startTime: '2022-09-05T14:58:33Z'
              submittedTime: '2022-09-05T17:58:33Z'
              unpluggedAt: '2022-09-30T16:46:00Z'
            - chargeCost: 123
              chargerName: Kent-Jake
              driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              id: 123
              location:
                address:
                  country: United Kingdom
                  line1: 234 Banner St
                  line2: Westminster
                  postcode: EC1Y 8QE
                  prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                  town: London
                id: 456
                locationType: home
              pluggedInAt: '2022-09-30T15:35:00Z'
              processedByFullName: John Smith
              processedTime: '2022-09-05T14:58:33Z'
              startTime: '2022-09-05T14:58:33Z'
              submittedTime: '2022-09-05T17:58:33Z'
              unpluggedAt: '2022-09-30T16:46:00Z'
      description: Expensable charges
      example:
        expensableCharges:
          - chargeCost: 123
            chargerName: Kent-Jake
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2022-09-30T15:35:00Z'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2022-09-30T16:46:00Z'
          - chargeCost: 123
            chargerName: Kent-Jake
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2022-09-30T15:35:00Z'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2022-09-30T16:46:00Z'
          - chargeCost: 123
            chargerName: Kent-Jake
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2022-09-30T15:35:00Z'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2022-09-30T16:46:00Z'
          - chargeCost: 123
            chargerName: Kent-Jake
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2022-09-30T15:35:00Z'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2022-09-30T16:46:00Z'
      required:
        - expensableCharges
    OrganisationDriversChargeStatistics:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: Aggregate charge duration in seconds
          example: 123
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        driver:
          $ref: '#/components/schemas/Driver'
        numberOfCharges:
          type: integer
          description: Total number of charges.
          example: 2048
          format: int64
        pluggedInDuration:
          type: integer
          description: Aggregate plugged-in duration in seconds
          example: 123
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        totalCost:
          type: integer
          description: Aggregate charge cost in pence
          example: 324550
          format: int64
        totalUsage:
          type: number
          description: Energy used this period in kWh.
          example: 567.89
          format: double
      example:
        chargingDuration: 123
        co2Savings: 4325.62
        driver:
          email: <EMAIL>
          firstName: Max
          fullName: Max Verstappen
          id: 123
          lastName: Verstappen
        numberOfCharges: 2048
        pluggedInDuration: 123
        revenueGenerated: 457600
        totalCost: 324550
        totalUsage: 567.89
      required:
        - driver
        - numberOfCharges
        - totalUsage
        - co2Savings
        - chargingDuration
        - pluggedInDuration
        - totalCost
        - revenueGenerated
    OrganisationDriversChargeStatisticsResponse:
      type: object
      properties:
        charges:
          type: array
          items:
            $ref: '#/components/schemas/OrganisationDriversChargeStatistics'
          example:
            - chargingDuration: 123
              co2Savings: 4325.62
              driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              numberOfCharges: 2048
              pluggedInDuration: 123
              revenueGenerated: 457600
              totalCost: 324550
              totalUsage: 567.89
            - chargingDuration: 123
              co2Savings: 4325.62
              driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              numberOfCharges: 2048
              pluggedInDuration: 123
              revenueGenerated: 457600
              totalCost: 324550
              totalUsage: 567.89
            - chargingDuration: 123
              co2Savings: 4325.62
              driver:
                email: <EMAIL>
                firstName: Max
                fullName: Max Verstappen
                id: 123
                lastName: Verstappen
              numberOfCharges: 2048
              pluggedInDuration: 123
              revenueGenerated: 457600
              totalCost: 324550
              totalUsage: 567.89
      description: Aggregate statistics for each organisation driver
      example:
        charges:
          - chargingDuration: 123
            co2Savings: 4325.62
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            numberOfCharges: 2048
            pluggedInDuration: 123
            revenueGenerated: 457600
            totalCost: 324550
            totalUsage: 567.89
          - chargingDuration: 123
            co2Savings: 4325.62
            driver:
              email: <EMAIL>
              firstName: Max
              fullName: Max Verstappen
              id: 123
              lastName: Verstappen
            numberOfCharges: 2048
            pluggedInDuration: 123
            revenueGenerated: 457600
            totalCost: 324550
            totalUsage: 567.89
    OrganisationNotFound:
      type: object
      description: OrganisationNotFound is the error returned when there is no organisation for a given {organisationId}.
      example: {}
    Organisationchargessummary:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: Total duration of charging in seconds.
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/ChargeEnergySummary'
        numberOfChargers:
          type: integer
          description: Total number of Chargers.
          example: 32
          format: int64
        numberOfCharges:
          type: integer
          description: Total number of charges.
          example: 2048
          format: int64
        numberOfDrivers:
          type: integer
          description: Total number of unique drivers.
          example: 64
          format: int64
        numberOfSites:
          type: integer
          description: Total number of Sites.
          example: 16
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfChargers: 32
        numberOfCharges: 2048
        numberOfDrivers: 64
        numberOfSites: 16
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - numberOfSites
        - numberOfChargers
        - numberOfDrivers
        - chargingDuration
        - revenueGenerated
        - co2Savings
    Period:
      type: object
      properties:
        from:
          type: string
          example: 2018-01-20T12:00Z
        generationmix:
          type: array
          items:
            $ref: '#/components/schemas/Mix'
          example:
            - fuel: Dolorem molestias sed magnam consequatur.
              perc: 0.85192853
            - fuel: Dolorem molestias sed magnam consequatur.
              perc: 0.85192853
        intensity:
          $ref: '#/components/schemas/Intensity'
        to:
          type: string
          example: 2018-01-20T12:00Z
      example:
        from: 2018-01-20T12:00Z
        generationmix:
          - fuel: Dolorem molestias sed magnam consequatur.
            perc: 0.85192853
          - fuel: Dolorem molestias sed magnam consequatur.
            perc: 0.85192853
          - fuel: Dolorem molestias sed magnam consequatur.
            perc: 0.85192853
          - fuel: Dolorem molestias sed magnam consequatur.
            perc: 0.85192853
        intensity:
          forecast: 1609991548261271046
          index: Amet autem dolorem.
        to: 2018-01-20T12:00Z
      required:
        - from
        - to
        - intensity
        - generationmix
    ProjectionChargesResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Charges3'
          example:
            - chargeDurationTotal: 22245
              chargerId: PSL-0001
              chargerName: Corrupti sit dignissimos nam labore illo.
              co2Avoided: 4325.62
              confirmed: true
              door: A
              driverIDs:
                - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
              endedAt: '2018-05-15T12:00:00Z'
              energyCost: 32
              energyTotal: 567.89
              generationEnergyTotal: 35.44
              gridEnergyTotal: 532.45
              id: 5588483f-7125-414a-887f-6fdc37911182
              pluggedInAt: '2018-05-15T12:00:00Z'
              revenueGenerated: 457600
              siteName: Et illum non quidem sequi veritatis quibusdam.
              startedAt: '2018-05-15T12:00:00Z'
              unpluggedAt: '2018-05-15T12:00:00Z'
            - chargeDurationTotal: 22245
              chargerId: PSL-0001
              chargerName: Corrupti sit dignissimos nam labore illo.
              co2Avoided: 4325.62
              confirmed: true
              door: A
              driverIDs:
                - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
              endedAt: '2018-05-15T12:00:00Z'
              energyCost: 32
              energyTotal: 567.89
              generationEnergyTotal: 35.44
              gridEnergyTotal: 532.45
              id: 5588483f-7125-414a-887f-6fdc37911182
              pluggedInAt: '2018-05-15T12:00:00Z'
              revenueGenerated: 457600
              siteName: Et illum non quidem sequi veritatis quibusdam.
              startedAt: '2018-05-15T12:00:00Z'
              unpluggedAt: '2018-05-15T12:00:00Z'
            - chargeDurationTotal: 22245
              chargerId: PSL-0001
              chargerName: Corrupti sit dignissimos nam labore illo.
              co2Avoided: 4325.62
              confirmed: true
              door: A
              driverIDs:
                - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
              endedAt: '2018-05-15T12:00:00Z'
              energyCost: 32
              energyTotal: 567.89
              generationEnergyTotal: 35.44
              gridEnergyTotal: 532.45
              id: 5588483f-7125-414a-887f-6fdc37911182
              pluggedInAt: '2018-05-15T12:00:00Z'
              revenueGenerated: 457600
              siteName: Et illum non quidem sequi veritatis quibusdam.
              startedAt: '2018-05-15T12:00:00Z'
              unpluggedAt: '2018-05-15T12:00:00Z'
            - chargeDurationTotal: 22245
              chargerId: PSL-0001
              chargerName: Corrupti sit dignissimos nam labore illo.
              co2Avoided: 4325.62
              confirmed: true
              door: A
              driverIDs:
                - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
                - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
              endedAt: '2018-05-15T12:00:00Z'
              energyCost: 32
              energyTotal: 567.89
              generationEnergyTotal: 35.44
              gridEnergyTotal: 532.45
              id: 5588483f-7125-414a-887f-6fdc37911182
              pluggedInAt: '2018-05-15T12:00:00Z'
              revenueGenerated: 457600
              siteName: Et illum non quidem sequi veritatis quibusdam.
              startedAt: '2018-05-15T12:00:00Z'
              unpluggedAt: '2018-05-15T12:00:00Z'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          - chargeDurationTotal: 22245
            chargerId: PSL-0001
            chargerName: Corrupti sit dignissimos nam labore illo.
            co2Avoided: 4325.62
            confirmed: true
            door: A
            driverIDs:
              - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
              - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
            endedAt: '2018-05-15T12:00:00Z'
            energyCost: 32
            energyTotal: 567.89
            generationEnergyTotal: 35.44
            gridEnergyTotal: 532.45
            id: 5588483f-7125-414a-887f-6fdc37911182
            pluggedInAt: '2018-05-15T12:00:00Z'
            revenueGenerated: 457600
            siteName: Et illum non quidem sequi veritatis quibusdam.
            startedAt: '2018-05-15T12:00:00Z'
            unpluggedAt: '2018-05-15T12:00:00Z'
          - chargeDurationTotal: 22245
            chargerId: PSL-0001
            chargerName: Corrupti sit dignissimos nam labore illo.
            co2Avoided: 4325.62
            confirmed: true
            door: A
            driverIDs:
              - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
              - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
            endedAt: '2018-05-15T12:00:00Z'
            energyCost: 32
            energyTotal: 567.89
            generationEnergyTotal: 35.44
            gridEnergyTotal: 532.45
            id: 5588483f-7125-414a-887f-6fdc37911182
            pluggedInAt: '2018-05-15T12:00:00Z'
            revenueGenerated: 457600
            siteName: Et illum non quidem sequi veritatis quibusdam.
            startedAt: '2018-05-15T12:00:00Z'
            unpluggedAt: '2018-05-15T12:00:00Z'
          - chargeDurationTotal: 22245
            chargerId: PSL-0001
            chargerName: Corrupti sit dignissimos nam labore illo.
            co2Avoided: 4325.62
            confirmed: true
            door: A
            driverIDs:
              - 3e4a9823-d60e-4f14-aa88-eea8c327b8f1
              - 7a8b9c63-6a8b-4a68-8efa-097c1a6d257d
            endedAt: '2018-05-15T12:00:00Z'
            energyCost: 32
            energyTotal: 567.89
            generationEnergyTotal: 35.44
            gridEnergyTotal: 532.45
            id: 5588483f-7125-414a-887f-6fdc37911182
            pluggedInAt: '2018-05-15T12:00:00Z'
            revenueGenerated: 457600
            siteName: Et illum non quidem sequi veritatis quibusdam.
            startedAt: '2018-05-15T12:00:00Z'
            unpluggedAt: '2018-05-15T12:00:00Z'
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    ProjectionGroupAndUserChargesResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/UserChargesSchema'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          charges:
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    ProjectionchargerChargeStatisticsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ChargerChargeStatistics'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          chargingDuration: 22245
          co2Savings: 4325.62
          energy:
            claimedUsage: 243.89
            cost: 324550
            revenueGeneratingClaimedUsage: 43.5
            totalUsage: 567.89
            unclaimedUsage: 324.65
          numberOfCharges: 10
          numberOfUsers: 2
          revenueGenerated: 457600
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    ProjectiongroupChargeStatisticsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/GroupChargeStatistics'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          chargingDuration: 22245
          co2Savings: 4325.62
          energy:
            claimedUsage: 243.89
            cost: 324550
            revenueGeneratingClaimedUsage: 43.5
            totalUsage: 567.89
            unclaimedUsage: 324.65
          numberOfChargers: 3
          numberOfCharges: 10
          numberOfSites: 3
          numberOfUsers: 2
          revenueGenerated: 457600
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    ProjectionsiteChargeStatisticsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SiteChargeStatistics'
        meta:
          $ref: '#/components/schemas/Meta'
      example:
        data:
          chargingDuration: 22245
          co2Savings: 4325.62
          energy:
            claimedUsage: 243.89
            cost: 324550
            revenueGeneratingClaimedUsage: 43.5
            totalUsage: 567.89
            unclaimedUsage: 324.65
          numberOfChargers: 3
          numberOfCharges: 10
          numberOfUsers: 2
          revenueGenerated: 457600
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
      required:
        - data
        - meta
    Region:
      type: object
      properties:
        regionid:
          type: integer
          description: National grid DNO region id.
          example: 1
          format: int64
      example:
        regionid: 1
      required:
        - regionid
    Regions:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Dnoregion'
          example:
            - dnoregion: Scottish Hydro Electric Power Distribution
              regionid: 1
              shortname: North Scotland
            - dnoregion: Scottish Hydro Electric Power Distribution
              regionid: 1
              shortname: North Scotland
            - dnoregion: Scottish Hydro Electric Power Distribution
              regionid: 1
              shortname: North Scotland
      example:
        data:
          - dnoregion: Scottish Hydro Electric Power Distribution
            regionid: 1
            shortname: North Scotland
          - dnoregion: Scottish Hydro Electric Power Distribution
            regionid: 1
            shortname: North Scotland
      required:
        - data
    RetrieveOrganisationDriversStatisticsRequestBody:
      type: object
      properties:
        driverIds:
          type: array
          items:
            type: string
            example: ef542eab-3e6b-4b6c-ba6c-c0a678a3e926
            format: uuid
          example:
            - 12d9423b-8a65-40b1-af24-062b41e1cedb
            - 648450d6-a670-4a60-8a1e-acb79eafe708
          minItems: 1
      example:
        driverIds:
          - 12d9423b-8a65-40b1-af24-062b41e1cedb
          - 648450d6-a670-4a60-8a1e-acb79eafe708
      required:
        - driverIds
    SiteChargeStatistics:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: sum of charging duration in seconds
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/EnergyStatistics'
        numberOfChargers:
          type: integer
          description: count of distinct charger ids
          example: 3
          format: int64
        numberOfCharges:
          type: integer
          description: count of distinct charge uuids
          example: 10
          format: int64
        numberOfUsers:
          type: integer
          description: count of distinct user ids
          example: 2
          format: int64
        revenueGenerated:
          type: integer
          description: sum of settlement amount in pence
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfChargers: 3
        numberOfCharges: 10
        numberOfUsers: 2
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - co2Savings
        - energy
        - numberOfChargers
        - numberOfUsers
        - chargingDuration
        - revenueGenerated
    SiteNotFound:
      type: object
      description: SiteNotFound is the error returned when there is no site for a given site ID.
      example: {}
    SiteStats:
      type: object
      properties:
        groupId:
          type: string
          description: Group unique identifier.
          example: 9b9cbbcc-100b-4c38-9355-d28ee713097d
          format: uuid
        groupName:
          type: string
          description: Group Name.
          example: Autem fugit rerum ratione recusandae rerum eius.
        id:
          type: string
          description: Site unique identifier.
          example: be7126ec-d1d8-40e4-8ee2-24b296045368
          format: uuid
        name:
          type: string
          description: Site name.
          example: Minima et.
        revenueGenerated:
          type: integer
          description: Revenue generated in pence
          example: 16
          format: int64
        totalEnergy:
          type: number
          description: Energy used in kWh.
          example: 567.89
          format: double
      example:
        groupId: 4b025907-8f0c-4238-8009-f7e4a57cc74f
        groupName: Ut quibusdam occaecati a officia blanditiis reiciendis.
        id: 7d52777b-0d39-4203-8c5b-acfca81cbc68
        name: Temporibus autem expedita.
        revenueGenerated: 16
        totalEnergy: 567.89
      required:
        - id
        - totalEnergy
        - revenueGenerated
    SiteStatsResponse:
      type: object
      properties:
        count:
          type: integer
          description: Count of sites returned.
          example: 650
          format: int64
        data:
          type: array
          items:
            $ref: '#/components/schemas/SiteStats'
          description: Charge data for site.
          example:
            - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
              groupName: Voluptates aut velit nihil id quam.
              id: d4d86287-ebd1-454e-9bd2-c3049563c061
              name: Ipsum provident.
              revenueGenerated: 16
              totalEnergy: 567.89
            - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
              groupName: Voluptates aut velit nihil id quam.
              id: d4d86287-ebd1-454e-9bd2-c3049563c061
              name: Ipsum provident.
              revenueGenerated: 16
              totalEnergy: 567.89
            - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
              groupName: Voluptates aut velit nihil id quam.
              id: d4d86287-ebd1-454e-9bd2-c3049563c061
              name: Ipsum provident.
              revenueGenerated: 16
              totalEnergy: 567.89
            - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
              groupName: Voluptates aut velit nihil id quam.
              id: d4d86287-ebd1-454e-9bd2-c3049563c061
              name: Ipsum provident.
              revenueGenerated: 16
              totalEnergy: 567.89
        from:
          type: string
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          example: '2022-10-12'
          format: date
        to:
          type: string
          description: 'Statistics report inclusive end date eg: 2022-01-31'
          example: '2022-10-19'
          format: date
      example:
        count: 650
        data:
          - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
            groupName: Voluptates aut velit nihil id quam.
            id: d4d86287-ebd1-454e-9bd2-c3049563c061
            name: Ipsum provident.
            revenueGenerated: 16
            totalEnergy: 567.89
          - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
            groupName: Voluptates aut velit nihil id quam.
            id: d4d86287-ebd1-454e-9bd2-c3049563c061
            name: Ipsum provident.
            revenueGenerated: 16
            totalEnergy: 567.89
          - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
            groupName: Voluptates aut velit nihil id quam.
            id: d4d86287-ebd1-454e-9bd2-c3049563c061
            name: Ipsum provident.
            revenueGenerated: 16
            totalEnergy: 567.89
          - groupId: 594c3d4b-c093-40dc-b191-2850071fe1b0
            groupName: Voluptates aut velit nihil id quam.
            id: d4d86287-ebd1-454e-9bd2-c3049563c061
            name: Ipsum provident.
            revenueGenerated: 16
            totalEnergy: 567.89
        from: '2022-10-12'
        to: '2022-10-19'
      required:
        - count
        - from
        - to
        - data
    Sitechargessummary:
      type: object
      properties:
        chargingDuration:
          type: integer
          description: Total duration of charging in seconds.
          example: 22245
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        energy:
          $ref: '#/components/schemas/ChargeEnergySummary'
        numberOfChargers:
          type: integer
          description: Total number of Chargers.
          example: 32
          format: int64
        numberOfCharges:
          type: integer
          description: Total number of charges.
          example: 2048
          format: int64
        numberOfDrivers:
          type: integer
          description: Total number of unique drivers.
          example: 64
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
      example:
        chargingDuration: 22245
        co2Savings: 4325.62
        energy:
          claimedUsage: 243.89
          cost: 324550
          revenueGeneratingClaimedUsage: 43.5
          totalUsage: 567.89
          unclaimedUsage: 324.65
        numberOfChargers: 32
        numberOfCharges: 2048
        numberOfDrivers: 64
        revenueGenerated: 457600
      required:
        - numberOfCharges
        - numberOfDrivers
        - energy
        - chargingDuration
        - revenueGenerated
        - co2Savings
        - numberOfChargers
    StatsSummary:
      type: object
      properties:
        cost:
          $ref: '#/components/schemas/Cost'
        duration:
          $ref: '#/components/schemas/Duration'
        energy:
          $ref: '#/components/schemas/Energy'
      example:
        cost:
          home:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
          private:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
          public:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
          total:
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
            - amount: 2356
              currency: GBP
        duration:
          home: 680
          private: 170
          public: 170
          total: 850
        energy:
          home:
            generation: 12.5
            grid: 123.4
            total: 135.9
          private:
            generation: 12.5
            grid: 123.4
            total: 135.9
          public:
            generation: 12.5
            grid: 123.4
            total: 135.9
          total:
            generation: 12.5
            grid: 123.4
            total: 135.9
      required:
        - energy
        - cost
        - duration
    SubmitDriverExpensesRequestBody:
      type: object
      properties:
        expenses:
          type: array
          items:
            $ref: '#/components/schemas/SubmitExpenseRequest'
          example:
            - chargeId: c7b9a78a-6008-47d1-843e-15dc585c30df
          minItems: 1
      example:
        expenses:
          - chargeId: c7b9a78a-6008-47d1-843e-15dc585c30df
    SubmitExpenseRequest:
      type: object
      properties:
        chargeId:
          type: string
          description: UUID of the submitted charge.
          example: 738ddcec-ef99-4aa1-bec2-1623f89b309c
          format: uuid
      example:
        chargeId: 3eea2842-32bd-408a-9e82-312397b41ab9
      required:
        - chargeId
    SubmittedCharge:
      type: object
      properties:
        chargeCost:
          type: integer
          description: Charge cost in pence
          example: 123
          format: int64
        chargerName:
          type: string
          description: Public charger name or home charger PSL
          example: Kent-Jake
        duration:
          type: integer
          description: Charge duration in seconds
          example: 123
          format: int64
        endTime:
          type: string
          description: Charge end time UTC
          example: '2022-09-05T17:58:33Z'
        energyUsage:
          type: number
          description: Energy used in KWh
          example: 98.76
          format: double
        id:
          type: integer
          description: Primary key of the charge
          example: 123
          format: int64
        location:
          $ref: '#/components/schemas/SubmittedChargeLocation'
        pluggedInAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
        processedByFullName:
          type: string
          description: Who processed this expense (if processed)
          example: John Smith
        processedTime:
          type: string
          description: Processed time UTC (if processed)
          example: '2022-09-05T14:58:33Z'
        startTime:
          type: string
          description: Charge start time UTC
          example: '2022-09-05T14:58:33Z'
        status:
          type: string
          description: Whether the charge is NEW or PROCESSED
          example: NEW
          enum:
            - NEW
            - PROCESSED
        submittedTime:
          type: string
          description: Submitted for approval time UTC
          example: '2022-09-05T17:58:33Z'
        unpluggedAt:
          type: string
          description: Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.
          example: '2018-05-15T12:00:00+12:00'
      example:
        chargeCost: 123
        chargerName: Kent-Jake
        duration: 123
        endTime: '2022-09-05T17:58:33Z'
        energyUsage: 98.76
        id: 123
        location:
          address:
            country: United Kingdom
            line1: 234 Banner St
            line2: Westminster
            postcode: EC1Y 8QE
            prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
            town: London
          id: 456
          locationType: home
        pluggedInAt: '2018-05-15T12:00:00+12:00'
        processedByFullName: John Smith
        processedTime: '2022-09-05T14:58:33Z'
        startTime: '2022-09-05T14:58:33Z'
        status: NEW
        submittedTime: '2022-09-05T17:58:33Z'
        unpluggedAt: '2018-05-15T12:00:00+12:00'
      required:
        - id
        - startTime
        - endTime
        - submittedTime
        - chargeCost
        - energyUsage
        - duration
        - location
        - chargerName
        - status
    SubmittedChargeAddress:
      type: object
      properties:
        country:
          type: string
          description: Country full name
          example: United Kingdom
        line1:
          type: string
          description: Address line 1
          example: 234 Banner St
        line2:
          type: string
          description: Address line 2
          example: Westminster
        postcode:
          type: string
          description: Postcode
          example: EC1Y 8QE
        prettyPrint:
          type: string
          description: User-friendly string representation of address
          example: 234 Banner St, Westminster, London, EC1Y 8QE
        town:
          type: string
          description: Town
          example: London
      example:
        country: United Kingdom
        line1: 234 Banner St
        line2: Westminster
        postcode: EC1Y 8QE
        prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
        town: London
      required:
        - town
        - line1
        - prettyPrint
    SubmittedChargeLocation:
      type: object
      properties:
        address:
          $ref: '#/components/schemas/SubmittedChargeAddress'
        id:
          type: integer
          description: ID of the charge location
          example: 456
          format: int64
        locationType:
          type: string
          description: 'Type of the location: home or public'
          example: home
          enum:
            - home
            - public
      example:
        address:
          country: United Kingdom
          line1: 234 Banner St
          line2: Westminster
          postcode: EC1Y 8QE
          prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
          town: London
        id: 456
        locationType: home
      required:
        - id
        - locationType
        - address
    SubmittedChargesResponse:
      type: object
      properties:
        driver:
          $ref: '#/components/schemas/Driver'
        submittedCharges:
          type: array
          items:
            $ref: '#/components/schemas/SubmittedCharge'
          example:
            - chargeCost: 123
              chargerName: Kent-Jake
              duration: 123
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              id: 123
              location:
                address:
                  country: United Kingdom
                  line1: 234 Banner St
                  line2: Westminster
                  postcode: EC1Y 8QE
                  prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                  town: London
                id: 456
                locationType: home
              pluggedInAt: '2018-05-15T12:00:00+12:00'
              processedByFullName: John Smith
              processedTime: '2022-09-05T14:58:33Z'
              startTime: '2022-09-05T14:58:33Z'
              status: NEW
              submittedTime: '2022-09-05T17:58:33Z'
              unpluggedAt: '2018-05-15T12:00:00+12:00'
            - chargeCost: 123
              chargerName: Kent-Jake
              duration: 123
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              id: 123
              location:
                address:
                  country: United Kingdom
                  line1: 234 Banner St
                  line2: Westminster
                  postcode: EC1Y 8QE
                  prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                  town: London
                id: 456
                locationType: home
              pluggedInAt: '2018-05-15T12:00:00+12:00'
              processedByFullName: John Smith
              processedTime: '2022-09-05T14:58:33Z'
              startTime: '2022-09-05T14:58:33Z'
              status: NEW
              submittedTime: '2022-09-05T17:58:33Z'
              unpluggedAt: '2018-05-15T12:00:00+12:00'
        totalCost:
          $ref: '#/components/schemas/TotalCost'
        totalUsage:
          $ref: '#/components/schemas/TotalUsage'
      description: List of submitted charges and driver data
      example:
        driver:
          email: <EMAIL>
          firstName: Max
          fullName: Max Verstappen
          id: 123
          lastName: Verstappen
        submittedCharges:
          - chargeCost: 123
            chargerName: Kent-Jake
            duration: 123
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2018-05-15T12:00:00+12:00'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            status: NEW
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2018-05-15T12:00:00+12:00'
          - chargeCost: 123
            chargerName: Kent-Jake
            duration: 123
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2018-05-15T12:00:00+12:00'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            status: NEW
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2018-05-15T12:00:00+12:00'
          - chargeCost: 123
            chargerName: Kent-Jake
            duration: 123
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            id: 123
            location:
              address:
                country: United Kingdom
                line1: 234 Banner St
                line2: Westminster
                postcode: EC1Y 8QE
                prettyPrint: 234 Banner St, Westminster, London, EC1Y 8QE
                town: London
              id: 456
              locationType: home
            pluggedInAt: '2018-05-15T12:00:00+12:00'
            processedByFullName: John Smith
            processedTime: '2022-09-05T14:58:33Z'
            startTime: '2022-09-05T14:58:33Z'
            status: NEW
            submittedTime: '2022-09-05T17:58:33Z'
            unpluggedAt: '2018-05-15T12:00:00+12:00'
        totalCost:
          home: 12300
          public: 45670
        totalUsage:
          home: 12.34
          public: 56.78
          total: 12.34
      required:
        - driver
        - totalUsage
        - totalCost
        - submittedCharges
    SubmittedExpense:
      type: object
      properties:
        chargeId:
          type: string
          description: UUID of the charge associated with the expense.
          example: 2d770a66-3f98-43a6-b30f-d0519f314db4
          format: uuid
        id:
          type: integer
          description: Primary key of the expense.
          example: 456
          format: int64
      example:
        chargeId: 0823abbe-4af7-4a19-bf0e-6b1a1771a32c
        id: 456
      required:
        - id
        - chargeId
    SubmittedExpenseResponse:
      type: object
      properties:
        expenses:
          type: array
          items:
            $ref: '#/components/schemas/SubmittedExpense'
          example:
            - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
              id: 456
            - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
              id: 456
            - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
              id: 456
            - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
              id: 456
      example:
        expenses:
          - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
            id: 456
          - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
            id: 456
          - chargeId: ccfb6246-8b69-4c6a-b019-653e1b155d5b
            id: 456
    TimeRangeOutOfBounds:
      type: object
      properties:
        reason:
          type: string
          example: Et adipisci ipsum aut recusandae.
        status:
          type: integer
          example: 5885470646616663802
          format: int64
      description: TimeRangeOutOfBounds is the error returned when the requested time window exceeds the maximum range.
      example:
        reason: Sequi ut reiciendis est officiis animi nemo.
        status: 3521278625033142951
      required:
        - reason
        - status
    TotalCharges:
      type: object
      properties:
        home:
          type: integer
          description: Number of charges attributed to Home chargers.
          example: 367
          format: int64
        public:
          type: integer
          description: Number of charges attributed to Public chargers.
          example: 367
          format: int64
        total:
          type: integer
          description: Number of charges.
          example: 367
          format: int64
      example:
        home: 367
        public: 367
        total: 367
      required:
        - total
        - home
        - public
    TotalCost:
      type: object
      properties:
        home:
          type: integer
          description: Amount expensed in pence for home charges.
          example: 12300
          format: int64
        public:
          type: integer
          description: Amount expensed in pence for public charges.
          example: 45670
          format: int64
      example:
        home: 12300
        public: 45670
      required:
        - home
        - public
    TotalUsage:
      type: object
      properties:
        home:
          type: number
          description: Total number of kWh used for home charges.
          example: 12.34
          format: double
        public:
          type: number
          description: Total number of kWh used for public charges.
          example: 56.78
          format: double
        total:
          type: number
          description: Total number of kWh used.
          example: 12.34
          format: double
      example:
        home: 12.34
        public: 56.78
        total: 12.34
      required:
        - home
        - public
    TransactionNotStarted:
      type: object
      properties:
        reason:
          type: string
          example: Fuga rem soluta sunt expedita unde.
        status:
          type: integer
          example: 3652059760431991678
          format: int64
      description: TransactionNotStarted is the error returned when a charge cycle transaction was not created as part of OCPI charge authorisation.
      example:
        reason: Eaque sunt architecto suscipit sed.
        status: 7132691231002814303
      required:
        - reason
        - status
    Usage:
      type: object
      properties:
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 2469.32
          format: double
        cost:
          type: integer
          description: Energy cost in pence
          example: 135668
          format: int64
        intervalStartDate:
          type: string
          description: The start date of this usage interval
          example: '2023-06-07'
          format: date
        revenueGenerated:
          type: integer
          description: Revenue generated in pence
          example: 16
          format: int64
        totalUsage:
          type: number
          description: Energy used this period in kWh
          example: 669.2
          format: double
      example:
        co2Savings: 2469.32
        cost: 135668
        intervalStartDate: '2023-06-07'
        revenueGenerated: 16
        totalUsage: 669.2
      required:
        - intervalStartDate
        - totalUsage
        - co2Savings
        - revenueGenerated
        - cost
    UsageResponse:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/Meta'
        usage:
          type: array
          items:
            $ref: '#/components/schemas/Usage'
          example:
            - co2Savings: 2469.32
              cost: 135668
              intervalStartDate: '2023-06-07'
              revenueGenerated: 16
              totalUsage: 669.2
            - co2Savings: 2469.32
              cost: 135668
              intervalStartDate: '2023-06-07'
              revenueGenerated: 16
              totalUsage: 669.2
      description: Basic usage by time periods
      example:
        meta:
          params:
            Consequatur et error hic sunt qui.: Porro quae.
            Eos eos et laudantium.: Reprehenderit vitae consequatur quia molestias.
            Quis et possimus dicta voluptatem ut.: Consequatur possimus esse blanditiis.
        usage:
          - co2Savings: 2469.32
            cost: 135668
            intervalStartDate: '2023-06-07'
            revenueGenerated: 16
            totalUsage: 669.2
          - co2Savings: 2469.32
            cost: 135668
            intervalStartDate: '2023-06-07'
            revenueGenerated: 16
            totalUsage: 669.2
          - co2Savings: 2469.32
            cost: 135668
            intervalStartDate: '2023-06-07'
            revenueGenerated: 16
            totalUsage: 669.2
          - co2Savings: 2469.32
            cost: 135668
            intervalStartDate: '2023-06-07'
            revenueGenerated: 16
            totalUsage: 669.2
      required:
        - meta
    UserCharge:
      type: object
      properties:
        businessName:
          type: string
          description: Business to whom this charge is associated
          example: Pod Point - Software Team
        chargeCost:
          type: integer
          description: Charge cost in pence
          example: 324550
          format: int64
        chargerName:
          type: string
          description: Name of charger associated with charge
          example: Nick-Gary
        chargingDuration:
          type: integer
          description: Charge duration in seconds
          example: 123
          format: int64
        co2Savings:
          type: number
          description: CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
          example: 4325.62
          format: double
        endTime:
          type: string
          description: Charge end time UTC
          example: '2022-09-05T17:58:33Z'
        energyUsage:
          type: number
          description: Energy used in kWh
          example: 98.76
          format: double
        pluggedInDuration:
          type: integer
          description: Plugged-in duration in seconds
          example: 123
          format: int64
        revenueGenerated:
          type: integer
          description: Revenue generated in pence.
          example: 457600
          format: int64
        startTime:
          type: string
          description: Charge start time UTC
          example: '2022-09-05T14:58:33Z'
      example:
        businessName: Pod Point - Software Team
        chargeCost: 324550
        chargerName: Nick-Gary
        chargingDuration: 123
        co2Savings: 4325.62
        endTime: '2022-09-05T17:58:33Z'
        energyUsage: 98.76
        pluggedInDuration: 123
        revenueGenerated: 457600
        startTime: '2022-09-05T14:58:33Z'
      required:
        - chargerName
        - businessName
        - startTime
        - endTime
        - chargingDuration
        - pluggedInDuration
        - energyUsage
        - chargeCost
        - revenueGenerated
        - co2Savings
    UserChargesSchema:
      type: object
      properties:
        charges:
          type: array
          items:
            $ref: '#/components/schemas/UserCharge'
          example:
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
            - businessName: Pod Point - Software Team
              chargeCost: 324550
              chargerName: Nick-Gary
              chargingDuration: 123
              co2Savings: 4325.62
              endTime: '2022-09-05T17:58:33Z'
              energyUsage: 98.76
              pluggedInDuration: 123
              revenueGenerated: 457600
              startTime: '2022-09-05T14:58:33Z'
      description: List of user's charges
      example:
        charges:
          - businessName: Pod Point - Software Team
            chargeCost: 324550
            chargerName: Nick-Gary
            chargingDuration: 123
            co2Savings: 4325.62
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            pluggedInDuration: 123
            revenueGenerated: 457600
            startTime: '2022-09-05T14:58:33Z'
          - businessName: Pod Point - Software Team
            chargeCost: 324550
            chargerName: Nick-Gary
            chargingDuration: 123
            co2Savings: 4325.62
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            pluggedInDuration: 123
            revenueGenerated: 457600
            startTime: '2022-09-05T14:58:33Z'
          - businessName: Pod Point - Software Team
            chargeCost: 324550
            chargerName: Nick-Gary
            chargingDuration: 123
            co2Savings: 4325.62
            endTime: '2022-09-05T17:58:33Z'
            energyUsage: 98.76
            pluggedInDuration: 123
            revenueGenerated: 457600
            startTime: '2022-09-05T14:58:33Z'
    UserNotFound:
      type: object
      description: UserNotFound is the error returned when there is no user found for the given user ID.
      example: {}
tags:
  - name: Charging statistics
    description: Aggregate charging statistics for sites, organisations and chargers
  - name: Charge commands
    description: Commands API for interfacing with event store.
  - name: Driver charges
    description: Retrieve charges for a driver
  - name: Chargers
    description: Data and statistics related to chargers
  - name: drivers
    description: Service for retrieving driver related data.
  - name: Charge Authorisation
    description: API endpoints related to the authorisation steps of beginning/claiming a charge.
  - name: Carbon intensity
    description: API mirroring certain aspects of the Carbon Intensity API from the National Grid - https://carbonintensity.org.uk
  - name: Link user
  - name: Organisation charges
    description: Allows to retrieve charges for an organisation.
  - name: Projection Charges
    description: Retrieve projection charges data
  - name: Projection Group Statistics
    description: Group charging statistics from projections
  - name: User Charges
  - name: sites
    description: Service for retrieving charge stats grouped by site between two dates.
  - name: Charge Statistics
    description: Retrieve aggregated charge data.
  - name: usage
    description: Aggregate basic usage by time periods
