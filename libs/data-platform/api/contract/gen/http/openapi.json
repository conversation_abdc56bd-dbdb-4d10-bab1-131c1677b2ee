{"swagger": "2.0", "info": {"title": "Data Platform API", "description": "Collection of APIs fulfilling needs in the experience domain.", "version": "0.0.1"}, "host": "0.0.0.0:2830", "consumes": ["application/json", "application/xml", "application/gob"], "produces": ["application/json", "application/xml", "application/gob"], "paths": {"/charge-authorisations/{authorisationMethod}": {"post": {"tags": ["Charge Authorisation"], "summary": "authoriseCharge Charge Authorisation", "description": "Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger's group.", "operationId": "Charge Authorisation#authoriseCharge", "parameters": [{"name": "authorisationMethod", "in": "path", "required": true, "type": "string", "enum": ["rfid", "ocpi", "guest"]}, {"name": "AuthoriseChargeRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChargeAuthorisationAuthoriseChargeRequestBody", "required": ["token", "chargerId", "door"]}}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ChargeAuthorisationResponse", "required": ["authorised", "meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/ChargeAuthorisationAuthoriseChargeBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/AuthoriserNotFound", "required": ["reason", "status"]}}, "500": {"description": "Internal Server Error response.", "schema": {"$ref": "#/definitions/TransactionNotStarted", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/chargers/{chargerId}/charge-statistics": {"get": {"tags": ["Charge Statistics"], "summary": "Charger Charge Statistics Charge Statistics", "description": "Charge statistics for a charger, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/charger/{locationId}", "operationId": "Charge Statistics#Charger Charge Statistics", "parameters": [{"name": "chargerId", "in": "path", "description": "ID of the charger (equivalent to PPID).", "required": true, "type": "string", "minLength": 1}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ProjectionchargerChargeStatisticsResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeStatisticsChargerChargeStatisticsBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/chargers/{ppID}/limit": {"get": {"tags": ["Chargers"], "summary": "Retrieve charging limit Chargers", "description": "Retrieve limit for charging", "operationId": "Chargers#Retrieve charging limit", "parameters": [{"name": "ppID", "in": "path", "description": "PPID (PSL number) of a charger", "required": true, "type": "string", "minLength": 1}, {"name": "authoriserUUID", "in": "query", "description": "UUID of charge authoriser - can be user's UUID or app name, like `dcs` or RFID key", "required": true, "type": "string", "minLength": 1}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ChargesLimitResponse", "required": ["allowed", "balance"]}}, "400": {"description": "Returns 400 Bad Request when PPID or authoriser UUID is missing", "schema": {"$ref": "#/definitions/ChargersRetrieveChargingLimitBadRequestResponseBody"}}, "404": {"description": "Returns 404 Not Found, when PPID or authoriser UUID has not been found"}}, "schemes": ["http"]}}, "/chargers/{ppId}/dnoregion": {"get": {"tags": ["Chargers"], "summary": "Retrieve charger region Chargers", "description": "For a given PSL / PPID, return the associated DNO region id.", "operationId": "Chargers#Retrieve charger region", "parameters": [{"name": "ppId", "in": "path", "description": "PPID of a given charger", "required": true, "type": "string"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Region", "required": ["regionid"]}}, "400": {"description": "Returns bad request when a location does not exist for the provided charger psl/ppid."}, "404": {"description": "Returns not found when a DNO region cannot be determined for the provided charger psl/ppid."}, "500": {"description": "Internal Server Error response.", "schema": {"$ref": "#/definitions/ChargersRetrieveChargerRegionInternalServerErrorResponseBody"}}}, "schemes": ["http"]}}, "/charges": {"get": {"tags": ["Projection Charges"], "summary": "Projection Charge Data Projection Charges", "description": "All projection charge data for a group, site or charger between two dates. From the start of the from day provided up to and excluding the to date provided.", "operationId": "Projection Charges#Projection Charge Data", "parameters": [{"name": "groupId", "in": "query", "description": "UUID of the group.", "required": false, "type": "string", "format": "uuid"}, {"name": "siteId", "in": "query", "description": "UUID of the site.", "required": false, "type": "string", "format": "uuid"}, {"name": "chargerId", "in": "query", "description": "Id of the charger.", "required": false, "type": "string"}, {"name": "from", "in": "query", "description": "Query param filter from charge unpluggedAt datetime. The from field is inclusive.", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Query param filter to charge unpluggedAt datetime. The to field is exclusive.", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ProjectionChargesResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ProjectionChargesProjectionChargeDataBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/charges/charger/{locationId}": {"get": {"tags": ["Charging statistics"], "summary": "Charger statistics Charging statistics", "description": "Charge statistics for a single charger between two inclusive dates.", "operationId": "Charging statistics#Charger statistics", "parameters": [{"name": "locationId", "in": "path", "description": "Primary key of the charger location.", "required": true, "type": "integer"}, {"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Chargerchargessummary"}}, "400": {"description": "Returns bad request when date parameters are invalid", "schema": {"$ref": "#/definitions/ChargingStatisticsChargerStatisticsBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/charges/group/{organisationId}": {"get": {"tags": ["Charging statistics"], "summary": "Organisation statistics Charging statistics", "description": "Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the 'from' and 'to' date parameters constitute a valid date range. Where the 'to' date is  prior to  the 'from' date no results will be returned.", "operationId": "Charging statistics#Organisation statistics", "parameters": [{"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Organisationchargessummary"}}, "400": {"description": "Returns bad request when organisation ID is malformed or date parameters are invalid", "schema": {"$ref": "#/definitions/ChargingStatisticsOrganisationStatisticsBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/charges/groups/{groupId}/sites": {"get": {"tags": ["Projection Group Statistics"], "summary": "Group site statistics Projection Group Statistics", "description": "Charge statistics for all sites within a group for the given month.", "operationId": "Projection Group Statistics#Group site statistics", "parameters": [{"name": "groupId", "in": "path", "description": "UUID of the group.", "required": true, "type": "string", "format": "uuid"}, {"name": "year", "in": "query", "description": "Year to be queried.", "required": true, "type": "integer", "minimum": 1}, {"name": "month", "in": "query", "description": "Month to be queried where Jan = 1, Feb = 2 etc...", "required": true, "type": "integer", "maximum": 12, "minimum": 1}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/GroupSitesStatsResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ProjectionGroupStatisticsGroupSiteStatisticsBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/GroupNotFound", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/charges/site/{siteId}": {"get": {"tags": ["Charging statistics"], "summary": "Site statistics Charging statistics", "description": "Charge statistics for a site between two inclusive dates.", "operationId": "Charging statistics#Site statistics", "parameters": [{"name": "siteId", "in": "path", "required": true, "type": "integer", "minimum": 1}, {"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Sitechargessummary"}}, "400": {"description": "Returns bad request when site ID is malformed", "schema": {"$ref": "#/definitions/ChargingStatisticsSiteStatisticsBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/commands/charges/{chargeID}/correct-energy-cost": {"post": {"tags": ["Charge commands"], "summary": "Correct energy cost Charge commands", "description": "Update the energy cost of a charge.", "operationId": "Charge commands#Correct energy cost", "parameters": [{"name": "chargeID", "in": "path", "description": "UUID of the charge to correct.", "required": true, "type": "string", "format": "uuid"}, {"name": "Correct Energy CostRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChargeCommandsCorrectEnergyCostRequestBody", "required": ["cost"]}}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/AggregateCostCorrectedResponse", "required": ["id", "cost"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeCommandsCorrectEnergyCostBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/ChargeNotFound", "required": ["reason", "status"]}}, "409": {"description": "Conflict response.", "schema": {"$ref": "#/definitions/EventOutOfDate", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/commands/charges/{chargeID}/correct-settlement-amount": {"post": {"tags": ["Charge commands"], "summary": "Correct settlement amount Charge commands", "description": "Update the settlement amount of a charge.", "operationId": "Charge commands#Correct settlement amount", "parameters": [{"name": "chargeID", "in": "path", "description": "UUID of the charge to correct.", "required": true, "type": "string", "format": "uuid"}, {"name": "Correct Settlement AmountRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChargeCommandsCorrectSettlementAmountRequestBody", "required": ["settlementAmount"]}}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/AggregateSettlementAmountCorrectedResponse", "required": ["id", "settlementAmount"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeCommandsCorrectSettlementAmountBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/ChargeNotFound", "required": ["reason", "status"]}}, "409": {"description": "Conflict response.", "schema": {"$ref": "#/definitions/EventOutOfDate", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/dno-regions": {"get": {"tags": ["Chargers"], "summary": "Retrieve DNO regions Chargers", "description": "Retrieve the complete collection of DNO region objects.", "operationId": "Chargers#Retrieve DNO regions", "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Regions", "required": ["data"]}}, "500": {"description": "Internal Server Error response.", "schema": {"$ref": "#/definitions/ChargersRetrieveDNOregionsInternalServerErrorResponseBody"}}}, "schemes": ["http"]}}, "/drivers/{driverId}/charges": {"get": {"tags": ["drivers"], "summary": "retrieveCharges drivers", "description": "Retrieve charge details for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn't present.", "operationId": "drivers#retrieveCharges", "parameters": [{"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}, {"name": "driverId", "in": "path", "description": "UUID of the driver", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/DriversChargesResponse", "required": ["data", "meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/DriversRetrieveChargesBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/drivers/{driverId}/charges/{chargeId}": {"get": {"tags": ["drivers"], "summary": "retrieveCharge drivers", "description": "Retrieve charge details.", "operationId": "drivers#retrieveCharge", "parameters": [{"name": "driverId", "in": "path", "description": "ID for the driver", "required": true, "type": "string", "format": "uuid"}, {"name": "chargeId", "in": "path", "description": "Charge ID", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/DriversChargeResponse", "required": ["data", "meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/DriversRetrieveChargeBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/DriversChargeNotFound", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/drivers/{driverId}/groups/{organisationId}/expenses": {"post": {"tags": ["Driver charges"], "summary": "Submit driver expenses Driver charges", "description": "Submitting a list of charges as expenses for a driver within a group.", "operationId": "Driver charges#Submit driver expenses", "parameters": [{"name": "driverId", "in": "path", "description": "UUID of the driver.", "required": true, "type": "string", "format": "uuid"}, {"name": "organisationId", "in": "path", "required": true, "type": "integer", "minimum": 1}, {"name": "Submit Driver ExpensesRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DriverChargesSubmitDriverExpensesRequestBody"}}], "responses": {"201": {"description": "Created response.", "schema": {"$ref": "#/definitions/SubmittedExpenseResponse"}}, "400": {"description": "Driver ID, Group ID or Expenses payload is malformed.", "schema": {"$ref": "#/definitions/DriverChargesSubmitDriverExpensesBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/ChargeNotFound", "required": ["reason", "status"]}}, "409": {"description": "Conflict response."}}, "schemes": ["http"]}}, "/drivers/{driverId}/organisations/{organisationId}/expenses": {"post": {"tags": ["Driver charges"], "summary": "Create driver expenses Driver charges", "description": "Submitting a list of charges as expenses for a driver within an organisation.", "operationId": "Driver charges#Create driver expenses", "parameters": [{"name": "driverId", "in": "path", "required": true, "type": "integer", "minimum": 1}, {"name": "organisationId", "in": "path", "required": true, "type": "integer", "minimum": 1}, {"name": "Create Driver ExpensesRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DriverChargesCreateDriverExpensesRequestBody"}}], "responses": {"201": {"description": "Created response.", "schema": {"$ref": "#/definitions/CreatedExpenseResponse"}}, "400": {"description": "Driver ID, Organisation ID or Expenses payload is malformed.", "schema": {"$ref": "#/definitions/DriverChargesCreateDriverExpensesBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/ChargeNotFound", "required": ["reason", "status"]}}, "409": {"description": "Conflict response."}}, "schemes": ["http"]}}, "/drivers/{driverId}/stats": {"get": {"tags": ["drivers"], "summary": "retrieveStats drivers", "description": "Retrieve driver stats for a given time range, sorted by started_at in descending order, falling back to plugged_in_at when necessary. When filtering, we first look for started_at and otherwise use the passed times to filter by plugged_in_at if started_at wasn't present. ", "operationId": "drivers#retrieveStats", "parameters": [{"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}, {"name": "interval", "in": "query", "description": "Time duration interval data should be provided in.", "required": false, "type": "string", "enum": ["day", "week", "month"]}, {"name": "driverId", "in": "path", "description": "UUID of the driver", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/DriverStatsResponse", "required": ["data", "meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/DriversRetrieveStatsBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/groups/{groupId}/charge-statistics": {"get": {"tags": ["Charge Statistics"], "summary": "Group Charge Statistics Charge Statistics", "description": "Charge statistics for a group from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}", "operationId": "Charge Statistics#Group Charge Statistics", "parameters": [{"name": "groupId", "in": "path", "description": "UUID of the group.", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ProjectiongroupChargeStatisticsResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeStatisticsGroupChargeStatisticsBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/groups/{groupId}/charge-statistics/{interval}": {"get": {"tags": ["Charge Statistics"], "summary": "Group Usage Summaries Charge Statistics", "description": "Usage summaries for all sites within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats", "operationId": "Charge Statistics#Group Usage Summaries", "parameters": [{"name": "groupId", "in": "path", "description": "UUID of the group.", "required": true, "type": "string", "format": "uuid"}, {"name": "interval", "in": "path", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeStatisticsGroupUsageSummariesBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/GroupNotFound", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/groups/{groupId}/chargers/{chargerId}/charge-statistics/{interval}": {"get": {"tags": ["Charge Statistics"], "summary": "Group and Charger Usage Summaries Charge Statistics", "description": "Usage summaries for a charger within a group, sorted by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/chargers/{locationId}/stats", "operationId": "Charge Statistics#Group and Charger Usage Summaries", "parameters": [{"name": "groupId", "in": "path", "description": "UUID of the group.", "required": true, "type": "string", "format": "uuid"}, {"name": "chargerId", "in": "path", "description": "ID of the charger (equivalent to PPID).", "required": true, "type": "string", "minLength": 1}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "interval", "in": "path", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/ChargeStatisticsGroupAndChargerUsageSummariesBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/groups/{groupId}/sites/{siteId}/charge-statistics/{interval}": {"get": {"tags": ["Charge Statistics"], "summary": "Group and Site Usage Summaries Charge Statistics", "description": "Usage summaries for a site within the group, grouped by the given interval.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/sites/{siteId}/stats", "operationId": "Charge Statistics#Group and Site Usage Summaries", "parameters": [{"name": "groupId", "in": "path", "description": "UUID of the group.", "required": true, "type": "string", "format": "uuid"}, {"name": "siteId", "in": "path", "description": "UUID of the site.", "required": true, "type": "string", "format": "uuid"}, {"name": "interval", "in": "path", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeStatisticsGroupAndSiteUsageSummariesBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/groups/{groupId}/users/{userId}/charges": {"get": {"tags": ["User Charges"], "summary": "Group And User Charges User Charges", "description": "Charges of a user within the group, derived from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /organisations/{organisationId}/drivers/{userId}/charges", "operationId": "User Charges#Group And User Charges", "parameters": [{"name": "groupId", "in": "path", "description": "The UUID of the group within which charges for the associated user have taken place", "required": true, "type": "string", "format": "uuid"}, {"name": "userId", "in": "path", "description": "The UUID of the user", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "description": "Query param filter from charge endsAt datetime", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Query param filter to charge endsAt datetime", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ProjectionGroupAndUserChargesResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/UserChargesGroupAndUserChargesBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/link-user/{userId}/charger/{ppId}": {"post": {"tags": ["Link user"], "summary": "Link user to home charger Link user", "operationId": "Link user#Link user to home charger", "parameters": [{"name": "ppId", "in": "path", "description": "PPID (PSL number) of a charger", "required": true, "type": "string"}, {"name": "userId", "in": "path", "description": "The UUID of the user", "required": true, "type": "string", "format": "uuid"}], "responses": {"202": {"description": "Accepted response."}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/LinkUserLinkUserToHomeChargerBadRequestResponseBody"}}, "404": {"description": "Not Found response."}, "500": {"description": "Internal Server Error response.", "schema": {"$ref": "#/definitions/LinkUserLinkUserToHomeChargerInternalServerErrorResponseBody"}}}, "schemes": ["http"]}}, "/organisations/{organisationId}/chargers/{locationId}/stats": {"get": {"tags": ["usage"], "summary": "Usage by organisation and charger usage", "description": "Charger usage between two inclusive dates", "operationId": "usage#Usage by organisation and charger", "parameters": [{"name": "organisationId", "in": "path", "description": "UUID of the organisation", "required": true, "type": "string", "format": "uuid"}, {"name": "locationId", "in": "path", "description": "Primary key of the charger location from podadmin", "required": true, "type": "integer", "format": "int32"}, {"name": "from", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "interval", "in": "query", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/UsageUsageByOrganisationAndChargerBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/drivers/stats": {"post": {"tags": ["Driver charges"], "summary": "Retrieve organisation drivers statistics Driver charges", "description": "Retrieve charge statistics for given organisation drivers", "operationId": "Driver charges#Retrieve organisation drivers statistics", "parameters": [{"name": "from", "in": "query", "description": "Query param filter from charge endsAt datetime", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Query param filter to charge endsAt datetime", "required": true, "type": "string", "format": "date"}, {"name": "organisationId", "in": "path", "description": "The UUID of the group within which charges for the associated drivers have taken place", "required": true, "type": "string", "format": "uuid"}, {"name": "Retrieve Organisation Drivers StatisticsRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DriverChargesRetrieveOrganisationDriversStatisticsRequestBody", "required": ["driverIds"]}}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/OrganisationDriversChargeStatisticsResponse"}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/DriverChargesRetrieveOrganisationDriversStatisticsBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/drivers/{userId}/charges": {"get": {"tags": ["Driver charges"], "summary": "Retrieve many Driver charges", "description": "Retrieve charges for a driver", "operationId": "Driver charges#Retrieve many", "parameters": [{"name": "from", "in": "query", "description": "Query param filter from charge endsAt datetime", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Query param filter to charge endsAt datetime", "required": true, "type": "string", "format": "date"}, {"name": "organisationId", "in": "path", "description": "The UUID of the group within which charges for the associated user have taken place", "required": true, "type": "string", "format": "uuid"}, {"name": "userId", "in": "path", "description": "The UUID of the user", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/DriverChargesResponse"}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/DriverChargesRetrieveManyBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/fleet-usage": {"get": {"tags": ["Organisation charges"], "summary": "Fleet usage by organisation Organisation charges", "description": "This calendars month view of when the charges have been completed. It will only show the charges the drivers have submitted to expense regardless of the state.", "operationId": "Organisation charges#Fleet usage by organisation", "parameters": [{"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/FleetUsageResponse", "required": ["totalCharges", "totalUsage", "co2Savings", "numberOfDrivers"]}}, "400": {"description": "Returns bad request when org ID is malformed", "schema": {"$ref": "#/definitions/OrganisationChargesFleetUsageByOrganisationBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/process-charges": {"post": {"tags": ["Organisation charges"], "summary": "<PERSON> submitted charges as processed Organisation charges", "description": "<PERSON> submitted charges as processed", "operationId": "Organisation charges#Mark submitted charges as processed", "parameters": [{"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "Mark Submitted Charges As ProcessedRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/OrganisationChargesMarkSubmittedChargesAsProcessedRequestBody", "required": ["approverId", "submittedChargeIds"]}}], "responses": {"200": {"description": "OK response."}, "400": {"description": "Bad Request response."}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/sites/{siteId}/stats": {"get": {"tags": ["usage"], "summary": "Usage by organisation and site usage", "description": "Site usage between two inclusive dates", "operationId": "usage#Usage by organisation and site", "parameters": [{"name": "organisationId", "in": "path", "description": "UUID of the organisation", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "interval", "in": "query", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}, {"name": "siteId", "in": "path", "description": "Site ID - primary key of the podadmin pod_addresses table", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/UsageUsageByOrganisationAndSiteBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/stats": {"get": {"tags": ["usage"], "summary": "Usage by organisation usage", "description": "Organisation usage between two inclusive dates", "operationId": "usage#Usage by organisation", "parameters": [{"name": "organisationId", "in": "path", "description": "UUID of the organisation", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "required": true, "type": "string", "format": "date"}, {"name": "interval", "in": "query", "description": "Reporting interval", "required": true, "type": "string", "enum": ["day", "week", "month"]}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/UsageResponse", "required": ["meta"]}}, "400": {"description": "Bad Request response.", "schema": {"$ref": "#/definitions/UsageUsageByOrganisationBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/submitted-charges": {"get": {"tags": ["Organisation charges"], "summary": "Expenses by organisation Organisation charges", "description": "Get all expensable charges per organisation between two dates (maximum 1 month).", "operationId": "Organisation charges#Expenses by organisation", "parameters": [{"name": "status", "in": "query", "description": "Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.", "required": false, "type": "string", "enum": ["NEW", "PROCESSED"]}, {"name": "from", "in": "query", "description": "Charges expensed from and including this date will be returned.", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Charges expensed up to and not including this date will be returned.", "required": true, "type": "string", "format": "date"}, {"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/OrganisationChargesResponse", "required": ["expensableCharges"]}}, "400": {"description": "Returns bad request when org ID is malformed", "schema": {"$ref": "#/definitions/OrganisationChargesExpensesByOrganisationBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/submitted-charges/drivers": {"get": {"tags": ["Organisation charges"], "summary": "Expenses by organisation, grouped by driver Organisation charges", "description": "Get all expensable charges per organisation between two dates (maximum 1 month). This result set will be grouped per-user.", "operationId": "Organisation charges#Expenses by organisation, grouped by driver", "parameters": [{"name": "status", "in": "query", "description": "Filters the submitted expenses based on their status. Omitting this will return all submitted expenses.", "required": false, "type": "string", "enum": ["NEW", "PROCESSED"]}, {"name": "from", "in": "query", "description": "Charges expensed from and including this date will be returned.", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Charges expensed up to and not including this date will be returned.", "required": true, "type": "string", "format": "date"}, {"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/OrganisationChargesDriverSummaryResponse", "required": ["driverExpensableChargeSummaries"]}}, "400": {"description": "Returns bad request when org ID is malformed", "schema": {"$ref": "#/definitions/OrganisationChargesExpensesByOrganisationGroupedByDriverBadRequestResponseBody"}}, "404": {"description": "Not Found response."}}, "schemes": ["http"]}}, "/organisations/{organisationId}/submitted-charges/{driverId}": {"get": {"tags": ["Organisation charges"], "summary": "Submitted charges for driver Organisation charges", "description": "Retrieve submitted charges for a driver", "operationId": "Organisation charges#Submitted charges for driver", "parameters": [{"name": "organisationId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "driverId", "in": "path", "required": true, "type": "integer", "minimum": 1}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/SubmittedChargesResponse", "required": ["driver", "totalUsage", "totalCost", "submittedCharges"]}}, "400": {"description": "Returns bad request when org ID is malformed", "schema": {"$ref": "#/definitions/OrganisationChargesSubmittedChargesForDriverBadRequestResponseBody"}}, "404": {"description": "Not Found response.", "schema": {"$ref": "#/definitions/DriverNotFound", "required": ["reason", "status"]}}}, "schemes": ["http"]}}, "/regional/intensity/{from}/fw48h/regionid/{regionId}": {"get": {"tags": ["Carbon intensity"], "summary": "Retrieve regional forecast 48 hours from date Carbon intensity", "description": "Retrieve half-hourly forecast data 48 hours from provided date.", "operationId": "Carbon intensity#Retrieve regional forecast 48 hours from date", "parameters": [{"name": "from", "in": "path", "description": "Datetime is in ISO8601 and RFC3339 compliant format.", "required": true, "type": "string"}, {"name": "regionId", "in": "path", "description": "Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.", "required": true, "type": "integer"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/Forecast", "required": ["data"]}}, "400": {"description": "Bad Request response."}, "500": {"description": "Internal Server Error response.", "schema": {"$ref": "#/definitions/CarbonIntensityRetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody"}}}, "schemes": ["http"]}}, "/sites": {"get": {"tags": ["sites"], "summary": "retrieveChargeStatsGroupedBySite sites", "description": "Retrieve charge information by site.", "operationId": "sites#retrieveChargeStatsGroupedBySite", "parameters": [{"name": "from", "in": "query", "description": "Statistics report inclusive start date eg: 2022-01-01", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Statistics report inclusive end date eg: 2022-01-31", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/SiteStatsResponse", "required": ["count", "from", "to", "data"]}}, "400": {"description": "Returns bad request when date is invalid", "schema": {"$ref": "#/definitions/SitesRetrieveChargeStatsGroupedBySiteBadRequestResponseBody"}}}, "schemes": ["http"]}}, "/sites/{siteId}/charge-statistics": {"get": {"tags": ["Charge Statistics"], "summary": "Site Charge Statistics Charge Statistics", "description": "Charge statistics for a site from projections.<br><br>The from and to attributes are used to filter on the unpluggedAt field. Both the from and to fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}", "operationId": "Charge Statistics#Site Charge Statistics", "parameters": [{"name": "siteId", "in": "path", "description": "UUID of the site.", "required": true, "type": "string", "format": "uuid"}, {"name": "from", "in": "query", "description": "Inclusive from date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}, {"name": "to", "in": "query", "description": "Inclusive to date used for filtering on unpluggedAt", "required": true, "type": "string", "format": "date"}], "responses": {"200": {"description": "OK response.", "schema": {"$ref": "#/definitions/ProjectionsiteChargeStatisticsResponse", "required": ["data", "meta"]}}, "400": {"description": "Returns bad request when request parameters are invalid", "schema": {"$ref": "#/definitions/ChargeStatisticsSiteChargeStatisticsBadRequestResponseBody"}}}, "schemes": ["http"]}}}, "definitions": {"AggregateCostCorrectedResponse": {"title": "AggregateCostCorrectedResponse", "type": "object", "properties": {"cost": {"type": "integer", "description": "New cost of the charge in lowest denomination of its currency (pence, euro cents).", "example": 16, "format": "int64"}, "id": {"type": "string", "description": "UUID of the charge.", "example": "81e3d6d3-75df-4aba-9ede-fc0467930b88", "format": "uuid"}, "submittedBy": {"type": "string", "description": "Who has submitted the correction request.", "example": "<PERSON>"}}, "example": {"cost": 16, "id": "38b6e87f-**************-62014991d67a", "submittedBy": "<PERSON>"}, "required": ["id", "cost"]}, "AggregateSettlementAmountCorrectedResponse": {"title": "AggregateSettlementAmountCorrectedResponse", "type": "object", "properties": {"id": {"type": "string", "description": "UUID of the charge.", "example": "1d75236c-6dbb-4730-8bc3-2018cddc8348", "format": "uuid"}, "settlementAmount": {"type": "integer", "description": "New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).", "example": 16, "format": "int64"}, "submittedBy": {"type": "string", "description": "Who has submitted the correction request.", "example": "<PERSON>"}}, "example": {"id": "f0ed7459-9a13-453b-b9f9-c1d9a0d88c8b", "settlementAmount": 16, "submittedBy": "<PERSON>"}, "required": ["id", "settlementAmount"]}, "AuthoriserNotFound": {"title": "AuthoriserNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Sit ipsam nesciunt asperiores."}, "status": {"type": "integer", "example": 3790388471575744703, "format": "int64"}}, "example": {"reason": "Nobis repellendus.", "status": 2360600983228098248}, "required": ["reason", "status"]}, "Balance": {"title": "Balance", "type": "object", "properties": {"actual": {"type": "integer", "description": "Actual user balance (or guest pre-authorised amount) in pence/cents/øre", "example": 75, "format": "int64"}, "currency": {"type": "string", "description": "Balance currency code", "example": "GBP", "enum": ["GBP", "EUR", "NOK"]}, "minimum": {"type": "integer", "description": "Minimum balance required to start charge in pence/cents/øre", "example": 50, "format": "int64"}}, "example": {"actual": 75, "currency": "GBP", "minimum": 50}}, "Breakdown": {"title": "Breakdown", "type": "object", "properties": {"generation": {"type": "number", "description": "Generation energy in kWh", "example": 12.5, "format": "double"}, "grid": {"type": "number", "description": "Grid energy in kWh", "example": 123.4, "format": "double"}, "total": {"type": "number", "description": "Total energy in kWh", "example": 135.9, "format": "double"}}, "example": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "required": ["total"]}, "CarbonIntensityRetrieveRegionalForecast48HoursFromDateInternalServerErrorResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Retrieve regional forecast 48 hours from date_internal_server_error_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Charge": {"title": "Charge", "type": "object", "properties": {"charger": {"$ref": "#/definitions/Charger"}, "cost": {"$ref": "#/definitions/Money"}, "duration": {"type": "integer", "description": "Duration of charging in seconds.", "example": 22245, "format": "int64"}, "endedAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}, "energyTotal": {"type": "number", "description": "Energy used in kWh.", "example": 567.89, "format": "double"}, "expensedTo": {"$ref": "#/definitions/ExpensedTo"}, "generationEnergyTotal": {"type": "number", "description": "Energy used in kWh that was generated e.g. Solar.", "example": 67.89, "format": "double"}, "gridEnergyTotal": {"type": "number", "description": "Energy used in kWh that is imported from the grid.", "example": 500, "format": "double"}, "id": {"type": "string", "description": "Charges unique identifier.", "example": "2383ff5b-796b-403a-8199-a07ae9e2af00", "format": "uuid"}, "startedAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}}, "example": {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "62b28ca4-d30c-4040-b14a-9b5cd91e0983", "startedAt": "2018-05-15T12:00:00+12:00"}, "required": ["id", "charger"]}, "ChargeAuthorisationAuthoriseChargeBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "authoriseCharge_bad_request_response_body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeAuthorisationAuthoriseChargeInternalServerErrorResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "authoriseCharge_internal_server_error_response_body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeAuthorisationAuthoriseChargeRequestBody": {"title": "ChargeAuthorisationAuthoriseChargeRequestBody", "type": "object", "properties": {"chargerId": {"type": "string", "description": "PPID (PSL number) of a charger", "example": "PP-12345"}, "door": {"type": "string", "description": "Charger door. A, B or C", "example": "A", "enum": ["A", "B", "C"]}, "token": {"type": "string", "description": "RFID card token, OCPI token, or Billing event ID for guest authorisation.", "example": "0DA46GKEFP3"}}, "example": {"chargerId": "PP-12345", "door": "A", "token": "0DA46GKEFP3"}, "required": ["token", "chargerId", "door"]}, "ChargeAuthorisationResponse": {"title": "ChargeAuthorisationResponse", "type": "object", "properties": {"authorised": {"type": "boolean", "description": "Whether the charge is authorised.", "example": true}, "id": {"type": "string", "description": "UUID representing the charge authorisation, present if authorisation was successful.", "example": "1e9c61b2-2cf6-4256-a873-441dda291c0f", "format": "uuid"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"authorised": true, "id": "856cc71c-33fd-453f-b91d-4b32c2f005bd", "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["authorised", "meta"]}, "ChargeCommandsCorrectEnergyCostBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Correct energy cost_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeCommandsCorrectEnergyCostRequestBody": {"title": "ChargeCommandsCorrectEnergyCostRequestBody", "type": "object", "properties": {"cost": {"type": "integer", "description": "New cost of the charge in lowest denomination of its currency (pence, euro cents).", "example": 16, "format": "int64", "minimum": 1}, "submittedBy": {"type": "string", "description": "Who has submitted the correction request.", "example": "<PERSON>"}}, "example": {"cost": 16, "submittedBy": "<PERSON>"}, "required": ["cost"]}, "ChargeCommandsCorrectSettlementAmountBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Correct settlement amount_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeCommandsCorrectSettlementAmountRequestBody": {"title": "ChargeCommandsCorrectSettlementAmountRequestBody", "type": "object", "properties": {"settlementAmount": {"type": "integer", "description": "New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).", "example": 16, "format": "int64", "minimum": 1}, "submittedBy": {"type": "string", "description": "Who has submitted the correction request.", "example": "<PERSON>"}}, "example": {"settlementAmount": 16, "submittedBy": "<PERSON>"}, "required": ["settlementAmount"]}, "ChargeEnergySummary": {"title": "ChargeEnergySummary", "type": "object", "properties": {"claimedUsage": {"type": "number", "description": "Energy used this period in kWh, filtering out any unclaimed charges.", "example": 243.89, "format": "double"}, "cost": {"type": "integer", "description": "Energy cost in pence.", "example": 324550, "format": "int64"}, "revenueGeneratingClaimedUsage": {"type": "number", "description": "Energy used this period in kWh by claimed charges with a positive revenue.", "example": 43.5, "format": "double"}, "totalUsage": {"type": "number", "description": "Energy used this period in kWh.", "example": 567.89, "format": "double"}, "unclaimedUsage": {"type": "number", "description": "Energy used this period in kWh, filtering out any claimed charges.", "example": 324.65, "format": "double"}}, "example": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "required": ["totalUsage", "claimedUsage", "revenueGeneratingClaimedUsage", "unclaimedUsage", "cost"]}, "ChargeExpensed": {"title": "ChargeExpensed", "type": "object", "properties": {"reason": {"type": "string", "example": "Porro esse."}, "status": {"type": "integer", "example": 3923184348611149699, "format": "int64"}}, "example": {"reason": "Iusto voluptatem officiis voluptate.", "status": 7755924034407962702}, "required": ["reason", "status"]}, "ChargeNotFound": {"title": "ChargeNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Quod adip<PERSON><PERSON>."}, "status": {"type": "integer", "example": 4171746218123302411, "format": "int64"}}, "example": {"reason": "Laudantium sed aut minima.", "status": 8792343440128344110}, "required": ["reason", "status"]}, "ChargeStatisticsChargerChargeStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Charger Charge Statistics_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeStatisticsGroupAndChargerUsageSummariesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Group and Charger Usage Summaries_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeStatisticsGroupAndSiteUsageSummariesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Group and Site Usage Summaries_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeStatisticsGroupChargeStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Group Charge Statistics_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeStatisticsGroupUsageSummariesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Group Usage Summaries_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargeStatisticsSiteChargeStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Site Charge Statistics_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Charger": {"title": "Charger", "type": "object", "properties": {"door": {"type": "string", "description": "Charger door used.", "example": "A"}, "id": {"type": "string", "description": "Chargers unique identifier.", "example": "veefil-602300633"}, "name": {"type": "string", "description": "Chargers user-friendly name.", "example": "Amoy-Reef"}, "pluggedInAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}, "pluggedInDuration": {"type": "integer", "description": "Duration a charger has been in use in seconds.", "example": 3600, "format": "int64"}, "siteName": {"type": "string", "description": "Name of the site where the charger is located.", "example": "Ecclestone Court Car Park"}, "type": {"type": "string", "description": "Type of charger.", "example": "home", "enum": ["public", "private", "home"]}, "unpluggedAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}}, "example": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "required": ["id", "door", "type"]}, "ChargerChargeStatistics": {"title": "ChargerChargeStatistics", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "sum of charging duration in seconds", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/EnergyStatistics"}, "numberOfCharges": {"type": "integer", "description": "count of distinct charge uuids", "example": 10, "format": "int64"}, "numberOfUsers": {"type": "integer", "description": "count of distinct user ids", "example": 2, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "sum of settlement amount in pence", "example": 457600, "format": "int64"}}, "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfCharges": 10, "numberOfUsers": 2, "revenueGenerated": 457600}, "required": ["numberOfCharges", "numberOfUsers", "chargingDuration", "revenueGenerated", "co2Savings", "energy"]}, "ChargerPpidNotFound": {"title": "ChargerPpidNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Sit sunt nostrum nihil deserunt est provident."}, "status": {"type": "integer", "example": 4295929835828227431, "format": "int64"}}, "example": {"reason": "Voluptatem ipsam sit omnis.", "status": 1806521058530705233}, "required": ["reason", "status"]}, "Chargerchargessummary": {"title": "Mediatype identifier: chargerchargessummary; view=default", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "Total duration of charging in seconds.", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/ChargeEnergySummary"}, "numberOfCharges": {"type": "integer", "description": "Total number of charges.", "example": 2048, "format": "int64"}, "numberOfDrivers": {"type": "integer", "description": "Total number of unique drivers.", "example": 64, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}}, "description": "Charger StatisticsResponseBody result type (default view)", "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600}, "required": ["numberOfCharges", "numberOfDrivers", "chargingDuration", "revenueGenerated", "co2Savings"]}, "ChargersRetrieveChargerRegionInternalServerErrorResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Retrieve charger region_internal_server_error_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargersRetrieveChargingLimitBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Retrieve charging limit_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargersRetrieveDNOregionsInternalServerErrorResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Retrieve DNO regions_internal_server_error_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Charges": {"title": "Charges", "type": "object", "properties": {"charges": {"type": "array", "items": {"$ref": "#/definitions/Charge"}, "description": "Charge data for driver.", "example": [{"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}]}, "count": {"type": "integer", "description": "Count of charges returned.", "example": 22245, "format": "int64"}}, "example": {"charges": [{"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}], "count": 22245}, "required": ["count", "charges"]}, "ChargesLimitResponse": {"title": "ChargesLimitResponse", "type": "object", "properties": {"allowed": {"type": "boolean", "description": "Indicator of whether charge is allowed", "example": true}, "balance": {"$ref": "#/definitions/Balance"}, "limits": {"type": "array", "items": {"$ref": "#/definitions/Limit"}, "example": [{"amount": 1.23, "type": "energy", "unit": "kWh"}, {"amount": 1.23, "type": "energy", "unit": "kWh"}, {"amount": 1.23, "type": "energy", "unit": "kWh"}]}}, "example": {"allowed": true, "balance": {"actual": 75, "currency": "GBP", "minimum": 50}, "limits": [{"amount": 1.23, "type": "energy", "unit": "kWh"}, {"amount": 1.23, "type": "energy", "unit": "kWh"}, {"amount": 1.23, "type": "energy", "unit": "kWh"}, {"amount": 1.23, "type": "energy", "unit": "kWh"}]}, "required": ["allowed", "balance"]}, "ChargingStatisticsChargerStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Charger statistics_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargingStatisticsOrganisationStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Organisation statistics_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ChargingStatisticsSiteStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Site statistics_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Cost": {"title": "Cost", "type": "object", "properties": {"home": {"type": "array", "items": {"$ref": "#/definitions/MoneyInt64"}, "description": "Total cost of all charges at home chargers in pence/cent/ore", "example": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "private": {"type": "array", "items": {"$ref": "#/definitions/MoneyInt64"}, "description": "Total cost of all chargers at private chargers in pence/cent/ore", "example": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "public": {"type": "array", "items": {"$ref": "#/definitions/MoneyInt64"}, "description": "Total cost of all chargers at public chargers in pence/cent/ore", "example": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "total": {"type": "array", "items": {"$ref": "#/definitions/MoneyInt64"}, "description": "Total cost of all charges in pence/cent/ore", "example": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}}, "example": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "required": ["home", "private", "public", "total"]}, "CreateExpenseRequest": {"title": "CreateExpenseRequest", "type": "object", "properties": {"chargeId": {"type": "integer", "description": "Primary key of the submitted charge.", "example": 123, "format": "int64"}}, "example": {"chargeId": 123}, "required": ["chargeId"]}, "CreatedExpense": {"title": "CreatedExpense", "type": "object", "properties": {"chargeId": {"type": "integer", "description": "ID of the charge associated with the expense.", "example": 123, "format": "int64"}, "id": {"type": "integer", "description": "Primary key of the expense.", "example": 456, "format": "int64"}}, "example": {"chargeId": 123, "id": 456}, "required": ["id", "chargeId"]}, "CreatedExpenseResponse": {"title": "CreatedExpenseResponse", "type": "object", "properties": {"expenses": {"type": "array", "items": {"$ref": "#/definitions/CreatedExpense"}, "example": [{"chargeId": 123, "id": 456}, {"chargeId": 123, "id": 456}]}}, "example": {"expenses": [{"chargeId": 123, "id": 456}, {"chargeId": 123, "id": 456}, {"chargeId": 123, "id": 456}]}}, "Dnoregion": {"title": "Dnoregion", "type": "object", "properties": {"dnoregion": {"type": "string", "description": "DNO region full name.", "example": "Scottish Hydro Electric Power Distribution"}, "regionid": {"type": "integer", "description": "National grid DNO region id.", "example": 1, "format": "int64"}, "shortname": {"type": "string", "description": "DNO region short name.", "example": "North Scotland"}}, "example": {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, "required": ["regionid", "dnoregion", "shortname"]}, "Driver": {"title": "Driver", "type": "object", "properties": {"email": {"type": "string", "description": "Email address of the driver.", "example": "<EMAIL>"}, "firstName": {"type": "string", "description": "First name of the driver.", "example": "Max"}, "fullName": {"type": "string", "description": "Full name of the driver.", "example": "<PERSON>"}, "id": {"type": "integer", "description": "Primary key of the driver.", "example": 123, "format": "int64"}, "lastName": {"type": "string", "description": "Last name of the driver.", "example": "Verstappen"}}, "example": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "required": ["id", "firstName", "lastName", "fullName", "email"]}, "DriverCharge": {"title": "DriverCharge", "type": "object", "properties": {"businessName": {"type": "string", "description": "Business to whom this charge is associated", "example": "Pod Point - Software Team"}, "chargeCost": {"type": "integer", "description": "Charge cost in pence", "example": 324550, "format": "int64"}, "chargerName": {"type": "string", "description": "Name of charger associated with charge", "example": "<PERSON><PERSON><PERSON>"}, "chargingDuration": {"type": "integer", "description": "Charge duration in seconds", "example": 123, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "endTime": {"type": "string", "description": "Charge end time UTC", "example": "2022-09-05T17:58:33Z"}, "energyUsage": {"type": "number", "description": "Energy used in kWh", "example": 98.76, "format": "double"}, "pluggedInDuration": {"type": "integer", "description": "Plugged-in duration in seconds", "example": 123, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}, "startTime": {"type": "string", "description": "Charge start time UTC", "example": "2022-09-05T14:58:33Z"}}, "example": {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, "required": ["chargerName", "businessName", "startTime", "endTime", "chargingDuration", "pluggedInDuration", "energyUsage", "chargeCost", "revenueGenerated", "co2Savings"]}, "DriverChargesCreateDriverExpensesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Create driver expenses_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriverChargesCreateDriverExpensesRequestBody": {"title": "DriverChargesCreateDriverExpensesRequestBody", "type": "object", "properties": {"expenses": {"type": "array", "items": {"$ref": "#/definitions/CreateExpenseRequest"}, "example": [{"chargeId": 123}, {"chargeId": 123}], "minItems": 1}}, "example": {"expenses": [{"chargeId": 123}, {"chargeId": 123}, {"chargeId": 123}]}}, "DriverChargesResponse": {"title": "DriverChargesResponse", "type": "object", "properties": {"charges": {"type": "array", "items": {"$ref": "#/definitions/DriverCharge"}, "example": [{"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}]}}, "example": {"charges": [{"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}]}}, "DriverChargesRetrieveManyBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Retrieve many_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriverChargesRetrieveOrganisationDriversStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Retrieve organisation drivers statistics_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriverChargesRetrieveOrganisationDriversStatisticsRequestBody": {"title": "DriverChargesRetrieveOrganisationDriversStatisticsRequestBody", "type": "object", "properties": {"driverIds": {"type": "array", "items": {"type": "string", "example": "7dd62567-d894-4cd4-a8bf-6a282f0b9187", "format": "uuid"}, "example": ["12d9423b-8a65-40b1-af24-062b41e1cedb", "648450d6-a670-4a60-8a1e-acb79eafe708"], "minItems": 1}}, "example": {"driverIds": ["12d9423b-8a65-40b1-af24-062b41e1cedb", "648450d6-a670-4a60-8a1e-acb79eafe708"]}, "required": ["driverIds"]}, "DriverChargesSubmitDriverExpensesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Submit driver expenses_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriverChargesSubmitDriverExpensesRequestBody": {"title": "DriverChargesSubmitDriverExpensesRequestBody", "type": "object", "properties": {"expenses": {"type": "array", "items": {"$ref": "#/definitions/SubmitExpenseRequest"}, "example": [{"chargeId": "c7b9a78a-6008-47d1-843e-15dc585c30df"}, {"chargeId": "c7b9a78a-6008-47d1-843e-15dc585c30df"}, {"chargeId": "c7b9a78a-6008-47d1-843e-15dc585c30df"}], "minItems": 1}}, "example": {"expenses": [{"chargeId": "c7b9a78a-6008-47d1-843e-15dc585c30df"}]}}, "DriverNotFound": {"title": "DriverNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Dolore sequi itaque quia amet accusantium eveniet."}, "status": {"type": "integer", "example": 6488763175172351820, "format": "int64"}}, "description": "driver not found", "example": {"reason": "Et sint consectetur.", "status": 5004826438210222751}, "required": ["reason", "status"]}, "DriverStatsData": {"title": "DriverStatsData", "type": "object", "properties": {"intervals": {"type": "array", "items": {"$ref": "#/definitions/Interval"}, "description": "Driver statistics broken down by the requested interval type.", "example": [{"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}, {"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}]}, "summary": {"$ref": "#/definitions/StatsSummary"}}, "example": {"intervals": [{"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}, {"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}, {"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}], "summary": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}}, "required": ["summary"]}, "DriverStatsResponse": {"title": "DriverStatsResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/DriverStatsData"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"intervals": [{"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}, {"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}], "summary": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "DriversChargeNotFound": {"title": "DriversChargeNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Rerum in ut corporis nihil."}, "status": {"type": "integer", "example": 2433336389346157246, "format": "int64"}}, "example": {"reason": "Provident fugiat.", "status": 6063053028665584839}, "required": ["reason", "status"]}, "DriversChargeResponse": {"title": "DriversChargeResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/Charge"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "DriversChargesResponse": {"title": "DriversChargesResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/Charges"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"charges": [{"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}, {"charger": {"door": "A", "id": "veefil-602300633", "name": "Amoy-Reef", "pluggedInAt": "2018-05-15T12:00:00+12:00", "pluggedInDuration": 3600, "siteName": "Ecclestone Court Car Park", "type": "home", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "cost": {"amount": 2356, "currency": "GBP"}, "duration": 22245, "endedAt": "2018-05-15T12:00:00+12:00", "energyTotal": 567.89, "expensedTo": {"id": "665130f3-dd26-46d8-9b50-eb1d69880539", "name": "Adept Power Solutions Ltd"}, "generationEnergyTotal": 67.89, "gridEnergyTotal": 500, "id": "a9775af6-da4f-4ef4-af71-9c7092823419", "startedAt": "2018-05-15T12:00:00+12:00"}], "count": 22245}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "DriversRetrieveChargeBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "retrieveCharge_bad_request_response_body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriversRetrieveChargesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "retrieveCharges_bad_request_response_body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "DriversRetrieveStatsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "retrieveStats_bad_request_response_body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Duration": {"title": "Duration", "type": "object", "properties": {"home": {"type": "integer", "description": "Total duration of all charges at home chargers in secs", "example": 680, "format": "int64"}, "private": {"type": "integer", "description": "Total duration of all charges at private chargers in secs", "example": 170, "format": "int64"}, "public": {"type": "integer", "description": "Total duration of all charges at public chargers in secs", "example": 170, "format": "int64"}, "total": {"type": "integer", "description": "Total duration of all charges in secs", "example": 850, "format": "int64"}}, "example": {"home": 680, "private": 170, "public": 170, "total": 850}, "required": ["home", "private", "public", "total"]}, "Energy": {"title": "Energy", "type": "object", "properties": {"home": {"$ref": "#/definitions/Breakdown"}, "private": {"$ref": "#/definitions/Breakdown"}, "public": {"$ref": "#/definitions/Breakdown"}, "total": {"$ref": "#/definitions/Breakdown"}}, "example": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}, "required": ["home", "private", "public", "total"]}, "EnergyStatistics": {"title": "EnergyStatistics", "type": "object", "properties": {"claimedUsage": {"type": "number", "description": "Energy used this period in kWh, filtering out any unclaimed charges.", "example": 243.89, "format": "double"}, "cost": {"type": "integer", "description": "Energy cost in pence.", "example": 324550, "format": "int64"}, "revenueGeneratingClaimedUsage": {"type": "number", "description": "Energy used this period in kWh by claimed charges with a positive revenue.", "example": 43.5, "format": "double"}, "totalUsage": {"type": "number", "description": "Energy used this period in kWh.", "example": 567.89, "format": "double"}, "unclaimedUsage": {"type": "number", "description": "Energy used this period in kWh, filtering out any claimed charges.", "example": 324.65, "format": "double"}}, "example": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "required": ["totalUsage", "claimedUsage", "revenueGeneratingClaimedUsage", "unclaimedUsage", "cost"]}, "EventOutOfDate": {"title": "EventOutOfDate", "type": "object", "properties": {"reason": {"type": "string", "example": "Et ad."}, "status": {"type": "integer", "example": 5968034220767179469, "format": "int64"}}, "example": {"reason": "<PERSON>ulla id labore qui doloremque.", "status": 567954616236605983}, "required": ["reason", "status"]}, "ExpensableCharge": {"title": "ExpensableCharge", "type": "object", "properties": {"chargeCost": {"type": "integer", "description": "Charge cost in pence", "example": 123, "format": "int64"}, "chargerName": {"type": "string", "description": "Public charger name or home charger PSL", "example": "Kent-<PERSON>"}, "driver": {"$ref": "#/definitions/Driver"}, "endTime": {"type": "string", "description": "Charge end time UTC", "example": "2022-09-05T17:58:33Z"}, "energyUsage": {"type": "number", "description": "Energy used in KWh", "example": 98.76, "format": "double"}, "id": {"type": "integer", "description": "Primary key of the charge", "example": 123, "format": "int64"}, "location": {"$ref": "#/definitions/SubmittedChargeLocation"}, "pluggedInAt": {"type": "string", "description": "Plugged in at time UTC", "example": "2022-09-30T15:35:00Z"}, "processedByFullName": {"type": "string", "description": "Who processed this expense (if processed)", "example": "<PERSON>"}, "processedTime": {"type": "string", "description": "Processed time UTC (if processed)", "example": "2022-09-05T14:58:33Z"}, "startTime": {"type": "string", "description": "Charge start time UTC", "example": "2022-09-05T14:58:33Z"}, "submittedTime": {"type": "string", "description": "Submitted for approval time UTC", "example": "2022-09-05T17:58:33Z"}, "unpluggedAt": {"type": "string", "description": "Unplugged at time UTC", "example": "2022-09-30T16:46:00Z"}}, "description": "Expensable charge", "example": {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}, "required": ["id", "driver", "startTime", "endTime", "submittedTime", "chargeCost", "energyUsage", "location", "chargerName"]}, "ExpensableChargeDriverSummary": {"title": "ExpensableChargeDriverSummary", "type": "object", "properties": {"driver": {"$ref": "#/definitions/Driver"}, "submittedChargeIds": {"type": "array", "items": {"type": "integer", "example": 211097512242539482, "format": "int64"}, "description": "List of submitted charge IDs.", "example": [1, 2, 4, 42, 54]}, "totalCharges": {"type": "integer", "description": "Number of charges.", "example": 1, "format": "int64"}, "totalCost": {"$ref": "#/definitions/TotalCost"}, "totalUsage": {"$ref": "#/definitions/TotalUsage"}}, "example": {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, "required": ["driver", "totalCharges", "totalUsage", "totalCost"]}, "ExpensedTo": {"title": "ExpensedTo", "type": "object", "properties": {"id": {"type": "string", "description": "Groups unique identifier.", "example": "451c808c-6c6b-4456-9a4e-0675eb5466a9", "format": "uuid"}, "name": {"type": "string", "description": "Groups name.", "example": "Adept Power Solutions Ltd"}}, "description": "Group the charge is to be expensed to.", "example": {"id": "66f12aca-d6c3-45b2-b68a-12c09bc4c409", "name": "Adept Power Solutions Ltd"}, "required": ["id", "name"]}, "FleetUsageResponse": {"title": "FleetUsageResponse", "type": "object", "properties": {"co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "numberOfDrivers": {"type": "integer", "description": "Number of drivers.", "example": 367, "format": "int64"}, "totalCharges": {"$ref": "#/definitions/TotalCharges"}, "totalUsage": {"$ref": "#/definitions/TotalUsage"}}, "example": {"co2Savings": 4325.62, "numberOfDrivers": 367, "totalCharges": {"home": 367, "public": 367, "total": 367}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, "required": ["totalCharges", "totalUsage", "co2Savings", "numberOfDrivers"]}, "Forecast": {"title": "Forecast", "type": "object", "properties": {"data": {"$ref": "#/definitions/Forecastdata"}}, "example": {"data": {"data": [{"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}], "dnoregion": "Tempore rerum hic suscipit temporibus.", "regionid": 3791299769356288913, "shortname": "Est dignissimos qui voluptatem earum."}}, "required": ["data"]}, "Forecastdata": {"title": "Forecastdata", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/Period"}, "example": [{"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}]}, "dnoregion": {"type": "string", "example": "Et quia error impedit adipisci."}, "regionid": {"type": "integer", "example": 8875093038101515855, "format": "int64"}, "shortname": {"type": "string", "example": "Facilis sit aspernatur possimus debitis dolorum."}}, "example": {"data": [{"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}], "dnoregion": "Id expedita sunt et nisi vel.", "regionid": 6609258387717968263, "shortname": "Non quas explicabo praesentium non iure maxime."}, "required": ["regionid", "dnoregion", "shortname", "data"]}, "GroupChargeStatistics": {"title": "GroupChargeStatistics", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "sum of charging duration in seconds", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/EnergyStatistics"}, "numberOfChargers": {"type": "integer", "description": "count of distinct charger ids", "example": 3, "format": "int64"}, "numberOfCharges": {"type": "integer", "description": "count of distinct charge uuids", "example": 10, "format": "int64"}, "numberOfSites": {"type": "integer", "description": "count of distinct sites", "example": 3, "format": "int64"}, "numberOfUsers": {"type": "integer", "description": "count of distinct user ids", "example": 2, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "sum of settlement amount in pence", "example": 457600, "format": "int64"}}, "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 3, "numberOfCharges": 10, "numberOfSites": 3, "numberOfUsers": 2, "revenueGenerated": 457600}, "required": ["numberOfCharges", "numberOfSites", "co2Savings", "energy", "numberOfChargers", "numberOfUsers", "chargingDuration", "revenueGenerated"]}, "GroupNotFound": {"title": "GroupNotFound", "type": "object", "properties": {"reason": {"type": "string", "example": "Sit recusandae asperiores tenetur similique laudantium."}, "status": {"type": "integer", "example": 2199605673663338287, "format": "int64"}}, "description": "Group not found", "example": {"reason": "Distinctio sapiente reiciendis ex vitae qui aut.", "status": 8059366099603535479}, "required": ["reason", "status"]}, "GroupSitesStats": {"title": "GroupSitesStats", "type": "object", "properties": {"co2AvoidedKg": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "energyCost": {"type": "integer", "description": "Total energy cost in pence.", "example": 3245, "format": "int64"}, "energyUsageKwh": {"type": "number", "description": "Total energy usage in kWh.", "example": 567.89, "format": "double"}, "numberOfCharges": {"type": "integer", "description": "Total number of charges.", "example": 2048, "format": "int64"}, "numberOfDrivers": {"type": "integer", "description": "Total number of unique drivers.", "example": 64, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}, "siteId": {"type": "string", "description": "Site ID", "example": "13c8fdaa-cb4d-48b4-84e7-ca493396fac6", "format": "uuid"}, "totalDuration": {"type": "integer", "description": "Total duration of charging in seconds.", "example": 22245, "format": "int64"}}, "example": {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "038b37b7-743c-4db8-a92a-a029388a08db", "totalDuration": 22245}, "required": ["siteId", "energyUsageKwh", "energyCost", "co2AvoidedKg", "revenueGenerated", "numberOfCharges", "numberOfDrivers", "totalDuration"]}, "GroupSitesStatsResponse": {"title": "GroupSitesStatsResponse", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/GroupSitesStats"}, "example": [{"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}, {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}, {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}]}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": [{"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}, {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}, {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}, {"co2AvoidedKg": 4325.62, "energyCost": 3245, "energyUsageKwh": 567.89, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600, "siteId": "b2d87e7b-562e-4e7c-91cb-0841e34faf6d", "totalDuration": 22245}], "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "IdentifierNotProvided": {"title": "IdentifierNotProvided", "type": "object", "properties": {"reason": {"type": "string", "example": "Et aut fugit non recusandae."}, "status": {"type": "integer", "example": 3896896775914551480, "format": "int64"}}, "description": "Identifier not provided. At least one of groupId, siteId or chargerId must be provided.", "example": {"reason": "Sequi et at architecto officia.", "status": 4722051546514969153}, "required": ["reason", "status"]}, "Intensity": {"title": "Intensity", "type": "object", "properties": {"forecast": {"type": "integer", "example": 5446500839424828971, "format": "int64"}, "index": {"type": "string", "description": "Intensity index with values: very low, low, moderate, high, very high", "example": "Incidunt ipsam consequatur dolorem animi tempora rerum."}}, "example": {"forecast": 3447670857361454986, "index": "Et quis."}, "required": ["forecast", "index"]}, "Interval": {"title": "Interval", "type": "object", "properties": {"from": {"type": "string", "description": "Statistics date time range start eg: 2022-01-01T00:00:00Z", "example": "2022-10-12T00:00:00Z", "format": "date-time"}, "stats": {"$ref": "#/definitions/StatsSummary"}, "to": {"type": "string", "description": "Statistics report inclusive end date eg: 2022-01-31T00:00:00Z", "example": "2022-10-19T00:00:00Z", "format": "date-time"}}, "example": {"from": "2022-10-12T00:00:00Z", "stats": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "to": "2022-10-19T00:00:00Z"}, "required": ["from", "to", "stats"]}, "Limit": {"title": "Limit", "type": "object", "properties": {"amount": {"type": "number", "description": "Amount for a limit", "example": 1.23, "format": "double"}, "type": {"type": "string", "description": "Type of a limit", "example": "energy", "enum": ["energy", "duration"]}, "unit": {"type": "string", "description": "Unit for a limit - currently only kWh, but could be time interval in the future", "example": "kWh"}}, "example": {"amount": 1.23, "type": "energy", "unit": "kWh"}}, "LinkUserLinkUserToHomeChargerBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Link user to home charger_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "LinkUserLinkUserToHomeChargerInternalServerErrorResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Link user to home charger_internal_server_error_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "Meta": {"title": "Meta", "type": "object", "properties": {"params": {"type": "object", "description": "Passed parameters", "example": {"Voluptate totam.": "Quia voluptas neque consectetur."}, "additionalProperties": true}}, "example": {"params": {"Labore iure qui sed quos laudantium sit.": "Enim qui."}}, "required": ["params"]}, "Mix": {"title": "Mix", "type": "object", "properties": {"fuel": {"type": "string", "description": "Fuel type with values: gas, coal, biomass, nuclear, hydro, storage, imports, other, wind, solar", "example": "Sed consequuntur et voluptas totam."}, "perc": {"type": "number", "example": 0.011182538, "format": "float"}}, "example": {"fuel": "Qui delectus nulla ab quod dolorem eum.", "perc": 0.65171766}, "required": ["fuel", "perc"]}, "Money": {"title": "Money", "type": "object", "properties": {"amount": {"type": "integer", "description": "Amount in smallest denomination of the associated currency", "example": 2356, "format": "int64"}, "currency": {"type": "string", "description": "ISO currency code", "example": "GBP"}}, "description": "An amount of money with its currency type.", "example": {"amount": 2356, "currency": "GBP"}, "required": ["amount", "currency"]}, "MoneyInt64": {"title": "MoneyInt64", "type": "object", "properties": {"amount": {"type": "integer", "description": "Amount in smallest denomination of the associated currency", "example": 2356, "format": "int64"}, "currency": {"type": "string", "description": "ISO currency code", "example": "GBP"}}, "description": "An amount of money with its currency type.", "example": {"amount": 2356, "currency": "GBP"}, "required": ["amount", "currency"]}, "OrganisationChargesDriverSummaryResponse": {"title": "OrganisationChargesDriverSummaryResponse", "type": "object", "properties": {"driverExpensableChargeSummaries": {"type": "array", "items": {"$ref": "#/definitions/ExpensableChargeDriverSummary"}, "example": [{"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}]}}, "example": {"driverExpensableChargeSummaries": [{"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedChargeIds": [1, 2, 4, 42, 54], "totalCharges": 1, "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}]}, "required": ["driverExpensableChargeSummaries"]}, "OrganisationChargesExpensesByOrganisationBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Expenses by organisation_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "OrganisationChargesExpensesByOrganisationGroupedByDriverBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Expenses by organisation, grouped by driver_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "OrganisationChargesFleetUsageByOrganisationBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Fleet usage by organisation_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "OrganisationChargesMarkSubmittedChargesAsProcessedRequestBody": {"title": "OrganisationChargesMarkSubmittedChargesAsProcessedRequestBody", "type": "object", "properties": {"approverId": {"type": "integer", "example": 7379243700420736929, "format": "int64"}, "submittedChargeIds": {"type": "array", "items": {"type": "integer", "example": 3745782137329410338, "format": "int64"}, "example": [12375, 17456], "minItems": 1}}, "example": {"approverId": 6886601237891525912, "submittedChargeIds": [12375, 17456]}, "required": ["approverId", "submittedChargeIds"]}, "OrganisationChargesResponse": {"title": "OrganisationChargesResponse", "type": "object", "properties": {"expensableCharges": {"type": "array", "items": {"$ref": "#/definitions/ExpensableCharge"}, "example": [{"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}]}}, "example": {"expensableCharges": [{"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2022-09-30T15:35:00Z", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2022-09-30T16:46:00Z"}]}, "required": ["expensableCharges"]}, "OrganisationChargesSubmittedChargesForDriverBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": false}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Submitted charges for driver_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "OrganisationDriversChargeStatistics": {"title": "OrganisationDriversChargeStatistics", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "Aggregate charge duration in seconds", "example": 123, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "driver": {"$ref": "#/definitions/Driver"}, "numberOfCharges": {"type": "integer", "description": "Total number of charges.", "example": 2048, "format": "int64"}, "pluggedInDuration": {"type": "integer", "description": "Aggregate plugged-in duration in seconds", "example": 123, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}, "totalCost": {"type": "integer", "description": "Aggregate charge cost in pence", "example": 324550, "format": "int64"}, "totalUsage": {"type": "number", "description": "Energy used this period in kWh.", "example": 567.89, "format": "double"}}, "example": {"chargingDuration": 123, "co2Savings": 4325.62, "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "numberOfCharges": 2048, "pluggedInDuration": 123, "revenueGenerated": 457600, "totalCost": 324550, "totalUsage": 567.89}, "required": ["driver", "numberOfCharges", "totalUsage", "co2Savings", "chargingDuration", "pluggedInDuration", "totalCost", "revenueGenerated"]}, "OrganisationDriversChargeStatisticsResponse": {"title": "OrganisationDriversChargeStatisticsResponse", "type": "object", "properties": {"charges": {"type": "array", "items": {"$ref": "#/definitions/OrganisationDriversChargeStatistics"}, "example": [{"chargingDuration": 123, "co2Savings": 4325.62, "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "numberOfCharges": 2048, "pluggedInDuration": 123, "revenueGenerated": 457600, "totalCost": 324550, "totalUsage": 567.89}, {"chargingDuration": 123, "co2Savings": 4325.62, "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "numberOfCharges": 2048, "pluggedInDuration": 123, "revenueGenerated": 457600, "totalCost": 324550, "totalUsage": 567.89}]}}, "example": {"charges": [{"chargingDuration": 123, "co2Savings": 4325.62, "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "numberOfCharges": 2048, "pluggedInDuration": 123, "revenueGenerated": 457600, "totalCost": 324550, "totalUsage": 567.89}, {"chargingDuration": 123, "co2Savings": 4325.62, "driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "numberOfCharges": 2048, "pluggedInDuration": 123, "revenueGenerated": 457600, "totalCost": 324550, "totalUsage": 567.89}]}}, "Organisationchargessummary": {"title": "Mediatype identifier: organisationchargessummary; view=default", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "Total duration of charging in seconds.", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/ChargeEnergySummary"}, "numberOfChargers": {"type": "integer", "description": "Total number of Chargers.", "example": 32, "format": "int64"}, "numberOfCharges": {"type": "integer", "description": "Total number of charges.", "example": 2048, "format": "int64"}, "numberOfDrivers": {"type": "integer", "description": "Total number of unique drivers.", "example": 64, "format": "int64"}, "numberOfSites": {"type": "integer", "description": "Total number of Sites.", "example": 16, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}}, "description": "Organisation StatisticsResponseBody result type (default view)", "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 32, "numberOfCharges": 2048, "numberOfDrivers": 64, "numberOfSites": 16, "revenueGenerated": 457600}, "required": ["numberOfCharges", "numberOfSites", "numberOfChargers", "numberOfDrivers", "chargingDuration", "revenueGenerated", "co2Savings"]}, "Period": {"title": "Period", "type": "object", "properties": {"from": {"type": "string", "example": "2018-01-20T12:00Z"}, "generationmix": {"type": "array", "items": {"$ref": "#/definitions/Mix"}, "example": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}]}, "intensity": {"$ref": "#/definitions/Intensity"}, "to": {"type": "string", "example": "2018-01-20T12:00Z"}}, "example": {"from": "2018-01-20T12:00Z", "generationmix": [{"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}, {"fuel": "Totam doloremque iste.", "perc": 0.48824632}], "intensity": {"forecast": 991886998364910352, "index": "Non autem ex alias iste ad voluptatum."}, "to": "2018-01-20T12:00Z"}, "required": ["from", "to", "intensity", "generationmix"]}, "ProjectionChargesProjectionChargeDataBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Projection Charge Data_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ProjectionChargesResponse": {"title": "ProjectionChargesResponse", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/Charges"}, "example": [{"chargeDurationTotal": 22245, "chargerId": "PSL-0001", "chargerName": "Quis est perferendis repudiandae id laboriosam aliquam.", "co2Avoided": 4325.62, "confirmed": true, "door": "A", "driverIDs": ["3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d"], "endedAt": "2018-05-15T12:00:00Z", "energyCost": 32, "energyTotal": 567.89, "generationEnergyTotal": 35.44, "gridEnergyTotal": 532.45, "id": "5588483f-7125-414a-887f-6fdc37911182", "pluggedInAt": "2018-05-15T12:00:00Z", "revenueGenerated": 457600, "siteName": "Corporis corporis omnis minima.", "startedAt": "2018-05-15T12:00:00Z", "unpluggedAt": "2018-05-15T12:00:00Z"}, {"chargeDurationTotal": 22245, "chargerId": "PSL-0001", "chargerName": "Quis est perferendis repudiandae id laboriosam aliquam.", "co2Avoided": 4325.62, "confirmed": true, "door": "A", "driverIDs": ["3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d"], "endedAt": "2018-05-15T12:00:00Z", "energyCost": 32, "energyTotal": 567.89, "generationEnergyTotal": 35.44, "gridEnergyTotal": 532.45, "id": "5588483f-7125-414a-887f-6fdc37911182", "pluggedInAt": "2018-05-15T12:00:00Z", "revenueGenerated": 457600, "siteName": "Corporis corporis omnis minima.", "startedAt": "2018-05-15T12:00:00Z", "unpluggedAt": "2018-05-15T12:00:00Z"}]}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": [{"chargeDurationTotal": 22245, "chargerId": "PSL-0001", "chargerName": "Quis est perferendis repudiandae id laboriosam aliquam.", "co2Avoided": 4325.62, "confirmed": true, "door": "A", "driverIDs": ["3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d"], "endedAt": "2018-05-15T12:00:00Z", "energyCost": 32, "energyTotal": 567.89, "generationEnergyTotal": 35.44, "gridEnergyTotal": 532.45, "id": "5588483f-7125-414a-887f-6fdc37911182", "pluggedInAt": "2018-05-15T12:00:00Z", "revenueGenerated": 457600, "siteName": "Corporis corporis omnis minima.", "startedAt": "2018-05-15T12:00:00Z", "unpluggedAt": "2018-05-15T12:00:00Z"}, {"chargeDurationTotal": 22245, "chargerId": "PSL-0001", "chargerName": "Quis est perferendis repudiandae id laboriosam aliquam.", "co2Avoided": 4325.62, "confirmed": true, "door": "A", "driverIDs": ["3e4a9823-d60e-4f14-aa88-eea8c327b8f1", "7a8b9c63-6a8b-4a68-8efa-097c1a6d257d"], "endedAt": "2018-05-15T12:00:00Z", "energyCost": 32, "energyTotal": 567.89, "generationEnergyTotal": 35.44, "gridEnergyTotal": 532.45, "id": "5588483f-7125-414a-887f-6fdc37911182", "pluggedInAt": "2018-05-15T12:00:00Z", "revenueGenerated": 457600, "siteName": "Corporis corporis omnis minima.", "startedAt": "2018-05-15T12:00:00Z", "unpluggedAt": "2018-05-15T12:00:00Z"}], "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "ProjectionGroupAndUserChargesResponse": {"title": "ProjectionGroupAndUserChargesResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/UserChargesSchema"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"charges": [{"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}]}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "ProjectionGroupStatisticsGroupSiteStatisticsBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Group site statistics_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "ProjectionchargerChargeStatisticsResponse": {"title": "ProjectionchargerChargeStatisticsResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/ChargerChargeStatistics"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfCharges": 10, "numberOfUsers": 2, "revenueGenerated": 457600}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "ProjectiongroupChargeStatisticsResponse": {"title": "ProjectiongroupChargeStatisticsResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/GroupChargeStatistics"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 3, "numberOfCharges": 10, "numberOfSites": 3, "numberOfUsers": 2, "revenueGenerated": 457600}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "ProjectionsiteChargeStatisticsResponse": {"title": "ProjectionsiteChargeStatisticsResponse", "type": "object", "properties": {"data": {"$ref": "#/definitions/SiteChargeStatistics"}, "meta": {"$ref": "#/definitions/Meta"}}, "example": {"data": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 3, "numberOfCharges": 10, "numberOfUsers": 2, "revenueGenerated": 457600}, "meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}}, "required": ["data", "meta"]}, "Region": {"title": "Region", "type": "object", "properties": {"regionid": {"type": "integer", "description": "National grid DNO region id.", "example": 1, "format": "int64"}}, "example": {"regionid": 1}, "required": ["regionid"]}, "Regions": {"title": "Regions", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/Dnoregion"}, "example": [{"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}]}}, "example": {"data": [{"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}, {"dnoregion": "Scottish Hydro Electric Power Distribution", "regionid": 1, "shortname": "North Scotland"}]}, "required": ["data"]}, "SiteChargeStatistics": {"title": "SiteChargeStatistics", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "sum of charging duration in seconds", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/EnergyStatistics"}, "numberOfChargers": {"type": "integer", "description": "count of distinct charger ids", "example": 3, "format": "int64"}, "numberOfCharges": {"type": "integer", "description": "count of distinct charge uuids", "example": 10, "format": "int64"}, "numberOfUsers": {"type": "integer", "description": "count of distinct user ids", "example": 2, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "sum of settlement amount in pence", "example": 457600, "format": "int64"}}, "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 3, "numberOfCharges": 10, "numberOfUsers": 2, "revenueGenerated": 457600}, "required": ["numberOfCharges", "co2Savings", "energy", "numberOfChargers", "numberOfUsers", "chargingDuration", "revenueGenerated"]}, "SiteStats": {"title": "SiteStats", "type": "object", "properties": {"groupId": {"type": "string", "description": "Group unique identifier.", "example": "79375c02-d867-4d88-bcb2-2bf4edc9edd1", "format": "uuid"}, "groupName": {"type": "string", "description": "Group Name.", "example": "Repellat minus aperiam reprehenderit."}, "id": {"type": "string", "description": "Site unique identifier.", "example": "3728ecea-c102-4f2f-aba4-849944c9b5ff", "format": "uuid"}, "name": {"type": "string", "description": "Site name.", "example": "Consequatur neque voluptatem quidem enim est."}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence", "example": 16, "format": "int64"}, "totalEnergy": {"type": "number", "description": "Energy used in kWh.", "example": 567.89, "format": "double"}}, "example": {"groupId": "e1e42db2-22a9-4e58-a22c-3859ab697071", "groupName": "Neque eos fugit sequi expedita blanditiis qui.", "id": "b8e3de76-c307-4400-b4ca-04fa61d6bc8b", "name": "Deleniti eum fugit molestiae temporibus nihil.", "revenueGenerated": 16, "totalEnergy": 567.89}, "required": ["id", "totalEnergy", "revenueGenerated"]}, "SiteStatsResponse": {"title": "SiteStatsResponse", "type": "object", "properties": {"count": {"type": "integer", "description": "Count of sites returned.", "example": 650, "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/SiteStats"}, "description": "Charge data for site.", "example": [{"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}, {"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}, {"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}, {"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}]}, "from": {"type": "string", "description": "Statistics report inclusive start date eg: 2022-01-01", "example": "2022-10-12", "format": "date"}, "to": {"type": "string", "description": "Statistics report inclusive end date eg: 2022-01-31", "example": "2022-10-19", "format": "date"}}, "example": {"count": 650, "data": [{"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}, {"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}, {"groupId": "e1f2dc78-6616-4200-bed9-f61a78e32c9d", "groupName": "In nihil voluptas rerum esse necessitatibus.", "id": "fc2f5f43-a56f-4d8b-847a-ac1ca7545cf2", "name": "Voluptatum ab est amet omnis nisi corrupti.", "revenueGenerated": 16, "totalEnergy": 567.89}], "from": "2022-10-12", "to": "2022-10-19"}, "required": ["count", "from", "to", "data"]}, "Sitechargessummary": {"title": "Mediatype identifier: sitechargessummary; view=default", "type": "object", "properties": {"chargingDuration": {"type": "integer", "description": "Total duration of charging in seconds.", "example": 22245, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "energy": {"$ref": "#/definitions/ChargeEnergySummary"}, "numberOfChargers": {"type": "integer", "description": "Total number of Chargers.", "example": 32, "format": "int64"}, "numberOfCharges": {"type": "integer", "description": "Total number of charges.", "example": 2048, "format": "int64"}, "numberOfDrivers": {"type": "integer", "description": "Total number of unique drivers.", "example": 64, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}}, "description": "Site StatisticsResponseBody result type (default view)", "example": {"chargingDuration": 22245, "co2Savings": 4325.62, "energy": {"claimedUsage": 243.89, "cost": 324550, "revenueGeneratingClaimedUsage": 43.5, "totalUsage": 567.89, "unclaimedUsage": 324.65}, "numberOfChargers": 32, "numberOfCharges": 2048, "numberOfDrivers": 64, "revenueGenerated": 457600}, "required": ["numberOfCharges", "numberOfDrivers", "energy", "chargingDuration", "revenueGenerated", "co2Savings", "numberOfChargers"]}, "SitesRetrieveChargeStatsGroupedBySiteBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "retrieveChargeStatsGroupedBySite_bad_request_response_body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "StatsSummary": {"title": "StatsSummary", "type": "object", "properties": {"cost": {"$ref": "#/definitions/Cost"}, "duration": {"$ref": "#/definitions/Duration"}, "energy": {"$ref": "#/definitions/Energy"}}, "example": {"cost": {"home": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "private": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "public": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}], "total": [{"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}, {"amount": 2356, "currency": "GBP"}]}, "duration": {"home": 680, "private": 170, "public": 170, "total": 850}, "energy": {"home": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "private": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "public": {"generation": 12.5, "grid": 123.4, "total": 135.9}, "total": {"generation": 12.5, "grid": 123.4, "total": 135.9}}}, "required": ["energy", "cost", "duration"]}, "SubmitExpenseRequest": {"title": "SubmitExpenseRequest", "type": "object", "properties": {"chargeId": {"type": "string", "description": "UUID of the submitted charge.", "example": "1aae4fdf-8999-444c-b4d9-ec4f60d9fab4", "format": "uuid"}}, "example": {"chargeId": "c0007022-752c-4ce5-bd7c-529678738da3"}, "required": ["chargeId"]}, "SubmittedCharge": {"title": "SubmittedCharge", "type": "object", "properties": {"chargeCost": {"type": "integer", "description": "Charge cost in pence", "example": 123, "format": "int64"}, "chargerName": {"type": "string", "description": "Public charger name or home charger PSL", "example": "Kent-<PERSON>"}, "duration": {"type": "integer", "description": "Charge duration in seconds", "example": 123, "format": "int64"}, "endTime": {"type": "string", "description": "Charge end time UTC", "example": "2022-09-05T17:58:33Z"}, "energyUsage": {"type": "number", "description": "Energy used in KWh", "example": 98.76, "format": "double"}, "id": {"type": "integer", "description": "Primary key of the charge", "example": 123, "format": "int64"}, "location": {"$ref": "#/definitions/SubmittedChargeLocation"}, "pluggedInAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}, "processedByFullName": {"type": "string", "description": "Who processed this expense (if processed)", "example": "<PERSON>"}, "processedTime": {"type": "string", "description": "Processed time UTC (if processed)", "example": "2022-09-05T14:58:33Z"}, "startTime": {"type": "string", "description": "Charge start time UTC", "example": "2022-09-05T14:58:33Z"}, "status": {"type": "string", "description": "Whether the charge is NEW or PROCESSED", "example": "NEW", "enum": ["NEW", "PROCESSED"]}, "submittedTime": {"type": "string", "description": "Submitted for approval time UTC", "example": "2022-09-05T17:58:33Z"}, "unpluggedAt": {"type": "string", "description": "Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.", "example": "2018-05-15T12:00:00+12:00"}}, "example": {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, "required": ["id", "startTime", "endTime", "submittedTime", "chargeCost", "energyUsage", "duration", "location", "chargerName", "status"]}, "SubmittedChargeAddress": {"title": "SubmittedChargeAddress", "type": "object", "properties": {"country": {"type": "string", "description": "Country full name", "example": "United Kingdom"}, "line1": {"type": "string", "description": "Address line 1", "example": "234 Banner St"}, "line2": {"type": "string", "description": "Address line 2", "example": "Westminster"}, "postcode": {"type": "string", "description": "Postcode", "example": "EC1Y 8QE"}, "prettyPrint": {"type": "string", "description": "User-friendly string representation of address", "example": "234 Banner St, Westminster, London, EC1Y 8QE"}, "town": {"type": "string", "description": "Town", "example": "London"}}, "example": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "required": ["town", "line1", "<PERSON><PERSON><PERSON><PERSON>"]}, "SubmittedChargeLocation": {"title": "SubmittedChargeLocation", "type": "object", "properties": {"address": {"$ref": "#/definitions/SubmittedChargeAddress"}, "id": {"type": "integer", "description": "ID of the charge location", "example": 456, "format": "int64"}, "locationType": {"type": "string", "description": "Type of the location: home or public", "example": "home", "enum": ["home", "public"]}}, "example": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "required": ["id", "locationType", "address"]}, "SubmittedChargesResponse": {"title": "SubmittedChargesResponse", "type": "object", "properties": {"driver": {"$ref": "#/definitions/Driver"}, "submittedCharges": {"type": "array", "items": {"$ref": "#/definitions/SubmittedCharge"}, "example": [{"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}]}, "totalCost": {"$ref": "#/definitions/TotalCost"}, "totalUsage": {"$ref": "#/definitions/TotalUsage"}}, "example": {"driver": {"email": "<EMAIL>", "firstName": "Max", "fullName": "<PERSON>", "id": 123, "lastName": "Verstappen"}, "submittedCharges": [{"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}, {"chargeCost": 123, "chargerName": "Kent-<PERSON>", "duration": 123, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "id": 123, "location": {"address": {"country": "United Kingdom", "line1": "234 Banner St", "line2": "Westminster", "postcode": "EC1Y 8QE", "prettyPrint": "234 Banner St, Westminster, London, EC1Y 8QE", "town": "London"}, "id": 456, "locationType": "home"}, "pluggedInAt": "2018-05-15T12:00:00+12:00", "processedByFullName": "<PERSON>", "processedTime": "2022-09-05T14:58:33Z", "startTime": "2022-09-05T14:58:33Z", "status": "NEW", "submittedTime": "2022-09-05T17:58:33Z", "unpluggedAt": "2018-05-15T12:00:00+12:00"}], "totalCost": {"home": 12300, "public": 45670}, "totalUsage": {"home": 12.34, "public": 56.78, "total": 12.34}}, "required": ["driver", "totalUsage", "totalCost", "submittedCharges"]}, "SubmittedExpense": {"title": "SubmittedExpense", "type": "object", "properties": {"chargeId": {"type": "string", "description": "UUID of the charge associated with the expense.", "example": "be6b9043-131c-4198-910f-4e20255afc24", "format": "uuid"}, "id": {"type": "integer", "description": "Primary key of the expense.", "example": 456, "format": "int64"}}, "example": {"chargeId": "d4246350-8f7d-4b8a-8c52-1c5ae10e3aeb", "id": 456}, "required": ["id", "chargeId"]}, "SubmittedExpenseResponse": {"title": "SubmittedExpenseResponse", "type": "object", "properties": {"expenses": {"type": "array", "items": {"$ref": "#/definitions/SubmittedExpense"}, "example": [{"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}, {"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}, {"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}, {"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}]}}, "example": {"expenses": [{"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}, {"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}, {"chargeId": "efc8b3e5-e8b7-4503-b077-8df02aa39ee0", "id": 456}]}}, "TimeRangeOutOfBounds": {"title": "TimeRangeOutOfBounds", "type": "object", "properties": {"reason": {"type": "string", "example": "Aut totam rerum."}, "status": {"type": "integer", "example": 3456610295582185205, "format": "int64"}}, "description": "requested time range is too wide (maximum 1 month)", "example": {"reason": "Necessitatibus pariatur omnis doloremque tenetur.", "status": 2720375916759340280}, "required": ["reason", "status"]}, "TotalCharges": {"title": "TotalCharges", "type": "object", "properties": {"home": {"type": "integer", "description": "Number of charges attributed to Home chargers.", "example": 367, "format": "int64"}, "public": {"type": "integer", "description": "Number of charges attributed to Public chargers.", "example": 367, "format": "int64"}, "total": {"type": "integer", "description": "Number of charges.", "example": 367, "format": "int64"}}, "example": {"home": 367, "public": 367, "total": 367}, "required": ["total", "home", "public"]}, "TotalCost": {"title": "TotalCost", "type": "object", "properties": {"home": {"type": "integer", "description": "Amount expensed in pence for home charges.", "example": 12300, "format": "int64"}, "public": {"type": "integer", "description": "Amount expensed in pence for public charges.", "example": 45670, "format": "int64"}}, "example": {"home": 12300, "public": 45670}, "required": ["home", "public"]}, "TotalUsage": {"title": "TotalUsage", "type": "object", "properties": {"home": {"type": "number", "description": "Total number of kWh used for home charges.", "example": 12.34, "format": "double"}, "public": {"type": "number", "description": "Total number of kWh used for public charges.", "example": 56.78, "format": "double"}, "total": {"type": "number", "description": "Total number of kWh used.", "example": 12.34, "format": "double"}}, "example": {"home": 12.34, "public": 56.78, "total": 12.34}, "required": ["home", "public"]}, "TransactionNotStarted": {"title": "TransactionNotStarted", "type": "object", "properties": {"reason": {"type": "string", "example": "Quo asperiores alias neque impedit maiores."}, "status": {"type": "integer", "example": 1191603016839432784, "format": "int64"}}, "example": {"reason": "Pariatur doloribus molestiae reiciendis omnis.", "status": 575494032158758249}, "required": ["reason", "status"]}, "Usage": {"title": "Usage", "type": "object", "properties": {"co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 2469.32, "format": "double"}, "cost": {"type": "integer", "description": "Energy cost in pence", "example": 135668, "format": "int64"}, "intervalStartDate": {"type": "string", "description": "The start date of this usage interval", "example": "2023-06-07", "format": "date"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence", "example": 16, "format": "int64"}, "totalUsage": {"type": "number", "description": "Energy used this period in kWh", "example": 669.2, "format": "double"}}, "example": {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, "required": ["intervalStartDate", "totalUsage", "co2Savings", "revenueGenerated", "cost"]}, "UsageResponse": {"title": "UsageResponse", "type": "object", "properties": {"meta": {"$ref": "#/definitions/Meta"}, "usage": {"type": "array", "items": {"$ref": "#/definitions/Usage"}, "example": [{"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}]}}, "example": {"meta": {"params": {"Enim blanditiis ex omnis.": "Deserunt nemo placeat.", "Impedit facilis debitis esse sit.": "Deserunt molestiae.", "Quo itaque molestiae inventore.": "Ipsum quia amet aut possimus consequatur."}}, "usage": [{"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}, {"co2Savings": 2469.32, "cost": 135668, "intervalStartDate": "2023-06-07", "revenueGenerated": 16, "totalUsage": 669.2}]}, "required": ["meta"]}, "UsageUsageByOrganisationAndChargerBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Usage by organisation and charger_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "UsageUsageByOrganisationAndSiteBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Usage by organisation and site_bad_request_Response_Body result type (default view)", "example": {"fault": false, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "UsageUsageByOrganisationBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": true}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": true}}, "description": "Usage by organisation_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": true, "timeout": false}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "UserCharge": {"title": "UserCharge", "type": "object", "properties": {"businessName": {"type": "string", "description": "Business to whom this charge is associated", "example": "Pod Point - Software Team"}, "chargeCost": {"type": "integer", "description": "Charge cost in pence", "example": 324550, "format": "int64"}, "chargerName": {"type": "string", "description": "Name of charger associated with charge", "example": "<PERSON><PERSON><PERSON>"}, "chargingDuration": {"type": "integer", "description": "Charge duration in seconds", "example": 123, "format": "int64"}, "co2Savings": {"type": "number", "description": "CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)", "example": 4325.62, "format": "double"}, "endTime": {"type": "string", "description": "Charge end time UTC", "example": "2022-09-05T17:58:33Z"}, "energyUsage": {"type": "number", "description": "Energy used in kWh", "example": 98.76, "format": "double"}, "pluggedInDuration": {"type": "integer", "description": "Plugged-in duration in seconds", "example": 123, "format": "int64"}, "revenueGenerated": {"type": "integer", "description": "Revenue generated in pence.", "example": 457600, "format": "int64"}, "startTime": {"type": "string", "description": "Charge start time UTC", "example": "2022-09-05T14:58:33Z"}}, "example": {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, "required": ["chargerName", "businessName", "startTime", "endTime", "chargingDuration", "pluggedInDuration", "energyUsage", "chargeCost", "revenueGenerated", "co2Savings"]}, "UserChargesGroupAndUserChargesBadRequestResponseBody": {"title": "Mediatype identifier: application/vnd.goa.error; view=default", "type": "object", "properties": {"fault": {"type": "boolean", "description": "Is the error a server-side fault?", "example": true}, "id": {"type": "string", "description": "ID is a unique identifier for this particular occurrence of the problem.", "example": "123abc"}, "message": {"type": "string", "description": "Message is a human-readable explanation specific to this occurrence of the problem.", "example": "parameter 'p' must be an integer"}, "name": {"type": "string", "description": "Name is the name of this class of errors.", "example": "bad_request"}, "temporary": {"type": "boolean", "description": "Is the error temporary?", "example": false}, "timeout": {"type": "boolean", "description": "Is the error a timeout?", "example": false}}, "description": "Group And User Charges_bad_request_Response_Body result type (default view)", "example": {"fault": true, "id": "123abc", "message": "parameter 'p' must be an integer", "name": "bad_request", "temporary": false, "timeout": true}, "required": ["name", "id", "message", "temporary", "timeout", "fault"]}, "UserChargesSchema": {"title": "UserChargesSchema", "type": "object", "properties": {"charges": {"type": "array", "items": {"$ref": "#/definitions/UserCharge"}, "example": [{"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}]}}, "description": "List of user's charges", "example": {"charges": [{"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}, {"businessName": "Pod Point - Software Team", "chargeCost": 324550, "chargerName": "<PERSON><PERSON><PERSON>", "chargingDuration": 123, "co2Savings": 4325.62, "endTime": "2022-09-05T17:58:33Z", "energyUsage": 98.76, "pluggedInDuration": 123, "revenueGenerated": 457600, "startTime": "2022-09-05T14:58:33Z"}]}}}}