// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge commands HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	chargecommands "experience/libs/data-platform/api/contract/gen/charge_commands"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildCorrectEnergyCostRequest instantiates a HTTP request object with method
// and path set to call the "Charge commands" service "Correct energy cost"
// endpoint
func (c *Client) BuildCorrectEnergyCostRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		chargeID string
	)
	{
		p, ok := v.(*chargecommands.CorrectEnergyCostPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Charge commands", "Correct energy cost", "*chargecommands.CorrectEnergyCostPayload", v)
		}
		chargeID = p.ChargeID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: CorrectEnergyCostChargeCommandsPath(chargeID)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Charge commands", "Correct energy cost", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeCorrectEnergyCostRequest returns an encoder for requests sent to the
// Charge commands Correct energy cost server.
func EncodeCorrectEnergyCostRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*chargecommands.CorrectEnergyCostPayload)
		if !ok {
			return goahttp.ErrInvalidType("Charge commands", "Correct energy cost", "*chargecommands.CorrectEnergyCostPayload", v)
		}
		body := NewCorrectEnergyCostRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Charge commands", "Correct energy cost", err)
		}
		return nil
	}
}

// DecodeCorrectEnergyCostResponse returns a decoder for responses returned by
// the Charge commands Correct energy cost endpoint. restoreBody controls
// whether the response body should be restored after having been read.
// DecodeCorrectEnergyCostResponse may return the following errors:
//   - "charge_expensed" (type *chargecommands.ChargeExpensed): http.StatusBadRequest
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - "charge_not_found" (type *chargecommands.ChargeNotFound): http.StatusNotFound
//   - "duplicate_requests" (type *chargecommands.EventOutOfDate): http.StatusConflict
//   - error: internal error
func DecodeCorrectEnergyCostResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body CorrectEnergyCostResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct energy cost", err)
			}
			err = ValidateCorrectEnergyCostResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct energy cost", err)
			}
			res := NewCorrectEnergyCostAggregateCostCorrectedResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			en := resp.Header.Get("goa-error")
			switch en {
			case "charge_expensed":
				var (
					body CorrectEnergyCostChargeExpensedResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge commands", "Correct energy cost", err)
				}
				err = ValidateCorrectEnergyCostChargeExpensedResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge commands", "Correct energy cost", err)
				}
				return nil, NewCorrectEnergyCostChargeExpensed(&body)
			case "bad_request":
				var (
					body CorrectEnergyCostBadRequestResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge commands", "Correct energy cost", err)
				}
				err = ValidateCorrectEnergyCostBadRequestResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge commands", "Correct energy cost", err)
				}
				return nil, NewCorrectEnergyCostBadRequest(&body)
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Charge commands", "Correct energy cost", resp.StatusCode, string(body))
			}
		case http.StatusNotFound:
			var (
				body CorrectEnergyCostChargeNotFoundResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct energy cost", err)
			}
			err = ValidateCorrectEnergyCostChargeNotFoundResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct energy cost", err)
			}
			return nil, NewCorrectEnergyCostChargeNotFound(&body)
		case http.StatusConflict:
			var (
				body CorrectEnergyCostDuplicateRequestsResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct energy cost", err)
			}
			err = ValidateCorrectEnergyCostDuplicateRequestsResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct energy cost", err)
			}
			return nil, NewCorrectEnergyCostDuplicateRequests(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Charge commands", "Correct energy cost", resp.StatusCode, string(body))
		}
	}
}

// BuildCorrectSettlementAmountRequest instantiates a HTTP request object with
// method and path set to call the "Charge commands" service "Correct
// settlement amount" endpoint
func (c *Client) BuildCorrectSettlementAmountRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		chargeID string
	)
	{
		p, ok := v.(*chargecommands.CorrectSettlementAmountPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Charge commands", "Correct settlement amount", "*chargecommands.CorrectSettlementAmountPayload", v)
		}
		chargeID = p.ChargeID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: CorrectSettlementAmountChargeCommandsPath(chargeID)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Charge commands", "Correct settlement amount", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeCorrectSettlementAmountRequest returns an encoder for requests sent to
// the Charge commands Correct settlement amount server.
func EncodeCorrectSettlementAmountRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*chargecommands.CorrectSettlementAmountPayload)
		if !ok {
			return goahttp.ErrInvalidType("Charge commands", "Correct settlement amount", "*chargecommands.CorrectSettlementAmountPayload", v)
		}
		body := NewCorrectSettlementAmountRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Charge commands", "Correct settlement amount", err)
		}
		return nil
	}
}

// DecodeCorrectSettlementAmountResponse returns a decoder for responses
// returned by the Charge commands Correct settlement amount endpoint.
// restoreBody controls whether the response body should be restored after
// having been read.
// DecodeCorrectSettlementAmountResponse may return the following errors:
//   - "charge_not_found" (type *chargecommands.ChargeNotFound): http.StatusNotFound
//   - "duplicate_requests" (type *chargecommands.EventOutOfDate): http.StatusConflict
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeCorrectSettlementAmountResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body CorrectSettlementAmountResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct settlement amount", err)
			}
			err = ValidateCorrectSettlementAmountResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct settlement amount", err)
			}
			res := NewCorrectSettlementAmountAggregateSettlementAmountCorrectedResponseOK(&body)
			return res, nil
		case http.StatusNotFound:
			var (
				body CorrectSettlementAmountChargeNotFoundResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct settlement amount", err)
			}
			err = ValidateCorrectSettlementAmountChargeNotFoundResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct settlement amount", err)
			}
			return nil, NewCorrectSettlementAmountChargeNotFound(&body)
		case http.StatusConflict:
			var (
				body CorrectSettlementAmountDuplicateRequestsResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct settlement amount", err)
			}
			err = ValidateCorrectSettlementAmountDuplicateRequestsResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct settlement amount", err)
			}
			return nil, NewCorrectSettlementAmountDuplicateRequests(&body)
		case http.StatusBadRequest:
			var (
				body CorrectSettlementAmountBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge commands", "Correct settlement amount", err)
			}
			err = ValidateCorrectSettlementAmountBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge commands", "Correct settlement amount", err)
			}
			return nil, NewCorrectSettlementAmountBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Charge commands", "Correct settlement amount", resp.StatusCode, string(body))
		}
	}
}
