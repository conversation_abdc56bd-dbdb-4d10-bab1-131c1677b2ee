// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge commands HTTP client CLI support package
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"encoding/json"
	chargecommands "experience/libs/data-platform/api/contract/gen/charge_commands"
	"fmt"

	goa "goa.design/goa/v3/pkg"
)

// BuildCorrectEnergyCostPayload builds the payload for the Charge commands
// Correct energy cost endpoint from CLI flags.
func BuildCorrectEnergyCostPayload(chargeCommandsCorrectEnergyCostBody string, chargeCommandsCorrectEnergyCostChargeID string) (*chargecommands.CorrectEnergyCostPayload, error) {
	var err error
	var body CorrectEnergyCostRequestBody
	{
		err = json.Unmarshal([]byte(chargeCommandsCorrectEnergyCostBody), &body)
		if err != nil {
			return nil, fmt.Errorf("invalid JSON for body, \nerror: %s, \nexample of valid JSON:\n%s", err, "'{\n      \"cost\": 16,\n      \"submittedBy\": \"John Doe\"\n   }'")
		}
		if body.Cost < 1 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("body.cost", body.Cost, 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var chargeID string
	{
		chargeID = chargeCommandsCorrectEnergyCostChargeID
		err = goa.MergeErrors(err, goa.ValidateFormat("chargeID", chargeID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	v := &chargecommands.CorrectEnergyCostPayload{
		Cost:        body.Cost,
		SubmittedBy: body.SubmittedBy,
	}
	v.ChargeID = chargeID

	return v, nil
}

// BuildCorrectSettlementAmountPayload builds the payload for the Charge
// commands Correct settlement amount endpoint from CLI flags.
func BuildCorrectSettlementAmountPayload(chargeCommandsCorrectSettlementAmountBody string, chargeCommandsCorrectSettlementAmountChargeID string) (*chargecommands.CorrectSettlementAmountPayload, error) {
	var err error
	var body CorrectSettlementAmountRequestBody
	{
		err = json.Unmarshal([]byte(chargeCommandsCorrectSettlementAmountBody), &body)
		if err != nil {
			return nil, fmt.Errorf("invalid JSON for body, \nerror: %s, \nexample of valid JSON:\n%s", err, "'{\n      \"settlementAmount\": 16,\n      \"submittedBy\": \"John Doe\"\n   }'")
		}
		if body.SettlementAmount < 1 {
			err = goa.MergeErrors(err, goa.InvalidRangeError("body.settlementAmount", body.SettlementAmount, 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var chargeID string
	{
		chargeID = chargeCommandsCorrectSettlementAmountChargeID
		err = goa.MergeErrors(err, goa.ValidateFormat("chargeID", chargeID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	v := &chargecommands.CorrectSettlementAmountPayload{
		SettlementAmount: body.SettlementAmount,
		SubmittedBy:      body.SubmittedBy,
	}
	v.ChargeID = chargeID

	return v, nil
}
