// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the Charge commands service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// CorrectEnergyCostChargeCommandsPath returns the URL path to the Charge commands service Correct energy cost HTTP endpoint.
func CorrectEnergyCostChargeCommandsPath(chargeID string) string {
	return fmt.Sprintf("/commands/charges/%v/correct-energy-cost", chargeID)
}

// CorrectSettlementAmountChargeCommandsPath returns the URL path to the Charge commands service Correct settlement amount HTTP endpoint.
func CorrectSettlementAmountChargeCommandsPath(chargeID string) string {
	return fmt.Sprintf("/commands/charges/%v/correct-settlement-amount", chargeID)
}
