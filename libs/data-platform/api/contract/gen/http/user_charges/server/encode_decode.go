// Code generated by goa v3.20.1, DO NOT EDIT.
//
// User Charges HTTP server encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"context"
	"errors"
	usercharges "experience/libs/data-platform/api/contract/gen/user_charges"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// EncodeGroupAndUserChargesResponse returns an encoder for responses returned
// by the User Charges Group And User Charges endpoint.
func EncodeGroupAndUserChargesResponse(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder) func(context.Context, http.ResponseWriter, any) error {
	return func(ctx context.Context, w http.ResponseWriter, v any) error {
		res, _ := v.(*usercharges.ProjectionGroupAndUserChargesResponse)
		enc := encoder(ctx, w)
		body := NewGroupAndUserChargesResponseBody(res)
		w.<PERSON>rite<PERSON>eader(http.StatusOK)
		return enc.Encode(body)
	}
}

// DecodeGroupAndUserChargesRequest returns a decoder for requests sent to the
// User Charges Group And User Charges endpoint.
func DecodeGroupAndUserChargesRequest(mux goahttp.Muxer, decoder func(*http.Request) goahttp.Decoder) func(*http.Request) (any, error) {
	return func(r *http.Request) (any, error) {
		var (
			groupID string
			userID  string
			from    string
			to      string
			err     error

			params = mux.Vars(r)
		)
		groupID = params["groupId"]
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		userID = params["userId"]
		err = goa.MergeErrors(err, goa.ValidateFormat("userId", userID, goa.FormatUUID))
		qp := r.URL.Query()
		from = qp.Get("from")
		if from == "" {
			err = goa.MergeErrors(err, goa.MissingFieldError("from", "query string"))
		}
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		to = qp.Get("to")
		if to == "" {
			err = goa.MergeErrors(err, goa.MissingFieldError("to", "query string"))
		}
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
		payload := NewGroupAndUserChargesPayload(groupID, userID, from, to)

		return payload, nil
	}
}

// EncodeGroupAndUserChargesError returns an encoder for errors returned by the
// Group And User Charges User Charges endpoint.
func EncodeGroupAndUserChargesError(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder, formatter func(ctx context.Context, err error) goahttp.Statuser) func(context.Context, http.ResponseWriter, error) error {
	encodeError := goahttp.ErrorEncoder(encoder, formatter)
	return func(ctx context.Context, w http.ResponseWriter, v error) error {
		var en goa.GoaErrorNamer
		if !errors.As(v, &en) {
			return encodeError(ctx, w, v)
		}
		switch en.GoaErrorName() {
		case "bad_request":
			var res *goa.ServiceError
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewGroupAndUserChargesBadRequestResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		default:
			return encodeError(ctx, w, v)
		}
	}
}

// marshalUserchargesUserChargesSchemaToUserChargesSchemaResponseBody builds a
// value of type *UserChargesSchemaResponseBody from a value of type
// *usercharges.UserChargesSchema.
func marshalUserchargesUserChargesSchemaToUserChargesSchemaResponseBody(v *usercharges.UserChargesSchema) *UserChargesSchemaResponseBody {
	res := &UserChargesSchemaResponseBody{}
	if v.Charges != nil {
		res.Charges = make([]*UserChargeResponseBody, len(v.Charges))
		for i, val := range v.Charges {
			res.Charges[i] = marshalUserchargesUserChargeToUserChargeResponseBody(val)
		}
	}

	return res
}

// marshalUserchargesUserChargeToUserChargeResponseBody builds a value of type
// *UserChargeResponseBody from a value of type *usercharges.UserCharge.
func marshalUserchargesUserChargeToUserChargeResponseBody(v *usercharges.UserCharge) *UserChargeResponseBody {
	if v == nil {
		return nil
	}
	res := &UserChargeResponseBody{
		ChargerName:       v.ChargerName,
		BusinessName:      v.BusinessName,
		StartTime:         v.StartTime,
		EndTime:           v.EndTime,
		ChargingDuration:  v.ChargingDuration,
		PluggedInDuration: v.PluggedInDuration,
		EnergyUsage:       v.EnergyUsage,
		ChargeCost:        v.ChargeCost,
		RevenueGenerated:  v.RevenueGenerated,
		Co2Savings:        v.Co2Savings,
	}

	return res
}

// marshalUserchargesMetaToMetaResponseBody builds a value of type
// *MetaResponseBody from a value of type *usercharges.Meta.
func marshalUserchargesMetaToMetaResponseBody(v *usercharges.Meta) *MetaResponseBody {
	res := &MetaResponseBody{}
	if v.Params != nil {
		res.Params = make(map[string]any, len(v.Params))
		for key, val := range v.Params {
			tk := key
			tv := val
			res.Params[tk] = tv
		}
	}

	return res
}
