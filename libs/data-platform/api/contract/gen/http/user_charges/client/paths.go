// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the User Charges service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// GroupAndUserChargesUserChargesPath returns the URL path to the User Charges service Group And User Charges HTTP endpoint.
func GroupAndUserChargesUserChargesPath(groupID string, userID string) string {
	return fmt.Sprintf("/groups/%v/users/%v/charges", groupID, userID)
}
