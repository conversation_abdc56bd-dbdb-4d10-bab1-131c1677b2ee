// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the Chargers service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// RetrieveChargerRegionChargersPath returns the URL path to the Chargers service Retrieve charger region HTTP endpoint.
func RetrieveChargerRegionChargersPath(ppID string) string {
	return fmt.Sprintf("/chargers/%v/dnoregion", ppID)
}

// RetrieveDNOregionsChargersPath returns the URL path to the Chargers service Retrieve DNO regions HTTP endpoint.
func RetrieveDNOregionsChargersPath() string {
	return "/dno-regions"
}

// RetrieveChargingLimitChargersPath returns the URL path to the Chargers service Retrieve charging limit HTTP endpoint.
func RetrieveChargingLimitChargersPath(ppID string) string {
	return fmt.Sprintf("/chargers/%v/limit", ppID)
}
