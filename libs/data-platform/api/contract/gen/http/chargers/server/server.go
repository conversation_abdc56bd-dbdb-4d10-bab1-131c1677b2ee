// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Chargers HTTP server
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"context"
	chargers "experience/libs/data-platform/api/contract/gen/chargers"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Server lists the Chargers service endpoint HTTP handlers.
type Server struct {
	Mounts                []*MountPoint
	RetrieveChargerRegion http.Handler
	RetrieveDNOregions    http.Handler
	RetrieveChargingLimit http.Handler
}

// MountPoint holds information about the mounted endpoints.
type MountPoint struct {
	// Method is the name of the service method served by the mounted HTTP handler.
	Method string
	// Verb is the HTTP method used to match requests to the mounted handler.
	Verb string
	// Pattern is the HTTP request path pattern used to match requests to the
	// mounted handler.
	Pattern string
}

// New instantiates HTTP handlers for all the Chargers service endpoints using
// the provided encoder and decoder. The handlers are mounted on the given mux
// using the HTTP verb and path defined in the design. errhandler is called
// whenever a response fails to be encoded. formatter is used to format errors
// returned by the service methods prior to encoding. Both errhandler and
// formatter are optional and can be nil.
func New(
	e *chargers.Endpoints,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) *Server {
	return &Server{
		Mounts: []*MountPoint{
			{"RetrieveChargerRegion", "GET", "/chargers/{ppId}/dnoregion"},
			{"RetrieveDNOregions", "GET", "/dno-regions"},
			{"RetrieveChargingLimit", "GET", "/chargers/{ppID}/limit"},
		},
		RetrieveChargerRegion: NewRetrieveChargerRegionHandler(e.RetrieveChargerRegion, mux, decoder, encoder, errhandler, formatter),
		RetrieveDNOregions:    NewRetrieveDNOregionsHandler(e.RetrieveDNOregions, mux, decoder, encoder, errhandler, formatter),
		RetrieveChargingLimit: NewRetrieveChargingLimitHandler(e.RetrieveChargingLimit, mux, decoder, encoder, errhandler, formatter),
	}
}

// Service returns the name of the service served.
func (s *Server) Service() string { return "Chargers" }

// Use wraps the server handlers with the given middleware.
func (s *Server) Use(m func(http.Handler) http.Handler) {
	s.RetrieveChargerRegion = m(s.RetrieveChargerRegion)
	s.RetrieveDNOregions = m(s.RetrieveDNOregions)
	s.RetrieveChargingLimit = m(s.RetrieveChargingLimit)
}

// MethodNames returns the methods served.
func (s *Server) MethodNames() []string { return chargers.MethodNames[:] }

// Mount configures the mux to serve the Chargers endpoints.
func Mount(mux goahttp.Muxer, h *Server) {
	MountRetrieveChargerRegionHandler(mux, h.RetrieveChargerRegion)
	MountRetrieveDNOregionsHandler(mux, h.RetrieveDNOregions)
	MountRetrieveChargingLimitHandler(mux, h.RetrieveChargingLimit)
}

// Mount configures the mux to serve the Chargers endpoints.
func (s *Server) Mount(mux goahttp.Muxer) {
	Mount(mux, s)
}

// MountRetrieveChargerRegionHandler configures the mux to serve the "Chargers"
// service "Retrieve charger region" endpoint.
func MountRetrieveChargerRegionHandler(mux goahttp.Muxer, h http.Handler) {
	f, ok := h.(http.HandlerFunc)
	if !ok {
		f = func(w http.ResponseWriter, r *http.Request) {
			h.ServeHTTP(w, r)
		}
	}
	mux.Handle("GET", "/chargers/{ppId}/dnoregion", f)
}

// NewRetrieveChargerRegionHandler creates a HTTP handler which loads the HTTP
// request and calls the "Chargers" service "Retrieve charger region" endpoint.
func NewRetrieveChargerRegionHandler(
	endpoint goa.Endpoint,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) http.Handler {
	var (
		decodeRequest  = DecodeRetrieveChargerRegionRequest(mux, decoder)
		encodeResponse = EncodeRetrieveChargerRegionResponse(encoder)
		encodeError    = EncodeRetrieveChargerRegionError(encoder, formatter)
	)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := context.WithValue(r.Context(), goahttp.AcceptTypeKey, r.Header.Get("Accept"))
		ctx = context.WithValue(ctx, goa.MethodKey, "Retrieve charger region")
		ctx = context.WithValue(ctx, goa.ServiceKey, "Chargers")
		payload, err := decodeRequest(r)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		res, err := endpoint(ctx, payload)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		if err := encodeResponse(ctx, w, res); err != nil {
			errhandler(ctx, w, err)
		}
	})
}

// MountRetrieveDNOregionsHandler configures the mux to serve the "Chargers"
// service "Retrieve DNO regions" endpoint.
func MountRetrieveDNOregionsHandler(mux goahttp.Muxer, h http.Handler) {
	f, ok := h.(http.HandlerFunc)
	if !ok {
		f = func(w http.ResponseWriter, r *http.Request) {
			h.ServeHTTP(w, r)
		}
	}
	mux.Handle("GET", "/dno-regions", f)
}

// NewRetrieveDNOregionsHandler creates a HTTP handler which loads the HTTP
// request and calls the "Chargers" service "Retrieve DNO regions" endpoint.
func NewRetrieveDNOregionsHandler(
	endpoint goa.Endpoint,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) http.Handler {
	var (
		encodeResponse = EncodeRetrieveDNOregionsResponse(encoder)
		encodeError    = EncodeRetrieveDNOregionsError(encoder, formatter)
	)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := context.WithValue(r.Context(), goahttp.AcceptTypeKey, r.Header.Get("Accept"))
		ctx = context.WithValue(ctx, goa.MethodKey, "Retrieve DNO regions")
		ctx = context.WithValue(ctx, goa.ServiceKey, "Chargers")
		var err error
		res, err := endpoint(ctx, nil)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		if err := encodeResponse(ctx, w, res); err != nil {
			errhandler(ctx, w, err)
		}
	})
}

// MountRetrieveChargingLimitHandler configures the mux to serve the "Chargers"
// service "Retrieve charging limit" endpoint.
func MountRetrieveChargingLimitHandler(mux goahttp.Muxer, h http.Handler) {
	f, ok := h.(http.HandlerFunc)
	if !ok {
		f = func(w http.ResponseWriter, r *http.Request) {
			h.ServeHTTP(w, r)
		}
	}
	mux.Handle("GET", "/chargers/{ppID}/limit", f)
}

// NewRetrieveChargingLimitHandler creates a HTTP handler which loads the HTTP
// request and calls the "Chargers" service "Retrieve charging limit" endpoint.
func NewRetrieveChargingLimitHandler(
	endpoint goa.Endpoint,
	mux goahttp.Muxer,
	decoder func(*http.Request) goahttp.Decoder,
	encoder func(context.Context, http.ResponseWriter) goahttp.Encoder,
	errhandler func(context.Context, http.ResponseWriter, error),
	formatter func(ctx context.Context, err error) goahttp.Statuser,
) http.Handler {
	var (
		decodeRequest  = DecodeRetrieveChargingLimitRequest(mux, decoder)
		encodeResponse = EncodeRetrieveChargingLimitResponse(encoder)
		encodeError    = EncodeRetrieveChargingLimitError(encoder, formatter)
	)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := context.WithValue(r.Context(), goahttp.AcceptTypeKey, r.Header.Get("Accept"))
		ctx = context.WithValue(ctx, goa.MethodKey, "Retrieve charging limit")
		ctx = context.WithValue(ctx, goa.ServiceKey, "Chargers")
		payload, err := decodeRequest(r)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		res, err := endpoint(ctx, payload)
		if err != nil {
			if err := encodeError(ctx, w, err); err != nil {
				errhandler(ctx, w, err)
			}
			return
		}
		if err := encodeResponse(ctx, w, res); err != nil {
			errhandler(ctx, w, err)
		}
	})
}
