// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Chargers HTTP server types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	chargers "experience/libs/data-platform/api/contract/gen/chargers"

	goa "goa.design/goa/v3/pkg"
)

// RetrieveChargerRegionResponseBody is the type of the "Chargers" service
// "Retrieve charger region" endpoint HTTP response body.
type RetrieveChargerRegionResponseBody struct {
	// National grid DNO region id.
	Regionid int `form:"regionid" json:"regionid" xml:"regionid"`
}

// RetrieveDNORegionsResponseBody is the type of the "Chargers" service
// "Retrieve DNO regions" endpoint HTTP response body.
type RetrieveDNORegionsResponseBody struct {
	Data []*DnoregionResponseBody `form:"data" json:"data" xml:"data"`
}

// RetrieveChargingLimitResponseBody is the type of the "Chargers" service
// "Retrieve charging limit" endpoint HTTP response body.
type RetrieveChargingLimitResponseBody struct {
	// Indicator of whether charge is allowed
	Allowed bool                 `form:"allowed" json:"allowed" xml:"allowed"`
	Balance *BalanceResponseBody `form:"balance" json:"balance" xml:"balance"`
	Limits  []*LimitResponseBody `form:"limits,omitempty" json:"limits,omitempty" xml:"limits,omitempty"`
}

// RetrieveChargerRegionInternalServerErrorResponseBody is the type of the
// "Chargers" service "Retrieve charger region" endpoint HTTP response body for
// the "internal_server_error" error.
type RetrieveChargerRegionInternalServerErrorResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// RetrieveDNOregionsInternalServerErrorResponseBody is the type of the
// "Chargers" service "Retrieve DNO regions" endpoint HTTP response body for
// the "internal_server_error" error.
type RetrieveDNOregionsInternalServerErrorResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// RetrieveChargingLimitBadRequestResponseBody is the type of the "Chargers"
// service "Retrieve charging limit" endpoint HTTP response body for the
// "bad_request" error.
type RetrieveChargingLimitBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// DnoregionResponseBody is used to define fields on response body types.
type DnoregionResponseBody struct {
	// National grid DNO region id.
	Regionid int `form:"regionid" json:"regionid" xml:"regionid"`
	// DNO region full name.
	Dnoregion string `form:"dnoregion" json:"dnoregion" xml:"dnoregion"`
	// DNO region short name.
	Shortname string `form:"shortname" json:"shortname" xml:"shortname"`
}

// BalanceResponseBody is used to define fields on response body types.
type BalanceResponseBody struct {
	// Actual user balance (or guest pre-authorised amount) in pence/cents/øre
	Actual *int `form:"actual,omitempty" json:"actual,omitempty" xml:"actual,omitempty"`
	// Minimum balance required to start charge in pence/cents/øre
	Minimum *int `form:"minimum,omitempty" json:"minimum,omitempty" xml:"minimum,omitempty"`
	// Balance currency code
	Currency *string `form:"currency,omitempty" json:"currency,omitempty" xml:"currency,omitempty"`
}

// LimitResponseBody is used to define fields on response body types.
type LimitResponseBody struct {
	// Amount for a limit
	Amount *float64 `form:"amount,omitempty" json:"amount,omitempty" xml:"amount,omitempty"`
	// Unit for a limit - currently only kWh, but could be time interval in the
	// future
	Unit *string `form:"unit,omitempty" json:"unit,omitempty" xml:"unit,omitempty"`
	// Type of a limit
	Type *string `form:"type,omitempty" json:"type,omitempty" xml:"type,omitempty"`
}

// NewRetrieveChargerRegionResponseBody builds the HTTP response body from the
// result of the "Retrieve charger region" endpoint of the "Chargers" service.
func NewRetrieveChargerRegionResponseBody(res *chargers.Region) *RetrieveChargerRegionResponseBody {
	body := &RetrieveChargerRegionResponseBody{
		Regionid: res.Regionid,
	}
	return body
}

// NewRetrieveDNORegionsResponseBody builds the HTTP response body from the
// result of the "Retrieve DNO regions" endpoint of the "Chargers" service.
func NewRetrieveDNORegionsResponseBody(res *chargers.Regions) *RetrieveDNORegionsResponseBody {
	body := &RetrieveDNORegionsResponseBody{}
	if res.Data != nil {
		body.Data = make([]*DnoregionResponseBody, len(res.Data))
		for i, val := range res.Data {
			body.Data[i] = marshalChargersDnoregionToDnoregionResponseBody(val)
		}
	} else {
		body.Data = []*DnoregionResponseBody{}
	}
	return body
}

// NewRetrieveChargingLimitResponseBody builds the HTTP response body from the
// result of the "Retrieve charging limit" endpoint of the "Chargers" service.
func NewRetrieveChargingLimitResponseBody(res *chargers.ChargesLimitResponse) *RetrieveChargingLimitResponseBody {
	body := &RetrieveChargingLimitResponseBody{
		Allowed: res.Allowed,
	}
	if res.Balance != nil {
		body.Balance = marshalChargersBalanceToBalanceResponseBody(res.Balance)
	}
	if res.Limits != nil {
		body.Limits = make([]*LimitResponseBody, len(res.Limits))
		for i, val := range res.Limits {
			body.Limits[i] = marshalChargersLimitToLimitResponseBody(val)
		}
	}
	return body
}

// NewRetrieveChargerRegionInternalServerErrorResponseBody builds the HTTP
// response body from the result of the "Retrieve charger region" endpoint of
// the "Chargers" service.
func NewRetrieveChargerRegionInternalServerErrorResponseBody(res *goa.ServiceError) *RetrieveChargerRegionInternalServerErrorResponseBody {
	body := &RetrieveChargerRegionInternalServerErrorResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewRetrieveDNOregionsInternalServerErrorResponseBody builds the HTTP
// response body from the result of the "Retrieve DNO regions" endpoint of the
// "Chargers" service.
func NewRetrieveDNOregionsInternalServerErrorResponseBody(res *goa.ServiceError) *RetrieveDNOregionsInternalServerErrorResponseBody {
	body := &RetrieveDNOregionsInternalServerErrorResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewRetrieveChargingLimitBadRequestResponseBody builds the HTTP response body
// from the result of the "Retrieve charging limit" endpoint of the "Chargers"
// service.
func NewRetrieveChargingLimitBadRequestResponseBody(res *goa.ServiceError) *RetrieveChargingLimitBadRequestResponseBody {
	body := &RetrieveChargingLimitBadRequestResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewRetrieveChargerRegionPayload builds a Chargers service Retrieve charger
// region endpoint payload.
func NewRetrieveChargerRegionPayload(ppID string) *chargers.RetrieveChargerRegionPayload {
	v := &chargers.RetrieveChargerRegionPayload{}
	v.PpID = ppID

	return v
}

// NewRetrieveChargingLimitPayload builds a Chargers service Retrieve charging
// limit endpoint payload.
func NewRetrieveChargingLimitPayload(ppID string, authoriserUUID string) *chargers.RetrieveChargingLimitPayload {
	v := &chargers.RetrieveChargingLimitPayload{}
	v.PpID = ppID
	v.AuthoriserUUID = authoriserUUID

	return v
}
