// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge Statistics HTTP client CLI support package
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	chargestatistics "experience/libs/data-platform/api/contract/gen/charge_statistics"
	"unicode/utf8"

	goa "goa.design/goa/v3/pkg"
)

// BuildGroupChargeStatisticsEndpointPayload builds the payload for the Charge
// Statistics Group Charge Statistics endpoint from CLI flags.
func BuildGroupChargeStatisticsEndpointPayload(chargeStatisticsGroupChargeStatisticsGroupID string, chargeStatisticsGroupChargeStatisticsFrom string, chargeStatisticsGroupChargeStatisticsTo string) (*chargestatistics.GroupChargeStatisticsPayload, error) {
	var err error
	var groupID string
	{
		groupID = chargeStatisticsGroupChargeStatisticsGroupID
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsGroupChargeStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsGroupChargeStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.GroupChargeStatisticsPayload{}
	v.GroupID = groupID
	v.From = from
	v.To = to

	return v, nil
}

// BuildSiteChargeStatisticsEndpointPayload builds the payload for the Charge
// Statistics Site Charge Statistics endpoint from CLI flags.
func BuildSiteChargeStatisticsEndpointPayload(chargeStatisticsSiteChargeStatisticsSiteID string, chargeStatisticsSiteChargeStatisticsFrom string, chargeStatisticsSiteChargeStatisticsTo string) (*chargestatistics.SiteChargeStatisticsPayload, error) {
	var err error
	var siteID string
	{
		siteID = chargeStatisticsSiteChargeStatisticsSiteID
		err = goa.MergeErrors(err, goa.ValidateFormat("siteId", siteID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsSiteChargeStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsSiteChargeStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.SiteChargeStatisticsPayload{}
	v.SiteID = siteID
	v.From = from
	v.To = to

	return v, nil
}

// BuildChargerChargeStatisticsEndpointPayload builds the payload for the
// Charge Statistics Charger Charge Statistics endpoint from CLI flags.
func BuildChargerChargeStatisticsEndpointPayload(chargeStatisticsChargerChargeStatisticsChargerID string, chargeStatisticsChargerChargeStatisticsFrom string, chargeStatisticsChargerChargeStatisticsTo string) (*chargestatistics.ChargerChargeStatisticsPayload, error) {
	var err error
	var chargerID string
	{
		chargerID = chargeStatisticsChargerChargeStatisticsChargerID
		if utf8.RuneCountInString(chargerID) < 1 {
			err = goa.MergeErrors(err, goa.InvalidLengthError("chargerId", chargerID, utf8.RuneCountInString(chargerID), 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsChargerChargeStatisticsFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsChargerChargeStatisticsTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.ChargerChargeStatisticsPayload{}
	v.ChargerID = chargerID
	v.From = from
	v.To = to

	return v, nil
}

// BuildGroupUsageSummariesPayload builds the payload for the Charge Statistics
// Group Usage Summaries endpoint from CLI flags.
func BuildGroupUsageSummariesPayload(chargeStatisticsGroupUsageSummariesGroupID string, chargeStatisticsGroupUsageSummariesInterval string, chargeStatisticsGroupUsageSummariesFrom string, chargeStatisticsGroupUsageSummariesTo string) (*chargestatistics.GroupUsageSummariesPayload, error) {
	var err error
	var groupID string
	{
		groupID = chargeStatisticsGroupUsageSummariesGroupID
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var interval string
	{
		interval = chargeStatisticsGroupUsageSummariesInterval
		if !(interval == "day" || interval == "week" || interval == "month") {
			err = goa.MergeErrors(err, goa.InvalidEnumValueError("interval", interval, []any{"day", "week", "month"}))
		}
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsGroupUsageSummariesFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsGroupUsageSummariesTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.GroupUsageSummariesPayload{}
	v.GroupID = groupID
	v.Interval = interval
	v.From = from
	v.To = to

	return v, nil
}

// BuildGroupAndSiteUsageSummariesPayload builds the payload for the Charge
// Statistics Group and Site Usage Summaries endpoint from CLI flags.
func BuildGroupAndSiteUsageSummariesPayload(chargeStatisticsGroupAndSiteUsageSummariesGroupID string, chargeStatisticsGroupAndSiteUsageSummariesSiteID string, chargeStatisticsGroupAndSiteUsageSummariesInterval string, chargeStatisticsGroupAndSiteUsageSummariesFrom string, chargeStatisticsGroupAndSiteUsageSummariesTo string) (*chargestatistics.GroupAndSiteUsageSummariesPayload, error) {
	var err error
	var groupID string
	{
		groupID = chargeStatisticsGroupAndSiteUsageSummariesGroupID
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var siteID string
	{
		siteID = chargeStatisticsGroupAndSiteUsageSummariesSiteID
		err = goa.MergeErrors(err, goa.ValidateFormat("siteId", siteID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var interval string
	{
		interval = chargeStatisticsGroupAndSiteUsageSummariesInterval
		if !(interval == "day" || interval == "week" || interval == "month") {
			err = goa.MergeErrors(err, goa.InvalidEnumValueError("interval", interval, []any{"day", "week", "month"}))
		}
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsGroupAndSiteUsageSummariesFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsGroupAndSiteUsageSummariesTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.GroupAndSiteUsageSummariesPayload{}
	v.GroupID = groupID
	v.SiteID = siteID
	v.Interval = interval
	v.From = from
	v.To = to

	return v, nil
}

// BuildGroupAndChargerUsageSummariesPayload builds the payload for the Charge
// Statistics Group and Charger Usage Summaries endpoint from CLI flags.
func BuildGroupAndChargerUsageSummariesPayload(chargeStatisticsGroupAndChargerUsageSummariesGroupID string, chargeStatisticsGroupAndChargerUsageSummariesChargerID string, chargeStatisticsGroupAndChargerUsageSummariesInterval string, chargeStatisticsGroupAndChargerUsageSummariesFrom string, chargeStatisticsGroupAndChargerUsageSummariesTo string) (*chargestatistics.GroupAndChargerUsageSummariesPayload, error) {
	var err error
	var groupID string
	{
		groupID = chargeStatisticsGroupAndChargerUsageSummariesGroupID
		err = goa.MergeErrors(err, goa.ValidateFormat("groupId", groupID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
	}
	var chargerID string
	{
		chargerID = chargeStatisticsGroupAndChargerUsageSummariesChargerID
		if utf8.RuneCountInString(chargerID) < 1 {
			err = goa.MergeErrors(err, goa.InvalidLengthError("chargerId", chargerID, utf8.RuneCountInString(chargerID), 1, true))
		}
		if err != nil {
			return nil, err
		}
	}
	var interval string
	{
		interval = chargeStatisticsGroupAndChargerUsageSummariesInterval
		if !(interval == "day" || interval == "week" || interval == "month") {
			err = goa.MergeErrors(err, goa.InvalidEnumValueError("interval", interval, []any{"day", "week", "month"}))
		}
		if err != nil {
			return nil, err
		}
	}
	var from string
	{
		from = chargeStatisticsGroupAndChargerUsageSummariesFrom
		err = goa.MergeErrors(err, goa.ValidateFormat("from", from, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	var to string
	{
		to = chargeStatisticsGroupAndChargerUsageSummariesTo
		err = goa.MergeErrors(err, goa.ValidateFormat("to", to, goa.FormatDate))
		if err != nil {
			return nil, err
		}
	}
	v := &chargestatistics.GroupAndChargerUsageSummariesPayload{}
	v.GroupID = groupID
	v.ChargerID = chargerID
	v.Interval = interval
	v.From = from
	v.To = to

	return v, nil
}
