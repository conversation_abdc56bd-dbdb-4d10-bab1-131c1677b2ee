// Code generated by goa v3.20.1, DO NOT EDIT.
//
// usage HTTP server types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	usage "experience/libs/data-platform/api/contract/gen/usage"

	goa "goa.design/goa/v3/pkg"
)

// UsageByOrganisationResponseBody is the type of the "usage" service "Usage by
// organisation" endpoint HTTP response body.
type UsageByOrganisationResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta" json:"meta" xml:"meta"`
}

// UsageByOrganisationAndSiteResponseBody is the type of the "usage" service
// "Usage by organisation and site" endpoint HTTP response body.
type UsageByOrganisationAndSiteResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta" json:"meta" xml:"meta"`
}

// UsageByOrganisationAndChargerResponseBody is the type of the "usage" service
// "Usage by organisation and charger" endpoint HTTP response body.
type UsageByOrganisationAndChargerResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta" json:"meta" xml:"meta"`
}

// UsageByOrganisationBadRequestResponseBody is the type of the "usage" service
// "Usage by organisation" endpoint HTTP response body for the "bad_request"
// error.
type UsageByOrganisationBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// UsageByOrganisationAndSiteBadRequestResponseBody is the type of the "usage"
// service "Usage by organisation and site" endpoint HTTP response body for the
// "bad_request" error.
type UsageByOrganisationAndSiteBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// UsageByOrganisationAndChargerBadRequestResponseBody is the type of the
// "usage" service "Usage by organisation and charger" endpoint HTTP response
// body for the "bad_request" error.
type UsageByOrganisationAndChargerBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name string `form:"name" json:"name" xml:"name"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID string `form:"id" json:"id" xml:"id"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message string `form:"message" json:"message" xml:"message"`
	// Is the error temporary?
	Temporary bool `form:"temporary" json:"temporary" xml:"temporary"`
	// Is the error a timeout?
	Timeout bool `form:"timeout" json:"timeout" xml:"timeout"`
	// Is the error a server-side fault?
	Fault bool `form:"fault" json:"fault" xml:"fault"`
}

// UsageResponseBody is used to define fields on response body types.
type UsageResponseBody struct {
	// The start date of this usage interval
	IntervalStartDate string `form:"intervalStartDate" json:"intervalStartDate" xml:"intervalStartDate"`
	// Energy used this period in kWh
	TotalUsage float64 `form:"totalUsage" json:"totalUsage" xml:"totalUsage"`
	// CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per
	// mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy
	// saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
	Co2Savings float64 `form:"co2Savings" json:"co2Savings" xml:"co2Savings"`
	// Revenue generated in pence
	RevenueGenerated int `form:"revenueGenerated" json:"revenueGenerated" xml:"revenueGenerated"`
	// Energy cost in pence
	Cost int `form:"cost" json:"cost" xml:"cost"`
}

// MetaResponseBody is used to define fields on response body types.
type MetaResponseBody struct {
	// Passed parameters
	Params map[string]any `form:"params" json:"params" xml:"params"`
}

// NewUsageByOrganisationResponseBody builds the HTTP response body from the
// result of the "Usage by organisation" endpoint of the "usage" service.
func NewUsageByOrganisationResponseBody(res *usage.UsageResponse) *UsageByOrganisationResponseBody {
	body := &UsageByOrganisationResponseBody{}
	if res.Usage != nil {
		body.Usage = make([]*UsageResponseBody, len(res.Usage))
		for i, val := range res.Usage {
			body.Usage[i] = marshalUsageUsageToUsageResponseBody(val)
		}
	}
	if res.Meta != nil {
		body.Meta = marshalUsageMetaToMetaResponseBody(res.Meta)
	}
	return body
}

// NewUsageByOrganisationAndSiteResponseBody builds the HTTP response body from
// the result of the "Usage by organisation and site" endpoint of the "usage"
// service.
func NewUsageByOrganisationAndSiteResponseBody(res *usage.UsageResponse) *UsageByOrganisationAndSiteResponseBody {
	body := &UsageByOrganisationAndSiteResponseBody{}
	if res.Usage != nil {
		body.Usage = make([]*UsageResponseBody, len(res.Usage))
		for i, val := range res.Usage {
			body.Usage[i] = marshalUsageUsageToUsageResponseBody(val)
		}
	}
	if res.Meta != nil {
		body.Meta = marshalUsageMetaToMetaResponseBody(res.Meta)
	}
	return body
}

// NewUsageByOrganisationAndChargerResponseBody builds the HTTP response body
// from the result of the "Usage by organisation and charger" endpoint of the
// "usage" service.
func NewUsageByOrganisationAndChargerResponseBody(res *usage.UsageResponse) *UsageByOrganisationAndChargerResponseBody {
	body := &UsageByOrganisationAndChargerResponseBody{}
	if res.Usage != nil {
		body.Usage = make([]*UsageResponseBody, len(res.Usage))
		for i, val := range res.Usage {
			body.Usage[i] = marshalUsageUsageToUsageResponseBody(val)
		}
	}
	if res.Meta != nil {
		body.Meta = marshalUsageMetaToMetaResponseBody(res.Meta)
	}
	return body
}

// NewUsageByOrganisationBadRequestResponseBody builds the HTTP response body
// from the result of the "Usage by organisation" endpoint of the "usage"
// service.
func NewUsageByOrganisationBadRequestResponseBody(res *goa.ServiceError) *UsageByOrganisationBadRequestResponseBody {
	body := &UsageByOrganisationBadRequestResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewUsageByOrganisationAndSiteBadRequestResponseBody builds the HTTP response
// body from the result of the "Usage by organisation and site" endpoint of the
// "usage" service.
func NewUsageByOrganisationAndSiteBadRequestResponseBody(res *goa.ServiceError) *UsageByOrganisationAndSiteBadRequestResponseBody {
	body := &UsageByOrganisationAndSiteBadRequestResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewUsageByOrganisationAndChargerBadRequestResponseBody builds the HTTP
// response body from the result of the "Usage by organisation and charger"
// endpoint of the "usage" service.
func NewUsageByOrganisationAndChargerBadRequestResponseBody(res *goa.ServiceError) *UsageByOrganisationAndChargerBadRequestResponseBody {
	body := &UsageByOrganisationAndChargerBadRequestResponseBody{
		Name:      res.Name,
		ID:        res.ID,
		Message:   res.Message,
		Temporary: res.Temporary,
		Timeout:   res.Timeout,
		Fault:     res.Fault,
	}
	return body
}

// NewUsageByOrganisationPayload builds a usage service Usage by organisation
// endpoint payload.
func NewUsageByOrganisationPayload(organisationID string, from string, to string, interval string) *usage.UsageByOrganisationPayload {
	v := &usage.UsageByOrganisationPayload{}
	v.OrganisationID = organisationID
	v.From = from
	v.To = to
	v.Interval = interval

	return v
}

// NewUsageByOrganisationAndSitePayload builds a usage service Usage by
// organisation and site endpoint payload.
func NewUsageByOrganisationAndSitePayload(organisationID string, siteID int32, from string, to string, interval string) *usage.UsageByOrganisationAndSitePayload {
	v := &usage.UsageByOrganisationAndSitePayload{}
	v.OrganisationID = organisationID
	v.SiteID = siteID
	v.From = from
	v.To = to
	v.Interval = interval

	return v
}

// NewUsageByOrganisationAndChargerPayload builds a usage service Usage by
// organisation and charger endpoint payload.
func NewUsageByOrganisationAndChargerPayload(organisationID string, locationID int32, from string, to string, interval string) *usage.UsageByOrganisationAndChargerPayload {
	v := &usage.UsageByOrganisationAndChargerPayload{}
	v.OrganisationID = organisationID
	v.LocationID = locationID
	v.From = from
	v.To = to
	v.Interval = interval

	return v
}
