// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the usage service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"fmt"
)

// UsageByOrganisationUsagePath returns the URL path to the usage service Usage by organisation HTTP endpoint.
func UsageByOrganisationUsagePath(organisationID string) string {
	return fmt.Sprintf("/organisations/%v/stats", organisationID)
}

// UsageByOrganisationAndSiteUsagePath returns the URL path to the usage service Usage by organisation and site HTTP endpoint.
func UsageByOrganisationAndSiteUsagePath(organisationID string, siteID int32) string {
	return fmt.Sprintf("/organisations/%v/sites/%v/stats", organisationID, siteID)
}

// UsageByOrganisationAndChargerUsagePath returns the URL path to the usage service Usage by organisation and charger HTTP endpoint.
func UsageByOrganisationAndChargerUsagePath(organisationID string, locationID int32) string {
	return fmt.Sprintf("/organisations/%v/chargers/%v/stats", organisationID, locationID)
}
