// Code generated by goa v3.20.1, DO NOT EDIT.
//
// usage HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	usage "experience/libs/data-platform/api/contract/gen/usage"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildUsageByOrganisationRequest instantiates a HTTP request object with
// method and path set to call the "usage" service "Usage by organisation"
// endpoint
func (c *Client) BuildUsageByOrganisationRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		organisationID string
	)
	{
		p, ok := v.(*usage.UsageByOrganisationPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("usage", "Usage by organisation", "*usage.UsageByOrganisationPayload", v)
		}
		organisationID = p.OrganisationID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: UsageByOrganisationUsagePath(organisationID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("usage", "Usage by organisation", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeUsageByOrganisationRequest returns an encoder for requests sent to the
// usage Usage by organisation server.
func EncodeUsageByOrganisationRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*usage.UsageByOrganisationPayload)
		if !ok {
			return goahttp.ErrInvalidType("usage", "Usage by organisation", "*usage.UsageByOrganisationPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		values.Add("interval", p.Interval)
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeUsageByOrganisationResponse returns a decoder for responses returned
// by the usage Usage by organisation endpoint. restoreBody controls whether
// the response body should be restored after having been read.
// DecodeUsageByOrganisationResponse may return the following errors:
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - "organisation_not_found" (type *usage.OrganisationNotFound): http.StatusNotFound
//   - error: internal error
func DecodeUsageByOrganisationResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body UsageByOrganisationResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation", err)
			}
			err = ValidateUsageByOrganisationResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation", err)
			}
			res := NewUsageByOrganisationUsageResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			var (
				body UsageByOrganisationBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation", err)
			}
			err = ValidateUsageByOrganisationBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation", err)
			}
			return nil, NewUsageByOrganisationBadRequest(&body)
		case http.StatusNotFound:
			return nil, NewUsageByOrganisationOrganisationNotFound()
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("usage", "Usage by organisation", resp.StatusCode, string(body))
		}
	}
}

// BuildUsageByOrganisationAndSiteRequest instantiates a HTTP request object
// with method and path set to call the "usage" service "Usage by organisation
// and site" endpoint
func (c *Client) BuildUsageByOrganisationAndSiteRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		organisationID string
		siteID         int32
	)
	{
		p, ok := v.(*usage.UsageByOrganisationAndSitePayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("usage", "Usage by organisation and site", "*usage.UsageByOrganisationAndSitePayload", v)
		}
		organisationID = p.OrganisationID
		siteID = p.SiteID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: UsageByOrganisationAndSiteUsagePath(organisationID, siteID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("usage", "Usage by organisation and site", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeUsageByOrganisationAndSiteRequest returns an encoder for requests sent
// to the usage Usage by organisation and site server.
func EncodeUsageByOrganisationAndSiteRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*usage.UsageByOrganisationAndSitePayload)
		if !ok {
			return goahttp.ErrInvalidType("usage", "Usage by organisation and site", "*usage.UsageByOrganisationAndSitePayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		values.Add("interval", p.Interval)
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeUsageByOrganisationAndSiteResponse returns a decoder for responses
// returned by the usage Usage by organisation and site endpoint. restoreBody
// controls whether the response body should be restored after having been read.
// DecodeUsageByOrganisationAndSiteResponse may return the following errors:
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - "organisation_not_found" (type *usage.OrganisationNotFound): http.StatusNotFound
//   - "site_not_found" (type *usage.SiteNotFound): http.StatusNotFound
//   - error: internal error
func DecodeUsageByOrganisationAndSiteResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body UsageByOrganisationAndSiteResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation and site", err)
			}
			err = ValidateUsageByOrganisationAndSiteResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation and site", err)
			}
			res := NewUsageByOrganisationAndSiteUsageResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			var (
				body UsageByOrganisationAndSiteBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation and site", err)
			}
			err = ValidateUsageByOrganisationAndSiteBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation and site", err)
			}
			return nil, NewUsageByOrganisationAndSiteBadRequest(&body)
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "organisation_not_found":
				return nil, NewUsageByOrganisationAndSiteOrganisationNotFound()
			case "site_not_found":
				return nil, NewUsageByOrganisationAndSiteSiteNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("usage", "Usage by organisation and site", resp.StatusCode, string(body))
			}
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("usage", "Usage by organisation and site", resp.StatusCode, string(body))
		}
	}
}

// BuildUsageByOrganisationAndChargerRequest instantiates a HTTP request object
// with method and path set to call the "usage" service "Usage by organisation
// and charger" endpoint
func (c *Client) BuildUsageByOrganisationAndChargerRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		organisationID string
		locationID     int32
	)
	{
		p, ok := v.(*usage.UsageByOrganisationAndChargerPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("usage", "Usage by organisation and charger", "*usage.UsageByOrganisationAndChargerPayload", v)
		}
		organisationID = p.OrganisationID
		locationID = p.LocationID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: UsageByOrganisationAndChargerUsagePath(organisationID, locationID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("usage", "Usage by organisation and charger", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeUsageByOrganisationAndChargerRequest returns an encoder for requests
// sent to the usage Usage by organisation and charger server.
func EncodeUsageByOrganisationAndChargerRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*usage.UsageByOrganisationAndChargerPayload)
		if !ok {
			return goahttp.ErrInvalidType("usage", "Usage by organisation and charger", "*usage.UsageByOrganisationAndChargerPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		values.Add("interval", p.Interval)
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeUsageByOrganisationAndChargerResponse returns a decoder for responses
// returned by the usage Usage by organisation and charger endpoint.
// restoreBody controls whether the response body should be restored after
// having been read.
// DecodeUsageByOrganisationAndChargerResponse may return the following errors:
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - "charger_not_found" (type *usage.ChargerNotFound): http.StatusNotFound
//   - "organisation_not_found" (type *usage.OrganisationNotFound): http.StatusNotFound
//   - error: internal error
func DecodeUsageByOrganisationAndChargerResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body UsageByOrganisationAndChargerResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation and charger", err)
			}
			err = ValidateUsageByOrganisationAndChargerResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation and charger", err)
			}
			res := NewUsageByOrganisationAndChargerUsageResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			var (
				body UsageByOrganisationAndChargerBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("usage", "Usage by organisation and charger", err)
			}
			err = ValidateUsageByOrganisationAndChargerBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("usage", "Usage by organisation and charger", err)
			}
			return nil, NewUsageByOrganisationAndChargerBadRequest(&body)
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "charger_not_found":
				return nil, NewUsageByOrganisationAndChargerChargerNotFound()
			case "organisation_not_found":
				return nil, NewUsageByOrganisationAndChargerOrganisationNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("usage", "Usage by organisation and charger", resp.StatusCode, string(body))
			}
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("usage", "Usage by organisation and charger", resp.StatusCode, string(body))
		}
	}
}

// unmarshalUsageResponseBodyToUsageUsage builds a value of type *usage.Usage
// from a value of type *UsageResponseBody.
func unmarshalUsageResponseBodyToUsageUsage(v *UsageResponseBody) *usage.Usage {
	if v == nil {
		return nil
	}
	res := &usage.Usage{
		IntervalStartDate: *v.IntervalStartDate,
		TotalUsage:        *v.TotalUsage,
		Co2Savings:        *v.Co2Savings,
		RevenueGenerated:  *v.RevenueGenerated,
		Cost:              *v.Cost,
	}

	return res
}

// unmarshalMetaResponseBodyToUsageMeta builds a value of type *usage.Meta from
// a value of type *MetaResponseBody.
func unmarshalMetaResponseBodyToUsageMeta(v *MetaResponseBody) *usage.Meta {
	res := &usage.Meta{}
	res.Params = make(map[string]any, len(v.Params))
	for key, val := range v.Params {
		tk := key
		tv := val
		res.Params[tk] = tv
	}

	return res
}
