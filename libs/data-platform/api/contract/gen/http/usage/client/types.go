// Code generated by goa v3.20.1, DO NOT EDIT.
//
// usage HTTP client types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	usage "experience/libs/data-platform/api/contract/gen/usage"

	goa "goa.design/goa/v3/pkg"
)

// UsageByOrganisationResponseBody is the type of the "usage" service "Usage by
// organisation" endpoint HTTP response body.
type UsageByOrganisationResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta,omitempty" json:"meta,omitempty" xml:"meta,omitempty"`
}

// UsageByOrganisationAndSiteResponseBody is the type of the "usage" service
// "Usage by organisation and site" endpoint HTTP response body.
type UsageByOrganisationAndSiteResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta,omitempty" json:"meta,omitempty" xml:"meta,omitempty"`
}

// UsageByOrganisationAndChargerResponseBody is the type of the "usage" service
// "Usage by organisation and charger" endpoint HTTP response body.
type UsageByOrganisationAndChargerResponseBody struct {
	Usage []*UsageResponseBody `form:"usage,omitempty" json:"usage,omitempty" xml:"usage,omitempty"`
	Meta  *MetaResponseBody    `form:"meta,omitempty" json:"meta,omitempty" xml:"meta,omitempty"`
}

// UsageByOrganisationBadRequestResponseBody is the type of the "usage" service
// "Usage by organisation" endpoint HTTP response body for the "bad_request"
// error.
type UsageByOrganisationBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name *string `form:"name,omitempty" json:"name,omitempty" xml:"name,omitempty"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID *string `form:"id,omitempty" json:"id,omitempty" xml:"id,omitempty"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message *string `form:"message,omitempty" json:"message,omitempty" xml:"message,omitempty"`
	// Is the error temporary?
	Temporary *bool `form:"temporary,omitempty" json:"temporary,omitempty" xml:"temporary,omitempty"`
	// Is the error a timeout?
	Timeout *bool `form:"timeout,omitempty" json:"timeout,omitempty" xml:"timeout,omitempty"`
	// Is the error a server-side fault?
	Fault *bool `form:"fault,omitempty" json:"fault,omitempty" xml:"fault,omitempty"`
}

// UsageByOrganisationAndSiteBadRequestResponseBody is the type of the "usage"
// service "Usage by organisation and site" endpoint HTTP response body for the
// "bad_request" error.
type UsageByOrganisationAndSiteBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name *string `form:"name,omitempty" json:"name,omitempty" xml:"name,omitempty"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID *string `form:"id,omitempty" json:"id,omitempty" xml:"id,omitempty"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message *string `form:"message,omitempty" json:"message,omitempty" xml:"message,omitempty"`
	// Is the error temporary?
	Temporary *bool `form:"temporary,omitempty" json:"temporary,omitempty" xml:"temporary,omitempty"`
	// Is the error a timeout?
	Timeout *bool `form:"timeout,omitempty" json:"timeout,omitempty" xml:"timeout,omitempty"`
	// Is the error a server-side fault?
	Fault *bool `form:"fault,omitempty" json:"fault,omitempty" xml:"fault,omitempty"`
}

// UsageByOrganisationAndChargerBadRequestResponseBody is the type of the
// "usage" service "Usage by organisation and charger" endpoint HTTP response
// body for the "bad_request" error.
type UsageByOrganisationAndChargerBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name *string `form:"name,omitempty" json:"name,omitempty" xml:"name,omitempty"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID *string `form:"id,omitempty" json:"id,omitempty" xml:"id,omitempty"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message *string `form:"message,omitempty" json:"message,omitempty" xml:"message,omitempty"`
	// Is the error temporary?
	Temporary *bool `form:"temporary,omitempty" json:"temporary,omitempty" xml:"temporary,omitempty"`
	// Is the error a timeout?
	Timeout *bool `form:"timeout,omitempty" json:"timeout,omitempty" xml:"timeout,omitempty"`
	// Is the error a server-side fault?
	Fault *bool `form:"fault,omitempty" json:"fault,omitempty" xml:"fault,omitempty"`
}

// UsageResponseBody is used to define fields on response body types.
type UsageResponseBody struct {
	// The start date of this usage interval
	IntervalStartDate *string `form:"intervalStartDate,omitempty" json:"intervalStartDate,omitempty" xml:"intervalStartDate,omitempty"`
	// Energy used this period in kWh
	TotalUsage *float64 `form:"totalUsage,omitempty" json:"totalUsage,omitempty" xml:"totalUsage,omitempty"`
	// CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per
	// mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy
	// saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
	Co2Savings *float64 `form:"co2Savings,omitempty" json:"co2Savings,omitempty" xml:"co2Savings,omitempty"`
	// Revenue generated in pence
	RevenueGenerated *int `form:"revenueGenerated,omitempty" json:"revenueGenerated,omitempty" xml:"revenueGenerated,omitempty"`
	// Energy cost in pence
	Cost *int `form:"cost,omitempty" json:"cost,omitempty" xml:"cost,omitempty"`
}

// MetaResponseBody is used to define fields on response body types.
type MetaResponseBody struct {
	// Passed parameters
	Params map[string]any `form:"params,omitempty" json:"params,omitempty" xml:"params,omitempty"`
}

// NewUsageByOrganisationUsageResponseOK builds a "usage" service "Usage by
// organisation" endpoint result from a HTTP "OK" response.
func NewUsageByOrganisationUsageResponseOK(body *UsageByOrganisationResponseBody) *usage.UsageResponse {
	v := &usage.UsageResponse{}
	if body.Usage != nil {
		v.Usage = make([]*usage.Usage, len(body.Usage))
		for i, val := range body.Usage {
			v.Usage[i] = unmarshalUsageResponseBodyToUsageUsage(val)
		}
	}
	v.Meta = unmarshalMetaResponseBodyToUsageMeta(body.Meta)

	return v
}

// NewUsageByOrganisationBadRequest builds a usage service Usage by
// organisation endpoint bad_request error.
func NewUsageByOrganisationBadRequest(body *UsageByOrganisationBadRequestResponseBody) *goa.ServiceError {
	v := &goa.ServiceError{
		Name:      *body.Name,
		ID:        *body.ID,
		Message:   *body.Message,
		Temporary: *body.Temporary,
		Timeout:   *body.Timeout,
		Fault:     *body.Fault,
	}

	return v
}

// NewUsageByOrganisationOrganisationNotFound builds a usage service Usage by
// organisation endpoint organisation_not_found error.
func NewUsageByOrganisationOrganisationNotFound() *usage.OrganisationNotFound {
	v := &usage.OrganisationNotFound{}

	return v
}

// NewUsageByOrganisationAndSiteUsageResponseOK builds a "usage" service "Usage
// by organisation and site" endpoint result from a HTTP "OK" response.
func NewUsageByOrganisationAndSiteUsageResponseOK(body *UsageByOrganisationAndSiteResponseBody) *usage.UsageResponse {
	v := &usage.UsageResponse{}
	if body.Usage != nil {
		v.Usage = make([]*usage.Usage, len(body.Usage))
		for i, val := range body.Usage {
			v.Usage[i] = unmarshalUsageResponseBodyToUsageUsage(val)
		}
	}
	v.Meta = unmarshalMetaResponseBodyToUsageMeta(body.Meta)

	return v
}

// NewUsageByOrganisationAndSiteBadRequest builds a usage service Usage by
// organisation and site endpoint bad_request error.
func NewUsageByOrganisationAndSiteBadRequest(body *UsageByOrganisationAndSiteBadRequestResponseBody) *goa.ServiceError {
	v := &goa.ServiceError{
		Name:      *body.Name,
		ID:        *body.ID,
		Message:   *body.Message,
		Temporary: *body.Temporary,
		Timeout:   *body.Timeout,
		Fault:     *body.Fault,
	}

	return v
}

// NewUsageByOrganisationAndSiteOrganisationNotFound builds a usage service
// Usage by organisation and site endpoint organisation_not_found error.
func NewUsageByOrganisationAndSiteOrganisationNotFound() *usage.OrganisationNotFound {
	v := &usage.OrganisationNotFound{}

	return v
}

// NewUsageByOrganisationAndSiteSiteNotFound builds a usage service Usage by
// organisation and site endpoint site_not_found error.
func NewUsageByOrganisationAndSiteSiteNotFound() *usage.SiteNotFound {
	v := &usage.SiteNotFound{}

	return v
}

// NewUsageByOrganisationAndChargerUsageResponseOK builds a "usage" service
// "Usage by organisation and charger" endpoint result from a HTTP "OK"
// response.
func NewUsageByOrganisationAndChargerUsageResponseOK(body *UsageByOrganisationAndChargerResponseBody) *usage.UsageResponse {
	v := &usage.UsageResponse{}
	if body.Usage != nil {
		v.Usage = make([]*usage.Usage, len(body.Usage))
		for i, val := range body.Usage {
			v.Usage[i] = unmarshalUsageResponseBodyToUsageUsage(val)
		}
	}
	v.Meta = unmarshalMetaResponseBodyToUsageMeta(body.Meta)

	return v
}

// NewUsageByOrganisationAndChargerBadRequest builds a usage service Usage by
// organisation and charger endpoint bad_request error.
func NewUsageByOrganisationAndChargerBadRequest(body *UsageByOrganisationAndChargerBadRequestResponseBody) *goa.ServiceError {
	v := &goa.ServiceError{
		Name:      *body.Name,
		ID:        *body.ID,
		Message:   *body.Message,
		Temporary: *body.Temporary,
		Timeout:   *body.Timeout,
		Fault:     *body.Fault,
	}

	return v
}

// NewUsageByOrganisationAndChargerChargerNotFound builds a usage service Usage
// by organisation and charger endpoint charger_not_found error.
func NewUsageByOrganisationAndChargerChargerNotFound() *usage.ChargerNotFound {
	v := &usage.ChargerNotFound{}

	return v
}

// NewUsageByOrganisationAndChargerOrganisationNotFound builds a usage service
// Usage by organisation and charger endpoint organisation_not_found error.
func NewUsageByOrganisationAndChargerOrganisationNotFound() *usage.OrganisationNotFound {
	v := &usage.OrganisationNotFound{}

	return v
}

// ValidateUsageByOrganisationResponseBody runs the validations defined on
// Usage By OrganisationResponseBody
func ValidateUsageByOrganisationResponseBody(body *UsageByOrganisationResponseBody) (err error) {
	if body.Meta == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("meta", "body"))
	}
	for _, e := range body.Usage {
		if e != nil {
			if err2 := ValidateUsageResponseBody(e); err2 != nil {
				err = goa.MergeErrors(err, err2)
			}
		}
	}
	if body.Meta != nil {
		if err2 := ValidateMetaResponseBody(body.Meta); err2 != nil {
			err = goa.MergeErrors(err, err2)
		}
	}
	return
}

// ValidateUsageByOrganisationAndSiteResponseBody runs the validations defined
// on Usage By Organisation And SiteResponseBody
func ValidateUsageByOrganisationAndSiteResponseBody(body *UsageByOrganisationAndSiteResponseBody) (err error) {
	if body.Meta == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("meta", "body"))
	}
	for _, e := range body.Usage {
		if e != nil {
			if err2 := ValidateUsageResponseBody(e); err2 != nil {
				err = goa.MergeErrors(err, err2)
			}
		}
	}
	if body.Meta != nil {
		if err2 := ValidateMetaResponseBody(body.Meta); err2 != nil {
			err = goa.MergeErrors(err, err2)
		}
	}
	return
}

// ValidateUsageByOrganisationAndChargerResponseBody runs the validations
// defined on Usage By Organisation And ChargerResponseBody
func ValidateUsageByOrganisationAndChargerResponseBody(body *UsageByOrganisationAndChargerResponseBody) (err error) {
	if body.Meta == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("meta", "body"))
	}
	for _, e := range body.Usage {
		if e != nil {
			if err2 := ValidateUsageResponseBody(e); err2 != nil {
				err = goa.MergeErrors(err, err2)
			}
		}
	}
	if body.Meta != nil {
		if err2 := ValidateMetaResponseBody(body.Meta); err2 != nil {
			err = goa.MergeErrors(err, err2)
		}
	}
	return
}

// ValidateUsageByOrganisationBadRequestResponseBody runs the validations
// defined on Usage by organisation_bad_request_Response_Body
func ValidateUsageByOrganisationBadRequestResponseBody(body *UsageByOrganisationBadRequestResponseBody) (err error) {
	if body.Name == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("name", "body"))
	}
	if body.ID == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("id", "body"))
	}
	if body.Message == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("message", "body"))
	}
	if body.Temporary == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("temporary", "body"))
	}
	if body.Timeout == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("timeout", "body"))
	}
	if body.Fault == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("fault", "body"))
	}
	return
}

// ValidateUsageByOrganisationAndSiteBadRequestResponseBody runs the
// validations defined on Usage by organisation and
// site_bad_request_Response_Body
func ValidateUsageByOrganisationAndSiteBadRequestResponseBody(body *UsageByOrganisationAndSiteBadRequestResponseBody) (err error) {
	if body.Name == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("name", "body"))
	}
	if body.ID == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("id", "body"))
	}
	if body.Message == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("message", "body"))
	}
	if body.Temporary == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("temporary", "body"))
	}
	if body.Timeout == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("timeout", "body"))
	}
	if body.Fault == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("fault", "body"))
	}
	return
}

// ValidateUsageByOrganisationAndChargerBadRequestResponseBody runs the
// validations defined on Usage by organisation and
// charger_bad_request_Response_Body
func ValidateUsageByOrganisationAndChargerBadRequestResponseBody(body *UsageByOrganisationAndChargerBadRequestResponseBody) (err error) {
	if body.Name == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("name", "body"))
	}
	if body.ID == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("id", "body"))
	}
	if body.Message == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("message", "body"))
	}
	if body.Temporary == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("temporary", "body"))
	}
	if body.Timeout == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("timeout", "body"))
	}
	if body.Fault == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("fault", "body"))
	}
	return
}

// ValidateUsageResponseBody runs the validations defined on usageResponseBody
func ValidateUsageResponseBody(body *UsageResponseBody) (err error) {
	if body.IntervalStartDate == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("intervalStartDate", "body"))
	}
	if body.TotalUsage == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("totalUsage", "body"))
	}
	if body.Co2Savings == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("co2Savings", "body"))
	}
	if body.RevenueGenerated == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("revenueGenerated", "body"))
	}
	if body.Cost == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("cost", "body"))
	}
	if body.IntervalStartDate != nil {
		err = goa.MergeErrors(err, goa.ValidateFormat("body.intervalStartDate", *body.IntervalStartDate, goa.FormatDate))
	}
	return
}

// ValidateMetaResponseBody runs the validations defined on metaResponseBody
func ValidateMetaResponseBody(body *MetaResponseBody) (err error) {
	if body.Params == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("params", "body"))
	}
	return
}
