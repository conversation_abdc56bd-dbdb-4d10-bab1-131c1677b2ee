// Code generated by goa v3.20.1, DO NOT EDIT.
//
// drivers HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	drivers "experience/libs/data-platform/api/contract/gen/drivers"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildRetrieveChargeRequest instantiates a HTTP request object with method
// and path set to call the "drivers" service "retrieveCharge" endpoint
func (c *Client) BuildRetrieveChargeRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		driverID string
		chargeID string
	)
	{
		p, ok := v.(*drivers.RetrieveChargePayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("drivers", "retrieveCharge", "*drivers.RetrieveChargePayload", v)
		}
		driverID = p.DriverID
		chargeID = p.ChargeID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveChargeDriversPath(driverID, chargeID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("drivers", "retrieveCharge", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// DecodeRetrieveChargeResponse returns a decoder for responses returned by the
// drivers retrieveCharge endpoint. restoreBody controls whether the response
// body should be restored after having been read.
// DecodeRetrieveChargeResponse may return the following errors:
//   - "charge_not_found" (type *drivers.DriversChargeNotFound): http.StatusNotFound
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeRetrieveChargeResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveChargeResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveCharge", err)
			}
			err = ValidateRetrieveChargeResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveCharge", err)
			}
			res := NewRetrieveChargeDriversChargeResponseOK(&body)
			return res, nil
		case http.StatusNotFound:
			var (
				body RetrieveChargeChargeNotFoundResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveCharge", err)
			}
			err = ValidateRetrieveChargeChargeNotFoundResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveCharge", err)
			}
			return nil, NewRetrieveChargeChargeNotFound(&body)
		case http.StatusBadRequest:
			var (
				body RetrieveChargeBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveCharge", err)
			}
			err = ValidateRetrieveChargeBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveCharge", err)
			}
			return nil, NewRetrieveChargeBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("drivers", "retrieveCharge", resp.StatusCode, string(body))
		}
	}
}

// BuildRetrieveChargesRequest instantiates a HTTP request object with method
// and path set to call the "drivers" service "retrieveCharges" endpoint
func (c *Client) BuildRetrieveChargesRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		driverID string
	)
	{
		p, ok := v.(*drivers.RetrieveChargesPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("drivers", "retrieveCharges", "*drivers.RetrieveChargesPayload", v)
		}
		driverID = p.DriverID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveChargesDriversPath(driverID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("drivers", "retrieveCharges", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeRetrieveChargesRequest returns an encoder for requests sent to the
// drivers retrieveCharges server.
func EncodeRetrieveChargesRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivers.RetrieveChargesPayload)
		if !ok {
			return goahttp.ErrInvalidType("drivers", "retrieveCharges", "*drivers.RetrieveChargesPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeRetrieveChargesResponse returns a decoder for responses returned by
// the drivers retrieveCharges endpoint. restoreBody controls whether the
// response body should be restored after having been read.
// DecodeRetrieveChargesResponse may return the following errors:
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeRetrieveChargesResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveChargesResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveCharges", err)
			}
			err = ValidateRetrieveChargesResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveCharges", err)
			}
			res := NewRetrieveChargesDriversChargesResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			var (
				body RetrieveChargesBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveCharges", err)
			}
			err = ValidateRetrieveChargesBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveCharges", err)
			}
			return nil, NewRetrieveChargesBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("drivers", "retrieveCharges", resp.StatusCode, string(body))
		}
	}
}

// BuildRetrieveStatsRequest instantiates a HTTP request object with method and
// path set to call the "drivers" service "retrieveStats" endpoint
func (c *Client) BuildRetrieveStatsRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		driverID string
	)
	{
		p, ok := v.(*drivers.RetrieveStatsPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("drivers", "retrieveStats", "*drivers.RetrieveStatsPayload", v)
		}
		driverID = p.DriverID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveStatsDriversPath(driverID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("drivers", "retrieveStats", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeRetrieveStatsRequest returns an encoder for requests sent to the
// drivers retrieveStats server.
func EncodeRetrieveStatsRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivers.RetrieveStatsPayload)
		if !ok {
			return goahttp.ErrInvalidType("drivers", "retrieveStats", "*drivers.RetrieveStatsPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		if p.Interval != nil {
			values.Add("interval", *p.Interval)
		}
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeRetrieveStatsResponse returns a decoder for responses returned by the
// drivers retrieveStats endpoint. restoreBody controls whether the response
// body should be restored after having been read.
// DecodeRetrieveStatsResponse may return the following errors:
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeRetrieveStatsResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveStatsResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveStats", err)
			}
			err = ValidateRetrieveStatsResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveStats", err)
			}
			res := NewRetrieveStatsDriverStatsResponseOK(&body)
			return res, nil
		case http.StatusBadRequest:
			var (
				body RetrieveStatsBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("drivers", "retrieveStats", err)
			}
			err = ValidateRetrieveStatsBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("drivers", "retrieveStats", err)
			}
			return nil, NewRetrieveStatsBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("drivers", "retrieveStats", resp.StatusCode, string(body))
		}
	}
}

// unmarshalChargeResponseBodyToDriversCharge builds a value of type
// *drivers.Charge from a value of type *ChargeResponseBody.
func unmarshalChargeResponseBodyToDriversCharge(v *ChargeResponseBody) *drivers.Charge {
	res := &drivers.Charge{
		ID:                    *v.ID,
		StartedAt:             v.StartedAt,
		EndedAt:               v.EndedAt,
		Duration:              v.Duration,
		EnergyTotal:           v.EnergyTotal,
		GenerationEnergyTotal: v.GenerationEnergyTotal,
		GridEnergyTotal:       v.GridEnergyTotal,
	}
	if v.Cost != nil {
		res.Cost = unmarshalMoneyResponseBodyToDriversMoney(v.Cost)
	}
	if v.ExpensedTo != nil {
		res.ExpensedTo = unmarshalExpensedToResponseBodyToDriversExpensedTo(v.ExpensedTo)
	}
	res.Charger = unmarshalChargerResponseBodyToDriversCharger(v.Charger)

	return res
}

// unmarshalMoneyResponseBodyToDriversMoney builds a value of type
// *drivers.Money from a value of type *MoneyResponseBody.
func unmarshalMoneyResponseBodyToDriversMoney(v *MoneyResponseBody) *drivers.Money {
	if v == nil {
		return nil
	}
	res := &drivers.Money{
		Amount:   *v.Amount,
		Currency: *v.Currency,
	}

	return res
}

// unmarshalExpensedToResponseBodyToDriversExpensedTo builds a value of type
// *drivers.ExpensedTo from a value of type *ExpensedToResponseBody.
func unmarshalExpensedToResponseBodyToDriversExpensedTo(v *ExpensedToResponseBody) *drivers.ExpensedTo {
	if v == nil {
		return nil
	}
	res := &drivers.ExpensedTo{
		ID:   *v.ID,
		Name: *v.Name,
	}

	return res
}

// unmarshalChargerResponseBodyToDriversCharger builds a value of type
// *drivers.Charger from a value of type *ChargerResponseBody.
func unmarshalChargerResponseBodyToDriversCharger(v *ChargerResponseBody) *drivers.Charger {
	res := &drivers.Charger{
		Type:              *v.Type,
		ID:                *v.ID,
		Name:              v.Name,
		Door:              *v.Door,
		SiteName:          v.SiteName,
		PluggedInAt:       v.PluggedInAt,
		UnpluggedAt:       v.UnpluggedAt,
		PluggedInDuration: v.PluggedInDuration,
	}

	return res
}

// unmarshalMetaResponseBodyToDriversMeta builds a value of type *drivers.Meta
// from a value of type *MetaResponseBody.
func unmarshalMetaResponseBodyToDriversMeta(v *MetaResponseBody) *drivers.Meta {
	res := &drivers.Meta{}
	res.Params = make(map[string]any, len(v.Params))
	for key, val := range v.Params {
		tk := key
		tv := val
		res.Params[tk] = tv
	}

	return res
}

// unmarshalChargesResponseBodyToDriversCharges builds a value of type
// *drivers.Charges from a value of type *ChargesResponseBody.
func unmarshalChargesResponseBodyToDriversCharges(v *ChargesResponseBody) *drivers.Charges {
	res := &drivers.Charges{
		Count: *v.Count,
	}
	res.Charges = make([]*drivers.Charge, len(v.Charges))
	for i, val := range v.Charges {
		res.Charges[i] = unmarshalChargeResponseBodyToDriversCharge(val)
	}

	return res
}

// unmarshalDriverStatsDataResponseBodyToDriversDriverStatsData builds a value
// of type *drivers.DriverStatsData from a value of type
// *DriverStatsDataResponseBody.
func unmarshalDriverStatsDataResponseBodyToDriversDriverStatsData(v *DriverStatsDataResponseBody) *drivers.DriverStatsData {
	res := &drivers.DriverStatsData{}
	res.Summary = unmarshalStatsSummaryResponseBodyToDriversStatsSummary(v.Summary)
	if v.Intervals != nil {
		res.Intervals = make([]*drivers.Interval, len(v.Intervals))
		for i, val := range v.Intervals {
			res.Intervals[i] = unmarshalIntervalResponseBodyToDriversInterval(val)
		}
	}

	return res
}

// unmarshalStatsSummaryResponseBodyToDriversStatsSummary builds a value of
// type *drivers.StatsSummary from a value of type *StatsSummaryResponseBody.
func unmarshalStatsSummaryResponseBodyToDriversStatsSummary(v *StatsSummaryResponseBody) *drivers.StatsSummary {
	res := &drivers.StatsSummary{}
	res.Energy = unmarshalEnergyResponseBodyToDriversEnergy(v.Energy)
	res.Cost = unmarshalCostResponseBodyToDriversCost(v.Cost)
	res.Duration = unmarshalDurationResponseBodyToDriversDuration(v.Duration)

	return res
}

// unmarshalEnergyResponseBodyToDriversEnergy builds a value of type
// *drivers.Energy from a value of type *EnergyResponseBody.
func unmarshalEnergyResponseBodyToDriversEnergy(v *EnergyResponseBody) *drivers.Energy {
	res := &drivers.Energy{}
	res.Home = unmarshalBreakdownResponseBodyToDriversBreakdown(v.Home)
	res.Private = unmarshalBreakdownResponseBodyToDriversBreakdown(v.Private)
	res.Public = unmarshalBreakdownResponseBodyToDriversBreakdown(v.Public)
	res.Total = unmarshalBreakdownResponseBodyToDriversBreakdown(v.Total)

	return res
}

// unmarshalBreakdownResponseBodyToDriversBreakdown builds a value of type
// *drivers.Breakdown from a value of type *BreakdownResponseBody.
func unmarshalBreakdownResponseBodyToDriversBreakdown(v *BreakdownResponseBody) *drivers.Breakdown {
	res := &drivers.Breakdown{
		Grid:       v.Grid,
		Generation: v.Generation,
		Total:      *v.Total,
	}

	return res
}

// unmarshalCostResponseBodyToDriversCost builds a value of type *drivers.Cost
// from a value of type *CostResponseBody.
func unmarshalCostResponseBodyToDriversCost(v *CostResponseBody) *drivers.Cost {
	res := &drivers.Cost{}
	res.Home = make([]*drivers.MoneyInt64, len(v.Home))
	for i, val := range v.Home {
		res.Home[i] = unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64(val)
	}
	res.Private = make([]*drivers.MoneyInt64, len(v.Private))
	for i, val := range v.Private {
		res.Private[i] = unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64(val)
	}
	res.Public = make([]*drivers.MoneyInt64, len(v.Public))
	for i, val := range v.Public {
		res.Public[i] = unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64(val)
	}
	res.Total = make([]*drivers.MoneyInt64, len(v.Total))
	for i, val := range v.Total {
		res.Total[i] = unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64(val)
	}

	return res
}

// unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64 builds a value of type
// *drivers.MoneyInt64 from a value of type *MoneyInt64ResponseBody.
func unmarshalMoneyInt64ResponseBodyToDriversMoneyInt64(v *MoneyInt64ResponseBody) *drivers.MoneyInt64 {
	res := &drivers.MoneyInt64{
		Amount:   *v.Amount,
		Currency: *v.Currency,
	}

	return res
}

// unmarshalDurationResponseBodyToDriversDuration builds a value of type
// *drivers.Duration from a value of type *DurationResponseBody.
func unmarshalDurationResponseBodyToDriversDuration(v *DurationResponseBody) *drivers.Duration {
	res := &drivers.Duration{
		Home:    *v.Home,
		Private: *v.Private,
		Public:  *v.Public,
		Total:   *v.Total,
	}

	return res
}

// unmarshalIntervalResponseBodyToDriversInterval builds a value of type
// *drivers.Interval from a value of type *IntervalResponseBody.
func unmarshalIntervalResponseBodyToDriversInterval(v *IntervalResponseBody) *drivers.Interval {
	if v == nil {
		return nil
	}
	res := &drivers.Interval{
		From: *v.From,
		To:   *v.To,
	}
	res.Stats = unmarshalStatsSummaryResponseBodyToDriversStatsSummary(v.Stats)

	return res
}
