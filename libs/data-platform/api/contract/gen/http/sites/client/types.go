// Code generated by goa v3.20.1, DO NOT EDIT.
//
// sites HTTP client types
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	sites "experience/libs/data-platform/api/contract/gen/sites"

	goa "goa.design/goa/v3/pkg"
)

// RetrieveChargeStatsGroupedBySiteResponseBody is the type of the "sites"
// service "retrieveChargeStatsGroupedBySite" endpoint HTTP response body.
type RetrieveChargeStatsGroupedBySiteResponseBody struct {
	// Count of sites returned.
	Count *int `form:"count,omitempty" json:"count,omitempty" xml:"count,omitempty"`
	// Statistics report inclusive start date eg: 2022-01-01
	From *string `form:"from,omitempty" json:"from,omitempty" xml:"from,omitempty"`
	// Statistics report inclusive end date eg: 2022-01-31
	To *string `form:"to,omitempty" json:"to,omitempty" xml:"to,omitempty"`
	// Charge data for site.
	Data []*SiteStatsResponseBody `form:"data,omitempty" json:"data,omitempty" xml:"data,omitempty"`
}

// RetrieveChargeStatsGroupedBySiteBadRequestResponseBody is the type of the
// "sites" service "retrieveChargeStatsGroupedBySite" endpoint HTTP response
// body for the "bad_request" error.
type RetrieveChargeStatsGroupedBySiteBadRequestResponseBody struct {
	// Name is the name of this class of errors.
	Name *string `form:"name,omitempty" json:"name,omitempty" xml:"name,omitempty"`
	// ID is a unique identifier for this particular occurrence of the problem.
	ID *string `form:"id,omitempty" json:"id,omitempty" xml:"id,omitempty"`
	// Message is a human-readable explanation specific to this occurrence of the
	// problem.
	Message *string `form:"message,omitempty" json:"message,omitempty" xml:"message,omitempty"`
	// Is the error temporary?
	Temporary *bool `form:"temporary,omitempty" json:"temporary,omitempty" xml:"temporary,omitempty"`
	// Is the error a timeout?
	Timeout *bool `form:"timeout,omitempty" json:"timeout,omitempty" xml:"timeout,omitempty"`
	// Is the error a server-side fault?
	Fault *bool `form:"fault,omitempty" json:"fault,omitempty" xml:"fault,omitempty"`
}

// SiteStatsResponseBody is used to define fields on response body types.
type SiteStatsResponseBody struct {
	// Site unique identifier.
	ID *string `form:"id,omitempty" json:"id,omitempty" xml:"id,omitempty"`
	// Site name.
	Name *string `form:"name,omitempty" json:"name,omitempty" xml:"name,omitempty"`
	// Group unique identifier.
	GroupID *string `form:"groupId,omitempty" json:"groupId,omitempty" xml:"groupId,omitempty"`
	// Group Name.
	GroupName *string `form:"groupName,omitempty" json:"groupName,omitempty" xml:"groupName,omitempty"`
	// Energy used in kWh.
	TotalEnergy *float64 `form:"totalEnergy,omitempty" json:"totalEnergy,omitempty" xml:"totalEnergy,omitempty"`
	// Revenue generated in pence
	RevenueGenerated *int `form:"revenueGenerated,omitempty" json:"revenueGenerated,omitempty" xml:"revenueGenerated,omitempty"`
}

// NewRetrieveChargeStatsGroupedBySiteSiteStatsResponseOK builds a "sites"
// service "retrieveChargeStatsGroupedBySite" endpoint result from a HTTP "OK"
// response.
func NewRetrieveChargeStatsGroupedBySiteSiteStatsResponseOK(body *RetrieveChargeStatsGroupedBySiteResponseBody) *sites.SiteStatsResponse {
	v := &sites.SiteStatsResponse{
		Count: *body.Count,
		From:  *body.From,
		To:    *body.To,
	}
	v.Data = make([]*sites.SiteStats, len(body.Data))
	for i, val := range body.Data {
		v.Data[i] = unmarshalSiteStatsResponseBodyToSitesSiteStats(val)
	}

	return v
}

// NewRetrieveChargeStatsGroupedBySiteBadRequest builds a sites service
// retrieveChargeStatsGroupedBySite endpoint bad_request error.
func NewRetrieveChargeStatsGroupedBySiteBadRequest(body *RetrieveChargeStatsGroupedBySiteBadRequestResponseBody) *goa.ServiceError {
	v := &goa.ServiceError{
		Name:      *body.Name,
		ID:        *body.ID,
		Message:   *body.Message,
		Temporary: *body.Temporary,
		Timeout:   *body.Timeout,
		Fault:     *body.Fault,
	}

	return v
}

// ValidateRetrieveChargeStatsGroupedBySiteResponseBody runs the validations
// defined on RetrieveChargeStatsGroupedBySiteResponseBody
func ValidateRetrieveChargeStatsGroupedBySiteResponseBody(body *RetrieveChargeStatsGroupedBySiteResponseBody) (err error) {
	if body.Count == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("count", "body"))
	}
	if body.From == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("from", "body"))
	}
	if body.To == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("to", "body"))
	}
	if body.Data == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("data", "body"))
	}
	if body.From != nil {
		err = goa.MergeErrors(err, goa.ValidateFormat("body.from", *body.From, goa.FormatDate))
	}
	if body.To != nil {
		err = goa.MergeErrors(err, goa.ValidateFormat("body.to", *body.To, goa.FormatDate))
	}
	for _, e := range body.Data {
		if e != nil {
			if err2 := ValidateSiteStatsResponseBody(e); err2 != nil {
				err = goa.MergeErrors(err, err2)
			}
		}
	}
	return
}

// ValidateRetrieveChargeStatsGroupedBySiteBadRequestResponseBody runs the
// validations defined on
// retrieveChargeStatsGroupedBySite_bad_request_response_body
func ValidateRetrieveChargeStatsGroupedBySiteBadRequestResponseBody(body *RetrieveChargeStatsGroupedBySiteBadRequestResponseBody) (err error) {
	if body.Name == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("name", "body"))
	}
	if body.ID == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("id", "body"))
	}
	if body.Message == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("message", "body"))
	}
	if body.Temporary == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("temporary", "body"))
	}
	if body.Timeout == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("timeout", "body"))
	}
	if body.Fault == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("fault", "body"))
	}
	return
}

// ValidateSiteStatsResponseBody runs the validations defined on
// siteStatsResponseBody
func ValidateSiteStatsResponseBody(body *SiteStatsResponseBody) (err error) {
	if body.ID == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("id", "body"))
	}
	if body.TotalEnergy == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("totalEnergy", "body"))
	}
	if body.RevenueGenerated == nil {
		err = goa.MergeErrors(err, goa.MissingFieldError("revenueGenerated", "body"))
	}
	if body.ID != nil {
		err = goa.MergeErrors(err, goa.ValidateFormat("body.id", *body.ID, goa.FormatUUID))
	}
	if body.GroupID != nil {
		err = goa.MergeErrors(err, goa.ValidateFormat("body.groupId", *body.GroupID, goa.FormatUUID))
	}
	return
}
