// Code generated by goa v3.20.1, DO NOT EDIT.
//
// HTTP request path constructors for the Driver charges service.
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"fmt"
)

// RetrieveManyDriverChargesPath returns the URL path to the Driver charges service Retrieve many HTTP endpoint.
func RetrieveManyDriverChargesPath(organisationID string, userID string) string {
	return fmt.Sprintf("/organisations/%v/drivers/%v/charges", organisationID, userID)
}

// RetrieveOrganisationDriversStatisticsDriverChargesPath returns the URL path to the Driver charges service Retrieve organisation drivers statistics HTTP endpoint.
func RetrieveOrganisationDriversStatisticsDriverChargesPath(organisationID string) string {
	return fmt.Sprintf("/organisations/%v/drivers/stats", organisationID)
}

// CreateDriverExpensesDriverChargesPath returns the URL path to the Driver charges service Create driver expenses HTTP endpoint.
func CreateDriverExpensesDriverChargesPath(driverID int, organisationID int) string {
	return fmt.Sprintf("/drivers/%v/organisations/%v/expenses", driverID, organisationID)
}

// SubmitDriverExpensesDriverChargesPath returns the URL path to the Driver charges service Submit driver expenses HTTP endpoint.
func SubmitDriverExpensesDriverChargesPath(driverID string, organisationID int) string {
	return fmt.Sprintf("/drivers/%v/groups/%v/expenses", driverID, organisationID)
}
