// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Driver charges HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	drivercharges "experience/libs/data-platform/api/contract/gen/driver_charges"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildRetrieveManyRequest instantiates a HTTP request object with method and
// path set to call the "Driver charges" service "Retrieve many" endpoint
func (c *Client) BuildRetrieveManyRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		organisationID string
		userID         string
	)
	{
		p, ok := v.(*drivercharges.RetrieveManyPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Driver charges", "Retrieve many", "*drivercharges.RetrieveManyPayload", v)
		}
		organisationID = p.OrganisationID
		userID = p.UserID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveManyDriverChargesPath(organisationID, userID)}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Driver charges", "Retrieve many", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeRetrieveManyRequest returns an encoder for requests sent to the Driver
// charges Retrieve many server.
func EncodeRetrieveManyRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivercharges.RetrieveManyPayload)
		if !ok {
			return goahttp.ErrInvalidType("Driver charges", "Retrieve many", "*drivercharges.RetrieveManyPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		req.URL.RawQuery = values.Encode()
		return nil
	}
}

// DecodeRetrieveManyResponse returns a decoder for responses returned by the
// Driver charges Retrieve many endpoint. restoreBody controls whether the
// response body should be restored after having been read.
// DecodeRetrieveManyResponse may return the following errors:
//   - "driver_not_found" (type *drivercharges.DriverNotFound): http.StatusNotFound
//   - "organisation_not_found" (type *drivercharges.OrganisationNotFound): http.StatusNotFound
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeRetrieveManyResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveManyResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve many", err)
			}
			err = ValidateRetrieveManyResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Retrieve many", err)
			}
			res := NewRetrieveManyDriverChargesResponseOK(&body)
			return res, nil
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "driver_not_found":
				var (
					body RetrieveManyDriverNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve many", err)
				}
				err = ValidateRetrieveManyDriverNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Retrieve many", err)
				}
				return nil, NewRetrieveManyDriverNotFound(&body)
			case "organisation_not_found":
				return nil, NewRetrieveManyOrganisationNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Driver charges", "Retrieve many", resp.StatusCode, string(body))
			}
		case http.StatusBadRequest:
			var (
				body RetrieveManyBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve many", err)
			}
			err = ValidateRetrieveManyBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Retrieve many", err)
			}
			return nil, NewRetrieveManyBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Driver charges", "Retrieve many", resp.StatusCode, string(body))
		}
	}
}

// BuildRetrieveOrganisationDriversStatisticsRequest instantiates a HTTP
// request object with method and path set to call the "Driver charges" service
// "Retrieve organisation drivers statistics" endpoint
func (c *Client) BuildRetrieveOrganisationDriversStatisticsRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		organisationID string
	)
	{
		p, ok := v.(*drivercharges.RetrieveOrganisationDriversStatisticsPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Driver charges", "Retrieve organisation drivers statistics", "*drivercharges.RetrieveOrganisationDriversStatisticsPayload", v)
		}
		organisationID = p.OrganisationID
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: RetrieveOrganisationDriversStatisticsDriverChargesPath(organisationID)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Driver charges", "Retrieve organisation drivers statistics", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeRetrieveOrganisationDriversStatisticsRequest returns an encoder for
// requests sent to the Driver charges Retrieve organisation drivers statistics
// server.
func EncodeRetrieveOrganisationDriversStatisticsRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivercharges.RetrieveOrganisationDriversStatisticsPayload)
		if !ok {
			return goahttp.ErrInvalidType("Driver charges", "Retrieve organisation drivers statistics", "*drivercharges.RetrieveOrganisationDriversStatisticsPayload", v)
		}
		values := req.URL.Query()
		values.Add("from", p.From)
		values.Add("to", p.To)
		req.URL.RawQuery = values.Encode()
		body := NewRetrieveOrganisationDriversStatisticsRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Driver charges", "Retrieve organisation drivers statistics", err)
		}
		return nil
	}
}

// DecodeRetrieveOrganisationDriversStatisticsResponse returns a decoder for
// responses returned by the Driver charges Retrieve organisation drivers
// statistics endpoint. restoreBody controls whether the response body should
// be restored after having been read.
// DecodeRetrieveOrganisationDriversStatisticsResponse may return the following
// errors:
//   - "driver_not_found" (type *drivercharges.DriverNotFound): http.StatusNotFound
//   - "organisation_not_found" (type *drivercharges.OrganisationNotFound): http.StatusNotFound
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeRetrieveOrganisationDriversStatisticsResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body RetrieveOrganisationDriversStatisticsResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve organisation drivers statistics", err)
			}
			err = ValidateRetrieveOrganisationDriversStatisticsResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Retrieve organisation drivers statistics", err)
			}
			res := NewRetrieveOrganisationDriversStatisticsOrganisationDriversChargeStatisticsResponseOK(&body)
			return res, nil
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "driver_not_found":
				var (
					body RetrieveOrganisationDriversStatisticsDriverNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve organisation drivers statistics", err)
				}
				err = ValidateRetrieveOrganisationDriversStatisticsDriverNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Retrieve organisation drivers statistics", err)
				}
				return nil, NewRetrieveOrganisationDriversStatisticsDriverNotFound(&body)
			case "organisation_not_found":
				return nil, NewRetrieveOrganisationDriversStatisticsOrganisationNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Driver charges", "Retrieve organisation drivers statistics", resp.StatusCode, string(body))
			}
		case http.StatusBadRequest:
			var (
				body RetrieveOrganisationDriversStatisticsBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Retrieve organisation drivers statistics", err)
			}
			err = ValidateRetrieveOrganisationDriversStatisticsBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Retrieve organisation drivers statistics", err)
			}
			return nil, NewRetrieveOrganisationDriversStatisticsBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Driver charges", "Retrieve organisation drivers statistics", resp.StatusCode, string(body))
		}
	}
}

// BuildCreateDriverExpensesRequest instantiates a HTTP request object with
// method and path set to call the "Driver charges" service "Create driver
// expenses" endpoint
func (c *Client) BuildCreateDriverExpensesRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		driverID       int
		organisationID int
	)
	{
		p, ok := v.(*drivercharges.CreateDriverExpensesPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Driver charges", "Create driver expenses", "*drivercharges.CreateDriverExpensesPayload", v)
		}
		if p.DriverID != nil {
			driverID = *p.DriverID
		}
		if p.OrganisationID != nil {
			organisationID = *p.OrganisationID
		}
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: CreateDriverExpensesDriverChargesPath(driverID, organisationID)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Driver charges", "Create driver expenses", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeCreateDriverExpensesRequest returns an encoder for requests sent to
// the Driver charges Create driver expenses server.
func EncodeCreateDriverExpensesRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivercharges.CreateDriverExpensesPayload)
		if !ok {
			return goahttp.ErrInvalidType("Driver charges", "Create driver expenses", "*drivercharges.CreateDriverExpensesPayload", v)
		}
		body := NewCreateDriverExpensesRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Driver charges", "Create driver expenses", err)
		}
		return nil
	}
}

// DecodeCreateDriverExpensesResponse returns a decoder for responses returned
// by the Driver charges Create driver expenses endpoint. restoreBody controls
// whether the response body should be restored after having been read.
// DecodeCreateDriverExpensesResponse may return the following errors:
//   - "charge_not_found" (type *drivercharges.ChargeNotFound): http.StatusNotFound
//   - "driver_not_found" (type *drivercharges.DriverNotFound): http.StatusNotFound
//   - "organisation_not_found" (type *drivercharges.OrganisationNotFound): http.StatusNotFound
//   - "duplicate_charge_submission" (type *drivercharges.DuplicateChargeSubmission): http.StatusConflict
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeCreateDriverExpensesResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusCreated:
			var (
				body CreateDriverExpensesResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Create driver expenses", err)
			}
			err = ValidateCreateDriverExpensesResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Create driver expenses", err)
			}
			res := NewCreateDriverExpensesCreatedExpenseResponseCreated(&body)
			return res, nil
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "charge_not_found":
				var (
					body CreateDriverExpensesChargeNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Create driver expenses", err)
				}
				err = ValidateCreateDriverExpensesChargeNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Create driver expenses", err)
				}
				return nil, NewCreateDriverExpensesChargeNotFound(&body)
			case "driver_not_found":
				var (
					body CreateDriverExpensesDriverNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Create driver expenses", err)
				}
				err = ValidateCreateDriverExpensesDriverNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Create driver expenses", err)
				}
				return nil, NewCreateDriverExpensesDriverNotFound(&body)
			case "organisation_not_found":
				return nil, NewCreateDriverExpensesOrganisationNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Driver charges", "Create driver expenses", resp.StatusCode, string(body))
			}
		case http.StatusConflict:
			return nil, NewCreateDriverExpensesDuplicateChargeSubmission()
		case http.StatusBadRequest:
			var (
				body CreateDriverExpensesBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Create driver expenses", err)
			}
			err = ValidateCreateDriverExpensesBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Create driver expenses", err)
			}
			return nil, NewCreateDriverExpensesBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Driver charges", "Create driver expenses", resp.StatusCode, string(body))
		}
	}
}

// BuildSubmitDriverExpensesRequest instantiates a HTTP request object with
// method and path set to call the "Driver charges" service "Submit driver
// expenses" endpoint
func (c *Client) BuildSubmitDriverExpensesRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		driverID       string
		organisationID int
	)
	{
		p, ok := v.(*drivercharges.SubmitDriverExpensesPayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Driver charges", "Submit driver expenses", "*drivercharges.SubmitDriverExpensesPayload", v)
		}
		driverID = p.DriverID
		if p.OrganisationID != nil {
			organisationID = *p.OrganisationID
		}
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: SubmitDriverExpensesDriverChargesPath(driverID, organisationID)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Driver charges", "Submit driver expenses", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeSubmitDriverExpensesRequest returns an encoder for requests sent to
// the Driver charges Submit driver expenses server.
func EncodeSubmitDriverExpensesRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*drivercharges.SubmitDriverExpensesPayload)
		if !ok {
			return goahttp.ErrInvalidType("Driver charges", "Submit driver expenses", "*drivercharges.SubmitDriverExpensesPayload", v)
		}
		body := NewSubmitDriverExpensesRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Driver charges", "Submit driver expenses", err)
		}
		return nil
	}
}

// DecodeSubmitDriverExpensesResponse returns a decoder for responses returned
// by the Driver charges Submit driver expenses endpoint. restoreBody controls
// whether the response body should be restored after having been read.
// DecodeSubmitDriverExpensesResponse may return the following errors:
//   - "charge_not_found" (type *drivercharges.ChargeNotFound): http.StatusNotFound
//   - "driver_not_found" (type *drivercharges.DriverNotFound): http.StatusNotFound
//   - "organisation_not_found" (type *drivercharges.OrganisationNotFound): http.StatusNotFound
//   - "duplicate_charge_submission" (type *drivercharges.DuplicateChargeSubmission): http.StatusConflict
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeSubmitDriverExpensesResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusCreated:
			var (
				body SubmitDriverExpensesResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Submit driver expenses", err)
			}
			err = ValidateSubmitDriverExpensesResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Submit driver expenses", err)
			}
			res := NewSubmitDriverExpensesSubmittedExpenseResponseCreated(&body)
			return res, nil
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "charge_not_found":
				var (
					body SubmitDriverExpensesChargeNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Submit driver expenses", err)
				}
				err = ValidateSubmitDriverExpensesChargeNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Submit driver expenses", err)
				}
				return nil, NewSubmitDriverExpensesChargeNotFound(&body)
			case "driver_not_found":
				var (
					body SubmitDriverExpensesDriverNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Driver charges", "Submit driver expenses", err)
				}
				err = ValidateSubmitDriverExpensesDriverNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Driver charges", "Submit driver expenses", err)
				}
				return nil, NewSubmitDriverExpensesDriverNotFound(&body)
			case "organisation_not_found":
				return nil, NewSubmitDriverExpensesOrganisationNotFound()
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Driver charges", "Submit driver expenses", resp.StatusCode, string(body))
			}
		case http.StatusConflict:
			return nil, NewSubmitDriverExpensesDuplicateChargeSubmission()
		case http.StatusBadRequest:
			var (
				body SubmitDriverExpensesBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Driver charges", "Submit driver expenses", err)
			}
			err = ValidateSubmitDriverExpensesBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Driver charges", "Submit driver expenses", err)
			}
			return nil, NewSubmitDriverExpensesBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Driver charges", "Submit driver expenses", resp.StatusCode, string(body))
		}
	}
}

// unmarshalDriverChargeResponseBodyToDriverchargesDriverCharge builds a value
// of type *drivercharges.DriverCharge from a value of type
// *DriverChargeResponseBody.
func unmarshalDriverChargeResponseBodyToDriverchargesDriverCharge(v *DriverChargeResponseBody) *drivercharges.DriverCharge {
	if v == nil {
		return nil
	}
	res := &drivercharges.DriverCharge{
		ChargerName:       *v.ChargerName,
		BusinessName:      *v.BusinessName,
		StartTime:         *v.StartTime,
		EndTime:           *v.EndTime,
		ChargingDuration:  *v.ChargingDuration,
		PluggedInDuration: *v.PluggedInDuration,
		EnergyUsage:       *v.EnergyUsage,
		ChargeCost:        *v.ChargeCost,
		RevenueGenerated:  *v.RevenueGenerated,
		Co2Savings:        *v.Co2Savings,
	}

	return res
}

// unmarshalOrganisationDriversChargeStatisticsResponseBodyToDriverchargesOrganisationDriversChargeStatistics
// builds a value of type *drivercharges.OrganisationDriversChargeStatistics
// from a value of type *OrganisationDriversChargeStatisticsResponseBody.
func unmarshalOrganisationDriversChargeStatisticsResponseBodyToDriverchargesOrganisationDriversChargeStatistics(v *OrganisationDriversChargeStatisticsResponseBody) *drivercharges.OrganisationDriversChargeStatistics {
	if v == nil {
		return nil
	}
	res := &drivercharges.OrganisationDriversChargeStatistics{
		NumberOfCharges:   *v.NumberOfCharges,
		TotalUsage:        *v.TotalUsage,
		Co2Savings:        *v.Co2Savings,
		ChargingDuration:  *v.ChargingDuration,
		PluggedInDuration: *v.PluggedInDuration,
		TotalCost:         *v.TotalCost,
		RevenueGenerated:  *v.RevenueGenerated,
	}
	res.Driver = unmarshalDriverResponseBodyToDriverchargesDriver(v.Driver)

	return res
}

// unmarshalDriverResponseBodyToDriverchargesDriver builds a value of type
// *drivercharges.Driver from a value of type *DriverResponseBody.
func unmarshalDriverResponseBodyToDriverchargesDriver(v *DriverResponseBody) *drivercharges.Driver {
	res := &drivercharges.Driver{
		ID:        *v.ID,
		FirstName: *v.FirstName,
		LastName:  *v.LastName,
		FullName:  *v.FullName,
		Email:     *v.Email,
	}

	return res
}

// marshalDriverchargesCreateExpenseRequestToCreateExpenseRequestRequestBody
// builds a value of type *CreateExpenseRequestRequestBody from a value of type
// *drivercharges.CreateExpenseRequest.
func marshalDriverchargesCreateExpenseRequestToCreateExpenseRequestRequestBody(v *drivercharges.CreateExpenseRequest) *CreateExpenseRequestRequestBody {
	if v == nil {
		return nil
	}
	res := &CreateExpenseRequestRequestBody{
		ChargeID: v.ChargeID,
	}

	return res
}

// marshalCreateExpenseRequestRequestBodyToDriverchargesCreateExpenseRequest
// builds a value of type *drivercharges.CreateExpenseRequest from a value of
// type *CreateExpenseRequestRequestBody.
func marshalCreateExpenseRequestRequestBodyToDriverchargesCreateExpenseRequest(v *CreateExpenseRequestRequestBody) *drivercharges.CreateExpenseRequest {
	if v == nil {
		return nil
	}
	res := &drivercharges.CreateExpenseRequest{
		ChargeID: v.ChargeID,
	}

	return res
}

// unmarshalCreatedExpenseResponseBodyToDriverchargesCreatedExpense builds a
// value of type *drivercharges.CreatedExpense from a value of type
// *CreatedExpenseResponseBody.
func unmarshalCreatedExpenseResponseBodyToDriverchargesCreatedExpense(v *CreatedExpenseResponseBody) *drivercharges.CreatedExpense {
	if v == nil {
		return nil
	}
	res := &drivercharges.CreatedExpense{
		ID:       *v.ID,
		ChargeID: *v.ChargeID,
	}

	return res
}

// marshalDriverchargesSubmitExpenseRequestToSubmitExpenseRequestRequestBody
// builds a value of type *SubmitExpenseRequestRequestBody from a value of type
// *drivercharges.SubmitExpenseRequest.
func marshalDriverchargesSubmitExpenseRequestToSubmitExpenseRequestRequestBody(v *drivercharges.SubmitExpenseRequest) *SubmitExpenseRequestRequestBody {
	if v == nil {
		return nil
	}
	res := &SubmitExpenseRequestRequestBody{
		ChargeID: v.ChargeID,
	}

	return res
}

// marshalSubmitExpenseRequestRequestBodyToDriverchargesSubmitExpenseRequest
// builds a value of type *drivercharges.SubmitExpenseRequest from a value of
// type *SubmitExpenseRequestRequestBody.
func marshalSubmitExpenseRequestRequestBodyToDriverchargesSubmitExpenseRequest(v *SubmitExpenseRequestRequestBody) *drivercharges.SubmitExpenseRequest {
	if v == nil {
		return nil
	}
	res := &drivercharges.SubmitExpenseRequest{
		ChargeID: v.ChargeID,
	}

	return res
}

// unmarshalSubmittedExpenseResponseBodyToDriverchargesSubmittedExpense builds
// a value of type *drivercharges.SubmittedExpense from a value of type
// *SubmittedExpenseResponseBody.
func unmarshalSubmittedExpenseResponseBodyToDriverchargesSubmittedExpense(v *SubmittedExpenseResponseBody) *drivercharges.SubmittedExpense {
	if v == nil {
		return nil
	}
	res := &drivercharges.SubmittedExpense{
		ID:       *v.ID,
		ChargeID: *v.ChargeID,
	}

	return res
}
