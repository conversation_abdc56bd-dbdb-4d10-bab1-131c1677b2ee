// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Driver charges client HTTP transport
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"context"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Client lists the Driver charges service endpoint HTTP clients.
type Client struct {
	// RetrieveMany Doer is the HTTP client used to make requests to the Retrieve
	// many endpoint.
	RetrieveManyDoer goahttp.Doer

	// RetrieveOrganisationDriversStatistics Doer is the HTTP client used to make
	// requests to the Retrieve organisation drivers statistics endpoint.
	RetrieveOrganisationDriversStatisticsDoer goahttp.Doer

	// CreateDriverExpenses Doer is the HTTP client used to make requests to the
	// Create driver expenses endpoint.
	CreateDriverExpensesDoer goahttp.Doer

	// SubmitDriverExpenses Doer is the HTTP client used to make requests to the
	// Submit driver expenses endpoint.
	SubmitDriverExpensesDoer goahttp.Doer

	// RestoreResponseBody controls whether the response bodies are reset after
	// decoding so they can be read again.
	RestoreResponseBody bool

	scheme  string
	host    string
	encoder func(*http.Request) goahttp.Encoder
	decoder func(*http.Response) goahttp.Decoder
}

// NewClient instantiates HTTP clients for all the Driver charges service
// servers.
func NewClient(
	scheme string,
	host string,
	doer goahttp.Doer,
	enc func(*http.Request) goahttp.Encoder,
	dec func(*http.Response) goahttp.Decoder,
	restoreBody bool,
) *Client {
	return &Client{
		RetrieveManyDoer: doer,
		RetrieveOrganisationDriversStatisticsDoer: doer,
		CreateDriverExpensesDoer:                  doer,
		SubmitDriverExpensesDoer:                  doer,
		RestoreResponseBody:                       restoreBody,
		scheme:                                    scheme,
		host:                                      host,
		decoder:                                   dec,
		encoder:                                   enc,
	}
}

// RetrieveMany returns an endpoint that makes HTTP requests to the Driver
// charges service Retrieve many server.
func (c *Client) RetrieveMany() goa.Endpoint {
	var (
		encodeRequest  = EncodeRetrieveManyRequest(c.encoder)
		decodeResponse = DecodeRetrieveManyResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildRetrieveManyRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.RetrieveManyDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Driver charges", "Retrieve many", err)
		}
		return decodeResponse(resp)
	}
}

// RetrieveOrganisationDriversStatistics returns an endpoint that makes HTTP
// requests to the Driver charges service Retrieve organisation drivers
// statistics server.
func (c *Client) RetrieveOrganisationDriversStatistics() goa.Endpoint {
	var (
		encodeRequest  = EncodeRetrieveOrganisationDriversStatisticsRequest(c.encoder)
		decodeResponse = DecodeRetrieveOrganisationDriversStatisticsResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildRetrieveOrganisationDriversStatisticsRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.RetrieveOrganisationDriversStatisticsDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Driver charges", "Retrieve organisation drivers statistics", err)
		}
		return decodeResponse(resp)
	}
}

// CreateDriverExpenses returns an endpoint that makes HTTP requests to the
// Driver charges service Create driver expenses server.
func (c *Client) CreateDriverExpenses() goa.Endpoint {
	var (
		encodeRequest  = EncodeCreateDriverExpensesRequest(c.encoder)
		decodeResponse = DecodeCreateDriverExpensesResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildCreateDriverExpensesRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.CreateDriverExpensesDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Driver charges", "Create driver expenses", err)
		}
		return decodeResponse(resp)
	}
}

// SubmitDriverExpenses returns an endpoint that makes HTTP requests to the
// Driver charges service Submit driver expenses server.
func (c *Client) SubmitDriverExpenses() goa.Endpoint {
	var (
		encodeRequest  = EncodeSubmitDriverExpensesRequest(c.encoder)
		decodeResponse = DecodeSubmitDriverExpensesResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildSubmitDriverExpensesRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.SubmitDriverExpensesDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Driver charges", "Submit driver expenses", err)
		}
		return decodeResponse(resp)
	}
}
