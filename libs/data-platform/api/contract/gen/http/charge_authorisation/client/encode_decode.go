// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge Authorisation HTTP client encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"bytes"
	"context"
	chargeauthorisation "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	"io"
	"net/http"
	"net/url"

	goahttp "goa.design/goa/v3/http"
)

// BuildAuthoriseChargeRequest instantiates a HTTP request object with method
// and path set to call the "Charge Authorisation" service "authoriseCharge"
// endpoint
func (c *Client) BuildAuthoriseChargeRequest(ctx context.Context, v any) (*http.Request, error) {
	var (
		authorisationMethod string
	)
	{
		p, ok := v.(*chargeauthorisation.AuthoriseChargePayload)
		if !ok {
			return nil, goahttp.ErrInvalidType("Charge Authorisation", "authoriseCharge", "*chargeauthorisation.AuthoriseChargePayload", v)
		}
		authorisationMethod = string(p.AuthorisationMethod)
	}
	u := &url.URL{Scheme: c.scheme, Host: c.host, Path: AuthoriseChargeChargeAuthorisationPath(authorisationMethod)}
	req, err := http.NewRequest("POST", u.String(), nil)
	if err != nil {
		return nil, goahttp.ErrInvalidURL("Charge Authorisation", "authoriseCharge", u.String(), err)
	}
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return req, nil
}

// EncodeAuthoriseChargeRequest returns an encoder for requests sent to the
// Charge Authorisation authoriseCharge server.
func EncodeAuthoriseChargeRequest(encoder func(*http.Request) goahttp.Encoder) func(*http.Request, any) error {
	return func(req *http.Request, v any) error {
		p, ok := v.(*chargeauthorisation.AuthoriseChargePayload)
		if !ok {
			return goahttp.ErrInvalidType("Charge Authorisation", "authoriseCharge", "*chargeauthorisation.AuthoriseChargePayload", v)
		}
		body := NewAuthoriseChargeRequestBody(p)
		if err := encoder(req).Encode(&body); err != nil {
			return goahttp.ErrEncodingError("Charge Authorisation", "authoriseCharge", err)
		}
		return nil
	}
}

// DecodeAuthoriseChargeResponse returns a decoder for responses returned by
// the Charge Authorisation authoriseCharge endpoint. restoreBody controls
// whether the response body should be restored after having been read.
// DecodeAuthoriseChargeResponse may return the following errors:
//   - "authoriser_not_found" (type *chargeauthorisation.AuthoriserNotFound): http.StatusNotFound
//   - "charger_not_found" (type *chargeauthorisation.ChargerPpidNotFound): http.StatusNotFound
//   - "transaction_not_started" (type *chargeauthorisation.TransactionNotStarted): http.StatusInternalServerError
//   - "internal_server_error" (type *goa.ServiceError): http.StatusInternalServerError
//   - "bad_request" (type *goa.ServiceError): http.StatusBadRequest
//   - error: internal error
func DecodeAuthoriseChargeResponse(decoder func(*http.Response) goahttp.Decoder, restoreBody bool) func(*http.Response) (any, error) {
	return func(resp *http.Response) (any, error) {
		if restoreBody {
			b, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(b))
			defer func() {
				resp.Body = io.NopCloser(bytes.NewBuffer(b))
			}()
		} else {
			defer resp.Body.Close()
		}
		switch resp.StatusCode {
		case http.StatusOK:
			var (
				body AuthoriseChargeResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
			}
			err = ValidateAuthoriseChargeResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
			}
			res := NewAuthoriseChargeChargeAuthorisationResponseOK(&body)
			return res, nil
		case http.StatusNotFound:
			en := resp.Header.Get("goa-error")
			switch en {
			case "authoriser_not_found":
				var (
					body AuthoriseChargeAuthoriserNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
				}
				err = ValidateAuthoriseChargeAuthoriserNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
				}
				return nil, NewAuthoriseChargeAuthoriserNotFound(&body)
			case "charger_not_found":
				var (
					body AuthoriseChargeChargerNotFoundResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
				}
				err = ValidateAuthoriseChargeChargerNotFoundResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
				}
				return nil, NewAuthoriseChargeChargerNotFound(&body)
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Charge Authorisation", "authoriseCharge", resp.StatusCode, string(body))
			}
		case http.StatusInternalServerError:
			en := resp.Header.Get("goa-error")
			switch en {
			case "transaction_not_started":
				var (
					body AuthoriseChargeTransactionNotStartedResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
				}
				err = ValidateAuthoriseChargeTransactionNotStartedResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
				}
				return nil, NewAuthoriseChargeTransactionNotStarted(&body)
			case "internal_server_error":
				var (
					body AuthoriseChargeInternalServerErrorResponseBody
					err  error
				)
				err = decoder(resp).Decode(&body)
				if err != nil {
					return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
				}
				err = ValidateAuthoriseChargeInternalServerErrorResponseBody(&body)
				if err != nil {
					return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
				}
				return nil, NewAuthoriseChargeInternalServerError(&body)
			default:
				body, _ := io.ReadAll(resp.Body)
				return nil, goahttp.ErrInvalidResponse("Charge Authorisation", "authoriseCharge", resp.StatusCode, string(body))
			}
		case http.StatusBadRequest:
			var (
				body AuthoriseChargeBadRequestResponseBody
				err  error
			)
			err = decoder(resp).Decode(&body)
			if err != nil {
				return nil, goahttp.ErrDecodingError("Charge Authorisation", "authoriseCharge", err)
			}
			err = ValidateAuthoriseChargeBadRequestResponseBody(&body)
			if err != nil {
				return nil, goahttp.ErrValidationError("Charge Authorisation", "authoriseCharge", err)
			}
			return nil, NewAuthoriseChargeBadRequest(&body)
		default:
			body, _ := io.ReadAll(resp.Body)
			return nil, goahttp.ErrInvalidResponse("Charge Authorisation", "authoriseCharge", resp.StatusCode, string(body))
		}
	}
}

// unmarshalMetaResponseBodyToChargeauthorisationMeta builds a value of type
// *chargeauthorisation.Meta from a value of type *MetaResponseBody.
func unmarshalMetaResponseBodyToChargeauthorisationMeta(v *MetaResponseBody) *chargeauthorisation.Meta {
	res := &chargeauthorisation.Meta{}
	res.Params = make(map[string]any, len(v.Params))
	for key, val := range v.Params {
		tk := key
		tv := val
		res.Params[tk] = tv
	}

	return res
}
