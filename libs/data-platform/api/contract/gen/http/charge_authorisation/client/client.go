// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge Authorisation client HTTP transport
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package client

import (
	"context"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// Client lists the Charge Authorisation service endpoint HTTP clients.
type Client struct {
	// AuthoriseCharge Doer is the HTTP client used to make requests to the
	// authoriseCharge endpoint.
	AuthoriseChargeDoer goahttp.Doer

	// RestoreResponseBody controls whether the response bodies are reset after
	// decoding so they can be read again.
	RestoreResponseBody bool

	scheme  string
	host    string
	encoder func(*http.Request) goahttp.Encoder
	decoder func(*http.Response) goahttp.Decoder
}

// NewClient instantiates HTTP clients for all the Charge Authorisation service
// servers.
func NewClient(
	scheme string,
	host string,
	doer goahttp.Doer,
	enc func(*http.Request) goahttp.Encoder,
	dec func(*http.Response) goahttp.Decoder,
	restoreBody bool,
) *Client {
	return &Client{
		AuthoriseChargeDoer: doer,
		RestoreResponseBody: restoreBody,
		scheme:              scheme,
		host:                host,
		decoder:             dec,
		encoder:             enc,
	}
}

// AuthoriseCharge returns an endpoint that makes HTTP requests to the Charge
// Authorisation service authoriseCharge server.
func (c *Client) AuthoriseCharge() goa.Endpoint {
	var (
		encodeRequest  = EncodeAuthoriseChargeRequest(c.encoder)
		decodeResponse = DecodeAuthoriseChargeResponse(c.decoder, c.RestoreResponseBody)
	)
	return func(ctx context.Context, v any) (any, error) {
		req, err := c.BuildAuthoriseChargeRequest(ctx, v)
		if err != nil {
			return nil, err
		}
		err = encodeRequest(req, v)
		if err != nil {
			return nil, err
		}
		resp, err := c.AuthoriseChargeDoer.Do(req)
		if err != nil {
			return nil, goahttp.ErrRequestError("Charge Authorisation", "authoriseCharge", err)
		}
		return decodeResponse(resp)
	}
}
