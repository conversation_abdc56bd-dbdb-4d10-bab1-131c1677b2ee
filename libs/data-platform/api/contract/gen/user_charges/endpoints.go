// Code generated by goa v3.20.1, DO NOT EDIT.
//
// User Charges endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package usercharges

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "User Charges" service endpoints.
type Endpoints struct {
	GroupAndUserCharges goa.Endpoint
}

// NewEndpoints wraps the methods of the "User Charges" service with endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		GroupAndUserCharges: NewGroupAndUserChargesEndpoint(s),
	}
}

// Use applies the given middleware to all the "User Charges" service endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.GroupAndUserCharges = m(e.GroupAndUserCharges)
}

// NewGroupAndUserChargesEndpoint returns an endpoint function that calls the
// method "Group And User Charges" of service "User Charges".
func NewGroupAndUserChargesEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*GroupAndUserChargesPayload)
		return s.GroupAndUserCharges(ctx, p)
	}
}
