// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charging statistics endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package chargingstatistics

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "Charging statistics" service endpoints.
type Endpoints struct {
	SiteStatistics         goa.Endpoint
	OrganisationStatistics goa.Endpoint
	ChargerStatistics      goa.Endpoint
}

// NewEndpoints wraps the methods of the "Charging statistics" service with
// endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		SiteStatistics:         NewSiteStatisticsEndpoint(s),
		OrganisationStatistics: NewOrganisationStatisticsEndpoint(s),
		ChargerStatistics:      NewChargerStatisticsEndpoint(s),
	}
}

// Use applies the given middleware to all the "Charging statistics" service
// endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.SiteStatistics = m(e.SiteStatistics)
	e.OrganisationStatistics = m(e.OrganisationStatistics)
	e.ChargerStatistics = m(e.ChargerStatistics)
}

// NewSiteStatisticsEndpoint returns an endpoint function that calls the method
// "Site statistics" of service "Charging statistics".
func NewSiteStatisticsEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*SiteStatisticsPayload)
		res, err := s.SiteStatistics(ctx, p)
		if err != nil {
			return nil, err
		}
		vres := NewViewedSitechargessummary(res, "default")
		return vres, nil
	}
}

// NewOrganisationStatisticsEndpoint returns an endpoint function that calls
// the method "Organisation statistics" of service "Charging statistics".
func NewOrganisationStatisticsEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*OrganisationStatisticsPayload)
		res, err := s.OrganisationStatistics(ctx, p)
		if err != nil {
			return nil, err
		}
		vres := NewViewedOrganisationchargessummary(res, "default")
		return vres, nil
	}
}

// NewChargerStatisticsEndpoint returns an endpoint function that calls the
// method "Charger statistics" of service "Charging statistics".
func NewChargerStatisticsEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*ChargerStatisticsPayload)
		res, err := s.ChargerStatistics(ctx, p)
		if err != nil {
			return nil, err
		}
		vres := NewViewedChargerchargessummary(res, "default")
		return vres, nil
	}
}
