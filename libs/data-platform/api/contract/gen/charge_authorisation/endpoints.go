// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge Authorisation endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package chargeauthorisation

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "Charge Authorisation" service endpoints.
type Endpoints struct {
	AuthoriseCharge goa.Endpoint
}

// NewEndpoints wraps the methods of the "Charge Authorisation" service with
// endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		AuthoriseCharge: NewAuthoriseChargeEndpoint(s),
	}
}

// Use applies the given middleware to all the "Charge Authorisation" service
// endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.AuthoriseCharge = m(e.AuthoriseCharge)
}

// NewAuthoriseChargeEndpoint returns an endpoint function that calls the
// method "authoriseCharge" of service "Charge Authorisation".
func NewAuthoriseChargeEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*AuthoriseChargePayload)
		return s.AuthoriseCharge(ctx, p)
	}
}
