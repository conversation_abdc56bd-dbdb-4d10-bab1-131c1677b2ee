// Code generated by goa v3.20.1, DO NOT EDIT.
//
// drivers endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package drivers

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "drivers" service endpoints.
type Endpoints struct {
	RetrieveCharge  goa.Endpoint
	RetrieveCharges goa.Endpoint
	RetrieveStats   goa.Endpoint
}

// NewEndpoints wraps the methods of the "drivers" service with endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		RetrieveCharge:  NewRetrieveChargeEndpoint(s),
		RetrieveCharges: NewRetrieveChargesEndpoint(s),
		RetrieveStats:   NewRetrieveStatsEndpoint(s),
	}
}

// Use applies the given middleware to all the "drivers" service endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.RetrieveCharge = m(e.RetrieveCharge)
	e.RetrieveCharges = m(e.RetrieveCharges)
	e.RetrieveStats = m(e.RetrieveStats)
}

// NewRetrieveChargeEndpoint returns an endpoint function that calls the method
// "retrieveCharge" of service "drivers".
func NewRetrieveChargeEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*RetrieveChargePayload)
		return s.RetrieveCharge(ctx, p)
	}
}

// NewRetrieveChargesEndpoint returns an endpoint function that calls the
// method "retrieveCharges" of service "drivers".
func NewRetrieveChargesEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*RetrieveChargesPayload)
		return s.RetrieveCharges(ctx, p)
	}
}

// NewRetrieveStatsEndpoint returns an endpoint function that calls the method
// "retrieveStats" of service "drivers".
func NewRetrieveStatsEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*RetrieveStatsPayload)
		return s.RetrieveStats(ctx, p)
	}
}
