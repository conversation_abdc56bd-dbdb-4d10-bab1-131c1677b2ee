// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge commands service
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package chargecommands

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Commands API for interfacing with event store.
type Service interface {
	// Update the energy cost of a charge.
	CorrectEnergyCost(context.Context, *CorrectEnergyCostPayload) (res *AggregateCostCorrectedResponse, err error)
	// Update the settlement amount of a charge.
	CorrectSettlementAmount(context.Context, *CorrectSettlementAmountPayload) (res *AggregateSettlementAmountCorrectedResponse, err error)
}

// APIName is the name of the API as defined in the design.
const APIName = "Data Platform"

// APIVersion is the version of the API as defined in the design.
const APIVersion = "0.0.1"

// ServiceName is the name of the service as defined in the design. This is the
// same value that is set in the endpoint request contexts under the ServiceKey
// key.
const ServiceName = "Charge commands"

// MethodNames lists the service method names as defined in the design. These
// are the same values that are set in the endpoint request contexts under the
// MethodKey key.
var MethodNames = [2]string{"Correct energy cost", "Correct settlement amount"}

// AggregateCostCorrectedResponse is the result type of the Charge commands
// service Correct energy cost method.
type AggregateCostCorrectedResponse struct {
	// UUID of the charge.
	ID string
	// New cost of the charge in lowest denomination of its currency (pence, euro
	// cents).
	Cost int
	// Who has submitted the correction request.
	SubmittedBy *string
}

// AggregateSettlementAmountCorrectedResponse is the result type of the Charge
// commands service Correct settlement amount method.
type AggregateSettlementAmountCorrectedResponse struct {
	// UUID of the charge.
	ID string
	// New settlement amount of the charge in lowest denomination of its currency
	// (pence, euro cents).
	SettlementAmount int
	// Who has submitted the correction request.
	SubmittedBy *string
}

// The cost of an already expensed charge cannot be updated.
type ChargeExpensed struct {
	Reason string
	Status int
}

// ChargeNotFound is the error returned when there is no charge found for the
// given charge ID.
type ChargeNotFound struct {
	Reason string
	Status int
}

// CorrectEnergyCostPayload is the payload type of the Charge commands service
// Correct energy cost method.
type CorrectEnergyCostPayload struct {
	// UUID of the charge to correct.
	ChargeID string
	// New cost of the charge in lowest denomination of its currency (pence, euro
	// cents).
	Cost int
	// Who has submitted the correction request.
	SubmittedBy *string
}

// CorrectSettlementAmountPayload is the payload type of the Charge commands
// service Correct settlement amount method.
type CorrectSettlementAmountPayload struct {
	// UUID of the charge to correct.
	ChargeID string
	// New settlement amount of the charge in lowest denomination of its currency
	// (pence, euro cents).
	SettlementAmount int
	// Who has submitted the correction request.
	SubmittedBy *string
}

// EventOutOfDate is the error returned when there is another update on the
// same charge, resulting in a unique constraint on the database.
type EventOutOfDate struct {
	Reason string
	Status int
}

// Error returns an error description.
func (e *ChargeExpensed) Error() string {
	return "The cost of an already expensed charge cannot be updated."
}

// ErrorName returns "ChargeExpensed".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *ChargeExpensed) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "ChargeExpensed".
func (e *ChargeExpensed) GoaErrorName() string {
	return "charge_expensed"
}

// Error returns an error description.
func (e *ChargeNotFound) Error() string {
	return "ChargeNotFound is the error returned when there is no charge found for the given charge ID."
}

// ErrorName returns "ChargeNotFound".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *ChargeNotFound) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "ChargeNotFound".
func (e *ChargeNotFound) GoaErrorName() string {
	return "charge_not_found"
}

// Error returns an error description.
func (e *EventOutOfDate) Error() string {
	return "EventOutOfDate is the error returned when there is another update on the same charge, resulting in a unique constraint on the database."
}

// ErrorName returns "EventOutOfDate".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *EventOutOfDate) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "EventOutOfDate".
func (e *EventOutOfDate) GoaErrorName() string {
	return "duplicate_requests"
}

// MakeBadRequest builds a goa.ServiceError from an error.
func MakeBadRequest(err error) *goa.ServiceError {
	return goa.NewServiceError(err, "bad_request", false, false, false)
}
