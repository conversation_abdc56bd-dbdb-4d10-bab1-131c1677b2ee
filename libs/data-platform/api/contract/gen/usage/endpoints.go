// Code generated by goa v3.20.1, DO NOT EDIT.
//
// usage endpoints
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package usage

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Endpoints wraps the "usage" service endpoints.
type Endpoints struct {
	UsageByOrganisation           goa.Endpoint
	UsageByOrganisationAndSite    goa.Endpoint
	UsageByOrganisationAndCharger goa.Endpoint
}

// NewEndpoints wraps the methods of the "usage" service with endpoints.
func NewEndpoints(s Service) *Endpoints {
	return &Endpoints{
		UsageByOrganisation:           NewUsageByOrganisationEndpoint(s),
		UsageByOrganisationAndSite:    NewUsageByOrganisationAndSiteEndpoint(s),
		UsageByOrganisationAndCharger: NewUsageByOrganisationAndChargerEndpoint(s),
	}
}

// Use applies the given middleware to all the "usage" service endpoints.
func (e *Endpoints) Use(m func(goa.Endpoint) goa.Endpoint) {
	e.UsageByOrganisation = m(e.UsageByOrganisation)
	e.UsageByOrganisationAndSite = m(e.UsageByOrganisationAndSite)
	e.UsageByOrganisationAndCharger = m(e.UsageByOrganisationAndCharger)
}

// NewUsageByOrganisationEndpoint returns an endpoint function that calls the
// method "Usage by organisation" of service "usage".
func NewUsageByOrganisationEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*UsageByOrganisationPayload)
		return s.UsageByOrganisation(ctx, p)
	}
}

// NewUsageByOrganisationAndSiteEndpoint returns an endpoint function that
// calls the method "Usage by organisation and site" of service "usage".
func NewUsageByOrganisationAndSiteEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*UsageByOrganisationAndSitePayload)
		return s.UsageByOrganisationAndSite(ctx, p)
	}
}

// NewUsageByOrganisationAndChargerEndpoint returns an endpoint function that
// calls the method "Usage by organisation and charger" of service "usage".
func NewUsageByOrganisationAndChargerEndpoint(s Service) goa.Endpoint {
	return func(ctx context.Context, req any) (any, error) {
		p := req.(*UsageByOrganisationAndChargerPayload)
		return s.UsageByOrganisationAndCharger(ctx, p)
	}
}
