// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Chargers client
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package chargers

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Client is the "Chargers" service client.
type Client struct {
	RetrieveChargerRegionEndpoint goa.Endpoint
	RetrieveDNOregionsEndpoint    goa.Endpoint
	RetrieveChargingLimitEndpoint goa.Endpoint
}

// NewClient initializes a "Chargers" service client given the endpoints.
func NewClient(retrieveChargerRegion, retrieveDNOregions, retrieveChargingLimit goa.Endpoint) *Client {
	return &Client{
		RetrieveChargerRegionEndpoint: retrieveChargerRegion,
		RetrieveDNOregionsEndpoint:    retrieveDNOregions,
		RetrieveChargingLimitEndpoint: retrieveChargingLimit,
	}
}

// RetrieveChargerRegion calls the "Retrieve charger region" endpoint of the
// "Chargers" service.
// RetrieveChargerRegion may return the following errors:
//   - "internal_server_error" (type *goa.ServiceError)
//   - "bad_request" (type *ChargerLocationNotFound): Charger location not found
//   - "not_found" (type *ChargerRegionNotFound): Charger region not found
//   - error: internal error
func (c *Client) RetrieveChargerRegion(ctx context.Context, p *RetrieveChargerRegionPayload) (res *Region, err error) {
	var ires any
	ires, err = c.RetrieveChargerRegionEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*Region), nil
}

// RetrieveDNOregions calls the "Retrieve DNO regions" endpoint of the
// "Chargers" service.
// RetrieveDNOregions may return the following errors:
//   - "internal_server_error" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) RetrieveDNOregions(ctx context.Context) (res *Regions, err error) {
	var ires any
	ires, err = c.RetrieveDNOregionsEndpoint(ctx, nil)
	if err != nil {
		return
	}
	return ires.(*Regions), nil
}

// RetrieveChargingLimit calls the "Retrieve charging limit" endpoint of the
// "Chargers" service.
// RetrieveChargingLimit may return the following errors:
//   - "not_found" (type *ChargerLimitNotFound): Charger location/authoriser not found
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) RetrieveChargingLimit(ctx context.Context, p *RetrieveChargingLimitPayload) (res *ChargesLimitResponse, err error) {
	var ires any
	ires, err = c.RetrieveChargingLimitEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*ChargesLimitResponse), nil
}
