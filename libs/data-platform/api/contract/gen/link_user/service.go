// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Link user service
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package linkuser

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Service is the Link user service interface.
type Service interface {
	// LinkUserToHomeCharger implements Link user to home charger.
	LinkUserToHomeCharger(context.Context, *LinkUserToHomeChargerPayload) (err error)
}

// APIName is the name of the API as defined in the design.
const APIName = "Data Platform"

// APIVersion is the version of the API as defined in the design.
const APIVersion = "0.0.1"

// ServiceName is the name of the service as defined in the design. This is the
// same value that is set in the endpoint request contexts under the ServiceKey
// key.
const ServiceName = "Link user"

// MethodNames lists the service method names as defined in the design. These
// are the same values that are set in the endpoint request contexts under the
// MethodKey key.
var MethodNames = [1]string{"Link user to home charger"}

// ChargerNotFound is the error returned when there is no charger found for a
// given {locationId}.
type ChargerNotFound struct {
}

// LinkUserToHomeChargerPayload is the payload type of the Link user service
// Link user to home charger method.
type LinkUserToHomeChargerPayload struct {
	// PPID (PSL number) of a charger
	PpID string
	// The UUID of the user
	UserID string
}

// UserNotFound is the error returned when there is no user found for the given
// user ID.
type UserNotFound struct {
}

// Error returns an error description.
func (e *ChargerNotFound) Error() string {
	return "ChargerNotFound is the error returned when there is no charger found for a given {locationId}."
}

// ErrorName returns "ChargerNotFound".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *ChargerNotFound) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "ChargerNotFound".
func (e *ChargerNotFound) GoaErrorName() string {
	return "charger_not_found"
}

// Error returns an error description.
func (e *UserNotFound) Error() string {
	return "UserNotFound is the error returned when there is no user found for the given user ID."
}

// ErrorName returns "UserNotFound".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *UserNotFound) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "UserNotFound".
func (e *UserNotFound) GoaErrorName() string {
	return "user_not_found"
}

// MakeInternalServerError builds a goa.ServiceError from an error.
func MakeInternalServerError(err error) *goa.ServiceError {
	return goa.NewServiceError(err, "internal_server_error", false, false, false)
}

// MakeBadRequest builds a goa.ServiceError from an error.
func MakeBadRequest(err error) *goa.ServiceError {
	return goa.NewServiceError(err, "bad_request", false, false, false)
}
