// Code generated by goa v3.20.1, DO NOT EDIT.
//
// sites service
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package sites

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Service for retrieving charge stats grouped by site between two dates.
type Service interface {
	// Retrieve charge information by site.
	RetrieveChargeStatsGroupedBySite(context.Context, *RetrieveChargeStatsGroupedBySitePayload) (res *SiteStatsResponse, err error)
}

// APIName is the name of the API as defined in the design.
const APIName = "Data Platform"

// APIVersion is the version of the API as defined in the design.
const APIVersion = "0.0.1"

// ServiceName is the name of the service as defined in the design. This is the
// same value that is set in the endpoint request contexts under the ServiceKey
// key.
const ServiceName = "sites"

// MethodNames lists the service method names as defined in the design. These
// are the same values that are set in the endpoint request contexts under the
// MethodKey key.
var MethodNames = [1]string{"retrieveChargeStatsGroupedBySite"}

// RetrieveChargeStatsGroupedBySitePayload is the payload type of the sites
// service retrieveChargeStatsGroupedBySite method.
type RetrieveChargeStatsGroupedBySitePayload struct {
	// Statistics report inclusive start date eg: 2022-01-01
	From string
	// Statistics report inclusive end date eg: 2022-01-31
	To string
}

type SiteStats struct {
	// Site unique identifier.
	ID string
	// Site name.
	Name *string
	// Group unique identifier.
	GroupID *string
	// Group Name.
	GroupName *string
	// Energy used in kWh.
	TotalEnergy float64
	// Revenue generated in pence
	RevenueGenerated int
}

// SiteStatsResponse is the result type of the sites service
// retrieveChargeStatsGroupedBySite method.
type SiteStatsResponse struct {
	// Count of sites returned.
	Count int
	// Statistics report inclusive start date eg: 2022-01-01
	From string
	// Statistics report inclusive end date eg: 2022-01-31
	To string
	// Charge data for site.
	Data []*SiteStats
}

// MakeBadRequest builds a goa.ServiceError from an error.
func MakeBadRequest(err error) *goa.ServiceError {
	return goa.NewServiceError(err, "bad_request", false, false, false)
}
