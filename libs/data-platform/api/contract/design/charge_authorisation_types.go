package design

import (
	. "goa.design/goa/v3/dsl"
)

var authorisationMethod = Type("authorisationMethod", String)

var ChargeAuthorisationResponse = Type("chargeAuthorisationResponse", func() {
	Attribute("id", String, func() {
		Description("UUID representing the charge authorisation, present if authorisation was successful.")
		Format(FormatUUID)
	})
	Attribute("authorised", Boolean, func() {
		Description("Whether the charge is authorised.")
	})
	Attribute("meta", ResponseMeta)
	Required("authorised", "meta")
})
