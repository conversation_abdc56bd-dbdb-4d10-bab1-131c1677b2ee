package design

import (
	. "goa.design/goa/v3/dsl"
)

var _ = Service("Charging statistics", func() {
	Description("Aggregate charging statistics for sites, organisations and chargers")

	Method("Site statistics", func() {
		Description("Charge statistics for a site between two inclusive dates.")
		Payload(func() {
			Attribute("siteId", Int, func() {
				Minimum(1)
			})
			DateRangeAttributes()
			Required("siteId", "from", "to")
		})

		Result(SiteChargesSummaryResponse)

		Error("site_not_found", SiteNotFound, "site not found")
		Error("bad_request")

		HTTP(func() {
			GET("/charges/site/{siteId}")
			Param("siteId", String)
			Param("from", String)
			Param("to", String)
			Response("site_not_found", StatusNotFound)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when site ID is malformed")
			})
		})
	})

	Method("Organisation statistics", func() {
		Description("Charge statistics for all sites with charges within an organisation (host group) between two inclusive dates. We do not check that the 'from' and 'to' date parameters constitute a valid date range. Where the 'to' date is  prior to  the 'from' date no results will be returned.")
		Payload(func() {
			AttributeOrganisationID()
			DateRangeAttributes()
			Required("organisationId", "from", "to")
		})

		Result(OrganisationChargesSummaryResponse)

		Error("organisation_not_found", OrganisationNotFound, "Organisation not found")
		Error("bad_request")

		HTTP(func() {
			GET("/charges/group/{organisationId}")
			Param("organisationId", String)
			Param("from", String)
			Param("to", String)
			Response("organisation_not_found", StatusNotFound)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when organisation ID is malformed or date parameters are invalid")
			})
		})
	})

	Method("Charger statistics", func() {
		Description("Charge statistics for a single charger between two inclusive dates.")
		Payload(func() {
			Attribute("locationId", Int, func() {
				Description("Primary key of the charger location.")
				Example(123)
			})
			DateRangeAttributes()
			Required("locationId", "from", "to")
		})

		Result(ChargerChargesSummaryResponse)

		Error("charger_not_found", ChargerNotFound, "Charger location not found")
		Error("bad_request")

		HTTP(func() {
			GET("/charges/charger/{locationId}")
			Param("locationId", String)
			Param("from", String)
			Param("to", String)
			Response("charger_not_found", StatusNotFound)
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when date parameters are invalid")
			})
		})
	})
})
