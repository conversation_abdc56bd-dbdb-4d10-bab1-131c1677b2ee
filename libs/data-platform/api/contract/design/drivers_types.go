package design

import (
	. "goa.design/goa/v3/dsl"
)

var DriversChargesResponse = Type("driversChargesResponse", func() {
	Attribute("data", Charges)
	Attribute("meta", ResponseMeta)
	Required("data", "meta")
})

var Charges = Type("charges", func() {
	Attribute("count", Int, func() {
		Description("Count of charges returned.")
		Example(22245)
	})
	Attribute("charges", ArrayOf(Charge), func() {
		Description("Charge data for driver.")
	})
	Required("count", "charges")
})

var DriversChargeResponse = Type("driversChargeResponse", func() {
	Attribute("data", Charge)
	Attribute("meta", ResponseMeta)
	Required("data", "meta")
})

var Charge = Type("charge", func() {
	Attribute("id", String, func() {
		Description("Charges unique identifier.")
		Format(FormatUUID)
	})
	Attribute("startedAt", String, func() {
		Description("Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.")
		Example("2018-05-15T12:00:00+12:00")
	})
	Attribute("endedAt", String, func() {
		Description("Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.")
		Example("2018-05-15T12:00:00+12:00")
	})
	Attribute("duration", Int, func() {
		Description("Duration of charging in seconds.")
		Example(22245)
	})
	Attribute("energyTotal", Float64, func() {
		Description("Energy used in kWh.")
		Example(567.89)
	})
	Attribute("generationEnergyTotal", Float64, func() {
		Description("Energy used in kWh that was generated e.g. Solar.")
		Example(67.89)
	})
	Attribute("gridEnergyTotal", Float64, func() {
		Description("Energy used in kWh that is imported from the grid.")
		Example(500.00)
	})
	Attribute("cost", Money, func() {
		Description(
			"Cost of the charge to the driver. " +
				"This is based on the driver's home tariff or the amount paid by the driver for a charge using a public charger.")
	})
	Attribute("expensedTo", ExpensedTo)
	Attribute("charger", Charger)
	Required("id", "charger")
})

var Charger = Type("charger", func() {
	Attribute("type", String, func() {
		Description("Type of charger.")
		Enum("public", "private", "home")
		Example("home")
	})
	Attribute("id", String, func() {
		Description("Chargers unique identifier.")
		Example("PG-245400", "veefil-602300633")
	})
	Attribute("name", String, func() {
		Description("Chargers user-friendly name.")
		Example("Amoy-Reef")
	})
	Attribute("door", String, func() {
		Description("Charger door used.")
		Example("A")
	})
	Attribute("siteName", String, func() {
		Description("Name of the site where the charger is located.")
		Example("Ecclestone Court Car Park")
	})
	Attribute("pluggedInAt", String, func() {
		Description("Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.")
		Example("2018-05-15T12:00:00+12:00")
	})
	Attribute("unpluggedAt", String, func() {
		Description("Datetime is in ISO8601 and RFC3339 compliant format, localised with timezone offset.")
		Example("2018-05-15T12:00:00+12:00")
	})
	Attribute("pluggedInDuration", Int, func() {
		Description("Duration a charger has been in use in seconds.")
		Example(3600)
	})
	Required("id", "door", "type")
})

var ExpensedTo = Type("expensedTo", func() {
	Description("Group the charge is to be expensed to.")
	Attribute("id", String, func() {
		Description("Groups unique identifier.")
		Format(FormatUUID)
	})
	Attribute("name", String, func() {
		Description("Groups name.")
		Example("Adept Power Solutions Ltd")
	})
	Required("id", "name")
})

var DriverStatsResponse = Type("driverStatsResponse", func() {
	Attribute("data", DriverStatsData)
	Attribute("meta", ResponseMeta)
	Required("data", "meta")
})

var DriverStatsData = Type("driverStatsData", func() {
	Attribute("summary", DriverStatsSummary)
	Attribute("intervals", ArrayOf(Interval), func() {
		Description("Driver statistics broken down by the requested interval type.")
	})
	Required("summary")
})

var DriverStatsSummary = Type("statsSummary", func() {
	Attribute("energy", EnergySummary)
	Attribute("cost", CostSummary)
	Attribute("duration", DurationSummary)
	Required("energy", "cost", "duration")
})

var EnergySummary = Type("energy", func() {
	Attribute("home", EnergyBreakdown, func() {
		Description("Energy used for all charges at home chargers in kWh")
	})
	Attribute("private", EnergyBreakdown, func() {
		Description("Energy used for all charges at private chargers in kWh")
	})
	Attribute("public", EnergyBreakdown, func() {
		Description("Energy used for all charges at public chargers in kWh")
	})
	Attribute("total", EnergyBreakdown, func() {
		Description("Energy used for all charges in kWh")
	})
	Required("home", "private", "public", "total")
})

var EnergyBreakdown = Type("breakdown", func() {
	Attribute("grid", Float64, func() {
		Description("Grid energy in kWh")
		Example(123.4)
	})
	Attribute("generation", Float64, func() {
		Description("Generation energy in kWh")
		Example(12.5)
	})
	Attribute("total", Float64, func() {
		Description("Total energy in kWh")
		Example(135.9)
	})
	Required("total")
})

var CostSummary = Type("cost", func() {
	Attribute("home", ArrayOf(Money64), func() {
		Description("Total cost of all charges at home chargers in pence/cent/ore")
	})
	Attribute("private", ArrayOf(Money64), func() {
		Description("Total cost of all chargers at private chargers in pence/cent/ore")
	})
	Attribute("public", ArrayOf(Money64), func() {
		Description("Total cost of all chargers at public chargers in pence/cent/ore")
	})
	Attribute("total", ArrayOf(Money64), func() {
		Description("Total cost of all charges in pence/cent/ore")
	})
	Required("home", "private", "public", "total")
})

var DurationSummary = Type("duration", func() {
	Attribute("home", Int, func() {
		Description("Total duration of all charges at home chargers in secs")
		Example(680)
	})
	Attribute("private", Int, func() {
		Description("Total duration of all charges at private chargers in secs")
		Example(170)
	})
	Attribute("public", Int, func() {
		Description("Total duration of all charges at public chargers in secs")
		Example(170)
	})
	Attribute("total", Int, func() {
		Description("Total duration of all charges in secs")
		Example(850)
	})
	Required("home", "private", "public", "total")
})

var Interval = Type("interval", func() {
	DateTimeRangeAttributes()
	Attribute("stats", DriverStatsSummary, func() {
		Description("Statistics for the time period relating to the from and to values.")
	})
	Required("from", "to", "stats")
})
