package design

import (
	. "goa.design/goa/v3/dsl"
)

var UsageResponse = Type("usageResponse", func() {
	Description("Basic usage by time periods")
	Attribute("usage", ArrayOf(Usage))
	Attribute("meta", ResponseMeta)
	Required("meta")
})

var Usage = Type("usage", func() {
	Attribute("intervalStartDate", String, func() {
		Description("The start date of this usage interval")
		Format(FormatDate)
		Example("2023-06-07")
	})
	Attribute("totalUsage", Float64, func() {
		Description("Energy used this period in kWh")
		Example(669.20)
	})
	Attribute("co2Savings", Float64, func() {
		Description("CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)")
		Example(2469.32)
	})
	Attribute("revenueGenerated", Int, func() {
		Description("Revenue generated in pence")
		Example(16)
	})
	Attribute("cost", Int, func() {
		Description("Energy cost in pence")
		Example(135668)
	})
	Required("intervalStartDate", "totalUsage", "co2Savings", "revenueGenerated", "cost")
})
