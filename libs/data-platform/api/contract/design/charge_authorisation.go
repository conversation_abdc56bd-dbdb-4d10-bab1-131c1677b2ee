package design

import (
	. "goa.design/goa/v3/dsl"
)

var _ = Service("Charge Authorisation", func() {
	Description("API endpoints related to the authorisation steps of beginning/claiming a charge.")

	Method("authoriseCharge", func() {
		Description("Authorises a charge claimed via RFID or OCPI. When RFID it checks that the rfid token is registered and that the group it is registered to matches the charger's group.")
		Payload(func() {
			Attribute("authorisationMethod", authorisationMethod, func() {
				Enum("rfid", "ocpi", "guest")
				Description("One of rfid or ocpi.")
				Example("rfid")
			})
			Attribute("token", String, func() {
				Description("RFID card token, OCPI token, or Billing event ID for guest authorisation.")
				Example("0DA46GKEFP3")
			})
			Attribute("chargerId", String, func() {
				Description("PPID (PSL number) of a charger")
				Example("PP-12345")
			})
			Attribute("door", String, func() {
				Enum("A", "B", "C")
				Description("Charger door. A, B or C")
				Example("A")
			})
			Required("authorisationMethod", "token", "chargerId", "door")
		})

		Result(ChargeAuthorisationResponse)
		Error("internal_server_error")
		Error("transaction_not_started", TransactionNotStarted)
		Error("bad_request")
		Error("charger_not_found", ChargerPpidNotFound)
		Error("authoriser_not_found", AuthoriserNotFound)

		HTTP(func() {
			POST("/charge-authorisations/{authorisationMethod}")
			Response(StatusOK)
			Response("internal_server_error", StatusInternalServerError)
			Response("transaction_not_started", StatusInternalServerError)
			Response("bad_request", StatusBadRequest)
			Response("charger_not_found", StatusNotFound)
			Response("authoriser_not_found", StatusNotFound)
		})
	})
})
