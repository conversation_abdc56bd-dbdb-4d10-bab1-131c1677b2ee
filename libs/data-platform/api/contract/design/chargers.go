package design

import (
	. "goa.design/goa/v3/dsl"
)

var _ = Service("Chargers", func() {
	Description("Data and statistics related to chargers")

	Method("Retrieve charger region", func() {
		Description("For a given PSL / PPID, return the associated DNO region id.")

		Payload(func() {
			Attribute("ppId", String, func() {
				Description("PPID of a given charger")
				Example("PP-12003")
			})
			Required("ppId")
		})

		Result(DNORegionResponse)

		Error("internal_server_error")
		Error("bad_request", ChargerLocationNotFound, "Charger location not found")
		Error("not_found", ChargerRegionNotFound, "Charger region not found")

		HTTP(func() {
			GET("/chargers/{ppId}/dnoregion")
			Response("bad_request", StatusBadRequest, func() {
				Description("Returns bad request when a location does not exist for the provided charger psl/ppid.")
			})
			Response("not_found", StatusNotFound, func() {
				Description("Returns not found when a DNO region cannot be determined for the provided charger psl/ppid.")
			})
			Response("internal_server_error", StatusInternalServerError)
		})
	})

	Method("Retrieve DNO regions", func() {
		Description("Retrieve the complete collection of DNO region objects.")

		Result(DnoRegionsResponse)

		Error("internal_server_error")

		HTTP(func() {
			GET("/dno-regions")
			Response("internal_server_error", StatusInternalServerError)
		})
	})

	Method("Retrieve charging limit", func() {
		Description("Retrieve limit for charging")

		Payload(func() {
			Attribute("ppID", String, func() {
				Description("PPID (PSL number) of a charger")
				MinLength(1)
				Example("PP-12003")
			})
			Attribute("authoriserUUID", String, func() {
				Description("UUID of charge authoriser - can be user's UUID or app name, like `dcs` or RFID key")
				MinLength(1)
				Example("a82cc207-ee2a-3f38-a517-88aa6784f322")
			})
			Required("ppID", "authoriserUUID")
		})

		Result(ChargersLimitResponse)

		Error("not_found", ChargerLimitNotFound, "Charger location/authoriser not found")
		Error("bad_request")

		HTTP(func() {
			GET("/chargers/{ppID}/limit")

			Param("ppID", String)
			Param("authoriserUUID", String)

			Response("bad_request", StatusBadRequest, func() {
				Description("Returns 400 Bad Request when PPID or authoriser UUID is missing")
			})
			Response("not_found", StatusNotFound, func() {
				Description("Returns 404 Not Found, when PPID or authoriser UUID has not been found")
			})
		})
	})
})
