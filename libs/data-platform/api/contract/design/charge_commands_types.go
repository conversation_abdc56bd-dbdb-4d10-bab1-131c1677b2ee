package design

import (
	. "goa.design/goa/v3/dsl"
)

var AggregateCostCorrectedResponse = Type("aggregateCostCorrectedResponse", func() {
	Attribute("id", String, func() {
		Description("UUID of the charge.")
		Format(FormatUUID)
	})
	Attribute("cost", Int, func() {
		Description("New cost of the charge in lowest denomination of its currency (pence, euro cents).")
		Example(16)
	})
	Attribute("submittedBy", String, func() {
		Description("Who has submitted the correction request.")
		Example("John Doe")
	})
	Required("id", "cost")
})

var AggregateSettlementAmountCorrectedResponse = Type("aggregateSettlementAmountCorrectedResponse", func() {
	Attribute("id", String, func() {
		Description("UUID of the charge.")
		Format(FormatUUID)
	})
	Attribute("settlementAmount", Int, func() {
		Description("New settlement amount of the charge in lowest denomination of its currency (pence, euro cents).")
		Example(16)
	})
	Attribute("submittedBy", String, func() {
		Description("Who has submitted the correction request.")
		Example("John Doe")
	})
	Required("id", "settlementAmount")
})
