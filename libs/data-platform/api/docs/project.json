{"name": "data-platform-api-docs", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-platform/api/docs/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g html -i ../contract/gen/http/openapi3.yaml -o src", "rm openapitools.json", "npx prettier . --write"]}, "configurations": {"docker": {"cwd": "", "commands": ["docker run --rm -v ${PWD}:/local -w /local openapitools/openapi-generator-cli generate -g html -i libs/data-platform/api/contract/gen/http/openapi3.yaml -o /local/libs/data-platform/api/docs/src", "npx prettier libs/data-platform/api/docs/src --write"]}}}}, "implicitDependencies": ["data-platform-api-contract"], "tags": ["data-platform"]}