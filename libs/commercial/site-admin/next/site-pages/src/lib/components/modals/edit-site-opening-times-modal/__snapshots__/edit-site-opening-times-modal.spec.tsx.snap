// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EditSiteOpeningTimesModal should match snapshot 1`] = `
<body>
  <div />
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
      <div>
        <div
          aria-label="Edit opening times"
          aria-labelledby="headlessui-dialog-title-:test-id-8"
          aria-modal="true"
          class="relative z-10"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-dialog-:test-id-0"
          role="dialog"
          tabindex="-1"
        >
          <div
            aria-hidden="true"
            class="fixed inset-0 bg-neutral/75 duration-300 ease-out data-[closed]:opacity-0"
            data-headlessui-state="open"
            data-open=""
          />
          <div
            class="fixed inset-0 z-10 overflow-y-auto"
          >
            <div
              class="flex min-h-full items-end justify-center p-4 text-center sm:items-center"
            >
              <div
                class="relative rounded-lg bg-white p-4 text-left shadow-xl duration-300 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 transition-all w-[60rem]"
                data-headlessui-state="open"
                data-open=""
                id="headlessui-dialog-panel-:test-id-7"
              >
                <div
                  class="absolute top-0 right-0 pt-4 pr-4"
                >
                  <button
                    class="flex w-7 h-7 justify-center items-center rounded-full bg-neutral/20 hover:bg-neutral/30 text-neutral outline-hidden focus:ring-2 focus:ring-neutral/50"
                    type="button"
                  >
                    <svg
                      class="h-4 w-4 stroke-1 stroke-current fill-current"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Close
                      </title>
                      <g>
                        <g>
                          <line
                            x1="5.16"
                            x2="18.94"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                        <g>
                          <line
                            x1="18.94"
                            x2="5.16"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
                <h2
                  class="pb-4 text-xxl"
                  data-headlessui-state="open"
                  data-open=""
                  id="headlessui-dialog-title-:test-id-8"
                >
                  Edit opening times
                </h2>
                <div
                  class="pb-6"
                >
                  <p
                    class="text-md font-normal text-neutral break-words"
                  >
                    Opening times will be reflected in the Pod Point app.
                  </p>
                  <div
                    class="pb-4"
                  />
                  <form
                    id="site-opening-times-form"
                    novalidate=""
                  >
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Monday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Monday open"
                                id="Monday open-label"
                              >
                                Monday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Monday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Monday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.0.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Monday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Monday all day"
                                id="Monday all day-label"
                              >
                                Monday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Monday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Monday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.0.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Monday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.0.from"
                          >
                            Monday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.0.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.0.to"
                          >
                            Monday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.0.to"
                          value="12:30"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Tuesday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Tuesday open"
                                id="Tuesday open-label"
                              >
                                Tuesday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Tuesday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Tuesday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.1.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Tuesday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Tuesday all day"
                                id="Tuesday all day-label"
                              >
                                Tuesday all day
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Tuesday all day"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Tuesday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.1.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Tuesday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.1.from"
                          >
                            Tuesday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          disabled=""
                          id="openingTimes.1.from"
                          value="All day"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.1.to"
                          >
                            Tuesday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          disabled=""
                          id="openingTimes.1.to"
                          value="All day"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Wednesday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Wednesday open"
                                id="Wednesday open-label"
                              >
                                Wednesday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Wednesday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Wednesday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.2.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Wednesday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Wednesday all day"
                                id="Wednesday all day-label"
                              >
                                Wednesday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Wednesday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Wednesday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.2.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Wednesday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.2.from"
                          >
                            Wednesday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.2.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.2.to"
                          >
                            Wednesday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.2.to"
                          value="12:30"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Thursday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Thursday open"
                                id="Thursday open-label"
                              >
                                Thursday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Thursday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Thursday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.3.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Thursday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Thursday all day"
                                id="Thursday all day-label"
                              >
                                Thursday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Thursday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Thursday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.3.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Thursday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.3.from"
                          >
                            Thursday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.3.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.3.to"
                          >
                            Thursday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.3.to"
                          value="12:30"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Friday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Friday open"
                                id="Friday open-label"
                              >
                                Friday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Friday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Friday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.4.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Friday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Friday all day"
                                id="Friday all day-label"
                              >
                                Friday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Friday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Friday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.4.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Friday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.4.from"
                          >
                            Friday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.4.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.4.to"
                          >
                            Friday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.4.to"
                          value="12:30"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Saturday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Saturday open"
                                id="Saturday open-label"
                              >
                                Saturday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Saturday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Saturday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.5.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Saturday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Saturday all day"
                                id="Saturday all day-label"
                              >
                                Saturday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Saturday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Saturday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.5.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Saturday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.5.from"
                          >
                            Saturday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.5.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.5.to"
                          >
                            Saturday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.5.to"
                          value="21:00"
                        />
                      </div>
                    </div>
                    <div
                      class="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-2"
                    >
                      <div
                        class="font-bold order-1"
                      >
                        Sunday
                      </div>
                      <div
                        class="sm:order-2 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Sunday open"
                                id="Sunday open-label"
                              >
                                Sunday open
                              </label>
                            </div>
                            <button
                              aria-checked="true"
                              aria-label="Sunday open"
                              class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-checked=""
                              data-headlessui-state="checked"
                              id="Sunday open"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.6.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Sunday
                          </span>
                           Open
                        </label>
                      </div>
                      <div
                        class="sm:order-5 justify-self-center flex content-center mt-2"
                      >
                        <div
                          class="flex"
                        >
                          <div
                            data-headlessui-state=""
                          >
                            <div
                              class="mr-2"
                            >
                              <label
                                class="text-md font-bold sr-only"
                                for="Sunday all day"
                                id="Sunday all day-label"
                              >
                                Sunday all day
                              </label>
                            </div>
                            <button
                              aria-checked="false"
                              aria-label="Sunday all day"
                              class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                              data-headlessui-state=""
                              id="Sunday all day"
                              role="switch"
                              tabindex="0"
                              type="button"
                            >
                              <span
                                aria-hidden="true"
                                class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                              />
                            </button>
                            <span
                              hidden=""
                              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            />
                          </div>
                        </div>
                        <label
                          class="ml-2"
                          for="openingTimes.6.allDay"
                        >
                          <span
                            class="sr-only"
                          >
                            Sunday
                          </span>
                           All day
                        </label>
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-3"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.6.from"
                          >
                            Sunday from
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.6.from"
                          value="10:30"
                        />
                      </div>
                      <div
                        class="mx-auto w-full max-w-sm mb-6 sm:mb-2 order-4"
                      >
                        <div
                          class=""
                        >
                          <label
                            class="text-md font-bold sr-only block mb-2"
                            for="openingTimes.6.to"
                          >
                            Sunday to
                          </label>
                        </div>
                        <input
                          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                          id="openingTimes.6.to"
                          value="21:00"
                        />
                      </div>
                    </div>
                  </form>
                </div>
                <div
                  class="flex justify-between"
                >
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 mr-4"
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
                    form="site-opening-times-form"
                    type="submit"
                  >
                    Save changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
    </div>
  </div>
</body>
`;
