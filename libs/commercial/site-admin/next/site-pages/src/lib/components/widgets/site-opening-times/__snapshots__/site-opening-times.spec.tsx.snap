// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SiteOpeningTimes should match snapshot 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="flex justify-between items-center mb-3"
      >
        <h3
          class="text-lg font-bold pb-0"
        >
          Opening times
        </h3>
        <button
          aria-label="Edit opening times"
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
          data-testid="edit-opening-times"
          id="edit-opening-times"
          name="edit-opening-times"
          type="button"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
              />
              <path
                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
              />
            </g>
          </svg>
        </button>
      </div>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Opening times table
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3 sr-only"
                  scope="col"
                >
                  Day
                </th>
                <th
                  class="text-left p-3 sr-only"
                  scope="col"
                >
                  Time
                </th>
              </tr>
            </thead>
            <tbody
              class=""
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Monday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Tuesday
                </td>
                <td
                  class="whitespace-normal"
                >
                  All day
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Wednesday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Thursday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Friday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Saturday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 21:00
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Sunday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 21:00
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</body>
`;

exports[`SiteOpeningTimes should match snapshot when there are closed days 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="flex justify-between items-center mb-3"
      >
        <h3
          class="text-lg font-bold pb-0"
        >
          Opening times
        </h3>
        <button
          aria-label="Edit opening times"
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
          data-testid="edit-opening-times"
          id="edit-opening-times"
          name="edit-opening-times"
          type="button"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
              />
              <path
                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
              />
            </g>
          </svg>
        </button>
      </div>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Opening times table
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3 sr-only"
                  scope="col"
                >
                  Day
                </th>
                <th
                  class="text-left p-3 sr-only"
                  scope="col"
                >
                  Time
                </th>
              </tr>
            </thead>
            <tbody
              class=""
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Monday
                </td>
                <td
                  class="whitespace-normal"
                >
                  Closed
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Tuesday
                </td>
                <td
                  class="whitespace-normal"
                >
                  All day
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Wednesday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Thursday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Friday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 12:30
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Saturday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 21:00
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal font-bold px-2"
                >
                  Sunday
                </td>
                <td
                  class="whitespace-normal"
                >
                  10:30 - 21:00
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</body>
`;
