// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TariffsPage should match snapshot 1`] = `
<body
  class=""
>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-1 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          70p per hour
        </h1>
      </div>
    </header>
    <div
      class="pb-4"
    />
    <div
      class="rounded-md p-3 bg-warning/10 border-2 border-warning/30"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-warning"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-warning"
          >
            <p
              class="text-md font-normal break-words"
            >
              The Public Charge Point Regulations 2023 mandate that the price for charging through a public charger must be displayed to drivers in pence per kilowatt hour from the appointed date of 24th November 2023. In order to comply, tariffs assigned to a publicly accessible charger must only contain per kWh pricing schedules for all drivers.
            </p>
            <div
              class="pb-4"
            />
            <p
              class="text-md font-normal break-words"
            >
              Should the tariff remain non-compliant, Pod Point will remove the assigned chargers from the public map, preventing public use.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="md:flex"
    >
      <div
        class="flex-1 mr-4"
      >
        <h2
          class="text-2xl tour-tariff-schedules-title"
        >
          Schedules
        </h2>
        <p
          class="text-md font-normal break-words"
        >
          The schedules which make up this tariff. Schedules define how much a driver pays depending on their tariff tier (driver, member or public) and when they charge.
        </p>
      </div>
      <div
        class="md:ml-auto md:my-3"
      >
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed tour-tariff-add-schedule"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Add schedule
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="xl:flex"
      >
        <div
          class="xl:min-w-[480px]"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Public schedule
          </h3>
          <div
            class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
          >
            <div
              class="inline-block min-w-full -my-2 py-2 align-middle"
            >
              <table
                class="min-w-full"
                tabindex="0"
              >
                <caption
                  class="sr-only"
                >
                  Tariff schedules
                </caption>
                <thead
                  class="border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Start
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Stop
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Price
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Type
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="divide-y border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        10:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        23:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <p
                        class="text-md font-normal mb-2 break-words"
                      >
                        50p
                      </p>
                      <button
                        aria-label="Add price band public Monday 10:30:00-23:30:00"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="add-price-band-public-Monday-10:30:00-23:30:00"
                        id="add-price-band-public-Monday-10:30:00-23:30:00"
                        name="add-price-band-public-Monday-10:30:00-23:30:00"
                        type="button"
                      >
                        +
                      </button>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      per hour (plugged in)
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4 w-1"
                    >
                      <div
                        class="flex whitespace-nowrap space-x-1"
                      >
                        <button
                          aria-label="Edit schedule public Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="edit-schedule-public-Monday-10:30:00-23:30:00"
                          id="edit-schedule-public-Monday-10:30:00-23:30:00"
                          name="edit-schedule-public-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                              />
                              <path
                                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                              />
                            </g>
                          </svg>
                        </button>
                        <button
                          aria-label="Delete schedule public Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="delete-schedule-public-Monday-10:30:00-23:30:00"
                          id="delete-schedule-public-Monday-10:30:00-23:30:00"
                          name="delete-schedule-public-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                            />
                            <path
                              d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                            <path
                              d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
          class="w-full xl:max-w-lg 2xl:max-w-2xl xl:ml-2"
        >
          <div
            class="recharts-responsive-container"
            style="width: 100%; height: 320px; min-width: 0;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-4"
    />
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="xl:flex"
      >
        <div
          class="xl:min-w-[480px]"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Drivers schedule
          </h3>
          <div
            class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
          >
            <div
              class="inline-block min-w-full -my-2 py-2 align-middle"
            >
              <table
                class="min-w-full"
                tabindex="0"
              >
                <caption
                  class="sr-only"
                >
                  Tariff schedules
                </caption>
                <thead
                  class="border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Start
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Stop
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Price
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Type
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="divide-y border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        10:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        23:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <p
                        class="text-md font-normal mb-2 break-words"
                      >
                        50p
                      </p>
                      <button
                        aria-label="Add price band drivers Monday 10:30:00-23:30:00"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="add-price-band-drivers-Monday-10:30:00-23:30:00"
                        id="add-price-band-drivers-Monday-10:30:00-23:30:00"
                        name="add-price-band-drivers-Monday-10:30:00-23:30:00"
                        type="button"
                      >
                        +
                      </button>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      per hour (plugged in)
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4 w-1"
                    >
                      <div
                        class="flex whitespace-nowrap space-x-1"
                      >
                        <button
                          aria-label="Edit schedule drivers Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                          id="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                          name="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                              />
                              <path
                                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                              />
                            </g>
                          </svg>
                        </button>
                        <button
                          aria-label="Delete schedule drivers Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                          id="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                          name="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                            />
                            <path
                              d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                            <path
                              d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
          class="w-full xl:max-w-lg 2xl:max-w-2xl xl:ml-2"
        >
          <div
            class="recharts-responsive-container"
            style="width: 100%; height: 320px; min-width: 0;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-4"
    />
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="xl:flex"
      >
        <div
          class="xl:min-w-[480px]"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Members schedule
          </h3>
          <div
            class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
          >
            <div
              class="inline-block min-w-full -my-2 py-2 align-middle"
            >
              <table
                class="min-w-full"
                tabindex="0"
              >
                <caption
                  class="sr-only"
                >
                  Tariff schedules
                </caption>
                <thead
                  class="border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Start
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Stop
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Price
                    </th>
                    <th
                      class="text-left p-3"
                      scope="col"
                    >
                      Type
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="divide-y border-b border-b-neutral-100"
                >
                  <tr
                    class="border-b-neutral-100"
                  >
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        10:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <div
                        class="align-text-top font-semibold"
                      >
                        Monday
                      </div>
                      <div>
                        23:30:00
                      </div>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      <p
                        class="text-md font-normal mb-2 break-words"
                      >
                        50p
                      </p>
                      <button
                        aria-label="Add price band members Monday 10:30:00-23:30:00"
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                        data-testid="add-price-band-members-Monday-10:30:00-23:30:00"
                        id="add-price-band-members-Monday-10:30:00-23:30:00"
                        name="add-price-band-members-Monday-10:30:00-23:30:00"
                        type="button"
                      >
                        +
                      </button>
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4"
                    >
                      per hour (plugged in)
                    </td>
                    <td
                      class="whitespace-normal px-3 py-4 w-1"
                    >
                      <div
                        class="flex whitespace-nowrap space-x-1"
                      >
                        <button
                          aria-label="Edit schedule members Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="edit-schedule-members-Monday-10:30:00-23:30:00"
                          id="edit-schedule-members-Monday-10:30:00-23:30:00"
                          name="edit-schedule-members-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                              />
                              <path
                                d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                              />
                            </g>
                          </svg>
                        </button>
                        <button
                          aria-label="Delete schedule members Monday 10:30:00-23:30:00"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                          data-testid="delete-schedule-members-Monday-10:30:00-23:30:00"
                          id="delete-schedule-members-Monday-10:30:00-23:30:00"
                          name="delete-schedule-members-Monday-10:30:00-23:30:00"
                          type="button"
                        >
                          <svg
                            class="fill-current h-4 w-4"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                            />
                            <path
                              d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                            <path
                              d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
          class="w-full xl:max-w-lg 2xl:max-w-2xl xl:ml-2"
        >
          <div
            class="recharts-responsive-container"
            style="width: 100%; height: 320px; min-width: 0;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="pb-4"
    />
    <div
      class="md:flex"
    >
      <div>
        <h2
          class="text-2xl"
        >
          Chargers
        </h2>
        <p
          class="text-md font-normal break-words"
        >
          List of all chargers on your network to which this tariff applies.
        </p>
      </div>
      <div
        class="md:ml-auto md:my-3"
      >
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 ml-20 tour-tariff-manage-chargers"
          type="button"
        >
          Manage
        </button>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of chargers
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Name
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  PPID
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Site location
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Model
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Last contact
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Last known status
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              />
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                  href="/pods/BAR"
                >
                  FOO
                </a>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                BAR
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                  href="/sites/undefined"
                />
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                T7-S
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-col"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      12/08/2022 - 08:38:12
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex flex-row"
                >
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      <div
                        class="flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          class="fill-current h-4 w-4 mr-2 text-sky"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="16"
                            cy="16"
                            r="12"
                          />
                        </svg>
                        Available
                      </div>
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  role="tooltip"
                  tabindex="0"
                >
                  <svg
                    class="fill-current h-4 w-4 text-warning-600"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      Warning icon
                    </title>
                    <g
                      id="master"
                    >
                      <path
                        d="M16.01,15.54c-.81,0-1.46-.66-1.46-1.46l-.47-6.75c0-1.16,1.12-1.46,1.93-1.46s1.91,.35,1.91,1.46l-.45,6.75c0,.81-.66,1.46-1.46,1.46Z"
                      />
                      <path
                        d="M16.01,20.3c-.81,0-1.46-.66-1.46-1.46v-.06c0-.81,.66-1.46,1.46-1.46s1.46,.66,1.46,1.46v.06c0,.81-.66,1.46-1.46,1.46Z"
                      />
                      <path
                        d="M1.83,31.51c-.21,0-.41-.04-.61-.12-.6-.25-.99-.83-.99-1.48V6C.23,2.96,2.71,.49,5.75,.49H26.25c3.04,0,5.51,2.47,5.51,5.51v14.6c0,3.04-2.47,5.51-5.51,5.51H8.32c-.29,0-.58,.12-.79,.33l-4.56,4.59c-.31,.31-.72,.47-1.13,.47ZM5.75,2.49c-1.94,0-3.51,1.58-3.51,3.51V28.94l3.88-3.9c.58-.58,1.38-.92,2.21-.92H26.25c1.94,0,3.51-1.58,3.51-3.51V6c0-1.94-1.58-3.51-3.51-3.51H5.75Z"
                      />
                    </g>
                  </svg>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="fixed top-0 left-0 right-0 bottom-0 w-full h-screen z-50 overflow-hidden bg-neutral opacity-75 flex flex-col items-center justify-center"
    >
      <div
        role="status"
        title="Loading..."
      >
        <svg
          aria-hidden="true"
          class="h-16 w-16 animate-spin fill-primary text-neutral/30"
          viewBox="0 0 100 101"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </g>
        </svg>
        <span
          class="sr-only"
        >
          Loading...
        </span>
      </div>
    </div>
  </div>
</body>
`;
