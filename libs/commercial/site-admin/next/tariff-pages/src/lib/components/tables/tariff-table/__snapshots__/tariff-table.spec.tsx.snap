// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tariff table component should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of Tariffs
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Tariff
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Applies to
              </th>
              <th
                class="text-left p-3 print:hidden"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                  href="/tariffs/1"
                >
                  70p per hour
                </a>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                1 Charger
              </td>
              <td
                class="whitespace-normal px-3 py-4 print:hidden"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="Edit tariff 70p per hour"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-tariff-1"
                    id="edit-tariff-1"
                    name="edit-tariff-1"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <div
                      class="p-1.5"
                    >
                      <svg
                        class="fill-current h-4 w-4 text-warning"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Warning icon
                        </title>
                        <g
                          id="master"
                        >
                          <path
                            d="M16.01,15.54c-.81,0-1.46-.66-1.46-1.46l-.47-6.75c0-1.16,1.12-1.46,1.93-1.46s1.91,.35,1.91,1.46l-.45,6.75c0,.81-.66,1.46-1.46,1.46Z"
                          />
                          <path
                            d="M16.01,20.3c-.81,0-1.46-.66-1.46-1.46v-.06c0-.81,.66-1.46,1.46-1.46s1.46,.66,1.46,1.46v.06c0,.81-.66,1.46-1.46,1.46Z"
                          />
                          <path
                            d="M1.83,31.51c-.21,0-.41-.04-.61-.12-.6-.25-.99-.83-.99-1.48V6C.23,2.96,2.71,.49,5.75,.49H26.25c3.04,0,5.51,2.47,5.51,5.51v14.6c0,3.04-2.47,5.51-5.51,5.51H8.32c-.29,0-.58,.12-.79,.33l-4.56,4.59c-.31,.31-.72,.47-1.13,.47ZM5.75,2.49c-1.94,0-3.51,1.58-3.51,3.51V28.94l3.88-3.9c.58-.58,1.38-.92,2.21-.92H26.25c1.94,0,3.51-1.58,3.51-3.51V6c0-1.94-1.58-3.51-3.51-3.51H5.75Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <a
                  class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                  href="/tariffs/1"
                >
                  Unassigned tariff
                </a>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                Tariff not applied
              </td>
              <td
                class="whitespace-normal px-3 py-4 print:hidden"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="Edit tariff Unassigned tariff"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-tariff-1"
                    id="edit-tariff-1"
                    name="edit-tariff-1"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete tariff Unassigned tariff"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-tariff-1"
                    id="delete-tariff-1"
                    name="delete-tariff-1"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff table component should match snapshot when there are no tariffs 1`] = `
<body>
  <div>
    <div
      class="flex flex-col items-center text-center h-full mx-auto mt-16"
    >
      <svg
        class="fill-current h-[197px] w-[191px]"
        viewBox="0 0 191 197"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          transform="translate(5.4, 30.3)"
        >
          <path
            d="M121.063 99.4563C97.0927 106.501 90.997 95.8224 76.8417 90.9033C62.6863 85.9843 47.1537 78.6973 26.3093 91.7446C5.46484 104.792 8.8621 128.994 23.7377 144.917C38.6132 160.84 65.9984 169.582 91.5466 163.466C117.095 157.35 156.194 142.602 158.562 123.199C160.931 103.795 145.031 92.4116 121.063 99.4563Z"
            fill="#7F8083"
            opacity="0.1"
          />
          <path
            d="M182.522 69.7332L24.9475 24.5493L0.000398323 111.55L157.575 156.734L182.522 69.7332Z"
            fill="#7F8083"
            opacity="0.3"
          />
          <path
            d="M40.222 25.9889L47.6609 0.0463867C47.6609 0.0463867 80.6428 18.2215 114.393 31.4738C152.986 46.6212 186.534 55.7406 186.534 55.7406L183.301 67.0162L40.222 25.9889Z"
            fill="#7F8083"
            opacity="0.1"
          />
          <path
            d="M48.9621 104.058C62.2343 107.864 76.0788 100.19 79.8846 86.9177C83.6903 73.6455 76.0162 59.801 62.7439 55.9953C49.4717 52.1895 35.6272 59.8636 31.8215 73.1359C28.0157 86.4081 35.6898 100.253 48.9621 104.058Z"
            fill="#7F8083"
            opacity="0.3"
          />
        </g>
      </svg>
      <p
        class="text-md font-bold mt-5 text-neutral break-words"
      >
        You don't have any tariffs
      </p>
      <div
        class="mt-4 w-40"
      >
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-2.5 px-3.75 whitespace-nowrap"
          type="button"
        >
          View example data
        </button>
      </div>
    </div>
  </div>
</body>
`;
