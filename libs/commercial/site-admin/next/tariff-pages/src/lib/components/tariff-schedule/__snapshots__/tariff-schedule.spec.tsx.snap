// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tariff Schedule should match snapshot for a drivers duration tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
                <button
                  aria-label="Add price band drivers Monday 10:30:00-23:30:00"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="add-price-band-drivers-Monday-10:30:00-23:30:00"
                  id="add-price-band-drivers-Monday-10:30:00-23:30:00"
                  name="add-price-band-drivers-Monday-10:30:00-23:30:00"
                  type="button"
                >
                  +
                </button>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per hour (plugged in)
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a drivers energy tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per kWh
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a drivers fixed tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per charge
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="edit-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule drivers Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    id="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    name="delete-schedule-drivers-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a members duration tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
                <button
                  aria-label="Add price band members Monday 10:30:00-23:30:00"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="add-price-band-members-Monday-10:30:00-23:30:00"
                  id="add-price-band-members-Monday-10:30:00-23:30:00"
                  name="add-price-band-members-Monday-10:30:00-23:30:00"
                  type="button"
                >
                  +
                </button>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per hour (plugged in)
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-members-Monday-10:30:00-23:30:00"
                    id="edit-schedule-members-Monday-10:30:00-23:30:00"
                    name="edit-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-members-Monday-10:30:00-23:30:00"
                    id="delete-schedule-members-Monday-10:30:00-23:30:00"
                    name="delete-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a members energy tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per kWh
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-members-Monday-10:30:00-23:30:00"
                    id="edit-schedule-members-Monday-10:30:00-23:30:00"
                    name="edit-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-members-Monday-10:30:00-23:30:00"
                    id="delete-schedule-members-Monday-10:30:00-23:30:00"
                    name="delete-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a members fixed tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per charge
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-members-Monday-10:30:00-23:30:00"
                    id="edit-schedule-members-Monday-10:30:00-23:30:00"
                    name="edit-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule members Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-members-Monday-10:30:00-23:30:00"
                    id="delete-schedule-members-Monday-10:30:00-23:30:00"
                    name="delete-schedule-members-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a public duration tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
                <button
                  aria-label="Add price band public Monday 10:30:00-23:30:00"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="add-price-band-public-Monday-10:30:00-23:30:00"
                  id="add-price-band-public-Monday-10:30:00-23:30:00"
                  name="add-price-band-public-Monday-10:30:00-23:30:00"
                  type="button"
                >
                  +
                </button>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per hour (plugged in)
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-public-Monday-10:30:00-23:30:00"
                    id="edit-schedule-public-Monday-10:30:00-23:30:00"
                    name="edit-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-public-Monday-10:30:00-23:30:00"
                    id="delete-schedule-public-Monday-10:30:00-23:30:00"
                    name="delete-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a public energy tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per kWh
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-public-Monday-10:30:00-23:30:00"
                    id="edit-schedule-public-Monday-10:30:00-23:30:00"
                    name="edit-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-public-Monday-10:30:00-23:30:00"
                    id="delete-schedule-public-Monday-10:30:00-23:30:00"
                    name="delete-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should match snapshot for a public fixed tariff 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Start
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Stop
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  10:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="align-text-top font-semibold"
                >
                  Monday
                </div>
                <div>
                  23:30:00
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per charge
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-public-Monday-10:30:00-23:30:00"
                    id="edit-schedule-public-Monday-10:30:00-23:30:00"
                    name="edit-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule public Monday 10:30:00-23:30:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-public-Monday-10:30:00-23:30:00"
                    id="delete-schedule-public-Monday-10:30:00-23:30:00"
                    name="delete-schedule-public-Monday-10:30:00-23:30:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`Tariff Schedule should not show start and end time if schedule is a flat rate 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Tariff schedules
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Price
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Type
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p
                  class="text-md font-normal mb-2 break-words"
                >
                  50p
                </p>
                <button
                  aria-label="Add price band drivers Tuesday 10:00-10:00"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="add-price-band-drivers-Tuesday-10:00-10:00"
                  id="add-price-band-drivers-Tuesday-10:00-10:00"
                  name="add-price-band-drivers-Tuesday-10:00-10:00"
                  type="button"
                >
                  +
                </button>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                per hour (plugged in)
              </td>
              <td
                class="whitespace-normal px-3 py-4 w-1"
              >
                <div
                  class="flex whitespace-nowrap space-x-1"
                >
                  <button
                    aria-label="Edit schedule drivers Tuesday 10:00-10:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-schedule-drivers-Tuesday-10:00-10:00"
                    id="edit-schedule-drivers-Tuesday-10:00-10:00"
                    name="edit-schedule-drivers-Tuesday-10:00-10:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Delete schedule drivers Tuesday 10:00-10:00"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="delete-schedule-drivers-Tuesday-10:00-10:00"
                    id="delete-schedule-drivers-Tuesday-10:00-10:00"
                    name="delete-schedule-drivers-Tuesday-10:00-10:00"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                      />
                      <path
                        d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                      <path
                        d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
