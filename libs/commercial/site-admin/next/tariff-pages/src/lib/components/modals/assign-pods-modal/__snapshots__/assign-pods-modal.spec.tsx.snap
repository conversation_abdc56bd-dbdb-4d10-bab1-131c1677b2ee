// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AssignPodsModal should match snapshot 1`] = `
<body>
  <div />
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
      <div>
        <div
          aria-label="Assign chargers"
          aria-labelledby="headlessui-dialog-title-:test-id-8"
          aria-modal="true"
          class="relative z-10"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-dialog-:test-id-0"
          role="dialog"
          tabindex="-1"
        >
          <div
            aria-hidden="true"
            class="fixed inset-0 bg-neutral/75 duration-300 ease-out data-[closed]:opacity-0"
            data-headlessui-state="open"
            data-open=""
          />
          <div
            class="fixed inset-0 z-10 overflow-y-auto"
          >
            <div
              class="flex min-h-full items-end justify-center p-4 text-center sm:items-center"
            >
              <div
                class="relative rounded-lg bg-white p-4 text-left shadow-xl duration-300 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 transition-all w-[52rem]"
                data-headlessui-state="open"
                data-open=""
                id="headlessui-dialog-panel-:test-id-7"
              >
                <div
                  class="absolute top-0 right-0 pt-4 pr-4"
                >
                  <button
                    class="flex w-7 h-7 justify-center items-center rounded-full bg-neutral/20 hover:bg-neutral/30 text-neutral outline-hidden focus:ring-2 focus:ring-neutral/50"
                    type="button"
                  >
                    <svg
                      class="h-4 w-4 stroke-1 stroke-current fill-current"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Close
                      </title>
                      <g>
                        <g>
                          <line
                            x1="5.16"
                            x2="18.94"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                        <g>
                          <line
                            x1="18.94"
                            x2="5.16"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
                <h2
                  class="pb-4 text-xxl"
                  data-headlessui-state="open"
                  data-open=""
                  id="headlessui-dialog-title-:test-id-8"
                >
                  Assign chargers
                </h2>
                <div
                  class="pb-6"
                >
                  <div
                    class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
                  >
                    <div
                      class="inline-block min-w-full -my-2 py-2 align-middle"
                    >
                      <table
                        class="min-w-full"
                        tabindex="0"
                      >
                        <caption
                          class="sr-only"
                        >
                          Assign chargers
                        </caption>
                        <thead
                          class="border-b border-b-neutral/20"
                        >
                          <tr
                            class="border-b-neutral/20"
                          >
                            <th
                              class="text-left p-3"
                              scope="col"
                            >
                              <div
                                class=""
                              >
                                <label
                                  class="text-md font-bold sr-only block mb-2"
                                  for="select-all"
                                >
                                  Select All
                                </label>
                              </div>
                              <input
                                class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -ml-1"
                                id="select-all"
                                type="checkbox"
                                value=""
                              />
                            </th>
                            <th
                              class="text-left p-3"
                              scope="col"
                            >
                              Chargers
                            </th>
                            <th
                              class="text-left p-3"
                              scope="col"
                            >
                              Site
                            </th>
                            <th
                              class="text-left p-3"
                              scope="col"
                            >
                              Current tariff
                            </th>
                          </tr>
                        </thead>
                        <tbody
                          class="divide-y border-b border-b-neutral/20"
                        >
                          <tr
                            class="border-b-neutral/20"
                          >
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              <div
                                class=""
                              >
                                <label
                                  class="text-md font-bold sr-only block mb-2"
                                  for="select-1"
                                >
                                  Select FOO
                                </label>
                              </div>
                              <input
                                checked=""
                                class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                                id="select-1"
                                type="checkbox"
                                value=""
                              />
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              FOO
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              70p per hour
                            </td>
                          </tr>
                          <tr
                            class="border-b-neutral/20 text-neutral/60"
                          >
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              <div
                                role="tooltip"
                                tabindex="0"
                              >
                                <div
                                  class=""
                                >
                                  <label
                                    class="text-md font-bold sr-only block mb-2"
                                    for="select-2"
                                  >
                                    Select BAR
                                  </label>
                                </div>
                                <input
                                  class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                                  disabled=""
                                  id="select-2"
                                  type="checkbox"
                                  value=""
                                />
                              </div>
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              BAR
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                          </tr>
                          <tr
                            class="border-b-neutral/20 text-neutral/60"
                          >
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              <div
                                role="tooltip"
                                tabindex="0"
                              >
                                <div
                                  class=""
                                >
                                  <label
                                    class="text-md font-bold sr-only block mb-2"
                                    for="select-3"
                                  >
                                    Select NoKwhSupport
                                  </label>
                                </div>
                                <input
                                  class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                                  disabled=""
                                  id="select-3"
                                  type="checkbox"
                                  value=""
                                />
                              </div>
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              NoKwhSupport
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                          </tr>
                          <tr
                            class="border-b-neutral/20 text-neutral/60"
                          >
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              <div
                                role="tooltip"
                                tabindex="0"
                              >
                                <div
                                  class=""
                                >
                                  <label
                                    class="text-md font-bold sr-only block mb-2"
                                    for="select-4"
                                  >
                                    Select OcppSupport
                                  </label>
                                </div>
                                <input
                                  class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                                  disabled=""
                                  id="select-4"
                                  type="checkbox"
                                  value=""
                                />
                              </div>
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            >
                              OcppSupport
                            </td>
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                            <td
                              class="whitespace-normal px-3 py-4"
                            />
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div
                    class="rounded-md p-3 bg-info/10 border-2 border-info/30 mt-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        class="shrink-0"
                      >
                        <span
                          class="text-info"
                        >
                          <svg
                            class="fill-current w-6 h-6"
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                              />
                              <path
                                d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                              />
                              <path
                                d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                              />
                            </g>
                          </svg>
                        </span>
                      </div>
                      <div
                        class="flex flex-col justify-center ml-4"
                      >
                        <p
                          class="text-md font-normal sr-only break-words"
                        >
                          Alert
                        </p>
                        <div
                          class="text-info"
                        >
                          <p
                            class="text-md font-normal break-words"
                          >
                            Chargers which don't currently require drivers to confirm their charge will be updated to confirm charge when assigned to a tariff.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="flex justify-between"
                >
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 mr-4"
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
                    type="submit"
                  >
                    Save changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
    </div>
  </div>
</body>
`;
