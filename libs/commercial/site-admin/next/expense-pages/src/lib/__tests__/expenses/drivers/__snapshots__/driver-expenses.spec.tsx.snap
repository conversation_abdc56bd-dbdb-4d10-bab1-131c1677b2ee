// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DriverExpensesPage should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col space-y-4 pb-4"
    >
      <header
        class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
      >
        <div
          class="flex items-baseline col-span-2"
        >
          <h1
            class="text-2xl font-medium"
          >
            <PERSON> Bloggs
          </h1>
        </div>
        <div
          class="flex items-center"
        >
          <p
            class="text-md font-normal break-words"
          >
            Expenses submitted by this driver.
          </p>
        </div>
        <div
          class="flex justify-end items-center print:hidden"
        >
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-:test-id-2"
              type="button"
            >
              <span>
                Actions
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </header>
      <div
        class="flex flex-1 divide-x-2"
      >
        <div
          class="pr-6"
        >
          <span
            class="font-bold"
          >
            Home Charges
          </span>
          <div
            class="flex items-baseline"
          >
            <h2
              class="text-2xl"
            >
              £1.23
            </h2>
            <div
              class="text-neutral-400 pl-2"
            >
              Exc. VAT
            </div>
          </div>
          <span
            class="text-neutral-400"
          >
            135
             kWh
          </span>
        </div>
        <div
          class="pl-6"
        >
          <span
            class="font-bold"
          >
            Public Charges
          </span>
          <div
            class="flex items-baseline"
          >
            <h2
              class="text-2xl"
            >
              £3.21
            </h2>
            <div
              class="text-neutral-400 pl-2"
            >
              Inc. VAT
            </div>
          </div>
          <span
            class="text-neutral-400"
          >
            531
             kWh
          </span>
        </div>
      </div>
    </div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="infinite-scroll-component__outerdiv"
    >
      <div
        class="infinite-scroll-component "
        style="height: auto; overflow: auto;"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of expensed charges
              </caption>
              <thead
                class="border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="select-all"
                      >
                        Select All
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -ml-1"
                      id="select-all"
                      type="checkbox"
                      value=""
                    />
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Started
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      >
                        <svg
                          class="fill-current h-4 w-4 stroke-current stroke-2"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            transform="translate(4 4)"
                          >
                            <path
                              d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                            />
                          </g>
                        </svg>
                      </span>
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Ended
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Submitted date time
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Cost
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      kWh used
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Location type
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Charger name
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Location
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Processed date time
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Processed by
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="select-1"
                      >
                        Select Driver
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                      id="select-1"
                      type="checkbox"
                      value=""
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    16 Sep 2022 - 14:58
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    16 Sep 2022 - 15:23
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex flex-1 items-center space-x-2"
                    >
                      <span>
                        16 Sep 2022 - 16:58
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    £70.12
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    12.5 kWh
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    public
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Kate-Jane
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    234 Banner St, London, EC1Y 8QE
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    16 Sep 2022 - 18:58
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    John Wick
                  </td>
                </tr>
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="select-2"
                      >
                        Select Driver
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-6 h-6 checked:bg-neutral -mt-1"
                      id="select-2"
                      type="checkbox"
                      value=""
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    09 Aug 2022 - 01:58
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    09 Aug 2022 - 02:56
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex flex-1 items-center space-x-2"
                    >
                      <span>
                        09 Oct 2022 - 08:01
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    £123.32
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    43.5 kWh
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    home
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    PSL123456
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    -
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    20 Oct 2022 - 08:01
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Alan Smith
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
