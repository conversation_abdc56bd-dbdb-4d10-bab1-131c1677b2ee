import {
  Checkbox,
  ExpandIcon,
  Paragraph,
  useMultiCheckbox,
} from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import { ExpensableChargeDriverSummary } from '@experience/shared/axios/data-platform-api-client';
import { InfiniteTable, Option } from '@experience/shared/react/design-system';
import { filterBySearchTerms } from '@experience/shared/react/hooks';
import { formatPenceAsCurrencyString } from '@experience/shared/typescript/utils';
import { useBoundStore } from '../services/store';
import { useEffect, useMemo, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import classNames from 'classnames';
import dayjs from 'dayjs';

export interface DriverExpensesSummaryTableProps {
  data: ExpensableChargeDriverSummary[];
  filterDate: dayjs.Dayjs;
  filterDateOptions: Option[];
  setFilterDate: (date: dayjs.Dayjs) => void;
}

export const DriverExpensesSummaryTable = ({
  data: summaries,
  filterDate,
  filterDateOptions,
  setFilterDate,
}: DriverExpensesSummaryTableProps) => {
  const [currentTabId, tabStore, setTabStore] = useBoundStore(
    useShallow((state) => [
      state.currentTabId,
      state.tabStore,
      state.setTabStore,
    ])
  );

  const [filteredData, setFilteredData] =
    useState<ExpensableChargeDriverSummary[]>(summaries);

  const { toggle, isChecked, toggleAll, areAllChecked } = useMultiCheckbox(
    summaries.map((summary) => summary.driver.id),
    {
      get: () => tabStore.get(currentTabId)?.selectedDriverIds ?? [],
      set: (selection) => {
        setTabStore(currentTabId, {
          ...tabStore.get(currentTabId),
          selectedDriverIds: selection,
          selectedChargeIds: mapSelectedDriverIdsIntoChargeIds(selection),
        });
      },
    }
  );

  const mapSelectedDriverIdsIntoChargeIds = (
    selectedDriverIds: number[]
  ): number[] => {
    if (selectedDriverIds.length === 0) {
      return [];
    }

    return summaries
      .filter((summary) => selectedDriverIds.includes(summary.driver.id))
      .flatMap((summary) => summary.submittedChargeIds)
      .filter((item): item is number => !!item);
  };

  const searchKeys = useMemo(() => ['driver.fullName', 'driver.email'], []);
  const currentSearchTerm = tabStore.get(currentTabId)?.searchTerm;

  useEffect(() => {
    const filterResult = filterBySearchTerms(
      summaries,
      [currentSearchTerm ?? ''],
      searchKeys
    ) as ExpensableChargeDriverSummary[];

    setFilteredData(filterResult);

    setTabStore(currentTabId, {
      ...tabStore.get(currentTabId),
      searchTerm: currentSearchTerm ?? '',
    });
  }, [
    currentSearchTerm,
    currentTabId,
    searchKeys,
    summaries,
    setTabStore,
    tabStore,
  ]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns: ColumnDef<ExpensableChargeDriverSummary, any>[] = [
    {
      accessorKey: 'checkboxes',
      enableSorting: false,
      header: () => (
        <Checkbox
          checked={areAllChecked()}
          className="-ml-1"
          id="select-all"
          label="Select All"
          onChange={toggleAll}
          srOnlyLabel={true}
        />
      ),
      cell: ({ row: { original: summary } }) => (
        <Checkbox
          checked={isChecked(summary.driver.id)}
          className="-mt-1"
          id={`select-${summary.driver.id}`}
          label="Select Driver"
          onChange={() => toggle(summary.driver.id)}
          srOnlyLabel={true}
        />
      ),
    },
    {
      accessorKey: 'driver.fullName',
      cell: ({ row: { original: summary } }) => (
        <a
          href={`/expenses/drivers/${summary.driver.id}`}
          target="_blank"
          rel="noreferrer"
          className={classNames(
            'flex items-center hover:underline hover:underline-offset-4'
          )}
        >
          {summary.driver.fullName}
          <ExpandIcon.SOLID className={classNames('ml-2')} aria-hidden="true" />
        </a>
      ),
      header: () => 'Name',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'driver.email',
      header: () => 'Email',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'totalCharges',
      header: () => 'Total charges',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'totalUsage.home',
      cell: ({ row: { original: summary } }) =>
        `${summary.totalUsage.home} kWh`,
      header: () => 'Home usage (kWh)',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'totalCost.home',
      cell: ({ row: { original: summary } }) =>
        formatPenceAsCurrencyString({
          amount: summary.totalCost.home,
        }),
      header: () => (
        <span>
          <span className="block">Home cost</span>
          <span className="inline-block text-xs text-neutral/60">Exc. VAT</span>
        </span>
      ),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'totalUsage.public',
      cell: ({ row: { original: summary } }) =>
        `${summary.totalUsage.public} kWh`,
      header: () => 'Public usage (kWh)',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'totalCost.public',
      cell: ({ row: { original: summary } }) =>
        formatPenceAsCurrencyString({
          amount: summary.totalCost.public,
        }),
      header: () => (
        <span>
          <span className="block">Public cost</span>
          <span className="inline-block text-xs text-neutral/60">Inc. VAT</span>
        </span>
      ),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'submittedTime',
      header: 'Submitted date time',
      sortingFn: 'alphanumeric',
    },
  ];

  return (
    <div className="relative">
      {filteredData.length !== summaries.length && (
        <Paragraph className="px-2 pb-2 absolute right-0 -top-10">
          {`Showing ${filteredData.length} of ${summaries.length} results`}
        </Paragraph>
      )}

      <InfiniteTable
        caption="Table of driver expenses"
        columns={columns}
        data={filteredData}
        filters={{
          serverSide: [
            {
              columnId: 'submittedTime',
              label: 'Submitted month',
              options: filterDateOptions,
              selected: filterDateOptions.find(
                (option) =>
                  option.id ===
                  filterDate.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
              ),
              onChange: (optionId: number | string) =>
                setFilterDate(dayjs(optionId as string)),
            },
          ],
        }}
        hiddenColumns={{ submittedTime: false }}
        id="driver-expenses-summary-table"
        initialSort={[
          {
            id: 'driver_fullName',
            desc: false,
          },
        ]}
        showSearchField
      />
    </div>
  );
};
