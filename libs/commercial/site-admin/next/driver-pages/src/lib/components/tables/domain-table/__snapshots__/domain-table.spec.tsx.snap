// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DomainTable should render successfully 1`] = `
<DocumentFragment>
  <div
    class="flex"
  >
    <div
      class="flex items-center mb-4 space-x-4"
    >
      <div
        class="items-center print:hidden self-end"
      >
        <div
          class="relative"
        >
          <span
            class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                />
              </g>
            </svg>
          </span>
          <div
            class=""
          >
            <label
              class="text-md font-bold sr-only block mb-2"
              for="table-search"
            >
              Search
            </label>
          </div>
          <input
            aria-label="Search table"
            class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
            id="table-search"
            placeholder="Search"
            role="searchbox"
            value=""
          />
          <button
            aria-label="Clear search"
            class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
            name="Clear search"
          >
            <svg
              class="h-3 w-3 stroke-1 stroke-current fill-current"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <g>
                  <line
                    x1="5.16"
                    x2="18.94"
                    y1="5.11"
                    y2="18.89"
                  />
                  <path
                    d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                  />
                </g>
                <g>
                  <line
                    x1="18.94"
                    x2="5.16"
                    y1="5.11"
                    y2="18.89"
                  />
                  <path
                    d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                  />
                </g>
              </g>
            </svg>
          </button>
        </div>
      </div>
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
        type="button"
      >
        Clear
      </button>
    </div>
  </div>
  <div
    class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
  >
    <div
      class="inline-block min-w-full -my-2 py-2 align-middle"
    >
      <table
        class="min-w-full"
        tabindex="0"
      >
        <caption
          class="sr-only"
        >
          Table of domains
        </caption>
        <thead
          class="border-b border-b-neutral-100"
        >
          <tr
            class="border-b-neutral-100"
          >
            <th
              class="text-left p-3 align-top"
              scope="col"
            >
              <button
                class="flex items-center hover:underline text-left cursor-pointer"
                type="button"
              >
                Domain name
                <span
                  class="w-4 h-4 ml-1 text-neutral shrink-0"
                />
              </button>
            </th>
            <th
              class="text-left p-3 align-top"
              scope="col"
            >
              <button
                class="flex items-center hover:underline text-left cursor-pointer"
                type="button"
              >
                Date activated
                <span
                  class="w-4 h-4 ml-1 text-neutral shrink-0"
                />
              </button>
            </th>
            <th
              class="text-left p-3 align-top"
              scope="col"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody
          class="divide-y border-b border-b-neutral-100"
        >
          <tr
            class="border-b-neutral-100"
          >
            <td
              class="whitespace-normal px-3 py-4"
            >
              testing.net
            </td>
            <td
              class="whitespace-normal px-3 py-4"
            >
              13th June 2021
            </td>
            <td
              class="whitespace-normal px-3 py-4"
            >
              <div
                class="flex space-x-1"
              >
                <button
                  aria-label="Edit domain testing.net"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="edit-domain-1"
                  id="edit-domain-1"
                  name="edit-domain-1"
                  type="button"
                >
                  <svg
                    class="fill-current h-4 w-4"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                      />
                      <path
                        d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                      />
                    </g>
                  </svg>
                </button>
                <button
                  aria-label="Delete domain testing.net"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="delete-domain-1"
                  id="delete-domain-1"
                  name="delete-domain-1"
                  type="button"
                >
                  <svg
                    class="fill-current h-4 w-4"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                    />
                    <path
                      d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                    />
                    <path
                      d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                    />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</DocumentFragment>
`;
