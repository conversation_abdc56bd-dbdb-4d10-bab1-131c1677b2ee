// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DriverChargeTable should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of driver charges
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charger
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Start time
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  CO2 avoided (kg)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Revenue generated
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`DriverChargeTable should render charge duration and plugged in duration as - 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of driver charges
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charger
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Start time
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  CO2 avoided (kg)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Revenue generated
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                -
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
