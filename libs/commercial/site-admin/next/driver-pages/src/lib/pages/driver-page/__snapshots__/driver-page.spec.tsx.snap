// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DriverChargePage should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          <PERSON> Bloggs
        </h1>
      </div>
      <div
        class="flex items-center"
      />
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Download CSV
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
      </div>
    </header>
    <h2
      class="text-2xl"
    >
      Charging stats - all charges
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              296.28
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              12976.860
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £13,728.00
        </span>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £9,736.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      *Estimated based on calculations. We are working hard to improve the accuracy of CO2 data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      All charges
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of driver charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charger
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Start time
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Charge duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Plugged in duration
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  CO2 avoided (kg)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Energy cost
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Revenue generated
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Nick-Gary
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                05 Sep, 2022 14:58
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                03:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                98.76
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                4325.620
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £3,245.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £4,576.00
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
