// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AdvertPage should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          VW Tesco Advert - ID3 - (2)
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          List of 1 charger where this advert appears.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 ml-20"
          type="button"
        >
          Assign charger
        </button>
      </div>
    </header>
    <p
      class="mb-2"
    >
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="https://podpoint-admin-tool.s3.eu-west-1.amazonaws.com/adverts/VW%20ID.3%20-%20855x975.png"
        target="_blank"
      >
        View advert
      </a>
    </p>
    <p
      class="mb-2"
    >
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="https://www.volkswagen.co.uk/electric/electric-cars/id3.html?gclsrc=aw.ds&gclid=Cj0KCQiAvbiBBhD-ARIsAGM48bzVNPkdyGrMJfauaYdnOJMQsY7uPS78caI6MBiOq3q_tEnPssuywbkaAul2EALw_wcB"
        target="_blank"
      >
        View webpage
      </a>
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Chargers
    </h2>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Chargers
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Name
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                PPID
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Site location
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Group
              </th>
              <th
                class="text-left p-3 print:hidden"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                FOO
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                BAR
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                Registers of Scotland
              </td>
              <td
                class="whitespace-normal px-3 py-4 print:hidden"
              >
                <button
                  aria-label="Remove advert"
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                  data-testid="remove-advert-from-charger-1"
                  id="remove-advert-from-charger-1"
                  name="remove-advert-from-charger-1"
                  type="button"
                >
                  <svg
                    class="fill-current h-4 w-4"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M28.88,4.85h-7.3v-1.63c0-1.66-1.35-3.01-3.01-3.01h-5.64c-1.66,0-3.01,1.35-3.01,3.01v1.63H3.12c-.55,0-1,.45-1,1s.45,1,1,1h1.71V27.19c0,2.53,2.06,4.6,4.6,4.6h12.6c2.53,0,4.6-2.06,4.6-4.6V6.85h2.26c.55,0,1-.45,1-1s-.45-1-1-1ZM11.93,3.22c0-.56,.45-1.01,1.01-1.01h5.64c.56,0,1.01,.45,1.01,1.01v1.63h-7.65v-1.63Zm12.69,23.98c0,1.43-1.16,2.6-2.6,2.6H9.43c-1.43,0-2.6-1.17-2.6-2.6V6.85H24.62V27.19Z"
                    />
                    <path
                      d="M12.23,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                    />
                    <path
                      d="M19.51,24.32c.55,0,1-.45,1-1V13.25c0-.55-.45-1-1-1s-1,.45-1,1v10.07c0,.55,.45,1,1,1Z"
                    />
                  </svg>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
