// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Login page should match snapshot 1`] = `
<body>
  <div>
    <h1
      class="text-2xl font-bold"
    >
      Hello!
    </h1>
    <form
      class="text-left"
      id="email-login-form"
      novalidate=""
    >
      <div
        class="space-y-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          Please enter your email address below and we will send you an email containing an access link.
        </p>
      </div>
      <div
        class="pb-4"
      />
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="email"
        >
          Email address
        </label>
      </div>
      <input
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="email"
        name="email"
        type="email"
        value=""
      />
      <div
        class="pb-4"
      />
      <div
        class="space-y-4"
      >
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 w-full"
          form="email-login-form"
          type="submit"
        >
          Request access link
        </button>
        <div
          class="relative flex items-center"
        >
          <div
            class="flex-grow border-t border-neutral"
          />
          <span
            class="flex-shrink mx-4"
          >
            <p
              class="text-md font-normal break-words"
            >
              or
            </p>
          </span>
          <div
            class="flex-grow border-t border-neutral"
          />
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 w-full"
          type="button"
        >
          Log in using password
        </button>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        This site is protected by reCAPTCHA Enterprise and the Google
         
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
          href="https://policies.google.com/privacy"
        >
          Privacy Policy
        </a>
         
        and
         
        <a
          class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
          href="https://policies.google.com/terms"
        >
          Terms of Service
        </a>
         
        apply.
      </p>
    </form>
  </div>
</body>
`;
