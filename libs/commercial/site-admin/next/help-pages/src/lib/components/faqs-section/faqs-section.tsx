import {
  Accordion,
  Anchor,
  <PERSON><PERSON>,
  Paragraph,
} from '@experience/shared/react/design-system';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import Image from 'next/image';

interface FaqsSectionProps {
  hash?: string;
}

export const faqs = [
  {
    title: 'The Pod Point network',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          How do EV drivers use the Pod Point network?
        </Heading.H4>
        <Paragraph>
          Using the Pod Point network is simple! The EV driver will arrive at
          the charger and connect their cable before opening the Pod Point app
          to search for the double-barrelled name of the charger under the
          Locate tab. Once the charger shows, they must click on it to see all
          details such as the charger&apos;s speed, tariff & any notices that
          the Site have shared. They then will select which door they are
          connected to by pressing &quot;Confirm Charge&quot;. The EV will start
          charging as soon as the cable is connected however a charge must be
          confirmed via the app within 15 minutes of this connection otherwise
          it will end.
        </Paragraph>
        <Paragraph>
          If your chargers are public, they won&apos;t need access to be granted
          via the Site Management Service portal however if your chargers are
          private for your network, you will need to add the EV driver as a
          Driver or Member within the portal so they find the Pod&apos;s in
          their app. Read more about the Drivers tab if your network are having
          issues finding a charger!
        </Paragraph>
        <Paragraph>
          You can read more about{' '}
          <Anchor
            href="https://help.pod-point.com/s/topic/0TO5q0000008OziGAE/using-the-pod-point-app"
            target="_blank"
          >
            how to use the Pod Point app
          </Anchor>{' '}
          here.
        </Paragraph>
        <Image
          src="/images/help-page/how-to-use-app-1.png"
          alt="How do EV drivers use the Pod Point network? - find location"
          width={320}
          height={320}
          className="mx-auto"
        />
        <Image
          src="/images/help-page/how-to-use-app-2.png"
          alt="How do EV drivers use the Pod Point network? - step-by-step"
          width={320}
          height={320}
          className="mx-auto"
        />

        <Heading.H4 className="text-md font-bold">
          Will the EV driver get 15 minutes of free energy?
        </Heading.H4>
        <Paragraph>
          While the EV driver has 15 minutes in total to claim the charge event,
          they will get a maximum of 5 minutes of energy dispensed before the
          Pod pauses & awaits the app authentication to be completed.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Our data shows that the EV drivers are getting free 15 minutes of
          energy - what now?
        </Heading.H4>
        <Paragraph>
          If you see in your data reports that there are full 15 minute charge
          periods being claimed back to back, please use the &quot;Report an
          issue&quot; button within the service as this is a fault with the
          charger.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          We have had reports of EV drivers not being able to see the charger on
          the map - what next?
        </Heading.H4>
        <Paragraph>
          If the EV driver is having difficulty finding the charger in the app
          they must make sure they are searching the double-barrelled name
          correctly with no auto-correct changes and that they are added as a
          registered driver to the private network by the Admin of the
          organisation that owns the charger. If they have been added and are
          searching the charger correctly, they will need to ensure they are
          signed into the Pod Point app with the exact same email that the Admin
          has placed on the Driver list for the organisation.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I remove the grace period so that no energy is dispensed and the
          charge only starts once confirmed?
        </Heading.H4>
        <Paragraph>
          This grace period cannot be turned off by yourselves or by us.
          Unfortunately this is a limitation with the hardware & not a feature
          that can be toggled off or on.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I remove the Confirm Charge requirement?
        </Heading.H4>
        <Paragraph>
          If you want your chargers to start without any app authentication,
          please contact our team with the charger details and we can action
          this for you. It&apos;s important to note that turning this off means
          you will not be able to collect revenue and your tariff will also be
          removed from the charger.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Help for EV Drivers
        </Heading.H4>
        <Paragraph>
          We always recommend directing the EV driver (Driver, Member or Public
          user) to our Customer Support team. They can assist with any faulty
          charger issues or user error. Please ask them to call our Support team
          directly on +44 (0) 207 247 4114 or visit our{' '}
          <Anchor href="https://help.pod-point.com/s/" target="_blank">
            Driver Help Centre
          </Anchor>{' '}
          for more assistance. They can also report issues with chargers via the
          app itself in a similar fashion to the &quot;Report an issue&quot;
          button available in the portal.
        </Paragraph>
        <Image
          src="/images/help-page/help-for-ev-drivers.png"
          alt="Help for EV Drivers"
          width={320}
          height={320}
          className="mx-auto"
        />
      </>
    ),
  },
  {
    title: 'Revenue and invoicing',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          How do we get our revenue back?
        </Heading.H4>
        <Paragraph>
          Every month, from when revenue is first taken, Pod Point issue a
          statement which advises the account how much revenue has been
          generated. A statement is generated for each site which has generated
          revenue. The statement outlines how much revenue is due and the fees
          which have been incurred. We will send you a Stripe invoice for our
          fees and we will automatically transfer your revenue to you once you
          have onboarded using the link we provide you with.
        </Paragraph>
        <Paragraph>
          If you have any questions regarding your statement or invoices, please
          get in touch and our Accounts Reconciliation team can assist.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I am experiencing delays in getting my revenue back - who can I
          contact?
        </Heading.H4>
        <Paragraph>
          If you have issues or delays with having your revenue returned to you,
          please contact{' '}
          <Anchor href="mailto:<EMAIL>">
            <EMAIL>
          </Anchor>{' '}
          with the below details;
        </Paragraph>
        <ul className="list-disc list-inside">
          <li>Account name </li>
          <li>Admin name & email</li>
          <li>Invoice number relating</li>
          <li>Description about the issue</li>
        </ul>

        <Heading.H4 className="text-md font-bold">
          Our revenue is not as expected
        </Heading.H4>
        <Paragraph>
          If your revenue amount is significantly different from your projected
          earnings, it&apos;s best to check your &quot;Tariffs&quot; page and
          ensure that there is a tariff applied to each charger you want to
          generate revenue with. Then, ensure you have the correct tariff tiers
          set for the visibility of the chargers. We always recommend setting a
          Driver, Member & Public schedule within each tariff so that no matter
          who on your admin list logs in to amend parts, there is no risk of the
          charger&apos;s tariff not covering all driver types. Setting a Public
          tariff while the charger is not on the public map does NOT
          automatically place this charger on the map. This is something we can
          action for you.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Reports and data',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          Where can I find downloadable data reports?
        </Heading.H4>
        <Paragraph>
          In order to find your usage data for a site, please log into the Site
          Management Service (SMS), click on the tab &quot;Sites&quot; on the
          left-hand side and select a site.
        </Paragraph>
        <Paragraph>
          Here you can see the four key metrics for this location so far in the
          current month. If you wish to download data for a specific month, you
          can do this by clicking the green button located under the map, marked
          &quot;Download CSV&quot; under the title &quot;Charging
          information&quot;.
        </Paragraph>
        <Paragraph>
          If you are looking for specific charger data, you can do the same
          within the &quot;Chargers&quot; section following the same steps. You
          can also download specific driver charge history within the
          &quot;Drivers&quot; tab by clicking on the persons name and, again,
          hitting the &quot;Download CSV&quot; button in the top right.
        </Paragraph>
        <Paragraph>
          We also have our &quot;Insights&quot; tab which can show you a
          downloadable view of the whole group/all sites by year.
        </Paragraph>
        <Paragraph>
          If ever you feel the data is not reflective of the chargers output,
          please give our support team a call on 0207 247 4114 or submit a
          ticket via the red &quot;Report an issue&quot; button for a specific
          site or charger and our team will investigate. The data is dependant
          on the communication of the chargers with our system so if there is a
          failure of contact this will mean data won&apos;t display correctly.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          What I want isn&apos;t visible - who do I contact?
        </Heading.H4>
        <Paragraph>
          If any reports aren&apos;t showing necessary information for your
          internal or external reporting needs, please use the
          &quot;Feedback&quot; button to advise us so we can look at
          improvements we can make. All feedback is considered however we will
          focus on requirement led requests so be sure to tell us what the need
          is for certain information.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Carbon avoided',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          How does Pod Point calculate carbon avoided?
        </Heading.H4>
        <Paragraph>
          We calculate carbon avoided by comparing the well-to-wheel CO2 from
          driving an electric car to driving equivalent number of miles in a
          petrol/diesel car.
        </Paragraph>
        <Paragraph>
          CO2e from electric miles: We first look at how many electric miles we
          enabled by charging via our connected network of chargers. We use an
          average electric car kWh per mile for the top 10 most sold cars over
          the last 2 years. - currently at 0.285 kWh per mile
        </Paragraph>
        <Paragraph>
          The energy enabling those miles comes via the grid, so we look at the
          average CO2e intensity of the grid (CO2 /kWh) to get to the total
          carbon emitted from the electric driving we enable. The intensity
          figure we use includes CO2 associated with consumption (e.g.
          transmission and distribution of electricity) but ignores upstream
          carbon emissions related to generation.
        </Paragraph>
        <Paragraph>
          It&apos;s usually assumed that a BEV will do around 3-4 miles per kWh.
          We looked at the top 10 sold BEVs in 2021 and 2022 and used a weighted
          average to find out the typical kWh per mile.
        </Paragraph>
        <Paragraph>
          CO2 from petrol miles: Finally, we look at how much CO2 would have
          been emitted if the electric miles we provided were driven by a petrol
          vehicle.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Source:</span>{' '}
          <Anchor href="https://www.gov.uk/government/publications/greenhouse-gas-reporting-conversion-factors-2022">
            Greenhouse gas reporting: conversion factors 2022 - GOV.UK
            (www.gov.uk)
          </Anchor>
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Drivers',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          What&apos;s the difference between Drivers & Members?
        </Heading.H4>
        <Paragraph>
          Drivers and Members are two different groups of exclusive driver. This
          means that, if listed, these people can see the charger on the private
          map. You can then select different &quot;tariff tiers&quot; for them.
          Typically, members are VIP drivers and will have a cheaper tariff
          again or sometimes no tariff at all. We always recommend setting a
          Driver, Member & Public schedule within each tariff so that no matter
          who on your admin list logs in to amend parts, there is no risk of the
          charger&quot;s tariff not covering all driver types. We think of this
          as &quot;default setting&quot; the schedule.
        </Paragraph>
        <Paragraph>
          If you wish to add a Member to your Driver list, please select
          &quot;Add driver&quot; as normal, input all details and then select
          tariff tier Member before hitting save.
        </Paragraph>
        <Paragraph>
          Drivers can be edited from Driver to Member and vice-versa by clicking
          the edit pencil next to their name.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I added a domain but our EV drivers still cannot access the Pods in
          their app
        </Heading.H4>
        <Paragraph>
          If you add a domain name (i.e. workplacedomain.com ) then anyone who
          creates a Pod Point app account with an email with the same domain
          from that moment onwards will have access to your network. If someone
          already has an app account with a workplace domain email, they
          won&apos;t be granted access retrospectively and will need to be added
          individually as a Driver or Member.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I have added EV drivers to my Drivers list but they still can&quot;t
          see the Pods in the app
        </Heading.H4>
        <Paragraph>
          If you can clearly see that you have added a Driver and they are
          showing as &quot;Registered&quot; next to their name, ensure they are
          searching the postcode for the location as it shows in SMS or the
          double barrelled name of the charger within the Locate tab. If they
          are doing all of the above, please ask them to call our Support team
          directly on +44 (0) 207 247 4114 or visit our{' '}
          <Anchor href="https://help.pod-point.com/s/">
            Driver Help Centre
          </Anchor>{' '}
          for more assistance.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          When we upload a Driver or Member do they get notified?
        </Heading.H4>
        <Paragraph>
          All Drivers, whether they&quot;re added via the bulk upload or
          individually, and regardless of their tariff tier, are sent an invite
          email to advise they have been granted access.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Do I have to use both Drivers and Members?
        </Heading.H4>
        <Paragraph>
          No - you can use just one of the Driver types if you wish! Be sure to
          always set a tariff tier for the driver type that you have listed to
          ensure revenue is taken from everyone you wish to charge.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I don&quot;t want to have to grant access by listing a domain or
          individual EV drivers - what are my options?
        </Heading.H4>
        <Paragraph>
          If you do not want to have to add individuals to the Driver list to
          grant access, you have two options;
        </Paragraph>
        <ol className="list-decimal list-outside pl-4">
          <li>
            The chargers can go on the public map with a public tariff applied.
            This would mean that anyone who has a Pod Point app account can see
            and use the chargers for the assigned Public tariff. This option is
            best if you are happy for anyone to use the chargers at any time or
            if you have a gated car park where people without authorisation are
            not able to enter. Please note that chargers must meet Public
            Charging regulations to be added to the public map, including using
            a per kWh pricing model for all tariff tiers.
          </li>
          <li>
            The chargers can have the app authentication feature (Confirm
            charge) turned off. This does mean that the chargers are not visible
            to the general public on the map, but anyone who can access the
            physical charger could plug in and use it, and you will also be
            unable to collect revenue from the chargers through an assigned
            tariff.
          </li>
        </ol>
        <Paragraph>
          If you need help with either putting a charger onto the public map or
          having the Confirm charge feature turned off, please get in touch with
          us via the Report an issue button.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          My network are reporting issues with the chargers - how can I report
          them to you?
        </Heading.H4>
        <Paragraph>
          We always recommend directing the EV driver end user (Driver, Member
          or Public user) to our Customer Support team. They can assist with any
          faulty charger issues or user error. Please ask them to call our
          Support team directly on +44 (0) 207 247 4114 or visit our{' '}
          <Anchor href="https://help.pod-point.com/s/">
            Driver Help Centre
          </Anchor>{' '}
          for more assistance. They can also report issues with chargers via the
          app itself in a similar fashion to the &quot;Report an issue&quot;
          button available in the portal.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          What does &quot;Can Expense&quot; mean?
        </Heading.H4>
        <Paragraph>
          Having this toggled on means that your Drivers will be able to expense
          charge events similar to how they would expense fuel. If you do not
          wish to use the Expenses feature, simply toggle this off. Having it
          off or on does not impact the functionality of the Pods for the EV
          driver. Read more about this feature under the Expenses FAQ.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I edit these sections later?
        </Heading.H4>
        <Paragraph>
          Yes - all sections are editable when you add a Driver. Simply click
          the edit pencil next to their name.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Expenses',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          Can I pay my drivers back in the portal?
        </Heading.H4>
        <Paragraph>
          This tab works as a receipt logging system where your network can send
          you individual charge event costs for reimbursement. You will have the
          option of exporting the expenses so that you can then reimburse your
          drivers externally. There is no way to reimburse drivers within the
          portal from the revenue you have already generated.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I accidentally had &quot;Can expense&quot; on for an EV driver who
          shouldn&apos;t be expensing charges - will they get a free charge?
        </Heading.H4>
        <Paragraph>
          No - the only change in their driver app is that they can submit the
          receipts to your organisation via the Expenses tab. They will not be
          getting free charge events unless they have been specifically set up
          as a Driver or Member with a 0p tariff tier associated. Read more
          about Drivers or Tariffs & Pricing in the related tabs.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can my network expense home charge events?
        </Heading.H4>
        <Paragraph>
          Yes! Your listed Drivers or Members can expense any charge event that
          they claim within the Pod Point app. This can include shopping centre,
          holiday lets, or their own home charge events.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can my network only expense charge events for a certain location?
        </Heading.H4>
        <Paragraph>
          If they have &quot;Can expense&quot; toggled on, they will be able to
          submit any charge event for expensing. This does not mean you have to
          accept that charge event! When you click into the charge event that
          was expensed within the tab, you can see the location along with
          whether or not it was a public or home charge event, kilowatt hours
          used and the cost.
        </Paragraph>

        <Heading.H4 className="text-md font-bold"></Heading.H4>
        <Paragraph></Paragraph>
      </>
    ),
  },
  {
    title: 'Tariffs and pricing',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          How do I create a tariff?
        </Heading.H4>
        <Paragraph>
          Start by using the tour feature in the &quot;Tariffs&quot; tab. If
          this no longer shows for you, read below.
        </Paragraph>
        <Paragraph>
          Head into the &quot;Tariffs&quot; tab & hit &quot;Add tariff&quot;.
        </Paragraph>
        <Paragraph>
          When creating the tariff, we recommend naming it for the site or group
          that it applies to along with the date so you can see when it was set
          at a glance.
        </Paragraph>
        <Paragraph>Eg: Block A - 1/1/2024</Paragraph>
        <Paragraph>
          Next, hit &quot;Add schedule&quot; before hitting the tariff tier
          type. We always recommend setting a Driver, Member & Public schedule
          within each tariff so there is no risk of the charger&apos;s tariff
          not covering all driver types. They can all be the same pricing or
          they can all be different. We think of this as &quot;default
          setting&quot; the schedule. Setting a Public tariff while the charger
          is not on the public map does NOT automatically place this charger on
          the map. This is something we can action for you so please get in
          touch if you need this actioned.
        </Paragraph>
        <Paragraph>
          Ensure you hit &quot;Manage&quot; and add all chargers that you want
          this pricing structure to have. It&apos;s important to note that only
          one tariff can apply to one charger - within the one tariff however
          you can have a pricing schedule for each of the tariff tiers.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          What should our tariff be?
        </Heading.H4>
        <Paragraph>
          A good guide on seeing what cost each kilowatt delivered incurs is:
          Energy Cost you&apos;re paying your provider per kilowatt + 20% of
          that amount for VAT + 1p (Pod Points transaction fee)
        </Paragraph>
        <Paragraph>
          After this, you can then add on any additional pence for internal
          admin relating to the chargers (often only 1-3p) as well as a few
          pence to accumulate over the warranty period of the chargers for any
          future out of warranty costs after the standard 3 years.
        </Paragraph>
        <Paragraph>
          Another way to check and see if you have set a fair market value for
          your chargers usage is to check Zap Map for other EVC&apos;s in your
          area.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Why is VAT 20% and not 5%?
        </Heading.H4>
        <Paragraph>
          When it comes time for the company to file their returns, the UK
          Government will see the revenue collected through EVC&apos;s as a
          service rather than utility. This means they will tax the income at
          20%. You will want to pass this on to your EV driver so always include
          an extra 20% on top of your energy cost to ensure your base costs are
          covered.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          How do we get our revenue back?
        </Heading.H4>
        <Paragraph>
          Every 90 days, starting when revenue is first taken, Pod Point issue a
          statement which advises the account how much to invoice us for. Once
          invoiced, we pay the money to a nominated bank account. We will send
          this invoice every 90 days however should you feel the amount is too
          small to invoice every 3 months, you&apos;re welcome to invoice us at
          6 or 12 months. Read more about this under the revenue & pricing FAQ.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I set a public tariff - will the charger automatically go on the
          public map?
        </Heading.H4>
        <Paragraph>
          Setting a Public tariff does not automatically place the charger on
          the public map, but rather sets a tariff in place in the event that
          the charger is placed on the public map. If you want any of your
          chargers placed off or on the public map, or you wish for the
          authentication feature (Confirm charge) to be turned off, we will need
          a written request by the organisations Admin with the below points
          listed;
        </Paragraph>
        <ul className="list-disc list-inside">
          <li>The double-barrelled name or PG number of all chargers</li>
          <li>
            The full installation address of the chargers including postcode
          </li>
          <li>The action you wish us to take</li>
        </ul>

        <Heading.H4 className="text-md font-bold">
          How do I know if my chargers are on the public map or not?
        </Heading.H4>
        <Paragraph>
          Head to the &quot;Chargers&quot; tab and use the filters to see if the
          configuration is correct for your Site requirements. Read more in the
          Chargers FAQ.
        </Paragraph>
        <Paragraph>
          If you wish for this to be amended, use the &quot;Report an
          issue&quot; button in the portal with details on which PG number you
          wish to be moved on or off the public map.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Sites',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          What is our Energy Cost?
        </Heading.H4>
        <Paragraph>
          Your Energy cost is the amount you are paying your supplier in pence
          per kilowatt. Please enter it without a 0 at the start i.e. 30 for
          30p. Inputting your Energy cost does not directly effect what your EV
          driver users are charged but rather is what calculates your overall
          cost spend on energy for this site. It is important to keep this up to
          date as you negotiate new energy tariffs with your provider as it is
          not currently possible to backdate this or place a timer on when it
          becomes live.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          From this page, what is visible to the EV driver and what is not?
        </Heading.H4>
        <Paragraph>
          Opening times & Additional information are both visible to the EV
          driver however the Contact details and Energy cost is not. Simply
          click the edit pencil on any of these four boxes to update the
          information accordingly.
        </Paragraph>
        <Paragraph>
          The site name of the Site is also visible to the EV driver user. If
          you want your site name to be changed, we can do this for you. Just
          get in touch with us via the &quot;Report an issue&quot; button.
        </Paragraph>
        <Paragraph>
          The drop pin on the map is also visible to the EV driver.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          The location of our Pods is incorrect on the map - how can I fix that?
        </Heading.H4>
        <Paragraph>
          If the drop pin is in the wrong please get the exact google
          coordinates (i.e. 51.502041, -0.14185791) and use the &quot;Report an
          issue&quot; button to advise our team of the correct coordinates. Your
          EV driver can also report chargers incorrectly located on the map by
          hitting the &quot;Report an issue&quot; button within the driver app.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Do the Opening Times effect when the Pods are restricted for use?
        </Heading.H4>
        <Paragraph>
          Setting Opening times does not restrict the use of the charger - This
          is a notification for the EV driver of any limitations of the chargers
          availability due to specific opening times for the location/car park.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Chargers',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          I can’t see a charger on my list?
        </Heading.H4>
        <Paragraph>
          If you have a Pod physically on site but not in visible in the portal,
          please get in touch with us via the &quot;Report an issue&quot; button
          and include the PG number & double barrelled name of the Pod. This
          will be on the front face of the charger.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I rename our Pods?
        </Heading.H4>
        <Paragraph>
          No, these are their human identifier names that they are given at the
          time of manufacturing.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          What do the filters mean on this page?
        </Heading.H4>
        <Paragraph>
          <span className="font-bold">Public</span> - If this is filtered YES
          and you see chargers listed, this means that these chargers are on the
          public map and anyone with a Pod Point app account can search &
          confirm a charge. If filtered NO, this means that the chargers are
          only visible on your private network and only those listed on the
          Driver list will be able to search and confirm charge events.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Confirm Charge</span> - This toggle
          identifies if the chargers require app authentication or if they are
          free-vend/plug & play. If you have chargers that are showing as
          Confirm charge YES and you want them to be free-vend, please get in
          touch with us via the Report an issue button with the chargers listed
          that you want to have the app authentication toggled off for. You can
          turn Confirm charge on for a charger by assigning a tariff in the
          service.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Tariff Assigned</span> - This filter shows
          you which chargers have a tariff assigned to them. You can amend this
          yourself anytime by heading to the &quot;Tariffs&quot; tab and adding
          the chargers to a valid Tariff. Read more about the Tariffs tab in the
          FAQ related.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Is there a way I can change a Pods public status?
        </Heading.H4>
        <Paragraph>
          There is no way for you to remove a charger from the Pod Point map,
          nor add it on. If you need this to be actioned, please use the
          &quot;Report an issue&quot; button and let us know the PG number and
          double-barrelled name of the Pod you want to be amended.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          The location of our Pods is incorrect on the map - how can I fix that?
        </Heading.H4>
        <Paragraph>
          If the drop pin is in the wrong please get the exact google
          coordinates (i.e.. 51.502041, -0.14185791) and use the &quot;Report an
          issue&quot; button to advise our team of the correct coordinates. Your
          EV drivers can also report chargers incorrectly located on the map by
          hitting the &quot;Report an issue&quot; button within the driver app.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Admins',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          How do I add an Admin?
        </Heading.H4>
        <Paragraph>
          Once a Group has been set up by Pod Point and the original admin
          added, we will not add or delete admins on behalf of our customers -
          this is to ensure we are not granting access to anyone who
          doesn&apos;t have the internal permissions or authorisation. If you
          have access, simply head to the Admins tab and select &quot;Add
          Admin&quot; before inputting their work email address.
        </Paragraph>
        <Paragraph>
          When adding a new admin, they will receive an automated email from the
          address <EMAIL> with detailed log in instructions that
          they must follow to gain access. If their email shows as Pending when
          added, it is possible that the email was input incorrectly or that the
          server the email operates from has increased security and has rejected
          the email. We recommend talking to your IT department, advising them
          of the no reply email and asking if they can grant access for inbound
          emails. You can retrigger the automated email after doing this.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          We don&apos;t have work domain emails. Can I use someone&apos;s
          personal email or a shared log in?
        </Heading.H4>
        <Paragraph>
          We strongly advise against shared log ins (i.e.
          <EMAIL>) or using personal emails. These both
          pose GDPR risks to your team.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Our previous Admin has left the company and we have lost access. How
          can we get in?
        </Heading.H4>
        <Paragraph>
          In the event of all admins having previously left the company and not
          adding a new admin prior to their departure, we can delete old admins
          & list a new admin. From there, it is up to the new Admin to list
          anyone else who may need access and be sure to hand over the portal if
          they are no longer involved in the ongoing management of the Pod’s due
          to moving teams or leaving the company. This is another reason why we
          strongly encourage not using shared log ins and ensuring the admin
          list is up to date with more than one person listed.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I can’t add an Admin
        </Heading.H4>
        <Paragraph>
          If you are unable to add an Admin, it is because their email is listed
          on another Group already. This is an unfortunate limitation within our
          database. If you have an Admin who needs access to two different
          Groups, you will need to use a different email for each Group log in.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Insights',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          Can I download monthly reports?
        </Heading.H4>
        <Paragraph>
          For any monthly reports, head into the &quot;Sites&quot; tab and
          download the report for that specific site. We are unable to do this
          on your behalf.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I download reports within date range?
        </Heading.H4>
        <Paragraph>
          At the moment we only provide reports that are by calendar month or
          year. If you have a requirement for a date range, please use the
          &quot;Feedback&quot; button with details on when you would use this
          feature and how it would benefit you.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Groups',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          Can I rename my Group?
        </Heading.H4>
        <Paragraph>
          We can do this for you! Please hit &quot;Report an issue&quot; and
          advise the current name of the group and the desired name.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can EV drivers see our Group name?
        </Heading.H4>
        <Paragraph>
          Yes anyone with access to the charger can see the name of the group.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          Can I provide limited access to the Site Management Service portal?
        </Heading.H4>
        <Paragraph>
          There are no levels of access in the Site Management Service. If an
          admin is added to the portal, they will have the same full permissions
          as yourself. They will be able to access the service, see data, add
          Drivers & Members, and set or amend Tariffs or Site details.
        </Paragraph>
      </>
    ),
  },
  {
    title: 'Emails',
    content: (
      <>
        <Heading.H4 className="text-md font-bold">
          I added an Admin and they show as &quot;Pending&quot; - why?
        </Heading.H4>
        <Paragraph>
          Pending means that the email listed is not valid and that we have been
          unable to deliver the automated email to it. Please check the email is
          spelt correctly. If it certainly is, please check with your IT team
          and ensure you have no security blocks that are preventing emails
          being received from no-reply addresses.
        </Paragraph>

        <Heading.H4 className="text-md font-bold">
          I added a Driver or Member but they aren&apos;t showing as
          &quot;Registered&quot; - what does that mean?
        </Heading.H4>
        <Paragraph>
          <span className="font-bold">Pending</span> - This means that the email
          listed is not associated with a Pod Point app account. Please check
          the spelling in SMS, the spelling that the driver input into the app
          and ensure that the two match exactly. If the driver has signed up
          with a personal email and you have added their work email, then the
          work email will show as pending. It&apos;s best to speak directly with
          the driver to be sure you both are using the exact same email.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Registered</span> - Success! This is an
          active Pod Point app account and they should have no issues with using
          the network. If they do, ask them to phone our Customer Support team
          for further help.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Deactivated</span> - This will show when
          the driver previously had an active Pod Point app account with this
          email, but it has since been deactivated due to them requesting
          closure of their account.
        </Paragraph>
        <Paragraph>
          <span className="font-bold">Deleted</span> - This status will show in
          red when the driver had been added & then deleted by the Admin in Site
          Management Service. For whatever reason, their access to the chargers
          has been taken away. Maybe they no longer live or work on premises.
        </Paragraph>
      </>
    ),
  },
];

const FaqsSection = ({ hash }: FaqsSectionProps) => (
  <section id="FAQs">
    <Heading.H3>FAQs</Heading.H3>
    <VerticalSpacer />
    {faqs.map((faq) => (
      <article
        id={`FAQs-${encodeURI(faq.title)}`}
        key={faq.title}
        className="mb-4"
      >
        <Accordion
          heading={faq.title}
          open={`FAQs-${encodeURI(faq.title)}` === hash}
        >
          <Paragraph className={'rounded-lg bg-white p-2 space-y-2'}>
            {faq.content}
          </Paragraph>
        </Accordion>
      </article>
    ))}
  </section>
);

export default FaqsSection;
