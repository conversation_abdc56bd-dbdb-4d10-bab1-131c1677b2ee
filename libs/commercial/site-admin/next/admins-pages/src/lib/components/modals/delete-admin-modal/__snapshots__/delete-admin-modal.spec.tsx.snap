// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DeleteAdminModal should match snapshot 1`] = `
<body>
  <div />
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
      <div>
        <div
          aria-label="Delete admin"
          aria-labelledby="headlessui-dialog-title-:test-id-8"
          aria-modal="true"
          class="relative z-10"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-dialog-:test-id-0"
          role="dialog"
          tabindex="-1"
        >
          <div
            aria-hidden="true"
            class="fixed inset-0 bg-neutral/75 duration-300 ease-out data-[closed]:opacity-0"
            data-headlessui-state="open"
            data-open=""
          />
          <div
            class="fixed inset-0 z-10 overflow-y-auto"
          >
            <div
              class="flex min-h-full items-end justify-center p-4 text-center sm:items-center"
            >
              <div
                class="relative rounded-lg bg-white p-4 text-left shadow-xl duration-300 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 transition-all w-96"
                data-headlessui-state="open"
                data-open=""
                id="headlessui-dialog-panel-:test-id-7"
              >
                <div
                  class="absolute top-0 right-0 pt-4 pr-4"
                >
                  <button
                    class="flex w-7 h-7 justify-center items-center rounded-full bg-neutral/20 hover:bg-neutral/30 text-neutral outline-hidden focus:ring-2 focus:ring-neutral/50"
                    type="button"
                  >
                    <svg
                      class="h-4 w-4 stroke-1 stroke-current fill-current"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Close
                      </title>
                      <g>
                        <g>
                          <line
                            x1="5.16"
                            x2="18.94"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                        <g>
                          <line
                            x1="18.94"
                            x2="5.16"
                            y1="5.11"
                            y2="18.89"
                          />
                          <path
                            d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
                <h2
                  class="pb-4 text-xxl"
                  data-headlessui-state="open"
                  data-open=""
                  id="headlessui-dialog-title-:test-id-8"
                >
                  Delete admin
                </h2>
                <div
                  class="pb-6"
                >
                  <p
                    class="text-md font-normal break-words"
                  >
                    Are you sure you want to delete the admin Joe
          Bloggs?
                  </p>
                  <p
                    class="text-xs font-normal mt-2 text-neutral break-words"
                  >
                    Please note this will only remove their admin access and there will be no change to their access to chargers.
                  </p>
                </div>
                <div
                  class="flex justify-between"
                >
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 mr-4"
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
                    type="submit"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-hidden="true"
        data-headlessui-focus-guard="true"
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
        type="button"
      />
    </div>
  </div>
</body>
`;
