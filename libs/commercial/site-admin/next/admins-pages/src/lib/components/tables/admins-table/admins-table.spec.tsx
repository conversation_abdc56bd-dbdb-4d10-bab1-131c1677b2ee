import {
  ANOTHER_TEST_ADMINISTRATOR,
  AdminStatus,
  TEST_ADMINISTRATOR,
  TEST_ADMIN_USER,
  TEST_DEACTIVATED_ADMINISTRATOR,
  TEST_PENDING_ADMINISTRATOR,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Toaster,
  useTableColumnFilterStore,
} from '@experience/shared/react/design-system';
import {
  getByText as domGetByText,
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import AdminsTable from './admins-table';
import axios from 'axios';
import dayjs from 'dayjs';
import userEvent from '@testing-library/user-event';

let mockSession = { user: { email: TEST_ADMIN_USER.email } };

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({ data: mockSession, status: 'authenticated' })),
}));

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

describe('AdminsTable', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <AdminsTable admins={[TEST_ADMINISTRATOR]} />
    );
    expect(baseElement).toBeTruthy();
  });

  it('should render snapshot', () => {
    const { baseElement } = render(
      <AdminsTable admins={[TEST_ADMINISTRATOR, ANOTHER_TEST_ADMINISTRATOR]} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a table caption', () => {
    render(<AdminsTable admins={[TEST_ADMINISTRATOR]} />);
    expect(screen.getByText('Table of administrators')).toBeInTheDocument();
  });

  it.each(['Name', 'Email address', 'Date registered', 'Status'])(
    'should render a table heading for %s',
    (heading) => {
      render(<AdminsTable admins={[TEST_ADMINISTRATOR]} />);
      expect(
        screen.getByRole('columnheader', { name: heading })
      ).toBeInTheDocument();
    }
  );

  it('should render the correct information for a table row', () => {
    render(<AdminsTable admins={[TEST_ADMINISTRATOR]} />);

    const row = screen.getByRole('cell', { name: 'Joe Bloggs' }).closest('tr');

    if (!row) fail('No first row found');

    expect(within(row).getByText('Joe Bloggs')).toBeInTheDocument();
    expect(
      within(row).getByText('<EMAIL>')
    ).toBeInTheDocument();
    expect(within(row).getByText('1st January 2020')).toBeInTheDocument();
    expect(within(row).getByText('Registered')).toBeInTheDocument();
  });

  it(`should show admins with a status of ${AdminStatus.PENDING}`, () => {
    render(
      <AdminsTable
        admins={[{ ...TEST_ADMINISTRATOR, status: AdminStatus.PENDING }]}
      />
    );

    const row = screen.getByRole('cell', { name: 'Joe Bloggs' }).closest('tr');

    if (!row) fail('No row found');

    expect(within(row).getByText('Pending')).toBeInTheDocument();
  });

  it(`should sort admins by status on load, then reorder on click asc then desc`, () => {
    render(
      <AdminsTable
        admins={[
          { ...TEST_ADMINISTRATOR, status: AdminStatus.PENDING },
          { ...TEST_ADMINISTRATOR, status: AdminStatus.REGISTERED },
          { ...TEST_ADMINISTRATOR, status: AdminStatus.DEACTIVATED },
        ]}
      />
    );

    const statuses = screen.getAllByText(/Deactivated|Pending|Registered/);

    expect(domGetByText(statuses[0], /Deactivated/)).toBeInTheDocument();
    expect(domGetByText(statuses[1], /Pending/)).toBeInTheDocument();
    expect(domGetByText(statuses[2], /Registered/)).toBeInTheDocument();

    const [thead, tbody] = screen.getAllByRole('rowgroup');

    fireEvent.click(within(thead).getByText('Status'));
    expect(within(tbody).getAllByRole('row')[0]).toHaveTextContent(
      /Deactivated/i
    );
    expect(within(tbody).getAllByRole('row')[1]).toHaveTextContent(/Pending/i);
    expect(within(tbody).getAllByRole('row')[2]).toHaveTextContent(
      /Registered/i
    );

    fireEvent.click(within(thead).getByText('Status'));
    expect(within(tbody).getAllByRole('row')[0]).toHaveTextContent(
      /Registered/i
    );
    expect(within(tbody).getAllByRole('row')[1]).toHaveTextContent(/Pending/i);
    expect(within(tbody).getAllByRole('row')[2]).toHaveTextContent(
      /Deactivated/i
    );
  });

  it(`should show sort admins descending by activated date`, () => {
    render(
      <AdminsTable
        admins={[
          { ...TEST_ADMINISTRATOR, activatedOn: new Date('2019-01-01') },
          { ...TEST_ADMINISTRATOR, activatedOn: new Date('2020-01-01') },
          { ...TEST_ADMINISTRATOR, activatedOn: new Date('2021-01-01') },
        ]}
      />
    );

    const statuses = screen.getAllByText(/January/);

    expect(domGetByText(statuses[0], /1st January 2021/)).toBeInTheDocument();
    expect(domGetByText(statuses[1], /1st January 2020/)).toBeInTheDocument();
    expect(domGetByText(statuses[2], /1st January 2019/)).toBeInTheDocument();
  });

  it(`should show sort admins descending by logged in date`, () => {
    render(
      <AdminsTable
        admins={[
          { ...TEST_ADMINISTRATOR, lastLogin: new Date('2019-02-01') },
          { ...TEST_ADMINISTRATOR, lastLogin: new Date('2020-02-01') },
          { ...TEST_ADMINISTRATOR, lastLogin: new Date('2021-02-01') },
        ]}
      />
    );

    const statuses = screen.getAllByText(/February/);

    expect(domGetByText(statuses[0], /1st February 2019/)).toBeInTheDocument();
    expect(domGetByText(statuses[1], /1st February 2020/)).toBeInTheDocument();
    expect(domGetByText(statuses[2], /1st February 2021/)).toBeInTheDocument();
  });

  it.each(['<EMAIL>', '<EMAIL>'])(
    'should disable the delete button for the current user (%s)',
    (email) => {
      mockSession = { user: { email } };

      render(
        <AdminsTable
          admins={[TEST_ADMINISTRATOR, ANOTHER_TEST_ADMINISTRATOR]}
        />
      );

      let row = screen.getByRole('cell', { name: 'Joe Bloggs' }).closest('tr');
      if (!row) fail('No row found');

      expect(
        within(row).getByRole('button', { name: 'Delete admin Joe Bloggs' })
      ).toBeInTheDocument();

      row = screen.getByRole('cell', { name: 'Jane Bloggs' }).closest('tr');
      if (!row) fail('No row found');

      expect(
        within(row).queryByRole('button', { name: 'Delete admin Jane Bloggs' })
      ).toBeDisabled();
    }
  );

  it.each([
    ['Name', 'Joe Bloggs', 'Joe Bloggs', 'Jane Bloggs'],
    [
      'Email address',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    [
      'Date registered',
      dayjs('2020-01-01').format('YYYY'),
      dayjs('2020-01-01').format('YYYY'),
      dayjs('2019-01-01').format('YYYY'),
    ],
    [
      'Last login',
      dayjs('2020-01-02').format('YYYY'),
      dayjs('2020-01-02').format('YYYY'),
      dayjs('2019-01-02').format('YYYY'),
    ],
  ])(
    'should reorder the rows to be ascending and then descending by %s',
    (columnHeader, initial, ascending, descending) => {
      render(
        <AdminsTable
          admins={[
            {
              ...TEST_ADMINISTRATOR,
              activatedOn: new Date('2020-01-01'),
              lastLogin: new Date('2020-01-02'),
            },
            {
              ...ANOTHER_TEST_ADMINISTRATOR,
              activatedOn: new Date('2019-01-01'),
              lastLogin: new Date('2019-01-02'),
            },
          ]}
        />
      );

      const [thead, tbody] = screen.getAllByRole('rowgroup');

      expect(within(tbody).getAllByRole('row')[0]).toHaveTextContent(initial);

      fireEvent.click(within(thead).getByText(columnHeader));
      expect(within(tbody).getAllByRole('row')[0]).toHaveTextContent(
        descending
      );

      fireEvent.click(within(thead).getByText(columnHeader));
      expect(within(tbody).getAllByRole('row')[0]).toHaveTextContent(ascending);
    }
  );

  it('should send an invitation email if the email icon is clicked', async () => {
    render(
      <>
        <Toaster />
        <AdminsTable admins={[TEST_ADMINISTRATOR]} />
      </>
    );

    const inviteButtonLabel = `Invite admin ${TEST_ADMINISTRATOR.firstName} ${TEST_ADMINISTRATOR.lastName}`;

    fireEvent.click(screen.getByLabelText(inviteButtonLabel));

    expect(screen.getByLabelText(inviteButtonLabel)).toBeDisabled();

    await waitFor(() => {
      expect(mockAxiosPost).toHaveBeenCalledTimes(1);
      expect(mockAxiosPost).toHaveBeenCalledWith('/api/admins/1/invitation', {
        email: TEST_ADMINISTRATOR.email,
      });
      expect(screen.getByLabelText(inviteButtonLabel)).not.toBeDisabled();
    });
  });

  it('should show send invitation email tooltip message on hover', async () => {
    render(
      <>
        <Toaster />
        <AdminsTable admins={[TEST_ADMINISTRATOR]} />
      </>
    );

    await userEvent.hover(screen.getAllByRole('tooltip')[0]);

    expect(
      await screen.findByText('Send invitation email')
    ).toBeInTheDocument();
  });

  it('should disable send invitation email if emails have bounced', async () => {
    render(
      <AdminsTable admins={[{ ...TEST_ADMINISTRATOR, emailBounced: true }]} />
    );

    const inviteButtonLabel = `Invite admin ${TEST_ADMINISTRATOR.firstName} ${TEST_ADMINISTRATOR.lastName}`;

    expect(screen.getByLabelText(inviteButtonLabel)).toBeDisabled();
  });

  it('should show send email bounced tooltip message on hover', async () => {
    render(
      <AdminsTable admins={[{ ...TEST_ADMINISTRATOR, emailBounced: true }]} />
    );

    await userEvent.hover(screen.getAllByRole('tooltip')[0]);

    expect(
      await screen.findByText(/Emails sent to this address have bounced/)
    ).toBeInTheDocument();
  });

  it('should search for admins by date in the format Do MMMM YYYY', async () => {
    render(<AdminsTable admins={[TEST_ADMINISTRATOR]} />);

    fireEvent.change(screen.getByLabelText('Search table'), {
      target: { value: '1st January 2020' },
    });

    await waitFor(() => {
      expect(screen.getByText('Showing 1 of 1 results.')).toBeInTheDocument();
      expect(screen.getByText(TEST_ADMINISTRATOR.email)).toBeInTheDocument();
    });
  });

  describe('Filters', () => {
    const initialStoreState = useTableColumnFilterStore.getState();
    const adminsList = {
      admins: [
        TEST_ADMINISTRATOR,
        TEST_PENDING_ADMINISTRATOR,
        TEST_DEACTIVATED_ADMINISTRATOR,
      ],
    };

    beforeEach(() => {
      useTableColumnFilterStore.setState(initialStoreState, true);
    });

    it.each([
      ['Registered', TEST_ADMINISTRATOR],
      ['Pending', TEST_PENDING_ADMINISTRATOR],
      ['Deactivated', TEST_DEACTIVATED_ADMINISTRATOR],
    ])(
      'should change selected option and show correct rows for the option %s for the status filter select',
      async (status, admin) => {
        render(<AdminsTable {...adminsList} />);

        const filterSelect = screen.getByLabelText('Status');
        fireEvent.click(filterSelect);

        await waitFor(() => {
          const option = screen.getByRole('option', { name: status });
          expect(option).toBeInTheDocument();
          fireEvent.click(option);
        });

        expect(within(filterSelect).queryByText(status)).toBeInTheDocument();
        expect(screen.queryByText(admin.email)).toBeInTheDocument();
        expect(screen.getAllByRole('row')).toHaveLength(2);
      }
    );

    it('should reset selected filter when All option is selected', async () => {
      render(<AdminsTable {...adminsList} />);

      fireEvent.click(screen.getByLabelText('Status'));

      await waitFor(() => {
        const option = screen.getByRole('option', {
          name: 'Pending',
        });
        expect(option).toBeInTheDocument();
        fireEvent.click(option);
      });

      expect(
        screen.queryByText(TEST_ADMINISTRATOR.email)
      ).not.toBeInTheDocument();

      fireEvent.click(screen.getByLabelText('Status'));

      await waitFor(() => {
        const option = screen.getByRole('option', { name: 'All' });
        expect(option).toBeInTheDocument();
        fireEvent.click(option);
      });

      expect(screen.queryByText(TEST_ADMINISTRATOR.email)).toBeInTheDocument();
    });

    it('should preselect filter based on state', async () => {
      useTableColumnFilterStore.setState(
        {
          ...initialStoreState,
          tableFilters: {
            'admins-table': [
              {
                id: 'status',
                value: AdminStatus.REGISTERED,
              },
            ],
          },
        },
        true
      );
      render(<AdminsTable {...adminsList} />);

      const selectField = screen.getByLabelText('Status');
      expect(within(selectField).getByText('Registered')).toBeInTheDocument();
    });

    it('should reset all filters when reset button is clicked', async () => {
      render(<AdminsTable {...adminsList} />);

      fireEvent.click(screen.getByLabelText('Status'));

      await waitFor(() => {
        const option = screen.getByRole('option', {
          name: 'Pending',
        });
        expect(option).toBeInTheDocument();
        fireEvent.click(option);
      });

      expect(
        screen.queryByText(TEST_ADMINISTRATOR.email)
      ).not.toBeInTheDocument();

      const resetButton = screen.getByText('Clear');

      await waitFor(() => {
        fireEvent.click(resetButton);
        expect(
          screen.queryByText(TEST_ADMINISTRATOR.email)
        ).toBeInTheDocument();
      });
    });
  });
});
