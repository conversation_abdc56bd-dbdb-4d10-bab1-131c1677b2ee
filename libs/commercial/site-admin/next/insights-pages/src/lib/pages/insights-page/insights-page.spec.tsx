import { FlagsProvider } from 'flagged';
import {
  TEST_POD,
  TEST_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_USAGE_RESPONSE } from '@experience/shared/axios/data-platform-api-client/fixtures';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import InsightsPage from './insights-page';
import useSWR, { SWRResponse } from 'swr';
import userEvent from '@testing-library/user-event';

jest.mock('swr');
const mockedUseSWR = jest.mocked(useSWR);

describe('InsightsPage', () => {
  beforeEach(() => {
    mockedUseSWR.mockImplementation((key) => {
      if (key === '/api/sites') {
        return { data: [TEST_SITE], error: null } as SWRResponse;
      }
      if (key === '/api/insights') {
        return { data: TEST_USAGE_RESPONSE.usage, error: null } as SWRResponse;
      }
      return { data: [], error: null } as SWRResponse;
    });
  });

  it('should be defined', () => {
    const { baseElement } = render(<InsightsPage />);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<InsightsPage />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should call insights api endpoint on first load and then if no site is selected', async () => {
    render(<InsightsPage />);

    expect(useSWR).toHaveBeenCalledWith('/api/insights');

    fireEvent.click(screen.getByText('Select data'));

    expect(useSWR).toHaveBeenCalledWith('/api/sites');

    const [chargerInput] = screen.getAllByPlaceholderText('All');

    expect(chargerInput).toBeInTheDocument();

    fireEvent.click(screen.getByText('Apply'));

    await waitFor(() => {
      expect(useSWR).toHaveBeenCalledWith('/api/insights');
      expect(useSWR).not.toHaveBeenCalledWith(
        `/api/insights/sites/${TEST_SITE.id}`
      );
      expect(useSWR).not.toHaveBeenCalledWith(
        `/api/insights/chargers/${TEST_POD.id}`
      );
    });
  });

  it('should call insights api endpoint with year parameter when year selected', async () => {
    render(<InsightsPage />);

    expect(useSWR).toHaveBeenCalledWith('/api/insights');

    fireEvent.click(screen.getByText('Select data'));

    expect(useSWR).toHaveBeenCalledWith('/api/sites');

    fireEvent.click(screen.getByLabelText('Year'));
    fireEvent.click(screen.getByRole('option', { name: '2021' }));
    fireEvent.click(screen.getByText('Apply'));

    await waitFor(() => {
      expect(useSWR).toHaveBeenCalledWith('/api/insights?year=2021');
    });
  });

  it('should call insights sites api endpoint when site is selected', async () => {
    render(<InsightsPage />);

    expect(useSWR).toHaveBeenCalledWith('/api/insights');

    fireEvent.click(screen.getByText('Select data'));

    expect(useSWR).toHaveBeenCalledWith('/api/sites');

    const siteSelect = screen.getByLabelText('Site');
    fireEvent.change(siteSelect, { target: { value: 'discovery' } });
    await userEvent.click(
      screen.getByRole('option', { name: 'Discovery House' })
    );
    fireEvent.click(screen.getByText('Apply'));

    await waitFor(() => {
      expect(useSWR).toHaveBeenCalledWith(
        `/api/insights/sites/${TEST_SITE.id}`
      );
      expect(useSWR).not.toHaveBeenCalledWith(
        `/api/insights/chargers/${TEST_POD.id}`
      );
    });
  });

  it.each([
    [false, TEST_POD.id],
    [true, TEST_POD.ppid],
  ])(
    'should call insights chargers api endpoint when charger is selected and flag is %s',
    async (useChargeProjectionEndpoints, chargerId) => {
      render(
        <FlagsProvider features={{ useChargeProjectionEndpoints }}>
          <InsightsPage />
        </FlagsProvider>
      );

      expect(useSWR).toHaveBeenCalledWith('/api/insights');

      fireEvent.click(screen.getByText('Select data'));

      expect(useSWR).toHaveBeenCalledWith('/api/sites');

      const siteSelect = screen.getByLabelText('Site');
      fireEvent.change(siteSelect, { target: { value: 'discovery' } });
      await userEvent.click(
        screen.getByRole('option', { name: 'Discovery House' })
      );

      const chargerSelect = screen.getByLabelText('Charger');
      fireEvent.change(chargerSelect, { target: { value: 'fo' } });
      await userEvent.click(screen.getByRole('option', { name: 'FOO' }));
      fireEvent.click(screen.getByText('Apply'));

      await waitFor(() => {
        expect(useSWR).toHaveBeenCalledWith(
          `/api/insights/chargers/${chargerId}`
        );
      });
    }
  );

  it('it should render error message if the call to the API fails', () => {
    mockedUseSWR.mockImplementation(
      () =>
        ({
          data: null,
          error: true,
        } as SWRResponse)
    );

    render(<InsightsPage />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('should open a new tab with the csv download url if no filters are selected', () => {
    jest.spyOn(window, 'open').mockImplementation(() => null);

    render(<InsightsPage />);

    fireEvent.click(screen.getByRole('button', { name: 'Download CSV' }));

    expect(window.open).toHaveBeenCalledWith('/api/insights/csv', '_blank');
  });

  it('should open a new tab with the csv download url with year if only year is selected', async () => {
    jest.spyOn(window, 'open').mockImplementation(() => null);

    render(<InsightsPage />);

    fireEvent.click(screen.getByRole('button', { name: 'Select data' }));

    fireEvent.click(screen.getByLabelText('Year'));
    await waitFor(() => {
      const option = screen.getByRole('option', { name: '2020' });
      expect(option).toBeInTheDocument();
      fireEvent.click(option);
    });
    fireEvent.click(screen.getByRole('button', { name: 'Apply' }));

    fireEvent.click(
      await screen.findByRole('button', { name: 'Download CSV' })
    );
    expect(window.open).toHaveBeenCalledWith(
      '/api/insights/csv?year=2020',
      '_blank'
    );
  });

  it('should open a new tab with the csv download url for groups with site if site is selected', async () => {
    jest.spyOn(window, 'open').mockImplementation(() => null);

    render(<InsightsPage />);

    fireEvent.click(screen.getByRole('button', { name: 'Select data' }));

    fireEvent.change(screen.getByLabelText('Site'), {
      target: { value: 'discovery' },
    });
    const option = screen.getByRole('option', { name: 'Discovery House' });
    expect(option).toBeInTheDocument();
    await userEvent.click(option);

    fireEvent.click(screen.getByRole('button', { name: 'Apply' }));
    fireEvent.click(
      await screen.findByRole('button', { name: 'Download CSV' })
    );
    expect(window.open).toHaveBeenCalledWith(
      `/api/insights/sites/${TEST_SITE.id}/csv`,
      '_blank'
    );
  });

  it('should open a new tab with the csv download url for groups with site and year if both are selected', async () => {
    jest.spyOn(window, 'open').mockImplementation(() => null);

    render(<InsightsPage />);

    fireEvent.click(screen.getByRole('button', { name: 'Select data' }));

    fireEvent.change(screen.getByLabelText('Site'), {
      target: { value: 'discovery' },
    });
    const option = screen.getByRole('option', { name: 'Discovery House' });
    expect(option).toBeInTheDocument();
    await userEvent.click(option);

    await userEvent.click(screen.getByLabelText('Year'));
    await waitFor(() => {
      const option = screen.getByRole('option', { name: '2020' });
      expect(option).toBeInTheDocument();
      fireEvent.click(option);
    });

    fireEvent.click(screen.getByRole('button', { name: 'Apply' }));

    fireEvent.click(
      await screen.findByRole('button', { name: 'Download CSV' })
    );
    expect(window.open).toHaveBeenCalledWith(
      `/api/insights/sites/${TEST_SITE.id}/csv?year=2020`,
      '_blank'
    );
  });

  it.each([
    [false, TEST_POD.id],
    [true, TEST_POD.ppid],
  ])(
    'should open a new tab with the csv download url for groups with charger if charger is selected and flag is %s',
    async (useChargeProjectionEndpoints, chargerId) => {
      jest.spyOn(window, 'open').mockImplementation(() => null);

      render(
        <FlagsProvider features={{ useChargeProjectionEndpoints }}>
          <InsightsPage />
        </FlagsProvider>
      );

      fireEvent.click(screen.getByRole('button', { name: 'Select data' }));

      fireEvent.change(screen.getByLabelText('Site'), {
        target: { value: 'discovery' },
      });
      const siteOption = screen.getByRole('option', {
        name: 'Discovery House',
      });
      expect(siteOption).toBeInTheDocument();
      await userEvent.click(siteOption);

      fireEvent.change(screen.getByLabelText('Charger'), {
        target: { value: 'fo' },
      });
      const chargerOption = screen.getByRole('option', { name: 'FOO' });
      expect(chargerOption).toBeInTheDocument();
      await userEvent.click(chargerOption);

      fireEvent.click(screen.getByRole('button', { name: 'Apply' }));
      fireEvent.click(
        await screen.findByRole('button', { name: 'Download CSV' })
      );
      expect(window.open).toHaveBeenCalledWith(
        `/api/insights/chargers/${chargerId}/csv`,
        '_blank'
      );
    }
  );

  it('should open a new tab with the csv download url for groups with charger and year if both are selected', async () => {
    jest.spyOn(window, 'open').mockImplementation(() => null);

    render(<InsightsPage />);

    fireEvent.click(screen.getByRole('button', { name: 'Select data' }));

    fireEvent.change(screen.getByLabelText('Site'), {
      target: { value: 'discovery' },
    });
    const siteOption = screen.getByRole('option', { name: 'Discovery House' });
    expect(siteOption).toBeInTheDocument();
    await userEvent.click(siteOption);

    fireEvent.change(screen.getByLabelText('Charger'), {
      target: { value: 'fo' },
    });
    const chargerOption = screen.getByRole('option', { name: 'FOO' });
    expect(chargerOption).toBeInTheDocument();
    await userEvent.click(chargerOption);

    fireEvent.click(screen.getByLabelText('Year'));
    const option = screen.getByRole('option', { name: '2020' });
    expect(option).toBeInTheDocument();
    fireEvent.click(option);

    fireEvent.click(screen.getByRole('button', { name: 'Apply' }));
    fireEvent.click(
      await screen.findByRole('button', { name: 'Download CSV' })
    );
    expect(window.open).toHaveBeenCalledWith(
      `/api/insights/chargers/${TEST_POD.id}/csv?year=2020`,
      '_blank'
    );
  });
});
