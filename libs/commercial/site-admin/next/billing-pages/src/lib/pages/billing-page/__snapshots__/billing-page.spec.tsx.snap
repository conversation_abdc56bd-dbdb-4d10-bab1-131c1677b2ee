// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BillingPage should match snapshot  1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Billing
        </h1>
      </div>
    </header>
    <div
      class="w-full"
    >
      <div>
        <div
          aria-orientation="horizontal"
          class="flex flex-row w-full space-x-4 mb-4"
          role="tablist"
        >
          <button
            aria-controls="headlessui-tabs-panel-:test-id-6"
            aria-selected="true"
            class="disabled:cursor-not-allowed px-2 font-semibold border-b outline-hidden text-info border-b-info hover:text-info cursor-default"
            data-headlessui-state="selected"
            data-selected=""
            id="headlessui-tabs-tab-:test-id-0"
            role="tab"
            tabindex="0"
            type="button"
          >
            Statements
          </button>
          <button
            aria-controls="headlessui-tabs-panel-:test-id-8"
            aria-selected="false"
            class="cursor-pointer disabled:cursor-not-allowed px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden"
            data-headlessui-state=""
            id="headlessui-tabs-tab-:test-id-2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            Subscription
          </button>
          <button
            aria-controls="headlessui-tabs-panel-:test-id-10"
            aria-selected="false"
            class="cursor-pointer disabled:cursor-not-allowed px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden"
            data-headlessui-state=""
            id="headlessui-tabs-tab-:test-id-4"
            role="tab"
            tabindex="-1"
            type="button"
          >
            Business details
          </button>
        </div>
        <div
          class="mt-2"
        >
          <div
            aria-labelledby="headlessui-tabs-tab-:test-id-0"
            data-headlessui-state="selected"
            data-selected=""
            id="headlessui-tabs-panel-:test-id-6"
            role="tabpanel"
            tabindex="0"
          >
            <div
              class="flex"
            >
              <div
                class="flex items-center mb-4 space-x-4"
              >
                <div
                  class="items-center print:hidden self-end"
                >
                  <div
                    class="relative"
                  >
                    <span
                      class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
                    >
                      <svg
                        class="fill-current h-4 w-4"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g>
                          <path
                            d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                          />
                        </g>
                      </svg>
                    </span>
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="table-search"
                      >
                        Search
                      </label>
                    </div>
                    <input
                      aria-label="Search table"
                      class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
                      id="table-search"
                      placeholder="Search"
                      role="searchbox"
                      value=""
                    />
                    <button
                      aria-label="Clear search"
                      class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral focus:outline-hidden"
                      name="Clear search"
                    >
                      <svg
                        class="h-3 w-3 stroke-1 stroke-current fill-current"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g>
                          <g>
                            <line
                              x1="5.16"
                              x2="18.94"
                              y1="5.11"
                              y2="18.89"
                            />
                            <path
                              d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                            />
                          </g>
                          <g>
                            <line
                              x1="18.94"
                              x2="5.16"
                              y1="5.11"
                              y2="18.89"
                            />
                            <path
                              d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                            />
                          </g>
                        </g>
                      </svg>
                    </button>
                  </div>
                </div>
                <button
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
                  type="button"
                >
                  Clear
                </button>
              </div>
            </div>
            <div
              class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
            >
              <div
                class="inline-block min-w-full -my-2 py-2 align-middle"
              >
                <table
                  class="min-w-full"
                  tabindex="0"
                >
                  <caption
                    class="sr-only"
                  >
                    Table of billing statements
                  </caption>
                  <thead
                    class="border-b border-b-neutral/20"
                  >
                    <tr
                      class="border-b-neutral/20"
                    >
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Site
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Month
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          >
                            <svg
                              class="fill-current h-4 w-4 stroke-current stroke-2"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                transform="translate(4 4)"
                              >
                                <path
                                  d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Fee invoice number
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Fee invoice status
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Revenue payout status
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    class="divide-y border-b border-b-neutral/20"
                  >
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Site 1
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1002
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="overdue"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                          role="status"
                        >
                          overdue
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="transferred"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          transferred
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/369b1302-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Site 1 for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/9be0da46-1462-42b5-85b1-d43cf64f339e/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Site 1 for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Banner Street
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1003
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid out"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid out
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/f7c7552c-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/ff407cc1-6898-4559-a220-ae66acb3f391/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Banner Street
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1004
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="warning"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                          role="status"
                        >
                          warning
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/f7c7552c-92b9-11ee-b9d1-0242ac121234/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/ff407cc1-6898-4559-a220-ae66acb3f111/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Site 1
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        January 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1001
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="open"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                          role="status"
                        >
                          open
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="pending"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                          role="status"
                        >
                          pending
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/9bd247e0-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Site 1 for month 2023-01-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/5ab6cbf4-9902-4942-a052-b6a62bcdbaac/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Site 1 for month 2023-01-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <span
            aria-hidden="true"
            aria-labelledby="headlessui-tabs-tab-:test-id-2"
            id="headlessui-tabs-panel-:test-id-8"
            role="tabpanel"
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
            tabindex="-1"
          />
          <span
            aria-hidden="true"
            aria-labelledby="headlessui-tabs-tab-:test-id-4"
            id="headlessui-tabs-panel-:test-id-10"
            role="tabpanel"
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
            tabindex="-1"
          />
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`BillingPage should match snapshot when onboarding link is present 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Billing
        </h1>
      </div>
    </header>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Connect revenue payout account
      </h3>
      <p
        class="text-md font-normal break-words"
      >
        For us to send your revenue payouts you must complete onboarding. Follow the unique link to complete the onboarding process which must be completed by the business owner or someone with significant management responsibility.
      </p>
      <div
        class="flex mt-4"
      >
        <div
          class=""
        >
          <label
            class="text-md font-bold sr-only block mb-2"
            for="account-onboarding-link"
          >
            Connected account onboarding link
          </label>
        </div>
        <input
          class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-1/4"
          disabled=""
          id="account-onboarding-link"
          name="account-onboarding-link"
          value="https://connect.stripe.com/"
        />
        <div
          role="tooltip"
          tabindex="0"
        >
          <button
            aria-label="Copy link"
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75"
            type="button"
          >
            <svg
              class="fill-current h-6 w-6"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <defs>
                <style>
                  .cls-1 
                </style>
              </defs>
              <path
                d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="w-full"
    >
      <div>
        <div
          aria-orientation="horizontal"
          class="flex flex-row w-full space-x-4 mb-4"
          role="tablist"
        >
          <button
            aria-controls="headlessui-tabs-panel-:test-id-8"
            aria-selected="true"
            class="disabled:cursor-not-allowed px-2 font-semibold border-b outline-hidden text-info border-b-info hover:text-info cursor-default"
            data-headlessui-state="selected"
            data-selected=""
            id="headlessui-tabs-tab-:test-id-2"
            role="tab"
            tabindex="0"
            type="button"
          >
            Statements
          </button>
          <button
            aria-controls="headlessui-tabs-panel-:test-id-10"
            aria-selected="false"
            class="cursor-pointer disabled:cursor-not-allowed px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden"
            data-headlessui-state=""
            id="headlessui-tabs-tab-:test-id-4"
            role="tab"
            tabindex="-1"
            type="button"
          >
            Subscription
          </button>
          <button
            aria-controls="headlessui-tabs-panel-:test-id-12"
            aria-selected="false"
            class="cursor-pointer disabled:cursor-not-allowed px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden"
            data-headlessui-state=""
            id="headlessui-tabs-tab-:test-id-6"
            role="tab"
            tabindex="-1"
            type="button"
          >
            Business details
          </button>
        </div>
        <div
          class="mt-2"
        >
          <div
            aria-labelledby="headlessui-tabs-tab-:test-id-2"
            data-headlessui-state="selected"
            data-selected=""
            id="headlessui-tabs-panel-:test-id-8"
            role="tabpanel"
            tabindex="0"
          >
            <div
              class="flex"
            >
              <div
                class="flex items-center mb-4 space-x-4"
              >
                <div
                  class="items-center print:hidden self-end"
                >
                  <div
                    class="relative"
                  >
                    <span
                      class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
                    >
                      <svg
                        class="fill-current h-4 w-4"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g>
                          <path
                            d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                          />
                        </g>
                      </svg>
                    </span>
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="table-search"
                      >
                        Search
                      </label>
                    </div>
                    <input
                      aria-label="Search table"
                      class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
                      id="table-search"
                      placeholder="Search"
                      role="searchbox"
                      value=""
                    />
                    <button
                      aria-label="Clear search"
                      class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral focus:outline-hidden"
                      name="Clear search"
                    >
                      <svg
                        class="h-3 w-3 stroke-1 stroke-current fill-current"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g>
                          <g>
                            <line
                              x1="5.16"
                              x2="18.94"
                              y1="5.11"
                              y2="18.89"
                            />
                            <path
                              d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                            />
                          </g>
                          <g>
                            <line
                              x1="18.94"
                              x2="5.16"
                              y1="5.11"
                              y2="18.89"
                            />
                            <path
                              d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                            />
                          </g>
                        </g>
                      </svg>
                    </button>
                  </div>
                </div>
                <button
                  class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
                  type="button"
                >
                  Clear
                </button>
              </div>
            </div>
            <div
              class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
            >
              <div
                class="inline-block min-w-full -my-2 py-2 align-middle"
              >
                <table
                  class="min-w-full"
                  tabindex="0"
                >
                  <caption
                    class="sr-only"
                  >
                    Table of billing statements
                  </caption>
                  <thead
                    class="border-b border-b-neutral/20"
                  >
                    <tr
                      class="border-b-neutral/20"
                    >
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Site
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Month
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          >
                            <svg
                              class="fill-current h-4 w-4 stroke-current stroke-2"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                transform="translate(4 4)"
                              >
                                <path
                                  d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Fee invoice number
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Fee invoice status
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        <button
                          class="flex items-center hover:underline text-left cursor-pointer"
                          type="button"
                        >
                          Revenue payout status
                          <span
                            class="w-4 h-4 ml-1 text-neutral shrink-0"
                          />
                        </button>
                      </th>
                      <th
                        class="text-left p-3 align-top"
                        scope="col"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    class="divide-y border-b border-b-neutral/20"
                  >
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Site 1
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1002
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="overdue"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                          role="status"
                        >
                          overdue
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="transferred"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          transferred
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/369b1302-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Site 1 for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/9be0da46-1462-42b5-85b1-d43cf64f339e/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Site 1 for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Banner Street
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1003
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid out"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid out
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/f7c7552c-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/ff407cc1-6898-4559-a220-ae66acb3f391/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Banner Street
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        February 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1004
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="paid"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                          role="status"
                        >
                          paid
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="warning"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                          role="status"
                        >
                          warning
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/f7c7552c-92b9-11ee-b9d1-0242ac121234/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/ff407cc1-6898-4559-a220-ae66acb3f111/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Banner Street for month 2023-02-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                    <tr
                      class="border-b-neutral/20"
                    >
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        Site 1
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        January 2023
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        ADF-1001
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="open"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                          role="status"
                        >
                          open
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <span
                          aria-label="pending"
                          class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                          role="status"
                        >
                          pending
                        </span>
                      </td>
                      <td
                        class="whitespace-normal px-3 py-4"
                      >
                        <div
                          class="flex space-x-3 items-center"
                        >
                          <a
                            href="/api/billing/statements/9bd247e0-92b9-11ee-b9d1-0242ac120002/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Statement for Site 1 for month 2023-01-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Statement
                            </button>
                          </a>
                          <a
                            href="/api/billing/invoices/5ab6cbf4-9902-4942-a052-b6a62bcdbaac/pdf"
                            rel="noreferrer"
                            target="_blank"
                          >
                            <button
                              aria-label="Fee invoice for Site 1 for month 2023-01-13"
                              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75 flex items-center gap-2"
                              type="button"
                            >
                              <svg
                                class="fill-current h-4 w-4"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <g
                                  fill="none"
                                >
                                  <path
                                    d="M5 11L12 18L19 11"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M12 2L12 18"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                  <path
                                    d="M21 22L4 22"
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                  />
                                </g>
                              </svg>
                              Fee invoice
                            </button>
                          </a>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <span
            aria-hidden="true"
            aria-labelledby="headlessui-tabs-tab-:test-id-4"
            id="headlessui-tabs-panel-:test-id-10"
            role="tabpanel"
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
            tabindex="-1"
          />
          <span
            aria-hidden="true"
            aria-labelledby="headlessui-tabs-tab-:test-id-6"
            id="headlessui-tabs-panel-:test-id-12"
            role="tabpanel"
            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px;"
            tabindex="-1"
          />
        </div>
      </div>
    </div>
  </div>
</body>
`;
