// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HomePage should match snapshot for the site service with additional title text 1`] = `
<body
  class=""
>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Home
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          Welcome to the 
          prototype
           Site Management Service.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="space-x-2"
        >
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download aggregated CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
    <h2
      class="text-2xl"
    >
      This month in review
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £4,576.00
            in revenue from 16 sites
            and delivered 567.89 kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              567.89
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              4,325.62
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £4,576.00
        </span>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £3,245.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Usage
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      64 customers used your
        chargers resulting in
        2048 charges
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charges
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              2,048
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Total charge duration
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          <span
            class="text-5xl leading-normal font-medium"
          >
            371
          </span>
          <span
            class="ml-3 text-neutral-400"
          >
            minutes
          </span>
        </p>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Unique drivers*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              64
            </span>
          </p>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Unique drivers are only counted where a charge has been claimed. Unclaimed charges will not contribute to this total. Please contact us if you have any questions regarding this information.
    </p>
  </div>
</body>
`;

exports[`HomePage should match snapshot with information banners 1`] = `
<body
  class="driver-active driver-fade"
>
  <div
    aria-controls="driver-popover-content"
    aria-expanded="true"
    aria-haspopup="dialog"
    class="driver-no-interaction driver-active-element"
    id="driver-dummy-element"
    style="width: 0px; height: 0px; pointer-events: none; opacity: 0; position: fixed; top: 50%; left: 50%;"
  />
  <div>
    <div
      class="flex py-1"
    >
      <div
        class="flex-1"
      >
        <div
          class="rounded-md p-3 bg-info/10 border-2 border-info/30"
        >
          <div
            class="flex"
          >
            <div
              class="shrink-0"
            >
              <span
                class="text-info"
              >
                <svg
                  class="fill-current w-6 h-6"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </span>
            </div>
            <div
              class="flex flex-col justify-center ml-4"
            >
              <p
                class="text-md font-normal sr-only break-words"
              >
                Alert
              </p>
              <div
                class="text-info"
              >
                <p
                  class="text-lg font-normal break-words"
                >
                  testing 456
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex py-1"
    >
      <div
        class="flex-1"
      >
        <div
          class="rounded-md p-3 bg-warning/10 border-2 border-warning/30"
        >
          <div
            class="flex"
          >
            <div
              class="shrink-0"
            >
              <span
                class="text-warning"
              >
                <svg
                  class="fill-current w-6 h-6"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </span>
            </div>
            <div
              class="flex flex-col justify-center ml-4"
            >
              <p
                class="text-md font-normal sr-only break-words"
              >
                Alert
              </p>
              <div
                class="text-warning"
              >
                <p
                  class="text-lg font-normal break-words"
                >
                  testing 123
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Home
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          Welcome to the 
           Site Management Service.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="space-x-2"
        >
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download aggregated CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
    <h2
      class="text-2xl"
    >
      This month in review
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £4,576.00
            in revenue from 16 sites
            and delivered 567.89 kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              567.89
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              4,325.62
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £4,576.00
        </span>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £3,245.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Usage
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      64 customers used your
        chargers resulting in
        2048 charges
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charges
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              2,048
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Total charge duration
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          <span
            class="text-5xl leading-normal font-medium"
          >
            371
          </span>
          <span
            class="ml-3 text-neutral-400"
          >
            minutes
          </span>
        </p>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Unique drivers*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              64
            </span>
          </p>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Unique drivers are only counted where a charge has been claimed. Unclaimed charges will not contribute to this total. Please contact us if you have any questions regarding this information.
    </p>
  </div>
  <div
    aria-describedby="driver-popover-description"
    aria-labelledby="driver-popover-title"
    class="driver-popover"
    id="driver-popover-content"
    role="dialog"
    style="display: block; left: 512px; top: 384px;"
  >
    <button
      aria-label="Close"
      class="driver-popover-close-btn"
      style="display: block;"
      type="button"
    >
      ×
    </button>
    <div
      class="driver-popover-arrow driver-popover-arrow-side-over driver-popover-arrow-align-start"
    />
    <header
      class="driver-popover-title"
      id="driver-popover-title"
      style="display: none;"
    />
    <div
      class="driver-popover-description"
      id="driver-popover-description"
      style="display: block;"
    >
      <p
        class="mb-2"
      >
        Welcome to the Site Management Service.
      </p>
      
            
      <p>
        This quick tour will introduce each page in the service.
      </p>
    </div>
    <footer
      class="driver-popover-footer"
      style="display: flex;"
    >
      <span
        class="driver-popover-progress-text"
        style="display: block;"
      >
        1 of 9
      </span>
      <span
        class="driver-popover-navigation-btns"
      >
        <button
          class="driver-popover-prev-btn driver-popover-btn-disabled"
          disabled=""
          style="display: none;"
          type="button"
        >
          ← Previous
        </button>
        <button
          class="driver-popover-next-btn"
          style="display: block;"
          type="button"
        >
          Next →
        </button>
      </span>
    </footer>
  </div>
</body>
`;

exports[`HomePage should match snapshot with navigation tour not showing when the cookie is set to true 1`] = `
<body
  class=""
>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Home
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          Welcome to the 
           Site Management Service.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="space-x-2"
        >
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download aggregated CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
    <h2
      class="text-2xl"
    >
      This month in review
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £4,576.00
            in revenue from 16 sites
            and delivered 567.89 kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              567.89
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              4,325.62
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £4,576.00
        </span>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £3,245.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Usage
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      64 customers used your
        chargers resulting in
        2048 charges
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charges
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              2,048
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Total charge duration
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          <span
            class="text-5xl leading-normal font-medium"
          >
            371
          </span>
          <span
            class="ml-3 text-neutral-400"
          >
            minutes
          </span>
        </p>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Unique drivers*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              64
            </span>
          </p>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Unique drivers are only counted where a charge has been claimed. Unclaimed charges will not contribute to this total. Please contact us if you have any questions regarding this information.
    </p>
  </div>
</body>
`;

exports[`HomePage should match snapshot with navigation tour showing when the cookie is not set 1`] = `
<body
  class="driver-active driver-fade"
>
  <div
    aria-controls="driver-popover-content"
    aria-expanded="true"
    aria-haspopup="dialog"
    class="driver-no-interaction driver-active-element"
    id="driver-dummy-element"
    style="width: 0px; height: 0px; pointer-events: none; opacity: 0; position: fixed; top: 50%; left: 50%;"
  />
  <svg
    class="driver-overlay driver-overlay-animated"
    preserveAspectRatio="xMinYMin slice"
    style="fill-rule: evenodd; clip-rule: evenodd; stroke-linejoin: round; stroke-miterlimit: 2; z-index: 10000; position: fixed; top: 0px; left: 0px; width: 100%; height: 100%;"
    version="1.1"
    viewBox="0 0 1024 768"
    xmlSpace="preserve"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <path
      d="M1024,0L0,0L0,768L1024,768L1024,0Z
    M-5,-10 h10 a5,5 0 0 1 5,5 v10 a5,5 0 0 1 -5,5 h-10 a5,5 0 0 1 -5,-5 v-10 a5,5 0 0 1 5,-5 z"
      style="fill: #000; opacity: 0.7; pointer-events: auto; cursor: auto;"
    />
  </svg>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Home
        </h1>
      </div>
      <div
        class="flex items-center"
      >
        <p
          class="text-md font-normal break-words"
        >
          Welcome to the 
           Site Management Service.
        </p>
      </div>
      <div
        class="flex justify-end items-center print:hidden"
      >
        <div
          class="space-x-2"
        >
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="relative inline-block text-left"
            data-headlessui-state=""
          >
            <button
              aria-expanded="false"
              aria-haspopup="menu"
              class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
              data-headlessui-state=""
              id="headlessui-menu-button-test-id"
              type="button"
            >
              <span>
                Download aggregated CSV
              </span>
              <svg
                class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
    <h2
      class="text-2xl"
    >
      This month in review
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £4,576.00
            in revenue from 16 sites
            and delivered 567.89 kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              567.89
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              4,325.62
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £4,576.00
        </span>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £3,245.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Usage
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      64 customers used your
        chargers resulting in
        2048 charges
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charges
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              2,048
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Total charge duration
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          <span
            class="text-5xl leading-normal font-medium"
          >
            371
          </span>
          <span
            class="ml-3 text-neutral-400"
          >
            minutes
          </span>
        </p>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Unique drivers*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              64
            </span>
          </p>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Unique drivers are only counted where a charge has been claimed. Unclaimed charges will not contribute to this total. Please contact us if you have any questions regarding this information.
    </p>
  </div>
  <div
    aria-describedby="driver-popover-description"
    aria-labelledby="driver-popover-title"
    class="driver-popover"
    id="driver-popover-content"
    role="dialog"
    style="display: block; left: 512px; top: 384px;"
  >
    <button
      aria-label="Close"
      class="driver-popover-close-btn"
      style="display: block;"
      type="button"
    >
      ×
    </button>
    <div
      class="driver-popover-arrow driver-popover-arrow-side-over driver-popover-arrow-align-start"
    />
    <header
      class="driver-popover-title"
      id="driver-popover-title"
      style="display: none;"
    />
    <div
      class="driver-popover-description"
      id="driver-popover-description"
      style="display: block;"
    >
      <p
        class="mb-2"
      >
        Welcome to the Site Management Service.
      </p>
      
            
      <p>
        This quick tour will introduce each page in the service.
      </p>
    </div>
    <footer
      class="driver-popover-footer"
      style="display: flex;"
    >
      <span
        class="driver-popover-progress-text"
        style="display: block;"
      >
        1 of 9
      </span>
      <span
        class="driver-popover-navigation-btns"
      >
        <button
          class="driver-popover-prev-btn driver-popover-btn-disabled"
          disabled=""
          style="display: none;"
          type="button"
        >
          ← Previous
        </button>
        <button
          class="driver-popover-next-btn"
          style="display: block;"
          type="button"
        >
          Next →
        </button>
      </span>
    </footer>
  </div>
</body>
`;
