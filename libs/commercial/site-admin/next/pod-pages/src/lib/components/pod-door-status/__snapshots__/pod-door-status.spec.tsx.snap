// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PodDoorStatus should match snapshot when socket firmware is unknown 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="2xl:flex justify-between mb-3"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Socket status
        </h3>
        <div
          class="space-x-2"
        >
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Event log
          </button>
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Report an issue
          </button>
        </div>
      </div>
      <div
        class="bg-neutral/10 rounded-lg p-4 mb-4"
      >
        <div
          class="flex my-auto"
        >
          <div
            class="pr-7"
          >
            <h5
              class="mb-2 text-6xl text-neutral font-bold"
            >
              A
            </h5>
            <p
              class="text-md font-semibold break-words"
            >
              Last known status
            </p>
            <div
              class="flex items-center"
            >
              <svg
                aria-hidden="true"
                class="fill-current h-4 w-4 mr-2 text-sky"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="12"
                />
              </svg>
              Available
            </div>
          </div>
          <div>
            <p
              class="text-md font-semibold break-words"
            >
              Last contact
            </p>
            <p
              class="text-md font-normal break-words"
            >
              12 Aug 2022 - 09:18
            </p>
            <div
              class="pb-4"
            />
            <p
              class="text-md font-semibold break-words"
            >
              Firmware version
            </p>
            <p
              class="text-md font-normal mb-2 break-words"
            >
              Unknown
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
`;

exports[`PodDoorStatus should match snapshot when socket firmware out of date 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="2xl:flex justify-between mb-3"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Socket status
        </h3>
        <div
          class="space-x-2"
        >
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Event log
          </button>
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Report an issue
          </button>
        </div>
      </div>
      <div
        class="bg-neutral/10 rounded-lg p-4 mb-4"
      >
        <div
          class="flex my-auto"
        >
          <div
            class="pr-7"
          >
            <h5
              class="mb-2 text-6xl text-neutral font-bold"
            >
              A
            </h5>
            <p
              class="text-md font-semibold break-words"
            >
              Last known status
            </p>
            <div
              class="flex items-center"
            >
              <svg
                aria-hidden="true"
                class="fill-current h-4 w-4 mr-2 text-sky"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="12"
                />
              </svg>
              Available
            </div>
          </div>
          <div>
            <p
              class="text-md font-semibold break-words"
            >
              Last contact
            </p>
            <p
              class="text-md font-normal break-words"
            >
              12 Aug 2022 - 09:18
            </p>
            <div
              class="pb-4"
            />
            <p
              class="text-md font-semibold break-words"
            >
              Firmware version
            </p>
            <p
              class="text-md font-normal mb-2 break-words"
            >
              abc-123
            </p>
            <span
              aria-label="Updating"
              class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary"
              role="status"
            >
              <span
                class="h-4 w-4 mr-1"
              >
                <div
                  role="status"
                >
                  <svg
                    aria-hidden="true"
                    class="h-4 w-4 animate-spin fill-primary text-neutral/30"
                    viewBox="0 0 100 101"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                      />
                    </g>
                  </svg>
                </div>
              </span>
              Updating
            </span>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
`;

exports[`PodDoorStatus should match snapshot when socket firmware up to date 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="2xl:flex justify-between mb-3"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Socket status
        </h3>
        <div
          class="space-x-2"
        >
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Event log
          </button>
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Report an issue
          </button>
        </div>
      </div>
      <div
        class="bg-neutral/10 rounded-lg p-4 mb-4"
      >
        <div
          class="flex my-auto"
        >
          <div
            class="pr-7"
          >
            <h5
              class="mb-2 text-6xl text-neutral font-bold"
            >
              A
            </h5>
            <p
              class="text-md font-semibold break-words"
            >
              Last known status
            </p>
            <div
              class="flex items-center"
            >
              <svg
                aria-hidden="true"
                class="fill-current h-4 w-4 mr-2 text-sky"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="12"
                />
              </svg>
              Available
            </div>
          </div>
          <div>
            <p
              class="text-md font-semibold break-words"
            >
              Last contact
            </p>
            <p
              class="text-md font-normal break-words"
            >
              12 Aug 2022 - 09:18
            </p>
            <div
              class="pb-4"
            />
            <p
              class="text-md font-semibold break-words"
            >
              Firmware version
            </p>
            <p
              class="text-md font-normal mb-2 break-words"
            >
              abc-123
            </p>
            <span
              aria-label="This is the latest version"
              class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary"
              role="status"
            >
              This is the latest version
            </span>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
`;

exports[`PodDoorStatus should match snapshot when socket last contact is unknown 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="2xl:flex justify-between mb-3"
      >
        <h3
          class="text-lg font-bold pb-1"
        >
          Socket status
        </h3>
        <div
          class="space-x-2"
        >
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Event log
          </button>
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
            type="button"
          >
            Report an issue
          </button>
        </div>
      </div>
      <div
        class="bg-neutral/10 rounded-lg p-4 mb-4"
      >
        <div
          class="flex my-auto"
        >
          <div
            class="pr-7"
          >
            <h5
              class="mb-2 text-6xl text-neutral font-bold"
            >
              A
            </h5>
            <p
              class="text-md font-semibold break-words"
            >
              Last known status
            </p>
            <div
              class="flex items-center"
            >
              <svg
                aria-hidden="true"
                class="fill-current h-4 w-4 mr-2 text-sky"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="16"
                  cy="16"
                  r="12"
                />
              </svg>
              Available
            </div>
          </div>
          <div>
            <p
              class="text-md font-semibold break-words"
            >
              Last contact
            </p>
            <p
              class="text-md font-normal break-words"
            >
              Unknown
            </p>
            <div
              class="pb-4"
            />
            <p
              class="text-md font-semibold break-words"
            >
              Firmware version
            </p>
            <p
              class="text-md font-normal mb-2 break-words"
            >
              Unknown
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
`;
