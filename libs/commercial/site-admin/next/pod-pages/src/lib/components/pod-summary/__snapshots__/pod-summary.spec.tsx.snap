// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PodSummary should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          FOO
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
           - 
          <a
            class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
            href="/sites/1"
          >
            View site
          </a>
        </p>
      </div>
    </header>
    <div
      class="pb-4"
    />
    <div
      class="rounded-md p-3 bg-info/10 border-2 border-info/30"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              This charger is free to use for the public
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <span
        aria-label="PPID: BAR"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        PPID: BAR
      </span>
      <span
        aria-label="Model: T7-S"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        Model: T7-S
      </span>
      <span
        aria-label="Part of a scheme"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Checkmark
            </title>
            <path
              d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
              id="prod"
            />
          </svg>
        </span>
        Part of a scheme
      </span>
      <span
        aria-label="Host: Registers of Scotland"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        Host: Registers of Scotland
      </span>
      <span
        aria-label="Scheme: Registers of England"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        Scheme: Registers of England
      </span>
      <span
        aria-label="Public access"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Checkmark
            </title>
            <path
              d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
              id="prod"
            />
          </svg>
        </span>
        Public access
      </span>
      <span
        aria-label="Tariff assigned"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="h-3 w-3 stroke-1 stroke-current fill-current mt-[3px]"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Cross
            </title>
            <g>
              <g>
                <line
                  x1="5.16"
                  x2="18.94"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                />
              </g>
              <g>
                <line
                  x1="18.94"
                  x2="5.16"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                />
              </g>
            </g>
          </svg>
        </span>
        Tariff assigned
      </span>
      <span
        aria-label="Confirm charge"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="h-3 w-3 stroke-1 stroke-current fill-current mt-[3px]"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Cross
            </title>
            <g>
              <g>
                <line
                  x1="5.16"
                  x2="18.94"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                />
              </g>
              <g>
                <line
                  x1="18.94"
                  x2="5.16"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                />
              </g>
            </g>
          </svg>
        </span>
        Confirm charge
      </span>
      <span
        aria-label="Supports energy tariff"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Checkmark
            </title>
            <path
              d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
              id="prod"
            />
          </svg>
        </span>
        Supports energy tariff
      </span>
      <span
        aria-label="Contactless"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Checkmark
            </title>
            <path
              d="M12.64,25.37c-.26,0-.51-.1-.71-.29l-6.67-6.67c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l5.96,5.96,12.83-12.83c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-13.54,13.54c-.2,.2-.45,.29-.71,.29Z"
              id="prod"
            />
          </svg>
        </span>
        Contactless
      </span>
      <span
        aria-label="RFID reader"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
        role="status"
      >
        <span
          class="h-4 w-4 mr-1"
        >
          <svg
            class="h-3 w-3 stroke-1 stroke-current fill-current mt-[3px]"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>
              Cross
            </title>
            <g>
              <g>
                <line
                  x1="5.16"
                  x2="18.94"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                />
              </g>
              <g>
                <line
                  x1="18.94"
                  x2="5.16"
                  y1="5.11"
                  y2="18.89"
                />
                <path
                  d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                />
              </g>
            </g>
          </svg>
        </span>
        RFID reader
      </span>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="2xl:flex justify-between mb-3"
        >
          <h3
            class="text-lg font-bold pb-1"
          >
            Socket status
          </h3>
          <div
            class="space-x-2"
          >
            <button
              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
              type="button"
            >
              Event log
            </button>
            <button
              class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
              type="button"
            >
              Report an issue
            </button>
          </div>
        </div>
        <div
          class="bg-neutral-50 rounded-lg p-4 mb-4"
        >
          <div
            class="flex my-auto"
          >
            <div
              class="pr-7"
            >
              <h5
                class="mb-2 text-6xl text-neutral-400 font-bold"
              >
                A
              </h5>
              <p
                class="text-md font-semibold break-words"
              >
                Last known status
              </p>
              <div
                class="flex items-center"
              >
                <svg
                  aria-hidden="true"
                  class="fill-current h-4 w-4 mr-2 text-sky"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="16"
                    cy="16"
                    r="12"
                  />
                </svg>
                Available
              </div>
            </div>
            <div>
              <p
                class="text-md font-semibold break-words"
              >
                Last contact
              </p>
              <p
                class="text-md font-normal break-words"
              >
                12 Aug 2022 - 09:18
              </p>
              <div
                class="pb-4"
              />
              <p
                class="text-md font-semibold break-words"
              >
                Firmware version
              </p>
              <p
                class="text-md font-normal mb-2 break-words"
              >
                Unknown
              </p>
            </div>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charge schedule
        </h3>
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Pod charge schedules
              </caption>
              <thead
                class="border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Stop
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="align-text-top font-semibold"
                    >
                      Monday
                    </div>
                    <div>
                      10:30:00
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="align-text-top font-semibold"
                    >
                      Friday
                    </div>
                    <div>
                      12:30:00
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Active
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <div
        class="rounded-lg overflow-hidden"
      >
        <div
          data-testid="map"
          style="width: 100%; height: 100%; position: relative; z-index: 0; min-height: 12rem;"
        />
      </div>
      <div
        class="lg:col-span-2"
      >
        <section
          class="p-3 rounded-lg bg-white h-full"
        >
          <div
            class="flex justify-between items-center mb-3"
          >
            <h3
              class="text-lg font-bold pb-0"
            >
              Contact details
            </h3>
          </div>
          <p
            class="text-lg font-normal break-words"
          >
            Joe Blogs
          </p>
          <p
            class="text-lg font-normal break-words"
          >
            0123456789
          </p>
          <p
            class="text-lg font-normal break-words"
          >
            <EMAIL>
          </p>
        </section>
      </div>
      <section
        class="p-3 rounded-lg bg-primary-200"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charging information
        </h3>
        <p
          class="text-lg font-normal break-words"
        >
          Display and export charging data
        </p>
        <div
          class="pb-2"
        />
        <div
          class="relative inline-block text-left"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="bg-black border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
            data-headlessui-state=""
            id="headlessui-menu-button-:test-id-2"
            type="button"
          >
            <span>
              Download CSV
            </span>
            <svg
              class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="pb-2"
        />
      </section>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Charging stats - this month so far
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      So far this month you have generated
            £4,576.00
            in revenue from 2048 charges
            and delivered 567.89 kWh of
            energy
    </p>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy delivered
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              567.89
            </span>
            <span
              class="ml-3 text-neutral"
            >
              kWh
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          CO
          <sub
            class="sub"
          >
            2
          </sub>
           avoided*
        </h3>
        <div>
          <p>
            <span
              class="text-5xl leading-normal font-medium"
            >
              4,325.62
            </span>
            <span
              class="ml-3 text-neutral"
            >
              Kg
            </span>
          </p>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Revenue
        </h3>
        <div
          class="text-5xl leading-normal font-medium"
        >
          £4,576.00
        </div>
        <div
          class="flex items-center space-x-1"
        >
          <p
            class="text-md font-normal break-words"
          >
            No tariff assigned
          </p>
          <button
            aria-label="Edit tariff for charger FOO"
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-tariff-1"
            id="edit-tariff-1"
            name="edit-tariff-1"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Energy cost
        </h3>
        <span
          class="text-5xl leading-normal font-medium"
        >
          £3,245.50
        </span>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-xs font-normal break-words"
    >
      * Estimated based on calculations. We are working hard to improve the accuracy of CO
      <sub
        class="sub"
      >
        2
      </sub>
       data by improving data modelling. Please contact us if you have any questions regarding this information.
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Recent charges
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      There are no recent charges to display
    </p>
    <div
      class="pb-4"
    />
  </div>
</body>
`;
