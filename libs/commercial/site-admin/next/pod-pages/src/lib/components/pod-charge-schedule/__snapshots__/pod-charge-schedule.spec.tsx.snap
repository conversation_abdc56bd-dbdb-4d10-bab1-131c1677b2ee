// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PodChargeSchedule should match snapshot 1`] = `
<body>
  <div>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Charge schedule
      </h3>
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Pod charge schedules
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Start
                </th>
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Stop
                </th>
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Status
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <div
                    class="align-text-top font-semibold"
                  >
                    Monday
                  </div>
                  <div>
                    10:30:00
                  </div>
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <div
                    class="align-text-top font-semibold"
                  >
                    Friday
                  </div>
                  <div>
                    12:30:00
                  </div>
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <p
                    class="text-md font-bold break-words"
                  >
                    Active
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</body>
`;
