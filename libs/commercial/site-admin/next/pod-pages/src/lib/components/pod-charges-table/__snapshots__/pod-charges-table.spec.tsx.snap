// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PodChargeTable should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Recent complete charges
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Started
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Ended
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Energy delivered (kWh)
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Socket
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Revenue
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Charge duration
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                12/08/2022 - 08:38:12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                12/08/2022 - 14:38:12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                26
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £123.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                00:01:40
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                12/08/2022 - 08:38:12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                12/08/2022 - 08:38:12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                26
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                A
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £123.45
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                00:01:40
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;

exports[`PodChargeTable should match snapshot when there are no charges 1`] = `
<body>
  <div>
    <p
      class="text-md font-normal break-words"
    >
      There are no recent charges to display
    </p>
    <div
      class="pb-4"
    />
  </div>
</body>
`;
