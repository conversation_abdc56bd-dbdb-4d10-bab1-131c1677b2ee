import { Command, CommandRunner } from 'nest-commander';
import {
  EXTERNAL_USER_DEACTIVATION_PERIOD,
  POD_POINT_USER_DEACTIVATION_PERIOD,
} from './deactivate-dormant-accounts.command';
import { Logger } from '@nestjs/common';
import { UserRecord } from 'firebase-admin/lib/auth';
import { getAuth } from '@experience/shared/firebase/admin';
import dayjs from 'dayjs';

@Command({ name: 'delete-deactivated-accounts' })
export class DeleteDeactivatedAccountsCommand extends CommandRunner {
  private readonly logger = new Logger(DeleteDeactivatedAccountsCommand.name);

  constructor() {
    super();
  }

  async run() {
    await this.deleteDeactivatedAccounts();
  }

  private async deleteDeactivatedAccounts(pageToken?: string) {
    const result = await getAuth().listUsers(1000, pageToken);
    for (const user of result.users) {
      this.logger.log({ user }, 'checking if user should be deleted');

      if (!user.disabled) {
        this.logger.log({ user }, 'user has not been deactivated');
        continue;
      }

      const isPodPointUser = user.email?.endsWith('@pod-point.com');
      const accountPeriod = dayjs().subtract(
        isPodPointUser
          ? POD_POINT_USER_DEACTIVATION_PERIOD + 30
          : EXTERNAL_USER_DEACTIVATION_PERIOD + 30,
        'days'
      );
      const { creationTime, lastSignInTime } = user.metadata;

      await this.deleteUserIfInactive(
        accountPeriod,
        lastSignInTime ? dayjs(lastSignInTime) : dayjs(creationTime),
        user
      );
    }

    if (result.pageToken) {
      await this.deleteDeactivatedAccounts(result.pageToken);
    }
  }

  private async deleteUserIfInactive(
    accountPeriod: dayjs.Dayjs,
    activityDate: dayjs.Dayjs,
    user: UserRecord
  ): Promise<void> {
    if (accountPeriod.isAfter(activityDate)) {
      this.logger.log({ user }, 'deleting user');
      await getAuth().deleteUser(user.uid);
    }
  }
}
