import * as CompanyEmailValidator from 'company-email-validator';
import { Attributes, FindOptions } from 'sequelize/types/model';
import {
  CreateDomainRequest,
  Domain,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  DeniedDomainException,
  DuplicateDomainException,
} from './domain.exception';
import { EvDriverDomains, Groups } from '@experience/shared/sequelize/podadmin';
import { Inject, Injectable, Logger } from '@nestjs/common';

export const includeOptions = [{ model: Groups, as: 'group' }];

@Injectable()
export class DomainService {
  private readonly logger = new Logger(DomainService.name);

  constructor(
    @Inject('EV_DRIVER_DOMAINS_REPOSITORY')
    private repository: typeof EvDriverDomains
  ) {}

  async findAll(): Promise<Domain[]> {
    this.logger.log('finding domains');
    return this.findByOptions({ include: includeOptions });
  }

  async findByGroupId(groupId: number): Promise<Domain[]> {
    this.logger.log({ groupId }, 'finding domains');

    return this.findByOptions({
      include: includeOptions,
      where: { groupId: groupId },
    });
  }

  async createByGroupId(
    groupId: number,
    request: CreateDomainRequest
  ): Promise<Domain> {
    this.logger.log({ groupId, request }, 'creating domain');

    this.checkCompanyDomain(request.domainName);

    return this.repository
      .findOne({
        paranoid: false,
        where: {
          domainName: request.domainName,
          groupId: groupId,
        },
      })
      .then(async (entity) => {
        if (entity) {
          if (entity.deletedAt) {
            await this.repository.restore({ where: { id: entity.id } });
          }
          return entity;
        }
      })
      .then(async (entity) => {
        if (entity) {
          return entity;
        }
        return await this.repository.create({
          domainName: request.domainName,
          groupId: groupId,
        });
      })
      .then((entity) => this.map(entity))
      .catch((error) => {
        this.checkForDuplicates(error.name);
        throw error;
      });
  }

  async updateByGroupIdAndDomainId(
    groupId: number,
    domainId: number,
    request: CreateDomainRequest
  ): Promise<void> {
    this.logger.log({ groupId, domainId, request }, 'updating domain');
    this.checkCompanyDomain(request.domainName);

    await this.repository
      .update(
        { domainName: request.domainName },
        { where: { id: domainId, groupId } }
      )
      .catch((error) => {
        this.checkForDuplicates(error.name);
        throw error;
      });
  }

  async deleteByGroupIdAndDomainId(
    groupId: number,
    domainId: number
  ): Promise<void> {
    this.logger.log({ groupId, domainId }, 'deleting domain');
    await this.repository.destroy({ where: { id: domainId, groupId } });
  }

  private async findByOptions(
    options: FindOptions<Attributes<EvDriverDomains>>
  ): Promise<Domain[]> {
    return this.repository
      .findAll(options)
      .then((entities) => this.mapArray(entities));
  }

  private mapArray(entities: EvDriverDomains[]): Domain[] {
    return entities.map((entity) => this.map(entity));
  }

  private map(entity: EvDriverDomains): Domain {
    return {
      activatedOn: entity.createdAt,
      domainName: entity.domainName,
      id: entity.id,
      groupId: entity.groupId,
    };
  }

  private checkCompanyDomain(domainName: string): void {
    if (!CompanyEmailValidator.isCompanyDomain(domainName)) {
      throw new DeniedDomainException();
    }
  }

  private checkForDuplicates(errorName: string): void {
    if (errorName === 'SequelizeUniqueConstraintError') {
      throw new DuplicateDomainException();
    }
  }
}
