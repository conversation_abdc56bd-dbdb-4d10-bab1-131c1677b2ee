import { CHARGE_ALREADY_CONFIRMED_ERROR } from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  Pod,
  TEST_CONFIRM_CHARGE_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import axios, { AxiosResponse } from 'axios';
import dayjs from 'dayjs';

const dateFormat = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;

const createPropertyMatchers = (response: AxiosResponse<Pod>) => ({
  recentCharges: new Array(response.data.recentCharges.length).fill({
    endedAt: expect.stringMatching(dateFormat),
    pluggedIn: expect.stringMatching(dateFormat),
    startedAt: expect.stringMatching(dateFormat),
  }),
});

export const describePodModule = (baseUrl: string) => {
  describe('pod module', () => {
    describe('pod controller', () => {
      it('should find a list of pods', async () => {
        const response = await axios.get(
          `${baseUrl}/pods?authId=d54561e3-222e-4e8d-be86-6c77891160ee`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should find a list of pods for a scheme', async () => {
        const response = await axios.get(
          `${baseUrl}/pods?authId=f3c55848-f8a0-4f70-98b3-327005c31c93`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      describe.each([true, false])(
        'with projections feature flag %s',
        (useChargeProjectionEndpoints) => {
          const featureFlagsHeader = useChargeProjectionEndpoints
            ? 'useChargeProjectionEndpoints'
            : '';

          it('should find an individual pod by id', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/91647?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );

            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot(
              createPropertyMatchers(response)
            );
          });

          it('should find an individual pod by id for a scheme', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/4090?authId=f3c55848-f8a0-4f70-98b3-327005c31c93`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);

            const recentChargeTable = createPropertyMatchers(response);

            recentChargeTable.recentCharges[0] = {
              pluggedIn: expect.stringMatching(dateFormat),
              startedAt: expect.stringMatching(dateFormat),
            };

            expect(response.data).toMatchSnapshot(recentChargeTable);
          });

          it('should find an individual pod by ppid', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/PG-70500?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot(
              createPropertyMatchers(response)
            );
          });

          it('should find an individual pod by ppid for a scheme', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/PG-80435?authId=f3c55848-f8a0-4f70-98b3-327005c31c93`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );

            const recentChargeTable = createPropertyMatchers(response);

            recentChargeTable.recentCharges[0] = {
              pluggedIn: expect.stringMatching(dateFormat),
              startedAt: expect.stringMatching(dateFormat),
            };

            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot(recentChargeTable);
          });

          it('should find charges for a pod by id', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/91647/charges?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              'Plugged in,Started at,Ended at,Energy delivered (kWh),Socket,Name,Email,Vehicle,Revenue,Total duration,Charging duration,Energy cost,CO2 avoided (kg),Confirmed'
            );
            if (useChargeProjectionEndpoints) {
              expect(response.data).toContain(
                '2022-09-27 18:00:00,2022-09-27 18:05:00,2022-09-27 19:00:00,567.89,A,-,-,-,"£4,576.00",00:55:00,00:55:00,£0.32,4325.620,Yes'
              );
              expect(response.data.startsWith('\uFEFF'));
              return;
            }
            expect(response.data).toContain(
              '5.20,A,Bonnie Nolan,<EMAIL>,Audi A3 e-tron,£0.00,00:00:08,-,£10.00,2.912,No'
            );
            expect(response.data.startsWith('\uFEFFPlugged in'));
          });

          it('should find charges with driver details for a pod by id', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/4090/charges?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              'Plugged in,Started at,Ended at,Energy delivered (kWh),Socket,Name,Email,Vehicle,Revenue,Total duration,Charging duration,Energy cost,CO2 avoided (kg),Confirmed'
            );
            if (useChargeProjectionEndpoints) {
              expect(response.data).toContain(
                '2022-09-27 18:00:00,2022-09-27 18:05:00,2022-09-27 19:00:00,567.89,A,Layla Moloney,<EMAIL>,-,"£4,576.00",00:55:00,00:55:00,£0.32,4325.620,Yes'
              );
              expect(response.data.startsWith('\uFEFF'));
              return;
            }
            expect(response.data).toContain(
              '1.70,A,-,-,Audi A3 e-tron,£0.00,00:00:20,01:00:10,£0.24,0.952,Yes'
            );
            expect(response.data).toContain(
              '1.70,A,Layla Moloney,<EMAIL>,-,£0.00,01:00:25,01:00:10,£0.24,0.952,Yes'
            );
            expect(response.data.startsWith('\uFEFFPlugged in'));
          });

          it('should find last months charges for a pod by id', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/91647/charges?authId=d54561e3-222e-4e8d-be86-6c77891160ee&date=${dayjs()
                .subtract(1, 'month')
                .startOf('month')}`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              'Plugged in,Started at,Ended at,Energy delivered (kWh),Socket,Name,Email,Vehicle,Revenue,Total duration,Charging duration,Energy cost,CO2 avoided (kg),Confirmed'
            );
            if (useChargeProjectionEndpoints) {
              expect(response.data).toContain(
                '2022-09-27 18:00:00,2022-09-27 18:05:00,2022-09-27 19:00:00,567.89,A,-,-,-,"£4,576.00",00:55:00,00:55:00,£0.32,4325.620,Yes'
              );
              expect(response.data.startsWith('\uFEFF'));
              return;
            }
            expect(response.data).toContain(
              '5.50,A,Bonnie Nolan,<EMAIL>,Audi A3 e-tron,£0.00,-,-,£20.00,3.080,No'
            );
            expect(response.data.startsWith('\uFEFFPlugged in'));
          });

          it('should find charges for a pod by id for a scheme', async () => {
            const response = await axios.get(
              `${baseUrl}/pods/4090/charges?authId=f3c55848-f8a0-4f70-98b3-327005c31c93`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              'Plugged in,Started at,Ended at,Energy delivered (kWh),Socket,Name,Email,Vehicle,Revenue,Total duration,Charging duration,Energy cost,CO2 avoided (kg),Confirmed'
            );
            if (useChargeProjectionEndpoints) {
              expect(response.data).toContain(
                '2022-09-27 18:00:00,2022-09-27 18:05:00,2022-09-27 19:00:00,567.89,A,Layla Moloney,<EMAIL>,-,"£4,576.00",00:55:00,00:55:00,£0.32,4325.620,Yes'
              );
              expect(response.data.startsWith('\uFEFF'));
              return;
            }
            expect(response.data).toContain(
              '5.20,A,-,-,Audi A3 e-tron,£0.00,00:00:08,-,£10.00,2.912,No'
            );
            expect(response.data).toContain(
              '2.90,A,-,-,Audi A3 e-tron,£0.00,00:00:04,00:03:20,£5.00,1.624,No'
            );
            expect(response.data).toContain(
              '1.70,A,-,-,Audi A3 e-tron,£0.00,00:00:20,01:00:10,£0.24,0.952,Yes'
            );
            expect(response.data.startsWith('\uFEFFPlugged in'));
          });
        }
      );

      it('should assign a tariff to a pod', async () => {
        const updateResponse = await axios.put(
          `${baseUrl}/pods/91647/tariff?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
          { id: 249 },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(updateResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/pods/91647?authId=d54561e3-222e-4e8d-be86-6c77891160ee`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should remove a tariff from a pod', async () => {
        const updateResponse = await axios.delete(
          `${baseUrl}/pods/91647/tariff?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        expect(updateResponse.status).toEqual(204);

        const response = await axios.get(
          `${baseUrl}/pods/91647?authId=d54561e3-222e-4e8d-be86-6c77891160ee`
        );

        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(createPropertyMatchers(response));
      });

      it('should find an individual pods security event log', async () => {
        const response = await axios.get(
          `${baseUrl}/pods/PG-80367/events/security?authId=f3c55848-f8a0-4f70-98b3-327005c31c93`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should confirm a charge for a pod', async () => {
        const response = await axios.post(
          `${baseUrl}/pods/4090/charges?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae&adminId=12`,
          {
            ...TEST_CONFIRM_CHARGE_REQUEST,
            driverEmail: '<EMAIL>',
          }
        );
        expect(response.status).toEqual(204);
      });

      it('should throw a charge already confirmed exception if confirming an already confirmed charge ', async () => {
        await axios
          .post(
            `${baseUrl}/pods/4090/charges?authId=f3c55848-f8a0-4f70-8802-f46d5957b0ae&adminId=12`,
            {
              ...TEST_CONFIRM_CHARGE_REQUEST,
              driverEmail: '<EMAIL>',
            }
          )
          .catch((error) => {
            expect(error.response.status).toEqual(409);
            expect(error.response.data.message).toEqual(
              CHARGE_ALREADY_CONFIRMED_ERROR
            );
          });
      });
    });
  });
};
