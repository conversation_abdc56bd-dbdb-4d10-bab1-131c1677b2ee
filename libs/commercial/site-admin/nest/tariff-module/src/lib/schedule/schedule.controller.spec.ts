import {
  DAY_NIGHT_TARIFF_SCHEDULE_MATCHING_START_TIMES_ERROR,
  TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
  TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
  TARIFF_INVALID_TARIFF_TIER_ERROR,
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TARIFF_SCHEDULE_PRICE_BAND_OVERLAP_ERROR,
  TariffErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';

import {
  COMMON_CURRENCY_PENCE_ERROR,
  COMMON_NUMERIC_STRING_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/shared/typescript/validation';
import { INestApplication } from '@nestjs/common';
import {
  PriceBandOverlapException,
  TariffIncompatiblePricingModelException,
} from './schedule.exception';
import { ScheduleController } from './schedule.controller';
import { ScheduleService } from './schedule.service';
import {
  TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
  TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
  TEST_CREATE_TARIFF_SCHEDULE_WITH_PRICE_BAND_REQUEST,
  TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
  TEST_DELETE_TARIFF_SCHEDULE_REQUEST,
  TEST_GROUP,
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
  TEST_UPDATE_TARIFF_SCHEDULE_REQUEST,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TariffScheduleErrorCodes } from '@experience/commercial/site-admin/typescript/domain-model-validation';
import { TariffScheduleOverlapError } from './schedule.exception';
import { Test, TestingModule } from '@nestjs/testing';
import { TransactionInterceptor } from '@experience/shared/sequelize/podadmin';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./schedule.service');
jest.mock('@experience/shared/sequelize/podadmin');

describe('SchedulesController', () => {
  let app: INestApplication;
  let scheduleService: ScheduleService;
  let transactionInterceptor: TransactionInterceptor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScheduleController],
      providers: [
        { provide: ScheduleService, useClass: ScheduleService },
        { provide: TransactionInterceptor, useClass: TransactionInterceptor },
      ],
    }).compile();

    scheduleService = module.get<ScheduleService>(ScheduleService);
    transactionInterceptor = module.get<TransactionInterceptor>(
      TransactionInterceptor
    );

    jest
      .spyOn(transactionInterceptor, 'intercept')
      .mockImplementation(async (context, next) => next.handle());

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Create custom schedule', () => {
    it.each([
      ['a positive price', TEST_CREATE_TARIFF_SCHEDULE_REQUEST.price],
      ['price of zero', '0'],
    ])(
      'should create a schedule by group id and tariff id with a %s',
      async (_, price) => {
        const createScheduleByGroupIdAndTariffId = jest.spyOn(
          scheduleService,
          'createScheduleByGroupIdAndTariffId'
        );

        const response = await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, price });

        expect(createScheduleByGroupIdAndTariffId).toHaveBeenCalledWith(
          TEST_GROUP.id,
          TEST_TARIFF.id,
          { ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, price }
        );
        expect(response.status).toEqual(201);
      }
    );

    it('should create a schedule by group id and tariff id', async () => {
      const createScheduleByGroupIdAndTariffId = jest.spyOn(
        scheduleService,
        'createScheduleByGroupIdAndTariffId'
      );

      const response = await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_CREATE_TARIFF_SCHEDULE_REQUEST);

      expect(createScheduleByGroupIdAndTariffId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_CREATE_TARIFF_SCHEDULE_REQUEST
      );
      expect(response.status).toEqual(201);
    });

    it('should create a schedule by group id and tariff id with a price band', async () => {
      const createScheduleByGroupIdAndTariffId = jest.spyOn(
        scheduleService,
        'createScheduleByGroupIdAndTariffId'
      );

      const response = await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_CREATE_TARIFF_SCHEDULE_WITH_PRICE_BAND_REQUEST);

      expect(createScheduleByGroupIdAndTariffId).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_CREATE_TARIFF_SCHEDULE_WITH_PRICE_BAND_REQUEST
      );
      expect(response.status).toEqual(201);
    });

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the endDay is "%s"',
      (endDay) =>
        request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, endDay })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the endTime is "%s"',
      async (endTime) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, endTime })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the startDay is "%s"',
      async (startDay) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, startDay })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the startTime is "%s"',
      async (startTime) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, startTime })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the price is "%s"',
      async (price) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, price })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the price is an invalid format: "%s"',
      async (price) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, price })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it('should throw a validation error when creating an energy schedule with a price that has too many decimal places', async () => {
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
          price: '1.0001',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_PER_KWH_PRICE_ERROR],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when creating an energy schedule with a price less than 1p (if not free)', async () => {
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
          price: '0.999',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR],
          error: 'Bad Request',
        });
    });

    it.each(['duration', 'fixed'])(
      'should throw a validation error when creating a %s schedule with a price that has decimal places',
      (pricingModel) => {
        request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({
            ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
            pricingModel,
            price: 0.1,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it('should throw a validation error when creating a tariff schedule and an invalid tariff schedule type', async () =>
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
          pricingModel: 'unlimited',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [
            // this error is for the pricing model validation
            TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
            // this error is for the price validation
            TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
          ],
          error: 'Bad Request',
        }));

    it('should throw a validation error when creating a tariff schedule and an invalid user type', async () =>
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({ ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST, tariffTier: 'admins' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it.each([-1, 'a string'])(
      'should throw a validation error when creating a tariff schedule with price band and start second is %s',
      async (value) =>
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({
            ...TEST_CREATE_TARIFF_SCHEDULE_WITH_PRICE_BAND_REQUEST,
            startSecond: value,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_NUMERIC_STRING_ERROR],
            error: 'Bad Request',
          })
    );

    it('should return bad request status code if the schedule service throws a schedule pricing model incompatibility exception', () => {
      jest
        .spyOn(scheduleService, 'createScheduleByGroupIdAndTariffId')
        .mockRejectedValueOnce(new TariffIncompatiblePricingModelException());

      return request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_CREATE_TARIFF_SCHEDULE_REQUEST)
        .expect(400)
        .expect({
          error: TariffErrorCodes.TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          message: TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          statusCode: 400,
        });
    });

    it('should return conflict status code if the schedule service throws a price band overlap exception', () => {
      jest
        .spyOn(scheduleService, 'createScheduleByGroupIdAndTariffId')
        .mockRejectedValueOnce(new PriceBandOverlapException());

      return request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_CREATE_TARIFF_SCHEDULE_WITH_PRICE_BAND_REQUEST)
        .expect(409)
        .expect({
          error: TariffErrorCodes.TARIFF_SCHEDULE_PRICE_BAND_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_PRICE_BAND_OVERLAP_ERROR,
          statusCode: 409,
        });
    });

    it('should throw a validation error when creating a tariff schedule with an overlap', () => {
      jest
        .spyOn(scheduleService, 'createScheduleByGroupIdAndTariffId')
        .mockRejectedValueOnce(new TariffScheduleOverlapError());

      return request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_CREATE_TARIFF_SCHEDULE_REQUEST)
        .expect(409)
        .expect({
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 409,
        });
    });
  });

  describe('Create work week schedule', () => {
    it.each([
      [
        'positive prices',
        TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST.weekdayPrice,
        TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST.weekendPrice,
      ],
      ['prices of zero', '0', '0'],
    ])(
      'should create a work week schedule by group id and tariff id with %s',
      async (_, weekdayPrice, weekendPrice) => {
        const createWorkWeekScheduleByGroupIdAndTariffId = jest.spyOn(
          scheduleService,
          'createWorkWeekScheduleByGroupIdAndTariffId'
        );

        const response = await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekdayPrice,
            weekendPrice,
          });

        expect(createWorkWeekScheduleByGroupIdAndTariffId).toHaveBeenCalledWith(
          TEST_GROUP.id,
          TEST_TARIFF.id,
          {
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekdayPrice,
            weekendPrice,
          }
        );
        expect(response.status).toEqual(201);
      }
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the weekendPrice is "%s"',
      async (weekendPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekendPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the weekdayPrice is "%s"',
      async (weekdayPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekdayPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the tariffTier is "%s"',
      async (tariffTier) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            tariffTier,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_INVALID_TARIFF_TIER_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the weekdayPrice is an invalid format: "%s"',
      async (weekdayPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekdayPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the weekendPrice is an invalid format: "%s"',
      async (weekendPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            weekendPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it.each(['weekdayPrice', 'weekendPrice'])(
      'should throw a validation error when creating a workweek schedule with a %s that has too many decimal places',
      async (price) => {
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            [price]: '1.0001',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_PER_KWH_PRICE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it.each(['weekdayPrice', 'weekendPrice'])(
      'should throw a validation error when creating a workweek schedule with a %s that is less than 1p (if not free)',
      async (price) => {
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            [price]: '0.999',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it.each(['weekdayPrice', 'weekendPrice'])(
      'should throw a validation error when creating a workweek schedule with a %s that is above the limit',
      (price) => {
        request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/workweek?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            [price]: '200',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_SCHEDULE_LIMIT_ERROR('£2.00')],
            error: 'Bad Request',
          });
      }
    );
  });

  describe('Create day/night schedule', () => {
    it.each([
      [
        'positive prices',
        TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST.dayPrice,
        TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST.nightPrice,
      ],
      ['prices of zero', '0', '0'],
    ])(
      'should create a day/night schedule by group id and tariff id',
      async (_, dayPrice, nightPrice) => {
        const createDayNightScheduleByGroupIdAndTariffId = jest.spyOn(
          scheduleService,
          'createDayNightScheduleByGroupIdAndTariffId'
        );

        const response = await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayPrice,
            nightPrice,
          });

        expect(createDayNightScheduleByGroupIdAndTariffId).toHaveBeenCalledWith(
          TEST_GROUP.id,
          TEST_TARIFF.id,
          {
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayPrice,
            nightPrice,
          }
        );
        expect(response.status).toEqual(201);
      }
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the day price is "%s"',
      async (dayPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the night price is "%s"',
      async (nightPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            nightPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the day start time is "%s"',
      async (dayStartTime) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayStartTime,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the night start time is "%s"',
      async (nightStartTime) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            nightStartTime,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and both the day and night start time is "%s"',
      async (time) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayStartTime: time,
            nightStartTime: time,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the tariff tier is "%s"',
      async (tariffTier) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            tariffTier,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_INVALID_TARIFF_TIER_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the day price is an invalid format: "%s"',
      async (dayPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            dayPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the night price is an invalid format: "%s"',
      async (nightPrice) =>
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            nightPrice,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it.each(['dayPrice', 'nightPrice'])(
      'should throw a validation error when creating a daynight schedule with a %s that has too many decimal places',
      async (price) => {
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            [price]: '1.0001',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_PER_KWH_PRICE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it.each(['dayPrice', 'nightPrice'])(
      'should throw a validation error when creating a daynight schedule with a %s that is less than 1p (if not free)',
      async (price) => {
        await request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
            [price]: '0.999',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it.each(['dayPrice', 'nightPrice'])(
      'should throw a validation error when creating a daynight schedule with a %s that is above the limit',
      (price) => {
        request(app.getHttpServer())
          .post(
            `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
          )
          .send({
            ...TEST_CREATE_WORK_WEEK_TARIFF_SCHEDULE_REQUEST,
            [price]: '200',
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [TARIFF_SCHEDULE_LIMIT_ERROR('£2.00')],
            error: 'Bad Request',
          });
      }
    );

    it('should throw a validation error when creating a daynight schedule with a night start time that is equal to the day start time', async () => {
      await request(app.getHttpServer())
        .post(
          `/tariffs/${TEST_TARIFF.id}/schedules/daynight?groupId=${TEST_GROUP.id}`
        )
        .send({
          ...TEST_CREATE_DAY_NIGHT_TARIFF_SCHEDULE_REQUEST,
          dayStartTime: '00:00',
          nightStartTime: '00:00',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [DAY_NIGHT_TARIFF_SCHEDULE_MATCHING_START_TIMES_ERROR],
          error: 'Bad Request',
        });
    });
  });

  describe('Update', () => {
    it.each([
      ['a positive price', TEST_UPDATE_TARIFF_SCHEDULE_REQUEST.price],
      ['a price of zero', '0'],
    ])('should update a tariff schedule with %s', async (_, price) => {
      const mockUpdateSchedule = jest
        .spyOn(scheduleService, 'updateScheduleByGroupIdAndTariffId')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, price });

      expect(mockUpdateSchedule).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id,
        { ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, price }
      );
      expect(response.status).toEqual(204);
    });

    it('should update a tariff schedule', async () => {
      const mockUpdateSchedule = jest
        .spyOn(scheduleService, 'updateScheduleByGroupIdAndTariffId')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send(TEST_UPDATE_TARIFF_SCHEDULE_REQUEST);

      expect(mockUpdateSchedule).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id,
        TEST_UPDATE_TARIFF_SCHEDULE_REQUEST
      );
      expect(response.status).toEqual(204);
    });

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the endDay is "%s"',
      async (endDay) =>
        await request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, endDay })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the endTime is "%s"',
      async (endTime) =>
        await request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, endTime })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the startDay is "%s"',
      async (startDay) =>
        await request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, startDay })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the startTime is "%s"',
      (startTime) =>
        request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, startTime })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([null, undefined, ''])(
      'should throw a validation error when creating a schedule and the price is "%s"',
      async (price) =>
        await request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, price })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );

    it.each([' ', 'hello', '0.0.2'])(
      'should throw a validation error when creating a schedule and the price is an invalid format: "%s"',
      async (price) =>
        await request(app.getHttpServer())
          .put(
            `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, price })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          })
    );

    it('should throw a validation error when updating an energy schedule with a price that has too many decimal places', async () => {
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
          price: '1.0001',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_PER_KWH_PRICE_ERROR],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when updating an energy schedule with a price less than 1p (if not free)', async () => {
      await request(app.getHttpServer())
        .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send({
          ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
          price: '0.999',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR],
          error: 'Bad Request',
        });
    });

    it.each(['duration', 'fixed'])(
      'should throw a validation error when creating a %s schedule with a price that has decimal places',
      async (pricingModel) => {
        await request(app.getHttpServer())
          .post(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
          .send({
            ...TEST_CREATE_TARIFF_SCHEDULE_REQUEST,
            pricingModel,
            price: 0.1,
          })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_CURRENCY_PENCE_ERROR],
            error: 'Bad Request',
          });
      }
    );

    it('should throw a validation error when creating a tariff schedule and an invalid tariff schedule type', async () =>
      await request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send({
          ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST,
          pricingModel: 'unlimited',
        })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [
            // this error is for the pricing model validation
            TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
            // this error is for the price validation
            TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
          ],
          error: 'Bad Request',
        }));

    it('should throw a validation error when creating a tariff schedule and an invalid user type', async () =>
      await request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send({ ...TEST_UPDATE_TARIFF_SCHEDULE_REQUEST, tariffTier: 'admins' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [TARIFF_INVALID_TARIFF_TIER_ERROR],
          error: 'Bad Request',
        }));

    it('should return bad request status code if the schedule service throws a schedule pricing model incompatibility exception', () => {
      jest
        .spyOn(scheduleService, 'updateScheduleByGroupIdAndTariffId')
        .mockRejectedValueOnce(new TariffIncompatiblePricingModelException());

      return request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send(TEST_UPDATE_TARIFF_SCHEDULE_REQUEST)
        .expect(400)
        .expect({
          error: TariffErrorCodes.TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          message: TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          statusCode: 400,
        });
    });

    it('should throw a validation error when updating a tariff schedule with an overlap', () => {
      jest
        .spyOn(scheduleService, 'updateScheduleByGroupIdAndTariffId')
        .mockRejectedValueOnce(new TariffScheduleOverlapError());

      return request(app.getHttpServer())
        .put(
          `/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}?groupId=${TEST_GROUP.id}`
        )
        .send(TEST_UPDATE_TARIFF_SCHEDULE_REQUEST)
        .expect(409)
        .expect({
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 409,
        });
    });
  });

  describe('Delete', () => {
    it('should delete by groupId, tariffId, and a list of scheduleIds', async () => {
      const mockDelete = jest.spyOn(
        scheduleService,
        'deleteByGroupIdAndTariffId'
      );

      const response = await request(app.getHttpServer())
        .delete(`/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`)
        .send(TEST_DELETE_TARIFF_SCHEDULE_REQUEST);

      expect(mockDelete).toHaveBeenCalledWith(
        TEST_GROUP.id,
        TEST_TARIFF.id,
        TEST_DELETE_TARIFF_SCHEDULE_REQUEST
      );

      expect(response.status).toEqual(204);
    });

    it.each([null, undefined, '', []])(
      'should throw a validation error when deleting a schedule and schedule IDs are %s',
      async (scheduleIds) =>
        await request(app.getHttpServer())
          .delete(
            `/tariffs/${TEST_TARIFF.id}/schedules?groupId=${TEST_GROUP.id}`
          )
          .send({ ...TEST_DELETE_TARIFF_SCHEDULE_REQUEST, scheduleIds })
          .expect(400)
          .expect({
            statusCode: 400,
            message: [COMMON_REQUIRED_ERROR],
            error: 'Bad Request',
          })
    );
  });
});
