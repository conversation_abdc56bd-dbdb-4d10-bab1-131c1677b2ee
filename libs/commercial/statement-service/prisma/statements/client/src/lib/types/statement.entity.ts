import { Prisma } from '@prisma/clients/statements';

export const statementShallowIncludeOptions = {
  include: {
    invoice: true,
    statementChargerConfig: true,
  },
};

export type StatementEntityShallow = Prisma.StatementGetPayload<
  typeof statementShallowIncludeOptions
>;

export const statementDeepIncludeOptions = {
  include: {
    ...statementShallowIncludeOptions.include,
    workItem: {
      include: {
        user: true,
      },
    },
  },
};

export type StatementEntity = Prisma.StatementGetPayload<
  typeof statementDeepIncludeOptions
>;

export const statementNoInvoiceIncludeOptions = {
  include: {
    ...statementShallowIncludeOptions.include,
    ...statementDeepIncludeOptions.include,
    invoice: false,
  },
};

export type StatementEntityNoInvoice = Prisma.StatementGetPayload<
  typeof statementNoInvoiceIncludeOptions
>;
