-- CreateTable
CREATE TABLE "statements"."ChargerConfig" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "siteId" TEXT NOT NULL,
    "ppid" TEXT NOT NULL,
    "fee" DECIMAL(12,2) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "ChargerConfig_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "statements"."ChargerConfig" ADD CONSTRAINT "ChargerConfig_siteId_fkey" FOREIGN KEY ("siteId") REFERENCES "statements"."SiteConfig"("siteId") ON DELETE RESTRICT ON UPDATE CASCADE;
