DO
$do$
BEGIN
   IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'grafana_ro') THEN
      RAISE NOTICE 'Role "grafana_ro" already exists. Skipping.';
   ELSE
      CREATE USER grafana_ro;
   END IF;
END
$do$;

GRANT USAGE ON SCHEMA statements TO grafana_ro;
ALTER DEFAULT PRIVILEGES IN SCHEMA statements GRANT SELECT ON TABLES TO grafana_ro;
GRANT SELECT ON ALL TABLES IN SCHEMA statements TO grafana_ro;
