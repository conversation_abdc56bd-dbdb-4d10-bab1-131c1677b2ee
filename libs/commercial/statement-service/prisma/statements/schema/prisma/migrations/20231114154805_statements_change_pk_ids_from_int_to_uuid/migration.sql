/*
  Warnings:

  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `WorkItem` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE "statements"."WorkItem" DROP CONSTRAINT "WorkItem_userId_fkey";

-- AlterTable
ALTER TABLE "statements"."User" DROP CONSTRAINT "User_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "User_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "statements"."User_id_seq";

-- AlterTable
ALTER TABLE "statements"."WorkItem" DROP CONSTRAINT "WorkItem_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ALTER COLUMN "userId" SET DATA TYPE TEXT,
ADD CONSTRAINT "WorkItem_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "statements"."WorkItem_id_seq";

-- AddForeignKey
ALTER TABLE "statements"."WorkItem" ADD CONSTRAINT "WorkItem_userId_fkey" FOREIGN KEY ("userId") REFERENCES "statements"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
