-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "statements";

-- CreateTable
CREATE TABLE "statements"."WorklistItem" (
    "id" SERIAL NOT NULL,
    "groupName" TEXT NOT NULL,
    "siteName" TEXT NOT NULL,
    "month" TIMESTAMP(3) NOT NULL,
    "statusId" INTEGER NOT NULL,
    "userId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "WorklistItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "statements"."Status" (
    "id" SERIAL NOT NULL,
    "type" TEXT NOT NULL,

    CONSTRAINT "Status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "statements"."User" (
    "id" SERIAL NOT NULL,
    "authId" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "statements"."WorklistItem" ADD CONSTRAINT "WorklistItem_statusId_fkey" FOREIGN KEY ("statusId") REFERENCES "statements"."Status"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "statements"."WorklistItem" ADD CONSTRAINT "WorklistItem_userId_fkey" FOREIGN KEY ("userId") REFERENCES "statements"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
