import {
  CreateGroupRequest,
  Group,
  SITE_ADMIN_API_URL,
  UpdateGroupRequest,
} from '@experience/commercial/statement-service/shared';
import {
  GroupNotFoundException,
  mapGroupConfigToDto,
} from '@experience/commercial/statement-service/nest/shared';
import { Injectable, Logger } from '@nestjs/common';
import { Group as SiteAdminGroup } from '@experience/commercial/site-admin/typescript/domain-model';
import { SitesService } from '@experience/commercial/statement-service/nest/sites-module';
import { StatementsPrismaClient } from '@experience/commercial/statement-service/prisma/statements/client';
import { StripeCustomerService } from '@experience/shared/nest/stripe';

@Injectable()
export class GroupsService {
  private readonly logger = new Logger(GroupsService.name);

  constructor(
    private readonly statementsDatabase: StatementsPrismaClient,
    private readonly sitesService: SitesService,
    private readonly stripeCustomerService: StripeCustomerService
  ) {}

  async createGroup(request: CreateGroupRequest): Promise<void> {
    this.logger.log({ request }, 'creating group');
    await this.statementsDatabase.groupConfig.create({
      data: request,
    });
  }

  async findAllGroups(): Promise<Group[]> {
    this.logger.log('finding group');
    const entities = await this.statementsDatabase.groupConfig.findMany();
    return entities.map(mapGroupConfigToDto);
  }

  async findSiteAdminGroups(): Promise<SiteAdminGroup[]> {
    this.logger.log('finding site admin groups');
    return fetch(`${SITE_ADMIN_API_URL}/user/groups?groupId=1`)
      .then((response) => response.json())
      .then((data: SiteAdminGroup[]) =>
        data?.filter((group) => group?.type !== 'scheme')
      );
  }

  async findGroupByGroupId(
    groupId: string,
    includeSites = false
  ): Promise<Group> {
    this.logger.log({ groupId }, 'finding group');
    const groupConfigEntity =
      await this.statementsDatabase.groupConfig.findUnique({
        where: { groupId },
      });
    if (!groupConfigEntity) {
      throw new GroupNotFoundException();
    }

    const group = mapGroupConfigToDto(groupConfigEntity);

    if (includeSites) {
      const sites = await this.sitesService.findSiteByGroupId(groupId);
      return { ...group, sites };
    }
    return group;
  }

  async updateGroupByGroupId(
    groupId: string,
    request: UpdateGroupRequest
  ): Promise<void> {
    this.logger.log('updating group');

    const groupConfigEntity =
      await this.statementsDatabase.groupConfig.findUnique({
        where: { groupId },
      });

    if (!groupConfigEntity) {
      throw new GroupNotFoundException();
    }

    await this.statementsDatabase.groupConfig.update({
      data: request,
      where: { groupId },
    });

    const { stripeCustomerId } = groupConfigEntity;

    if (stripeCustomerId) {
      await this.stripeCustomerService.update(stripeCustomerId, {
        name: request.businessName,
        email: request.businessEmail,
        address: {
          line1: request.addressLine1,
          line2: request.addressLine2,
          city: request.town,
          postalCode: request.postcode,
          country: 'GB',
        },
      });

      await this.statementsDatabase.groupConfig.updateMany({
        data: {
          businessName: request.businessName,
          businessEmail: request.businessEmail,
          addressLine1: request.addressLine1,
          addressLine2: request.addressLine2,
          town: request.town,
          postcode: request.postcode,
          county: request.county,
        },
        where: { stripeCustomerId },
      });
    }
  }

  async updateTransfersEnabledByConnectedAccountId(
    accountId: string,
    transfersEnabled: boolean
  ): Promise<void> {
    this.logger.log({ accountId }, 'updating transfers enabled');
    await this.statementsDatabase.groupConfig.updateMany({
      where: { stripeConnectedAccountId: accountId },
      data: { transfersEnabled },
    });
  }

  async updateSubscriptionStatusBySubscriptionId(
    stripeSubscriptionId: string,
    stripeSubscriptionStatus: string
  ): Promise<void> {
    this.logger.log(
      { stripeSubscriptionId },
      'updating stripe subscription status'
    );
    await this.statementsDatabase.groupConfig.updateMany({
      where: { stripeSubscriptionId },
      data: { stripeSubscriptionStatus },
    });
  }
}
