import { InvoiceDto } from '../invoice.dto';
import { TEST_GROUP } from './test-group';
import { TEST_SITE } from './test-site';
import { TEST_STATEMENT } from './test-statements';

export const TEST_INVOICE: InvoiceDto = {
  id: '32748f77-dec7-41eb-8b28-255a54e4397d',
  invoiceNumber: 'AF0000001',
  quoteNumber: 'AF001',
  invoiceDate: '2024-01-23',
  group: TEST_GROUP,
  site: TEST_SITE,
  statement: TEST_STATEMENT,
};

export const TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA: Partial<InvoiceDto> = {
  id: '32748f77-dec7-41eb-8b28-255a54e4397d',
  invoiceNumber: 'AF0000001',
  stripeInvoiceId: 'e50fe0e5-b51f-4563-81e2-fed6ef7e50da',
  stripeInvoiceNumber: 'ADF-4567',
  stripeInvoiceStatus: 'open',
};

export const TEST_INVOICE_WITH_STRIPE_DATA: InvoiceDto = {
  ...TEST_INVOICE,
  ...TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA,
};
