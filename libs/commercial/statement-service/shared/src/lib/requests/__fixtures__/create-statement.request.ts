import { CreateStatementRequest } from '../create-statement.request';
import { TEST_MONTHLY_SITE_STATEMENT } from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_WORK_ITEM } from '../../dtos/__fixtures__/test-work-items';

export const TEST_CREATE_STATEMENT_REQUEST: CreateStatementRequest = {
  adjustedFees: [{ ppid: 'test', fee: 0.1 }],
  date: TEST_MONTHLY_SITE_STATEMENT.date,
  groupUid: TEST_WORK_ITEM.groupUid,
  siteId: TEST_WORK_ITEM.siteId,
  workItemId: TEST_WORK_ITEM.id,
};
