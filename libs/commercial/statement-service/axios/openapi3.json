{"openapi": "3.0.0", "paths": {"/health": {"get": {"operationId": "HealthController_check", "parameters": [], "responses": {"200": {"description": "The Health Check is successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}, "503": {"description": "The Health Check is not successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {"redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}, "redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}}}}, "/invoices/{invoiceId}": {"get": {"operationId": "InvoiceController_findInvoiceById", "parameters": [{"name": "invoiceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}}}}, "/invoices/{invoiceId}/pdf": {"get": {"operationId": "InvoiceController_getInvoicePdf", "parameters": [{"name": "invoiceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/invoices/export/ifs": {"get": {"operationId": "InvoiceController_generateIfsInvoicesExport", "parameters": [{"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/invoices/reissue": {"post": {"operationId": "InvoiceController_reissueStripeInvoices", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReissueStripeInvoicesRequest"}}}}, "responses": {"204": {"description": ""}}}}, "/groups": {"get": {"operationId": "GroupsController_findAllGroups", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}}}}, "post": {"operationId": "GroupsController_addGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupRequest"}}}}, "responses": {"201": {"description": ""}}}}, "/groups/site-admin": {"get": {"operationId": "GroupsController_findSiteAdminGroups", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}}}}}, "/groups/{groupId}": {"get": {"operationId": "GroupsController_findGroup", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "includeSites", "required": true, "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Group"}}}}}}, "put": {"operationId": "GroupsController_updateGroup", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGroupRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/groups/{groupId}/statements": {"get": {"operationId": "GroupsStatementsController_findStatementsByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Statement"}}}}}}}}, "/sites/{siteId}": {"put": {"operationId": "SitesController_updateSiteConfig", "parameters": [{"name": "siteId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSiteRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/sites/{siteId}/chargers": {"get": {"operationId": "SitesController_findChargerConfigsWithPods", "parameters": [{"name": "siteId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChargerWithPod"}}}}}}}, "put": {"operationId": "SitesController_updateChargerConfig", "parameters": [{"name": "siteId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChargerRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/statements": {"get": {"operationId": "StatementController_findAllStatements", "parameters": [{"name": "month", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "groupId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "workItemStatus", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Statement"}}}}}}}, "post": {"operationId": "StatementController_createStatement", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStatementRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStatementResponse"}}}}}}}, "/statements/{statementId}": {"get": {"operationId": "StatementController_findStatement", "parameters": [{"name": "statementId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Statement"}}}}}}}, "/statements/{statementId}/pdf": {"get": {"operationId": "StatementController_getStatementPdf", "parameters": [{"name": "statementId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/stripe/customers": {"post": {"operationId": "StripeController_createStripeCustomerForGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeCustomerRequest"}}}}, "responses": {"201": {"description": ""}}}}, "/stripe/connected-accounts": {"post": {"operationId": "StripeController_createStripeConnectedAccountForGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeConnectedAccountRequest"}}}}, "responses": {"201": {"description": ""}}}, "delete": {"operationId": "StripeController_removeStripeConnectedAccountForGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeConnectedAccountRequest"}}}}, "responses": {"204": {"description": ""}}}}, "/stripe/connected-accounts/account-links": {"post": {"operationId": "StripeController_createStripeConnectedAccountLink", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeConnectedAccountAccountLinkRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/stripe/subscriptions": {"post": {"operationId": "StripeController_createStripeSubscriptionForGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateStripeSubscriptionRequest"}}}}, "responses": {"201": {"description": ""}}}, "put": {"operationId": "StripeController_updateStripeSubscriptionForGroup", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrUpdateStripeSubscriptionRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/stripe/subscriptions/{subscriptionId}": {"get": {"operationId": "StripeController_getSubscription", "parameters": [{"name": "subscriptionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/stripe/subscriptions/{subscriptionId}/invoices": {"get": {"operationId": "StripeController_getInvoicesBySubscriptionId", "parameters": [{"name": "subscriptionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionInvoiceDto"}}}}}}}}, "/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}": {"get": {"operationId": "StripeController_getSubscriptionInvoice", "parameters": [{"name": "invoiceId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "subscriptionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionInvoiceDto"}}}}}}}, "/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}/pdf": {"get": {"operationId": "StripeController_getSubscriptionInvoicePdf", "parameters": [{"name": "invoiceId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "subscriptionId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/stripe/webhooks": {"post": {"operationId": "WebhookController_webhook", "parameters": [{"name": "stripe-signature", "required": true, "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}}}, "/stripe/webhooks/connect": {"post": {"operationId": "WebhookController_connectWebhook", "parameters": [{"name": "stripe-signature", "required": true, "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}}}, "/work-items": {"get": {"operationId": "WorkItemController_findAllWorkItems", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkItem"}}}}}}}}, "/work-items/stats": {"get": {"operationId": "WorkItemController_countWorkItems", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatsWorkItems"}}}}}}}, "/work-items/{workItemId}": {"get": {"operationId": "WorkItemController_findWorkItem", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkItem"}}}}}}}, "/work-items/{workItemId}/user": {"post": {"operationId": "WorkItemController_assignUserToWorkItem", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignUserToWorkItemRequest"}}}}, "responses": {"201": {"description": ""}}}, "delete": {"operationId": "WorkItemController_removeUserFromWorkItem", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}}}, "/work-items/{workItemId}/automated": {"put": {"operationId": "WorkItemController_updateAutomatedStatus", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAutomatedStatusRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/work-items/{workItemId}/status": {"put": {"operationId": "WorkItemController_updateWorkItemStatus", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkItemStatusRequest"}}}}, "responses": {"200": {"description": ""}}}}, "/work-items/{workItemId}/statement": {"put": {"operationId": "WorkItemController_updateStatementByWorkItemId", "parameters": [{"name": "workItemId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSiteRequest"}}}}, "responses": {"200": {"description": ""}}}}}, "info": {"title": "", "description": "", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"Site": {"type": "object", "properties": {"automated": {"type": "boolean"}, "emails": {"type": "array", "items": {"type": "string"}}, "groupId": {"type": "string"}, "id": {"type": "string"}, "siteId": {"type": "string"}, "siteName": {"type": "string"}}, "required": ["automated", "emails", "groupId", "id", "siteId", "siteName"]}, "Group": {"type": "object", "properties": {"groupName": {"type": "string"}, "accountRef": {"type": "string"}, "groupId": {"type": "string"}, "businessName": {"type": "string"}, "businessEmail": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "town": {"type": "string"}, "county": {"type": "string"}, "postcode": {"type": "string"}, "poNumber": {"type": "string"}, "sites": {"type": "array", "items": {"$ref": "#/components/schemas/Site"}}, "stripeConnectedAccountId": {"type": "string"}, "stripeCustomerId": {"type": "string"}, "stripeSubscriptionId": {"type": "string"}, "stripeSubscriptionStatus": {"type": "string"}, "transfersEnabled": {"type": "boolean"}}, "required": ["groupName", "groupId", "transfersEnabled"]}, "AdjustedFee": {"type": "object", "properties": {"fee": {"type": "number"}, "ppid": {"type": "string"}}, "required": ["fee", "ppid"]}, "WorkItem": {"type": "object", "properties": {"automated": {"type": "boolean"}, "groupUid": {"type": "string"}, "groupName": {"type": "string"}, "id": {"type": "string"}, "month": {"type": "string"}, "previousStatement": {"$ref": "#/components/schemas/Statement"}, "siteId": {"type": "string"}, "siteName": {"type": "string"}, "statement": {"$ref": "#/components/schemas/Statement"}, "status": {"enum": ["New", "Ready", "Generating", "Generated", "Sending", "<PERSON><PERSON>", "Cancelled"], "type": "string"}, "userId": {"type": "string"}, "userName": {"type": "string"}}, "required": ["automated", "groupUid", "groupName", "id", "month", "siteId", "siteName", "status"]}, "Statement": {"type": "object", "properties": {"id": {"type": "string"}, "adjustedFees": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustedFee"}}, "automaticPayout": {"type": "boolean"}, "emails": {"type": "string"}, "energy": {"type": "object"}, "fees": {"type": "object"}, "groupUid": {"type": "string"}, "invoice": {"type": "object"}, "numberOfCharges": {"type": "number"}, "payoutStatus": {"type": "string"}, "reference": {"type": "string"}, "revenue": {"type": "object"}, "siteAddress": {"type": "string"}, "workItem": {"$ref": "#/components/schemas/WorkItem"}}, "required": ["id", "adjustedFees", "automaticPayout", "emails", "energy", "fees", "groupUid", "numberOfCharges", "reference", "revenue", "siteAddress"]}, "InvoiceDto": {"type": "object", "properties": {"id": {"type": "string"}, "invoiceNumber": {"type": "string"}, "quoteNumber": {"type": "string"}, "invoiceDate": {"type": "string"}, "group": {"$ref": "#/components/schemas/Group"}, "site": {"$ref": "#/components/schemas/Site"}, "statement": {"$ref": "#/components/schemas/Statement"}, "stripeInvoiceId": {"type": "string"}, "stripeInvoiceStatus": {"type": "string"}, "stripeInvoiceNumber": {"type": "string"}}, "required": ["id", "invoiceNumber", "quoteNumber", "invoiceDate", "group", "site", "statement"]}, "ReissueStripeInvoicesRequest": {"type": "object", "properties": {}}, "GroupChargeSummary": {"type": "object", "properties": {"chargingDuration": {"type": "number"}, "co2Savings": {"type": "number"}, "energyCost": {"type": "number"}, "energyDelivered": {"type": "number"}, "numberOfChargers": {"type": "number"}, "numberOfCharges": {"type": "number"}, "numberOfDrivers": {"type": "number"}, "numberOfSites": {"type": "number"}, "revenueGenerated": {"type": "number"}}, "required": ["chargingDuration", "co2Savings", "energyCost", "energyDelivered", "numberOfChargers", "numberOfCharges", "numberOfDrivers", "numberOfSites", "revenueGenerated"]}, "UpdateGroupRequest": {"type": "object", "properties": {"businessName": {"type": "string"}, "businessEmail": {"type": "string"}, "accountRef": {"type": "string"}, "addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "town": {"type": "string"}, "county": {"type": "string"}, "postcode": {"type": "string"}, "poNumber": {"type": "string"}}, "required": ["businessName", "businessEmail", "accountRef", "addressLine1", "town", "county", "postcode"]}, "CreateGroupRequest": {"type": "object", "properties": {"groupName": {"type": "string"}, "groupId": {"type": "string"}, "accountRef": {"type": "string"}}, "required": ["groupName", "groupId", "accountRef"]}, "Email": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255}}, "required": ["email"]}, "UpdateSiteRequest": {"type": "object", "properties": {"emails": {"type": "array", "items": {"$ref": "#/components/schemas/Email"}}, "automated": {"type": "boolean"}}, "required": ["emails", "automated"]}, "ChargeSummary": {"type": "object", "properties": {"chargingDuration": {"type": "number"}, "claimedEnergyUsage": {"type": "number"}, "co2Savings": {"type": "number"}, "energyCost": {"type": "number"}, "energyDelivered": {"type": "number"}, "energyUsage": {"type": "number"}, "numberOfCharges": {"type": "number"}, "revenueGenerated": {"type": "number"}, "revenueGeneratingClaimedUsage": {"type": "number"}}, "required": ["chargingDuration", "claimedEnergyUsage", "co2Savings", "energyCost", "energyDelivered", "energyUsage", "numberOfCharges", "revenueGenerated", "revenueGeneratingClaimedUsage"]}, "Coordinates": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "Charge": {"type": "object", "properties": {"chargingDuration": {"type": "string"}, "confirmed": {"type": "boolean"}, "confirmedBy": {"type": "string", "enum": ["driver", "other"]}, "co2Savings": {"type": "string"}, "door": {"type": "string"}, "endedAt": {"type": "string"}, "energyCost": {"type": "string"}, "energyUsage": {"type": "string"}, "locationId": {"type": "number"}, "pluggedIn": {"type": "string"}, "podName": {"type": "string"}, "ppid": {"type": "string"}, "revenueGenerated": {"type": "string"}, "startedAt": {"type": "string"}, "siteName": {"type": "string"}, "totalDuration": {"type": "string"}, "userEmail": {"type": "string"}, "userName": {"type": "string"}, "vehicle": {"type": "string"}}, "required": ["chargingDuration", "confirmed", "co2Savings", "energyCost", "energyUsage", "pluggedIn", "revenueGenerated", "startedAt"]}, "Schedule": {"type": "object", "properties": {"endDay": {"type": "number"}, "endTime": {"type": "string"}, "isActive": {"type": "boolean"}, "startDay": {"type": "number"}, "startTime": {"type": "string"}}, "required": ["endDay", "endTime", "isActive", "startDay", "startTime"]}, "Socket": {"type": "object", "properties": {"door": {"type": "string"}, "firmwareVersion": {"type": "string"}, "isUpdateAvailable": {"type": "boolean"}, "lastContact": {"type": "string"}, "serialNumber": {"type": "string"}, "status": {"type": "string", "enum": ["Available", "Charging", "Offline", "Unavailable"]}}, "required": ["door", "firmwareVersion", "isUpdateAvailable", "serialNumber", "status"]}, "TariffSummary": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "Pod": {"type": "object", "properties": {"ageYears": {"type": "number"}, "confirmChargeEnabled": {"type": "boolean"}, "chargeSummary": {"$ref": "#/components/schemas/ChargeSummary"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}, "description": {"type": "string"}, "id": {"type": "number"}, "installDate": {"type": "string"}, "isEvZone": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "lastContact": {"type": "string"}, "model": {"type": "string"}, "mostRecentCharge": {"$ref": "#/components/schemas/Charge"}, "name": {"type": "string"}, "ppid": {"type": "string"}, "recentCharges": {"type": "array", "items": {"$ref": "#/components/schemas/Charge"}}, "schedules": {"type": "array", "items": {"$ref": "#/components/schemas/Schedule"}}, "schemes": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}, "site": {"$ref": "#/components/schemas/Site"}, "sockets": {"type": "array", "items": {"$ref": "#/components/schemas/Socket"}}, "status": {"type": "string"}, "supportsConfirmCharge": {"type": "boolean"}, "supportsContactless": {"type": "boolean"}, "supportsEnergyTariff": {"type": "boolean"}, "supportsOcpp": {"type": "boolean"}, "supportsPerKwh": {"type": "boolean"}, "supportsTariffs": {"type": "boolean"}, "tariff": {"$ref": "#/components/schemas/TariffSummary"}}, "required": ["confirmChargeEnabled", "coordinates", "description", "id", "isEvZone", "isPublic", "model", "ppid", "schemes", "sockets", "status", "supportsContactless", "supportsEnergyTariff", "supportsPerKwh", "supportsTariffs"]}, "ChargerWithPod": {"type": "object", "properties": {"pod": {"$ref": "#/components/schemas/Pod"}, "fee": {"type": "number", "nullable": true, "minimum": 0, "maximum": 0.99}, "ppid": {"type": "string"}, "siteId": {"type": "string"}}, "required": ["pod", "fee", "ppid", "siteId"]}, "UpdateChargerRequest": {"type": "object", "properties": {"chargers": {"type": "array", "items": {"$ref": "#/components/schemas/ChargerWithPod"}}}, "required": ["chargers"]}, "CreateStatementRequest": {"type": "object", "properties": {"adjustedFees": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustedFee"}}, "date": {"type": "string"}, "groupUid": {"type": "string"}, "siteId": {"type": "string"}, "workItemId": {"type": "string"}}, "required": ["adjustedFees", "date", "groupUid", "siteId", "workItemId"]}, "CreateStatementResponse": {"type": "object", "properties": {"statementId": {"type": "string"}}, "required": ["statementId"]}, "CreateStripeCustomerRequest": {"type": "object", "properties": {}}, "CreateStripeConnectedAccountRequest": {"type": "object", "properties": {}}, "CreateStripeConnectedAccountAccountLinkRequest": {"type": "object", "properties": {}}, "CreateOrUpdateStripeSubscriptionRequest": {"type": "object", "properties": {"groupId": {"type": "string"}, "socketQuantity": {"type": "number", "minimum": 1}}, "required": ["groupId", "socketQuantity"]}, "SubscriptionInvoiceDto": {"type": "object", "properties": {"id": {"type": "string"}, "amount": {"type": "number"}, "created": {"type": "string"}, "due": {"type": "string"}, "email": {"type": "string"}, "hostedInvoiceUrl": {"type": "string"}, "invoiceNumber": {"type": "string"}, "invoicePdfUrl": {"type": "string"}, "status": {"type": "string"}}, "required": ["id", "amount", "created", "due", "email", "hostedInvoiceUrl", "invoiceNumber", "status"]}, "Stats": {"type": "object", "properties": {"manual": {"type": "number"}, "automated": {"type": "number"}, "total": {"type": "number"}}, "required": ["manual", "automated", "total"]}, "WorkItemStatuses": {"type": "object", "properties": {"new": {"$ref": "#/components/schemas/Stats"}, "ready": {"$ref": "#/components/schemas/Stats"}, "generated": {"$ref": "#/components/schemas/Stats"}, "sent": {"$ref": "#/components/schemas/Stats"}}, "required": ["new", "ready", "generated", "sent"]}, "StatsWorkItems": {"type": "object", "properties": {"workitems": {"$ref": "#/components/schemas/WorkItemStatuses"}}, "required": ["workitems"]}, "AssignUserToWorkItemRequest": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}}, "required": ["email", "name"]}, "UpdateAutomatedStatusRequest": {"type": "object", "properties": {"automated": {"type": "boolean"}}, "required": ["automated"]}, "UpdateWorkItemStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["New", "Ready", "Generating", "Generated", "Sending", "<PERSON><PERSON>", "Cancelled"]}}, "required": ["status"]}}}}