import { Option } from '@experience/shared/react/design-system';
import {
  WorkItem,
  WorkItemStatus,
} from '@experience/commercial/statement-service/shared';
import { formatDateToMonthYear } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

export const generateWorkItemsTableFilters = (
  workItems: WorkItem[]
): Option[][] => {
  const groups = [
    ...new Set(
      workItems
        .filter((workItem) => workItem.groupName)
        .map(({ groupName }) => groupName)
        .sort()
    ),
  ];

  const groupOptions: Option[] = [
    { id: '', name: 'All' },
    ...groups.map((groupName) => ({ id: groupName, name: groupName })),
  ];

  const months = [
    ...new Set(
      workItems
        .filter((workItem) => workItem.month)
        .map(({ month }) => month)
        .sort((a, b) => (dayjs(b).isAfter(dayjs(a)) ? 1 : -1))
    ),
  ];

  const monthsOptions = [
    { id: '', name: 'All' },
    ...months.map((month) => ({
      id: formatDateToMonthYear(month),
      name: formatDateToMonthYear(month),
    })),
  ] as Option[];

  const statusOptions = [
    { id: '', name: 'All' },
    ...Object.values(WorkItemStatus)
      .filter((status) => status !== WorkItemStatus.CANCELLED)
      .filter((status) => status !== WorkItemStatus.SENT)
      .map((status) => ({
        id: status,
        name: status,
      })),
  ];

  const assignees = [
    ...new Set(
      workItems
        .filter((workItem) => workItem.userName)
        .map(({ userName }) => userName)
        .sort()
    ),
  ];

  const assigneeOptions = [
    { id: '', name: 'All' },
    ...assignees.map((name) => ({ id: name, name })),
  ] as Option[];

  const automatedOptions = [
    { id: '', name: 'All' },
    {
      id: true,
      name: 'Yes',
    },
    {
      id: false,
      name: 'No',
    },
  ] as Option[];

  return [
    groupOptions,
    monthsOptions,
    statusOptions,
    assigneeOptions,
    automatedOptions,
  ];
};
