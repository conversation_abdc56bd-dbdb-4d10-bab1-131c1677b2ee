// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Work items table should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="groupName"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="groupName"
              id="headlessui-label-:test-id-1"
            >
              Group
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 groupName"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="groupName"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-36"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="month"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="month"
              id="headlessui-label-:test-id-9"
            >
              Month
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-9 month"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="month"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-28"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="status"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="status"
              id="headlessui-label-:test-id-17"
            >
              Status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-17 status"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="status"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="userName"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="userName"
              id="headlessui-label-:test-id-25"
            >
              Assignee
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-25 userName"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="userName"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-24"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="automated"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="automated"
              id="headlessui-label-:test-id-33"
            >
              Automated
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-33 automated"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="automated"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="infinite-scroll-component__outerdiv"
    >
      <div
        class="infinite-scroll-component "
        style="height: auto; overflow: auto;"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of work items
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Group
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Site
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Month
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      >
                        <svg
                          class="fill-current h-4 w-4 stroke-current stroke-2"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            transform="translate(4 4)"
                          >
                            <path
                              d="m20.9,8.67L12.81.58c-.14-.16-.34-.27-.56-.27,0,0,0,0,0,0-.19,0-.39.07-.54.22L3.1,9.13c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l7.34-7.34v20.09c0,.41.34.75.75.75s.75-.34.75-.75V2.89l6.85,6.85c.29.29.77.29,1.06,0s.29-.77,0-1.06Z"
                            />
                          </g>
                        </svg>
                      </span>
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Assignee
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer justify-center"
                      type="button"
                    >
                      Automated
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/9ddd3c9e-03b8-470f-8f16-32bc24674a10"
                    >
                      EDF
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    EDF Head Office
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    January 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Ready"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Ready
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Randy Marsh
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-primary"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Yes
                        </title>
                        <g>
                          <path
                            d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                          />
                          <path
                            d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    />
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/9ddd3c9e-03b8-470f-8f16-32bc24674a10"
                    >
                      EDF
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    EDF Head Office
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    February 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Ready"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Ready
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Jane Rogers
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <form>
                        <input
                          name="workItemId"
                          type="hidden"
                          value="0ceaa635-70ed-4962-affa-ed5c3c458857"
                        />
                        <button
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          type="submit"
                        >
                          Remove assignee
                        </button>
                      </form>
                      <button
                        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                        type="button"
                      >
                        Generate statement
                      </button>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/90d06ff8-c92b-484a-8051-c0dfab6b97f6"
                    >
                      DPD
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    DPD Staff Car Park
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    March 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="New"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      New
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  />
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    />
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/312c21e0-db45-478c-bf22-fef39a8cb99b"
                    >
                      Tesco
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Tesco Wembley
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    March 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Ready"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Ready
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Melissa Moore
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <form>
                        <input
                          name="email"
                          type="hidden"
                          value="<EMAIL>"
                        />
                        <input
                          name="name"
                          type="hidden"
                          value="Bob Dylan"
                        />
                        <input
                          name="workItemId"
                          type="hidden"
                          value="72900c87-820c-4324-a1ee-14868b43a7b1"
                        />
                        <button
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          type="submit"
                        >
                          Assign to me
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/312c21e0-db45-478c-bf22-fef39a8cb99b"
                    >
                      Tesco
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Tesco Westminster
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    April 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="New"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      New
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  />
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-primary"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Yes
                        </title>
                        <g>
                          <path
                            d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                          />
                          <path
                            d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div>
                        <button
                          aria-label="Set to manual 96ea4743-89f3-41d7-a1e5-81c56ed61349"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          data-testid="set-to-manual-96ea4743-89f3-41d7-a1e5-81c56ed61349"
                          id="set-to-manual-96ea4743-89f3-41d7-a1e5-81c56ed61349"
                          name="set-to-manual-96ea4743-89f3-41d7-a1e5-81c56ed61349"
                          type="button"
                        >
                          Set to manual
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/312c21e0-db45-478c-bf22-fef39a8cb99b"
                    >
                      Tesco
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Tesco Plymouth
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    May 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Sent"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Sent
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Jane Rogers
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-primary"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          Yes
                        </title>
                        <g>
                          <path
                            d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                          />
                          <path
                            d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    />
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/99e7ae06-0b56-46fb-b3f4-0d01843045c7"
                    >
                      Lidl
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Lidl Dundee
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    June 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Generated"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Generated
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Jane Rogers
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <form>
                        <input
                          name="email"
                          type="hidden"
                          value="<EMAIL>"
                        />
                        <input
                          name="name"
                          type="hidden"
                          value="Bob Dylan"
                        />
                        <input
                          name="workItemId"
                          type="hidden"
                          value="156642de-b237-4aec-b87a-749213e7b98d"
                        />
                        <button
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          type="submit"
                        >
                          Assign to me
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/312c21e0-db45-478c-bf22-fef39a8cb99b"
                    >
                      Tesco
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Tesco Sheffield
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    June 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="New"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      New
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Melissa Moore
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    />
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/99e7ae06-0b56-46fb-b3f4-0d01843045c7"
                    >
                      Lidl
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Lidl Edinburgh West
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    November 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="Generated"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      Generated
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Eric Cartman
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <form>
                        <input
                          name="workItemId"
                          type="hidden"
                          value="0bb8124c-1ca8-4068-b702-e216f73f7956"
                        />
                        <button
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          type="submit"
                        >
                          Remove assignee
                        </button>
                      </form>
                      <div>
                        <button
                          aria-label="Mark sent 0bb8124c-1ca8-4068-b702-e216f73f7956"
                          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-transparent border-black text-black hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral py-1.5 px-3.75"
                          data-testid="mark-sent-0bb8124c-1ca8-4068-b702-e216f73f7956"
                          id="mark-sent-0bb8124c-1ca8-4068-b702-e216f73f7956"
                          name="mark-sent-0bb8124c-1ca8-4068-b702-e216f73f7956"
                          type="button"
                        >
                          Mark sent
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/90d06ff8-c92b-484a-8051-c0dfab6b97f6"
                    >
                      DPD
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    DPD Depot
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    December 2020
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="New"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral"
                      role="status"
                    >
                      New
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  />
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex justify-center xl:w-1/2"
                    >
                      <svg
                        class="fill-current h-6 w-6 text-error"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>
                          No
                        </title>
                        <g>
                          <path
                            d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                          />
                          <path
                            d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                          />
                        </g>
                      </svg>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
