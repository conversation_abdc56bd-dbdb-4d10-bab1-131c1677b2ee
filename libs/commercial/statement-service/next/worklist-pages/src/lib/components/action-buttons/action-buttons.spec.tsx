import {
  TEST_AZURE_USER,
  TEST_WORK_ITEM,
  TEST_WORK_ITEM_WITH_STATEMENT,
  WorkItemStatus,
} from '@experience/commercial/statement-service/shared';
import { render, screen } from '@testing-library/react';
import ActionButtons from './action-buttons';

const defaultProps = {
  email: TEST_AZURE_USER.email as string,
  setOpenMarkSentModal: jest.fn(),
  setWorkItemForModal: jest.fn(),
  setOpenSetToManualModal: jest.fn(),
  userName: TEST_AZURE_USER.name as string,
  workItem: TEST_WORK_ITEM,
};

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  useFormStatus: () => ({
    pending: false,
  }),
}));

describe('Action buttons', () => {
  describe('Status Ready', () => {
    it('should only render Assign to me button', () => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={{
            ...TEST_WORK_ITEM,
            status: WorkItemStatus.READY,
            userId: undefined,
          }}
        />
      );

      expect(
        screen.getByRole('button', { name: 'Assign to me' })
      ).toBeInTheDocument();

      expect(
        screen.queryByRole('button', { name: 'Remove assignee' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Generate statement' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Mark sent/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /View.../i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Download.../i })
      ).not.toBeInTheDocument();
    });
  });

  describe('Status Generated', () => {
    it('should only render remove user, view menu, download menu and mark as sent buttons', () => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={TEST_WORK_ITEM_WITH_STATEMENT}
        />
      );

      expect(
        screen.getByRole('button', { name: 'Remove assignee' })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /View.../i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /Download.../i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /Mark sent/i })
      ).toBeInTheDocument();

      expect(
        screen.queryByRole('button', { name: 'Assign to me' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Generate statement' })
      ).not.toBeInTheDocument();
    });

    it('should render view menu button even if work item is unassigned', () => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={{
            ...TEST_WORK_ITEM_WITH_STATEMENT,
            status: WorkItemStatus.GENERATED,
            userId: undefined,
          }}
        />
      );

      expect(
        screen.queryByRole('button', { name: 'Assign to me' })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /View.../i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /Download.../i })
      ).toBeInTheDocument();

      expect(
        screen.queryByRole('button', { name: 'Remove assignee' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Mark sent/i })
      ).not.toBeInTheDocument();
    });
  });

  describe('Status New', () => {
    it('should only render set to manual action button if automated is set to true', () => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={{
            ...TEST_WORK_ITEM,
            status: WorkItemStatus.NEW,
            automated: true,
          }}
        />
      );

      expect(
        screen.getByRole('button', { name: /Set to manual/i })
      ).toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Remove assignee' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Mark sent/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Assign to me' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Generate statement' })
      ).not.toBeInTheDocument();
    });

    it('should not render any action buttons if automated is set to false', () => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={{
            ...TEST_WORK_ITEM,
            status: WorkItemStatus.NEW,
            automated: false,
          }}
        />
      );

      expect(
        screen.queryByRole('button', { name: /Set to manual/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Remove assignee' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Mark sent/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Assign to me' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Generate statement' })
      ).not.toBeInTheDocument();
    });
  });

  it.each([WorkItemStatus.GENERATED, WorkItemStatus.READY])(
    'should not render any action buttons if automated is set to true and status is set to %s',
    (status) => {
      render(
        <ActionButtons
          {...defaultProps}
          workItem={{
            ...TEST_WORK_ITEM,
            status,
            automated: true,
          }}
        />
      );

      expect(
        screen.queryByRole('button', { name: /Set to manual/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Remove assignee' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: /Mark sent/i })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Assign to me' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'Generate statement' })
      ).not.toBeInTheDocument();
    }
  );
});
