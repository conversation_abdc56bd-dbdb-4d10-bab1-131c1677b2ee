import { TEST_WORK_ITEMS } from '@experience/commercial/statement-service/shared';
import { generateWorkItemsTableFilters } from './work-items-table-filters';

describe('work item table filters', () => {
  it('should generate the correct filter options', () => {
    const [
      groupOptions,
      monthsOptions,
      statusOptions,
      assigneeOptions,
      automatedOptions,
    ] = generateWorkItemsTableFilters(TEST_WORK_ITEMS);

    expect(groupOptions).toEqual([
      { id: '', name: 'All' },
      { id: 'DPD', name: 'DPD' },
      { id: 'EDF', name: 'EDF' },
      { id: 'Lidl', name: 'Lidl' },
      { id: 'Tesco', name: 'Tesco' },
    ]);
    expect(monthsOptions).toEqual([
      { id: '', name: 'All' },
      { id: 'December 2020', name: 'December 2020' },
      { id: 'November 2020', name: 'November 2020' },
      { id: 'June 2020', name: 'June 2020' },
      { id: 'May 2020', name: 'May 2020' },
      { id: 'April 2020', name: 'April 2020' },
      { id: 'March 2020', name: 'March 2020' },
      { id: 'February 2020', name: 'February 2020' },
      { id: 'January 2020', name: 'January 2020' },
    ]);
    expect(statusOptions).toEqual([
      { id: '', name: 'All' },
      { id: 'New', name: 'New' },
      { id: 'Ready', name: 'Ready' },
      { id: 'Generating', name: 'Generating' },
      { id: 'Generated', name: 'Generated' },
      { id: 'Sending', name: 'Sending' },
    ]);
    expect(assigneeOptions).toEqual([
      { id: '', name: 'All' },
      { id: 'Eric Cartman', name: 'Eric Cartman' },
      { id: 'Jane Rogers', name: 'Jane Rogers' },
      { id: 'Melissa Moore', name: 'Melissa Moore' },
      { id: 'Randy Marsh', name: 'Randy Marsh' },
    ]);
    expect(automatedOptions).toEqual([
      { id: '', name: 'All' },
      { id: true, name: 'Yes' },
      { id: false, name: 'No' },
    ]);
  });
});
