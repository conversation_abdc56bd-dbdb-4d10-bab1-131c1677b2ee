import {
  An<PERSON>,
  Badge,
  CheckmarkCircleIcon,
  CrossCircleIcon,
  IconCell,
  InfiniteTable,
} from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import { WorkItem } from '@experience/commercial/statement-service/shared';
import { formatDateToMonthYear } from '@experience/shared/typescript/utils';
import { generateWorkItemsTableFilters } from './work-items-table-filters';
import { useState } from 'react';
import ActionButtons from '../action-buttons/action-buttons';
import MarkSentModal from '../mark-sent-modal/mark-sent-modal';
import SetToManualModal from '../set-to-manual-modal/set-to-manual-modal';

interface WorkListTableProps {
  email: string;
  workItems: WorkItem[];
  userName: string;
}

export const WorkItemsTable = ({
  workItems,
  email,
  userName,
}: WorkListTableProps) => {
  const [openMarkSentModal, setOpenMarkSentModal] = useState(false);
  const [openSetToManualModal, setOpenSetToManualModal] = useState(false);
  const [workItem, setWorkItem] = useState<WorkItem>(workItems[0]);

  const [
    groupOptions,
    monthsOptions,
    statusOptions,
    assigneeOptions,
    automatedOptions,
  ] = generateWorkItemsTableFilters(workItems);

  const columns: ColumnDef<WorkItem>[] = [
    {
      accessorKey: 'groupName',
      header: () => 'Group',
      sortingFn: 'alphanumeric',
      cell: ({ row: { original: workItem } }) => (
        <Anchor href={`/groups/${workItem.groupUid}`}>
          {workItem.groupName}
        </Anchor>
      ),
    },
    {
      accessorKey: 'siteName',
      header: () => 'Site',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'month',
      accessorFn: ({ month }) => formatDateToMonthYear(month),
      header: () => 'Month',
      sortingFn: 'dateTimeSort',
    },
    {
      accessorKey: 'status',
      header: () => 'Status',
      cell: ({ getValue: status }) => (
        <Badge.Neutral label={status() as string} />
      ),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'userName',
      accessorFn: (workItem) => workItem.userName ?? '',
      header: () => 'Assignee',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'automated',
      cell: ({ getValue: automated }) => (
        <IconCell
          icon={
            automated() ? (
              <CheckmarkCircleIcon.LIGHT
                height="h-6"
                className="text-primary"
                width="w-6"
                title="Yes"
              />
            ) : (
              <CrossCircleIcon.LIGHT
                className="text-error"
                height="h-6"
                width="w-6"
                title="No"
              />
            )
          }
        />
      ),
      header: () => 'Automated',
      meta: {
        sortButtonClassName: 'justify-center',
      },
      sortingFn: 'basic',
    },
    {
      id: 'actions',
      header: () => 'Actions',
      cell: ({ row: { original: workItem } }) => (
        <div className="flex space-x-2">
          <ActionButtons
            email={email}
            setOpenMarkSentModal={setOpenMarkSentModal}
            setWorkItemForModal={setWorkItem}
            setOpenSetToManualModal={setOpenSetToManualModal}
            userName={userName}
            workItem={workItem}
          />
        </div>
      ),
    },
  ];

  return (
    <>
      <InfiniteTable
        caption="Table of work items"
        columns={columns}
        data={workItems}
        id="work-items-table"
        initialSort={[{ id: 'month', desc: false }]}
        filters={{
          clientSide: [
            {
              columnId: 'groupName',
              label: 'Group',
              options: groupOptions,
              width: 'w-48',
            },
            {
              columnId: 'month',
              label: 'Month',
              options: monthsOptions,
              width: 'w-36',
            },
            {
              columnId: 'status',
              label: 'Status',
              options: statusOptions,
              width: 'w-28',
            },
            {
              columnId: 'userName',
              label: 'Assignee',
              options: assigneeOptions,
              width: 'w-48',
            },
            {
              columnId: 'automated',
              label: 'Automated',
              options: automatedOptions,
              width: 'w-24',
            },
          ],
        }}
        showSearchField
      />
      <MarkSentModal
        open={openMarkSentModal}
        setOpen={setOpenMarkSentModal}
        workItem={workItem}
      />
      <SetToManualModal
        open={openSetToManualModal}
        setOpen={setOpenSetToManualModal}
        workItem={workItem}
      />
    </>
  );
};
