// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StatementFeesReadOnlyPage should match snapshot with adjusted fees 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Statement Fees
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          List of 1 charger at this site and their fees. Fees have been adjusted for 1 charger based on a previous statement.
        </p>
      </div>
    </header>
    <form
      id="statement-fees-read-only-form"
      novalidate=""
    >
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of site statement fees for each charger
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Name
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    PPID
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Installation date
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    >
                      <svg
                        class="fill-current h-4 w-4 stroke-current stroke-2"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          transform="translate(4 4)"
                        >
                          <path
                            d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Age (years)
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Default fee
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Adjusted fee
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  FOO
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  BAR
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  12/04/2021
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  4
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  £0.02
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <div
                    class=""
                  >
                    <label
                      class="text-md font-bold sr-only block mb-2"
                      for="adjustedFees.0.fee"
                    >
                      BAR fee
                    </label>
                  </div>
                  <div
                    class="relative"
                  >
                    <span
                      class="inline-flex items-center space-x-4 absolute inset-y-2 px-2 text-neutral"
                    >
                      £
                    </span>
                    <input
                      class="border-none pl-8 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                      disabled=""
                      id="adjustedFees.0.fee"
                      max="1"
                      min="0.01"
                      step="0.01"
                      type="number"
                      value=""
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="pb-4"
      />
    </form>
    <div
      class="flex justify-end"
    >
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="button"
      >
        View statement
      </button>
    </div>
  </div>
</body>
`;

exports[`StatementFeesReadOnlyPage should match snapshot with multiple adjusted fees 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Statement Fees
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          List of 1 charger at this site and their fees. Fees have been adjusted for 2 chargers based on a previous statement.
        </p>
      </div>
    </header>
    <form
      id="statement-fees-read-only-form"
      novalidate=""
    >
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of site statement fees for each charger
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Name
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    PPID
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Installation date
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    >
                      <svg
                        class="fill-current h-4 w-4 stroke-current stroke-2"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          transform="translate(4 4)"
                        >
                          <path
                            d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Age (years)
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Default fee
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Adjusted fee
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  FOO
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  BAR
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  12/04/2021
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  4
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  £0.02
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <div
                    class=""
                  >
                    <label
                      class="text-md font-bold sr-only block mb-2"
                      for="adjustedFees.0.fee"
                    >
                      BAR fee
                    </label>
                  </div>
                  <div
                    class="relative"
                  >
                    <span
                      class="inline-flex items-center space-x-4 absolute inset-y-2 px-2 text-neutral"
                    >
                      £
                    </span>
                    <input
                      class="border-none pl-8 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                      disabled=""
                      id="adjustedFees.0.fee"
                      max="1"
                      min="0.01"
                      step="0.01"
                      type="number"
                      value=""
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="pb-4"
      />
    </form>
    <div
      class="flex justify-end"
    >
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="button"
      >
        View statement
      </button>
    </div>
  </div>
</body>
`;

exports[`StatementFeesReadOnlyPage should match snapshot without adjusted fees 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Statement Fees
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          List of 1 charger at this site and their fees
        </p>
      </div>
    </header>
    <form
      id="statement-fees-read-only-form"
      novalidate=""
    >
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of site statement fees for each charger
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Name
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    PPID
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Installation date
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    >
                      <svg
                        class="fill-current h-4 w-4 stroke-current stroke-2"
                        viewBox="0 0 32 32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          transform="translate(4 4)"
                        >
                          <path
                            d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  <button
                    class="flex items-center hover:underline text-left cursor-pointer"
                    type="button"
                  >
                    Age (years)
                    <span
                      class="w-4 h-4 ml-1 text-neutral shrink-0"
                    />
                  </button>
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Default fee
                </th>
                <th
                  class="text-left p-3 align-top"
                  scope="col"
                >
                  Adjusted fee
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  FOO
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  BAR
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  12/04/2021
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  4
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  £0.02
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <div
                    class=""
                  >
                    <label
                      class="text-md font-bold sr-only block mb-2"
                      for="adjustedFees.0.fee"
                    >
                      BAR fee
                    </label>
                  </div>
                  <div
                    class="relative"
                  >
                    <span
                      class="inline-flex items-center space-x-4 absolute inset-y-2 px-2 text-neutral"
                    >
                      £
                    </span>
                    <input
                      class="border-none pl-8 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
                      disabled=""
                      id="adjustedFees.0.fee"
                      max="1"
                      min="0.01"
                      step="0.01"
                      type="number"
                      value=""
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="pb-4"
      />
    </form>
    <div
      class="flex justify-end"
    >
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="button"
      >
        View statement
      </button>
    </div>
  </div>
</body>
`;
