// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`statements table should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="workItem_groupId"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="workItem_groupId"
              id="headlessui-label-:test-id-1"
            >
              Group
            </label>
            <div
              class="relative"
              data-headlessui-state=""
            >
              <span
                class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                    />
                  </g>
                </svg>
              </span>
              <input
                aria-autocomplete="list"
                aria-expanded="false"
                autocomplete="off"
                class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-white w-full"
                data-headlessui-state=""
                id="workItem_groupId"
                name="workItem_groupId"
                placeholder=""
                role="combobox"
                type="text"
                value=""
              />
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="workItem_month"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="workItem_month"
              id="headlessui-label-:test-id-9"
            >
              Month
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-9 workItem_month"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="workItem_month"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  March 2025
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="invoice_stripeInvoiceStatus"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="invoice_stripeInvoiceStatus"
              id="headlessui-label-:test-id-17"
            >
              Fee invoice status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-17 invoice_stripeInvoiceStatus"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="invoice_stripeInvoiceStatus"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="w-48"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="payoutStatus"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="payoutStatus"
              id="headlessui-label-:test-id-25"
            >
              Revenue payout status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-25 payoutStatus"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="payoutStatus"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="infinite-scroll-component__outerdiv"
    >
      <div
        class="infinite-scroll-component "
        style="height: auto; overflow: auto;"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of statements
              </caption>
              <thead
                class="border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Group
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Site
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Month
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      >
                        <svg
                          class="fill-current h-4 w-4 stroke-current stroke-2"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            transform="translate(4 4)"
                          >
                            <path
                              d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                            />
                          </g>
                        </svg>
                      </span>
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Fee invoice number
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Fee invoice status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Revenue payout status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/725fead1-a43b-468b-a500-a279d8a47f95"
                    >
                      Group 1
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Site 1
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    February 2023
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    ADF-1002
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="overdue"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                      role="status"
                    >
                      overdue
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="transferred"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                      role="status"
                    >
                      transferred
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-34"
                          type="button"
                        >
                          <span>
                            View...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-40"
                          type="button"
                        >
                          <span>
                            Download...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-46"
                          type="button"
                        >
                          <span>
                            Actions...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/725fead1-a43b-468b-a500-a279d8a47f95"
                    >
                      Pod Point
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Banner Street
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    February 2023
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    ADF-1003
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="paid"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                      role="status"
                    >
                      paid
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="paid out"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                      role="status"
                    >
                      paid out
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-52"
                          type="button"
                        >
                          <span>
                            View...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-58"
                          type="button"
                        >
                          <span>
                            Download...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-64"
                          type="button"
                        >
                          <span>
                            Actions...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/725fead1-a43b-468b-a500-a279d8a47f95"
                    >
                      Tesco
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Banner Street
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    February 2023
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    ADF-1004
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="paid"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                      role="status"
                    >
                      paid
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="warning"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-error/20 border border-error/50 text-error capitalize"
                      role="status"
                    >
                      warning
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-70"
                          type="button"
                        >
                          <span>
                            View...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-76"
                          type="button"
                        >
                          <span>
                            Download...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-82"
                          type="button"
                        >
                          <span>
                            Actions...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <a
                      class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
                      href="/groups/725fead1-a43b-468b-a500-a279d8a47f95"
                    >
                      Group 1
                    </a>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Site 1
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    January 2023
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    ADF-1001
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="open"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                      role="status"
                    >
                      open
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="pending"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                      role="status"
                    >
                      pending
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-88"
                          type="button"
                        >
                          <span>
                            View...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-94"
                          type="button"
                        >
                          <span>
                            Download...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-100"
                          type="button"
                        >
                          <span>
                            Actions...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
