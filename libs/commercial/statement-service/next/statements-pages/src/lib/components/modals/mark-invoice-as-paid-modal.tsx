import { InvoiceDto } from '@experience/commercial/statement-service/shared';
import {
  Modal,
  ModalProps,
  Paragraph,
} from '@experience/shared/react/design-system';
import { updateInvoiceStatus } from '@experience/commercial/statement-service/next/actions';
import { useErrorHandler } from '@experience/shared/react/hooks';
import { useState } from 'react';
import toast from 'react-hot-toast';

interface MarkInvoiceAsPaidModalProps {
  invoice: InvoiceDto;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const MarkInvoiceAsPaidModal = ({
  invoice,
  open,
  setOpen,
}: MarkInvoiceAsPaidModalProps) => {
  const formId = `mark-invoice-as-paid-${invoice.id}`;
  const handleError = useErrorHandler();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCancel = () => {
    setOpen(false);
  };

  const handleConfirm = async (): Promise<void> => {
    try {
      setIsSubmitting(true);
      await updateInvoiceStatus(invoice.id, 'paid');
      toast.success(
        `Invoice ${invoice.stripeInvoiceNumber} marked as paid successfully`
      );
      setOpen(false);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const content = (
    <Paragraph>{`Are you sure you want to mark invoice ${invoice.stripeInvoiceNumber} as paid?`}</Paragraph>
  );

  const modalProps: ModalProps = {
    cancelButtonText: 'Cancel',
    confirmButtonText: 'Confirm',
    content,
    formId,
    handleCancel,
    handleConfirm,
    isSubmitting,
    open,
    setOpen,
    title: 'Mark invoice as paid',
    width: 'w-[28rem]',
  };

  return <Modal {...modalProps} />;
};
