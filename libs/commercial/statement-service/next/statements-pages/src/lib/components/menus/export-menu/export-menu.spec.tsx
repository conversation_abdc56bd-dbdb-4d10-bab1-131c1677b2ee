import { ExportMenu } from './export-menu';
import { fireEvent, render, screen } from '@testing-library/react';

const defaultProps = {
  date: '2024-01-01',
};

describe('export menu', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<ExportMenu {...defaultProps} />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<ExportMenu {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should call the ifs export endpoint when the menu item is clicked', async () => {
    jest.spyOn(window, 'open').mockImplementation(jest.fn());

    render(<ExportMenu {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Export...' }));
    fireEvent.click(
      screen.getByRole('menuitem', { name: 'Export invoices upload file' })
    );

    expect(window.open).toHaveBeenCalledWith(
      '/api/invoices/export/ifs?date=2024-01-01'
    );
  });
});
