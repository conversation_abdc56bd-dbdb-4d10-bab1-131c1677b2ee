// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Group page should match snapshot 1`] = `
<body>
  <div>
    <header
      class="grid grid-cols-[1fr,max-content] grid-rows-2 mb-4"
    >
      <div
        class="flex items-baseline col-span-2"
      >
        <h1
          class="text-2xl font-medium"
        >
          Test Group Inc.
        </h1>
      </div>
      <div
        class="flex items-center col-span-2"
      >
        <p
          class="text-md font-normal break-words"
        >
          The business details used to populate invoices for sites in this group
        </p>
      </div>
    </header>
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="flex justify-between items-center"
        >
          <h3
            class="text-lg font-bold pb-1"
          >
            Business address
          </h3>
          <button
            aria-label="Edit business address"
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-business-address"
            id="edit-business-address"
            name="edit-business-address"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <p
          class="text-md font-normal break-words"
        >
          Flat 1
        </p>
        <p
          class="text-md font-normal break-words"
        >
          1 Test Street
        </p>
        <p
          class="text-md font-normal break-words"
        >
          Test Town
        </p>
        <p
          class="text-md font-normal break-words"
        >
          Test County
        </p>
        <p
          class="text-md font-normal break-words"
        >
          TE1 1ST
        </p>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="flex justify-between items-center"
        >
          <h3
            class="text-lg font-bold pb-1"
          >
            Business name
          </h3>
          <button
            aria-label="Edit business details"
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-business-details"
            id="edit-business-details"
            name="edit-business-details"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <p
          class="text-md font-normal break-words"
        >
          Test Group Inc Invoice office
        </p>
        <h3
          class="text-lg font-bold pb-2 pt-2"
        >
          Business email address
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          <EMAIL>
        </p>
        <h3
          class="text-lg font-bold pb-2 pt-2"
        >
          Account reference
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          *********
        </p>
        <h3
          class="text-lg font-bold pb-2 pt-2"
        >
          PO number
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          UK10010001
        </p>
        <h3
          class="text-lg font-bold pb-2 pt-2"
        >
          VAT registered
        </h3>
        <p
          class="text-md font-normal break-words"
        >
          Yes
        </p>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="h-full flex flex-col"
        >
          <p
            class="text-md font-normal h-full text-center break-words"
          >
            Complete the business details for this group then create a Stripe customer for invoicing
          </p>
          <div
            class="pb-4"
          />
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 mx-auto"
            data-testid="create-stripe-customer"
            id="create-stripe-customer"
            type="button"
          >
            1. Create Stripe customer
          </button>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="h-full flex flex-col"
        >
          <p
            class="text-md font-normal h-full text-center break-words"
          >
            Once the Stripe customer is created, add a subscription for the number of sockets
          </p>
          <div
            class="pb-4"
          />
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 mx-auto"
            data-testid="create-subscription"
            disabled=""
            id="create-subscription"
            type="button"
          >
            2. Add a subscription
          </button>
        </div>
      </section>
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="h-full flex flex-col"
        >
          <p
            class="text-md font-normal h-full text-center break-words"
          >
            Set up revenue payouts from tariffs by generating a link for the customer to onboard to Stripe and connect their bank account
          </p>
          <div
            class="pb-4"
          />
          <button
            class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 mx-auto"
            data-testid="create-stripe-connected-account"
            disabled=""
            id="create-stripe-connected-account"
            type="button"
          >
            3. Enable revenue payouts
          </button>
        </div>
      </section>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Sites
    </h2>
    <p
      class="text-md font-normal break-words"
    >
      Sites in this group. Only sites with a work item in any status will appear
    </p>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of sites
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Site
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Recipients email address
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Automated
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Pod Point
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p>
                  <EMAIL>, <EMAIL>
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex justify-center xl:w-1/2"
                >
                  <svg
                    class="fill-current h-6 w-6 text-primary"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      Yes
                    </title>
                    <g>
                      <path
                        d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                      />
                      <path
                        d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                      />
                    </g>
                  </svg>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="Edit site Pod Point"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    id="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    name="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Edit fees Pod Point"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    id="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    name="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="m21.2,21.28c-.13-.2-.35-.29-.65-.29-.11,0-.24.02-.38.07-.14.04-.29.14-.44.29-.28.24-.63.43-1.05.57s-.93.21-1.54.21c-.74,0-1.57-.12-2.47-.36-.91-.24-1.83-.47-2.78-.7-.95-.23-1.88-.34-2.8-.34-.32,0-.64.01-.95.04.26-.4.49-.83.67-1.32.25-.68.38-1.42.38-2.23,0-.7-.09-1.41-.26-2.14-.18-.73-.38-1.49-.61-2.28-.04-.13-.07-.26-.11-.39h7.52c.26,0,.49-.08.67-.25.18-.16.28-.38.28-.64s-.09-.49-.28-.67c-.19-.19-.41-.28-.67-.28h-7.98c-.02-.08-.04-.16-.06-.25-.19-.86-.28-1.76-.28-2.7,0-1.11.21-2.1.62-2.96.41-.86,1-1.54,1.77-2.03.76-.49,1.68-.74,2.75-.74s1.99.2,2.83.61,1.53.94,2.08,1.62c.09.11.19.19.31.23s.25.07.38.07c.26,0,.51-.11.74-.33.23-.22.34-.45.34-.69,0-.17-.07-.35-.2-.54s-.31-.38-.52-.57c-.65-.76-1.52-1.34-2.6-1.72-1.08-.38-2.2-.57-3.36-.57-1.46,0-2.73.32-3.81.95-1.08.63-1.91,1.52-2.49,2.67-.58,1.15-.87,2.48-.87,4.01,0,.96.09,1.87.26,2.72.02.08.04.15.05.23h-1.98c-.28,0-.51.09-.69.26s-.26.39-.26.65.09.48.26.66.4.26.69.26h2.43c.04.13.07.26.11.39.23.79.44,1.56.62,2.32.18.76.28,1.54.28,2.32s-.11,1.49-.34,2.11c-.23.62-.5,1.14-.8,1.54-.02.03-.04.05-.07.08-.16.04-.33.08-.49.13-.59.17-1.26.37-2,.59-.28.09-.5.22-.64.39-.14.17-.21.38-.21.62,0,.2.07.39.21.57s.34.28.61.28c.15,0,.36-.07.62-.2.26-.13.55-.23.85-.29.22-.04.53-.13.95-.25.41-.12.89-.23,1.44-.34.55-.11,1.09-.16,1.64-.16.61,0,1.26.08,1.95.23.69.15,1.39.32,2.11.51.72.19,1.43.35,2.14.51.71.15,1.39.23,2.05.23.48,0,1.02-.08,1.6-.23.59-.15,1.13-.38,1.64-.69.28-.13.52-.29.7-.49.18-.2.28-.44.28-.72,0-.2-.07-.39-.2-.59Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="pb-6"
    />
    <h2
      class="text-2xl"
    >
      Statements
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="infinite-scroll-component__outerdiv"
    >
      <div
        class="infinite-scroll-component "
        style="height: auto; overflow: auto;"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of statements
              </caption>
              <thead
                class="border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Site
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Month
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      >
                        <svg
                          class="fill-current h-4 w-4 stroke-current stroke-2"
                          viewBox="0 0 32 32"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            transform="translate(4 4)"
                          >
                            <path
                              d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                            />
                          </g>
                        </svg>
                      </span>
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Fee invoice number
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Fee invoice status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    <button
                      class="flex items-center hover:underline text-left cursor-pointer"
                      type="button"
                    >
                      Revenue payout status
                      <span
                        class="w-4 h-4 ml-1 text-neutral shrink-0"
                      />
                    </button>
                  </th>
                  <th
                    class="text-left p-3 align-top"
                    scope="col"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral-100"
              >
                <tr
                  class="border-b-neutral-100"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    Site 1
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    February 2023
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    N/A
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="no stripe invoice"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-neutral/20 border border-neutral/30 text-neutral capitalize"
                      role="status"
                    >
                      no stripe invoice
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <span
                      aria-label="pending"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/20 border border-warning/50 text-brick/70 capitalize"
                      role="status"
                    >
                      pending
                    </span>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex space-x-2"
                    >
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-2"
                          type="button"
                        >
                          <span>
                            View...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-8"
                          type="button"
                        >
                          <span>
                            Download...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <div
                        class="relative inline-block text-left"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="menu"
                          class="bg-white border-black border-2 border-solid rounded-lg px-4 py-1.5 hover:bg-neutral/10 focus:bg-neutral/10 active:bg-neutral/20 disabled:border-neutral disabled:text-neutral font-bold inline-flex items-center text-black cursor-pointer disabled:cursor-not-allowed"
                          data-headlessui-state=""
                          id="headlessui-menu-button-:test-id-14"
                          type="button"
                        >
                          <span>
                            Actions...
                          </span>
                          <svg
                            class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g>
                              <path
                                d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                              />
                            </g>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-6"
    />
    <h2
      class="text-2xl"
    >
      Subscription payments
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of subscription payments
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Created
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Amount
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Due
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Invoice number
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Customer email
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <span
                  aria-label="paid"
                  class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                  role="status"
                >
                  paid
                </span>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-01-14
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-02-11
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                ADF-1001
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <EMAIL>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="View invoice payment page"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    id="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    name="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M25.05,6.7h-7.94c-.55,0-1,.45-1,1s.45,1,1,1h5.57l-14.26,14.43c-.39,.39-.38,1.03,0,1.41,.2,.19,.45,.29,.7,.29s.52-.1,.71-.3l14.21-14.38v4.66c0,.55,.45,1,1,1s1-.45,1-1V7.7c0-.55-.45-1-1-1Z"
                        />
                        <path
                          d="M26.83,.17H5.17C2.42,.17,.17,2.42,.17,5.17V26.83c0,2.76,2.24,5,5,5H26.83c2.76,0,5-2.24,5-5V5.17C31.83,2.42,29.58,.17,26.83,.17Zm3,26.66c0,1.65-1.35,3-3,3H5.17c-1.65,0-3-1.35-3-3V5.17c0-1.65,1.35-3,3-3H26.83c1.65,0,3,1.35,3,3V26.83Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Download pdf ADF-1001"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="download-pdf-ADF-1001"
                    id="download-pdf-ADF-1001"
                    name="download-pdf-ADF-1001"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="m20.73,14.27l-3.65-3.65c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.38,2.38h-7.49c-.41,0-.75.34-.75.75s.34.75.75.75h7.48l-2.17,2.17c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l3.44-3.44c.15-.14.24-.33.24-.54,0,0,0,0,0,0s0,0,0,0c0-.2-.08-.39-.22-.53Z"
                      />
                      <path
                        d="m20.91,7.14s0-.02,0-.03c0-.03-.01-.06-.02-.09-.11-.83-.46-1.61-1.06-2.23l-3.22-3.33c-.63-.65-1.48-1.05-2.37-1.16-.05-.01-.11-.03-.16-.03-.02,0-.03,0-.05.01-.08,0-.16-.03-.25-.03h-7.99c-1.52,0-2.75,1.23-2.75,2.75v18.01c0,1.52,1.23,2.75,2.75,2.75h12.4c1.52,0,2.75-1.23,2.75-2.75v-1.49c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.49c0,.69-.56,1.25-1.25,1.25H5.8c-.69,0-1.25-.56-1.25-1.25V2.99c0-.69.56-1.25,1.25-1.25h7.54v3.36c0,1.52,1.23,2.75,2.75,2.75h3.36v1.79c0,.41.34.75.75.75s.75-.34.75-.75v-2.12c0-.13-.02-.25-.04-.38Zm-6.07-2.03V1.99c.26.12.5.29.71.5l3.22,3.33c.15.16.27.34.38.53h-3.05c-.69,0-1.25-.56-1.25-1.25Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
