// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`subscription payments table should match snapshot 1`] = `
<body>
  <div>
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <div
          class="items-center print:hidden self-end"
        >
          <div
            class="relative"
          >
            <span
              class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
                  />
                </g>
              </svg>
            </span>
            <div
              class=""
            >
              <label
                class="text-md font-bold sr-only block mb-2"
                for="table-search"
              >
                Search
              </label>
            </div>
            <input
              aria-label="Search table"
              class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-white px-8"
              id="table-search"
              placeholder="Search"
              role="searchbox"
              value=""
            />
            <button
              aria-label="Clear search"
              class="invisible w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral focus:text-neutral focus:ring-2 focus:ring-neutral-500 focus:outline-hidden"
              name="Clear search"
            >
              <svg
                class="h-3 w-3 stroke-1 stroke-current fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <g>
                    <line
                      x1="5.16"
                      x2="18.94"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m18.94,19.64c-.19,0-.38-.07-.53-.22L4.63,5.64c-.29-.29-.29-.77,0-1.06s.77-.29,1.06,0l13.78,13.78c.29.29.29.77,0,1.06-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                  <g>
                    <line
                      x1="18.94"
                      x2="5.16"
                      y1="5.11"
                      y2="18.89"
                    />
                    <path
                      d="m5.16,19.64c-.19,0-.38-.07-.53-.22-.29-.29-.29-.77,0-1.06l13.78-13.78c.29-.29.77-.29,1.06,0s.29.77,0,1.06l-13.78,13.78c-.15.15-.34.22-.53.22Z"
                    />
                  </g>
                </g>
              </svg>
            </button>
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of subscription payments
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Created
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  >
                    <svg
                      class="fill-current h-4 w-4 stroke-current stroke-2"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        transform="translate(4 4)"
                      >
                        <path
                          d="m20.9,13.81c-.29-.29-.77-.29-1.06,0l-7.34,7.34V1.06c0-.41-.34-.75-.75-.75s-.75.34-.75.75v20.06l-6.85-6.85c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l8.1,8.1c.14.16.34.27.56.27,0,0,0,0,0,0,0,0,0,0,0,0,.19,0,.38-.07.53-.22l8.6-8.6c.29-.29.29-.77,0-1.06Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Amount
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Due
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Invoice number
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Customer email
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                <span
                  aria-label="paid"
                  class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-primary/20 border border-primary/50 text-primary capitalize"
                  role="status"
                >
                  paid
                </span>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-01-14
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                £0.50
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-02-11
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                ADF-1001
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <EMAIL>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="View invoice payment page"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    id="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    name="view-invoice-payment-page-in_1MtHbELkdIwHu7ixl4OzzPMv"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M25.05,6.7h-7.94c-.55,0-1,.45-1,1s.45,1,1,1h5.57l-14.26,14.43c-.39,.39-.38,1.03,0,1.41,.2,.19,.45,.29,.7,.29s.52-.1,.71-.3l14.21-14.38v4.66c0,.55,.45,1,1,1s1-.45,1-1V7.7c0-.55-.45-1-1-1Z"
                        />
                        <path
                          d="M26.83,.17H5.17C2.42,.17,.17,2.42,.17,5.17V26.83c0,2.76,2.24,5,5,5H26.83c2.76,0,5-2.24,5-5V5.17C31.83,2.42,29.58,.17,26.83,.17Zm3,26.66c0,1.65-1.35,3-3,3H5.17c-1.65,0-3-1.35-3-3V5.17c0-1.65,1.35-3,3-3H26.83c1.65,0,3,1.35,3,3V26.83Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Download pdf ADF-1001"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="download-pdf-ADF-1001"
                    id="download-pdf-ADF-1001"
                    name="download-pdf-ADF-1001"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="m20.73,14.27l-3.65-3.65c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.38,2.38h-7.49c-.41,0-.75.34-.75.75s.34.75.75.75h7.48l-2.17,2.17c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l3.44-3.44c.15-.14.24-.33.24-.54,0,0,0,0,0,0s0,0,0,0c0-.2-.08-.39-.22-.53Z"
                      />
                      <path
                        d="m20.91,7.14s0-.02,0-.03c0-.03-.01-.06-.02-.09-.11-.83-.46-1.61-1.06-2.23l-3.22-3.33c-.63-.65-1.48-1.05-2.37-1.16-.05-.01-.11-.03-.16-.03-.02,0-.03,0-.05.01-.08,0-.16-.03-.25-.03h-7.99c-1.52,0-2.75,1.23-2.75,2.75v18.01c0,1.52,1.23,2.75,2.75,2.75h12.4c1.52,0,2.75-1.23,2.75-2.75v-1.49c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.49c0,.69-.56,1.25-1.25,1.25H5.8c-.69,0-1.25-.56-1.25-1.25V2.99c0-.69.56-1.25,1.25-1.25h7.54v3.36c0,1.52,1.23,2.75,2.75,2.75h3.36v1.79c0,.41.34.75.75.75s.75-.34.75-.75v-2.12c0-.13-.02-.25-.04-.38Zm-6.07-2.03V1.99c.26.12.5.29.71.5l3.22,3.33c.15.16.27.34.38.53h-3.05c-.69,0-1.25-.56-1.25-1.25Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
