// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Site table should match snapshot 1`] = `
<body>
  <div>
    <h2
      class="text-2xl"
    >
      Sites
    </h2>
    <p
      class="text-md font-normal break-words"
    >
      Sites in this group. Only sites with a work item in any status will appear
    </p>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of sites
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3"
                scope="col"
              >
                Site
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Recipients email address
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Automated
              </th>
              <th
                class="text-left p-3"
                scope="col"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                Pod Point
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <p>
                  <EMAIL>, <EMAIL>
                </p>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex justify-center xl:w-1/2"
                >
                  <svg
                    class="fill-current h-6 w-6 text-primary"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      Yes
                    </title>
                    <g>
                      <path
                        d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                      />
                      <path
                        d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                      />
                    </g>
                  </svg>
                </div>
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                <div
                  class="flex space-x-1"
                >
                  <button
                    aria-label="Edit site Pod Point"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    id="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    name="edit-site-0f3d15fb-6f87-4221-b298-f5d456def592"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                        />
                        <path
                          d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                        />
                      </g>
                    </svg>
                  </button>
                  <button
                    aria-label="Edit fees Pod Point"
                    class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                    data-testid="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    id="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    name="edit-fees-0f3d15fb-6f87-4221-b298-f5d456def592"
                    type="button"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="m21.2,21.28c-.13-.2-.35-.29-.65-.29-.11,0-.24.02-.38.07-.14.04-.29.14-.44.29-.28.24-.63.43-1.05.57s-.93.21-1.54.21c-.74,0-1.57-.12-2.47-.36-.91-.24-1.83-.47-2.78-.7-.95-.23-1.88-.34-2.8-.34-.32,0-.64.01-.95.04.26-.4.49-.83.67-1.32.25-.68.38-1.42.38-2.23,0-.7-.09-1.41-.26-2.14-.18-.73-.38-1.49-.61-2.28-.04-.13-.07-.26-.11-.39h7.52c.26,0,.49-.08.67-.25.18-.16.28-.38.28-.64s-.09-.49-.28-.67c-.19-.19-.41-.28-.67-.28h-7.98c-.02-.08-.04-.16-.06-.25-.19-.86-.28-1.76-.28-2.7,0-1.11.21-2.1.62-2.96.41-.86,1-1.54,1.77-2.03.76-.49,1.68-.74,2.75-.74s1.99.2,2.83.61,1.53.94,2.08,1.62c.09.11.19.19.31.23s.25.07.38.07c.26,0,.51-.11.74-.33.23-.22.34-.45.34-.69,0-.17-.07-.35-.2-.54s-.31-.38-.52-.57c-.65-.76-1.52-1.34-2.6-1.72-1.08-.38-2.2-.57-3.36-.57-1.46,0-2.73.32-3.81.95-1.08.63-1.91,1.52-2.49,2.67-.58,1.15-.87,2.48-.87,4.01,0,.96.09,1.87.26,2.72.02.08.04.15.05.23h-1.98c-.28,0-.51.09-.69.26s-.26.39-.26.65.09.48.26.66.4.26.69.26h2.43c.04.13.07.26.11.39.23.79.44,1.56.62,2.32.18.76.28,1.54.28,2.32s-.11,1.49-.34,2.11c-.23.62-.5,1.14-.8,1.54-.02.03-.04.05-.07.08-.16.04-.33.08-.49.13-.59.17-1.26.37-2,.59-.28.09-.5.22-.64.39-.14.17-.21.38-.21.62,0,.2.07.39.21.57s.34.28.61.28c.15,0,.36-.07.62-.2.26-.13.55-.23.85-.29.22-.04.53-.13.95-.25.41-.12.89-.23,1.44-.34.55-.11,1.09-.16,1.64-.16.61,0,1.26.08,1.95.23.69.15,1.39.32,2.11.51.72.19,1.43.35,2.14.51.71.15,1.39.23,2.05.23.48,0,1.02-.08,1.6-.23.59-.15,1.13-.38,1.64-.69.28-.13.52-.29.7-.49.18-.2.28-.44.28-.72,0-.2-.07-.39-.2-.59Z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
