// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StatementView should match snapshot for a monthly statement 1`] = `
<body>
  <div>
    <div
      class="flex items-baseline"
    >
      <h1
        class="text-3xl font-semibold print:text-2xl"
      >
        Charge activity statement
      </h1>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Registers of Scotland - Discovery House
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Month:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      January 2023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Document reference:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      ROSDH012023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Group:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Registers of Scotland
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Site and Address:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Discovery House, 28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Owed to you
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Your revenue
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross revenue
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            4,576.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            3,813.33
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            762.67
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal content-center break-words"
      >
        Please produce an invoice for 'Your Revenue' and send it to us at:
      </p>
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="mailto:<EMAIL>"
      >
        <EMAIL>
      </a>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="break-after-page"
    />
    <h2
      class="text-2xl"
    >
      Data summary
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total charges:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      2048
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      567.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Confirmed energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      243.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Paid energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      43.5
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Invoice from Pod Point
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Our fees
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross fees
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            1.04
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            0.87
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            0.17
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        We have sent an invoice for our fees enclosed with this statement.
      </p>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="hidden print:block py-4 fixed bottom-0"
    >
      <svg
        height="47px"
        id="Layer_1"
        viewBox="0 0 180.5 49.6"
        width="172px"
        x="0px"
        xmlns="http://www.w3.org/2000/svg"
        y="0px"
      >
        <g>
          <path
            class="fill-current text-neutral"
            d="M95,25.6c-1.6,1-3.7,1.5-6.3,1.5H81c0-0.6,0.5-1.2,1.1-1.2h6.5c3.6,0,7.9-1,7.9-5.8 c0-3.7-2.7-5.6-7.9-5.6h-8.8c-0.1,0-0.2,0.1-0.2,0.2v21.7c0,0.6-0.5,1.1-1.2,1.1v-23c0-0.6,0.5-1.1,1.2-1.1h9   c7.5,0,9.1,3.7,9.1,6.7C97.7,22.6,96.8,24.5,95,25.6C95,25.7,95,25.6,95,25.6z M112.7,37.7c-6.8,0-12.3-5.6-12.3-12.4   S105.9,13,112.7,13c6.8,0,12.3,5.5,12.3,12.4C125,32.2,119.5,37.7,112.7,37.7L112.7,37.7z M112.7,14.2c-6.1,0-11.1,5-11.1,11.2   s5,11.2,11.1,11.2c6.2,0,11.1-5,11.1-11.2C123.8,19.2,118.8,14.2,112.7,14.2L112.7,14.2z M130.3,37.6c-0.6,0-1.1-0.5-1.1-1.1V13.4   c0.6,0,1.2,0.5,1.2,1.1L130.3,37.6C130.4,37.6,130.3,37.6,130.3,37.6z M155.6,37.5c-0.3,0-0.6-0.1-0.8-0.4l-17.3-22.3v21.6   c0,0.7-0.5,1.1-1.2,1.2V14.5c0-0.6,0.5-1.2,1.1-1.2c0.3,0,0.5,0.1,0.7,0.4l17.4,22.5V14.5c0-0.6,0.5-1.1,1.2-1.1v23.1   C156.6,37,156.1,37.5,155.6,37.5L155.6,37.5z M179.3,14.7h-8.4v22.8c-0.7,0-1.2-0.5-1.2-1.1V14.7h-8.5c-0.6,0-1.2-0.5-1.2-1.2h20.4   C180.5,14.2,180,14.7,179.3,14.7L179.3,14.7z M67.4,24.8V3.5c-0.8-1.2-1.6-2.4-2.5-3.5v24.8c0,5.3-4.3,9.6-9.6,9.6   c-5.1,0-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1c-5.3,0-9.6-4.3-9.6-9.6H0.2   c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1s9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12   C62,36.8,67.4,31.4,67.4,24.8L67.4,24.8z"
          />
          <path
            class="fill-current text-primary"
            d="M64.9,17.5c-2.3-3-5.8-4.7-9.6-4.7c-6.6,0-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1 s-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v21.3c0.8,1.2,1.6,2.4,2.5,3.5V24.8c0-5.3,4.3-9.6,9.6-9.6   c5.1,0,9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1c5.3,0,9.5,4.3,9.6,9.5L64.9,17.5"
          />
        </g>
      </svg>
    </div>
  </div>
</body>
`;

exports[`StatementView should match snapshot for a monthly statement with fee overrides 1`] = `
<body>
  <div>
    <div
      class="flex items-baseline"
    >
      <h1
        class="text-3xl font-semibold print:text-2xl"
      >
        Charge activity statement
      </h1>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Registers of Scotland - Discovery House
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Month:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      January 2023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Document reference:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      ROSDH012023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Group:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Registers of Scotland
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Site and Address:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Discovery House, 28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Owed to you
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Your revenue
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross revenue
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            4,576.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            3,813.33
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            762.67
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal content-center break-words"
      >
        Please produce an invoice for 'Your Revenue' and send it to us at:
      </p>
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="mailto:<EMAIL>"
      >
        <EMAIL>
      </a>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="break-after-page"
    />
    <h2
      class="text-2xl"
    >
      Data summary
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total charges:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      2048
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      567.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Confirmed energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      243.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Paid energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      43.5
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Invoice from Pod Point
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Our fees
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross fees
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            100.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            80.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            20.00
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        We have sent an invoice for our fees enclosed with this statement.
      </p>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="hidden print:block py-4 fixed bottom-0"
    >
      <svg
        height="47px"
        id="Layer_1"
        viewBox="0 0 180.5 49.6"
        width="172px"
        x="0px"
        xmlns="http://www.w3.org/2000/svg"
        y="0px"
      >
        <g>
          <path
            class="fill-current text-neutral"
            d="M95,25.6c-1.6,1-3.7,1.5-6.3,1.5H81c0-0.6,0.5-1.2,1.1-1.2h6.5c3.6,0,7.9-1,7.9-5.8 c0-3.7-2.7-5.6-7.9-5.6h-8.8c-0.1,0-0.2,0.1-0.2,0.2v21.7c0,0.6-0.5,1.1-1.2,1.1v-23c0-0.6,0.5-1.1,1.2-1.1h9   c7.5,0,9.1,3.7,9.1,6.7C97.7,22.6,96.8,24.5,95,25.6C95,25.7,95,25.6,95,25.6z M112.7,37.7c-6.8,0-12.3-5.6-12.3-12.4   S105.9,13,112.7,13c6.8,0,12.3,5.5,12.3,12.4C125,32.2,119.5,37.7,112.7,37.7L112.7,37.7z M112.7,14.2c-6.1,0-11.1,5-11.1,11.2   s5,11.2,11.1,11.2c6.2,0,11.1-5,11.1-11.2C123.8,19.2,118.8,14.2,112.7,14.2L112.7,14.2z M130.3,37.6c-0.6,0-1.1-0.5-1.1-1.1V13.4   c0.6,0,1.2,0.5,1.2,1.1L130.3,37.6C130.4,37.6,130.3,37.6,130.3,37.6z M155.6,37.5c-0.3,0-0.6-0.1-0.8-0.4l-17.3-22.3v21.6   c0,0.7-0.5,1.1-1.2,1.2V14.5c0-0.6,0.5-1.2,1.1-1.2c0.3,0,0.5,0.1,0.7,0.4l17.4,22.5V14.5c0-0.6,0.5-1.1,1.2-1.1v23.1   C156.6,37,156.1,37.5,155.6,37.5L155.6,37.5z M179.3,14.7h-8.4v22.8c-0.7,0-1.2-0.5-1.2-1.1V14.7h-8.5c-0.6,0-1.2-0.5-1.2-1.2h20.4   C180.5,14.2,180,14.7,179.3,14.7L179.3,14.7z M67.4,24.8V3.5c-0.8-1.2-1.6-2.4-2.5-3.5v24.8c0,5.3-4.3,9.6-9.6,9.6   c-5.1,0-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1c-5.3,0-9.6-4.3-9.6-9.6H0.2   c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1s9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12   C62,36.8,67.4,31.4,67.4,24.8L67.4,24.8z"
          />
          <path
            class="fill-current text-primary"
            d="M64.9,17.5c-2.3-3-5.8-4.7-9.6-4.7c-6.6,0-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1 s-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v21.3c0.8,1.2,1.6,2.4,2.5,3.5V24.8c0-5.3,4.3-9.6,9.6-9.6   c5.1,0,9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1c5.3,0,9.5,4.3,9.6,9.5L64.9,17.5"
          />
        </g>
      </svg>
    </div>
  </div>
</body>
`;

exports[`StatementView should match snapshot for a quarterly statement 1`] = `
<body>
  <div>
    <div
      class="flex items-baseline"
    >
      <h1
        class="text-3xl font-semibold print:text-2xl"
      >
        Charge activity statement
      </h1>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Registers of Scotland - Discovery House
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Month:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      November 2022 - January 2023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Document reference:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      ROSDH112022
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Group:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Registers of Scotland
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Site and Address:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Discovery House, 28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Owed to you
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Your revenue
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross revenue
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            4,576.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            3,813.33
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            762.67
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal content-center break-words"
      >
        Please produce an invoice for 'Your Revenue' and send it to us at:
      </p>
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="mailto:<EMAIL>"
      >
        <EMAIL>
      </a>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="break-after-page"
    />
    <h2
      class="text-2xl"
    >
      Data summary
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total charges:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      2048
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      567.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Confirmed energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      243.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Paid energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      43.5
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Breakdown by month
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Breakdown by month
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Month
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total charges
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Confirmed energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Paid energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-11
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2023-01
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      * Gross Revenue is calculated using claimed charges only
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Invoice from Pod Point
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Our fees
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross fees
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            1.04
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            0.87
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            0.17
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        We have sent an invoice for our fees enclosed with this statement.
      </p>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="hidden print:block py-4 fixed bottom-0"
    >
      <svg
        height="47px"
        id="Layer_1"
        viewBox="0 0 180.5 49.6"
        width="172px"
        x="0px"
        xmlns="http://www.w3.org/2000/svg"
        y="0px"
      >
        <g>
          <path
            class="fill-current text-neutral"
            d="M95,25.6c-1.6,1-3.7,1.5-6.3,1.5H81c0-0.6,0.5-1.2,1.1-1.2h6.5c3.6,0,7.9-1,7.9-5.8 c0-3.7-2.7-5.6-7.9-5.6h-8.8c-0.1,0-0.2,0.1-0.2,0.2v21.7c0,0.6-0.5,1.1-1.2,1.1v-23c0-0.6,0.5-1.1,1.2-1.1h9   c7.5,0,9.1,3.7,9.1,6.7C97.7,22.6,96.8,24.5,95,25.6C95,25.7,95,25.6,95,25.6z M112.7,37.7c-6.8,0-12.3-5.6-12.3-12.4   S105.9,13,112.7,13c6.8,0,12.3,5.5,12.3,12.4C125,32.2,119.5,37.7,112.7,37.7L112.7,37.7z M112.7,14.2c-6.1,0-11.1,5-11.1,11.2   s5,11.2,11.1,11.2c6.2,0,11.1-5,11.1-11.2C123.8,19.2,118.8,14.2,112.7,14.2L112.7,14.2z M130.3,37.6c-0.6,0-1.1-0.5-1.1-1.1V13.4   c0.6,0,1.2,0.5,1.2,1.1L130.3,37.6C130.4,37.6,130.3,37.6,130.3,37.6z M155.6,37.5c-0.3,0-0.6-0.1-0.8-0.4l-17.3-22.3v21.6   c0,0.7-0.5,1.1-1.2,1.2V14.5c0-0.6,0.5-1.2,1.1-1.2c0.3,0,0.5,0.1,0.7,0.4l17.4,22.5V14.5c0-0.6,0.5-1.1,1.2-1.1v23.1   C156.6,37,156.1,37.5,155.6,37.5L155.6,37.5z M179.3,14.7h-8.4v22.8c-0.7,0-1.2-0.5-1.2-1.1V14.7h-8.5c-0.6,0-1.2-0.5-1.2-1.2h20.4   C180.5,14.2,180,14.7,179.3,14.7L179.3,14.7z M67.4,24.8V3.5c-0.8-1.2-1.6-2.4-2.5-3.5v24.8c0,5.3-4.3,9.6-9.6,9.6   c-5.1,0-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1c-5.3,0-9.6-4.3-9.6-9.6H0.2   c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1s9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12   C62,36.8,67.4,31.4,67.4,24.8L67.4,24.8z"
          />
          <path
            class="fill-current text-primary"
            d="M64.9,17.5c-2.3-3-5.8-4.7-9.6-4.7c-6.6,0-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1 s-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v21.3c0.8,1.2,1.6,2.4,2.5,3.5V24.8c0-5.3,4.3-9.6,9.6-9.6   c5.1,0,9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1c5.3,0,9.5,4.3,9.6,9.5L64.9,17.5"
          />
        </g>
      </svg>
    </div>
  </div>
</body>
`;

exports[`StatementView should match snapshot for a quarterly statement with fee overrides 1`] = `
<body>
  <div>
    <div
      class="flex items-baseline"
    >
      <h1
        class="text-3xl font-semibold print:text-2xl"
      >
        Charge activity statement
      </h1>
    </div>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Registers of Scotland - Discovery House
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Month:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      November 2022 - January 2023
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Document reference:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      ROSDH112022
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Group:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Registers of Scotland
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Site and Address:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      Discovery House, 28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Owed to you
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Your revenue
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross revenue
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            4,576.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            3,813.33
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            762.67
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal content-center break-words"
      >
        Please produce an invoice for 'Your Revenue' and send it to us at:
      </p>
      <a
        class="text-info hover:underline hover:text-info active:text-info cursor-pointer"
        href="mailto:<EMAIL>"
      >
        <EMAIL>
      </a>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="break-after-page"
    />
    <h2
      class="text-2xl"
    >
      Data summary
    </h2>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total charges:
    </p>
    <p
      class="text-md font-normal break-words"
    >
      2048
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Total energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      567.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Confirmed energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      243.89
    </p>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-bold break-words"
    >
      Paid energy delivered (kWh):
    </p>
    <p
      class="text-md font-normal break-words"
    >
      43.5
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Breakdown by month
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Breakdown by month
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Month
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total charges
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Confirmed energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Paid energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-11
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2023-01
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      * Gross Revenue is calculated using claimed charges only
    </p>
    <div
      class="pb-4"
    />
    <h2
      class="text-2xl"
    >
      Invoice from Pod Point
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="border-primary border-solid border-4 px-4 py-4 text-center"
    >
      <p
        class="text-md font-bold text-primary text-xl text-left break-words"
      >
        Our fees
      </p>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Gross fees
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            100.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            Net
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            80.00
          </p>
        </div>
        <div>
          <p
            class="text-md font-bold break-words"
          >
            VAT at 20%
          </p>
          <div
            class="pb-4"
          />
          <p
            class="text-md font-normal break-words"
          >
            £
            20.00
          </p>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <p
        class="text-md font-normal break-words"
      >
        We have sent an invoice for our fees enclosed with this statement.
      </p>
    </div>
    <div
      class="pb-4"
    />
    <div
      class="hidden print:block py-4 fixed bottom-0"
    >
      <svg
        height="47px"
        id="Layer_1"
        viewBox="0 0 180.5 49.6"
        width="172px"
        x="0px"
        xmlns="http://www.w3.org/2000/svg"
        y="0px"
      >
        <g>
          <path
            class="fill-current text-neutral"
            d="M95,25.6c-1.6,1-3.7,1.5-6.3,1.5H81c0-0.6,0.5-1.2,1.1-1.2h6.5c3.6,0,7.9-1,7.9-5.8 c0-3.7-2.7-5.6-7.9-5.6h-8.8c-0.1,0-0.2,0.1-0.2,0.2v21.7c0,0.6-0.5,1.1-1.2,1.1v-23c0-0.6,0.5-1.1,1.2-1.1h9   c7.5,0,9.1,3.7,9.1,6.7C97.7,22.6,96.8,24.5,95,25.6C95,25.7,95,25.6,95,25.6z M112.7,37.7c-6.8,0-12.3-5.6-12.3-12.4   S105.9,13,112.7,13c6.8,0,12.3,5.5,12.3,12.4C125,32.2,119.5,37.7,112.7,37.7L112.7,37.7z M112.7,14.2c-6.1,0-11.1,5-11.1,11.2   s5,11.2,11.1,11.2c6.2,0,11.1-5,11.1-11.2C123.8,19.2,118.8,14.2,112.7,14.2L112.7,14.2z M130.3,37.6c-0.6,0-1.1-0.5-1.1-1.1V13.4   c0.6,0,1.2,0.5,1.2,1.1L130.3,37.6C130.4,37.6,130.3,37.6,130.3,37.6z M155.6,37.5c-0.3,0-0.6-0.1-0.8-0.4l-17.3-22.3v21.6   c0,0.7-0.5,1.1-1.2,1.2V14.5c0-0.6,0.5-1.2,1.1-1.2c0.3,0,0.5,0.1,0.7,0.4l17.4,22.5V14.5c0-0.6,0.5-1.1,1.2-1.1v23.1   C156.6,37,156.1,37.5,155.6,37.5L155.6,37.5z M179.3,14.7h-8.4v22.8c-0.7,0-1.2-0.5-1.2-1.1V14.7h-8.5c-0.6,0-1.2-0.5-1.2-1.2h20.4   C180.5,14.2,180,14.7,179.3,14.7L179.3,14.7z M67.4,24.8V3.5c-0.8-1.2-1.6-2.4-2.5-3.5v24.8c0,5.3-4.3,9.6-9.6,9.6   c-5.1,0-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1c-5.3,0-9.6-4.3-9.6-9.6H0.2   c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1s9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12   C62,36.8,67.4,31.4,67.4,24.8L67.4,24.8z"
          />
          <path
            class="fill-current text-primary"
            d="M64.9,17.5c-2.3-3-5.8-4.7-9.6-4.7c-6.6,0-12,5.4-12,12v0.4c-0.2,5.1-4.4,9.1-9.5,9.1 s-9.3-4.1-9.5-9.1v-0.4c0-6.6-5.4-12-12-12s-12,5.4-12,12v21.3c0.8,1.2,1.6,2.4,2.5,3.5V24.8c0-5.3,4.3-9.6,9.6-9.6   c5.1,0,9.3,4.1,9.5,9.1v0.4c0,6.6,5.4,12,12,12s12-5.4,12-12v-0.4c0.2-5.1,4.4-9.1,9.5-9.1c5.3,0,9.5,4.3,9.6,9.5L64.9,17.5"
          />
        </g>
      </svg>
    </div>
  </div>
</body>
`;
