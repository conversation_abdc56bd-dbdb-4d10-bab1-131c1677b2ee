// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Monthly breakdown section should match snapshot 1`] = `
<body>
  <div>
    <h2
      class="text-2xl"
    >
      Breakdown by month
    </h2>
    <div
      class="pb-4"
    />
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Breakdown by month
          </caption>
          <thead
            class="border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Month
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total charges
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Total energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Confirmed energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Paid energy delivered (kWh)
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral-100"
          >
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-11
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2022-12
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
            <tr
              class="border-b-neutral-100"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2023-01
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                2048
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                567.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                243.89
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                43.5
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <p
      class="text-md font-normal break-words"
    >
      * Gross Revenue is calculated using claimed charges only
    </p>
    <div
      class="pb-4"
    />
  </div>
</body>
`;
