import {
  CreateTescoClubcardRequest,
  TescoClubcard as TescoClubcardDto,
  TescoClubcardNotFoundException,
} from '@experience/commercial/loyalty-card-service/shared';
import { Injectable, Logger } from '@nestjs/common';
import {
  LoyaltyCardPrismaClient,
  TescoClubcard as TescoClubcardEntity,
} from '@experience/commercial/loyalty-card-service/prisma/loyalty-card/client';

@Injectable()
export class TescoClubcardService {
  private readonly logger = new Logger(TescoClubcardService.name);

  constructor(private readonly database: LoyaltyCardPrismaClient) {}

  async create(request: CreateTescoClubcardRequest): Promise<void> {
    this.logger.log({ request }, 'creating tesco clubcard');

    await this.database.tescoClubcard.upsert({
      where: { authId: request.authId },
      update: { customerId: request.customerId, deletedAt: null },
      create: { ...request },
    });
  }

  async get(authId: string): Promise<TescoClubcardDto> {
    this.logger.log({ authId }, 'fetching tesco clubcard');

    const entity: TescoClubcardEntity | null =
      await this.database.tescoClubcard.findUnique({
        where: { authId, deletedAt: null },
      });

    if (!entity) {
      throw new TescoClubcardNotFoundException();
    }

    return this.mapTescoClubcard(entity);
  }

  async delete(authId: string): Promise<void> {
    this.logger.log({ authId }, 'deleting tesco clubcard');

    await this.database.tescoClubcard.updateMany({
      data: { deletedAt: new Date() },
      where: { authId },
    });
  }

  private mapTescoClubcard(entity: TescoClubcardEntity): TescoClubcardDto {
    return {
      authId: entity.authId,
      customerId: entity.customerId,
    };
  }
}
