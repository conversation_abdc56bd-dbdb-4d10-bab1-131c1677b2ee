import { ApiParam } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import {
  CreateTescoClubcardRequest,
  TescoClubcard,
  TescoClubcardDeleteParams,
  TescoClubcardGetParams,
} from '@experience/commercial/loyalty-card-service/shared';
import { TescoClubcardInterceptor } from './tesco-clubcard.interceptor';
import { TescoClubcardService } from './tesco-clubcard.service';

@UseInterceptors(TescoClubcardInterceptor)
@Controller('/loyalty-cards/tesco')
export class TescoClubcardController {
  constructor(private service: TescoClubcardService) {}

  @Post()
  async create(@Body() request: CreateTescoClubcardRequest): Promise<void> {
    return this.service.create(request);
  }

  @Get('/:authId')
  @ApiParam({ name: 'authId' })
  async get(
    @Param() { authId }: TescoClubcardGetParams
  ): Promise<TescoClubcard> {
    return this.service.get(authId);
  }

  @HttpCode(204)
  @Delete('/:authId')
  @ApiParam({ name: 'authId' })
  async delete(@Param() { authId }: TescoClubcardDeleteParams): Promise<void> {
    return this.service.delete(authId);
  }
}
