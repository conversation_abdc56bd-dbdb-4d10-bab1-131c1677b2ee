# Commercial Loyalty Card Service Prisma Loyalty Card Schema

This library was generated with [Nx](https://nx.dev).

## Running Prisma commands

Duplicate the `.env.sample` and fill in the database connection details e.g `COMMERCIAL_DB_URL="postgres://{USERNAME}:{PASSWORD}@localhost:5432/commercial"`

To run Prisma commands run this command:`nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma` suffixed with the command you want to run.

## Common commands:

### To introspect the database against a locally running db (or reintrospect if a change has been made):

- `nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma db pull`

### To generate the Prisma client:

`nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma generate`

### To reformat the schema file:

`nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma format`

### To generate a new migration from any changes made to the schema file:

`nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma migrate dev --name init`

### To apply all pending migrations, and create the schema if it does not exist

`nx run commercial-loyalty-card-service-prisma-loyalty-card-schema:prisma migrate deploy`
