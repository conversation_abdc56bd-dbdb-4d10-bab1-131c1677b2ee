DO
$do$
  BEGIN
     IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'loyalty_card_service') THEN
        RAISE NOTICE 'Role "loyalty_card_service" already exists. Skipping.';
    ELSE
        CREATE USER loyalty_card_service;
    END IF;
  END
$do$;

GRANT USAGE ON SCHEMA "loyalty_card" TO loyalty_card_service;
ALTER DEFAULT PRIVILEGES IN SCHEMA "loyalty_card" GRANT SELECT,INSERT,UPDATE,DELETE ON TABLES TO loyalty_card_service;

GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "loyalty_card" TO loyalty_card_service;
