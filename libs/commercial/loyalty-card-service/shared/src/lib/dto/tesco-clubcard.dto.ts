import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const CreateTescoClubcardRequestSchema = z.object({
  authId: z.string().uuid(),
  customerId: z.string().uuid(),
});

export class CreateTescoClubcardRequest extends createZodDto(
  CreateTescoClubcardRequestSchema
) {}

const TescoClubcardSchema = z.object({
  authId: z.string().uuid(),
  customerId: z.string().uuid(),
});

export class TescoClubcard extends createZodDto(TescoClubcardSchema) {}

const TescoClubcardGetParamsSchema = z.object({
  authId: z.string().uuid(),
});

export class TescoClubcardGetParams extends createZodDto(
  TescoClubcardGetParamsSchema
) {}

const TescoClubcardDeleteParamsSchema = z.object({
  authId: z.string().uuid(),
});

export class TescoClubcardDeleteParams extends createZodDto(
  TescoClubcardDeleteParamsSchema
) {}
