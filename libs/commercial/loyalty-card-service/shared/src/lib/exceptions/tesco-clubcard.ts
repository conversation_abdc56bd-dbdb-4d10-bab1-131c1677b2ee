export const TESCO_CLUBCARD_NOT_FOUND_EXCEPTION = 'Tesco clubcard not found';
export const TESCO_CLUBCARD_POINTS_NOT_CREDITED_EXCEPTION =
  'Failed to credit points to Tesco clubcard';

export enum TescoClubcardErrorCodes {
  NOT_FOUND = 'NOT_FOUND',
}

export class TescoClubcardNotFoundException extends Error {
  constructor() {
    super(TESCO_CLUBCARD_NOT_FOUND_EXCEPTION);
  }
}

export class TescoClubcardPointsNotCreditedException extends Error {
  constructor() {
    super(TESCO_CLUBCARD_POINTS_NOT_CREDITED_EXCEPTION);
  }
}
