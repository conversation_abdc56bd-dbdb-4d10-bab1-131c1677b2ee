-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "ocpi/v221";

-- CreateEnum
CREATE TYPE "ocpi/v221"."ConnectorFormat" AS ENUM ('CABLE', 'SOCKET');

-- CreateEnum
CREATE TYPE "ocpi/v221"."ConnectorPowerType" AS ENUM ('AC_1_PHASE', 'AC_3_PHASE', 'DC');

-- CreateEnum
CREATE TYPE "ocpi/v221"."ConnectorStandard" AS ENUM ('CHADEMO', 'DOMESTIC_G', 'IEC_62196_T1', 'IEC_62196_T2', 'IEC_62196_T2_COMBO');

-- Create<PERSON>num
CREATE TYPE "ocpi/v221"."EVSECapability" AS ENUM ('CONTACTLESS_CARD_SUPPORT', 'REMOTE_START_STOP_CAPABLE');

-- CreateEnum
CREATE TYPE "ocpi/v221"."EVSEStatus" AS ENUM ('AVAILABLE', 'CHARGING', 'OUTOFORDER', 'REMOVED', 'UNKNOWN');

-- CreateEnum
CREATE TYPE "ocpi/v221"."TariffDimensionType" AS ENUM ('ENERGY');

-- CreateTable
CREATE TABLE "ocpi/v221"."Credentials" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "token" TEXT NOT NULL,
    "url" TEXT NOT NULL,

    CONSTRAINT "Credentials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."Location" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "countryCode" TEXT NOT NULL,
    "partyId" TEXT NOT NULL,
    "publish" BOOLEAN NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "postalCode" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "latitude" DECIMAL(9,7) NOT NULL,
    "longitude" DECIMAL(10,7) NOT NULL,
    "owner" TEXT NOT NULL,
    "timezone" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."EVSE" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "uid" TEXT NOT NULL,
    "status" "ocpi/v221"."EVSEStatus" NOT NULL,
    "capabilities" "ocpi/v221"."EVSECapability"[],
    "latitude" DECIMAL(9,7) NOT NULL,
    "longitude" DECIMAL(10,7) NOT NULL,
    "physicalReference" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "locationId" TEXT NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "EVSE_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."Connector" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "standard" "ocpi/v221"."ConnectorStandard" NOT NULL,
    "format" "ocpi/v221"."ConnectorFormat" NOT NULL,
    "powerType" "ocpi/v221"."ConnectorPowerType" NOT NULL,
    "maxVoltage" INTEGER NOT NULL,
    "maxAmperage" INTEGER NOT NULL,
    "maxElectricPower" INTEGER NOT NULL,
    "tariffIds" TEXT[],
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "evseId" TEXT NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "Connector_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."Tariff" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "countryCode" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "partyId" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "Tariff_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."TariffElement" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "type" "ocpi/v221"."TariffDimensionType" NOT NULL,
    "price" DECIMAL(65,30) NOT NULL,
    "stepSize" INTEGER NOT NULL,
    "tariffId" TEXT NOT NULL,
    "sys_period" tstzrange,

    CONSTRAINT "TariffElement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ocpi/v221"."_ConnectorToTariff" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "Credentials_token_key" ON "ocpi/v221"."Credentials"("token");

-- CreateIndex
CREATE UNIQUE INDEX "_ConnectorToTariff_AB_unique" ON "ocpi/v221"."_ConnectorToTariff"("A", "B");

-- CreateIndex
CREATE INDEX "_ConnectorToTariff_B_index" ON "ocpi/v221"."_ConnectorToTariff"("B");

-- AddForeignKey
ALTER TABLE "ocpi/v221"."EVSE" ADD CONSTRAINT "EVSE_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "ocpi/v221"."Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ocpi/v221"."Connector" ADD CONSTRAINT "Connector_evseId_fkey" FOREIGN KEY ("evseId") REFERENCES "ocpi/v221"."EVSE"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ocpi/v221"."TariffElement" ADD CONSTRAINT "TariffElement_tariffId_fkey" FOREIGN KEY ("tariffId") REFERENCES "ocpi/v221"."Tariff"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ocpi/v221"."_ConnectorToTariff" ADD CONSTRAINT "_ConnectorToTariff_A_fkey" FOREIGN KEY ("A") REFERENCES "ocpi/v221"."Connector"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ocpi/v221"."_ConnectorToTariff" ADD CONSTRAINT "_ConnectorToTariff_B_fkey" FOREIGN KEY ("B") REFERENCES "ocpi/v221"."Tariff"("id") ON DELETE CASCADE ON UPDATE CASCADE;
