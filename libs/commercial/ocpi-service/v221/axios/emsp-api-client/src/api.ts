/* tslint:disable */
/* eslint-disable */
/**
 * OCPI 2.2.1 Integration
 * OCPI 2.2.1 endpoints according to the Open Charge Point Interface (OCPI) specification.
 *
 * The version of the OpenAPI document: 2.2.1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface Address
 */
export interface Address {}
/**
 *
 * @export
 * @enum {string}
 */

export const AllowedType = {
  Allowed: 'ALLOWED',
  Blocked: 'BLOCKED',
  Expired: 'EXPIRED',
  NoCredit: 'NO_CREDIT',
  NotAllowed: 'NOT_ALLOWED',
} as const;

export type AllowedType = (typeof AllowedType)[keyof typeof AllowedType];

/**
 *
 * @export
 * @enum {string}
 */

export const AuthMethod = {
  AuthRequest: 'AUTH_REQUEST',
  Command: 'COMMAND',
  Whitelist: 'WHITELIST',
} as const;

export type AuthMethod = (typeof AuthMethod)[keyof typeof AuthMethod];

/**
 *
 * @export
 * @interface AuthResponse
 */
export interface AuthResponse {
  /**
   *
   * @type {AuthorizationInfo}
   * @memberof AuthResponse
   */
  data: AuthorizationInfo;
  /**
   *
   * @type {number}
   * @memberof AuthResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof AuthResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof AuthResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface AuthorizationInfo
 */
export interface AuthorizationInfo {
  /**
   *
   * @type {AllowedType}
   * @memberof AuthorizationInfo
   */
  allowed: AllowedType;
  /**
   *
   * @type {Token}
   * @memberof AuthorizationInfo
   */
  token: Token;
  /**
   *
   * @type {LocationReferences}
   * @memberof AuthorizationInfo
   */
  location?: LocationReferences | null;
  /**
   *
   * @type {string}
   * @memberof AuthorizationInfo
   */
  authorization_reference?: string | null;
  /**
   *
   * @type {Array<DisplayText>}
   * @memberof AuthorizationInfo
   */
  info?: Array<DisplayText> | null;
}

/**
 *
 * @export
 * @interface BusinessDetails
 */
export interface BusinessDetails {
  /**
   *
   * @type {string}
   * @memberof BusinessDetails
   */
  name: string;
  /**
   *
   * @type {Image}
   * @memberof BusinessDetails
   */
  logo?: Image | null;
  /**
   *
   * @type {string}
   * @memberof BusinessDetails
   */
  website?: string | null;
}
/**
 *
 * @export
 * @interface CPOCredentials
 */
export interface CPOCredentials {
  /**
   *
   * @type {string}
   * @memberof CPOCredentials
   */
  token: string;
  /**
   *
   * @type {string}
   * @memberof CPOCredentials
   */
  url: string;
  /**
   *
   * @type {Array<CredentialsRole>}
   * @memberof CPOCredentials
   */
  roles: Array<CredentialsRole>;
}
/**
 *
 * @export
 * @interface Capabilities
 */
export interface Capabilities {}
/**
 *
 * @export
 * @enum {string}
 */

export const Capability = {
  ChargingProfileCapable: 'CHARGING_PROFILE_CAPABLE',
  ChargingPreferencesCapable: 'CHARGING_PREFERENCES_CAPABLE',
  ChipCardSupport: 'CHIP_CARD_SUPPORT',
  ContactlessCardSupport: 'CONTACTLESS_CARD_SUPPORT',
  PedTerminal: 'PED_TERMINAL',
  RemoteStartStopCapable: 'REMOTE_START_STOP_CAPABLE',
  Reservable: 'RESERVABLE',
  RfidReader: 'RFID_READER',
  CreditCardPayable: 'CREDIT_CARD_PAYABLE',
  DebitCardPayable: 'DEBIT_CARD_PAYABLE',
  TokenGroupCapable: 'TOKEN_GROUP_CAPABLE',
  UnlockCapable: 'UNLOCK_CAPABLE',
  StartSessionConnectorRequired: 'START_SESSION_CONNECTOR_REQUIRED',
} as const;

export type Capability = (typeof Capability)[keyof typeof Capability];

/**
 *
 * @export
 * @interface Cdr
 */
export interface Cdr {
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  country_code: string;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  start_date_time: string;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  end_date_time: string;
  /**
   *
   * @type {CdrToken}
   * @memberof Cdr
   */
  cdr_token: CdrToken;
  /**
   *
   * @type {AuthMethod}
   * @memberof Cdr
   */
  auth_method: AuthMethod;
  /**
   *
   * @type {CdrLocation}
   * @memberof Cdr
   */
  cdr_location: CdrLocation;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  currency: string;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_cost: Price;
  /**
   *
   * @type {number}
   * @memberof Cdr
   */
  total_energy: number;
  /**
   *
   * @type {number}
   * @memberof Cdr
   */
  total_time: number;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  last_updated: string;
  /**
   * Must define at least one charging period
   * @type {Array<ChargingPeriod>}
   * @memberof Cdr
   */
  charging_periods: Array<ChargingPeriod>;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  session_id?: string | null;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  authorization_reference?: string | null;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  meter_id?: string | null;
  /**
   *
   * @type {Array<Tariff>}
   * @memberof Cdr
   */
  tariffs?: Array<Tariff> | null;
  /**
   *
   * @type {SignedData}
   * @memberof Cdr
   */
  signed_data?: SignedData | null;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_fixed_cost?: Price | null;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_energy_cost?: Price | null;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_time_cost?: Price | null;
  /**
   *
   * @type {number}
   * @memberof Cdr
   */
  total_parking_time?: number | null;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_parking_cost?: Price | null;
  /**
   *
   * @type {Price}
   * @memberof Cdr
   */
  total_reservation_cost?: Price | null;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  remark?: string | null;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  invoice_reference_id?: string | null;
  /**
   *
   * @type {boolean}
   * @memberof Cdr
   */
  credit?: boolean | null;
  /**
   *
   * @type {string}
   * @memberof Cdr
   */
  credit_reference_id?: string | null;
  /**
   *
   * @type {boolean}
   * @memberof Cdr
   */
  home_charging_compensation?: boolean | null;
}

/**
 *
 * @export
 * @interface CdrDimension
 */
export interface CdrDimension {
  /**
   *
   * @type {CdrDimensionType}
   * @memberof CdrDimension
   */
  type: CdrDimensionType;
  /**
   *
   * @type {number}
   * @memberof CdrDimension
   */
  volume: number;
}

/**
 *
 * @export
 * @enum {string}
 */

export const CdrDimensionType = {
  Current: 'CURRENT',
  Energy: 'ENERGY',
  EnergyExport: 'ENERGY_EXPORT',
  EnergyImport: 'ENERGY_IMPORT',
  MaxCurrent: 'MAX_CURRENT',
  MinCurrent: 'MIN_CURRENT',
  MaxPower: 'MAX_POWER',
  MinPower: 'MIN_POWER',
  ParkingTime: 'PARKING_TIME',
  Power: 'POWER',
  ReservationTime: 'RESERVATION_TIME',
  StateOfCharge: 'STATE_OF_CHARGE',
  Time: 'TIME',
} as const;

export type CdrDimensionType =
  (typeof CdrDimensionType)[keyof typeof CdrDimensionType];

/**
 *
 * @export
 * @interface CdrLocation
 */
export interface CdrLocation {
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  address: string;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  city: string;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  country: string;
  /**
   *
   * @type {GeoLocation}
   * @memberof CdrLocation
   */
  coordinates: GeoLocation;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  evse_uid: string;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  evse_id: string;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  connector_id: string;
  /**
   *
   * @type {ConnectorType}
   * @memberof CdrLocation
   */
  connector_standard: ConnectorType;
  /**
   *
   * @type {ConnectorFormat}
   * @memberof CdrLocation
   */
  connector_format: ConnectorFormat;
  /**
   *
   * @type {PowerType}
   * @memberof CdrLocation
   */
  connector_power_type: PowerType;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  name?: string | null;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  postal_code?: string | null;
  /**
   *
   * @type {string}
   * @memberof CdrLocation
   */
  state?: string | null;
}

/**
 *
 * @export
 * @interface CdrResponse
 */
export interface CdrResponse {
  /**
   *
   * @type {Cdr}
   * @memberof CdrResponse
   */
  data: Cdr;
  /**
   *
   * @type {number}
   * @memberof CdrResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof CdrResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof CdrResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface CdrToken
 */
export interface CdrToken {
  /**
   *
   * @type {string}
   * @memberof CdrToken
   */
  country_code?: string;
  /**
   *
   * @type {string}
   * @memberof CdrToken
   */
  party_id?: string;
  /**
   *
   * @type {string}
   * @memberof CdrToken
   */
  uid: string;
  /**
   *
   * @type {TokenType}
   * @memberof CdrToken
   */
  type: TokenType;
  /**
   *
   * @type {string}
   * @memberof CdrToken
   */
  contract_id: string;
}

/**
 *
 * @export
 * @interface ChargingPeriod
 */
export interface ChargingPeriod {
  /**
   *
   * @type {string}
   * @memberof ChargingPeriod
   */
  start_date_time: string;
  /**
   *
   * @type {Array<CdrDimension>}
   * @memberof ChargingPeriod
   */
  dimensions: Array<CdrDimension>;
  /**
   *
   * @type {string}
   * @memberof ChargingPeriod
   */
  tariff_id?: string | null;
}
/**
 *
 * @export
 * @interface ChargingWhenClosed
 */
export interface ChargingWhenClosed {}
/**
 *
 * @export
 * @interface City
 */
export interface City {}
/**
 *
 * @export
 * @interface CommandResult
 */
export interface CommandResult {
  /**
   *
   * @type {CommandResultType}
   * @memberof CommandResult
   */
  result: CommandResultType;
  /**
   *
   * @type {Array<DisplayText>}
   * @memberof CommandResult
   */
  message?: Array<DisplayText> | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const CommandResultType = {
  Accepted: 'ACCEPTED',
  CanceledReservation: 'CANCELED_RESERVATION',
  EvseOccupied: 'EVSE_OCCUPIED',
  EvseInoperative: 'EVSE_INOPERATIVE',
  Failed: 'FAILED',
  NotSupported: 'NOT_SUPPORTED',
  Rejected: 'REJECTED',
  UnknownReservation: 'UNKNOWN_RESERVATION',
  Timeout: 'TIMEOUT',
} as const;

export type CommandResultType =
  (typeof CommandResultType)[keyof typeof CommandResultType];

/**
 *
 * @export
 * @interface Connector
 */
export interface Connector {
  /**
   *
   * @type {string}
   * @memberof Connector
   */
  id: string;
  /**
   *
   * @type {ConnectorType}
   * @memberof Connector
   */
  standard: ConnectorType;
  /**
   *
   * @type {ConnectorFormat}
   * @memberof Connector
   */
  format: ConnectorFormat;
  /**
   *
   * @type {PowerType}
   * @memberof Connector
   */
  power_type: PowerType;
  /**
   *
   * @type {number}
   * @memberof Connector
   */
  max_voltage: number;
  /**
   *
   * @type {number}
   * @memberof Connector
   */
  max_amperage: number;
  /**
   *
   * @type {string}
   * @memberof Connector
   */
  last_updated: string;
  /**
   *
   * @type {number}
   * @memberof Connector
   */
  max_electric_power?: number | null;
  /**
   *
   * @type {Array<string>}
   * @memberof Connector
   */
  tariff_ids?: Array<string> | null;
  /**
   *
   * @type {string}
   * @memberof Connector
   */
  terms_and_conditions?: string | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const ConnectorFormat = {
  Socket: 'SOCKET',
  Cable: 'CABLE',
} as const;

export type ConnectorFormat =
  (typeof ConnectorFormat)[keyof typeof ConnectorFormat];

/**
 *
 * @export
 * @interface ConnectorPatch
 */
export interface ConnectorPatch {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof ConnectorPatch
   */
  last_updated: string;
  /**
   *
   * @type {ConnectorPatchStandard}
   * @memberof ConnectorPatch
   */
  standard?: ConnectorPatchStandard;
  /**
   *
   * @type {ConnectorPatchFormat}
   * @memberof ConnectorPatch
   */
  format?: ConnectorPatchFormat;
  /**
   *
   * @type {ConnectorPatchPowerType}
   * @memberof ConnectorPatch
   */
  power_type?: ConnectorPatchPowerType;
  /**
   *
   * @type {MaxVoltage}
   * @memberof ConnectorPatch
   */
  max_voltage?: MaxVoltage;
  /**
   *
   * @type {MaxAmperage}
   * @memberof ConnectorPatch
   */
  max_amperage?: MaxAmperage;
  /**
   *
   * @type {MaxElectricPower}
   * @memberof ConnectorPatch
   */
  max_electric_power?: MaxElectricPower;
  /**
   *
   * @type {TariffIds}
   * @memberof ConnectorPatch
   */
  tariff_ids?: TariffIds;
  /**
   *
   * @type {TermsAndConditions}
   * @memberof ConnectorPatch
   */
  terms_and_conditions?: TermsAndConditions;
}
/**
 *
 * @export
 * @interface ConnectorPatchFormat
 */
export interface ConnectorPatchFormat {}
/**
 *
 * @export
 * @interface ConnectorPatchPowerType
 */
export interface ConnectorPatchPowerType {}
/**
 *
 * @export
 * @interface ConnectorPatchStandard
 */
export interface ConnectorPatchStandard {}
/**
 *
 * @export
 * @interface ConnectorResponse
 */
export interface ConnectorResponse {
  /**
   *
   * @type {Connector}
   * @memberof ConnectorResponse
   */
  data: Connector;
  /**
   *
   * @type {number}
   * @memberof ConnectorResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof ConnectorResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof ConnectorResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @enum {string}
 */

export const ConnectorType = {
  Chademo: 'CHADEMO',
  DomesticA: 'DOMESTIC_A',
  DomesticB: 'DOMESTIC_B',
  DomesticC: 'DOMESTIC_C',
  DomesticD: 'DOMESTIC_D',
  DomesticE: 'DOMESTIC_E',
  DomesticF: 'DOMESTIC_F',
  DomesticG: 'DOMESTIC_G',
  DomesticH: 'DOMESTIC_H',
  DomesticI: 'DOMESTIC_I',
  DomesticJ: 'DOMESTIC_J',
  DomesticK: 'DOMESTIC_K',
  DomesticL: 'DOMESTIC_L',
  Iec603092Single16: 'IEC_60309_2_single_16',
  Iec603092Three16: 'IEC_60309_2_three_16',
  Iec603092Three32: 'IEC_60309_2_three_32',
  Iec603092Three64: 'IEC_60309_2_three_64',
  Iec62196T1: 'IEC_62196_T1',
  Iec62196T1Combo: 'IEC_62196_T1_COMBO',
  Iec62196T2: 'IEC_62196_T2',
  Iec62196T2Combo: 'IEC_62196_T2_COMBO',
  Iec62196T3A: 'IEC_62196_T3A',
  Iec62196T3C: 'IEC_62196_T3C',
  PantographBottomUp: 'PANTOGRAPH_BOTTOM_UP',
  PantographTopDown: 'PANTOGRAPH_TOP_DOWN',
  TeslaR: 'TESLA_R',
  TeslaS: 'TESLA_S',
  Chaoji: 'CHAOJI',
  DomesticM: 'DOMESTIC_M',
  DomesticN: 'DOMESTIC_N',
  DomesticO: 'DOMESTIC_O',
  GbtAc: 'GBT_AC',
  GbtDc: 'GBT_DC',
  Nema520: 'NEMA_5_20',
  Nema630: 'NEMA_6_30',
  Nema650: 'NEMA_6_50',
  Nema1030: 'NEMA_10_30',
  Nema1050: 'NEMA_10_50',
  Nema1430: 'NEMA_14_30',
  Nema1450: 'NEMA_14_50',
} as const;

export type ConnectorType = (typeof ConnectorType)[keyof typeof ConnectorType];

/**
 *
 * @export
 * @interface Country
 */
export interface Country {}
/**
 *
 * @export
 * @interface Credentials
 */
export interface Credentials {
  /**
   *
   * @type {string}
   * @memberof Credentials
   */
  token: string;
  /**
   *
   * @type {string}
   * @memberof Credentials
   */
  url: string;
  /**
   *
   * @type {Array<CredentialsRole>}
   * @memberof Credentials
   */
  roles: Array<CredentialsRole>;
}
/**
 *
 * @export
 * @interface CredentialsResponse
 */
export interface CredentialsResponse {
  /**
   *
   * @type {Credentials}
   * @memberof CredentialsResponse
   */
  data: Credentials;
  /**
   *
   * @type {number}
   * @memberof CredentialsResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof CredentialsResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof CredentialsResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface CredentialsRole
 */
export interface CredentialsRole {
  /**
   *
   * @type {Role}
   * @memberof CredentialsRole
   */
  role: Role;
  /**
   *
   * @type {BusinessDetails}
   * @memberof CredentialsRole
   */
  business_details: BusinessDetails;
  /**
   *
   * @type {string}
   * @memberof CredentialsRole
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof CredentialsRole
   */
  country_code: string;
}

/**
 *
 * @export
 * @interface Data
 */
export interface Data {}
/**
 *
 * @export
 * @interface DataAnyOfInner
 */
export interface DataAnyOfInner {}
/**
 *
 * @export
 * @enum {string}
 */

export const DayOfWeek = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type DayOfWeek = (typeof DayOfWeek)[keyof typeof DayOfWeek];

/**
 *
 * @export
 * @interface Directions
 */
export interface Directions {}
/**
 *
 * @export
 * @interface DisplayText
 */
export interface DisplayText {
  /**
   *
   * @type {string}
   * @memberof DisplayText
   */
  language: string;
  /**
   *
   * @type {string}
   * @memberof DisplayText
   */
  text: string;
}
/**
 *
 * @export
 * @interface EVSE
 */
export interface EVSE {
  /**
   *
   * @type {string}
   * @memberof EVSE
   */
  uid: string;
  /**
   *
   * @type {Status}
   * @memberof EVSE
   */
  status: Status;
  /**
   *
   * @type {string}
   * @memberof EVSE
   */
  last_updated: string;
  /**
   *
   * @type {string}
   * @memberof EVSE
   */
  evse_id?: string | null;
  /**
   *
   * @type {Array<DisplayText>}
   * @memberof EVSE
   */
  directions?: Array<DisplayText> | null;
  /**
   *
   * @type {Array<StatusSchedule>}
   * @memberof EVSE
   */
  status_schedule?: Array<StatusSchedule> | null;
  /**
   *
   * @type {Array<Capability>}
   * @memberof EVSE
   */
  capabilities?: Array<Capability> | null;
  /**
   *
   * @type {string}
   * @memberof EVSE
   */
  floor_level?: string | null;
  /**
   *
   * @type {GeoLocation}
   * @memberof EVSE
   */
  coordinates?: GeoLocation | null;
  /**
   *
   * @type {string}
   * @memberof EVSE
   */
  physical_reference?: string | null;
  /**
   *
   * @type {Array<ParkingRestriction>}
   * @memberof EVSE
   */
  parking_restrictions?: Array<ParkingRestriction> | null;
  /**
   *
   * @type {Array<Image>}
   * @memberof EVSE
   */
  images?: Array<Image> | null;
  /**
   *
   * @type {Array<Connector>}
   * @memberof EVSE
   */
  connectors: Array<Connector>;
}

/**
 *
 * @export
 * @interface EVSEPatch
 */
export interface EVSEPatch {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof EVSEPatch
   */
  last_updated: string;
  /**
   *
   * @type {EVSEPatchStatus}
   * @memberof EVSEPatch
   */
  status?: EVSEPatchStatus;
  /**
   *
   * @type {EvseId}
   * @memberof EVSEPatch
   */
  evse_id?: EvseId;
  /**
   *
   * @type {Directions}
   * @memberof EVSEPatch
   */
  directions?: Directions;
  /**
   *
   * @type {StatusSchedule}
   * @memberof EVSEPatch
   */
  status_schedule?: StatusSchedule;
  /**
   *
   * @type {Capabilities}
   * @memberof EVSEPatch
   */
  capabilities?: Capabilities;
  /**
   *
   * @type {FloorLevel}
   * @memberof EVSEPatch
   */
  floor_level?: FloorLevel;
  /**
   *
   * @type {LocationPatchCoordinates}
   * @memberof EVSEPatch
   */
  coordinates?: LocationPatchCoordinates;
  /**
   *
   * @type {PhysicalReference}
   * @memberof EVSEPatch
   */
  physical_reference?: PhysicalReference;
  /**
   *
   * @type {ParkingRestrictions}
   * @memberof EVSEPatch
   */
  parking_restrictions?: ParkingRestrictions;
  /**
   *
   * @type {Images}
   * @memberof EVSEPatch
   */
  images?: Images;
}
/**
 *
 * @export
 * @interface EVSEPatchStatus
 */
export interface EVSEPatchStatus {}
/**
 *
 * @export
 * @interface EVSEResponse
 */
export interface EVSEResponse {
  /**
   *
   * @type {EVSE}
   * @memberof EVSEResponse
   */
  data: EVSE;
  /**
   *
   * @type {number}
   * @memberof EVSEResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof EVSEResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof EVSEResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface EmptyResponse
 */
export interface EmptyResponse {
  /**
   *
   * @type {any}
   * @memberof EmptyResponse
   */
  data?: any | null;
  /**
   *
   * @type {number}
   * @memberof EmptyResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof EmptyResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof EmptyResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface EnergyContract
 */
export interface EnergyContract {
  /**
   *
   * @type {string}
   * @memberof EnergyContract
   */
  supplier_name: string;
  /**
   *
   * @type {string}
   * @memberof EnergyContract
   */
  contract_id?: string | null;
}
/**
 *
 * @export
 * @interface EnergyMix
 */
export interface EnergyMix {
  /**
   *
   * @type {boolean}
   * @memberof EnergyMix
   */
  is_green_energy: boolean;
  /**
   *
   * @type {Array<EnergySource>}
   * @memberof EnergyMix
   */
  energy_sources?: Array<EnergySource> | null;
  /**
   *
   * @type {Array<EnvironmentalImpact>}
   * @memberof EnergyMix
   */
  environ_impact?: Array<EnvironmentalImpact> | null;
  /**
   *
   * @type {string}
   * @memberof EnergyMix
   */
  supplier_name?: string | null;
  /**
   *
   * @type {string}
   * @memberof EnergyMix
   */
  energy_product_name?: string | null;
}
/**
 *
 * @export
 * @interface EnergySource
 */
export interface EnergySource {
  /**
   *
   * @type {EnergySourceCategory}
   * @memberof EnergySource
   */
  source: EnergySourceCategory;
  /**
   *
   * @type {number}
   * @memberof EnergySource
   */
  percentage: number;
}

/**
 *
 * @export
 * @enum {string}
 */

export const EnergySourceCategory = {
  Nuclear: 'NUCLEAR',
  GeneralFossil: 'GENERAL_FOSSIL',
  Coal: 'COAL',
  Gas: 'GAS',
  GeneralGreen: 'GENERAL_GREEN',
  Solar: 'SOLAR',
  Wind: 'WIND',
  Water: 'WATER',
} as const;

export type EnergySourceCategory =
  (typeof EnergySourceCategory)[keyof typeof EnergySourceCategory];

/**
 * category: The environmental impact category of this value amount:   Amount of this portion in g/kWh.
 * @export
 * @interface EnvironmentalImpact
 */
export interface EnvironmentalImpact {
  /**
   *
   * @type {EnvironmentalImpactCategory}
   * @memberof EnvironmentalImpact
   */
  category: EnvironmentalImpactCategory;
  /**
   *
   * @type {number}
   * @memberof EnvironmentalImpact
   */
  amount: number;
}

/**
 *
 * @export
 * @enum {string}
 */

export const EnvironmentalImpactCategory = {
  NuclearWaste: 'NUCLEAR_WASTE',
  CarbonDioxide: 'CARBON_DIOXIDE',
} as const;

export type EnvironmentalImpactCategory =
  (typeof EnvironmentalImpactCategory)[keyof typeof EnvironmentalImpactCategory];

/**
 *
 * @export
 * @interface Error4XX
 */
export interface Error4XX {
  /**
   *
   * @type {string}
   * @memberof Error4XX
   */
  detail: string;
}
/**
 *
 * @export
 * @interface EvseId
 */
export interface EvseId {}
/**
 *
 * @export
 * @interface ExceptionalPeriod
 */
export interface ExceptionalPeriod {
  /**
   *
   * @type {string}
   * @memberof ExceptionalPeriod
   */
  period_begin: string;
  /**
   *
   * @type {string}
   * @memberof ExceptionalPeriod
   */
  period_end: string;
}
/**
 *
 * @export
 * @interface Facilities
 */
export interface Facilities {}
/**
 *
 * @export
 * @enum {string}
 */

export const Facility = {
  Hotel: 'HOTEL',
  Restaurant: 'RESTAURANT',
  Cafe: 'CAFE',
  Mall: 'MALL',
  Supermarket: 'SUPERMARKET',
  Sport: 'SPORT',
  RecreationArea: 'RECREATION_AREA',
  Nature: 'NATURE',
  Museum: 'MUSEUM',
  BikeSharing: 'BIKE_SHARING',
  BusStop: 'BUS_STOP',
  TaxiStand: 'TAXI_STAND',
  TramStop: 'TRAM_STOP',
  MetroStation: 'METRO_STATION',
  TrainStation: 'TRAIN_STATION',
  Airport: 'AIRPORT',
  ParkingLot: 'PARKING_LOT',
  CarpoolParking: 'CARPOOL_PARKING',
  FuelStation: 'FUEL_STATION',
  Wifi: 'WIFI',
} as const;

export type Facility = (typeof Facility)[keyof typeof Facility];

/**
 *
 * @export
 * @interface FloorLevel
 */
export interface FloorLevel {}
/**
 *
 * @export
 * @interface GeoLocation
 */
export interface GeoLocation {
  /**
   *
   * @type {string}
   * @memberof GeoLocation
   */
  latitude: string;
  /**
   *
   * @type {string}
   * @memberof GeoLocation
   */
  longitude: string;
}
/**
 *
 * @export
 * @interface Hours
 */
export interface Hours {
  /**
   *
   * @type {boolean}
   * @memberof Hours
   */
  twentyfourseven: boolean;
  /**
   *
   * @type {Array<RegularHours>}
   * @memberof Hours
   */
  regular_hours?: Array<RegularHours> | null;
  /**
   *
   * @type {Array<ExceptionalPeriod>}
   * @memberof Hours
   */
  exceptional_openings?: Array<ExceptionalPeriod> | null;
  /**
   *
   * @type {Array<ExceptionalPeriod>}
   * @memberof Hours
   */
  exceptional_closings?: Array<ExceptionalPeriod> | null;
}
/**
 *
 * @export
 * @interface Image
 */
export interface Image {
  /**
   *
   * @type {string}
   * @memberof Image
   */
  url: string;
  /**
   *
   * @type {ImageCategory}
   * @memberof Image
   */
  category: ImageCategory;
  /**
   *
   * @type {string}
   * @memberof Image
   */
  type?: string;
  /**
   *
   * @type {string}
   * @memberof Image
   */
  thumbnail?: string | null;
  /**
   *
   * @type {number}
   * @memberof Image
   */
  width?: number | null;
  /**
   *
   * @type {number}
   * @memberof Image
   */
  height?: number | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const ImageCategory = {
  Charger: 'CHARGER',
  Entrance: 'ENTRANCE',
  Location: 'LOCATION',
  Network: 'NETWORK',
  Operator: 'OPERATOR',
  Other: 'OTHER',
  Owner: 'OWNER',
} as const;

export type ImageCategory = (typeof ImageCategory)[keyof typeof ImageCategory];

/**
 *
 * @export
 * @interface Images
 */
export interface Images {}
/**
 *
 * @export
 * @interface Location
 */
export interface Location {
  /**
   *
   * @type {string}
   * @memberof Location
   */
  country_code: string;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  id: string;
  /**
   *
   * @type {boolean}
   * @memberof Location
   */
  publish: boolean;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  address: string;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  city: string;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  country: string;
  /**
   *
   * @type {GeoLocation}
   * @memberof Location
   */
  coordinates: GeoLocation;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  time_zone: string;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  last_updated: string;
  /**
   *
   * @type {Array<object>}
   * @memberof Location
   */
  publish_allowed_to?: Array<object> | null;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  name?: string | null;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  postal_code?: string | null;
  /**
   *
   * @type {string}
   * @memberof Location
   */
  state?: string | null;
  /**
   *
   * @type {Array<object>}
   * @memberof Location
   */
  related_locations?: Array<object> | null;
  /**
   *
   * @type {ParkingType}
   * @memberof Location
   */
  parking_type?: ParkingType | null;
  /**
   *
   * @type {Array<DisplayText>}
   * @memberof Location
   */
  directions?: Array<DisplayText> | null;
  /**
   *
   * @type {BusinessDetails}
   * @memberof Location
   */
  operator?: BusinessDetails | null;
  /**
   *
   * @type {BusinessDetails}
   * @memberof Location
   */
  suboperator?: BusinessDetails | null;
  /**
   *
   * @type {BusinessDetails}
   * @memberof Location
   */
  owner?: BusinessDetails | null;
  /**
   *
   * @type {Array<Facility>}
   * @memberof Location
   */
  facilities?: Array<Facility> | null;
  /**
   *
   * @type {Hours}
   * @memberof Location
   */
  opening_times?: Hours | null;
  /**
   *
   * @type {boolean}
   * @memberof Location
   */
  charging_when_closed?: boolean | null;
  /**
   *
   * @type {Array<Image>}
   * @memberof Location
   */
  images?: Array<Image> | null;
  /**
   *
   * @type {EnergyMix}
   * @memberof Location
   */
  energy_mix?: EnergyMix | null;
  /**
   *
   * @type {Array<EVSE>}
   * @memberof Location
   */
  evses?: Array<EVSE> | null;
}

/**
 *
 * @export
 * @interface LocationPatch
 */
export interface LocationPatch {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof LocationPatch
   */
  last_updated: string;
  /**
   *
   * @type {Publish}
   * @memberof LocationPatch
   */
  publish?: Publish;
  /**
   *
   * @type {Address}
   * @memberof LocationPatch
   */
  address?: Address;
  /**
   *
   * @type {City}
   * @memberof LocationPatch
   */
  city?: City;
  /**
   *
   * @type {Country}
   * @memberof LocationPatch
   */
  country?: Country;
  /**
   *
   * @type {LocationPatchCoordinates}
   * @memberof LocationPatch
   */
  coordinates?: LocationPatchCoordinates;
  /**
   *
   * @type {TimeZone}
   * @memberof LocationPatch
   */
  time_zone?: TimeZone;
  /**
   *
   * @type {PublishAllowedTo}
   * @memberof LocationPatch
   */
  publish_allowed_to?: PublishAllowedTo;
  /**
   *
   * @type {Name}
   * @memberof LocationPatch
   */
  name?: Name;
  /**
   *
   * @type {PostalCode}
   * @memberof LocationPatch
   */
  postal_code?: PostalCode;
  /**
   *
   * @type {State}
   * @memberof LocationPatch
   */
  state?: State;
  /**
   *
   * @type {RelatedLocations}
   * @memberof LocationPatch
   */
  related_locations?: RelatedLocations;
  /**
   *
   * @type {LocationPatchParkingType}
   * @memberof LocationPatch
   */
  parking_type?: LocationPatchParkingType;
  /**
   *
   * @type {Directions}
   * @memberof LocationPatch
   */
  directions?: Directions;
  /**
   *
   * @type {LocationPatchOperator}
   * @memberof LocationPatch
   */
  operator?: LocationPatchOperator;
  /**
   *
   * @type {LocationPatchOperator}
   * @memberof LocationPatch
   */
  suboperator?: LocationPatchOperator;
  /**
   *
   * @type {LocationPatchOperator}
   * @memberof LocationPatch
   */
  owner?: LocationPatchOperator;
  /**
   *
   * @type {Facilities}
   * @memberof LocationPatch
   */
  facilities?: Facilities;
  /**
   *
   * @type {LocationPatchOpeningTimes}
   * @memberof LocationPatch
   */
  opening_times?: LocationPatchOpeningTimes;
  /**
   *
   * @type {ChargingWhenClosed}
   * @memberof LocationPatch
   */
  charging_when_closed?: ChargingWhenClosed;
  /**
   *
   * @type {Images}
   * @memberof LocationPatch
   */
  images?: Images;
  /**
   *
   * @type {LocationPatchEnergyMix}
   * @memberof LocationPatch
   */
  energy_mix?: LocationPatchEnergyMix;
}
/**
 *
 * @export
 * @interface LocationPatchCoordinates
 */
export interface LocationPatchCoordinates {
  /**
   *
   * @type {string}
   * @memberof LocationPatchCoordinates
   */
  latitude: string;
  /**
   *
   * @type {string}
   * @memberof LocationPatchCoordinates
   */
  longitude: string;
}
/**
 *
 * @export
 * @interface LocationPatchEnergyMix
 */
export interface LocationPatchEnergyMix {
  /**
   *
   * @type {boolean}
   * @memberof LocationPatchEnergyMix
   */
  is_green_energy: boolean;
  /**
   *
   * @type {Array<EnergySource>}
   * @memberof LocationPatchEnergyMix
   */
  energy_sources?: Array<EnergySource>;
  /**
   *
   * @type {Array<EnvironmentalImpact>}
   * @memberof LocationPatchEnergyMix
   */
  environ_impact?: Array<EnvironmentalImpact>;
  /**
   *
   * @type {string}
   * @memberof LocationPatchEnergyMix
   */
  supplier_name?: string;
  /**
   *
   * @type {string}
   * @memberof LocationPatchEnergyMix
   */
  energy_product_name?: string;
}
/**
 *
 * @export
 * @interface LocationPatchOpeningTimes
 */
export interface LocationPatchOpeningTimes {
  /**
   *
   * @type {boolean}
   * @memberof LocationPatchOpeningTimes
   */
  twentyfourseven: boolean;
  /**
   *
   * @type {Array<RegularHours>}
   * @memberof LocationPatchOpeningTimes
   */
  regular_hours?: Array<RegularHours>;
  /**
   *
   * @type {Array<ExceptionalPeriod>}
   * @memberof LocationPatchOpeningTimes
   */
  exceptional_openings?: Array<ExceptionalPeriod>;
  /**
   *
   * @type {Array<ExceptionalPeriod>}
   * @memberof LocationPatchOpeningTimes
   */
  exceptional_closings?: Array<ExceptionalPeriod>;
}
/**
 *
 * @export
 * @interface LocationPatchOperator
 */
export interface LocationPatchOperator {
  /**
   *
   * @type {string}
   * @memberof LocationPatchOperator
   */
  name: string;
  /**
   *
   * @type {Image}
   * @memberof LocationPatchOperator
   */
  logo?: Image;
  /**
   *
   * @type {string}
   * @memberof LocationPatchOperator
   */
  website?: string;
}
/**
 *
 * @export
 * @interface LocationPatchParkingType
 */
export interface LocationPatchParkingType {}
/**
 *
 * @export
 * @interface LocationReferences
 */
export interface LocationReferences {
  /**
   *
   * @type {string}
   * @memberof LocationReferences
   */
  location_id: string;
  /**
   *
   * @type {Array<string>}
   * @memberof LocationReferences
   */
  evse_uids?: Array<string> | null;
}
/**
 *
 * @export
 * @interface LocationResponse
 */
export interface LocationResponse {
  /**
   *
   * @type {Location}
   * @memberof LocationResponse
   */
  data: Location;
  /**
   *
   * @type {number}
   * @memberof LocationResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof LocationResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof LocationResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @interface MaxAmperage
 */
export interface MaxAmperage {}
/**
 *
 * @export
 * @interface MaxElectricPower
 */
export interface MaxElectricPower {}
/**
 *
 * @export
 * @interface MaxVoltage
 */
export interface MaxVoltage {}
/**
 *
 * @export
 * @interface Name
 */
export interface Name {}
/**
 *
 * @export
 * @interface OCPIResponse
 */
export interface OCPIResponse {
  /**
   *
   * @type {Data}
   * @memberof OCPIResponse
   */
  data: Data | null;
  /**
   *
   * @type {number}
   * @memberof OCPIResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof OCPIResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof OCPIResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @enum {string}
 */

export const ParkingRestriction = {
  EvOnly: 'EV_ONLY',
  Plugged: 'PLUGGED',
  Disabled: 'DISABLED',
  Customers: 'CUSTOMERS',
  Motorcycles: 'MOTORCYCLES',
} as const;

export type ParkingRestriction =
  (typeof ParkingRestriction)[keyof typeof ParkingRestriction];

/**
 *
 * @export
 * @interface ParkingRestrictions
 */
export interface ParkingRestrictions {}
/**
 *
 * @export
 * @enum {string}
 */

export const ParkingType = {
  AlongMotorway: 'ALONG_MOTORWAY',
  ParkingGarage: 'PARKING_GARAGE',
  ParkingLot: 'PARKING_LOT',
  OnDriveway: 'ON_DRIVEWAY',
  OnStreet: 'ON_STREET',
  UndergroundGarage: 'UNDERGROUND_GARAGE',
} as const;

export type ParkingType = (typeof ParkingType)[keyof typeof ParkingType];

/**
 *
 * @export
 * @interface PhysicalReference
 */
export interface PhysicalReference {}
/**
 *
 * @export
 * @interface PostalCode
 */
export interface PostalCode {}
/**
 *
 * @export
 * @enum {string}
 */

export const PowerType = {
  Ac1Phase: 'AC_1_PHASE',
  Ac3Phase: 'AC_3_PHASE',
  Dc: 'DC',
  Ac2Phase: 'AC_2_PHASE',
  Ac2PhaseSplit: 'AC_2_PHASE_SPLIT',
} as const;

export type PowerType = (typeof PowerType)[keyof typeof PowerType];

/**
 *
 * @export
 * @interface Price
 */
export interface Price {
  /**
   *
   * @type {number}
   * @memberof Price
   */
  excl_vat: number;
  /**
   *
   * @type {number}
   * @memberof Price
   */
  incl_vat: number;
}
/**
 *
 * @export
 * @interface PriceComponent
 */
export interface PriceComponent {
  /**
   *
   * @type {TariffDimensionType}
   * @memberof PriceComponent
   */
  type: TariffDimensionType;
  /**
   *
   * @type {number}
   * @memberof PriceComponent
   */
  price: number;
  /**
   *
   * @type {number}
   * @memberof PriceComponent
   */
  step_size: number;
  /**
   *
   * @type {number}
   * @memberof PriceComponent
   */
  vat?: number | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const ProfileType = {
  Cheap: 'CHEAP',
  Fast: 'FAST',
  Green: 'GREEN',
  Regular: 'REGULAR',
} as const;

export type ProfileType = (typeof ProfileType)[keyof typeof ProfileType];

/**
 *
 * @export
 * @interface Publish
 */
export interface Publish {}
/**
 *
 * @export
 * @interface PublishAllowedTo
 */
export interface PublishAllowedTo {}
/**
 *
 * @export
 * @interface RealtimeAuthBody
 */
export interface RealtimeAuthBody {
  /**
   *
   * @type {LocationReferences}
   * @memberof RealtimeAuthBody
   */
  LocationReferences?: LocationReferences | null;
}
/**
 * weekday:      Number of day in the week, from Monday (1) till Sunday (7) period_begin: Begin of the regular period, in local time, given in hours               and minutes. Must be in 24h format with leading zeros.               Example:                 \"18:15\"                 Hour/Minute separator: \":\"                 Regex: ([0-1][0-9]|2[0-3]):[0-5][0-9]. period_end:   End of the regular period, in local time, syntax as for               period_begin. Must be later than period_begin.
 * @export
 * @interface RegularHours
 */
export interface RegularHours {
  /**
   *
   * @type {number}
   * @memberof RegularHours
   */
  weekday: number;
  /**
   *
   * @type {string}
   * @memberof RegularHours
   */
  period_begin: string;
  /**
   *
   * @type {string}
   * @memberof RegularHours
   */
  period_end: string;
}
/**
 *
 * @export
 * @interface RelatedLocations
 */
export interface RelatedLocations {}
/**
 *
 * @export
 * @enum {string}
 */

export const ReservationRestrictionType = {
  Reservation: 'RESERVATION',
  ReservationExpires: 'RESERVATION_EXPIRES',
} as const;

export type ReservationRestrictionType =
  (typeof ReservationRestrictionType)[keyof typeof ReservationRestrictionType];

/**
 *
 * @export
 * @enum {string}
 */

export const Role = {
  Cpo: 'CPO',
  Emsp: 'EMSP',
  Hub: 'HUB',
  Nap: 'NAP',
  Nsp: 'NSP',
  Other: 'OTHER',
  Scsp: 'SCSP',
} as const;

export type Role = (typeof Role)[keyof typeof Role];

/**
 *
 * @export
 * @interface Session
 */
export interface Session {
  /**
   *
   * @type {string}
   * @memberof Session
   */
  country_code: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  start_date_time: string;
  /**
   *
   * @type {number}
   * @memberof Session
   */
  kwh: number;
  /**
   *
   * @type {CdrToken}
   * @memberof Session
   */
  cdr_token: CdrToken;
  /**
   *
   * @type {AuthMethod}
   * @memberof Session
   */
  auth_method: AuthMethod;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  location_id: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  evse_uid: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  connector_id: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  currency: string;
  /**
   *
   * @type {SessionStatus}
   * @memberof Session
   */
  status: SessionStatus;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  last_updated: string;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  end_date_time?: string | null;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  authorization_reference?: string | null;
  /**
   *
   * @type {string}
   * @memberof Session
   */
  meter_id?: string | null;
  /**
   *
   * @type {Array<ChargingPeriod>}
   * @memberof Session
   */
  charging_periods?: Array<ChargingPeriod> | null;
  /**
   *
   * @type {Price}
   * @memberof Session
   */
  total_cost?: Price | null;
}

/**
 *
 * @export
 * @interface SessionPatch
 */
export interface SessionPatch {
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  last_updated: string;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  start_date_time?: string | null;
  /**
   *
   * @type {number}
   * @memberof SessionPatch
   */
  kwh?: number | null;
  /**
   *
   * @type {CdrToken}
   * @memberof SessionPatch
   */
  cdr_token?: CdrToken | null;
  /**
   *
   * @type {AuthMethod}
   * @memberof SessionPatch
   */
  auth_method?: AuthMethod | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  location_id?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  evse_uid?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  connector_id?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  currency?: string | null;
  /**
   *
   * @type {SessionStatus}
   * @memberof SessionPatch
   */
  status?: SessionStatus | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  end_date_time?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  authorization_reference?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionPatch
   */
  meter_id?: string | null;
  /**
   *
   * @type {Array<ChargingPeriod>}
   * @memberof SessionPatch
   */
  charging_periods?: Array<ChargingPeriod> | null;
  /**
   *
   * @type {Price}
   * @memberof SessionPatch
   */
  total_cost?: Price | null;
}

/**
 *
 * @export
 * @interface SessionResponse
 */
export interface SessionResponse {
  /**
   *
   * @type {Session}
   * @memberof SessionResponse
   */
  data: Session;
  /**
   *
   * @type {number}
   * @memberof SessionResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof SessionResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof SessionResponse
   */
  timestamp?: string | null;
}
/**
 *
 * @export
 * @enum {string}
 */

export const SessionStatus = {
  Active: 'ACTIVE',
  Completed: 'COMPLETED',
  Invalid: 'INVALID',
  Pending: 'PENDING',
  Reservation: 'RESERVATION',
} as const;

export type SessionStatus = (typeof SessionStatus)[keyof typeof SessionStatus];

/**
 *
 * @export
 * @interface SignedData
 */
export interface SignedData {
  /**
   *
   * @type {string}
   * @memberof SignedData
   */
  encoding_method: string;
  /**
   *
   * @type {number}
   * @memberof SignedData
   */
  encoding_method_version?: number | null;
  /**
   *
   * @type {string}
   * @memberof SignedData
   */
  public_key?: string | null;
  /**
   *
   * @type {Array<SignedValue>}
   * @memberof SignedData
   */
  signed_value?: Array<SignedValue> | null;
  /**
   *
   * @type {string}
   * @memberof SignedData
   */
  url?: string | null;
}
/**
 *
 * @export
 * @interface SignedValue
 */
export interface SignedValue {
  /**
   *
   * @type {string}
   * @memberof SignedValue
   */
  nature: string;
  /**
   *
   * @type {string}
   * @memberof SignedValue
   */
  plain_data: string;
  /**
   *
   * @type {string}
   * @memberof SignedValue
   */
  signed_data: string;
}
/**
 *
 * @export
 * @interface State
 */
export interface State {}
/**
 *
 * @export
 * @enum {string}
 */

export const Status = {
  Available: 'AVAILABLE',
  Blocked: 'BLOCKED',
  Charging: 'CHARGING',
  Inoperative: 'INOPERATIVE',
  Outoforder: 'OUTOFORDER',
  Planned: 'PLANNED',
  Removed: 'REMOVED',
  Reserved: 'RESERVED',
  Unknown: 'UNKNOWN',
} as const;

export type Status = (typeof Status)[keyof typeof Status];

/**
 *
 * @export
 * @interface StatusSchedule
 */
export interface StatusSchedule {}
/**
 *
 * @export
 * @interface Tariff
 */
export interface Tariff {
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  country_code: string;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  currency: string;
  /**
   *
   * @type {Array<TariffElement>}
   * @memberof Tariff
   */
  elements: Array<TariffElement>;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  last_updated: string;
  /**
   *
   * @type {TariffType}
   * @memberof Tariff
   */
  type?: TariffType | null;
  /**
   *
   * @type {Array<DisplayText>}
   * @memberof Tariff
   */
  tariff_alt_text?: Array<DisplayText> | null;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  tariff_alt_url?: string | null;
  /**
   *
   * @type {Price}
   * @memberof Tariff
   */
  min_price?: Price | null;
  /**
   *
   * @type {Price}
   * @memberof Tariff
   */
  max_price?: Price | null;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  start_date_time?: string | null;
  /**
   *
   * @type {string}
   * @memberof Tariff
   */
  end_date_time?: string | null;
  /**
   *
   * @type {EnergyMix}
   * @memberof Tariff
   */
  energy_mix?: EnergyMix | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const TariffDimensionType = {
  Energy: 'ENERGY',
  Flat: 'FLAT',
  ParkingTime: 'PARKING_TIME',
  Time: 'TIME',
} as const;

export type TariffDimensionType =
  (typeof TariffDimensionType)[keyof typeof TariffDimensionType];

/**
 *
 * @export
 * @interface TariffElement
 */
export interface TariffElement {
  /**
   *
   * @type {Array<PriceComponent>}
   * @memberof TariffElement
   */
  price_components: Array<PriceComponent>;
  /**
   *
   * @type {TariffRestrictions}
   * @memberof TariffElement
   */
  restrictions?: TariffRestrictions | null;
}
/**
 *
 * @export
 * @interface TariffIds
 */
export interface TariffIds {}
/**
 *
 * @export
 * @interface TariffResponse
 */
export interface TariffResponse {
  /**
   *
   * @type {Tariff}
   * @memberof TariffResponse
   */
  data: Tariff;
  /**
   *
   * @type {number}
   * @memberof TariffResponse
   */
  status_code: number;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  status_message?: string | null;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  timestamp?: string | null;
}
/**
 * start_time: Start time of day in local time, the time zone is defined in         the time_zone field of the Location, for example 13:30,         valid from this time of the day.         Must be in 24h format with leading zeros.         Hour/Minute separator: \":\" Regex: ([0-1][0- 9]|2[0-3]):[0-5][0-9] end_time: End time of day in local time, the time zone is defined in the         time_zone field of the Location, for example 19:45,         valid until this time of the day.         Same syntax as start_time.         If end_time < start_time then the period wraps around to the         next day. To stop at end of the day use: 00:00. start_date: Start date in local time, the time zone is defined in the         time_zone field of the Location, for example: 2015-12-24,         valid from this day (inclusive).         Regex: ([12][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])
 * @export
 * @interface TariffRestrictions
 */
export interface TariffRestrictions {
  /**
   *
   * @type {string}
   * @memberof TariffRestrictions
   */
  start_time?: string | null;
  /**
   *
   * @type {string}
   * @memberof TariffRestrictions
   */
  end_time?: string | null;
  /**
   *
   * @type {string}
   * @memberof TariffRestrictions
   */
  start_date?: string | null;
  /**
   *
   * @type {string}
   * @memberof TariffRestrictions
   */
  end_date?: string | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  min_kwh?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  max_kwh?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  min_current?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  max_current?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  min_power?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  max_power?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  min_duration?: number | null;
  /**
   *
   * @type {number}
   * @memberof TariffRestrictions
   */
  max_duration?: number | null;
  /**
   *
   * @type {Array<DayOfWeek>}
   * @memberof TariffRestrictions
   */
  day_of_week?: Array<DayOfWeek> | null;
  /**
   *
   * @type {ReservationRestrictionType}
   * @memberof TariffRestrictions
   */
  reservation?: ReservationRestrictionType | null;
}

/**
 *
 * @export
 * @enum {string}
 */

export const TariffType = {
  AdHocPayment: 'AD_HOC_PAYMENT',
  ProfileCheap: 'PROFILE_CHEAP',
  ProfileFast: 'PROFILE_FAST',
  ProfileGreen: 'PROFILE_GREEN',
  Regular: 'REGULAR',
} as const;

export type TariffType = (typeof TariffType)[keyof typeof TariffType];

/**
 *
 * @export
 * @interface TermsAndConditions
 */
export interface TermsAndConditions {}
/**
 *
 * @export
 * @interface TimeZone
 */
export interface TimeZone {}
/**
 *
 * @export
 * @interface Token
 */
export interface Token {
  /**
   *
   * @type {string}
   * @memberof Token
   */
  country_code: string;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  party_id: string;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  uid: string;
  /**
   *
   * @type {TokenType}
   * @memberof Token
   */
  type: TokenType;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  contract_id: string;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  visual_number?: string | null;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  issuer: string;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  group_id?: string | null;
  /**
   *
   * @type {boolean}
   * @memberof Token
   */
  valid: boolean;
  /**
   *
   * @type {WhitelistType}
   * @memberof Token
   */
  whitelist: WhitelistType;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  language?: string | null;
  /**
   *
   * @type {ProfileType}
   * @memberof Token
   */
  default_profile_type?: ProfileType | null;
  /**
   *
   * @type {EnergyContract}
   * @memberof Token
   */
  energy_contract?: EnergyContract | null;
  /**
   *
   * @type {string}
   * @memberof Token
   */
  last_updated: string;
}

/**
 *
 * @export
 * @enum {string}
 */

export const TokenType = {
  AdHocUser: 'AD_HOC_USER',
  AppUser: 'APP_USER',
  Other: 'OTHER',
  Rfid: 'RFID',
} as const;

export type TokenType = (typeof TokenType)[keyof typeof TokenType];

/**
 *
 * @export
 * @enum {string}
 */

export const WhitelistType = {
  Always: 'ALWAYS',
  Allowed: 'ALLOWED',
  AllowedOffline: 'ALLOWED_OFFLINE',
  Never: 'NEVER',
} as const;

export type WhitelistType = (typeof WhitelistType)[keyof typeof WhitelistType];

/**
 * CdrsModuleApi - axios parameter creator
 * @export
 */
export const CdrsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get Cdr
     * @param {string} cdrId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCdrsViewsGetCdr: async (
      cdrId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'cdrId' is not null or undefined
      assertParamExists('srcOcpi22InterfacesCdrsViewsGetCdr', 'cdrId', cdrId);
      const localVarPath = `/ocpi/2.2.1/cdrs/{cdr_id}`.replace(
        `{${'cdr_id'}}`,
        encodeURIComponent(String(cdrId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Post Cdr
     * @param {Cdr} cdr
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCdrsViewsPostCdr: async (
      cdr: Cdr,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'cdr' is not null or undefined
      assertParamExists('srcOcpi22InterfacesCdrsViewsPostCdr', 'cdr', cdr);
      const localVarPath = `/ocpi/2.2.1/cdrs/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        cdr,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CdrsModuleApi - functional programming interface
 * @export
 */
export const CdrsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CdrsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get Cdr
     * @param {string} cdrId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesCdrsViewsGetCdr(
      cdrId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<CdrResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesCdrsViewsGetCdr(
          cdrId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CdrsModuleApi.srcOcpi22InterfacesCdrsViewsGetCdr'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Post Cdr
     * @param {Cdr} cdr
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesCdrsViewsPostCdr(
      cdr: Cdr,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<EmptyResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesCdrsViewsPostCdr(
          cdr,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CdrsModuleApi.srcOcpi22InterfacesCdrsViewsPostCdr'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CdrsModuleApi - factory interface
 * @export
 */
export const CdrsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CdrsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Get Cdr
     * @param {string} cdrId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCdrsViewsGetCdr(
      cdrId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CdrResponse> {
      return localVarFp
        .srcOcpi22InterfacesCdrsViewsGetCdr(cdrId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Post Cdr
     * @param {Cdr} cdr
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCdrsViewsPostCdr(
      cdr: Cdr,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EmptyResponse> {
      return localVarFp
        .srcOcpi22InterfacesCdrsViewsPostCdr(cdr, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CdrsModuleApi - object-oriented interface
 * @export
 * @class CdrsModuleApi
 * @extends {BaseAPI}
 */
export class CdrsModuleApi extends BaseAPI {
  /**
   *
   * @summary Get Cdr
   * @param {string} cdrId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CdrsModuleApi
   */
  public srcOcpi22InterfacesCdrsViewsGetCdr(
    cdrId: string,
    options?: RawAxiosRequestConfig
  ) {
    return CdrsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesCdrsViewsGetCdr(cdrId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Post Cdr
   * @param {Cdr} cdr
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CdrsModuleApi
   */
  public srcOcpi22InterfacesCdrsViewsPostCdr(
    cdr: Cdr,
    options?: RawAxiosRequestConfig
  ) {
    return CdrsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesCdrsViewsPostCdr(cdr, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CommandsModuleApi - axios parameter creator
 * @export
 */
export const CommandsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Post Command
     * @param {string} commandIdentifier
     * @param {CommandResult} commandResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCommandsViewsPostCommand: async (
      commandIdentifier: string,
      commandResult: CommandResult,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'commandIdentifier' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesCommandsViewsPostCommand',
        'commandIdentifier',
        commandIdentifier
      );
      // verify required parameter 'commandResult' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesCommandsViewsPostCommand',
        'commandResult',
        commandResult
      );
      const localVarPath = `/ocpi/2.2.1/commands/{command_identifier}`.replace(
        `{${'command_identifier'}}`,
        encodeURIComponent(String(commandIdentifier))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        commandResult,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CommandsModuleApi - functional programming interface
 * @export
 */
export const CommandsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CommandsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Post Command
     * @param {string} commandIdentifier
     * @param {CommandResult} commandResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesCommandsViewsPostCommand(
      commandIdentifier: string,
      commandResult: CommandResult,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<EmptyResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesCommandsViewsPostCommand(
          commandIdentifier,
          commandResult,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CommandsModuleApi.srcOcpi22InterfacesCommandsViewsPostCommand'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CommandsModuleApi - factory interface
 * @export
 */
export const CommandsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CommandsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Post Command
     * @param {string} commandIdentifier
     * @param {CommandResult} commandResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCommandsViewsPostCommand(
      commandIdentifier: string,
      commandResult: CommandResult,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EmptyResponse> {
      return localVarFp
        .srcOcpi22InterfacesCommandsViewsPostCommand(
          commandIdentifier,
          commandResult,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CommandsModuleApi - object-oriented interface
 * @export
 * @class CommandsModuleApi
 * @extends {BaseAPI}
 */
export class CommandsModuleApi extends BaseAPI {
  /**
   *
   * @summary Post Command
   * @param {string} commandIdentifier
   * @param {CommandResult} commandResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CommandsModuleApi
   */
  public srcOcpi22InterfacesCommandsViewsPostCommand(
    commandIdentifier: string,
    commandResult: CommandResult,
    options?: RawAxiosRequestConfig
  ) {
    return CommandsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesCommandsViewsPostCommand(
        commandIdentifier,
        commandResult,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CredentialsModuleApi - axios parameter creator
 * @export
 */
export const CredentialsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Post Credentials
     * @param {CPOCredentials} cPOCredentials
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCredentialsViewsPostCredentials: async (
      cPOCredentials: CPOCredentials,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'cPOCredentials' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesCredentialsViewsPostCredentials',
        'cPOCredentials',
        cPOCredentials
      );
      const localVarPath = `/ocpi/2.2.1/credentials/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication CredentialsReceiverAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        cPOCredentials,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CredentialsModuleApi - functional programming interface
 * @export
 */
export const CredentialsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CredentialsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Post Credentials
     * @param {CPOCredentials} cPOCredentials
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesCredentialsViewsPostCredentials(
      cPOCredentials: CPOCredentials,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CredentialsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesCredentialsViewsPostCredentials(
          cPOCredentials,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CredentialsModuleApi.srcOcpi22InterfacesCredentialsViewsPostCredentials'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CredentialsModuleApi - factory interface
 * @export
 */
export const CredentialsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CredentialsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Post Credentials
     * @param {CPOCredentials} cPOCredentials
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesCredentialsViewsPostCredentials(
      cPOCredentials: CPOCredentials,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CredentialsResponse> {
      return localVarFp
        .srcOcpi22InterfacesCredentialsViewsPostCredentials(
          cPOCredentials,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CredentialsModuleApi - object-oriented interface
 * @export
 * @class CredentialsModuleApi
 * @extends {BaseAPI}
 */
export class CredentialsModuleApi extends BaseAPI {
  /**
   *
   * @summary Post Credentials
   * @param {CPOCredentials} cPOCredentials
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CredentialsModuleApi
   */
  public srcOcpi22InterfacesCredentialsViewsPostCredentials(
    cPOCredentials: CPOCredentials,
    options?: RawAxiosRequestConfig
  ) {
    return CredentialsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesCredentialsViewsPostCredentials(
        cPOCredentials,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DetailsModuleApi - axios parameter creator
 * @export
 */
export const DetailsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get all the endpoints supported by the OCPI 2.2.1 version.
     * @summary OCPI 2.2.1 Details
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesDetailsOcpi221Details: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/ocpi/2.2.1/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DetailsModuleApi - functional programming interface
 * @export
 */
export const DetailsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    DetailsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     * Get all the endpoints supported by the OCPI 2.2.1 version.
     * @summary OCPI 2.2.1 Details
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesDetailsOcpi221Details(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesDetailsOcpi221Details(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DetailsModuleApi.srcOcpi22InterfacesDetailsOcpi221Details'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DetailsModuleApi - factory interface
 * @export
 */
export const DetailsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DetailsModuleApiFp(configuration);
  return {
    /**
     * Get all the endpoints supported by the OCPI 2.2.1 version.
     * @summary OCPI 2.2.1 Details
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesDetailsOcpi221Details(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .srcOcpi22InterfacesDetailsOcpi221Details(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DetailsModuleApi - object-oriented interface
 * @export
 * @class DetailsModuleApi
 * @extends {BaseAPI}
 */
export class DetailsModuleApi extends BaseAPI {
  /**
   * Get all the endpoints supported by the OCPI 2.2.1 version.
   * @summary OCPI 2.2.1 Details
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DetailsModuleApi
   */
  public srcOcpi22InterfacesDetailsOcpi221Details(
    options?: RawAxiosRequestConfig
  ) {
    return DetailsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesDetailsOcpi221Details(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * LocationsModuleApi - axios parameter creator
 * @export
 */
export const LocationsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetConnector: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetConnector',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetConnector',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetConnector',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetConnector',
        'evseId',
        evseId
      );
      // verify required parameter 'connectorId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetConnector',
        'connectorId',
        connectorId
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}/{connector_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)))
          .replace(
            `{${'connector_id'}}`,
            encodeURIComponent(String(connectorId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetEvse: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetEvse',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetEvse',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetEvse',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetEvse',
        'evseId',
        evseId
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetLocation: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetLocation',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetLocation',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsGetLocation',
        'locationId',
        locationId
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(
            `{${'location_id'}}`,
            encodeURIComponent(String(locationId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Patch Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {ConnectorPatch} connectorPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchConnector: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connectorPatch: ConnectorPatch,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'evseId',
        evseId
      );
      // verify required parameter 'connectorId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'connectorId',
        connectorId
      );
      // verify required parameter 'connectorPatch' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchConnector',
        'connectorPatch',
        connectorPatch
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}/{connector_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)))
          .replace(
            `{${'connector_id'}}`,
            encodeURIComponent(String(connectorId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        connectorPatch,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Patch Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSEPatch} eVSEPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchEvse: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSEPatch: EVSEPatch,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchEvse',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchEvse',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchEvse',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchEvse',
        'evseId',
        evseId
      );
      // verify required parameter 'eVSEPatch' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchEvse',
        'eVSEPatch',
        eVSEPatch
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        eVSEPatch,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Patch Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {LocationPatch} locationPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchLocation: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      locationPatch: LocationPatch,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchLocation',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchLocation',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchLocation',
        'locationId',
        locationId
      );
      // verify required parameter 'locationPatch' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPatchLocation',
        'locationPatch',
        locationPatch
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(
            `{${'location_id'}}`,
            encodeURIComponent(String(locationId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        locationPatch,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Put Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {Connector} connector
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutConnector: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connector: Connector,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'evseId',
        evseId
      );
      // verify required parameter 'connectorId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'connectorId',
        connectorId
      );
      // verify required parameter 'connector' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutConnector',
        'connector',
        connector
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}/{connector_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)))
          .replace(
            `{${'connector_id'}}`,
            encodeURIComponent(String(connectorId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        connector,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Put Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSE} eVSE
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutEvse: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSE: EVSE,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutEvse',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutEvse',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutEvse',
        'locationId',
        locationId
      );
      // verify required parameter 'evseId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutEvse',
        'evseId',
        evseId
      );
      // verify required parameter 'eVSE' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutEvse',
        'eVSE',
        eVSE
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}/{evse_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'location_id'}}`, encodeURIComponent(String(locationId)))
          .replace(`{${'evse_id'}}`, encodeURIComponent(String(evseId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        eVSE,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Put Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {Location} location
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutLocation: async (
      countryCode: string,
      partyId: string,
      locationId: string,
      location: Location,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutLocation',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutLocation',
        'partyId',
        partyId
      );
      // verify required parameter 'locationId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutLocation',
        'locationId',
        locationId
      );
      // verify required parameter 'location' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesLocationsViewsPutLocation',
        'location',
        location
      );
      const localVarPath =
        `/ocpi/2.2.1/locations/{country_code}/{party_id}/{location_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(
            `{${'location_id'}}`,
            encodeURIComponent(String(locationId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        location,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * LocationsModuleApi - functional programming interface
 * @export
 */
export const LocationsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    LocationsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsGetConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ConnectorResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsGetConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsGetConnector'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsGetEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<EVSEResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsGetEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsGetEvse'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsGetLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<LocationResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsGetLocation(
          countryCode,
          partyId,
          locationId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsGetLocation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Patch Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {ConnectorPatch} connectorPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPatchConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connectorPatch: ConnectorPatch,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPatchConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          connectorPatch,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPatchConnector'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Patch Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSEPatch} eVSEPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPatchEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSEPatch: EVSEPatch,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPatchEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          eVSEPatch,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPatchEvse'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Patch Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {LocationPatch} locationPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPatchLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      locationPatch: LocationPatch,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPatchLocation(
          countryCode,
          partyId,
          locationId,
          locationPatch,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPatchLocation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Put Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {Connector} connector
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPutConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connector: Connector,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPutConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          connector,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPutConnector'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Put Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSE} eVSE
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPutEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSE: EVSE,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPutEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          eVSE,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPutEvse'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Put Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {Location} location
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesLocationsViewsPutLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      location: Location,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesLocationsViewsPutLocation(
          countryCode,
          partyId,
          locationId,
          location,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LocationsModuleApi.srcOcpi22InterfacesLocationsViewsPutLocation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * LocationsModuleApi - factory interface
 * @export
 */
export const LocationsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = LocationsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Get Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ConnectorResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsGetConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<EVSEResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsGetEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsGetLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<LocationResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsGetLocation(
          countryCode,
          partyId,
          locationId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Patch Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {ConnectorPatch} connectorPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connectorPatch: ConnectorPatch,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPatchConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          connectorPatch,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Patch Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSEPatch} eVSEPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSEPatch: EVSEPatch,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPatchEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          eVSEPatch,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Patch Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {LocationPatch} locationPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPatchLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      locationPatch: LocationPatch,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPatchLocation(
          countryCode,
          partyId,
          locationId,
          locationPatch,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Put Connector
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {string} connectorId
     * @param {Connector} connector
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutConnector(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      connectorId: string,
      connector: Connector,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPutConnector(
          countryCode,
          partyId,
          locationId,
          evseId,
          connectorId,
          connector,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Put Evse
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {string} evseId
     * @param {EVSE} eVSE
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutEvse(
      countryCode: string,
      partyId: string,
      locationId: string,
      evseId: string,
      eVSE: EVSE,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPutEvse(
          countryCode,
          partyId,
          locationId,
          evseId,
          eVSE,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Put Location
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} locationId
     * @param {Location} location
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesLocationsViewsPutLocation(
      countryCode: string,
      partyId: string,
      locationId: string,
      location: Location,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesLocationsViewsPutLocation(
          countryCode,
          partyId,
          locationId,
          location,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * LocationsModuleApi - object-oriented interface
 * @export
 * @class LocationsModuleApi
 * @extends {BaseAPI}
 */
export class LocationsModuleApi extends BaseAPI {
  /**
   *
   * @summary Get Connector
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {string} connectorId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsGetConnector(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    connectorId: string,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsGetConnector(
        countryCode,
        partyId,
        locationId,
        evseId,
        connectorId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get Evse
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsGetEvse(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsGetEvse(
        countryCode,
        partyId,
        locationId,
        evseId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get Location
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsGetLocation(
    countryCode: string,
    partyId: string,
    locationId: string,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsGetLocation(
        countryCode,
        partyId,
        locationId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Patch Connector
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {string} connectorId
   * @param {ConnectorPatch} connectorPatch
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPatchConnector(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    connectorId: string,
    connectorPatch: ConnectorPatch,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPatchConnector(
        countryCode,
        partyId,
        locationId,
        evseId,
        connectorId,
        connectorPatch,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Patch Evse
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {EVSEPatch} eVSEPatch
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPatchEvse(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    eVSEPatch: EVSEPatch,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPatchEvse(
        countryCode,
        partyId,
        locationId,
        evseId,
        eVSEPatch,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Patch Location
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {LocationPatch} locationPatch
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPatchLocation(
    countryCode: string,
    partyId: string,
    locationId: string,
    locationPatch: LocationPatch,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPatchLocation(
        countryCode,
        partyId,
        locationId,
        locationPatch,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Put Connector
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {string} connectorId
   * @param {Connector} connector
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPutConnector(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    connectorId: string,
    connector: Connector,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPutConnector(
        countryCode,
        partyId,
        locationId,
        evseId,
        connectorId,
        connector,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Put Evse
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {string} evseId
   * @param {EVSE} eVSE
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPutEvse(
    countryCode: string,
    partyId: string,
    locationId: string,
    evseId: string,
    eVSE: EVSE,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPutEvse(
        countryCode,
        partyId,
        locationId,
        evseId,
        eVSE,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Put Location
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} locationId
   * @param {Location} location
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationsModuleApi
   */
  public srcOcpi22InterfacesLocationsViewsPutLocation(
    countryCode: string,
    partyId: string,
    locationId: string,
    location: Location,
    options?: RawAxiosRequestConfig
  ) {
    return LocationsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesLocationsViewsPutLocation(
        countryCode,
        partyId,
        locationId,
        location,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SessionsModuleApi - axios parameter creator
 * @export
 */
export const SessionsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsGetSession: async (
      countryCode: string,
      partyId: string,
      sessionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsGetSession',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsGetSession',
        'partyId',
        partyId
      );
      // verify required parameter 'sessionId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsGetSession',
        'sessionId',
        sessionId
      );
      const localVarPath =
        `/ocpi/2.2.1/sessions/{country_code}/{party_id}/{session_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'session_id'}}`, encodeURIComponent(String(sessionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Patch Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {SessionPatch} sessionPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsPatchSession: async (
      countryCode: string,
      partyId: string,
      sessionId: string,
      sessionPatch: SessionPatch,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPatchSession',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPatchSession',
        'partyId',
        partyId
      );
      // verify required parameter 'sessionId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPatchSession',
        'sessionId',
        sessionId
      );
      // verify required parameter 'sessionPatch' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPatchSession',
        'sessionPatch',
        sessionPatch
      );
      const localVarPath =
        `/ocpi/2.2.1/sessions/{country_code}/{party_id}/{session_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'session_id'}}`, encodeURIComponent(String(sessionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sessionPatch,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Put Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {Session} session
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsPutSession: async (
      countryCode: string,
      partyId: string,
      sessionId: string,
      session: Session,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPutSession',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPutSession',
        'partyId',
        partyId
      );
      // verify required parameter 'sessionId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPutSession',
        'sessionId',
        sessionId
      );
      // verify required parameter 'session' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesSessionsViewsPutSession',
        'session',
        session
      );
      const localVarPath =
        `/ocpi/2.2.1/sessions/{country_code}/{party_id}/{session_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'session_id'}}`, encodeURIComponent(String(sessionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        session,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SessionsModuleApi - functional programming interface
 * @export
 */
export const SessionsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SessionsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesSessionsViewsGetSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SessionResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesSessionsViewsGetSession(
          countryCode,
          partyId,
          sessionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SessionsModuleApi.srcOcpi22InterfacesSessionsViewsGetSession'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Patch Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {SessionPatch} sessionPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesSessionsViewsPatchSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      sessionPatch: SessionPatch,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesSessionsViewsPatchSession(
          countryCode,
          partyId,
          sessionId,
          sessionPatch,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SessionsModuleApi.srcOcpi22InterfacesSessionsViewsPatchSession'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Put Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {Session} session
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesSessionsViewsPutSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      session: Session,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesSessionsViewsPutSession(
          countryCode,
          partyId,
          sessionId,
          session,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SessionsModuleApi.srcOcpi22InterfacesSessionsViewsPutSession'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SessionsModuleApi - factory interface
 * @export
 */
export const SessionsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SessionsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Get Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsGetSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SessionResponse> {
      return localVarFp
        .srcOcpi22InterfacesSessionsViewsGetSession(
          countryCode,
          partyId,
          sessionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Patch Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {SessionPatch} sessionPatch
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsPatchSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      sessionPatch: SessionPatch,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesSessionsViewsPatchSession(
          countryCode,
          partyId,
          sessionId,
          sessionPatch,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Put Session
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} sessionId
     * @param {Session} session
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesSessionsViewsPutSession(
      countryCode: string,
      partyId: string,
      sessionId: string,
      session: Session,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesSessionsViewsPutSession(
          countryCode,
          partyId,
          sessionId,
          session,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SessionsModuleApi - object-oriented interface
 * @export
 * @class SessionsModuleApi
 * @extends {BaseAPI}
 */
export class SessionsModuleApi extends BaseAPI {
  /**
   *
   * @summary Get Session
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} sessionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SessionsModuleApi
   */
  public srcOcpi22InterfacesSessionsViewsGetSession(
    countryCode: string,
    partyId: string,
    sessionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SessionsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesSessionsViewsGetSession(
        countryCode,
        partyId,
        sessionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Patch Session
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} sessionId
   * @param {SessionPatch} sessionPatch
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SessionsModuleApi
   */
  public srcOcpi22InterfacesSessionsViewsPatchSession(
    countryCode: string,
    partyId: string,
    sessionId: string,
    sessionPatch: SessionPatch,
    options?: RawAxiosRequestConfig
  ) {
    return SessionsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesSessionsViewsPatchSession(
        countryCode,
        partyId,
        sessionId,
        sessionPatch,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Put Session
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} sessionId
   * @param {Session} session
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SessionsModuleApi
   */
  public srcOcpi22InterfacesSessionsViewsPutSession(
    countryCode: string,
    partyId: string,
    sessionId: string,
    session: Session,
    options?: RawAxiosRequestConfig
  ) {
    return SessionsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesSessionsViewsPutSession(
        countryCode,
        partyId,
        sessionId,
        session,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * TariffsModuleApi - axios parameter creator
 * @export
 */
export const TariffsModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Get Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTariffsViewsGetTariff: async (
      countryCode: string,
      partyId: string,
      tariffId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsGetTariff',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsGetTariff',
        'partyId',
        partyId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsGetTariff',
        'tariffId',
        tariffId
      );
      const localVarPath =
        `/ocpi/2.2.1/tariffs/{country_code}/{party_id}/{tariff_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'tariff_id'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Put Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {Tariff} tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTariffsViewsPutTariff: async (
      countryCode: string,
      partyId: string,
      tariffId: string,
      tariff: Tariff,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'countryCode' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsPutTariff',
        'countryCode',
        countryCode
      );
      // verify required parameter 'partyId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsPutTariff',
        'partyId',
        partyId
      );
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsPutTariff',
        'tariffId',
        tariffId
      );
      // verify required parameter 'tariff' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTariffsViewsPutTariff',
        'tariff',
        tariff
      );
      const localVarPath =
        `/ocpi/2.2.1/tariffs/{country_code}/{party_id}/{tariff_id}`
          .replace(
            `{${'country_code'}}`,
            encodeURIComponent(String(countryCode))
          )
          .replace(`{${'party_id'}}`, encodeURIComponent(String(partyId)))
          .replace(`{${'tariff_id'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        tariff,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * TariffsModuleApi - functional programming interface
 * @export
 */
export const TariffsModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    TariffsModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Get Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesTariffsViewsGetTariff(
      countryCode: string,
      partyId: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<TariffResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesTariffsViewsGetTariff(
          countryCode,
          partyId,
          tariffId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TariffsModuleApi.srcOcpi22InterfacesTariffsViewsGetTariff'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Put Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {Tariff} tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesTariffsViewsPutTariff(
      countryCode: string,
      partyId: string,
      tariffId: string,
      tariff: Tariff,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<OCPIResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesTariffsViewsPutTariff(
          countryCode,
          partyId,
          tariffId,
          tariff,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TariffsModuleApi.srcOcpi22InterfacesTariffsViewsPutTariff'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TariffsModuleApi - factory interface
 * @export
 */
export const TariffsModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = TariffsModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Get Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTariffsViewsGetTariff(
      countryCode: string,
      partyId: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<TariffResponse> {
      return localVarFp
        .srcOcpi22InterfacesTariffsViewsGetTariff(
          countryCode,
          partyId,
          tariffId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Put Tariff
     * @param {string} countryCode
     * @param {string} partyId
     * @param {string} tariffId
     * @param {Tariff} tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTariffsViewsPutTariff(
      countryCode: string,
      partyId: string,
      tariffId: string,
      tariff: Tariff,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<OCPIResponse> {
      return localVarFp
        .srcOcpi22InterfacesTariffsViewsPutTariff(
          countryCode,
          partyId,
          tariffId,
          tariff,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TariffsModuleApi - object-oriented interface
 * @export
 * @class TariffsModuleApi
 * @extends {BaseAPI}
 */
export class TariffsModuleApi extends BaseAPI {
  /**
   *
   * @summary Get Tariff
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} tariffId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffsModuleApi
   */
  public srcOcpi22InterfacesTariffsViewsGetTariff(
    countryCode: string,
    partyId: string,
    tariffId: string,
    options?: RawAxiosRequestConfig
  ) {
    return TariffsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesTariffsViewsGetTariff(
        countryCode,
        partyId,
        tariffId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Put Tariff
   * @param {string} countryCode
   * @param {string} partyId
   * @param {string} tariffId
   * @param {Tariff} tariff
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TariffsModuleApi
   */
  public srcOcpi22InterfacesTariffsViewsPutTariff(
    countryCode: string,
    partyId: string,
    tariffId: string,
    tariff: Tariff,
    options?: RawAxiosRequestConfig
  ) {
    return TariffsModuleApiFp(this.configuration)
      .srcOcpi22InterfacesTariffsViewsPutTariff(
        countryCode,
        partyId,
        tariffId,
        tariff,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * TokensModuleApi - axios parameter creator
 * @export
 */
export const TokensModuleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary Post Token
     * @param {string} tokenUid
     * @param {RealtimeAuthBody} realtimeAuthBody
     * @param {SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum} [type]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTokensViewsPostToken: async (
      tokenUid: string,
      realtimeAuthBody: RealtimeAuthBody,
      type?: SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'tokenUid' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTokensViewsPostToken',
        'tokenUid',
        tokenUid
      );
      // verify required parameter 'realtimeAuthBody' is not null or undefined
      assertParamExists(
        'srcOcpi22InterfacesTokensViewsPostToken',
        'realtimeAuthBody',
        realtimeAuthBody
      );
      const localVarPath = `/ocpi/2.2.1/tokens/{token_uid}/authorize`.replace(
        `{${'token_uid'}}`,
        encodeURIComponent(String(tokenUid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication OCPIAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'Authorization',
        configuration
      );

      if (type !== undefined) {
        localVarQueryParameter['type'] = type;
      }

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        realtimeAuthBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * TokensModuleApi - functional programming interface
 * @export
 */
export const TokensModuleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    TokensModuleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Post Token
     * @param {string} tokenUid
     * @param {RealtimeAuthBody} realtimeAuthBody
     * @param {SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum} [type]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async srcOcpi22InterfacesTokensViewsPostToken(
      tokenUid: string,
      realtimeAuthBody: RealtimeAuthBody,
      type?: SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.srcOcpi22InterfacesTokensViewsPostToken(
          tokenUid,
          realtimeAuthBody,
          type,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TokensModuleApi.srcOcpi22InterfacesTokensViewsPostToken'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TokensModuleApi - factory interface
 * @export
 */
export const TokensModuleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = TokensModuleApiFp(configuration);
  return {
    /**
     *
     * @summary Post Token
     * @param {string} tokenUid
     * @param {RealtimeAuthBody} realtimeAuthBody
     * @param {SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum} [type]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    srcOcpi22InterfacesTokensViewsPostToken(
      tokenUid: string,
      realtimeAuthBody: RealtimeAuthBody,
      type?: SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AuthResponse> {
      return localVarFp
        .srcOcpi22InterfacesTokensViewsPostToken(
          tokenUid,
          realtimeAuthBody,
          type,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TokensModuleApi - object-oriented interface
 * @export
 * @class TokensModuleApi
 * @extends {BaseAPI}
 */
export class TokensModuleApi extends BaseAPI {
  /**
   *
   * @summary Post Token
   * @param {string} tokenUid
   * @param {RealtimeAuthBody} realtimeAuthBody
   * @param {SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum} [type]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TokensModuleApi
   */
  public srcOcpi22InterfacesTokensViewsPostToken(
    tokenUid: string,
    realtimeAuthBody: RealtimeAuthBody,
    type?: SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum,
    options?: RawAxiosRequestConfig
  ) {
    return TokensModuleApiFp(this.configuration)
      .srcOcpi22InterfacesTokensViewsPostToken(
        tokenUid,
        realtimeAuthBody,
        type,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum = {
  AdHocUser: 'AD_HOC_USER',
  AppUser: 'APP_USER',
  Other: 'OTHER',
  Rfid: 'RFID',
} as const;
export type SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum =
  (typeof SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum)[keyof typeof SrcOcpi22InterfacesTokensViewsPostTokenTypeEnum];
