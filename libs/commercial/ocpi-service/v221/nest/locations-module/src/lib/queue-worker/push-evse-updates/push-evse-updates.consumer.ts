import { AsyncLocalStorage } from 'async_hooks';
import { CredentialService } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import {
  CredentialsStore,
  EvseNotFoundException,
} from '@experience/commercial/ocpi-service/v221/shared';
import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { LocationService } from '../../location.service';
import { Message } from '@aws-sdk/client-sqs';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';

export abstract class PushEvseUpdatesQueueConsumerService extends QueueConsumerService {}

@Injectable()
export class PushEvseUpdatesConsumer implements OnApplicationBootstrap {
  private readonly logger = new Logger(PushEvseUpdatesConsumer.name);

  constructor(
    private readonly als: AsyncLocalStorage<CredentialsStore>,
    private readonly credentialService: CredentialService,
    private readonly queueConsumerService: PushEvseUpdatesQueueConsumerService,
    private readonly locationService: LocationService
  ) {}

  async handle(message: Message): Promise<void> {
    this.logger.log({ message }, 'received push evse update message');
    const { evseId } = JSON.parse(message.Body as string);
    const location = await this.locationService
      .findLocationByEvseId(evseId)
      .catch((error) => {
        if (error instanceof EvseNotFoundException) {
          return null;
        }
        throw error;
      });

    if (!location) {
      this.logger.log({ evseId }, 'location not found');
      return;
    }

    const { evses, id: locationId } = location;

    const evse = evses?.find((evse) => evse.evse_id === evseId);

    if (!evse) {
      this.logger.log({ evseId }, 'evse not found');
      return;
    }

    const stores = await this.credentialService.createStoresForPushEvses();

    for (const store of stores) {
      await this.als.run(store, async () => {
        await this.locationService.pushEvse(locationId, evse, ['status']);
      });
    }
  }

  async onApplicationBootstrap() {
    this.queueConsumerService.setMessageHandler(this.handle.bind(this));
    await this.queueConsumerService.start();
  }
}
