/* tslint:disable */
/* eslint-disable */
/**
 * Rewards API
 * Rewards API service
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AccountDetailsHttpDto
 */
export interface AccountDetailsHttpDto {
  /**
   * The id of the account
   * @type {string}
   * @memberof AccountDetailsHttpDto
   */
  id: string;
  /**
   * The type of the account
   * @type {string}
   * @memberof AccountDetailsHttpDto
   */
  type: AccountDetailsHttpDtoTypeEnum;
  /**
   * The balance of the account
   * @type {BalanceHttpDto}
   * @memberof AccountDetailsHttpDto
   */
  balance: BalanceHttpDto;
  /**
   * The transactions associated with the account
   * @type {Array<AccountTransactionDTO>}
   * @memberof AccountDetailsHttpDto
   */
  transactions: Array<AccountTransactionDTO>;
}

export const AccountDetailsHttpDtoTypeEnum = {
  Allowance: 'ALLOWANCE',
  Rewards: 'REWARDS',
  SystemMiles: 'SYSTEM_MILES',
} as const;

export type AccountDetailsHttpDtoTypeEnum =
  (typeof AccountDetailsHttpDtoTypeEnum)[keyof typeof AccountDetailsHttpDtoTypeEnum];

/**
 *
 * @export
 * @interface AccountTransactionDTO
 */
export interface AccountTransactionDTO {
  /**
   * A hash representing the transaction
   * @type {string}
   * @memberof AccountTransactionDTO
   */
  id: string;
  /**
   * When the transaction took place
   * @type {string}
   * @memberof AccountTransactionDTO
   */
  date: string;
  /**
   * The status of the transaction
   * @type {string}
   * @memberof AccountTransactionDTO
   */
  status: AccountTransactionDTOStatusEnum;
  /**
   * A reference for the transaction
   * @type {object}
   * @memberof AccountTransactionDTO
   */
  reference: object | null;
  /**
   * The currency of the transaction
   * @type {string}
   * @memberof AccountTransactionDTO
   */
  currency: AccountTransactionDTOCurrencyEnum;
  /**
   * The value of the transaction
   * @type {number}
   * @memberof AccountTransactionDTO
   */
  amount: number;
}

export const AccountTransactionDTOStatusEnum = {
  New: 'NEW',
  Pending: 'PENDING',
  Completed: 'COMPLETED',
} as const;

export type AccountTransactionDTOStatusEnum =
  (typeof AccountTransactionDTOStatusEnum)[keyof typeof AccountTransactionDTOStatusEnum];
export const AccountTransactionDTOCurrencyEnum = {
  Miles: 'MILES',
} as const;

export type AccountTransactionDTOCurrencyEnum =
  (typeof AccountTransactionDTOCurrencyEnum)[keyof typeof AccountTransactionDTOCurrencyEnum];

/**
 *
 * @export
 * @interface BalanceHttpDto
 */
export interface BalanceHttpDto {
  /**
   * The amount of the balance
   * @type {number}
   * @memberof BalanceHttpDto
   */
  amount: number;
  /**
   * The currency of the balance
   * @type {string}
   * @memberof BalanceHttpDto
   */
  currency: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface UpsertWalletDTO
 */
export interface UpsertWalletDTO {
  /**
   * The type of wallet to create
   * @type {string}
   * @memberof UpsertWalletDTO
   */
  type: UpsertWalletDTOTypeEnum;
  /**
   * The ID of the subscription the wallet relates to
   * @type {string}
   * @memberof UpsertWalletDTO
   */
  subscriptionId: string;
}

export const UpsertWalletDTOTypeEnum = {
  PodDrive: 'POD_DRIVE',
  System: 'SYSTEM',
} as const;

export type UpsertWalletDTOTypeEnum =
  (typeof UpsertWalletDTOTypeEnum)[keyof typeof UpsertWalletDTOTypeEnum];

/**
 *
 * @export
 * @interface WalletActionAccrualDTO
 */
export interface WalletActionAccrualDTO {
  /**
   *
   * @type {string}
   * @memberof WalletActionAccrualDTO
   */
  action: WalletActionAccrualDTOActionEnum;
  /**
   *
   * @type {WalletActionAmountDTO}
   * @memberof WalletActionAccrualDTO
   */
  amount: WalletActionAmountDTO;
}

export const WalletActionAccrualDTOActionEnum = {
  RewardAccrual: 'REWARD_ACCRUAL',
} as const;

export type WalletActionAccrualDTOActionEnum =
  (typeof WalletActionAccrualDTOActionEnum)[keyof typeof WalletActionAccrualDTOActionEnum];

/**
 *
 * @export
 * @interface WalletActionAmountDTO
 */
export interface WalletActionAmountDTO {
  /**
   *
   * @type {number}
   * @memberof WalletActionAmountDTO
   */
  amount: number;
  /**
   *
   * @type {string}
   * @memberof WalletActionAmountDTO
   */
  currency: WalletActionAmountDTOCurrencyEnum;
}

export const WalletActionAmountDTOCurrencyEnum = {
  Miles: 'MILES',
} as const;

export type WalletActionAmountDTOCurrencyEnum =
  (typeof WalletActionAmountDTOCurrencyEnum)[keyof typeof WalletActionAmountDTOCurrencyEnum];

/**
 *
 * @export
 * @interface WalletActionWithdrawalDTO
 */
export interface WalletActionWithdrawalDTO {
  /**
   *
   * @type {string}
   * @memberof WalletActionWithdrawalDTO
   */
  action: WalletActionWithdrawalDTOActionEnum;
}

export const WalletActionWithdrawalDTOActionEnum = {
  RewardWithdrawal: 'REWARD_WITHDRAWAL',
} as const;

export type WalletActionWithdrawalDTOActionEnum =
  (typeof WalletActionWithdrawalDTOActionEnum)[keyof typeof WalletActionWithdrawalDTOActionEnum];

/**
 *
 * @export
 * @interface WalletBalanceSummaryAllowanceHttpDTO
 */
export interface WalletBalanceSummaryAllowanceHttpDTO {
  /**
   * The miles remaining in the user\'s allowance
   * @type {number}
   * @memberof WalletBalanceSummaryAllowanceHttpDTO
   */
  balanceMiles: number;
  /**
   * The total miles a user can claim within a year
   * @type {number}
   * @memberof WalletBalanceSummaryAllowanceHttpDTO
   */
  annualAllowanceMiles: number;
}
/**
 *
 * @export
 * @interface WalletBalanceSummaryHttpDTO
 */
export interface WalletBalanceSummaryHttpDTO {
  /**
   * The user\'s reward wallet allowance
   * @type {WalletBalanceSummaryAllowanceHttpDTO}
   * @memberof WalletBalanceSummaryHttpDTO
   */
  allowance: WalletBalanceSummaryAllowanceHttpDTO;
  /**
   * The rewards a user has
   * @type {WalletBalanceSummaryRewardsHttpDTO}
   * @memberof WalletBalanceSummaryHttpDTO
   */
  rewards: WalletBalanceSummaryRewardsHttpDTO;
}
/**
 *
 * @export
 * @interface WalletBalanceSummaryRewardsHttpDTO
 */
export interface WalletBalanceSummaryRewardsHttpDTO {
  /**
   * How many miles can be withdrawn
   * @type {number}
   * @memberof WalletBalanceSummaryRewardsHttpDTO
   */
  balanceMiles: number;
  /**
   * The cash value of the withdrawable balance
   * @type {number}
   * @memberof WalletBalanceSummaryRewardsHttpDTO
   */
  balanceGbp: number;
}
/**
 * @type WalletControllerHandleActionRequest
 * @export
 */
export type WalletControllerHandleActionRequest =
  | WalletActionAccrualDTO
  | WalletActionWithdrawalDTO;

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get Rewards API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get Rewards API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary get Rewards API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary get Rewards API health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RewardWalletsApi - axios parameter creator
 * @export
 */
export const RewardWalletsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerGetWalletBalanceSummary: async (
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists(
        'walletControllerGetWalletBalanceSummary',
        'userId',
        userId
      );
      const localVarPath = `/reward-wallets/{userId}/balance`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Based on the request payload, perform an action on the given wallet
     * @summary Perform Wallet Action
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {WalletControllerHandleActionRequest} walletControllerHandleActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerHandleAction: async (
      userId: string,
      walletControllerHandleActionRequest: WalletControllerHandleActionRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('walletControllerHandleAction', 'userId', userId);
      // verify required parameter 'walletControllerHandleActionRequest' is not null or undefined
      assertParamExists(
        'walletControllerHandleAction',
        'walletControllerHandleActionRequest',
        walletControllerHandleActionRequest
      );
      const localVarPath = `/reward-wallets/{userId}/actions`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        walletControllerHandleActionRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerRead: async (
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('walletControllerRead', 'userId', userId);
      const localVarPath = `/reward-wallets/{userId}`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given user, create or update a wallet
     * @summary Upsert Wallet
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {UpsertWalletDTO} upsertWalletDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerUpsertWallet: async (
      userId: string,
      upsertWalletDTO: UpsertWalletDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('walletControllerUpsertWallet', 'userId', userId);
      // verify required parameter 'upsertWalletDTO' is not null or undefined
      assertParamExists(
        'walletControllerUpsertWallet',
        'upsertWalletDTO',
        upsertWalletDTO
      );
      const localVarPath = `/reward-wallets/{userId}`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        upsertWalletDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RewardWalletsApi - functional programming interface
 * @export
 */
export const RewardWalletsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    RewardWalletsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async walletControllerGetWalletBalanceSummary(
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<WalletBalanceSummaryHttpDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.walletControllerGetWalletBalanceSummary(
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'RewardWalletsApi.walletControllerGetWalletBalanceSummary'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Based on the request payload, perform an action on the given wallet
     * @summary Perform Wallet Action
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {WalletControllerHandleActionRequest} walletControllerHandleActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async walletControllerHandleAction(
      userId: string,
      walletControllerHandleActionRequest: WalletControllerHandleActionRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AccountTransactionDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.walletControllerHandleAction(
          userId,
          walletControllerHandleActionRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardWalletsApi.walletControllerHandleAction']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async walletControllerRead(
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.walletControllerRead(userId, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardWalletsApi.walletControllerRead']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given user, create or update a wallet
     * @summary Upsert Wallet
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {UpsertWalletDTO} upsertWalletDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async walletControllerUpsertWallet(
      userId: string,
      upsertWalletDTO: UpsertWalletDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.walletControllerUpsertWallet(
          userId,
          upsertWalletDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardWalletsApi.walletControllerUpsertWallet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RewardWalletsApi - factory interface
 * @export
 */
export const RewardWalletsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RewardWalletsApiFp(configuration);
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerGetWalletBalanceSummary(
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<WalletBalanceSummaryHttpDTO> {
      return localVarFp
        .walletControllerGetWalletBalanceSummary(userId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Based on the request payload, perform an action on the given wallet
     * @summary Perform Wallet Action
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {WalletControllerHandleActionRequest} walletControllerHandleActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerHandleAction(
      userId: string,
      walletControllerHandleActionRequest: WalletControllerHandleActionRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AccountTransactionDTO> {
      return localVarFp
        .walletControllerHandleAction(
          userId,
          walletControllerHandleActionRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerRead(
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<object> {
      return localVarFp
        .walletControllerRead(userId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given user, create or update a wallet
     * @summary Upsert Wallet
     * @param {string} userId The ID of the user the wallet belongs to
     * @param {UpsertWalletDTO} upsertWalletDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    walletControllerUpsertWallet(
      userId: string,
      upsertWalletDTO: UpsertWalletDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .walletControllerUpsertWallet(userId, upsertWalletDTO, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RewardWalletsApi - object-oriented interface
 * @export
 * @class RewardWalletsApi
 * @extends {BaseAPI}
 */
export class RewardWalletsApi extends BaseAPI {
  /**
   *
   * @param {string} userId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardWalletsApi
   */
  public walletControllerGetWalletBalanceSummary(
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return RewardWalletsApiFp(this.configuration)
      .walletControllerGetWalletBalanceSummary(userId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Based on the request payload, perform an action on the given wallet
   * @summary Perform Wallet Action
   * @param {string} userId The ID of the user the wallet belongs to
   * @param {WalletControllerHandleActionRequest} walletControllerHandleActionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardWalletsApi
   */
  public walletControllerHandleAction(
    userId: string,
    walletControllerHandleActionRequest: WalletControllerHandleActionRequest,
    options?: RawAxiosRequestConfig
  ) {
    return RewardWalletsApiFp(this.configuration)
      .walletControllerHandleAction(
        userId,
        walletControllerHandleActionRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} userId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardWalletsApi
   */
  public walletControllerRead(userId: string, options?: RawAxiosRequestConfig) {
    return RewardWalletsApiFp(this.configuration)
      .walletControllerRead(userId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given user, create or update a wallet
   * @summary Upsert Wallet
   * @param {string} userId The ID of the user the wallet belongs to
   * @param {UpsertWalletDTO} upsertWalletDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardWalletsApi
   */
  public walletControllerUpsertWallet(
    userId: string,
    upsertWalletDTO: UpsertWalletDTO,
    options?: RawAxiosRequestConfig
  ) {
    return RewardWalletsApiFp(this.configuration)
      .walletControllerUpsertWallet(userId, upsertWalletDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RewardsApi - axios parameter creator
 * @export
 */
export const RewardsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetAllowanceDetails: async (
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists(
        'rewardsControllerGetAllowanceDetails',
        'userId',
        userId
      );
      const localVarPath = `/reward-wallets/{userId}/allowance`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetRewardsDetails: async (
      userId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'userId' is not null or undefined
      assertParamExists('rewardsControllerGetRewardsDetails', 'userId', userId);
      const localVarPath = `/reward-wallets/{userId}/rewards`.replace(
        `{${'userId'}}`,
        encodeURIComponent(String(userId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RewardsApi - functional programming interface
 * @export
 */
export const RewardsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = RewardsApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerGetAllowanceDetails(
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AccountDetailsHttpDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerGetAllowanceDetails(
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerGetAllowanceDetails']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerGetRewardsDetails(
      userId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AccountDetailsHttpDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerGetRewardsDetails(
          userId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerGetRewardsDetails']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RewardsApi - factory interface
 * @export
 */
export const RewardsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RewardsApiFp(configuration);
  return {
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetAllowanceDetails(
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AccountDetailsHttpDto> {
      return localVarFp
        .rewardsControllerGetAllowanceDetails(userId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} userId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetRewardsDetails(
      userId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AccountDetailsHttpDto> {
      return localVarFp
        .rewardsControllerGetRewardsDetails(userId, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RewardsApi - object-oriented interface
 * @export
 * @class RewardsApi
 * @extends {BaseAPI}
 */
export class RewardsApi extends BaseAPI {
  /**
   *
   * @param {string} userId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerGetAllowanceDetails(
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerGetAllowanceDetails(userId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} userId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerGetRewardsDetails(
    userId: string,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerGetRewardsDetails(userId, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
