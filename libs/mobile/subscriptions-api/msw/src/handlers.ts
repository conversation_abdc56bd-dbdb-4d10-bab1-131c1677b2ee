/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/subscriptions`, async () => {
    const resultArray = [
      [getSubscriptionsControllerSearch200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /subscriptions`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/subscriptions/:subscriptionId`, async () => {
    const resultArray = [
      [
        getSubscriptionsControllerGetBySubscriptionId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/subscriptions/:subscriptionId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /subscriptions/:subscriptionId`) % resultArray.length
      ]
    );
  }),
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/actions/:actionId`,
    async () => {
      const resultArray = [
        [getSubscriptionsControllerGetByActionId200Response(), { status: 200 }],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /subscriptions/:subscriptionId/actions/:actionId`) %
            resultArray.length
        ]
      );
    }
  ),
  http.patch(
    `${baseURL}/subscriptions/:subscriptionId/actions/:actionId`,
    async () => {
      const resultArray = [
        [
          getSubscriptionsControllerUpdateActionById200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`patch /subscriptions/:subscriptionId/actions/:actionId`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/direct-debit`,
    async () => {
      const resultArray = [
        [
          getSubscriptionsControllerGetSubscriptionDirectDebit200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(
        ...resultArray[
          next(`get /subscriptions/:subscriptionId/direct-debit`) %
            resultArray.length
        ]
      );
    }
  ),
  http.get(`${baseURL}/subscriptions/:subscriptionId/documents`, async () => {
    const resultArray = [
      [
        getSubscriptionsControllerGetSubscriptionDocuments200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /subscriptions/:subscriptionId/documents`) %
          resultArray.length
      ]
    );
  }),
];

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getSubscriptionsControllerSearch200Response() {
  return {
    subscriptions: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      id: faker.string.uuid(),
      userId: '5357be96-1495-4951-8046-c2d59ba76c33',
      status: faker.helpers.arrayElement([
        'PENDING',
        'ACTIVE',
        'CANCELLED',
        'SUSPENDED',
        'ENDED',
        'REJECTED',
      ]),
      order: {
        id: faker.string.uuid(),
        origin: faker.helpers.arrayElement(['SALESFORCE', 'SALESFORCE_TEST']),
        orderedAt: '2025-01-01T00:00:00.000Z',
        firstName: faker.person.fullName(),
        lastName: faker.person.fullName(),
        email: faker.internet.email(),
        phoneNumber: faker.lorem.words(),
        address: {
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          line3: faker.lorem.words(),
          postcode: faker.lorem.words(),
        },
        mpan: faker.lorem.words(),
        eCommerceId: faker.string.uuid(),
      },
      actions: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) =>
        faker.helpers.arrayElement([
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['COMPLETE_HOME_SURVEY_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {
              surveyUrl: faker.internet.url(),
            },
          },
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['CHECK_AFFORDABILITY_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {
              applicationId: faker.number.int(),
              loanId: faker.number.int(),
            },
          },
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['INSTALL_CHARGING_STATION_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {
              ppid: faker.string.uuid(),
            },
          },
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['SETUP_DIRECT_DEBIT_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {},
          },
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['PAY_UPFRONT_FEE_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {},
          },
          {
            id: faker.string.uuid(),
            type: faker.helpers.arrayElement(['SIGN_DOCUMENTS_V1']),
            subscriptionId: faker.string.uuid(),
            owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
            status: faker.helpers.arrayElement([
              'PENDING',
              'SUCCESS',
              'FAILURE',
            ]),
            dependsOn: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.string.uuid()),
            data: {
              documents: [
                ...new Array(
                  faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
                ).keys(),
              ].map((_) => ({
                code: faker.helpers.arrayElement(['rca', 'ha']),
                signingUrl: faker.internet.url(),
                signed: faker.datatype.boolean(),
              })),
            },
          },
        ])
      ),
      activatedAt: '2025-01-01T00:00:00.000Z',
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:00:00.000Z',
      deletedAt: '2025-01-01T00:00:00.000Z',
      plan: {
        id: faker.string.uuid(),
        type: faker.helpers.arrayElement(['POD_DRIVE']),
        productCode: faker.lorem.words(),
        allowanceMiles: 10000,
        allowancePeriod: faker.helpers.arrayElement(['ANNUAL']),
        upfrontFeePounds: 99,
        monthlyFeePounds: 35,
        contractDurationMonths: 18,
        ratePencePerMile: 2.28,
        rateMilesPerKwh: 3.5,
        milesRenewalDate: faker.date.past(),
      },
    })),
  };
}

export function getSubscriptionsControllerGetBySubscriptionId200Response() {
  return {
    id: faker.string.uuid(),
    userId: '5357be96-1495-4951-8046-c2d59ba76c33',
    status: faker.helpers.arrayElement([
      'PENDING',
      'ACTIVE',
      'CANCELLED',
      'SUSPENDED',
      'ENDED',
      'REJECTED',
    ]),
    order: {
      id: faker.string.uuid(),
      origin: faker.helpers.arrayElement(['SALESFORCE', 'SALESFORCE_TEST']),
      orderedAt: '2025-01-01T00:00:00.000Z',
      firstName: faker.person.fullName(),
      lastName: faker.person.fullName(),
      email: faker.internet.email(),
      phoneNumber: faker.lorem.words(),
      address: {
        line1: faker.lorem.words(),
        line2: faker.lorem.words(),
        line3: faker.lorem.words(),
        postcode: faker.lorem.words(),
      },
      mpan: faker.lorem.words(),
      eCommerceId: faker.string.uuid(),
    },
    actions: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) =>
      faker.helpers.arrayElement([
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['COMPLETE_HOME_SURVEY_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {
            surveyUrl: faker.internet.url(),
          },
        },
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['CHECK_AFFORDABILITY_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {
            applicationId: faker.number.int(),
            loanId: faker.number.int(),
          },
        },
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['INSTALL_CHARGING_STATION_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {
            ppid: faker.string.uuid(),
          },
        },
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['SETUP_DIRECT_DEBIT_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {},
        },
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['PAY_UPFRONT_FEE_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {},
        },
        {
          id: faker.string.uuid(),
          type: faker.helpers.arrayElement(['SIGN_DOCUMENTS_V1']),
          subscriptionId: faker.string.uuid(),
          owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
          status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
          dependsOn: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.string.uuid()),
          data: {
            documents: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => ({
              code: faker.helpers.arrayElement(['rca', 'ha']),
              signingUrl: faker.internet.url(),
              signed: faker.datatype.boolean(),
            })),
          },
        },
      ])
    ),
    activatedAt: '2025-01-01T00:00:00.000Z',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z',
    deletedAt: '2025-01-01T00:00:00.000Z',
    plan: {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['POD_DRIVE']),
      productCode: faker.lorem.words(),
      allowanceMiles: 10000,
      allowancePeriod: faker.helpers.arrayElement(['ANNUAL']),
      upfrontFeePounds: 99,
      monthlyFeePounds: 35,
      contractDurationMonths: 18,
      ratePencePerMile: 2.28,
      rateMilesPerKwh: 3.5,
      milesRenewalDate: faker.date.past(),
    },
  };
}

export function getSubscriptionsControllerGetByActionId200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['SETUP_DIRECT_DEBIT_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {},
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['CHECK_AFFORDABILITY_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        applicationId: faker.number.int(),
        loanId: faker.number.int(),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['INSTALL_CHARGING_STATION_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        ppid: faker.string.uuid(),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['COMPLETE_HOME_SURVEY_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        surveyUrl: faker.internet.url(),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['SIGN_DOCUMENTS_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        documents: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          code: faker.helpers.arrayElement(['rca', 'ha']),
          signingUrl: faker.internet.url(),
          signed: faker.datatype.boolean(),
        })),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['PAY_UPFRONT_FEE_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {},
    },
  ]);
}

export function getSubscriptionsControllerUpdateActionById200Response() {
  return faker.helpers.arrayElement([
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['SETUP_DIRECT_DEBIT_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {},
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['CHECK_AFFORDABILITY_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        applicationId: faker.number.int(),
        loanId: faker.number.int(),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['COMPLETE_HOME_SURVEY_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        surveyUrl: faker.internet.url(),
      },
    },
    {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(['SIGN_DOCUMENTS_V1']),
      subscriptionId: faker.string.uuid(),
      owner: faker.helpers.arrayElement(['USER', 'SYSTEM']),
      status: faker.helpers.arrayElement(['PENDING', 'SUCCESS', 'FAILURE']),
      dependsOn: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.string.uuid()),
      data: {
        documents: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          code: faker.helpers.arrayElement(['rca', 'ha']),
          signingUrl: faker.internet.url(),
          signed: faker.datatype.boolean(),
        })),
      },
    },
  ]);
}

export function getSubscriptionsControllerGetSubscriptionDirectDebit200Response() {
  return {
    accountNumberLastDigits: '1234',
    sortCodeLastDigits: '23',
    nameOnAccount: 'Mr Tom Wallace',
    monthlyPaymentDay: 13,
  };
}

export function getSubscriptionsControllerGetSubscriptionDocuments200Response() {
  return {
    documents: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      issued: '2025-05-28T18:28:21.959Z',
      link: '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
      format: 'PDF',
      active: true,
      type: faker.helpers.arrayElement(['rca', 'ha']),
    })),
  };
}
handlers.push(
  http.get(
    `${baseURL}/subscriptions/:subscriptionId/documents/:documentId`,
    () => {
      const stream = new ReadableStream({
        start: (controller) => controller.close(),
      });

      return new HttpResponse(stream, {
        headers: {
          'Content-Type': 'application/pdf',
        },
      });
    }
  )
);
