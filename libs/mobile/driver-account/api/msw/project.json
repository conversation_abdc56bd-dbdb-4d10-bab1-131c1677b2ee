{"name": "driver-account-api-msw", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/driver-account/api/msw/src", "projectType": "library", "tags": ["mobile"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/libs/mobile/driver-account/api/msw", "main": "libs/mobile/driver-account/api/msw/src/node.ts", "tsConfig": "libs/mobile/driver-account/api/msw/tsconfig.app.json", "webpackConfig": "libs/mobile/driver-account/api/msw/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "driver-account-api-msw:build"}, "configurations": {"development": {"buildTarget": "driver-account-api-msw:build:development"}, "production": {"buildTarget": "driver-account-api-msw:build:production"}}}, "generate-mocks": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Generate mocks using configuration file.", "command": "npx msw-auto-mock libs/mobile/driver-account/api/contract/openapi3.yaml -o libs/mobile/driver-account/api/msw/src -c 200,201,202,204,302", "forwardAllArgs": false}, {"description": "Convert handlers.js to typescript", "command": "mv libs/mobile/driver-account/api/msw/src/handlers.js libs/mobile/driver-account/api/msw/src/handlers.ts", "forwardAllArgs": false}, {"description": "Convert and rename node.js to main.ts", "command": "mv libs/mobile/driver-account/api/msw/src/node.js libs/mobile/driver-account/api/msw/src/node.ts", "forwardAllArgs": false}, {"description": "<PERSON><PERSON> generated mocks.", "command": "npx nx lint driver-account-api-msw --fix", "forwardAllArgs": false}, {"description": "Format generated mocks.", "command": "npx prettier libs/mobile/driver-account/api/msw --write", "forwardAllArgs": false}], "parallel": false}, "outputs": ["{projectRoot}/src/models"]}}}