/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface OAuthClientResponseDto {
  /**
   * The user friendly name of the client
   */
  name: string;
  /**
   * The website URL for the client\'s information about this oauth connection
   */
  websiteUrl: string;
  /**
   * The URL for the client\'s logo image
   */
  imageUrl: string;
}
