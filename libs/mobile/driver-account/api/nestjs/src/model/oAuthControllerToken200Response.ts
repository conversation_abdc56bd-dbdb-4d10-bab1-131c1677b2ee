/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OAuthTokenClientCredentialsResponseDto } from './oAuthTokenClientCredentialsResponseDto';
import { OAuthTokenAuthorizationCodeResponseDto } from './oAuthTokenAuthorizationCodeResponseDto';
import { OAuthTokenRefreshTokenResponseDto } from './oAuthTokenRefreshTokenResponseDto';

/**
 * @type OAuthControllerToken200Response
 * @export
 */
export type OAuthControllerToken200Response =
  | OAuthTokenAuthorizationCodeResponseDto
  | OAuthTokenClientCredentialsResponseDto
  | OAuthTokenRefreshTokenResponseDto;
