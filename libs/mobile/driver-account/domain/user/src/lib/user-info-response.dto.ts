import { ApiProperty } from '@nestjs/swagger';
import { PreferencesDto } from './preferences.dto';
import { UserDto } from './user.dto';

export class UserInfoResponseDto extends UserDto {
  @ApiProperty({
    description: 'auth id',
    type: String,
    example: 'a3450f38-217a-4d0c-8eec-b7d63c6fd2e0',
  })
  uid: string;
  @ApiProperty({ required: false })
  preferences?: PreferencesDto;
}

class BalanceDto {
  @ApiProperty({ type: String, example: 'GBP' })
  currency: string;

  @ApiProperty({ type: Number, example: 500 })
  amount: number;
}

export class RewardsChargerDto {
  @ApiProperty({
    type: String,
    description: 'The PPID of the charger',
    example: 'PSL-12345',
  })
  id: string;

  @ApiProperty({
    type: Number,
    description: 'The amount of rewardable miles for this charger',
    example: 10.5,
  })
  miles: number;
}

export class RewardsDto {
  @ApiProperty({
    type: Number,
    description: 'The total amount of reward miles from individual chargers',
    example: 10.5,
  })
  totalMiles: number;

  @ApiProperty({
    type: Number,
    description: 'The balance in the lowest unit of currency (pence for GBP)',
    example: 5000,
  })
  balance: number;

  @ApiProperty({
    type: String,
    description: 'The currency represented by the balance',
    example: 'GBP',
  })
  currency: string;

  @ApiProperty({
    type: Number,
    description: 'The minimum amount of rewards miles required for payout',
    example: 150,
  })
  payoutThreshold: number;

  @ApiProperty({
    type: [RewardsChargerDto],
    description: 'The chargers for which the user is eligible for the reward',
  })
  chargers: RewardsChargerDto[];
}

export class ExtendedUserInfoResponseDto extends UserInfoResponseDto {
  @ApiProperty()
  balance: BalanceDto;

  @ApiProperty({
    type: RewardsDto,
    nullable: true,
  })
  rewards?: RewardsDto;

  @ApiProperty({
    nullable: true,
    example: 'cus_hfJJeHsFdaV2H3s',
  })
  paymentProcessorId?: string | null;

  @ApiProperty()
  emailVerified: boolean;

  @ApiProperty({
    oneOf: [
      { type: 'null' },
      { type: 'string', example: '1995-07-16T13:46:06.0Z' },
    ],
  })
  lastSignInTimestamp: Date;

  @ApiProperty({ type: String, example: '1995-07-16T13:46:06.0Z' })
  accountCreationTimestamp: Date | null;

  @ApiProperty({ enum: ['active', 'disabled'] })
  status: 'active' | 'disabled';

  @ApiProperty({
    type: String,
    example: '1995-07-16T13:46:06.0Z',
    nullable: true,
  })
  deletedAtTimestamp: Date | null;
}
