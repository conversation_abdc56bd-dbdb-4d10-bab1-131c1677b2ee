import {
  ExtendedUserInfoResponseDto,
  MarketingDto,
} from '@experience/driver-account-api/api-client';
import { RewardsDto } from '../user-info-response.dto';
import { UserChargerDto } from '../user-charger.dto';

export const TEST_MARKETING_CONSENT: MarketingDto = {
  isConsentGiven: 0,
  type: 'string',
  copy: 'string',
  origin: 'string',
};
export const TEST_USER_INFO_RESPONSE: ExtendedUserInfoResponseDto = {
  email: '<EMAIL>',
  first_name: '<PERSON>',
  last_name: 'Bond',
  locale: 'en',
  uid: '123',
  preferences: {
    unitOfDistance: 'mi',
  },
  balance: {
    amount: 1000,
    currency: 'GBP',
  },
  paymentProcessorId: null,
  emailVerified: true,
  deletedAtTimestamp: null,
  lastSignInTimestamp: '2023-08-24T10:14:52.000Z',
  accountCreationTimestamp: '2023-08-24T10:14:52.000Z',
  status: 'active',
  rewards: {
    totalMiles: 0,
    chargers: [],
    balance: 0,
    currency: 'GBP',
    payoutThreshold: 0,
  },
};

export const TEST_REWARD_DATA: RewardsDto = {
  chargers: [
    {
      id: 'PSL-12345',
      miles: 10.5,
    },
  ],
  totalMiles: 10.5,
  balance: 2394,
  currency: 'GBP',
  payoutThreshold: 100,
};

export const TEST_USER_CHARGERS_RESPONSE: UserChargerDto[] = [
  {
    ppid: 'PSL-11111',
    unitId: 1,
    timezone: 'UTC',
    linkedAt: '2024-01-03T:00:00:00.000Z',
  },
  {
    ppid: 'PSL-123456',
    unitId: 2,
    timezone: 'UTC',
    linkedAt: '2024-01-02T:00:00:00.000Z',
  },
  {
    ppid: 'PSL-555555',
    unitId: 3,
    timezone: 'UTC',
    linkedAt: '2024-01-01T:00:00:00.000Z',
  },
];
