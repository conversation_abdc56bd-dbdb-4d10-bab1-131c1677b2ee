import { ApiProperty } from '@nestjs/swagger';
import { IsEmpty } from 'class-validator';

export class UpdateUserSuppressedStatusDto {
  @IsEmpty()
  @ApiProperty({
    type: String,
    example: null,
    nullable: true,
    enum: [null],
  })
  status: null;
}

export class UserSuppressedStatusDto {
  @ApiProperty({
    type: String,
    enum: ['BOUNCE', 'COMPLAINT', 'UNKNOWN'],
    nullable: true,
  })
  status?: 'BOUNCE' | 'COMPLAINT' | 'UNKNOWN';
}
