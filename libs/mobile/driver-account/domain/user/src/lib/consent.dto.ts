import { ApiProperty } from '@nestjs/swagger';

export class MarketingDto {
  @ApiProperty({
    example: 0,
    type: Number,
  })
  isConsentGiven: number;
  @ApiProperty({
    example: 'express',
    type: String,
  })
  type: string;
  @ApiProperty({
    example:
      'I would like to receive updates about Pod Point products and services by email (and know that I can update my preferences from within any of the emails if I change my mind)',
    type: String,
  })
  copy: string;
  @ApiProperty({
    example: 'opencharge-mobile-app',
    type: String,
  })
  origin: string;
}
export class ConsentDto {
  @ApiProperty({
    description: 'Marketing',
  })
  marketing: MarketingDto;
}
