import { ApiProperty } from '@nestjs/swagger';
import { CountryCode } from 'libphonenumber-js';
import { IsDefined, IsEmail } from 'class-validator';

export class RemoveFactorRequest {
  @IsEmail()
  @IsDefined()
  @ApiProperty({
    description: 'The email of the user removing the factor',
    example: '<EMAIL>',
  })
  email: string;

  @IsDefined()
  @ApiProperty({
    description: 'The region of the phone',
    example: 'en',
  })
  countryCode: CountryCode;

  @IsDefined()
  @ApiProperty({
    description: 'The phone number of the user removing the factor',
    example: 1234567890,
  })
  phoneNumber: string;
}
