import { ApiProperty } from '@nestjs/swagger';
import {
  COMMON_CONTINUE_URL_ERROR,
  COMMON_EMAIL_MAX_LENGTH,
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_ORIGIN_SERVICE_URL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/mobile/driver-account/typescript/domain-model-validation';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsUrl,
  MaxLength,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class SendSignInWithEmailRequest {
  @ApiProperty({
    description: 'Email',
    type: String,
  })
  @Transform(({ value }) => value?.trim())
  @IsEmail({}, { message: COMMON_INVALID_EMAIL_ERROR })
  @MaxLength(COMMON_EMAIL_MAX_LENGTH, {
    message: COMMON_EMAIL_MAX_LENGTH_ERROR,
  })
  @IsNotEmpty({ message: COMMON_REQUIRED_ERROR })
  email: string;

  @ApiProperty({
    description: 'Continue url',
    type: String,
  })
  @IsUrl({}, { message: COMMON_CONTINUE_URL_ERROR })
  @IsNotEmpty({ message: COMMON_REQUIRED_ERROR })
  continue_url: string;

  @ApiProperty({
    description:
      'Origin service url, external service to authenticate the user',
    type: String,
  })
  @IsUrl({}, { message: COMMON_ORIGIN_SERVICE_URL_ERROR })
  @IsOptional()
  origin_service_url?: string;
}
