export const COMMON_EMAIL_MAX_LENGTH = 255;
export const COMMON_EMAIL_MAX_LENGTH_ERROR = `Email address cannot be longer than ${COMMON_EMAIL_MAX_LENGTH} characters`;
export const COMMON_INVALID_EMAIL_ERROR = 'Email address must be valid';
export const COMMON_REQUIRED_ERROR = 'This field is required';
export const COMMON_CONTINUE_URL_ERROR = 'Continue Url should be valid';
export const COMMON_ORIGIN_SERVICE_URL_ERROR =
  'Origin Serivce Url should be valid';
