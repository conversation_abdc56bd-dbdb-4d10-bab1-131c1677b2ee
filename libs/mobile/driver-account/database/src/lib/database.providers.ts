import { ConfigService } from '@nestjs/config';
import { DataSource, ReplicationMode } from 'typeorm';
import { DriverAccountDbConfig, DriverAccountDbConfigMap } from './types';
import {
  ENTITY_MANAGER,
  MIGRATIONS_DATA_SOURCE,
  SERVICE_DATASOURCE,
} from './constants';
import { OAuthApplication } from './entities/oauth-application.entity';
import { OAuthScope } from './entities/oauth-scope.entity';
import { PaymentsBankAccounts } from './entities/payments-bank-accounts.entity';
import { PaymentsRecipients } from './entities/payments-recipients.entity';
import { PaymentsTransactions } from './entities/payments-transactions.entity';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { ProfileFcmToken } from './entities/profile-fcm-token.entity';
import { ProfileUser } from './entities/profile-user.entity';
import { RewardsAccount } from './entities/rewards-account.entity';
import { RewardsTransaction } from './entities/rewards-transaction.entity';
import { RewardsWallet } from './entities/rewards-wallet.entity';
import { Signer } from '@aws-sdk/rds-signer';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionAction } from './entities/subscription-action.entity';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { migrations } from './migrations';

const driverAccountDbConfigEnvKey = 'DRIVER_ACCOUNT_DB_CONFIG';
const region = 'eu-west-1';
const entities = [
  OAuthApplication,
  OAuthScope,
  ProfileUser,
  ProfileFcmToken,
  Subscription,
  SubscriptionPlan,
  SubscriptionAction,
  RewardsAccount,
  RewardsWallet,
  RewardsTransaction,
  PaymentsRecipients,
  PaymentsBankAccounts,
  PaymentsTransactions,
];

const getPassword = (config: DriverAccountDbConfig) =>
  typeof config.password === 'string'
    ? config.password
    : async () => {
        const signer = new Signer({
          region,
          hostname: config.host,
          username: config.username,
          port: config.port,
        });

        return signer.getAuthToken();
      };

const getConfig = (configService: ConfigService): DriverAccountDbConfigMap => {
  try {
    const configJson = configService.getOrThrow<string>(
      driverAccountDbConfigEnvKey
    );

    const config = JSON.parse(configJson);

    return {
      read: config['read'],
      write: config['write'],
      migrate: config['migrate'],
    };
  } catch (error) {
    throw new Error(
      `Failed to parse ${driverAccountDbConfigEnvKey} environment variable: ${
        (error as Error).message
      }`
    );
  }
};

const getConnection = (
  configService: ConfigService,
  replicationMode: ReplicationMode = 'slave',
  overrides: Partial<PostgresConnectionOptions> = {}
) => {
  const config = getConfig(configService);

  const options: PostgresConnectionOptions = {
    logging: true,
    replication: {
      defaultMode: replicationMode,
      master: {
        ...config.write,
        password: getPassword(config.write),
      },
      slaves: [
        {
          ...config.read,
          password: getPassword(config.read),
        },
      ],
    },
    type: 'postgres',
    entities,
    namingStrategy: new SnakeNamingStrategy(),
    ...overrides,
  };

  const dataSource = new DataSource(options);

  return dataSource.initialize();
};

const getMigrationConnection = (configService: ConfigService) => {
  const config = getConfig(configService);
  const migrateConfig = config.migrate;

  const options: PostgresConnectionOptions = {
    logging: true,
    ...migrateConfig,
    password: getPassword(migrateConfig),
    type: 'postgres',
    entities,
    namingStrategy: new SnakeNamingStrategy(),
    migrationsTableName: 'driver_account_migration_table',
    migrationsRun: true,
    migrations,
    migrationsTransactionMode: 'all',
  };

  const dataSource = new DataSource(options);

  return dataSource.initialize();
};

export const databaseProvidersForAccessingData = [
  {
    provide: SERVICE_DATASOURCE,
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) =>
      getConnection(configService),
  },
  {
    provide: ENTITY_MANAGER,
    useFactory: (dataSource: DataSource) => dataSource.manager,
    inject: [SERVICE_DATASOURCE],
  },
];

export const databaseProviders = [
  {
    provide: MIGRATIONS_DATA_SOURCE,
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) =>
      getMigrationConnection(configService),
  },
  ...databaseProvidersForAccessingData,
];
