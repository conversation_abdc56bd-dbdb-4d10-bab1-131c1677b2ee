import {
  Column,
  <PERSON>tity,
  JoinTable,
  ManyToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { OAuthScope } from './oauth-scope.entity';

@Entity({ name: 'applications', schema: 'oauth' })
export class OAuthApplication {
  @PrimaryGeneratedColumn('uuid', {
    primaryKeyConstraintName: 'applications_pk',
  })
  id: string;
  @Column()
  name: string;
  @Column()
  imageUrl: string;
  @Column()
  websiteUrl: string;
  @Column()
  grantType: string;
  @Column('varchar', { array: true })
  redirectUris: string[];
  @Column()
  maximumSessionLengthSeconds: number;
  @ManyToMany(() => OAuthScope)
  @JoinTable({
    name: 'applications_scopes',
    joinColumn: { name: 'application_id' },
    inverseJoinColumn: { name: 'scope_id' },
  })
  allowedScopes: OAuthScope[];
  @Column()
  secretSalt: string;
}
