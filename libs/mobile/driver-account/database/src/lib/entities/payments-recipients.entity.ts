import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'recipients', schema: 'payments' })
export class PaymentsRecipients {
  @PrimaryGeneratedColumn('uuid', {
    primaryKeyConstraintName: 'recipients_pk',
  })
  id: string;

  @Column({ name: 'auth_id' })
  authId: string;

  @Column({ name: 'stripe_account_id' })
  stripeAccountId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
