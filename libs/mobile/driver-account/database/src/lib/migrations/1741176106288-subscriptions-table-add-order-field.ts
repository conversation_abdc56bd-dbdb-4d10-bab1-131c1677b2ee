import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class SubscriptionsTableAddOrderField1741176106288
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'subscriptions.subscriptions',
      new TableColumn({
        name: 'order',
        type: 'jsonb',
        isNullable: false,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('subscriptions.subscriptions', 'order');
  }
}
