import { MigrationInterface, QueryRunner } from 'typeorm';

export class FcmTokensIndex1729783199000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE INDEX fcm_tokens_token_idx ON profile.fcm_tokens USING btree (fcm_token);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS fcm_tokens_token_idx;
    `);
  }
}
