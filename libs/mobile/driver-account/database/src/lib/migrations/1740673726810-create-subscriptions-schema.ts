import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionsSchema1740673726810
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createSchema('subscriptions', true);
    await queryRunner.query(`
        GRANT USAGE ON SCHEMA subscriptions TO driver_account_api;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropSchema('subscriptions', true);
    await queryRunner.query(`
        REVOKE USAGE ON SCHEMA subscriptions FROM driver_account_api;
      `);
  }
}
