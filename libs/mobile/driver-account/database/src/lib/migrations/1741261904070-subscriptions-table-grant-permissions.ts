import { MigrationInterface, QueryRunner } from 'typeorm';

export class SubscriptionsTableGrantPermissions1741261904070
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON subscriptions.subscriptions TO driver_account_api;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON subscriptions.subscriptions FROM driver_account_api;
      `);
  }
}
