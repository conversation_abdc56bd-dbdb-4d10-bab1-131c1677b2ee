import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class SubscriptionPlanTable1741268674541 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const plansTable = new Table({
      schema: 'subscriptions',
      name: 'plans',
      columns: [
        {
          name: 'id',
          type: 'uuid',
          default: 'uuid_generate_v4()',
          isPrimary: true,
          primaryKeyConstraintName: 'plans_pk',
        },
        {
          name: 'subscription_id',
          type: 'uuid',
          isNullable: false,
        },
        {
          name: 'type',
          type: 'text',
          isNullable: false,
        },
        {
          name: 'details',
          type: 'jsonb',
          isNullable: false,
        },
        {
          name: 'created_at',
          type: 'timestamptz',
          default: 'clock_timestamp()',
          isNullable: false,
        },
        {
          name: 'updated_at',
          type: 'timestamptz',
          default: 'clock_timestamp()',
          isNullable: false,
        },
        {
          name: 'deleted_at',
          type: 'timestamptz',
          isNullable: true,
        },
      ],
      foreignKeys: [
        {
          name: 'fk_subscriptions_plans',
          columnNames: ['subscription_id'],
          referencedTableName: 'subscriptions',
          referencedColumnNames: ['id'],
          onDelete: 'CASCADE',
        },
      ],
    });

    await queryRunner.createTable(plansTable, true, true, true);

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON subscriptions.plans TO driver_account_api;
        CREATE INDEX plans_subscription_id_idx ON subscriptions.plans USING btree (subscription_id);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('subscriptions.plans', true, true, true);

    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON subscriptions.plans FROM driver_account_api;
      `);

    await queryRunner.dropIndex(
      'subscriptions.plans',
      'plans_subscription_id_idx'
    );
  }
}
