import { MigrationInterface, QueryRunner } from 'typeorm';

export class FcmTokensUpdatedAtIndex1730974401000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE INDEX fcm_tokens_updated_at_idx ON profile.fcm_tokens USING BRIN (fcm_token);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS fcm_tokens_updated_at_idx;
    `);
  }
}
