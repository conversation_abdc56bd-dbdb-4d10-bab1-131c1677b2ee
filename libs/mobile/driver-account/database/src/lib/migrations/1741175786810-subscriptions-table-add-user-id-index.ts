import { MigrationInterface, QueryRunner } from 'typeorm';

export class SubscriptionsTableAddUserIdIndex1741175786810
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX subscriptions_user_id_idx ON subscriptions.subscriptions USING btree (user_id);`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(
      'subscriptions.subscriptions',
      'subscriptions_user_id_idx'
    );
  }
}
