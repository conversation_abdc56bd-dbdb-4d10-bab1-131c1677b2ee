import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const rewardsBalanceTable = new Table({
  database: 'driver_account',
  schema: 'profile',
  name: 'rewards_balance',
  columns: [
    {
      name: 'id',
      type: 'serial4',
      isPrimary: true,
      primaryKeyConstraintName: 'rewards_balance_pk',
    },
    {
      name: 'user_id',
      type: 'int4',
      isNullable: false,
    },
    {
      name: 'ppid',
      type: 'text',
      isNullable: false,
    },
    {
      name: 'miles',
      type: 'int4',
      isNullable: false,
      default: 0,
    },
    {
      name: 'created_at',
      type: 'timestamptz',
      isNullable: false,
    },
    {
      name: 'updated_at',
      type: 'timestamptz',
      isNullable: true,
    },
    {
      name: 'deleted_at',
      type: 'timestamptz',
      isNullable: true,
    },
  ],
  foreignKeys: [
    {
      name: 'fk_users_rewards_balance',
      columnNames: ['user_id'],
      referencedTableName: 'users',
      referencedColumnNames: ['id'],
      // onDelete: 'CASCADE',
    },
  ],
});

export class CreateBalanceTable1734107591636 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(rewardsBalanceTable, true, true, true);

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_balance TO driver_account_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_balance_id_seq TO driver_account_api;
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_balance TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_balance_id_seq TO rewards_api;
      `);

    await queryRunner.query(`
        CREATE INDEX rewards_balance_user_id_idx ON profile.rewards_balance USING btree (user_id);
        CREATE INDEX rewards_balance_ppid_idx ON profile.rewards_balance USING btree (ppid);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_balance FROM driver_account_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_balance_id_seq FROM driver_account_api;
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_balance FROM rewards_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_balance_id_seq FROM rewards_api;
      `);

    await queryRunner.query(`
      DROP INDEX IF EXISTS rewards_balance_user_id_idx;
      DROP INDEX IF EXISTS rewards_balance_ppid_idx;
    `);

    await queryRunner.dropTable(rewardsBalanceTable, true, true, true);
  }
}
