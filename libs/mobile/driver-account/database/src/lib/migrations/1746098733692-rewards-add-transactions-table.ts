import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const SCHEMA = 'rewards';
const USER = 'rewards_api';
const TABLE = 'transactions';

export class RewardsAddTransactionsTable1746098733692
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'reward_transactions_pk',
          },
          {
            name: 'source_account_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'destination_account_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'decimal(19,2)',
            isNullable: false,
          },
          {
            name: 'currency',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'transaction_date',
            type: 'timestamptz',
            isNullable: false,
          },
          {
            name: 'reference',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            name: 'fk_reward_transactions_source_account_id',
            columnNames: ['source_account_id'],
            referencedTableName: 'accounts',
            referencedColumnNames: ['id'],
          },
          {
            name: 'fk_reward_transactions_destination_account_id',
            columnNames: ['destination_account_id'],
            referencedTableName: 'accounts',
            referencedColumnNames: ['id'],
          },
        ],
      })
    );

    await queryRunner.query(`
      GRANT SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} TO ${USER};
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      REVOKE SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} FROM ${USER};
    `);

    await queryRunner.dropTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
      })
    );
  }
}
