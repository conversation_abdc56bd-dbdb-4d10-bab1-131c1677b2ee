import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateServiceUser1721839916826 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          CREATE ROLE driver_account_api WITH LOGIN;
          DO
          $$
            BEGIN
              CREATE ROLE rds_iam;
            EXCEPTION
              WHEN duplicate_object THEN RAISE NOTICE '%, moving to next statement', sqlerrm USING ERRCODE = sqlstate;
            END
          $$;

          GRANT rds_iam TO driver_account_api;
          GRANT USAGE ON SCHEMA oauth TO driver_account_api;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          REVOKE SELECT, INSERT, UPDATE ON driver_account.oauth FROM driver_account_api;
          REVOKE USAGE ON SCHEMA oauth FROM driver_account_api;
          REVOKE rds_iam FROM driver_account_api;
          DROP ROLE IF EXISTS driver_account_api;
       `);
  }
}
