import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const rewardsBillingAccountsTable = new Table({
  database: 'driver_account',
  schema: 'profile',
  name: 'rewards_billing_accounts',
  columns: [
    {
      name: 'id',
      type: 'serial4',
      isPrimary: true,
      primaryKeyConstraintName: 'rewards_billing_accounts_pk',
    },
    {
      name: 'user_id',
      type: 'int4',
      isNullable: false,
    },
    {
      name: 'account_id',
      type: 'text',
      isNullable: false,
    },
    {
      name: 'created_at',
      type: 'timestamptz',
      isNullable: false,
    },
    {
      name: 'updated_at',
      type: 'timestamptz',
      isNullable: true,
    },
    {
      name: 'deleted_at',
      type: 'timestamptz',
      isNullable: true,
    },
  ],
  foreignKeys: [
    {
      name: 'fk_users_rewards_billing_accounts',
      columnNames: ['user_id'],
      referencedTableName: 'users',
      referencedColumnNames: ['id'],
      onDelete: 'CASCADE',
    },
  ],
});

export class RewardsBillingAccountsTable1733241407240
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      rewardsBillingAccountsTable,
      true,
      true,
      true
    );

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_billing_accounts TO driver_account_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_billing_accounts_id_seq TO driver_account_api;
      `);

    await queryRunner.query(`
        CREATE INDEX rewards_billing_accounts_user_id_idx ON profile.rewards_billing_accounts USING btree (user_id);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(rewardsBillingAccountsTable, true, true, true);

    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_billing_accounts FROM driver_account_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_billing_accounts FROM driver_account_api;
      `);

    await queryRunner.query(`
        DROP INDEX IF EXISTS rewards_billing_accounts_user_id_idx;
      `);
  }
}
