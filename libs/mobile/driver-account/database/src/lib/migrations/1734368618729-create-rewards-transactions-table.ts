import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const rewardsTransactionsTable = new Table({
  database: 'driver_account',
  schema: 'profile',
  name: 'rewards_transactions',
  columns: [
    {
      name: 'id',
      type: 'serial4',
      isPrimary: true,
      primaryKeyConstraintName: 'rewards_transactions_pk',
    },
    {
      name: 'rewards_account_id',
      type: 'int4',
      isNullable: false,
    },
    {
      name: 'miles',
      type: 'int4',
      isNullable: false,
    },
    {
      name: 'status',
      type: 'text',
      isNullable: false,
    },
    {
      name: 'transaction_id',
      type: 'text',
      isNullable: false,
    },
    {
      name: 'created_at',
      type: 'timestamptz',
      isNullable: false,
    },
    {
      name: 'updated_at',
      type: 'timestamptz',
      isNullable: true,
    },
    {
      name: 'deleted_at',
      type: 'timestamptz',
      isNullable: true,
    },
  ],
  foreignKeys: [
    {
      name: 'fk_users_rewards_transactions',
      columnNames: ['rewards_account_id'],
      referencedTableName: 'rewards_billing_accounts',
      referencedColumnNames: ['id'],
      onDelete: 'CASCADE',
    },
  ],
});

export class CreateRewardsTransactionsTable1734368618729
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(rewardsTransactionsTable, true, true, true);

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions TO driver_account_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_transactions_id_seq TO driver_account_api;
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_transactions_id_seq TO rewards_api;
      `);

    await queryRunner.query(`
        CREATE INDEX rewards_balance_rewards_account_id_idx ON profile.rewards_transactions USING btree (rewards_account_id);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(rewardsTransactionsTable, true, true, true);

    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions FROM driver_account_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_transactions_id_seq FROM driver_account_api;
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions FROM rewards_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_transactions_id_seq FROM rewards_api;
      `);

    await queryRunner.query(`
        DROP INDEX IF EXISTS rewards_balance_rewards_account_id_idx;
      `);
  }
}
