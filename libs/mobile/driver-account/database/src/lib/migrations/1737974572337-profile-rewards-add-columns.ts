import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ProfileRewardsAddColumns1737974572337
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'profile.rewards_bank_accounts',
      new TableColumn({
        name: 'uuid',
        type: 'uuid',
        default: 'uuid_generate_v4()',
        isNullable: false,
      })
    );

    await queryRunner.addColumn(
      'profile.rewards_bank_accounts',
      new TableColumn({
        name: 'name',
        type: 'text',
        default: `'bank account'`,
        isNullable: false,
      })
    );

    await queryRunner.addColumn(
      'profile.rewards_bank_accounts',
      new TableColumn({
        name: 'last4',
        type: 'text',
        default: `'****'`,
        isNullable: false,
      })
    );

    await queryRunner.query(`
      ALTER TABLE "profile"."rewards_bank_accounts" ALTER COLUMN "name" DROP DEFAULT;
      ALTER TABLE "profile"."rewards_bank_accounts" ALTER COLUMN "last4" DROP DEFAULT;
      CREATE INDEX rewards_bank_accounts_uuid_idx ON profile.rewards_bank_accounts USING btree (uuid);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('profile.rewards_bank_accounts', 'last4');
    await queryRunner.dropColumn('profile.rewards_bank_accounts', 'name');
    await queryRunner.dropColumn('profile.rewards_bank_accounts', 'uuid');

    await queryRunner.query(`
        DROP INDEX IF EXISTS profile.rewards_bank_accounts_uuid_idx;
      `);
  }
}
