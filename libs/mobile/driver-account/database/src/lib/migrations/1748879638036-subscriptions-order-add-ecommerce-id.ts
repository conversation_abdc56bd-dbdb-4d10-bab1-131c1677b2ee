import { MigrationInterface, QueryRunner } from 'typeorm';

export class SubscriptionsOrderAddEcommerceId1748879638036
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE subscriptions.subscriptions SET "order" = jsonb_set("order", '{eCommerceId}', '"unknown"', true)
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE subscriptions.subscriptions SET "order" = "order" - 'eCommerceId'
      `);
  }
}
