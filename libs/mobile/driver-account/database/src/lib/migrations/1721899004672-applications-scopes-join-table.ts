import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const applications_scopes = new Table({
  database: 'driver_account',
  schema: 'oauth',
  name: 'applications_scopes',
  columns: [
    {
      name: 'application_id',
      type: 'uuid',
    },
    {
      name: 'scope_id',
      type: 'int4',
    },
  ],
  foreignKeys: [
    {
      name: 'applications_scopes_application_id_fk',
      columnNames: ['application_id'],
      referencedColumnNames: ['id'],
      referencedTableName: 'applications',
      onUpdate: 'CASCADE',
    },
    {
      name: 'applications_scopes_scope_id_fk',
      columnNames: ['scope_id'],
      referencedColumnNames: ['id'],
      referencedTableName: 'scopes',
      onUpdate: 'CASCADE',
    },
  ],
  uniques: [{ columnNames: ['application_id', 'scope_id'] }],
});

export class ApplicationsScopesJoinTable1721840437959
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(applications_scopes, true, true, true);
    await queryRunner.query(
      `GRANT SELECT ON "oauth"."applications_scopes" TO driver_account_api;`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(applications_scopes, true, true, true);
    await queryRunner.query(
      `REVOKE SELECT ON "oauth"."applications_scopes" FROM driver_account_api;`
    );
  }
}
