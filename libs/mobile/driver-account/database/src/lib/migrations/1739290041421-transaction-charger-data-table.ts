import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class TransactionChargerDataTable1739290041421
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const rewardsTransactionsChargerDataTable = new Table({
      database: 'driver_account',
      schema: 'profile',
      name: 'rewards_transactions_charger_data',
      columns: [
        {
          name: 'id',
          type: 'serial4',
          isPrimary: true,
          primaryKeyConstraintName: 'rewards_transactions_charger_data_pk',
        },
        {
          name: 'transaction_id',
          type: 'int4',
          isNullable: false,
        },
        {
          name: 'ppid',
          type: 'text',
          isNullable: false,
        },
        {
          name: 'data_platform_response',
          type: 'jsonb',
          isNullable: true,
        },
        {
          name: 'created_at',
          type: 'timestamptz',
          isNullable: false,
        },
      ],
      foreignKeys: [
        {
          name: 'fk_rewards_transactions_rewards_transactions_charger_data',
          columnNames: ['transaction_id'],
          referencedTableName: 'rewards_transactions',
          referencedColumnNames: ['id'],
          onDelete: 'CASCADE',
        },
      ],
    });

    await queryRunner.createTable(
      rewardsTransactionsChargerDataTable,
      true,
      true,
      true
    );

    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions_charger_data TO driver_account_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_transactions_charger_data_id_seq TO driver_account_api;
        GRANT SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions_charger_data TO rewards_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.rewards_transactions_charger_data_id_seq TO rewards_api;
      `);

    await queryRunner.query(`
        CREATE INDEX rewards_transactions_charger_data_transaction_id_idx ON profile.rewards_transactions_charger_data USING btree(transaction_id);
        CREATE INDEX rewards_transactions_charger_data_ppid_idx ON profile.rewards_transactions_charger_data USING btree(ppid);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      'profile.rewards_transactions_charger_data',
      true,
      true,
      true
    );

    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions_charger_data FROM driver_account_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_transactions_charger_data_id_seq FROM driver_account_api;
        REVOKE SELECT, INSERT, UPDATE, DELETE ON profile.rewards_transactions_charger_data FROM rewards_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.rewards_transactions_charger_data_id_seq FROM rewards_api;
      `);

    await queryRunner.query(`
        DROP INDEX IF EXISTS rewards_transactions_charger_data_transaction_id_idx;
        DROP INDEX IF EXISTS rewards_transactions_charger_data_ppid_idx;
      `);
  }
}
