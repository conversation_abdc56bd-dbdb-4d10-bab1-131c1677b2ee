import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const fcmTokensTable = new Table({
  database: 'driver_account',
  schema: 'profile',
  name: 'fcm_tokens',
  columns: [
    {
      name: 'id',
      type: 'serial4',
      isPrimary: true,
      primaryKeyConstraintName: 'fcm_tokens_pk',
    },
    {
      name: 'user_id',
      type: 'int4',
      isNullable: false,
    },
    {
      name: 'fcm_token',
      type: 'text',
      isNullable: false,
    },
    {
      name: 'created_at',
      type: 'timestamptz',
      isNullable: true,
    },
  ],
  foreignKeys: [
    {
      name: 'fk_users_fcm_tokens',
      columnNames: ['user_id'],
      referencedTableName: 'users',
      referencedColumnNames: ['id'],
      onDelete: 'CASCADE',
    },
  ],
});

export class FcmTokensTable1729696870816 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(fcmTokensTable, true, true, true);
    await queryRunner.query(`
        GRANT SELECT, INSERT, UPDATE ON profile.fcm_tokens TO driver_account_api;
        GRANT USAGE, SELECT ON SEQUENCE profile.fcm_tokens_id_seq TO driver_account_api;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(fcmTokensTable, true, true, true);
    await queryRunner.query(`
        REVOKE SELECT, INSERT, UPDATE ON profile.fcm_tokens FROM driver_account_api;
        REVOKE USAGE, SELECT ON SEQUENCE profile.fcm_tokens_id_seq FROM driver_account_api;
      `);
  }
}
