import { AddUuidToBillingAccount************* } from './*************-add-uuid-to-billing-account';
import { CreateRewardsTransactionsTable************* } from './*************-create-rewards-transactions-table';
import { MigrateTransactionStatus************* } from './*************-migrate-transaction-status';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { ProfileRewardsAddColumns************* } from './*************-profile-rewards-add-columns';
import { RewardsBankAccountsTable************* } from './*************-rewards-bank-accounts-table';
import { RewardsBillingAccountsTable************* } from './*************-rewards-billing-accounts-table';
import { RewardsTransactionsUpdates************* } from './*************-rewards-transactions-updates';

export class RemoveOldRewardsTables1747231515885 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS profile.rewards_billing_accounts CASCADE;
      DROP TABLE IF EXISTS profile.rewards_bank_accounts CASCADE;
      DROP TABLE IF EXISTS profile.rewards_transactions CASCADE;
      DROP TABLE IF EXISTS profile.rewards_transactions_charger_data CASCADE;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await new RewardsBillingAccountsTable*************().up(queryRunner);
    await new RewardsBankAccountsTable*************().up(queryRunner);
    await new CreateRewardsTransactionsTable*************().up(queryRunner);
    await new ProfileRewardsAddColumns*************().up(queryRunner);
    await new RewardsTransactionsUpdates*************().up(queryRunner);
    await new AddUuidToBillingAccount*************().up(queryRunner);
    await new MigrateTransactionStatus*************().up(queryRunner);
  }
}
