import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateTransactionStatus1740148914523
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE profile.rewards_transactions SET status = 'POSTED' WHERE status = 'COMPLETE'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE profile.rewards_transactions SET status = 'COMPLETE' WHERE status = 'POSTED'`
    );
  }
}
