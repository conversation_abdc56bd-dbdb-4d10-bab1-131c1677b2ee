{"name": "driver-account-database", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/driver-account/database/src", "projectType": "library", "implicitDependencies": [], "tags": ["mobile"], "targets": {"migration:create": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}/src/lib/migrations", "commands": ["npx typeorm migration:create {args.name}"]}}}}