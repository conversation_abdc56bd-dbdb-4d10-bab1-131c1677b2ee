'use client';

import * as Sentry from '@sentry/nextjs';
import { Heading, Paragraph } from '@experience/shared/react/design-system';
import { useEffect } from 'react';

// eslint-disable-next-line prefer-arrow-functions/prefer-arrow-functions
export function GenericErrorPage(
  errorCode: string,
  header: string,
  body: string
) {
  useEffect(() => {
    // Log the error to Sentry
    Sentry.captureException(errorCode);
  }, [errorCode]);

  return (
    <div className="grid place-items-center">
      <main className="sm:flex">
        <Heading.H1 className="font-bold text-primary">
          {errorCode}
        </Heading.H1>
        <div className="sm:ml-6">
          <div className="sm:border-l sm:border-neutral sm:pl-6">
            <Heading.H2 className="font-bold">{header}</Heading.H2>{' '}
            <Paragraph>{body}</Paragraph>
          </div>
        </div>
      </main>
    </div>
  );
}
