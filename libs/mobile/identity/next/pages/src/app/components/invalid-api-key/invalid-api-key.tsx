'use client';

import {
  CrossCircleIcon,
  Heading,
  HeadingSizes,
  Paragraph,
} from '@experience/shared/react/design-system';
import { useTranslations } from 'next-intl';

const InvalidApiKey = () => {
  const t = useTranslations('invalid_api_key');
  return (
    <>
      <div className="flex justify-center">
        <CrossCircleIcon.LIGHT className="w-20 h-20 text-error" />
      </div>
      <Heading.H1 fontSize={HeadingSizes.M} className="font-bold">
        {t('header')}
      </Heading.H1>
      <Paragraph>{t('body')}</Paragraph>
    </>
  );
};

export default InvalidApiKey;
