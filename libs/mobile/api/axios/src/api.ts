/* tslint:disable */
/* eslint-disable */
/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AccountTopUpRequestDTO
 */
export interface AccountTopUpRequestDTO {
  /**
   *
   * @type {number}
   * @memberof AccountTopUpRequestDTO
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof AccountTopUpRequestDTO
   */
  currency: string;
  /**
   *
   * @type {number}
   * @memberof AccountTopUpRequestDTO
   */
  amount: number;
  /**
   *
   * @type {string}
   * @memberof AccountTopUpRequestDTO
   */
  card_id?: string;
  /**
   *
   * @type {string}
   * @memberof AccountTopUpRequestDTO
   */
  token?: string;
}
/**
 *
 * @export
 * @interface Address
 */
export interface Address {
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Id: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  DomesticId: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Language: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  LanguageAlternatives: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Department: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Company: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  SubBuilding: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  BuildingNumber: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  BuildingName: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  SecondaryStreet: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Street: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Block: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Neighbourhood: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  District: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  City: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Line1: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Line2: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Line3: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Line4: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Line5: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  AdminAreaName: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  AdminAreaCode: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Province: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  ProvinceName: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  ProvinceCode: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  PostalCode: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  CountryName: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  CountryIso2: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  CountryIso3: string;
  /**
   * ISO numeric code for the country.
   * @type {string}
   * @memberof Address
   */
  CountryIsoNumber: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  SortingNumber1: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  SortingNumber2: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Barcode: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  POBoxNumber: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Label: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Type: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  DataLevel: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field1: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field2: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field3: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field4: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field5: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field6: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field7: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field8: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field9: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field10: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field11: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field12: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field13: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field14: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field15: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field16: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field17: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field18: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field19: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  Field20: string;
}
/**
 *
 * @export
 * @interface AffordabilityActionDTO
 */
export interface AffordabilityActionDTO {
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDTO
   */
  type: AffordabilityActionDTOTypeEnum;
  /**
   *
   * @type {AffordabilityActionDataDTO}
   * @memberof AffordabilityActionDTO
   */
  data: AffordabilityActionDataDTO;
}

export const AffordabilityActionDTOTypeEnum = {
  CheckAffordabilityV1: 'CHECK_AFFORDABILITY_V1',
} as const;

export type AffordabilityActionDTOTypeEnum =
  (typeof AffordabilityActionDTOTypeEnum)[keyof typeof AffordabilityActionDTOTypeEnum];

/**
 *
 * @export
 * @interface AffordabilityActionDataBillingAddressDTO
 */
export interface AffordabilityActionDataBillingAddressDTO {
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataBillingAddressDTO
   */
  line1: string;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataBillingAddressDTO
   */
  line2?: string;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataBillingAddressDTO
   */
  line3?: string;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataBillingAddressDTO
   */
  postcode: string;
}
/**
 *
 * @export
 * @interface AffordabilityActionDataDTO
 */
export interface AffordabilityActionDataDTO {
  /**
   * Title of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  title: AffordabilityActionDataDTOTitleEnum;
  /**
   * First name of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  firstName: string;
  /**
   * Last name of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  lastName: string;
  /**
   * Date of birth of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  dateOfBirth: string;
  /**
   * Email address of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  email: string;
  /**
   * Telephone number of the customer
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  phoneNumber: string;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  maritalStatus: AffordabilityActionDataDTOMaritalStatusEnum;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  residentialStatus: AffordabilityActionDataDTOResidentialStatusEnum;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  dependencies: AffordabilityActionDataDTODependenciesEnum;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  employmentStatus: AffordabilityActionDataDTOEmploymentStatusEnum;
  /**
   *
   * @type {number}
   * @memberof AffordabilityActionDataDTO
   */
  monthlyTakeHomePay: number;
  /**
   *
   * @type {number}
   * @memberof AffordabilityActionDataDTO
   */
  monthlyHousePayments: number;
  /**
   *
   * @type {number}
   * @memberof AffordabilityActionDataDTO
   */
  monthlyTravelAndLivingExpenses: number;
  /**
   *
   * @type {number}
   * @memberof AffordabilityActionDataDTO
   */
  monthlyHouseholdExpenses: number;
  /**
   *
   * @type {number}
   * @memberof AffordabilityActionDataDTO
   */
  monthlyCreditPayments: number;
  /**
   *
   * @type {boolean}
   * @memberof AffordabilityActionDataDTO
   */
  noExpectedChangesInAbilityToPay: boolean;
  /**
   *
   * @type {string}
   * @memberof AffordabilityActionDataDTO
   */
  vulnerabilities: string;
  /**
   *
   * @type {AffordabilityActionDataBillingAddressDTO}
   * @memberof AffordabilityActionDataDTO
   */
  billingAddress: AffordabilityActionDataBillingAddressDTO;
}

export const AffordabilityActionDataDTOTitleEnum = {
  Mr: 'MR',
  Mrs: 'MRS',
  Miss: 'MISS',
  Ms: 'MS',
  Mx: 'MX',
  Dr: 'DR',
  Other: 'OTHER',
} as const;

export type AffordabilityActionDataDTOTitleEnum =
  (typeof AffordabilityActionDataDTOTitleEnum)[keyof typeof AffordabilityActionDataDTOTitleEnum];
export const AffordabilityActionDataDTOMaritalStatusEnum = {
  Single: 'SINGLE',
  LivingTogether: 'LIVING_TOGETHER',
  CommonLaw: 'COMMON_LAW',
  Married: 'MARRIED',
  Seperated: 'SEPERATED',
  Divorced: 'DIVORCED',
  Widowed: 'WIDOWED',
  Other: 'OTHER',
} as const;

export type AffordabilityActionDataDTOMaritalStatusEnum =
  (typeof AffordabilityActionDataDTOMaritalStatusEnum)[keyof typeof AffordabilityActionDataDTOMaritalStatusEnum];
export const AffordabilityActionDataDTOResidentialStatusEnum = {
  LivingWithParents: 'LIVING_WITH_PARENTS',
  CouncilTenant: 'COUNCIL_TENANT',
  HomeownerWithMortgage: 'HOMEOWNER_WITH_MORTGAGE',
  HomeownerWithoutMortgage: 'HOMEOWNER_WITHOUT_MORTGAGE',
  PrivateTenant: 'PRIVATE_TENANT',
  HousingAssociation: 'HOUSING_ASSOCIATION',
} as const;

export type AffordabilityActionDataDTOResidentialStatusEnum =
  (typeof AffordabilityActionDataDTOResidentialStatusEnum)[keyof typeof AffordabilityActionDataDTOResidentialStatusEnum];
export const AffordabilityActionDataDTODependenciesEnum = {
  _0: '0',
  _1: '1',
  _2: '2',
  _3: '3+',
} as const;

export type AffordabilityActionDataDTODependenciesEnum =
  (typeof AffordabilityActionDataDTODependenciesEnum)[keyof typeof AffordabilityActionDataDTODependenciesEnum];
export const AffordabilityActionDataDTOEmploymentStatusEnum = {
  SelfEmployed: 'SELF_EMPLOYED',
  PartTime: 'PART_TIME',
  FullTime: 'FULL_TIME',
  Contract: 'CONTRACT',
  Retired: 'RETIRED',
  ArmedForced: 'ARMED_FORCED',
  HomeMaker: 'HOME_MAKER',
  SingleParent: 'SINGLE_PARENT',
  Disabled: 'DISABLED',
} as const;

export type AffordabilityActionDataDTOEmploymentStatusEnum =
  (typeof AffordabilityActionDataDTOEmploymentStatusEnum)[keyof typeof AffordabilityActionDataDTOEmploymentStatusEnum];

/**
 *
 * @export
 * @interface BalanceDto
 */
export interface BalanceDto {
  /**
   *
   * @type {string}
   * @memberof BalanceDto
   */
  currency: string;
  /**
   *
   * @type {number}
   * @memberof BalanceDto
   */
  amount: number;
}
/**
 *
 * @export
 * @interface ChargeOverrideRequestDTO
 */
export interface ChargeOverrideRequestDTO {
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideRequestDTO
   */
  endAt: string;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideRequestDTO
   */
  requestedAt: string;
}
/**
 *
 * @export
 * @interface ChargeOverrideScheduleChargingStation
 */
export interface ChargeOverrideScheduleChargingStation {
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleChargingStation
   */
  addressId?: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleChargingStation
   */
  id?: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleChargingStation
   */
  mpan?: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleChargingStation
   */
  ppid: string | null;
}
/**
 *
 * @export
 * @interface ChargeOverrideScheduleEves
 */
export interface ChargeOverrideScheduleEves {
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleEves
   */
  door: string;
  /**
   *
   * @type {number}
   * @memberof ChargeOverrideScheduleEves
   */
  ocppEvseId: number;
}
/**
 *
 * @export
 * @interface ChargeOverrideScheduleResponse
 */
export interface ChargeOverrideScheduleResponse {
  /**
   *
   * @type {ChargeOverrideScheduleChargingStation}
   * @memberof ChargeOverrideScheduleResponse
   */
  chargingStation: ChargeOverrideScheduleChargingStation;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleResponse
   */
  deletedAt: string | null;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleResponse
   */
  endAt: string | null;
  /**
   *
   * @type {ChargeOverrideScheduleEves}
   * @memberof ChargeOverrideScheduleResponse
   */
  evse: ChargeOverrideScheduleEves;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleResponse
   */
  receivedAt: string;
  /**
   *
   * @type {string}
   * @memberof ChargeOverrideScheduleResponse
   */
  requestedAt: string;
}
/**
 *
 * @export
 * @interface ChargeRequestDTO
 */
export interface ChargeRequestDTO {
  /**
   *
   * @type {number}
   * @memberof ChargeRequestDTO
   */
  user: number;
  /**
   *
   * @type {number}
   * @memberof ChargeRequestDTO
   */
  pod: number;
  /**
   *
   * @type {number}
   * @memberof ChargeRequestDTO
   */
  door: number;
  /**
   *
   * @type {number}
   * @memberof ChargeRequestDTO
   */
  claimed_by?: number;
}
/**
 *
 * @export
 * @interface ChargeRequestResponse
 */
export interface ChargeRequestResponse {
  /**
   *
   * @type {number}
   * @memberof ChargeRequestResponse
   */
  id: number;
}
/**
 *
 * @export
 * @interface ChargeStatsResponse
 */
export interface ChargeStatsResponse {
  /**
   *
   * @type {object}
   * @memberof ChargeStatsResponse
   */
  data: object;
  /**
   *
   * @type {object}
   * @memberof ChargeStatsResponse
   */
  meta: object;
}
/**
 *
 * @export
 * @interface ChargerConnectivityStatus
 */
export interface ChargerConnectivityStatus {
  /**
   *
   * @type {string}
   * @memberof ChargerConnectivityStatus
   */
  ppid: string;
  /**
   *
   * @type {Array<string>}
   * @memberof ChargerConnectivityStatus
   */
  connectedComponents: Array<string>;
  /**
   *
   * @type {object}
   * @memberof ChargerConnectivityStatus
   */
  chargingStation?: object;
  /**
   *
   * @type {Array<EvseComponent>}
   * @memberof ChargerConnectivityStatus
   */
  evses?: Array<EvseComponent>;
}
/**
 *
 * @export
 * @interface ChargerDelegatedControlResponse
 */
export interface ChargerDelegatedControlResponse {
  /**
   * The delegated control status of this charger
   * @type {string}
   * @memberof ChargerDelegatedControlResponse
   */
  status: ChargerDelegatedControlResponseStatusEnum;
}

export const ChargerDelegatedControlResponseStatusEnum = {
  Unknown: 'UNKNOWN',
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  Pending: 'PENDING',
} as const;

export type ChargerDelegatedControlResponseStatusEnum =
  (typeof ChargerDelegatedControlResponseStatusEnum)[keyof typeof ChargerDelegatedControlResponseStatusEnum];

/**
 *
 * @export
 * @interface ChargerFlexEnrolment
 */
export interface ChargerFlexEnrolment {
  /**
   *
   * @type {string}
   * @memberof ChargerFlexEnrolment
   */
  ppid: string;
  /**
   *
   * @type {EnrolmentStatus}
   * @memberof ChargerFlexEnrolment
   */
  enrolmentStatus: EnrolmentStatus;
}
/**
 *
 * @export
 * @interface ChargerModelInfoResponse
 */
export interface ChargerModelInfoResponse {
  /**
   * The LED colour set used on the charger
   * @type {string}
   * @memberof ChargerModelInfoResponse
   */
  ledColourSet: ChargerModelInfoResponseLedColourSetEnum;
  /**
   * The style of charger
   * @type {string}
   * @memberof ChargerModelInfoResponse
   */
  style: ChargerModelInfoResponseStyleEnum;
  /**
   * The colour of the charger
   * @type {string}
   * @memberof ChargerModelInfoResponse
   */
  colour: ChargerModelInfoResponseColourEnum;
  /**
   * The architecture of the charger
   * @type {string}
   * @memberof ChargerModelInfoResponse
   */
  architecture: string;
}

export const ChargerModelInfoResponseLedColourSetEnum = {
  Uk: 'uk',
  Eu: 'eu',
} as const;

export type ChargerModelInfoResponseLedColourSetEnum =
  (typeof ChargerModelInfoResponseLedColourSetEnum)[keyof typeof ChargerModelInfoResponseLedColourSetEnum];
export const ChargerModelInfoResponseStyleEnum = {
  Solo: 'solo',
  Solo3: 'solo3',
  Solo3s: 'solo3s',
} as const;

export type ChargerModelInfoResponseStyleEnum =
  (typeof ChargerModelInfoResponseStyleEnum)[keyof typeof ChargerModelInfoResponseStyleEnum];
export const ChargerModelInfoResponseColourEnum = {
  White: 'white',
  Black: 'black',
} as const;

export type ChargerModelInfoResponseColourEnum =
  (typeof ChargerModelInfoResponseColourEnum)[keyof typeof ChargerModelInfoResponseColourEnum];

/**
 *
 * @export
 * @interface ChargerResponse
 */
export interface ChargerResponse {
  /**
   * The PPID of the charger
   * @type {string}
   * @memberof ChargerResponse
   */
  ppid: string;
  /**
   * The unit id of the charger
   * @type {number}
   * @memberof ChargerResponse
   */
  unitId: number;
  /**
   * The timezone of the charger
   * @type {string}
   * @memberof ChargerResponse
   */
  timezone: string;
  /**
   * When the charger was linked to the user
   * @type {string}
   * @memberof ChargerResponse
   */
  linkedAt: string;
  /**
   * The delegated control information for the charger
   * @type {ChargerDelegatedControlResponse}
   * @memberof ChargerResponse
   */
  delegatedControl: ChargerDelegatedControlResponse;
  /**
   * The model information for the charger
   * @type {ChargerModelInfoResponse}
   * @memberof ChargerResponse
   */
  modelInfo: ChargerModelInfoResponse;
}
/**
 *
 * @export
 * @interface ChargerTariffDto
 */
export interface ChargerTariffDto {
  /**
   * The ID of the tariff
   * @type {string}
   * @memberof ChargerTariffDto
   */
  id: string;
  /**
   * The date from which the tariff is effective (inclusive)
   * @type {string}
   * @memberof ChargerTariffDto
   */
  effectiveFrom: string;
  /**
   * The maximum price during off-peak hours in pence
   * @type {number}
   * @memberof ChargerTariffDto
   */
  maxChargePrice?: number | null;
  /**
   * The PPID of the charger related to the tariff
   * @type {string}
   * @memberof ChargerTariffDto
   */
  ppid: string;
  /**
   * Reference to the supplier. Null if the supplier is unknown
   * @type {string}
   * @memberof ChargerTariffDto
   */
  supplierId: string | null;
  /**
   * Tariff information applicable to specific days and times.
   * @type {Array<TariffInfoDtoImpl>}
   * @memberof ChargerTariffDto
   */
  tariffInfo: Array<TariffInfoDtoImpl>;
  /**
   * Timezone the tariff information applies to
   * @type {string}
   * @memberof ChargerTariffDto
   */
  timezone: string;
  /**
   * The cheapest unit price (e.g., £0.10).
   * @type {number}
   * @memberof ChargerTariffDto
   */
  cheapestUnitPrice: number;
}
/**
 *
 * @export
 * @interface ChargersAndVehicles
 */
export interface ChargersAndVehicles {
  /**
   *
   * @type {string}
   * @memberof ChargersAndVehicles
   */
  ppid: string;
  /**
   *
   * @type {Array<ExtendedVehicleLinksResponseDtoImpl>}
   * @memberof ChargersAndVehicles
   */
  vehicles?: Array<ExtendedVehicleLinksResponseDtoImpl>;
}
/**
 *
 * @export
 * @interface ChargesResponse
 */
export interface ChargesResponse {
  /**
   *
   * @type {object}
   * @memberof ChargesResponse
   */
  data: object;
  /**
   *
   * @type {object}
   * @memberof ChargesResponse
   */
  meta: object;
}
/**
 *
 * @export
 * @interface ChargingStationTariffSearchCriteriaDtoImpl
 */
export interface ChargingStationTariffSearchCriteriaDtoImpl {
  /**
   * Charging Station PPID
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDtoImpl
   */
  ppid: string;
  /**
   * The effective from date
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDtoImpl
   */
  effectiveFrom?: string;
  /**
   * The effective to date
   * @type {string}
   * @memberof ChargingStationTariffSearchCriteriaDtoImpl
   */
  effectiveTo?: string;
}
/**
 *
 * @export
 * @interface ChargingStationTariffSearchDto
 */
export interface ChargingStationTariffSearchDto {
  /**
   *
   * @type {Array<ChargerTariffDto>}
   * @memberof ChargingStationTariffSearchDto
   */
  data: Array<ChargerTariffDto>;
  /**
   *
   * @type {ChargingStationTariffSearchMetadataDtoImpl}
   * @memberof ChargingStationTariffSearchDto
   */
  metadata: ChargingStationTariffSearchMetadataDtoImpl;
}
/**
 *
 * @export
 * @interface ChargingStationTariffSearchMetadataDtoImpl
 */
export interface ChargingStationTariffSearchMetadataDtoImpl {
  /**
   *
   * @type {ChargingStationTariffSearchCriteriaDtoImpl}
   * @memberof ChargingStationTariffSearchMetadataDtoImpl
   */
  criteria: ChargingStationTariffSearchCriteriaDtoImpl;
}
/**
 *
 * @export
 * @interface CheckForUpgrade
 */
export interface CheckForUpgrade {
  /**
   * Whether a version has been found or not
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  found: boolean;
  /**
   * Whether the app should force upgrade
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  forceUpgrade: boolean;
  /**
   * The message to display to the user
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  message: boolean;
  /**
   *
   * @type {QueryCheckForUpgrade}
   * @memberof CheckForUpgrade
   */
  query: QueryCheckForUpgrade;
}
/**
 *
 * @export
 * @interface ConfirmationOfPayeeDTO
 */
export interface ConfirmationOfPayeeDTO {
  /**
   * The status of the confirmation of payee check
   * @type {string}
   * @memberof ConfirmationOfPayeeDTO
   */
  status: ConfirmationOfPayeeDTOStatusEnum;
  /**
   * The name provided
   * @type {string}
   * @memberof ConfirmationOfPayeeDTO
   */
  provided: string;
  /**
   * The suggested name associated with the bank account if status is PARTIAL
   * @type {string}
   * @memberof ConfirmationOfPayeeDTO
   */
  suggested: string | null;
}

export const ConfirmationOfPayeeDTOStatusEnum = {
  Match: 'MATCH',
  Mismatch: 'MISMATCH',
  PartialMatch: 'PARTIAL_MATCH',
  Unavailable: 'UNAVAILABLE',
} as const;

export type ConfirmationOfPayeeDTOStatusEnum =
  (typeof ConfirmationOfPayeeDTOStatusEnum)[keyof typeof ConfirmationOfPayeeDTOStatusEnum];

/**
 *
 * @export
 * @interface ConnectedChargeStateImpl
 */
export interface ConnectedChargeStateImpl {
  /**
   * Battery capacity
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  batteryCapacity: number;
  /**
   * Battery level percent
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  batteryLevelPercent: number | null;
  /**
   * Charge limit percent
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  chargeLimitPercent: number | null;
  /**
   * Charge rate
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  chargeRate: number | null;
  /**
   * Charge time remaining
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  chargeTimeRemaining: number | null;
  /**
   * Is charging?
   * @type {boolean}
   * @memberof ConnectedChargeStateImpl
   */
  isCharging: boolean | null;
  /**
   * Is fully charged?
   * @type {boolean}
   * @memberof ConnectedChargeStateImpl
   */
  isFullyCharged: boolean | null;
  /**
   * Is plugged in?
   * @type {boolean}
   * @memberof ConnectedChargeStateImpl
   */
  isPluggedIn: boolean | null;
  /**
   * Last updated
   * @type {string}
   * @memberof ConnectedChargeStateImpl
   */
  lastUpdated: string | null;
  /**
   * Max current
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  maxCurrent: number | null;
  /**
   *
   * @type {string}
   * @memberof ConnectedChargeStateImpl
   */
  powerDeliveryState: ConnectedChargeStateImplPowerDeliveryStateEnum | null;
  /**
   * Range
   * @type {number}
   * @memberof ConnectedChargeStateImpl
   */
  range: number | null;
}

export const ConnectedChargeStateImplPowerDeliveryStateEnum = {
  Unplugged: 'UNPLUGGED',
  PluggedInNoPower: 'PLUGGED_IN:NO_POWER',
  PluggedInStopped: 'PLUGGED_IN:STOPPED',
  PluggedInComplete: 'PLUGGED_IN:COMPLETE',
  PluggedInCharging: 'PLUGGED_IN:CHARGING',
  Unknown: 'UNKNOWN',
  PluggedInInitializing: 'PLUGGED_IN:INITIALIZING',
  PluggedInFault: 'PLUGGED_IN:FAULT',
} as const;

export type ConnectedChargeStateImplPowerDeliveryStateEnum =
  (typeof ConnectedChargeStateImplPowerDeliveryStateEnum)[keyof typeof ConnectedChargeStateImplPowerDeliveryStateEnum];

/**
 *
 * @export
 * @interface ConnectedStatefulVehicleDtoImpl
 */
export interface ConnectedStatefulVehicleDtoImpl {
  /**
   * Vehicle Id
   * @type {string}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  id: string;
  /**
   * Last seen date
   * @type {string}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  lastSeen?: string | null;
  /**
   * Enode user id
   * @type {string}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  enodeUserId?: string | null;
  /**
   * Enode vehicle id
   * @type {string}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  enodeVehicleId?: string | null;
  /**
   * Vehicle data
   * @type {VehicleInformationImpl}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  vehicleInformation: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   * @type {ConnectedChargeStateImpl}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  chargeState: ConnectedChargeStateImpl;
  /**
   * Vehicle interventions
   * @type {InterventionDtoImpl}
   * @memberof ConnectedStatefulVehicleDtoImpl
   */
  interventions: InterventionDtoImpl;
}
/**
 *
 * @export
 * @interface ConsentDto
 */
export interface ConsentDto {
  /**
   * Marketing
   * @type {MarketingDto}
   * @memberof ConsentDto
   */
  marketing: MarketingDto;
}
/**
 *
 * @export
 * @interface CreateIntentResponse
 */
export interface CreateIntentResponse {
  /**
   * The customer id
   * @type {string}
   * @memberof CreateIntentResponse
   */
  customer: string;
  /**
   * The setup intent secret
   * @type {string}
   * @memberof CreateIntentResponse
   */
  setupIntent: string | null;
  /**
   * The ephemeral key
   * @type {string}
   * @memberof CreateIntentResponse
   */
  ephemeralKey: string;
}
/**
 *
 * @export
 * @interface CreatePaymentRequest
 */
export interface CreatePaymentRequest {
  /**
   * The amount to top up
   * @type {number}
   * @memberof CreatePaymentRequest
   */
  amount: number;
  /**
   * Currency of the amount that is to topup
   * @type {string}
   * @memberof CreatePaymentRequest
   */
  currency: string;
}
/**
 *
 * @export
 * @interface CreateRegisteredUserPaymentResponse
 */
export interface CreateRegisteredUserPaymentResponse {
  /**
   * The payment intent secret
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  paymentIntent: string | null;
  /**
   * The customer id
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  customer: string;
  /**
   * The ephemeral key
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  ephemeralKey: string;
}
/**
 *
 * @export
 * @interface CreateUserPayload
 */
export interface CreateUserPayload {
  /**
   * First name
   * @type {string}
   * @memberof CreateUserPayload
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof CreateUserPayload
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof CreateUserPayload
   */
  locale: string;
  /**
   * Email
   * @type {string}
   * @memberof CreateUserPayload
   */
  email: string;
  /**
   * password
   * @type {string}
   * @memberof CreateUserPayload
   */
  password?: string;
  /**
   *
   * @type {ConsentDto}
   * @memberof CreateUserPayload
   */
  consent?: ConsentDto;
  /**
   *
   * @type {PreferencesDto}
   * @memberof CreateUserPayload
   */
  preferences?: PreferencesDto;
}
/**
 *
 * @export
 * @interface CreateUserResponseDto
 */
export interface CreateUserResponseDto {
  /**
   * GIP userId
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  auth_id: string;
  /**
   *
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  created_at: string;
  /**
   * pk of user
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  id: string;
  /**
   * password reset link
   * @type {string}
   * @memberof CreateUserResponseDto
   */
  password_reset_link?: string;
}
/**
 *
 * @export
 * @interface CurrentIntentDtoChargingStationImpl
 */
export interface CurrentIntentDtoChargingStationImpl {
  /**
   *
   * @type {string}
   * @memberof CurrentIntentDtoChargingStationImpl
   */
  ppid?: string | null;
}
/**
 *
 * @export
 * @interface CurrentIntentDtoImpl
 */
export interface CurrentIntentDtoImpl {
  /**
   * Whether or not the intent can meet the target
   * @type {boolean}
   * @memberof CurrentIntentDtoImpl
   */
  canMeetTarget: boolean | null;
  /**
   * The charge details
   * @type {VehicleChargeInfoDtoImpl}
   * @memberof CurrentIntentDtoImpl
   */
  chargeDetail: VehicleChargeInfoDtoImpl;
  /**
   * The charging station
   * @type {CurrentIntentDtoChargingStationImpl}
   * @memberof CurrentIntentDtoImpl
   */
  chargingStation: CurrentIntentDtoChargingStationImpl;
  /**
   * The reason why the target can not be met
   * @type {string}
   * @memberof CurrentIntentDtoImpl
   */
  cannotMeetTargetReason: CurrentIntentDtoImplCannotMeetTargetReasonEnum | null;
}

export const CurrentIntentDtoImplCannotMeetTargetReasonEnum = {
  Price: 'PRICE',
  Time: 'TIME',
} as const;

export type CurrentIntentDtoImplCannotMeetTargetReasonEnum =
  (typeof CurrentIntentDtoImplCannotMeetTargetReasonEnum)[keyof typeof CurrentIntentDtoImplCannotMeetTargetReasonEnum];

/**
 *
 * @export
 * @interface DelegatedControlChargingStationResponseDtoImpl
 */
export interface DelegatedControlChargingStationResponseDtoImpl {
  /**
   * Id
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDtoImpl
   */
  id: string;
  /**
   * Pod Point id
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDtoImpl
   */
  ppid: string;
  /**
   *
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDtoImpl
   */
  status?: DelegatedControlChargingStationResponseDtoImplStatusEnum;
  /**
   * Created at
   * @type {string}
   * @memberof DelegatedControlChargingStationResponseDtoImpl
   */
  createdAt: string;
  /**
   * Vehicle links
   * @type {Array<VehicleLinkRequestDtoImpl>}
   * @memberof DelegatedControlChargingStationResponseDtoImpl
   */
  vehicleLinks?: Array<VehicleLinkRequestDtoImpl>;
}

export const DelegatedControlChargingStationResponseDtoImplStatusEnum = {
  Unknown: 'UNKNOWN',
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  Pending: 'PENDING',
} as const;

export type DelegatedControlChargingStationResponseDtoImplStatusEnum =
  (typeof DelegatedControlChargingStationResponseDtoImplStatusEnum)[keyof typeof DelegatedControlChargingStationResponseDtoImplStatusEnum];

/**
 *
 * @export
 * @interface DnoRegion
 */
export interface DnoRegion {
  /**
   * The DNO region\'s Id.
   * @type {number}
   * @memberof DnoRegion
   */
  regionid: number;
  /**
   * The DNO region.
   * @type {string}
   * @memberof DnoRegion
   */
  dnoregion: string;
  /**
   * The DNO region\'s shortname.
   * @type {string}
   * @memberof DnoRegion
   */
  shortname: string;
}
/**
 *
 * @export
 * @interface DnoRegions
 */
export interface DnoRegions {
  /**
   * The list of available DNO regions
   * @type {Array<DnoRegion>}
   * @memberof DnoRegions
   */
  data: Array<DnoRegion>;
}
/**
 *
 * @export
 * @interface EnrolmentStatus
 */
export interface EnrolmentStatus {
  /**
   *
   * @type {boolean}
   * @memberof EnrolmentStatus
   */
  isEnrolled: boolean;
  /**
   *
   * @type {string}
   * @memberof EnrolmentStatus
   */
  optOutUrl: string;
}
/**
 *
 * @export
 * @interface Entry
 */
export interface Entry {
  /**
   * Carbon intensity entries for the specified date
   * @type {string}
   * @memberof Entry
   */
  date: string;
  /**
   * The carbon intensity entries for a half hour period.
   * @type {Array<Period>}
   * @memberof Entry
   */
  entries: Array<Period>;
}
/**
 *
 * @export
 * @interface EvseComponent
 */
export interface EvseComponent {
  /**
   *
   * @type {number}
   * @memberof EvseComponent
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof EvseComponent
   */
  architecture?: string;
  /**
   *
   * @type {object}
   * @memberof EvseComponent
   */
  connectivityState: object;
  /**
   *
   * @type {Array<object>}
   * @memberof EvseComponent
   */
  connectors: Array<object>;
  /**
   *
   * @type {object}
   * @memberof EvseComponent
   */
  energyOfferStatus?: object;
  /**
   *
   * @type {string}
   * @memberof EvseComponent
   */
  serialNumber?: string;
  /**
   *
   * @type {string}
   * @memberof EvseComponent
   */
  macAddress?: string;
}
/**
 *
 * @export
 * @interface ExpensesRequestBody
 */
export interface ExpensesRequestBody {
  /**
   *
   * @type {Array<object>}
   * @memberof ExpensesRequestBody
   */
  expenses: Array<object>;
}
/**
 *
 * @export
 * @interface ExpensesResponse
 */
export interface ExpensesResponse {
  /**
   *
   * @type {Array<object>}
   * @memberof ExpensesResponse
   */
  expenses: Array<object>;
}
/**
 *
 * @export
 * @interface ExtendedUserInfoResponseDto
 */
export interface ExtendedUserInfoResponseDto {
  /**
   * First name
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  locale: string;
  /**
   * Email
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  email: string;
  /**
   * auth id
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  uid: string;
  /**
   *
   * @type {PreferencesDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  preferences?: PreferencesDto;
  /**
   *
   * @type {BalanceDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  balance: BalanceDto;
  /**
   *
   * @type {RewardsDto}
   * @memberof ExtendedUserInfoResponseDto
   */
  rewards: RewardsDto | null;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  paymentProcessorId: string | null;
  /**
   *
   * @type {boolean}
   * @memberof ExtendedUserInfoResponseDto
   */
  emailVerified: boolean;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  lastSignInTimestamp: string | null;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  accountCreationTimestamp: string;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  status: ExtendedUserInfoResponseDtoStatusEnum;
  /**
   *
   * @type {string}
   * @memberof ExtendedUserInfoResponseDto
   */
  deletedAtTimestamp: string | null;
}

export const ExtendedUserInfoResponseDtoStatusEnum = {
  Active: 'active',
  Disabled: 'disabled',
} as const;

export type ExtendedUserInfoResponseDtoStatusEnum =
  (typeof ExtendedUserInfoResponseDtoStatusEnum)[keyof typeof ExtendedUserInfoResponseDtoStatusEnum];

/**
 *
 * @export
 * @interface ExtendedVehicleLinksResponseDtoImpl
 */
export interface ExtendedVehicleLinksResponseDtoImpl {
  /**
   * Id
   * @type {string}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  id: string;
  /**
   * Is this the primary charger for this vehicle
   * @type {boolean}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  isPrimary: boolean;
  /**
   * Is this vehicle plugged into the charger
   * @type {boolean}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  isPluggedInToThisCharger: boolean;
  /**
   *
   * @type {VehicleLinkResponseDtoImplVehicle}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  vehicle: VehicleLinkResponseDtoImplVehicle;
  /**
   * Intent data
   * @type {VehicleIntentsResponseDtoImpl}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  intents: VehicleIntentsResponseDtoImpl;
  /**
   * The current intent for the vehicle
   * @type {CurrentIntentDtoImpl}
   * @memberof ExtendedVehicleLinksResponseDtoImpl
   */
  currentIntent: CurrentIntentDtoImpl | null;
}
/**
 *
 * @export
 * @interface FcmTokenDtoImpl
 */
export interface FcmTokenDtoImpl {
  /**
   * The notification token you want to save
   * @type {string}
   * @memberof FcmTokenDtoImpl
   */
  token: string;
  /**
   * The timestamp at which the notification token was created
   * @type {string}
   * @memberof FcmTokenDtoImpl
   */
  timestamp: string;
}
/**
 *
 * @export
 * @interface FieldHelpDTO
 */
export interface FieldHelpDTO {
  /**
   *
   * @type {string}
   * @memberof FieldHelpDTO
   */
  title?: string;
  /**
   *
   * @type {string}
   * @memberof FieldHelpDTO
   */
  content?: string;
}
/**
 *
 * @export
 * @interface FieldValidatorDTO
 */
export interface FieldValidatorDTO {
  /**
   *
   * @type {string}
   * @memberof FieldValidatorDTO
   */
  type: string;
}
/**
 *
 * @export
 * @interface FieldValueDTO
 */
export interface FieldValueDTO {
  /**
   *
   * @type {string}
   * @memberof FieldValueDTO
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof FieldValueDTO
   */
  label?: string;
  /**
   *
   * @type {string}
   * @memberof FieldValueDTO
   */
  value: string;
}
/**
 *
 * @export
 * @interface FirmwareStatusResponse
 */
export interface FirmwareStatusResponse {
  /**
   *
   * @type {Array<FirmwareStatusResponseData>}
   * @memberof FirmwareStatusResponse
   */
  data: Array<FirmwareStatusResponseData>;
}
/**
 *
 * @export
 * @interface FirmwareStatusResponseData
 */
export interface FirmwareStatusResponseData {
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseData
   */
  serialNumber: string;
  /**
   *
   * @type {FirmwareStatusResponseManifestType}
   * @memberof FirmwareStatusResponseData
   */
  versionInfo: FirmwareStatusResponseManifestType;
  /**
   *
   * @type {FirmwareStatusResponseUpdateStatus}
   * @memberof FirmwareStatusResponseData
   */
  updateStatus: FirmwareStatusResponseUpdateStatus;
}
/**
 *
 * @export
 * @interface FirmwareStatusResponseManifestType
 */
export interface FirmwareStatusResponseManifestType {
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestType
   */
  architecture: FirmwareStatusResponseManifestTypeArchitectureEnum;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestType
   */
  createdDate?: string | null;
  /**
   *
   * @type {FirmwareStatusResponseManifestTypeDetails}
   * @memberof FirmwareStatusResponseManifestType
   */
  details: FirmwareStatusResponseManifestTypeDetails;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestType
   */
  manifestId?: string | null;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestType
   */
  status?: FirmwareStatusResponseManifestTypeStatusEnum;
}

export const FirmwareStatusResponseManifestTypeArchitectureEnum = {
  Arch1: 'arch1',
  Arch2: 'arch2',
  Arch24: 'arch2.4',
  Arch3: 'arch3',
  Arch5: 'arch5',
} as const;

export type FirmwareStatusResponseManifestTypeArchitectureEnum =
  (typeof FirmwareStatusResponseManifestTypeArchitectureEnum)[keyof typeof FirmwareStatusResponseManifestTypeArchitectureEnum];
export const FirmwareStatusResponseManifestTypeStatusEnum = {
  Candidate: 'candidate',
  Alpha: 'alpha',
  Beta: 'beta',
  Release: 'release',
  Archived: 'archived',
} as const;

export type FirmwareStatusResponseManifestTypeStatusEnum =
  (typeof FirmwareStatusResponseManifestTypeStatusEnum)[keyof typeof FirmwareStatusResponseManifestTypeStatusEnum];

/**
 *
 * @export
 * @interface FirmwareStatusResponseManifestTypeDetails
 */
export interface FirmwareStatusResponseManifestTypeDetails {
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestTypeDetails
   */
  rfidVersion?: string;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestTypeDetails
   */
  dspVersion?: string;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseManifestTypeDetails
   */
  wifiVersion?: string;
}
/**
 *
 * @export
 * @interface FirmwareStatusResponseUpdateStatus
 */
export interface FirmwareStatusResponseUpdateStatus {
  /**
   *
   * @type {boolean}
   * @memberof FirmwareStatusResponseUpdateStatus
   */
  isUpdateAvailable: boolean;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseUpdateStatus
   */
  status?: FirmwareStatusResponseUpdateStatusStatusEnum;
  /**
   *
   * @type {string}
   * @memberof FirmwareStatusResponseUpdateStatus
   */
  updateId?: string;
  /**
   *
   * @type {FirmwareStatusResponseManifestType}
   * @memberof FirmwareStatusResponseUpdateStatus
   */
  updateVersion?: FirmwareStatusResponseManifestType;
}

export const FirmwareStatusResponseUpdateStatusStatusEnum = {
  NotRequested: 'NOT_REQUESTED',
  NotAccepted: 'NOT_ACCEPTED',
  InProgress: 'IN_PROGRESS',
  Failed: 'FAILED',
} as const;

export type FirmwareStatusResponseUpdateStatusStatusEnum =
  (typeof FirmwareStatusResponseUpdateStatusStatusEnum)[keyof typeof FirmwareStatusResponseUpdateStatusStatusEnum];

/**
 *
 * @export
 * @interface FlexRequest
 */
export interface FlexRequest {
  /**
   *
   * @type {string}
   * @memberof FlexRequest
   */
  id: string;
  /**
   * When the flex request was created, in ISO 8601 format
   * @type {string}
   * @memberof FlexRequest
   */
  requestedAt: string;
  /**
   * When the flex request is scheduled to start, in ISO 8601 format
   * @type {string}
   * @memberof FlexRequest
   */
  startAt: string;
  /**
   * When the flex request is scheduled to end, in ISO 8601 format
   * @type {string}
   * @memberof FlexRequest
   */
  endAt: string;
  /**
   * The direction of the flexibility request
   * @type {string}
   * @memberof FlexRequest
   */
  direction: FlexRequestDirectionEnum;
  /**
   * The limit of the flexibility request
   * @type {FlexRequestLimit}
   * @memberof FlexRequest
   */
  limit: FlexRequestLimit;
}

export const FlexRequestDirectionEnum = {
  Increase: 'INCREASE',
  Reduce: 'REDUCE',
} as const;

export type FlexRequestDirectionEnum =
  (typeof FlexRequestDirectionEnum)[keyof typeof FlexRequestDirectionEnum];

/**
 *
 * @export
 * @interface FlexRequestLimit
 */
export interface FlexRequestLimit {
  /**
   *
   * @type {object}
   * @memberof FlexRequestLimit
   */
  unit: object;
  /**
   *
   * @type {number}
   * @memberof FlexRequestLimit
   */
  value: number;
}
/**
 *
 * @export
 * @interface Forecast
 */
export interface Forecast {
  /**
   * The carbon intensity forecast data.
   * @type {Forecastdata}
   * @memberof Forecast
   */
  data: Forecastdata;
}
/**
 *
 * @export
 * @interface ForecastSnapshot
 */
export interface ForecastSnapshot {
  /**
   * The DNO region\'s ID.
   * @type {number}
   * @memberof ForecastSnapshot
   */
  regionid: number;
  /**
   * The DNO region.
   * @type {string}
   * @memberof ForecastSnapshot
   */
  dnoregion: string;
  /**
   * The DNO region\'s shortname.
   * @type {string}
   * @memberof ForecastSnapshot
   */
  shortname: string;
  /**
   * Carbon intensity for 30min period.
   * @type {Period}
   * @memberof ForecastSnapshot
   */
  data: Period;
}
/**
 *
 * @export
 * @interface Forecastdata
 */
export interface Forecastdata {
  /**
   * The DNO region\'s ID.
   * @type {number}
   * @memberof Forecastdata
   */
  regionid: number;
  /**
   * The DNO region.
   * @type {string}
   * @memberof Forecastdata
   */
  dnoregion: string;
  /**
   * The DNO region\'s shortname.
   * @type {string}
   * @memberof Forecastdata
   */
  shortname: string;
  /**
   * List of carbon intensity for 30min periods.
   * @type {Array<Entry>}
   * @memberof Forecastdata
   */
  dates: Array<Entry>;
}
/**
 *
 * @export
 * @interface FormDataDTO
 */
export interface FormDataDTO {
  /**
   *
   * @type {string}
   * @memberof FormDataDTO
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof FormDataDTO
   */
  name: string;
  /**
   *
   * @type {Array<FormSectionDTO>}
   * @memberof FormDataDTO
   */
  sections: Array<FormSectionDTO>;
}
/**
 *
 * @export
 * @interface FormFieldDTO
 */
export interface FormFieldDTO {
  /**
   *
   * @type {string}
   * @memberof FormFieldDTO
   */
  type: string;
  /**
   *
   * @type {string}
   * @memberof FormFieldDTO
   */
  label: string;
  /**
   *
   * @type {Array<FieldValidatorDTO>}
   * @memberof FormFieldDTO
   */
  validators?: Array<FieldValidatorDTO>;
  /**
   *
   * @type {string}
   * @memberof FormFieldDTO
   */
  id?: string;
  /**
   *
   * @type {Array<FieldValueDTO>}
   * @memberof FormFieldDTO
   */
  values?: Array<FieldValueDTO>;
  /**
   *
   * @type {FieldHelpDTO}
   * @memberof FormFieldDTO
   */
  help?: FieldHelpDTO;
}
/**
 *
 * @export
 * @interface FormSectionDTO
 */
export interface FormSectionDTO {
  /**
   *
   * @type {string}
   * @memberof FormSectionDTO
   */
  title: string;
  /**
   *
   * @type {Array<FormFieldDTO>}
   * @memberof FormSectionDTO
   */
  fields: Array<FormFieldDTO>;
}
/**
 *
 * @export
 * @interface GenericChargeStateImpl
 */
export interface GenericChargeStateImpl {
  /**
   * Battery capacity
   * @type {number}
   * @memberof GenericChargeStateImpl
   */
  batteryCapacity: number;
}
/**
 *
 * @export
 * @interface GenericStatefulVehicleDtoImpl
 */
export interface GenericStatefulVehicleDtoImpl {
  /**
   * Id
   * @type {string}
   * @memberof GenericStatefulVehicleDtoImpl
   */
  id: string;
  /**
   * Vehicle data
   * @type {VehicleInformationImpl}
   * @memberof GenericStatefulVehicleDtoImpl
   */
  vehicleInformation: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   * @type {GenericChargeStateImpl}
   * @memberof GenericStatefulVehicleDtoImpl
   */
  chargeState: GenericChargeStateImpl;
  /**
   * The user\'s Enode ID
   * @type {string}
   * @memberof GenericStatefulVehicleDtoImpl
   */
  enodeUserId?: string | null;
  /**
   * The vehicles\'s Enode ID
   * @type {string}
   * @memberof GenericStatefulVehicleDtoImpl
   */
  enodeVehicleId?: string | null;
}
/**
 *
 * @export
 * @interface GetLinkSessionRequest
 */
export interface GetLinkSessionRequest {
  /**
   * By specifying a vendor, the brand selection step in Link UI will be skipped. Instead, your user will go directly to the service selection view (if applicable for the specified vendor), or to the review data access step.
   * @type {string}
   * @memberof GetLinkSessionRequest
   */
  vendor?: string;
  /**
   * A unique identifier identifying the user to be used with the link session generated at the capture vehicle stage
   * @type {string}
   * @memberof GetLinkSessionRequest
   */
  enodeUserId?: string;
}
/**
 *
 * @export
 * @interface GetLinkSessionResponse
 */
export interface GetLinkSessionResponse {
  /**
   *
   * @type {string}
   * @memberof GetLinkSessionResponse
   */
  linkToken: string;
  /**
   *
   * @type {string}
   * @memberof GetLinkSessionResponse
   */
  linkUrl: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface Intensity
 */
export interface Intensity {
  /**
   * The forecast Carbon intensity (gCO2/kWh) for the current half hour.
   * @type {number}
   * @memberof Intensity
   */
  forecast: number;
  /**
   * The forecast Carbon intensity (gCO2/kWh) index for the current half hour.
   * @type {string}
   * @memberof Intensity
   */
  index: string;
}
/**
 *
 * @export
 * @interface InterventionDtoImpl
 */
export interface InterventionDtoImpl {
  /**
   * The endpoint to extract all interventions
   * @type {string}
   * @memberof InterventionDtoImpl
   */
  all: string;
  /**
   * The individual interventions for charge state
   * @type {Array<string>}
   * @memberof InterventionDtoImpl
   */
  chargeState: Array<string>;
  /**
   * The individual interventions for vehicle information
   * @type {Array<string>}
   * @memberof InterventionDtoImpl
   */
  information: Array<string>;
}
/**
 *
 * @export
 * @interface Item
 */
export interface Item {
  /**
   * This can be an address Id or a container Id for further results.
   * @type {string}
   * @memberof Item
   */
  Id: string;
  /**
   * Descriptive information about the result.
   * @type {string}
   * @memberof Item
   */
  Description: string;
  /**
   * If the Type is \"Address\" then the Id can be passed to the Retrieve service. Any other Id should be passed as the Container to a further Find request to get more results.
   * @type {string}
   * @memberof Item
   */
  Type: ItemTypeEnum;
  /**
   * The name of the result.
   * @type {string}
   * @memberof Item
   */
  Text: string;
  /**
   * A list of number ranges identifying the matched characters in the Text and Description.
   * @type {string}
   * @memberof Item
   */
  Highlight: string;
}

export const ItemTypeEnum = {
  Address: 'Address',
  Container: 'Container',
  Postcode: 'Postcode',
  Residential: 'Residential',
  Street: 'Street',
} as const;

export type ItemTypeEnum = (typeof ItemTypeEnum)[keyof typeof ItemTypeEnum];

/**
 *
 * @export
 * @interface LinkyDTO
 */
export interface LinkyDTO {
  /**
   * Wether or not the charger is Linky capable
   * @type {boolean}
   * @memberof LinkyDTO
   */
  linkyCapable: boolean | null;
  /**
   * Whether or not Linky is used for scheduling
   * @type {boolean}
   * @memberof LinkyDTO
   */
  scheduleEnabled: boolean | null;
}
/**
 *
 * @export
 * @interface LocaleResponse
 */
export interface LocaleResponse {
  /**
   *
   * @type {Array<object>}
   * @memberof LocaleResponse
   */
  data?: Array<object> | null;
  /**
   *
   * @type {object}
   * @memberof LocaleResponse
   */
  meta: object;
}
/**
 *
 * @export
 * @interface LocationControllerFind400Response
 */
export interface LocationControllerFind400Response {
  /**
   *
   * @type {number}
   * @memberof LocationControllerFind400Response
   */
  statusCode?: number;
  /**
   *
   * @type {string}
   * @memberof LocationControllerFind400Response
   */
  message?: string;
  /**
   *
   * @type {string}
   * @memberof LocationControllerFind400Response
   */
  error?: string;
}
/**
 *
 * @export
 * @interface LocationControllerSearch400Response
 */
export interface LocationControllerSearch400Response {
  /**
   *
   * @type {number}
   * @memberof LocationControllerSearch400Response
   */
  statusCode?: number;
  /**
   *
   * @type {string}
   * @memberof LocationControllerSearch400Response
   */
  message?: string;
  /**
   *
   * @type {string}
   * @memberof LocationControllerSearch400Response
   */
  error?: string;
}
/**
 *
 * @export
 * @interface MarketingDto
 */
export interface MarketingDto {
  /**
   *
   * @type {number}
   * @memberof MarketingDto
   */
  isConsentGiven: number;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  type: string;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  copy: string;
  /**
   *
   * @type {string}
   * @memberof MarketingDto
   */
  origin: string;
}
/**
 *
 * @export
 * @interface MarketingOpportunitiesDTO
 */
export interface MarketingOpportunitiesDTO {
  /**
   * A list of all the marketing opportunities a charger is eligible for
   * @type {Array<string>}
   * @memberof MarketingOpportunitiesDTO
   */
  opportunities: Array<MarketingOpportunitiesDTOOpportunitiesEnum>;
}

export const MarketingOpportunitiesDTOOpportunitiesEnum = {
  Rewards: 'REWARDS',
  Tariff: 'TARIFF',
  Migrate: 'MIGRATE',
} as const;

export type MarketingOpportunitiesDTOOpportunitiesEnum =
  (typeof MarketingOpportunitiesDTOOpportunitiesEnum)[keyof typeof MarketingOpportunitiesDTOOpportunitiesEnum];

/**
 *
 * @export
 * @interface PayoutRequestDTOImpl
 */
export interface PayoutRequestDTOImpl {
  /**
   * The ID of the bank account to make the payout from
   * @type {string}
   * @memberof PayoutRequestDTOImpl
   */
  bankAccountId: string;
}
/**
 *
 * @export
 * @interface PayoutResponseDTOImpl
 */
export interface PayoutResponseDTOImpl {
  /**
   * The balance that was paid out in the lowest unit of the currency
   * @type {number}
   * @memberof PayoutResponseDTOImpl
   */
  value: number;
  /**
   * The currency to payout in
   * @type {string}
   * @memberof PayoutResponseDTOImpl
   */
  currency: PayoutResponseDTOImplCurrencyEnum;
  /**
   * The total number of miles claimed
   * @type {number}
   * @memberof PayoutResponseDTOImpl
   */
  totalMiles: number;
  /**
   * The ID of the transaction
   * @type {string}
   * @memberof PayoutResponseDTOImpl
   */
  transactionId: string;
}

export const PayoutResponseDTOImplCurrencyEnum = {
  Gbp: 'GBP',
} as const;

export type PayoutResponseDTOImplCurrencyEnum =
  (typeof PayoutResponseDTOImplCurrencyEnum)[keyof typeof PayoutResponseDTOImplCurrencyEnum];

/**
 *
 * @export
 * @interface Period
 */
export interface Period {
  /**
   * Start time in the format hh:mm. Time is based on a 24 hour system. Seconds are not given.
   * @type {string}
   * @memberof Period
   */
  from: string;
  /**
   * End time in the format hh:mm. Time is based on a 24 hour system. Seconds are not given.
   * @type {string}
   * @memberof Period
   */
  to: string;
  /**
   * The carbon intensity for current half hour.
   * @type {Intensity}
   * @memberof Period
   */
  intensity: Intensity;
}
/**
 *
 * @export
 * @interface PreferencesDto
 */
export interface PreferencesDto {
  /**
   *
   * @type {string}
   * @memberof PreferencesDto
   */
  unitOfDistance: string;
}
/**
 *
 * @export
 * @interface QueryCheckForUpgrade
 */
export interface QueryCheckForUpgrade {
  /**
   * The app name
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_name: string;
  /**
   * The app version
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_version: string;
  /**
   * The app platform
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  platform: string;
  /**
   * The app environment
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  environment: string;
  /**
   * The app language
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_language: string;
}
/**
 *
 * @export
 * @interface Region
 */
export interface Region {
  /**
   * The DNO region\'s ID.
   * @type {number}
   * @memberof Region
   */
  regionid: number;
}
/**
 *
 * @export
 * @interface RemoteLockDTO
 */
export interface RemoteLockDTO {
  /**
   * Whether or not remote lock is enabled
   * @type {boolean}
   * @memberof RemoteLockDTO
   */
  offMode: boolean | null;
}
/**
 *
 * @export
 * @interface ReportsDtoPayload
 */
export interface ReportsDtoPayload {
  /**
   * Statistics report inclusive start date eg: 2022-01-01
   * @type {string}
   * @memberof ReportsDtoPayload
   */
  from: string;
  /**
   * Statistics report inclusive end date eg: 2022-01-02
   * @type {string}
   * @memberof ReportsDtoPayload
   */
  to: string;
  /**
   * Total and business distances
   * @type {object}
   * @memberof ReportsDtoPayload
   */
  distances?: object;
  /**
   * A filter for presenting only organisation only expenses
   * @type {boolean}
   * @memberof ReportsDtoPayload
   */
  organisationOnly: boolean;
  /**
   * The unit of distance mi|km
   * @type {string}
   * @memberof ReportsDtoPayload
   */
  unitOfDistance: string;
  /**
   * Override the delivery email address for the report. Used for testing purposes only, does nothing in production.
   * @type {string}
   * @memberof ReportsDtoPayload
   */
  overrideEmailAddress?: string;
}
/**
 *
 * @export
 * @interface ResetPasswordRequestDto
 */
export interface ResetPasswordRequestDto {
  /**
   * Email
   * @type {string}
   * @memberof ResetPasswordRequestDto
   */
  email: string;
  /**
   * Continue url
   * @type {string}
   * @memberof ResetPasswordRequestDto
   */
  reset_password_continue_url?: string;
}
/**
 *
 * @export
 * @interface Restrictions
 */
export interface Restrictions {
  /**
   * Boolean for whether the user is allowed to charge
   * @type {boolean}
   * @memberof Restrictions
   */
  chargeAllowed: boolean;
  /**
   * Minimum balance required to use the charger
   * @type {object}
   * @memberof Restrictions
   */
  minimumBalance: object;
  /**
   * The user\'s current balance
   * @type {object}
   * @memberof Restrictions
   */
  userBalance: object;
  /**
   * The amount of charge that can be used before stopping
   * @type {Array<string>}
   * @memberof Restrictions
   */
  chargeLimits?: Array<string>;
}
/**
 *
 * @export
 * @interface RewardsAccountAddressDTO
 */
export interface RewardsAccountAddressDTO {
  /**
   * The city of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  city: string;
  /**
   * The country of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  country: string;
  /**
   * The county of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  county: string;
  /**
   * The first line of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  line1: string;
  /**
   * The second line of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  line2?: string;
  /**
   * The post code of the address
   * @type {string}
   * @memberof RewardsAccountAddressDTO
   */
  postcode: string;
}
/**
 *
 * @export
 * @interface RewardsAccountDTO
 */
export interface RewardsAccountDTO {
  /**
   * The first name of the person who owns the bank account
   * @type {string}
   * @memberof RewardsAccountDTO
   */
  firstName: string;
  /**
   * The last name of the person who owns the bank account
   * @type {string}
   * @memberof RewardsAccountDTO
   */
  lastName: string;
  /**
   * The address of the person who owns the bank account
   * @type {RewardsAccountAddressDTO}
   * @memberof RewardsAccountDTO
   */
  address: RewardsAccountAddressDTO;
  /**
   * The account number of the bank account
   * @type {string}
   * @memberof RewardsAccountDTO
   */
  accountNumber: string;
  /**
   * The sort code of the bank account
   * @type {string}
   * @memberof RewardsAccountDTO
   */
  sortCode: string;
}
/**
 *
 * @export
 * @interface RewardsBankAccountDTOImpl
 */
export interface RewardsBankAccountDTOImpl {
  /**
   * The ID of the bank account
   * @type {string}
   * @memberof RewardsBankAccountDTOImpl
   */
  id: string;
  /**
   * The name of the person attached to the bank account
   * @type {string}
   * @memberof RewardsBankAccountDTOImpl
   */
  name: string;
  /**
   * The last 4 digits
   * @type {string}
   * @memberof RewardsBankAccountDTOImpl
   */
  last4: string;
}
/**
 *
 * @export
 * @interface RewardsChargerDto
 */
export interface RewardsChargerDto {
  /**
   * The PPID of the charger
   * @type {string}
   * @memberof RewardsChargerDto
   */
  id: string;
  /**
   * The amount of rewardable miles for this charger
   * @type {number}
   * @memberof RewardsChargerDto
   */
  miles: number;
}
/**
 *
 * @export
 * @interface RewardsDto
 */
export interface RewardsDto {
  /**
   * The total amount of reward miles from individual chargers
   * @type {number}
   * @memberof RewardsDto
   */
  totalMiles: number;
  /**
   * The balance in the lowest unit of currency (pence for GBP)
   * @type {number}
   * @memberof RewardsDto
   */
  balance: number;
  /**
   * The currency represented by the balance
   * @type {string}
   * @memberof RewardsDto
   */
  currency: string;
  /**
   * The minimum amount of rewards miles required for payout
   * @type {number}
   * @memberof RewardsDto
   */
  payoutThreshold: number;
  /**
   * The chargers for which the user is eligible for the reward
   * @type {Array<RewardsChargerDto>}
   * @memberof RewardsDto
   */
  chargers: Array<RewardsChargerDto>;
}
/**
 *
 * @export
 * @interface RewardsTransactionDTOImpl
 */
export interface RewardsTransactionDTOImpl {
  /**
   * The ID of the transaction
   * @type {string}
   * @memberof RewardsTransactionDTOImpl
   */
  id: string;
  /**
   * The amount of miles claimed in this transaction
   * @type {number}
   * @memberof RewardsTransactionDTOImpl
   */
  milesClaimed: number;
  /**
   * The rate that was applied to convert miles to balance
   * @type {number}
   * @memberof RewardsTransactionDTOImpl
   */
  rate: number;
  /**
   * The value of the transaction in the lowest currency unit (pence for GBP)
   * @type {number}
   * @memberof RewardsTransactionDTOImpl
   */
  value: number;
  /**
   * The currency that the transaction is in
   * @type {string}
   * @memberof RewardsTransactionDTOImpl
   */
  currency: string;
  /**
   * The current status of the transaction
   * @type {string}
   * @memberof RewardsTransactionDTOImpl
   */
  status: RewardsTransactionDTOImplStatusEnum;
  /**
   * The date/time this payment was made
   * @type {string}
   * @memberof RewardsTransactionDTOImpl
   */
  createdAt: string;
}

export const RewardsTransactionDTOImplStatusEnum = {
  Pending: 'PENDING',
  Cancelled: 'CANCELLED',
  Returned: 'RETURNED',
  Posted: 'POSTED',
  Failed: 'FAILED',
} as const;

export type RewardsTransactionDTOImplStatusEnum =
  (typeof RewardsTransactionDTOImplStatusEnum)[keyof typeof RewardsTransactionDTOImplStatusEnum];

/**
 *
 * @export
 * @interface SendRecoverFactorRequest
 */
export interface SendRecoverFactorRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  email: string;
  /**
   * Redirect URL after factor recovery
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  recover_factor_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendVerifyAndChangeEmailRequest
 */
export interface SendVerifyAndChangeEmailRequest {
  /**
   * New Email
   * @type {string}
   * @memberof SendVerifyAndChangeEmailRequest
   */
  newEmail: string;
}
/**
 *
 * @export
 * @interface SetChargerTariffDto
 */
export interface SetChargerTariffDto {
  /**
   * The date from which the tariff is effective (inclusive)
   * @type {string}
   * @memberof SetChargerTariffDto
   */
  effectiveFrom: string;
  /**
   * The maximum price during off-peak hours in pence
   * @type {number}
   * @memberof SetChargerTariffDto
   */
  maxChargePrice?: number | null;
  /**
   * Reference to the supplier. Null if the supplier is unknown
   * @type {string}
   * @memberof SetChargerTariffDto
   */
  supplierId: string | null;
  /**
   * Tariff information applicable to specific days and times.
   * @type {Array<TariffInfoDtoImpl>}
   * @memberof SetChargerTariffDto
   */
  tariffInfo: Array<TariffInfoDtoImpl>;
  /**
   * Timezone the tariff information applies to
   * @type {string}
   * @memberof SetChargerTariffDto
   */
  timezone: string;
}
/**
 *
 * @export
 * @interface SetLinkyDTO
 */
export interface SetLinkyDTO {
  /**
   * Whether or not Linky is used for scheduling
   * @type {boolean}
   * @memberof SetLinkyDTO
   */
  scheduleEnabled: boolean | null;
}
/**
 *
 * @export
 * @interface SetVehicleIntent
 */
export interface SetVehicleIntent {
  /**
   *
   * @type {string}
   * @memberof SetVehicleIntent
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof SetVehicleIntent
   */
  updatedAt: string;
  /**
   *
   * @type {string}
   * @memberof SetVehicleIntent
   */
  delegatedControlChargingStationVehicleId: string;
  /**
   *
   * @type {string}
   * @memberof SetVehicleIntent
   */
  id: string;
  /**
   *
   * @type {Array<VehicleIntentEntry>}
   * @memberof SetVehicleIntent
   */
  intentDetails: Array<VehicleIntentEntry>;
  /**
   * The maximum price per kWh
   * @type {number}
   * @memberof SetVehicleIntent
   */
  maxPrice: number | null;
}
/**
 *
 * @export
 * @interface SetupDirectDebitActionDTO
 */
export interface SetupDirectDebitActionDTO {
  /**
   *
   * @type {string}
   * @memberof SetupDirectDebitActionDTO
   */
  type: SetupDirectDebitActionDTOTypeEnum;
  /**
   *
   * @type {SetupDirectDebitActionDataDTO}
   * @memberof SetupDirectDebitActionDTO
   */
  data: SetupDirectDebitActionDataDTO;
}

export const SetupDirectDebitActionDTOTypeEnum = {
  SetupDirectDebitV1: 'SETUP_DIRECT_DEBIT_V1',
} as const;

export type SetupDirectDebitActionDTOTypeEnum =
  (typeof SetupDirectDebitActionDTOTypeEnum)[keyof typeof SetupDirectDebitActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SetupDirectDebitActionDataDTO
 */
export interface SetupDirectDebitActionDataDTO {
  /**
   * Bank account number of the customer
   * @type {string}
   * @memberof SetupDirectDebitActionDataDTO
   */
  accountNumber: string;
  /**
   * Sort code of the customer’s bank
   * @type {string}
   * @memberof SetupDirectDebitActionDataDTO
   */
  sortCode: string;
  /**
   * Name of account holder
   * @type {string}
   * @memberof SetupDirectDebitActionDataDTO
   */
  accountName: string;
  /**
   * Request is from the only authorised signatory
   * @type {boolean}
   * @memberof SetupDirectDebitActionDataDTO
   */
  isOnlyAuthorisedSignatory: boolean;
  /**
   * The account holder understands the direct debit guarantee
   * @type {boolean}
   * @memberof SetupDirectDebitActionDataDTO
   */
  understandsDirectDebitGuarantee: boolean;
}
/**
 *
 * @export
 * @interface SignInWithMagicLinkDto
 */
export interface SignInWithMagicLinkDto {
  /**
   * The email to send the magic link to
   * @type {string}
   * @memberof SignInWithMagicLinkDto
   */
  email: string;
}
/**
 *
 * @export
 * @interface SmartChargingControllerUpdateVehicle200Response
 */
export interface SmartChargingControllerUpdateVehicle200Response {
  /**
   * Id
   * @type {string}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  id: string;
  /**
   * Last seen date
   * @type {string}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  lastSeen?: string | null;
  /**
   * The user\'s Enode ID
   * @type {string}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  enodeUserId?: string | null;
  /**
   * The vehicles\'s Enode ID
   * @type {string}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  enodeVehicleId?: string | null;
  /**
   * Vehicle data
   * @type {VehicleInformationImpl}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  vehicleInformation: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   * @type {GenericChargeStateImpl}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  chargeState: GenericChargeStateImpl;
  /**
   * Vehicle interventions
   * @type {InterventionDtoImpl}
   * @memberof SmartChargingControllerUpdateVehicle200Response
   */
  interventions: InterventionDtoImpl;
}
/**
 *
 * @export
 * @interface SolarPreferences
 */
export interface SolarPreferences {
  /**
   *
   * @type {string}
   * @memberof SolarPreferences
   */
  powerGeneration: string;
  /**
   *
   * @type {string}
   * @memberof SolarPreferences
   */
  solarMatching: string;
  /**
   * Deprecated: please use solarMaxGridImport instead
   * @type {number}
   * @memberof SolarPreferences
   */
  solarThreshold?: number;
  /**
   * The maximum amount of power in kWh to import from the grid. Replaces solarThreshold which has been deprecated
   * @type {number}
   * @memberof SolarPreferences
   */
  solarMaxGridImport?: number;
}
/**
 *
 * @export
 * @interface SubmitSupportFeedbackDTO
 */
export interface SubmitSupportFeedbackDTO {
  /**
   * The region the support case is relevant to
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  region: SubmitSupportFeedbackDTORegionEnum;
  /**
   * The email of the user submitting the feedback
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  email: string;
  /**
   * The free-text entered by the user
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  description: string;
  /**
   * The name of the charger, if relevant
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  chargerName?: string;
  /**
   * The name of the site at which the charger is located, if relevant
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  siteName?: string;
  /**
   * The address of the site at which the charger is located, if relevant
   * @type {string}
   * @memberof SubmitSupportFeedbackDTO
   */
  siteAddress?: string;
}

export const SubmitSupportFeedbackDTORegionEnum = {
  Es: 'es',
  Fr: 'fr',
} as const;

export type SubmitSupportFeedbackDTORegionEnum =
  (typeof SubmitSupportFeedbackDTORegionEnum)[keyof typeof SubmitSupportFeedbackDTORegionEnum];

/**
 *
 * @export
 * @interface SubscriptionCheckAffordabilityActionDTO
 */
export interface SubscriptionCheckAffordabilityActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  owner: SubscriptionCheckAffordabilityActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  status: SubscriptionCheckAffordabilityActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  type: SubscriptionCheckAffordabilityActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SubscriptionCheckAffordabilityDataDTO}
   * @memberof SubscriptionCheckAffordabilityActionDTO
   */
  data: SubscriptionCheckAffordabilityDataDTO;
}

export const SubscriptionCheckAffordabilityActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionCheckAffordabilityActionDTOOwnerEnum =
  (typeof SubscriptionCheckAffordabilityActionDTOOwnerEnum)[keyof typeof SubscriptionCheckAffordabilityActionDTOOwnerEnum];
export const SubscriptionCheckAffordabilityActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionCheckAffordabilityActionDTOStatusEnum =
  (typeof SubscriptionCheckAffordabilityActionDTOStatusEnum)[keyof typeof SubscriptionCheckAffordabilityActionDTOStatusEnum];
export const SubscriptionCheckAffordabilityActionDTOTypeEnum = {
  CheckAffordabilityV1: 'CHECK_AFFORDABILITY_V1',
} as const;

export type SubscriptionCheckAffordabilityActionDTOTypeEnum =
  (typeof SubscriptionCheckAffordabilityActionDTOTypeEnum)[keyof typeof SubscriptionCheckAffordabilityActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionCheckAffordabilityDataDTO
 */
export interface SubscriptionCheckAffordabilityDataDTO {
  /**
   * The ID of the application within the LMS
   * @type {number}
   * @memberof SubscriptionCheckAffordabilityDataDTO
   */
  applicationId: number | null;
  /**
   * The loan ID within the LMS
   * @type {number}
   * @memberof SubscriptionCheckAffordabilityDataDTO
   */
  loanId: number | null;
}
/**
 *
 * @export
 * @interface SubscriptionConfirmationOfPayeeDTO
 */
export interface SubscriptionConfirmationOfPayeeDTO {
  /**
   * The account holder\'s name
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeDTO
   */
  accountName: string;
  /**
   * The bank’s sort code
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeDTO
   */
  sortCode: string;
  /**
   * The bank account number
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeDTO
   */
  accountNumber: string;
}
/**
 *
 * @export
 * @interface SubscriptionConfirmationOfPayeeResponse
 */
export interface SubscriptionConfirmationOfPayeeResponse {
  /**
   * The outcome of the check
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeResponse
   */
  status: SubscriptionConfirmationOfPayeeResponseStatusEnum;
  /**
   * Provides context to the status of the check
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeResponse
   */
  reason?: string;
  /**
   * The account holder\'s name
   * @type {string}
   * @memberof SubscriptionConfirmationOfPayeeResponse
   */
  accountName: string;
}

export const SubscriptionConfirmationOfPayeeResponseStatusEnum = {
  Matched: 'matched',
  PartialMatch: 'partial_match',
  NotMatched: 'not_matched',
} as const;

export type SubscriptionConfirmationOfPayeeResponseStatusEnum =
  (typeof SubscriptionConfirmationOfPayeeResponseStatusEnum)[keyof typeof SubscriptionConfirmationOfPayeeResponseStatusEnum];

/**
 *
 * @export
 * @interface SubscriptionDTO
 */
export interface SubscriptionDTO {
  /**
   *
   * @type {Array<SubscriptionsControllerGetSubscriptionActionById200Response>}
   * @memberof SubscriptionDTO
   */
  actions: Array<SubscriptionsControllerGetSubscriptionActionById200Response>;
  /**
   * When the subscription was created
   * @type {string}
   * @memberof SubscriptionDTO
   */
  createdAt: string;
  /**
   * When the subscription was deleted
   * @type {string}
   * @memberof SubscriptionDTO
   */
  deletedAt: string | null;
  /**
   * The ID of the subscription
   * @type {string}
   * @memberof SubscriptionDTO
   */
  id: string;
  /**
   *
   * @type {SubscriptionOrderDTO}
   * @memberof SubscriptionDTO
   */
  order: SubscriptionOrderDTO;
  /**
   *
   * @type {SubscriptionPlanDTO}
   * @memberof SubscriptionDTO
   */
  plan: SubscriptionPlanDTO;
  /**
   * The status of the subscription
   * @type {string}
   * @memberof SubscriptionDTO
   */
  status: SubscriptionDTOStatusEnum;
  /**
   * When the subscription was last updated
   * @type {string}
   * @memberof SubscriptionDTO
   */
  updatedAt: string;
}

export const SubscriptionDTOStatusEnum = {
  Pending: 'PENDING',
  Active: 'ACTIVE',
  Cancelled: 'CANCELLED',
  Suspended: 'SUSPENDED',
  Ended: 'ENDED',
  Rejected: 'REJECTED',
} as const;

export type SubscriptionDTOStatusEnum =
  (typeof SubscriptionDTOStatusEnum)[keyof typeof SubscriptionDTOStatusEnum];

/**
 *
 * @export
 * @interface SubscriptionInstallChargingStationActionDTO
 */
export interface SubscriptionInstallChargingStationActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  owner: SubscriptionInstallChargingStationActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  status: SubscriptionInstallChargingStationActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  type: SubscriptionInstallChargingStationActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SubscriptionInstallChargingStationActionDataDTO}
   * @memberof SubscriptionInstallChargingStationActionDTO
   */
  data: SubscriptionInstallChargingStationActionDataDTO;
}

export const SubscriptionInstallChargingStationActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionInstallChargingStationActionDTOOwnerEnum =
  (typeof SubscriptionInstallChargingStationActionDTOOwnerEnum)[keyof typeof SubscriptionInstallChargingStationActionDTOOwnerEnum];
export const SubscriptionInstallChargingStationActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionInstallChargingStationActionDTOStatusEnum =
  (typeof SubscriptionInstallChargingStationActionDTOStatusEnum)[keyof typeof SubscriptionInstallChargingStationActionDTOStatusEnum];
export const SubscriptionInstallChargingStationActionDTOTypeEnum = {
  InstallChargingStationV1: 'INSTALL_CHARGING_STATION_V1',
} as const;

export type SubscriptionInstallChargingStationActionDTOTypeEnum =
  (typeof SubscriptionInstallChargingStationActionDTOTypeEnum)[keyof typeof SubscriptionInstallChargingStationActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionInstallChargingStationActionDataDTO
 */
export interface SubscriptionInstallChargingStationActionDataDTO {
  /**
   * The PPID of the charging station. Set once installed.
   * @type {string}
   * @memberof SubscriptionInstallChargingStationActionDataDTO
   */
  ppid: string | null;
}
/**
 *
 * @export
 * @interface SubscriptionListDTO
 */
export interface SubscriptionListDTO {
  /**
   * An array of subscriptions associated with the current user
   * @type {Array<SubscriptionDTO>}
   * @memberof SubscriptionListDTO
   */
  data: Array<SubscriptionDTO>;
}
/**
 *
 * @export
 * @interface SubscriptionOrderAddressDTO
 */
export interface SubscriptionOrderAddressDTO {
  /**
   * Line 1 of the address
   * @type {string}
   * @memberof SubscriptionOrderAddressDTO
   */
  line1: string;
  /**
   * Line 2 of the address
   * @type {string}
   * @memberof SubscriptionOrderAddressDTO
   */
  line2: string | null;
  /**
   * Line 3 of the address
   * @type {string}
   * @memberof SubscriptionOrderAddressDTO
   */
  line3: string | null;
  /**
   * Postcode of the address
   * @type {string}
   * @memberof SubscriptionOrderAddressDTO
   */
  postcode: string;
}
/**
 *
 * @export
 * @interface SubscriptionOrderDTO
 */
export interface SubscriptionOrderDTO {
  /**
   * The ID of the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  id: string;
  /**
   * When the order was placed
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  orderedAt: string;
  /**
   * Where the order details originate from
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  origin: SubscriptionOrderDTOOriginEnum;
  /**
   * The address of the person who placed the address
   * @type {SubscriptionOrderAddressDTO}
   * @memberof SubscriptionOrderDTO
   */
  address: SubscriptionOrderAddressDTO;
  /**
   * The email address of the person who placed the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  email: string;
  /**
   * The first name of the person who placed the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  firstName: string;
  /**
   * The last name of the person who placed the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  lastName: string;
  /**
   * The person who placed the order\'s MPAN
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  mpan: string;
  /**
   * The phone number of the person who placed the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  phoneNumber: string;
  /**
   * The title of the person who placed the order
   * @type {string}
   * @memberof SubscriptionOrderDTO
   */
  title: SubscriptionOrderDTOTitleEnum;
}

export const SubscriptionOrderDTOOriginEnum = {
  Salesforce: 'SALESFORCE',
  SalesforceTest: 'SALESFORCE_TEST',
} as const;

export type SubscriptionOrderDTOOriginEnum =
  (typeof SubscriptionOrderDTOOriginEnum)[keyof typeof SubscriptionOrderDTOOriginEnum];
export const SubscriptionOrderDTOTitleEnum = {
  Mr: 'MR',
  Mrs: 'MRS',
  Miss: 'MISS',
  Ms: 'MS',
  Mx: 'MX',
  Dr: 'DR',
  Other: 'OTHER',
} as const;

export type SubscriptionOrderDTOTitleEnum =
  (typeof SubscriptionOrderDTOTitleEnum)[keyof typeof SubscriptionOrderDTOTitleEnum];

/**
 *
 * @export
 * @interface SubscriptionPayUpfrontFeeActionDTO
 */
export interface SubscriptionPayUpfrontFeeActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  owner: SubscriptionPayUpfrontFeeActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  status: SubscriptionPayUpfrontFeeActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  type: SubscriptionPayUpfrontFeeActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {object}
   * @memberof SubscriptionPayUpfrontFeeActionDTO
   */
  data: object;
}

export const SubscriptionPayUpfrontFeeActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionPayUpfrontFeeActionDTOOwnerEnum =
  (typeof SubscriptionPayUpfrontFeeActionDTOOwnerEnum)[keyof typeof SubscriptionPayUpfrontFeeActionDTOOwnerEnum];
export const SubscriptionPayUpfrontFeeActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionPayUpfrontFeeActionDTOStatusEnum =
  (typeof SubscriptionPayUpfrontFeeActionDTOStatusEnum)[keyof typeof SubscriptionPayUpfrontFeeActionDTOStatusEnum];
export const SubscriptionPayUpfrontFeeActionDTOTypeEnum = {
  PayUpfrontFeeV1: 'PAY_UPFRONT_FEE_V1',
} as const;

export type SubscriptionPayUpfrontFeeActionDTOTypeEnum =
  (typeof SubscriptionPayUpfrontFeeActionDTOTypeEnum)[keyof typeof SubscriptionPayUpfrontFeeActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionPlanDTO
 */
export interface SubscriptionPlanDTO {
  /**
   * The ID of the plan
   * @type {string}
   * @memberof SubscriptionPlanDTO
   */
  id: string;
  /**
   * How many miles are allowed to be claimed
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  allowanceMiles: number;
  /**
   * How long the allowance period is
   * @type {string}
   * @memberof SubscriptionPlanDTO
   */
  allowancePeriod: SubscriptionPlanDTOAllowancePeriodEnum;
  /**
   * The upfront fee required for the plan (in GBP)
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  upfrontFeePounds: number;
  /**
   * The monthly fee required for the plan (in GBP)
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  monthlyFeePounds: number;
  /**
   * The number of months the contract lasts
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  contractDurationMonths: number;
  /**
   * The product code of the plan
   * @type {string}
   * @memberof SubscriptionPlanDTO
   */
  productCode: string;
  /**
   * The conversion rate between miles and KWH
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  rateMilesPerKwh: number;
  /**
   * The conversion rate between pence and miles
   * @type {number}
   * @memberof SubscriptionPlanDTO
   */
  ratePencePerMile: number;
  /**
   * The type of plan
   * @type {string}
   * @memberof SubscriptionPlanDTO
   */
  type: SubscriptionPlanDTOTypeEnum;
}

export const SubscriptionPlanDTOAllowancePeriodEnum = {
  Annual: 'ANNUAL',
} as const;

export type SubscriptionPlanDTOAllowancePeriodEnum =
  (typeof SubscriptionPlanDTOAllowancePeriodEnum)[keyof typeof SubscriptionPlanDTOAllowancePeriodEnum];
export const SubscriptionPlanDTOTypeEnum = {
  PodDrive: 'POD_DRIVE',
} as const;

export type SubscriptionPlanDTOTypeEnum =
  (typeof SubscriptionPlanDTOTypeEnum)[keyof typeof SubscriptionPlanDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionSetupDirectDebitActionDTO
 */
export interface SubscriptionSetupDirectDebitActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  owner: SubscriptionSetupDirectDebitActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  status: SubscriptionSetupDirectDebitActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  type: SubscriptionSetupDirectDebitActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {object}
   * @memberof SubscriptionSetupDirectDebitActionDTO
   */
  data: object;
}

export const SubscriptionSetupDirectDebitActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionSetupDirectDebitActionDTOOwnerEnum =
  (typeof SubscriptionSetupDirectDebitActionDTOOwnerEnum)[keyof typeof SubscriptionSetupDirectDebitActionDTOOwnerEnum];
export const SubscriptionSetupDirectDebitActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionSetupDirectDebitActionDTOStatusEnum =
  (typeof SubscriptionSetupDirectDebitActionDTOStatusEnum)[keyof typeof SubscriptionSetupDirectDebitActionDTOStatusEnum];
export const SubscriptionSetupDirectDebitActionDTOTypeEnum = {
  SetupDirectDebitV1: 'SETUP_DIRECT_DEBIT_V1',
} as const;

export type SubscriptionSetupDirectDebitActionDTOTypeEnum =
  (typeof SubscriptionSetupDirectDebitActionDTOTypeEnum)[keyof typeof SubscriptionSetupDirectDebitActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionSignDocumentsActionDTO
 */
export interface SubscriptionSignDocumentsActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  owner: SubscriptionSignDocumentsActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  status: SubscriptionSignDocumentsActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  type: SubscriptionSignDocumentsActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SubscriptionSignLoanAgreementActionDataDTO}
   * @memberof SubscriptionSignDocumentsActionDTO
   */
  data: SubscriptionSignLoanAgreementActionDataDTO;
}

export const SubscriptionSignDocumentsActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionSignDocumentsActionDTOOwnerEnum =
  (typeof SubscriptionSignDocumentsActionDTOOwnerEnum)[keyof typeof SubscriptionSignDocumentsActionDTOOwnerEnum];
export const SubscriptionSignDocumentsActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionSignDocumentsActionDTOStatusEnum =
  (typeof SubscriptionSignDocumentsActionDTOStatusEnum)[keyof typeof SubscriptionSignDocumentsActionDTOStatusEnum];
export const SubscriptionSignDocumentsActionDTOTypeEnum = {
  SignDocumentsV1: 'SIGN_DOCUMENTS_V1',
} as const;

export type SubscriptionSignDocumentsActionDTOTypeEnum =
  (typeof SubscriptionSignDocumentsActionDTOTypeEnum)[keyof typeof SubscriptionSignDocumentsActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionSignLoanAgreementActionDataDTO
 */
export interface SubscriptionSignLoanAgreementActionDataDTO {
  /**
   * A list of documents to be signed
   * @type {Array<SubscriptionSignLoanAgreementActionSignDocumentDTO>}
   * @memberof SubscriptionSignLoanAgreementActionDataDTO
   */
  documents: Array<SubscriptionSignLoanAgreementActionSignDocumentDTO>;
}
/**
 *
 * @export
 * @interface SubscriptionSignLoanAgreementActionSignDocumentDTO
 */
export interface SubscriptionSignLoanAgreementActionSignDocumentDTO {
  /**
   * The url to sign the document
   * @type {string}
   * @memberof SubscriptionSignLoanAgreementActionSignDocumentDTO
   */
  signingUrl: string;
  /**
   * The document type
   * @type {string}
   * @memberof SubscriptionSignLoanAgreementActionSignDocumentDTO
   */
  code: SubscriptionSignLoanAgreementActionSignDocumentDTOCodeEnum;
  /**
   * Is the document signed
   * @type {boolean}
   * @memberof SubscriptionSignLoanAgreementActionSignDocumentDTO
   */
  signed: boolean;
}

export const SubscriptionSignLoanAgreementActionSignDocumentDTOCodeEnum = {
  Rca: 'rca',
  Ha: 'ha',
} as const;

export type SubscriptionSignLoanAgreementActionSignDocumentDTOCodeEnum =
  (typeof SubscriptionSignLoanAgreementActionSignDocumentDTOCodeEnum)[keyof typeof SubscriptionSignLoanAgreementActionSignDocumentDTOCodeEnum];

/**
 *
 * @export
 * @interface SubscriptionSurveyActionDTO
 */
export interface SubscriptionSurveyActionDTO {
  /**
   * ID of the subscription the action is associated with
   * @type {string}
   * @memberof SubscriptionSurveyActionDTO
   */
  subscriptionId: string;
  /**
   * The ID of the action
   * @type {string}
   * @memberof SubscriptionSurveyActionDTO
   */
  id: string;
  /**
   * Who is required to complete the action
   * @type {string}
   * @memberof SubscriptionSurveyActionDTO
   */
  owner: SubscriptionSurveyActionDTOOwnerEnum;
  /**
   * The status of the action
   * @type {string}
   * @memberof SubscriptionSurveyActionDTO
   */
  status: SubscriptionSurveyActionDTOStatusEnum;
  /**
   * The type of action
   * @type {string}
   * @memberof SubscriptionSurveyActionDTO
   */
  type: SubscriptionSurveyActionDTOTypeEnum;
  /**
   * IDs of actions which this action depends on
   * @type {Array<string>}
   * @memberof SubscriptionSurveyActionDTO
   */
  dependsOn: Array<string>;
  /**
   *
   * @type {SubscriptionSurveyActionDataDTO}
   * @memberof SubscriptionSurveyActionDTO
   */
  data: SubscriptionSurveyActionDataDTO;
}

export const SubscriptionSurveyActionDTOOwnerEnum = {
  User: 'USER',
  System: 'SYSTEM',
} as const;

export type SubscriptionSurveyActionDTOOwnerEnum =
  (typeof SubscriptionSurveyActionDTOOwnerEnum)[keyof typeof SubscriptionSurveyActionDTOOwnerEnum];
export const SubscriptionSurveyActionDTOStatusEnum = {
  Pending: 'PENDING',
  Success: 'SUCCESS',
  Failure: 'FAILURE',
} as const;

export type SubscriptionSurveyActionDTOStatusEnum =
  (typeof SubscriptionSurveyActionDTOStatusEnum)[keyof typeof SubscriptionSurveyActionDTOStatusEnum];
export const SubscriptionSurveyActionDTOTypeEnum = {
  CompleteHomeSurveyV1: 'COMPLETE_HOME_SURVEY_V1',
} as const;

export type SubscriptionSurveyActionDTOTypeEnum =
  (typeof SubscriptionSurveyActionDTOTypeEnum)[keyof typeof SubscriptionSurveyActionDTOTypeEnum];

/**
 *
 * @export
 * @interface SubscriptionSurveyActionDataDTO
 */
export interface SubscriptionSurveyActionDataDTO {
  /**
   * URL of the survey
   * @type {string}
   * @memberof SubscriptionSurveyActionDataDTO
   */
  surveyUrl: string;
}
/**
 * @type SubscriptionsControllerGetSubscriptionActionById200Response
 * @export
 */
export type SubscriptionsControllerGetSubscriptionActionById200Response =
  | SubscriptionCheckAffordabilityActionDTO
  | SubscriptionInstallChargingStationActionDTO
  | SubscriptionPayUpfrontFeeActionDTO
  | SubscriptionSetupDirectDebitActionDTO
  | SubscriptionSignDocumentsActionDTO
  | SubscriptionSurveyActionDTO;

/**
 * @type SubscriptionsControllerUpdateSubscriptionAction200Response
 * @export
 */
export type SubscriptionsControllerUpdateSubscriptionAction200Response =
  | SubscriptionCheckAffordabilityActionDTO
  | SubscriptionSetupDirectDebitActionDTO;

/**
 * @type SubscriptionsControllerUpdateSubscriptionActionRequest
 * @export
 */
export type SubscriptionsControllerUpdateSubscriptionActionRequest =
  | AffordabilityActionDTO
  | SetupDirectDebitActionDTO;

/**
 *
 * @export
 * @interface SupplierDtoImpl
 */
export interface SupplierDtoImpl {
  /**
   *
   * @type {string}
   * @memberof SupplierDtoImpl
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof SupplierDtoImpl
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof SupplierDtoImpl
   */
  timeZone: SupplierDtoImplTimeZoneEnum;
  /**
   *
   * @type {string}
   * @memberof SupplierDtoImpl
   */
  icon: string;
  /**
   *
   * @type {number}
   * @memberof SupplierDtoImpl
   */
  defaultMaxChargePrice: number;
  /**
   *
   * @type {Array<TariffInfoDtoImpl>}
   * @memberof SupplierDtoImpl
   */
  defaultTariffInfo: Array<TariffInfoDtoImpl>;
}

export const SupplierDtoImplTimeZoneEnum = {
  EuropeLondon: 'Europe/London',
  EuropeMadrid: 'Europe/Madrid',
  EuropeParis: 'Europe/Paris',
  EtcUtc: 'Etc/UTC',
} as const;

export type SupplierDtoImplTimeZoneEnum =
  (typeof SupplierDtoImplTimeZoneEnum)[keyof typeof SupplierDtoImplTimeZoneEnum];

/**
 *
 * @export
 * @interface TariffInfoDtoImpl
 */
export interface TariffInfoDtoImpl {
  /**
   *
   * @type {Array<string>}
   * @memberof TariffInfoDtoImpl
   */
  days: Array<TariffInfoDtoImplDaysEnum>;
  /**
   * When the period starts (format: XX:XX:XX)
   * @type {string}
   * @memberof TariffInfoDtoImpl
   */
  start: string;
  /**
   * When the period ends (format: XX:XX:XX)
   * @type {string}
   * @memberof TariffInfoDtoImpl
   */
  end: string;
  /**
   * Price per unit (in pence)
   * @type {number}
   * @memberof TariffInfoDtoImpl
   */
  price: number;
}

export const TariffInfoDtoImplDaysEnum = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type TariffInfoDtoImplDaysEnum =
  (typeof TariffInfoDtoImplDaysEnum)[keyof typeof TariffInfoDtoImplDaysEnum];

/**
 *
 * @export
 * @interface TariffRequest
 */
export interface TariffRequest {
  /**
   * User Id
   * @type {number}
   * @memberof TariffRequest
   */
  user_id: number;
  /**
   * Energy supplier id
   * @type {number}
   * @memberof TariffRequest
   */
  energy_supplier_id: number | null;
  /**
   * A list of tarrifs
   * @type {Array<Tier>}
   * @memberof TariffRequest
   */
  tiers: Array<Tier>;
}
/**
 *
 * @export
 * @interface TariffResponse
 */
export interface TariffResponse {
  /**
   *
   * @type {number}
   * @memberof TariffResponse
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  name: string;
  /**
   *
   * @type {number}
   * @memberof TariffResponse
   */
  energy_supplier_id: number;
  /**
   *
   * @type {string}
   * @memberof TariffResponse
   */
  currency: string;
}
/**
 *
 * @export
 * @interface Tier
 */
export interface Tier {
  /**
   *
   * @type {number}
   * @memberof Tier
   */
  rate: number;
  /**
   *
   * @type {string}
   * @memberof Tier
   */
  begin_time?: string;
  /**
   *
   * @type {string}
   * @memberof Tier
   */
  end_time?: string;
}
/**
 *
 * @export
 * @interface TrackLoginRequest
 */
export interface TrackLoginRequest {
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  ipAddress: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  userAgent: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  timestamp: string;
  /**
   *
   * @type {string}
   * @memberof TrackLoginRequest
   */
  authId?: string;
}
/**
 *
 * @export
 * @interface UpdateUserDto
 */
export interface UpdateUserDto {
  /**
   * First name
   * @type {string}
   * @memberof UpdateUserDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof UpdateUserDto
   */
  last_name: string;
}
/**
 *
 * @export
 * @interface UpdateVehicleLinkRequestDtoImpl
 */
export interface UpdateVehicleLinkRequestDtoImpl {
  /**
   * Is this the primary charger for this vehicle
   * @type {boolean}
   * @memberof UpdateVehicleLinkRequestDtoImpl
   */
  isPrimary?: boolean;
  /**
   * Vehicle data
   * @type {VehicleRequestDtoImpl}
   * @memberof UpdateVehicleLinkRequestDtoImpl
   */
  vehicle?: VehicleRequestDtoImpl;
}
/**
 *
 * @export
 * @interface User
 */
export interface User {
  /**
   *
   * @type {string}
   * @memberof User
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  first_name: string;
  /**
   *
   * @type {number}
   * @memberof User
   */
  hasHomeCharge?: number;
  /**
   *
   * @type {number}
   * @memberof User
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof User
   */
  last_name: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  role: string;
  /**
   *
   * @type {string}
   * @memberof User
   */
  locale: string;
  /**
   *
   * @type {object}
   * @memberof User
   */
  tariff?: object;
  /**
   *
   * @type {object}
   * @memberof User
   */
  unit?: object;
  /**
   *
   * @type {object}
   * @memberof User
   */
  vehicle?: object;
  /**
   *
   * @type {object}
   * @memberof User
   */
  account?: object;
  /**
   *
   * @type {Array<object>}
   * @memberof User
   */
  preferences?: Array<object>;
  /**
   *
   * @type {Array<object>}
   * @memberof User
   */
  notifications?: Array<object>;
}
/**
 *
 * @export
 * @interface UserDetailsDto
 */
export interface UserDetailsDto {
  /**
   * First name
   * @type {string}
   * @memberof UserDetailsDto
   */
  first_name: string;
  /**
   * Last name
   * @type {string}
   * @memberof UserDetailsDto
   */
  last_name: string;
  /**
   * Locale
   * @type {string}
   * @memberof UserDetailsDto
   */
  locale: string;
}
/**
 *
 * @export
 * @interface UserResponse
 */
export interface UserResponse {
  /**
   *
   * @type {User}
   * @memberof UserResponse
   */
  users: User;
}
/**
 *
 * @export
 * @interface VehicleChargeInfoDtoImpl
 */
export interface VehicleChargeInfoDtoImpl {
  /**
   *
   * @type {number}
   * @memberof VehicleChargeInfoDtoImpl
   */
  expectedChargeByTargetPercent: number | null;
  /**
   *
   * @type {number}
   * @memberof VehicleChargeInfoDtoImpl
   */
  expectedChargeByTarget_kWh: number | null;
  /**
   *
   * @type {string}
   * @memberof VehicleChargeInfoDtoImpl
   */
  fullChargeByTime: string | null;
}
/**
 *
 * @export
 * @interface VehicleInformationImpl
 */
export interface VehicleInformationImpl {
  /**
   * Vehicle brand
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  brand?: string | null;
  /**
   * Vehicle model
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  model?: string | null;
  /**
   * Vehicle model variant
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  modelVariant?: string | null;
  /**
   * Vehicle registration plate
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  vehicleRegistrationPlate?: string | null;
  /**
   * Vehicle display name
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  displayName?: string | null;
  /**
   * The ID of the vehicle in the EV Database
   * @type {string}
   * @memberof VehicleInformationImpl
   */
  evDatabaseId?: string;
}
/**
 *
 * @export
 * @interface VehicleIntentEntry
 */
export interface VehicleIntentEntry {
  /**
   *
   * @type {string}
   * @memberof VehicleIntentEntry
   */
  chargeByTime: string;
  /**
   *
   * @type {number}
   * @memberof VehicleIntentEntry
   */
  chargeKWh: number;
  /**
   *
   * @type {string}
   * @memberof VehicleIntentEntry
   */
  dayOfWeek: VehicleIntentEntryDayOfWeekEnum;
}

export const VehicleIntentEntryDayOfWeekEnum = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type VehicleIntentEntryDayOfWeekEnum =
  (typeof VehicleIntentEntryDayOfWeekEnum)[keyof typeof VehicleIntentEntryDayOfWeekEnum];

/**
 *
 * @export
 * @interface VehicleIntentEntryDtoImpl
 */
export interface VehicleIntentEntryDtoImpl {
  /**
   * Charge by time
   * @type {string}
   * @memberof VehicleIntentEntryDtoImpl
   */
  chargeByTime: string;
  /**
   * Charge KWh
   * @type {number}
   * @memberof VehicleIntentEntryDtoImpl
   */
  chargeKWh: number;
  /**
   *
   * @type {string}
   * @memberof VehicleIntentEntryDtoImpl
   */
  dayOfWeek: VehicleIntentEntryDtoImplDayOfWeekEnum;
}

export const VehicleIntentEntryDtoImplDayOfWeekEnum = {
  Monday: 'MONDAY',
  Tuesday: 'TUESDAY',
  Wednesday: 'WEDNESDAY',
  Thursday: 'THURSDAY',
  Friday: 'FRIDAY',
  Saturday: 'SATURDAY',
  Sunday: 'SUNDAY',
} as const;

export type VehicleIntentEntryDtoImplDayOfWeekEnum =
  (typeof VehicleIntentEntryDtoImplDayOfWeekEnum)[keyof typeof VehicleIntentEntryDtoImplDayOfWeekEnum];

/**
 *
 * @export
 * @interface VehicleIntentsRequestDtoImpl
 */
export interface VehicleIntentsRequestDtoImpl {
  /**
   * Charging intents
   * @type {Array<VehicleIntentEntryDtoImpl>}
   * @memberof VehicleIntentsRequestDtoImpl
   */
  intentDetails: Array<VehicleIntentEntryDtoImpl>;
}
/**
 *
 * @export
 * @interface VehicleIntentsResponseDtoImpl
 */
export interface VehicleIntentsResponseDtoImpl {
  /**
   * Id
   * @type {string}
   * @memberof VehicleIntentsResponseDtoImpl
   */
  id: string;
  /**
   * Vehicle intent details
   * @type {Array<VehicleIntentEntryDtoImpl>}
   * @memberof VehicleIntentsResponseDtoImpl
   */
  details: Array<VehicleIntentEntryDtoImpl>;
  /**
   * The maximum price per kWh
   * @type {number}
   * @memberof VehicleIntentsResponseDtoImpl
   */
  maxPrice: number | null;
}
/**
 *
 * @export
 * @interface VehicleInterventionDtoImpl
 */
export interface VehicleInterventionDtoImpl {
  /**
   * The identifier of the intervention
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  id: string;
  /**
   * The vendor of the intervention
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  vendor: string | null;
  /**
   * The type of the intervention
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  vendorType: string | null;
  /**
   * The brand of the vehicle
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  brand: string | null;
  /**
   * The date the intervention was introduced
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  introducedAt: string;
  /**
   * The domain of the intervention
   * @type {string}
   * @memberof VehicleInterventionDtoImpl
   */
  domain: VehicleInterventionDtoImplDomainEnum;
  /**
   * The resolution of the intervention
   * @type {VehicleInterventionResolutionDtoImpl}
   * @memberof VehicleInterventionDtoImpl
   */
  resolution: VehicleInterventionResolutionDtoImpl;
}

export const VehicleInterventionDtoImplDomainEnum = {
  Account: 'Account',
  Device: 'Device',
} as const;

export type VehicleInterventionDtoImplDomainEnum =
  (typeof VehicleInterventionDtoImplDomainEnum)[keyof typeof VehicleInterventionDtoImplDomainEnum];

/**
 *
 * @export
 * @interface VehicleInterventionResolutionDtoImpl
 */
export interface VehicleInterventionResolutionDtoImpl {
  /**
   * The title of resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDtoImpl
   */
  title: string;
  /**
   * The description of resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDtoImpl
   */
  description: string;
  /**
   * The access required to perform the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDtoImpl
   */
  access: string;
  /**
   * The agent responsible for performing the resolution
   * @type {string}
   * @memberof VehicleInterventionResolutionDtoImpl
   */
  agent: string;
}
/**
 *
 * @export
 * @interface VehicleInterventionResponseDtoImpl
 */
export interface VehicleInterventionResponseDtoImpl {
  /**
   * Interventions relating to charge state
   * @type {Array<VehicleInterventionDtoImpl>}
   * @memberof VehicleInterventionResponseDtoImpl
   */
  chargeState: Array<VehicleInterventionDtoImpl>;
  /**
   * Interventions relating to information
   * @type {Array<VehicleInterventionDtoImpl>}
   * @memberof VehicleInterventionResponseDtoImpl
   */
  information: Array<VehicleInterventionDtoImpl>;
}
/**
 *
 * @export
 * @interface VehicleLinkRequestDtoImpl
 */
export interface VehicleLinkRequestDtoImpl {
  /**
   * Is this the primary charger for this vehicle
   * @type {boolean}
   * @memberof VehicleLinkRequestDtoImpl
   */
  isPrimary?: boolean;
  /**
   * Vehicle data
   * @type {VehicleRequestDtoImpl}
   * @memberof VehicleLinkRequestDtoImpl
   */
  vehicle: VehicleRequestDtoImpl;
  /**
   * Charging intents
   * @type {Array<VehicleIntentEntryDtoImpl>}
   * @memberof VehicleLinkRequestDtoImpl
   */
  intents: Array<VehicleIntentEntryDtoImpl>;
}
/**
 *
 * @export
 * @interface VehicleLinkResponseDtoImpl
 */
export interface VehicleLinkResponseDtoImpl {
  /**
   * Id
   * @type {string}
   * @memberof VehicleLinkResponseDtoImpl
   */
  id: string;
  /**
   * Is this the primary charger for this vehicle
   * @type {boolean}
   * @memberof VehicleLinkResponseDtoImpl
   */
  isPrimary: boolean;
  /**
   * Is this vehicle plugged into the charger
   * @type {boolean}
   * @memberof VehicleLinkResponseDtoImpl
   */
  isPluggedInToThisCharger: boolean;
  /**
   *
   * @type {VehicleLinkResponseDtoImplVehicle}
   * @memberof VehicleLinkResponseDtoImpl
   */
  vehicle: VehicleLinkResponseDtoImplVehicle;
  /**
   * Intent data
   * @type {VehicleIntentsResponseDtoImpl}
   * @memberof VehicleLinkResponseDtoImpl
   */
  intents: VehicleIntentsResponseDtoImpl;
}
/**
 * Vehicle data
 * @export
 * @interface VehicleLinkResponseDtoImplVehicle
 */
export interface VehicleLinkResponseDtoImplVehicle {
  /**
   * Id
   * @type {string}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  id: string;
  /**
   * Last seen date
   * @type {string}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  lastSeen?: string | null;
  /**
   * The user\'s Enode ID
   * @type {string}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  enodeUserId?: string | null;
  /**
   * The vehicles\'s Enode ID
   * @type {string}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  enodeVehicleId?: string | null;
  /**
   * Vehicle data
   * @type {VehicleInformationImpl}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  vehicleInformation: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   * @type {GenericChargeStateImpl}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  chargeState: GenericChargeStateImpl;
  /**
   * Vehicle interventions
   * @type {InterventionDtoImpl}
   * @memberof VehicleLinkResponseDtoImplVehicle
   */
  interventions: InterventionDtoImpl;
}
/**
 *
 * @export
 * @interface VehicleRequestDtoImpl
 */
export interface VehicleRequestDtoImpl {
  /**
   * Vehicle data
   * @type {VehicleInformationImpl}
   * @memberof VehicleRequestDtoImpl
   */
  vehicleInformation?: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   * @type {GenericChargeStateImpl}
   * @memberof VehicleRequestDtoImpl
   */
  chargeState?: GenericChargeStateImpl;
  /**
   * Enode user id
   * @type {string}
   * @memberof VehicleRequestDtoImpl
   */
  enodeUserId?: string;
  /**
   * Enode vehicle id
   * @type {string}
   * @memberof VehicleRequestDtoImpl
   */
  enodeVehicleId?: string;
}
/**
 *
 * @export
 * @interface WifiCredentials
 */
export interface WifiCredentials {
  /**
   *
   * @type {string}
   * @memberof WifiCredentials
   */
  ppid?: string;
  /**
   *
   * @type {string}
   * @memberof WifiCredentials
   */
  ssid?: string;
  /**
   *
   * @type {string}
   * @memberof WifiCredentials
   */
  password?: string;
}

/**
 * API3Api - axios parameter creator
 * @export
 */
export const API3ApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Claims a given charge
     * @summary claim a charge
     * @param {ChargeRequestDTO} chargeRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerClaimCharge: async (
      chargeRequestDTO: ChargeRequestDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'chargeRequestDTO' is not null or undefined
      assertParamExists(
        'api3ControllerClaimCharge',
        'chargeRequestDTO',
        chargeRequestDTO
      );
      const localVarPath = `/api3/v5/charges`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargeRequestDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Request firmware version
     * @summary request firmware version
     * @param {number} unitId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetCurrentFirmware: async (
      unitId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'unitId' is not null or undefined
      assertParamExists('api3ControllerGetCurrentFirmware', 'unitId', unitId);
      const localVarPath = `/api3/v5/units/{unitId}/firmware`.replace(
        `{${'unitId'}}`,
        encodeURIComponent(String(unitId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Request to get all the locales
     * @summary get locales
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetLocales: async (
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/api3/v5/locales`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve user info from API3 based on the JWT token
     * @summary retrieve user info
     * @param {string} [include] Query param for auth
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetUser: async (
      include?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/api3/v5/auth`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (include !== undefined) {
        localVarQueryParameter['include'] = include;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Request to store a new tariff or update an existing one
     * @summary store tariff
     * @param {TariffRequest} tariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerStoreTariff: async (
      tariffRequest: TariffRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'tariffRequest' is not null or undefined
      assertParamExists(
        'api3ControllerStoreTariff',
        'tariffRequest',
        tariffRequest
      );
      const localVarPath = `/api3/v5/tariffs`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        tariffRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Tops up the user\'s account
     * @summary top up an account
     * @param {string} id
     * @param {AccountTopUpRequestDTO} accountTopUpRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerTopUpAccount: async (
      id: string,
      accountTopUpRequestDTO: AccountTopUpRequestDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('api3ControllerTopUpAccount', 'id', id);
      // verify required parameter 'accountTopUpRequestDTO' is not null or undefined
      assertParamExists(
        'api3ControllerTopUpAccount',
        'accountTopUpRequestDTO',
        accountTopUpRequestDTO
      );
      const localVarPath = `/api3/v5/users/{id}/account/topup`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        accountTopUpRequestDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * API3Api - functional programming interface
 * @export
 */
export const API3ApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = API3ApiAxiosParamCreator(configuration);
  return {
    /**
     * Claims a given charge
     * @summary claim a charge
     * @param {ChargeRequestDTO} chargeRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerClaimCharge(
      chargeRequestDTO: ChargeRequestDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargeRequestResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerClaimCharge(
          chargeRequestDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerClaimCharge']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Request firmware version
     * @summary request firmware version
     * @param {number} unitId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerGetCurrentFirmware(
      unitId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FirmwareStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerGetCurrentFirmware(
          unitId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerGetCurrentFirmware']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Request to get all the locales
     * @summary get locales
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerGetLocales(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<LocaleResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerGetLocales(
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerGetLocales']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve user info from API3 based on the JWT token
     * @summary retrieve user info
     * @param {string} [include] Query param for auth
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerGetUser(
      include?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerGetUser(include, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerGetUser']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Request to store a new tariff or update an existing one
     * @summary store tariff
     * @param {TariffRequest} tariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerStoreTariff(
      tariffRequest: TariffRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<TariffResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerStoreTariff(
          tariffRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerStoreTariff']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Tops up the user\'s account
     * @summary top up an account
     * @param {string} id
     * @param {AccountTopUpRequestDTO} accountTopUpRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async api3ControllerTopUpAccount(
      id: string,
      accountTopUpRequestDTO: AccountTopUpRequestDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.api3ControllerTopUpAccount(
          id,
          accountTopUpRequestDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['API3Api.api3ControllerTopUpAccount']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * API3Api - factory interface
 * @export
 */
export const API3ApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = API3ApiFp(configuration);
  return {
    /**
     * Claims a given charge
     * @summary claim a charge
     * @param {ChargeRequestDTO} chargeRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerClaimCharge(
      chargeRequestDTO: ChargeRequestDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargeRequestResponse> {
      return localVarFp
        .api3ControllerClaimCharge(chargeRequestDTO, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Request firmware version
     * @summary request firmware version
     * @param {number} unitId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetCurrentFirmware(
      unitId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FirmwareStatusResponse> {
      return localVarFp
        .api3ControllerGetCurrentFirmware(unitId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Request to get all the locales
     * @summary get locales
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetLocales(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<LocaleResponse> {
      return localVarFp
        .api3ControllerGetLocales(acceptLanguage, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve user info from API3 based on the JWT token
     * @summary retrieve user info
     * @param {string} [include] Query param for auth
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerGetUser(
      include?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserResponse> {
      return localVarFp
        .api3ControllerGetUser(include, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Request to store a new tariff or update an existing one
     * @summary store tariff
     * @param {TariffRequest} tariffRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerStoreTariff(
      tariffRequest: TariffRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<TariffResponse> {
      return localVarFp
        .api3ControllerStoreTariff(tariffRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Tops up the user\'s account
     * @summary top up an account
     * @param {string} id
     * @param {AccountTopUpRequestDTO} accountTopUpRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    api3ControllerTopUpAccount(
      id: string,
      accountTopUpRequestDTO: AccountTopUpRequestDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .api3ControllerTopUpAccount(id, accountTopUpRequestDTO, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * API3Api - object-oriented interface
 * @export
 * @class API3Api
 * @extends {BaseAPI}
 */
export class API3Api extends BaseAPI {
  /**
   * Claims a given charge
   * @summary claim a charge
   * @param {ChargeRequestDTO} chargeRequestDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerClaimCharge(
    chargeRequestDTO: ChargeRequestDTO,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerClaimCharge(chargeRequestDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Request firmware version
   * @summary request firmware version
   * @param {number} unitId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerGetCurrentFirmware(
    unitId: number,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerGetCurrentFirmware(unitId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Request to get all the locales
   * @summary get locales
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerGetLocales(
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerGetLocales(acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve user info from API3 based on the JWT token
   * @summary retrieve user info
   * @param {string} [include] Query param for auth
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerGetUser(
    include?: string,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerGetUser(include, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Request to store a new tariff or update an existing one
   * @summary store tariff
   * @param {TariffRequest} tariffRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerStoreTariff(
    tariffRequest: TariffRequest,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerStoreTariff(tariffRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Tops up the user\'s account
   * @summary top up an account
   * @param {string} id
   * @param {AccountTopUpRequestDTO} accountTopUpRequestDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof API3Api
   */
  public api3ControllerTopUpAccount(
    id: string,
    accountTopUpRequestDTO: AccountTopUpRequestDTO,
    options?: RawAxiosRequestConfig
  ) {
    return API3ApiFp(this.configuration)
      .api3ControllerTopUpAccount(id, accountTopUpRequestDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CarbonIntensityApi - axios parameter creator
 * @export
 */
export const CarbonIntensityApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve, half-hourly, forecast data that includes the provided date
     * @summary retrieve regional forecast for a 30minute window that includes the provided date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetCarbonIntensity30m: async (
      from: string,
      regionId: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('carbonControllerGetCarbonIntensity30m', 'from', from);
      // verify required parameter 'regionId' is not null or undefined
      assertParamExists(
        'carbonControllerGetCarbonIntensity30m',
        'regionId',
        regionId
      );
      const localVarPath = `/carbon/intensity/{from}/fw30m/regionid/{regionId}`
        .replace(`{${'from'}}`, encodeURIComponent(String(from)))
        .replace(`{${'regionId'}}`, encodeURIComponent(String(regionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve, half-hourly, forecast data 48 hours from provided date
     * @summary retrieve regional forecast 48 hours from date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {any} [timezone] Timezone in which to normalise date transformation, as an IANA timezone name. Defaults to Etc/UTC. See list of time zones here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetCarbonIntensity48h: async (
      from: string,
      regionId: number,
      timezone?: any,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('carbonControllerGetCarbonIntensity48h', 'from', from);
      // verify required parameter 'regionId' is not null or undefined
      assertParamExists(
        'carbonControllerGetCarbonIntensity48h',
        'regionId',
        regionId
      );
      const localVarPath = `/carbon/intensity/{from}/fw48h/regionid/{regionId}`
        .replace(`{${'from'}}`, encodeURIComponent(String(from)))
        .replace(`{${'regionId'}}`, encodeURIComponent(String(regionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (timezone !== undefined) {
        for (const [key, value] of Object.entries(timezone)) {
          localVarQueryParameter[key] = value;
        }
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns a list of DNO regions.
     * @summary get the list of DNO regions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetDnoRegions: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/carbon/regions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CarbonIntensityApi - functional programming interface
 * @export
 */
export const CarbonIntensityApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CarbonIntensityApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve, half-hourly, forecast data that includes the provided date
     * @summary retrieve regional forecast for a 30minute window that includes the provided date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async carbonControllerGetCarbonIntensity30m(
      from: string,
      regionId: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ForecastSnapshot>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.carbonControllerGetCarbonIntensity30m(
          from,
          regionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CarbonIntensityApi.carbonControllerGetCarbonIntensity30m'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve, half-hourly, forecast data 48 hours from provided date
     * @summary retrieve regional forecast 48 hours from date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {any} [timezone] Timezone in which to normalise date transformation, as an IANA timezone name. Defaults to Etc/UTC. See list of time zones here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async carbonControllerGetCarbonIntensity48h(
      from: string,
      regionId: number,
      timezone?: any,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Forecast>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.carbonControllerGetCarbonIntensity48h(
          from,
          regionId,
          timezone,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CarbonIntensityApi.carbonControllerGetCarbonIntensity48h'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Returns a list of DNO regions.
     * @summary get the list of DNO regions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async carbonControllerGetDnoRegions(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<DnoRegions>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.carbonControllerGetDnoRegions(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CarbonIntensityApi.carbonControllerGetDnoRegions'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CarbonIntensityApi - factory interface
 * @export
 */
export const CarbonIntensityApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CarbonIntensityApiFp(configuration);
  return {
    /**
     * Retrieve, half-hourly, forecast data that includes the provided date
     * @summary retrieve regional forecast for a 30minute window that includes the provided date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetCarbonIntensity30m(
      from: string,
      regionId: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ForecastSnapshot> {
      return localVarFp
        .carbonControllerGetCarbonIntensity30m(from, regionId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve, half-hourly, forecast data 48 hours from provided date
     * @summary retrieve regional forecast 48 hours from date
     * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
     * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
     * @param {any} [timezone] Timezone in which to normalise date transformation, as an IANA timezone name. Defaults to Etc/UTC. See list of time zones here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetCarbonIntensity48h(
      from: string,
      regionId: number,
      timezone?: any,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Forecast> {
      return localVarFp
        .carbonControllerGetCarbonIntensity48h(
          from,
          regionId,
          timezone,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns a list of DNO regions.
     * @summary get the list of DNO regions
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    carbonControllerGetDnoRegions(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DnoRegions> {
      return localVarFp
        .carbonControllerGetDnoRegions(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CarbonIntensityApi - object-oriented interface
 * @export
 * @class CarbonIntensityApi
 * @extends {BaseAPI}
 */
export class CarbonIntensityApi extends BaseAPI {
  /**
   * Retrieve, half-hourly, forecast data that includes the provided date
   * @summary retrieve regional forecast for a 30minute window that includes the provided date
   * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
   * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CarbonIntensityApi
   */
  public carbonControllerGetCarbonIntensity30m(
    from: string,
    regionId: number,
    options?: RawAxiosRequestConfig
  ) {
    return CarbonIntensityApiFp(this.configuration)
      .carbonControllerGetCarbonIntensity30m(from, regionId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve, half-hourly, forecast data 48 hours from provided date
   * @summary retrieve regional forecast 48 hours from date
   * @param {string} from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
   * @param {number} regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
   * @param {any} [timezone] Timezone in which to normalise date transformation, as an IANA timezone name. Defaults to Etc/UTC. See list of time zones here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CarbonIntensityApi
   */
  public carbonControllerGetCarbonIntensity48h(
    from: string,
    regionId: number,
    timezone?: any,
    options?: RawAxiosRequestConfig
  ) {
    return CarbonIntensityApiFp(this.configuration)
      .carbonControllerGetCarbonIntensity48h(from, regionId, timezone, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns a list of DNO regions.
   * @summary get the list of DNO regions
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CarbonIntensityApi
   */
  public carbonControllerGetDnoRegions(options?: RawAxiosRequestConfig) {
    return CarbonIntensityApiFp(this.configuration)
      .carbonControllerGetDnoRegions(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargersApi - axios parameter creator
 * @export
 */
export const ChargersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given PPID, create a charge override
     * @summary create charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {ChargeOverrideRequestDTO} chargeOverrideRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerCreateChargerOverrides: async (
      ppid: string,
      chargeOverrideRequestDTO: ChargeOverrideRequestDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'chargersControllerCreateChargerOverrides',
        'ppid',
        ppid
      );
      // verify required parameter 'chargeOverrideRequestDTO' is not null or undefined
      assertParamExists(
        'chargersControllerCreateChargerOverrides',
        'chargeOverrideRequestDTO',
        chargeOverrideRequestDTO
      );
      const localVarPath = `/chargers/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        chargeOverrideRequestDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, delete the charge override
     * @summary delete charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteChargeOverride: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerDeleteChargeOverride', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, delete the charger\'s flex enrolment
     * @summary delete a charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteFlexEnrolment: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerDeleteFlexEnrolment', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/flex-enrolment`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete a flex request for the given charger
     * @summary delete a flex request
     * @param {string} id ID of the flex event to opt out of
     * @param {any} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteFlexRequest: async (
      id: string,
      ppid: any,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('chargersControllerDeleteFlexRequest', 'id', id);
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerDeleteFlexRequest', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/flex-requests/{id}`
        .replace(`{${'id'}}`, encodeURIComponent(String(id)))
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and TariffId, delete the charger\'s tariff
     * @summary Delete tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteTariff: async (
      ppid: string,
      tariffId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerDeleteTariff', 'ppid', ppid);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists('chargersControllerDeleteTariff', 'tariffId', tariffId);
      const localVarPath = `/chargers/{ppid}/tariffs/{tariffId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'tariffId'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, retrieve the firmware
     * @summary retrieve firmware for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerFirmware: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetChargerFirmware', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/firmware`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, get information about the charger
     * @summary get model information for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerModelInfo: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetChargerModelInfo', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/model-info`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, get the charge override
     * @summary get charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerOverrides: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetChargerOverrides', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/charge-overrides`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, retrieve the charger
     * @summary retrieve tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerTariffs: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetChargerTariffs', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/tariffs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For the authenticated user, return all their chargers
     * @summary retrieve all chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargers: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/chargers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary retrieve charger region
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetDnoRegion: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetDnoRegion', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/dnoregion`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s flex enrolment
     * @summary retrieve charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetFlexEnrolment: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetFlexEnrolment', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/flex-enrolment`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s active flex requests
     * @summary retrieve a list of active flex requests for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetFlexRequests: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetFlexRequests', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/flex-requests`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PSL / PPID and authenticated user, return the charger\'s usage allowance
     * @summary retrieve charger\'s allowance based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetRestrictions: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetRestrictions', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/restrictions`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s solar preferences
     * @summary retrieve solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetSolarPreferences: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetSolarPreferences', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/solar/preferences`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s connectivity status and energy offer statuses
     * @summary retrieve charger\'s connectivity status and energy offer statuses based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {string} [userAgent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetStatus: async (
      ppid: string,
      userAgent?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerGetStatus', 'ppid', ppid);
      const localVarPath = `/chargers/{ppid}/connectivity-status`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (userAgent != null) {
        localVarHeaderParameter['user-agent'] = String(userAgent);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, set the charger\'s tariff
     * @summary set tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerSetChargerTariffs: async (
      ppid: string,
      setChargerTariffDto: SetChargerTariffDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerSetChargerTariffs', 'ppid', ppid);
      // verify required parameter 'setChargerTariffDto' is not null or undefined
      assertParamExists(
        'chargersControllerSetChargerTariffs',
        'setChargerTariffDto',
        setChargerTariffDto
      );
      const localVarPath = `/chargers/{ppid}/tariffs`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setChargerTariffDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and authenticated user, sets the charger\'s solar preferences
     * @summary sets solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {SolarPreferences} solarPreferences
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerSetSolarPreferences: async (
      ppid: string,
      solarPreferences: SolarPreferences,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerSetSolarPreferences', 'ppid', ppid);
      // verify required parameter 'solarPreferences' is not null or undefined
      assertParamExists(
        'chargersControllerSetSolarPreferences',
        'solarPreferences',
        solarPreferences
      );
      const localVarPath = `/chargers/{ppid}/solar/preferences`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        solarPreferences,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerUpdateChargerTariff: async (
      ppid: string,
      tariffId: string,
      setChargerTariffDto: SetChargerTariffDto,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('chargersControllerUpdateChargerTariff', 'ppid', ppid);
      // verify required parameter 'tariffId' is not null or undefined
      assertParamExists(
        'chargersControllerUpdateChargerTariff',
        'tariffId',
        tariffId
      );
      // verify required parameter 'setChargerTariffDto' is not null or undefined
      assertParamExists(
        'chargersControllerUpdateChargerTariff',
        'setChargerTariffDto',
        setChargerTariffDto
      );
      const localVarPath = `/chargers/{ppid}/tariffs/{tariffId}`
        .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
        .replace(`{${'tariffId'}}`, encodeURIComponent(String(tariffId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setChargerTariffDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PSL / PPID of an arch5 charger, return the wifi credentials.
     * @summary retrieve wifi credentials of arch5 charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    ssidPasswordControllerGetWifiCredentials: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'ssidPasswordControllerGetWifiCredentials',
        'ppid',
        ppid
      );
      const localVarPath = `/chargers/arch5/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargersApi - functional programming interface
 * @export
 */
export const ChargersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ChargersApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given PPID, create a charge override
     * @summary create charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {ChargeOverrideRequestDTO} chargeOverrideRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerCreateChargerOverrides(
      ppid: string,
      chargeOverrideRequestDTO: ChargeOverrideRequestDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargeOverrideScheduleResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerCreateChargerOverrides(
          ppid,
          chargeOverrideRequestDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerCreateChargerOverrides'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, delete the charge override
     * @summary delete charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerDeleteChargeOverride(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerDeleteChargeOverride(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerDeleteChargeOverride'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, delete the charger\'s flex enrolment
     * @summary delete a charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerDeleteFlexEnrolment(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerDeleteFlexEnrolment(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerDeleteFlexEnrolment'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete a flex request for the given charger
     * @summary delete a flex request
     * @param {string} id ID of the flex event to opt out of
     * @param {any} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerDeleteFlexRequest(
      id: string,
      ppid: any,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerDeleteFlexRequest(
          id,
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerDeleteFlexRequest']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and TariffId, delete the charger\'s tariff
     * @summary Delete tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerDeleteTariff(
      ppid: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerDeleteTariff(
          ppid,
          tariffId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerDeleteTariff']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, retrieve the firmware
     * @summary retrieve firmware for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetChargerFirmware(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<FirmwareStatusResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetChargerFirmware(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerGetChargerFirmware'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, get information about the charger
     * @summary get model information for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetChargerModelInfo(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargerModelInfoResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetChargerModelInfo(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerGetChargerModelInfo'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, get the charge override
     * @summary get charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetChargerOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargeOverrideScheduleResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetChargerOverrides(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerGetChargerOverrides'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, retrieve the charger
     * @summary retrieve tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetChargerTariffs(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargingStationTariffSearchDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetChargerTariffs(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetChargerTariffs']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For the authenticated user, return all their chargers
     * @summary retrieve all chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetChargers(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargerResponse>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetChargers(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetChargers']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary retrieve charger region
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetDnoRegion(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Region>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetDnoRegion(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetDnoRegion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s flex enrolment
     * @summary retrieve charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetFlexEnrolment(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargerFlexEnrolment>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetFlexEnrolment(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetFlexEnrolment']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s active flex requests
     * @summary retrieve a list of active flex requests for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetFlexRequests(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<FlexRequest>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetFlexRequests(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetFlexRequests']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PSL / PPID and authenticated user, return the charger\'s usage allowance
     * @summary retrieve charger\'s allowance based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetRestrictions(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Restrictions>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetRestrictions(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetRestrictions']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s solar preferences
     * @summary retrieve solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetSolarPreferences(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SolarPreferences>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetSolarPreferences(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerGetSolarPreferences'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s connectivity status and energy offer statuses
     * @summary retrieve charger\'s connectivity status and energy offer statuses based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {string} [userAgent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerGetStatus(
      ppid: string,
      userAgent?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargerConnectivityStatus>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerGetStatus(
          ppid,
          userAgent,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerGetStatus']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, set the charger\'s tariff
     * @summary set tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerSetChargerTariffs(
      ppid: string,
      setChargerTariffDto: SetChargerTariffDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargerTariffDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerSetChargerTariffs(
          ppid,
          setChargerTariffDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargersApi.chargersControllerSetChargerTariffs']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and authenticated user, sets the charger\'s solar preferences
     * @summary sets solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {SolarPreferences} solarPreferences
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerSetSolarPreferences(
      ppid: string,
      solarPreferences: SolarPreferences,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SolarPreferences>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerSetSolarPreferences(
          ppid,
          solarPreferences,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerSetSolarPreferences'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargersControllerUpdateChargerTariff(
      ppid: string,
      tariffId: string,
      setChargerTariffDto: SetChargerTariffDto,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargerTariffDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargersControllerUpdateChargerTariff(
          ppid,
          tariffId,
          setChargerTariffDto,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.chargersControllerUpdateChargerTariff'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PSL / PPID of an arch5 charger, return the wifi credentials.
     * @summary retrieve wifi credentials of arch5 charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async ssidPasswordControllerGetWifiCredentials(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<WifiCredentials>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.ssidPasswordControllerGetWifiCredentials(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'ChargersApi.ssidPasswordControllerGetWifiCredentials'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargersApi - factory interface
 * @export
 */
export const ChargersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargersApiFp(configuration);
  return {
    /**
     * For a given PPID, create a charge override
     * @summary create charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {ChargeOverrideRequestDTO} chargeOverrideRequestDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerCreateChargerOverrides(
      ppid: string,
      chargeOverrideRequestDTO: ChargeOverrideRequestDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargeOverrideScheduleResponse>> {
      return localVarFp
        .chargersControllerCreateChargerOverrides(
          ppid,
          chargeOverrideRequestDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, delete the charge override
     * @summary delete charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteChargeOverride(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .chargersControllerDeleteChargeOverride(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, delete the charger\'s flex enrolment
     * @summary delete a charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteFlexEnrolment(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .chargersControllerDeleteFlexEnrolment(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete a flex request for the given charger
     * @summary delete a flex request
     * @param {string} id ID of the flex event to opt out of
     * @param {any} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteFlexRequest(
      id: string,
      ppid: any,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .chargersControllerDeleteFlexRequest(id, ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and TariffId, delete the charger\'s tariff
     * @summary Delete tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerDeleteTariff(
      ppid: string,
      tariffId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .chargersControllerDeleteTariff(ppid, tariffId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, retrieve the firmware
     * @summary retrieve firmware for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerFirmware(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FirmwareStatusResponse> {
      return localVarFp
        .chargersControllerGetChargerFirmware(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, get information about the charger
     * @summary get model information for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerModelInfo(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargerModelInfoResponse> {
      return localVarFp
        .chargersControllerGetChargerModelInfo(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, get the charge override
     * @summary get charge overrides for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerOverrides(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargeOverrideScheduleResponse>> {
      return localVarFp
        .chargersControllerGetChargerOverrides(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, retrieve the charger
     * @summary retrieve tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargerTariffs(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargingStationTariffSearchDto> {
      return localVarFp
        .chargersControllerGetChargerTariffs(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For the authenticated user, return all their chargers
     * @summary retrieve all chargers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetChargers(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargerResponse>> {
      return localVarFp
        .chargersControllerGetChargers(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PSL / PPID, return the associated DNO region id.
     * @summary retrieve charger region
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetDnoRegion(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Region> {
      return localVarFp
        .chargersControllerGetDnoRegion(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s flex enrolment
     * @summary retrieve charger\'s flex enrolment
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetFlexEnrolment(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargerFlexEnrolment> {
      return localVarFp
        .chargersControllerGetFlexEnrolment(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s active flex requests
     * @summary retrieve a list of active flex requests for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetFlexRequests(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<FlexRequest>> {
      return localVarFp
        .chargersControllerGetFlexRequests(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PSL / PPID and authenticated user, return the charger\'s usage allowance
     * @summary retrieve charger\'s allowance based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetRestrictions(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Restrictions> {
      return localVarFp
        .chargersControllerGetRestrictions(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s solar preferences
     * @summary retrieve solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetSolarPreferences(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SolarPreferences> {
      return localVarFp
        .chargersControllerGetSolarPreferences(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, return the charger\'s connectivity status and energy offer statuses
     * @summary retrieve charger\'s connectivity status and energy offer statuses based on authenticated user
     * @param {string} ppid PPID of a given charger
     * @param {string} [userAgent]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerGetStatus(
      ppid: string,
      userAgent?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargerConnectivityStatus> {
      return localVarFp
        .chargersControllerGetStatus(ppid, userAgent, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, set the charger\'s tariff
     * @summary set tariff for a given charger
     * @param {string} ppid PPID of a given charger
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerSetChargerTariffs(
      ppid: string,
      setChargerTariffDto: SetChargerTariffDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargerTariffDto> {
      return localVarFp
        .chargersControllerSetChargerTariffs(ppid, setChargerTariffDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and authenticated user, sets the charger\'s solar preferences
     * @summary sets solar preferences for this charger
     * @param {string} ppid PPID of a given charger
     * @param {SolarPreferences} solarPreferences
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerSetSolarPreferences(
      ppid: string,
      solarPreferences: SolarPreferences,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SolarPreferences> {
      return localVarFp
        .chargersControllerSetSolarPreferences(ppid, solarPreferences, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Given a PPID and tariff ID, replace the tariff details with the provided details
     * @summary
     * @param {string} ppid PPID of a given charger
     * @param {string} tariffId Id of a given tariff
     * @param {SetChargerTariffDto} setChargerTariffDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargersControllerUpdateChargerTariff(
      ppid: string,
      tariffId: string,
      setChargerTariffDto: SetChargerTariffDto,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargerTariffDto> {
      return localVarFp
        .chargersControllerUpdateChargerTariff(
          ppid,
          tariffId,
          setChargerTariffDto,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PSL / PPID of an arch5 charger, return the wifi credentials.
     * @summary retrieve wifi credentials of arch5 charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    ssidPasswordControllerGetWifiCredentials(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<WifiCredentials> {
      return localVarFp
        .ssidPasswordControllerGetWifiCredentials(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargersApi - object-oriented interface
 * @export
 * @class ChargersApi
 * @extends {BaseAPI}
 */
export class ChargersApi extends BaseAPI {
  /**
   * For a given PPID, create a charge override
   * @summary create charge overrides for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {ChargeOverrideRequestDTO} chargeOverrideRequestDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerCreateChargerOverrides(
    ppid: string,
    chargeOverrideRequestDTO: ChargeOverrideRequestDTO,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerCreateChargerOverrides(
        ppid,
        chargeOverrideRequestDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, delete the charge override
   * @summary delete charge overrides for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerDeleteChargeOverride(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerDeleteChargeOverride(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, delete the charger\'s flex enrolment
   * @summary delete a charger\'s flex enrolment
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerDeleteFlexEnrolment(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerDeleteFlexEnrolment(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete a flex request for the given charger
   * @summary delete a flex request
   * @param {string} id ID of the flex event to opt out of
   * @param {any} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerDeleteFlexRequest(
    id: string,
    ppid: any,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerDeleteFlexRequest(id, ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and TariffId, delete the charger\'s tariff
   * @summary Delete tariff for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {string} tariffId Id of a given tariff
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerDeleteTariff(
    ppid: string,
    tariffId: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerDeleteTariff(ppid, tariffId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, retrieve the firmware
   * @summary retrieve firmware for a given charger
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetChargerFirmware(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetChargerFirmware(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, get information about the charger
   * @summary get model information for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetChargerModelInfo(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetChargerModelInfo(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, get the charge override
   * @summary get charge overrides for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetChargerOverrides(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetChargerOverrides(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, retrieve the charger
   * @summary retrieve tariff for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetChargerTariffs(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetChargerTariffs(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For the authenticated user, return all their chargers
   * @summary retrieve all chargers
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetChargers(options?: RawAxiosRequestConfig) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetChargers(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PSL / PPID, return the associated DNO region id.
   * @summary retrieve charger region
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetDnoRegion(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetDnoRegion(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, return the charger\'s flex enrolment
   * @summary retrieve charger\'s flex enrolment
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetFlexEnrolment(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetFlexEnrolment(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, return the charger\'s active flex requests
   * @summary retrieve a list of active flex requests for this charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetFlexRequests(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetFlexRequests(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PSL / PPID and authenticated user, return the charger\'s usage allowance
   * @summary retrieve charger\'s allowance based on authenticated user
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetRestrictions(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetRestrictions(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, return the charger\'s solar preferences
   * @summary retrieve solar preferences for this charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetSolarPreferences(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetSolarPreferences(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, return the charger\'s connectivity status and energy offer statuses
   * @summary retrieve charger\'s connectivity status and energy offer statuses based on authenticated user
   * @param {string} ppid PPID of a given charger
   * @param {string} [userAgent]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerGetStatus(
    ppid: string,
    userAgent?: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerGetStatus(ppid, userAgent, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, set the charger\'s tariff
   * @summary set tariff for a given charger
   * @param {string} ppid PPID of a given charger
   * @param {SetChargerTariffDto} setChargerTariffDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerSetChargerTariffs(
    ppid: string,
    setChargerTariffDto: SetChargerTariffDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerSetChargerTariffs(ppid, setChargerTariffDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and authenticated user, sets the charger\'s solar preferences
   * @summary sets solar preferences for this charger
   * @param {string} ppid PPID of a given charger
   * @param {SolarPreferences} solarPreferences
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerSetSolarPreferences(
    ppid: string,
    solarPreferences: SolarPreferences,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerSetSolarPreferences(ppid, solarPreferences, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Given a PPID and tariff ID, replace the tariff details with the provided details
   * @summary
   * @param {string} ppid PPID of a given charger
   * @param {string} tariffId Id of a given tariff
   * @param {SetChargerTariffDto} setChargerTariffDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public chargersControllerUpdateChargerTariff(
    ppid: string,
    tariffId: string,
    setChargerTariffDto: SetChargerTariffDto,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .chargersControllerUpdateChargerTariff(
        ppid,
        tariffId,
        setChargerTariffDto,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PSL / PPID of an arch5 charger, return the wifi credentials.
   * @summary retrieve wifi credentials of arch5 charger
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargersApi
   */
  public ssidPasswordControllerGetWifiCredentials(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargersApiFp(this.configuration)
      .ssidPasswordControllerGetWifiCredentials(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ChargesApi - axios parameter creator
 * @export
 */
export const ChargesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For given dates it should return charger sessions.
     * @summary retrieve charger sessions
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargesControllerGetCharges: async (
      from: string,
      to: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'from' is not null or undefined
      assertParamExists('chargesControllerGetCharges', 'from', from);
      // verify required parameter 'to' is not null or undefined
      assertParamExists('chargesControllerGetCharges', 'to', to);
      const localVarPath = `/charges`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (from !== undefined) {
        localVarQueryParameter['from'] = from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] = to;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For given dates and inteval it should return charger stats.
     * @summary retrieve charger stats
     * @param {ChargesControllerGetChargesStatsIntervalEnum} interval Time duration interval data should be provided in.
     * @param {string} [from] Statistics report inclusive start date eg: 2022-01-01
     * @param {string} [to] Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargesControllerGetChargesStats: async (
      interval: ChargesControllerGetChargesStatsIntervalEnum,
      from?: string,
      to?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'interval' is not null or undefined
      assertParamExists(
        'chargesControllerGetChargesStats',
        'interval',
        interval
      );
      const localVarPath = `/charges/stats`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (from !== undefined) {
        localVarQueryParameter['from'] = from;
      }

      if (to !== undefined) {
        localVarQueryParameter['to'] = to;
      }

      if (interval !== undefined) {
        localVarQueryParameter['interval'] = interval;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ChargesApi - functional programming interface
 * @export
 */
export const ChargesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ChargesApiAxiosParamCreator(configuration);
  return {
    /**
     * For given dates it should return charger sessions.
     * @summary retrieve charger sessions
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargesControllerGetCharges(
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargesControllerGetCharges(
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargesApi.chargesControllerGetCharges']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For given dates and inteval it should return charger stats.
     * @summary retrieve charger stats
     * @param {ChargesControllerGetChargesStatsIntervalEnum} interval Time duration interval data should be provided in.
     * @param {string} [from] Statistics report inclusive start date eg: 2022-01-01
     * @param {string} [to] Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async chargesControllerGetChargesStats(
      interval: ChargesControllerGetChargesStatsIntervalEnum,
      from?: string,
      to?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ChargeStatsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.chargesControllerGetChargesStats(
          interval,
          from,
          to,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ChargesApi.chargesControllerGetChargesStats']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ChargesApi - factory interface
 * @export
 */
export const ChargesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ChargesApiFp(configuration);
  return {
    /**
     * For given dates it should return charger sessions.
     * @summary retrieve charger sessions
     * @param {string} from Statistics report inclusive start date eg: 2022-01-01
     * @param {string} to Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargesControllerGetCharges(
      from: string,
      to: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargesResponse> {
      return localVarFp
        .chargesControllerGetCharges(from, to, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For given dates and inteval it should return charger stats.
     * @summary retrieve charger stats
     * @param {ChargesControllerGetChargesStatsIntervalEnum} interval Time duration interval data should be provided in.
     * @param {string} [from] Statistics report inclusive start date eg: 2022-01-01
     * @param {string} [to] Statistics report inclusive end date eg: 2022-01-02
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    chargesControllerGetChargesStats(
      interval: ChargesControllerGetChargesStatsIntervalEnum,
      from?: string,
      to?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ChargeStatsResponse> {
      return localVarFp
        .chargesControllerGetChargesStats(interval, from, to, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ChargesApi - object-oriented interface
 * @export
 * @class ChargesApi
 * @extends {BaseAPI}
 */
export class ChargesApi extends BaseAPI {
  /**
   * For given dates it should return charger sessions.
   * @summary retrieve charger sessions
   * @param {string} from Statistics report inclusive start date eg: 2022-01-01
   * @param {string} to Statistics report inclusive end date eg: 2022-01-02
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargesApi
   */
  public chargesControllerGetCharges(
    from: string,
    to: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargesApiFp(this.configuration)
      .chargesControllerGetCharges(from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For given dates and inteval it should return charger stats.
   * @summary retrieve charger stats
   * @param {ChargesControllerGetChargesStatsIntervalEnum} interval Time duration interval data should be provided in.
   * @param {string} [from] Statistics report inclusive start date eg: 2022-01-01
   * @param {string} [to] Statistics report inclusive end date eg: 2022-01-02
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChargesApi
   */
  public chargesControllerGetChargesStats(
    interval: ChargesControllerGetChargesStatsIntervalEnum,
    from?: string,
    to?: string,
    options?: RawAxiosRequestConfig
  ) {
    return ChargesApiFp(this.configuration)
      .chargesControllerGetChargesStats(interval, from, to, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ChargesControllerGetChargesStatsIntervalEnum = {
  Day: 'day',
  Week: 'week',
  Month: 'month',
} as const;
export type ChargesControllerGetChargesStatsIntervalEnum =
  (typeof ChargesControllerGetChargesStatsIntervalEnum)[keyof typeof ChargesControllerGetChargesStatsIntervalEnum];

/**
 * CheckForUpgradeApi - axios parameter creator
 * @export
 */
export const CheckForUpgradeApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkForUpgradeControllerCheckForUpgrade: async (
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'appName' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appName',
        appName
      );
      // verify required parameter 'appVersion' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appVersion',
        appVersion
      );
      // verify required parameter 'platform' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'platform',
        platform
      );
      // verify required parameter 'environment' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'environment',
        environment
      );
      // verify required parameter 'appLanguage' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appLanguage',
        appLanguage
      );
      const localVarPath = `/check-for-upgrade`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (appName !== undefined) {
        localVarQueryParameter['app_name'] = appName;
      }

      if (appVersion !== undefined) {
        localVarQueryParameter['app_version'] = appVersion;
      }

      if (platform !== undefined) {
        localVarQueryParameter['platform'] = platform;
      }

      if (environment !== undefined) {
        localVarQueryParameter['environment'] = environment;
      }

      if (appLanguage !== undefined) {
        localVarQueryParameter['app_language'] = appLanguage;
      }

      if (xApiKey != null) {
        localVarHeaderParameter['x-api-key'] = String(xApiKey);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CheckForUpgradeApi - functional programming interface
 * @export
 */
export const CheckForUpgradeApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CheckForUpgradeApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async checkForUpgradeControllerCheckForUpgrade(
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CheckForUpgrade>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.checkForUpgradeControllerCheckForUpgrade(
          appName,
          appVersion,
          platform,
          environment,
          appLanguage,
          xApiKey,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CheckForUpgradeApi.checkForUpgradeControllerCheckForUpgrade'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CheckForUpgradeApi - factory interface
 * @export
 */
export const CheckForUpgradeApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CheckForUpgradeApiFp(configuration);
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkForUpgradeControllerCheckForUpgrade(
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CheckForUpgrade> {
      return localVarFp
        .checkForUpgradeControllerCheckForUpgrade(
          appName,
          appVersion,
          platform,
          environment,
          appLanguage,
          xApiKey,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CheckForUpgradeApi - object-oriented interface
 * @export
 * @class CheckForUpgradeApi
 * @extends {BaseAPI}
 */
export class CheckForUpgradeApi extends BaseAPI {
  /**
   * For a given version number returns whether a new update is available and upgrade is required
   * @summary returns whether an app version needs to be upgraded or not
   * @param {string} appName The app name
   * @param {string} appVersion The app version
   * @param {string} platform The app platform
   * @param {string} environment The app environment
   * @param {string} appLanguage The app language
   * @param {string} [xApiKey] x-api-key for your project
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CheckForUpgradeApi
   */
  public checkForUpgradeControllerCheckForUpgrade(
    appName: string,
    appVersion: string,
    platform: string,
    environment: string,
    appLanguage: string,
    xApiKey?: string,
    options?: RawAxiosRequestConfig
  ) {
    return CheckForUpgradeApiFp(this.configuration)
      .checkForUpgradeControllerCheckForUpgrade(
        appName,
        appVersion,
        platform,
        environment,
        appLanguage,
        xApiKey,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DriverAccountApi - axios parameter creator
 * @export
 */
export const DriverAccountApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {CreateUserPayload} createUserPayload
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerCreateNewUser: async (
      createUserPayload: CreateUserPayload,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createUserPayload' is not null or undefined
      assertParamExists(
        'accountClientControllerCreateNewUser',
        'createUserPayload',
        createUserPayload
      );
      const localVarPath = `/auth/users`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createUserPayload,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Deletes the user account
     * @summary deletes the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerDeleteUser: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/auth/user`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerGetTelephoneCodes: async (
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/auth/telephone-codes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerRecoverFactor: async (
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendRecoverFactorRequest' is not null or undefined
      assertParamExists(
        'accountClientControllerRecoverFactor',
        'sendRecoverFactorRequest',
        sendRecoverFactorRequest
      );
      const localVarPath = `/auth/recover-factor`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendRecoverFactorRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendEmailVerificationRequest: async (
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/auth/email-verification`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendPasswordResetRequest: async (
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'resetPasswordRequestDto' is not null or undefined
      assertParamExists(
        'accountClientControllerSendPasswordResetRequest',
        'resetPasswordRequestDto',
        resetPasswordRequestDto
      );
      const localVarPath = `/auth/password-reset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        resetPasswordRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendVerifyAndChangeEmail: async (
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendVerifyAndChangeEmailRequest' is not null or undefined
      assertParamExists(
        'accountClientControllerSendVerifyAndChangeEmail',
        'sendVerifyAndChangeEmailRequest',
        sendVerifyAndChangeEmailRequest
      );
      const localVarPath = `/auth/verify-and-change-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendVerifyAndChangeEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For the given email, send a magic link
     * @summary sends a magic link to the given email
     * @param {SignInWithMagicLinkDto} signInWithMagicLinkDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSignInWithMagicLink: async (
      signInWithMagicLinkDto: SignInWithMagicLinkDto,
      xAppName?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'signInWithMagicLinkDto' is not null or undefined
      assertParamExists(
        'accountClientControllerSignInWithMagicLink',
        'signInWithMagicLinkDto',
        signInWithMagicLinkDto
      );
      const localVarPath = `/auth/sign-in-with-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (xAppName != null) {
        localVarHeaderParameter['x-app-name'] = String(xAppName);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        signInWithMagicLinkDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * proxy call the call to driver account api
     * @summary Proxy update user
     * @param {UpdateUserDto} updateUserDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerUpdateUser: async (
      updateUserDto: UpdateUserDto,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'updateUserDto' is not null or undefined
      assertParamExists(
        'accountClientControllerUpdateUser',
        'updateUserDto',
        updateUserDto
      );
      const localVarPath = `/auth/user`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateUserDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DriverAccountApi - functional programming interface
 * @export
 */
export const DriverAccountApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    DriverAccountApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {CreateUserPayload} createUserPayload
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerCreateNewUser(
      createUserPayload: CreateUserPayload,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateUserResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerCreateNewUser(
          createUserPayload,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerCreateNewUser'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Deletes the user account
     * @summary deletes the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerDeleteUser(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerDeleteUser(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerDeleteUser'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerGetTelephoneCodes(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerGetTelephoneCodes(
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerGetTelephoneCodes'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerRecoverFactor(
          sendRecoverFactorRequest,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerRecoverFactor'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerSendEmailVerificationRequest(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerSendEmailVerificationRequest(
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerSendEmailVerificationRequest'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerSendPasswordResetRequest(
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerSendPasswordResetRequest(
          resetPasswordRequestDto,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerSendPasswordResetRequest'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerSendVerifyAndChangeEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerSendVerifyAndChangeEmail(
          sendVerifyAndChangeEmailRequest,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerSendVerifyAndChangeEmail'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For the given email, send a magic link
     * @summary sends a magic link to the given email
     * @param {SignInWithMagicLinkDto} signInWithMagicLinkDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerSignInWithMagicLink(
      signInWithMagicLinkDto: SignInWithMagicLinkDto,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerSignInWithMagicLink(
          signInWithMagicLinkDto,
          xAppName,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerSignInWithMagicLink'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * proxy call the call to driver account api
     * @summary Proxy update user
     * @param {UpdateUserDto} updateUserDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountClientControllerUpdateUser(
      updateUserDto: UpdateUserDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserDetailsDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountClientControllerUpdateUser(
          updateUserDto,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'DriverAccountApi.accountClientControllerUpdateUser'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DriverAccountApi - factory interface
 * @export
 */
export const DriverAccountApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = DriverAccountApiFp(configuration);
  return {
    /**
     * Creates a new user account
     * @summary create a new user
     * @param {CreateUserPayload} createUserPayload
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerCreateNewUser(
      createUserPayload: CreateUserPayload,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateUserResponseDto> {
      return localVarFp
        .accountClientControllerCreateNewUser(
          createUserPayload,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Deletes the user account
     * @summary deletes the user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerDeleteUser(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<object> {
      return localVarFp
        .accountClientControllerDeleteUser(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerGetTelephoneCodes(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerGetTelephoneCodes(acceptLanguage, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerRecoverFactor(
          sendRecoverFactorRequest,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendEmailVerificationRequest(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerSendEmailVerificationRequest(
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendPasswordResetRequest(
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerSendPasswordResetRequest(
          resetPasswordRequestDto,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSendVerifyAndChangeEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerSendVerifyAndChangeEmail(
          sendVerifyAndChangeEmailRequest,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * For the given email, send a magic link
     * @summary sends a magic link to the given email
     * @param {SignInWithMagicLinkDto} signInWithMagicLinkDto
     * @param {string} [xAppName] The name of the requesting app
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerSignInWithMagicLink(
      signInWithMagicLinkDto: SignInWithMagicLinkDto,
      xAppName?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountClientControllerSignInWithMagicLink(
          signInWithMagicLinkDto,
          xAppName,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * proxy call the call to driver account api
     * @summary Proxy update user
     * @param {UpdateUserDto} updateUserDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountClientControllerUpdateUser(
      updateUserDto: UpdateUserDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserDetailsDto> {
      return localVarFp
        .accountClientControllerUpdateUser(
          updateUserDto,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DriverAccountApi - object-oriented interface
 * @export
 * @class DriverAccountApi
 * @extends {BaseAPI}
 */
export class DriverAccountApi extends BaseAPI {
  /**
   * Creates a new user account
   * @summary create a new user
   * @param {CreateUserPayload} createUserPayload
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerCreateNewUser(
    createUserPayload: CreateUserPayload,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerCreateNewUser(
        createUserPayload,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Deletes the user account
   * @summary deletes the user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerDeleteUser(options?: RawAxiosRequestConfig) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerDeleteUser(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieves available telephone codes specified by the Firebase configuration
   * @summary retrieve available telephone codes
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerGetTelephoneCodes(
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerGetTelephoneCodes(acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends an email to the user to recover their 2 factor authentication
   * @summary Recover 2 factor autentication for user
   * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerRecoverFactor(
        sendRecoverFactorRequest,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends an email verification email to the user\'s email address
   * @summary verify a user\'s email address
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerSendEmailVerificationRequest(
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerSendEmailVerificationRequest(
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a reset password email to the user\'s email address
   * @summary reset a user\'s password
   * @param {ResetPasswordRequestDto} resetPasswordRequestDto
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerSendPasswordResetRequest(
    resetPasswordRequestDto: ResetPasswordRequestDto,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerSendPasswordResetRequest(
        resetPasswordRequestDto,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
   * @summary changes a user\'s email address
   * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerSendVerifyAndChangeEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerSendVerifyAndChangeEmail(
        sendVerifyAndChangeEmailRequest,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For the given email, send a magic link
   * @summary sends a magic link to the given email
   * @param {SignInWithMagicLinkDto} signInWithMagicLinkDto
   * @param {string} [xAppName] The name of the requesting app
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerSignInWithMagicLink(
    signInWithMagicLinkDto: SignInWithMagicLinkDto,
    xAppName?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerSignInWithMagicLink(
        signInWithMagicLinkDto,
        xAppName,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * proxy call the call to driver account api
   * @summary Proxy update user
   * @param {UpdateUserDto} updateUserDto
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DriverAccountApi
   */
  public accountClientControllerUpdateUser(
    updateUserDto: UpdateUserDto,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return DriverAccountApiFp(this.configuration)
      .accountClientControllerUpdateUser(updateUserDto, acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * EnergyApi - axios parameter creator
 * @export
 */
export const EnergyApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get a list of suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    energyControllerGetSuppliers: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/energy/suppliers`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * EnergyApi - functional programming interface
 * @export
 */
export const EnergyApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = EnergyApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get a list of suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async energyControllerGetSuppliers(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<SupplierDtoImpl>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.energyControllerGetSuppliers(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['EnergyApi.energyControllerGetSuppliers']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * EnergyApi - factory interface
 * @export
 */
export const EnergyApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = EnergyApiFp(configuration);
  return {
    /**
     *
     * @summary get a list of suppliers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    energyControllerGetSuppliers(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<SupplierDtoImpl>> {
      return localVarFp
        .energyControllerGetSuppliers(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * EnergyApi - object-oriented interface
 * @export
 * @class EnergyApi
 * @extends {BaseAPI}
 */
export class EnergyApi extends BaseAPI {
  /**
   *
   * @summary get a list of suppliers
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof EnergyApi
   */
  public energyControllerGetSuppliers(options?: RawAxiosRequestConfig) {
    return EnergyApiFp(this.configuration)
      .energyControllerGetSuppliers(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExampleApi - axios parameter creator
 * @export
 */
export const ExampleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExampleApi - functional programming interface
 * @export
 */
export const ExampleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ExampleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.helloControllerGetData(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExampleApi.helloControllerGetData']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExampleApi - factory interface
 * @export
 */
export const ExampleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ExampleApiFp(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .helloControllerGetData(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExampleApi - object-oriented interface
 * @export
 * @class ExampleApi
 * @extends {BaseAPI}
 */
export class ExampleApi extends BaseAPI {
  /**
   *
   * @summary welcome message endpoint
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExampleApi
   */
  public helloControllerGetData(options?: RawAxiosRequestConfig) {
    return ExampleApiFp(this.configuration)
      .helloControllerGetData(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExpensesApi - axios parameter creator
 * @export
 */
export const ExpensesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submitting a list of charges as expenses for a driver within a group.
     * @param {number} organisationId
     * @param {ExpensesRequestBody} expensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expensesControllerExpenseChargeTo: async (
      organisationId: number,
      expensesRequestBody: ExpensesRequestBody,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'organisationId' is not null or undefined
      assertParamExists(
        'expensesControllerExpenseChargeTo',
        'organisationId',
        organisationId
      );
      // verify required parameter 'expensesRequestBody' is not null or undefined
      assertParamExists(
        'expensesControllerExpenseChargeTo',
        'expensesRequestBody',
        expensesRequestBody
      );
      const localVarPath = `/expenses/groups/{organisationId}`.replace(
        `{${'organisationId'}}`,
        encodeURIComponent(String(organisationId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        expensesRequestBody,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExpensesApi - functional programming interface
 * @export
 */
export const ExpensesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ExpensesApiAxiosParamCreator(configuration);
  return {
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submitting a list of charges as expenses for a driver within a group.
     * @param {number} organisationId
     * @param {ExpensesRequestBody} expensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async expensesControllerExpenseChargeTo(
      organisationId: number,
      expensesRequestBody: ExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ExpensesResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.expensesControllerExpenseChargeTo(
          organisationId,
          expensesRequestBody,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExpensesApi.expensesControllerExpenseChargeTo']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExpensesApi - factory interface
 * @export
 */
export const ExpensesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ExpensesApiFp(configuration);
  return {
    /**
     * Submitting a list of charges as expenses for a driver within a group.
     * @summary Submitting a list of charges as expenses for a driver within a group.
     * @param {number} organisationId
     * @param {ExpensesRequestBody} expensesRequestBody
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    expensesControllerExpenseChargeTo(
      organisationId: number,
      expensesRequestBody: ExpensesRequestBody,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ExpensesResponse> {
      return localVarFp
        .expensesControllerExpenseChargeTo(
          organisationId,
          expensesRequestBody,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExpensesApi - object-oriented interface
 * @export
 * @class ExpensesApi
 * @extends {BaseAPI}
 */
export class ExpensesApi extends BaseAPI {
  /**
   * Submitting a list of charges as expenses for a driver within a group.
   * @summary Submitting a list of charges as expenses for a driver within a group.
   * @param {number} organisationId
   * @param {ExpensesRequestBody} expensesRequestBody
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpensesApi
   */
  public expensesControllerExpenseChargeTo(
    organisationId: number,
    expensesRequestBody: ExpensesRequestBody,
    options?: RawAxiosRequestConfig
  ) {
    return ExpensesApiFp(this.configuration)
      .expensesControllerExpenseChargeTo(
        organisationId,
        expensesRequestBody,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get mobile API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get mobile API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary get mobile API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary get mobile API health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * LinkyApi - axios parameter creator
 * @export
 */
export const LinkyApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkyControllerGetLinkyStatusForCharger: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'linkyControllerGetLinkyStatusForCharger',
        'ppid',
        ppid
      );
      const localVarPath = `/linky/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {SetLinkyDTO} setLinkyDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkyControllerSetLinkyStatusForCharger: async (
      ppid: string,
      setLinkyDTO: SetLinkyDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'linkyControllerSetLinkyStatusForCharger',
        'ppid',
        ppid
      );
      // verify required parameter 'setLinkyDTO' is not null or undefined
      assertParamExists(
        'linkyControllerSetLinkyStatusForCharger',
        'setLinkyDTO',
        setLinkyDTO
      );
      const localVarPath = `/linky/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        setLinkyDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * LinkyApi - functional programming interface
 * @export
 */
export const LinkyApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = LinkyApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async linkyControllerGetLinkyStatusForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<LinkyDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.linkyControllerGetLinkyStatusForCharger(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LinkyApi.linkyControllerGetLinkyStatusForCharger'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {SetLinkyDTO} setLinkyDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async linkyControllerSetLinkyStatusForCharger(
      ppid: string,
      setLinkyDTO: SetLinkyDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<LinkyDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.linkyControllerSetLinkyStatusForCharger(
          ppid,
          setLinkyDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'LinkyApi.linkyControllerSetLinkyStatusForCharger'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * LinkyApi - factory interface
 * @export
 */
export const LinkyApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = LinkyApiFp(configuration);
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkyControllerGetLinkyStatusForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<LinkyDTO> {
      return localVarFp
        .linkyControllerGetLinkyStatusForCharger(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {SetLinkyDTO} setLinkyDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    linkyControllerSetLinkyStatusForCharger(
      ppid: string,
      setLinkyDTO: SetLinkyDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<LinkyDTO> {
      return localVarFp
        .linkyControllerSetLinkyStatusForCharger(ppid, setLinkyDTO, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * LinkyApi - object-oriented interface
 * @export
 * @class LinkyApi
 * @extends {BaseAPI}
 */
export class LinkyApi extends BaseAPI {
  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LinkyApi
   */
  public linkyControllerGetLinkyStatusForCharger(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return LinkyApiFp(this.configuration)
      .linkyControllerGetLinkyStatusForCharger(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {SetLinkyDTO} setLinkyDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LinkyApi
   */
  public linkyControllerSetLinkyStatusForCharger(
    ppid: string,
    setLinkyDTO: SetLinkyDTO,
    options?: RawAxiosRequestConfig
  ) {
    return LinkyApiFp(this.configuration)
      .linkyControllerSetLinkyStatusForCharger(ppid, setLinkyDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * LocationApi - axios parameter creator
 * @export
 */
export const LocationApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Returns the full address details based on the Id.
     * @summary find the address
     * @param {string} id The ID from a search result to retrieve the details for.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    locationControllerFind: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('locationControllerFind', 'id', id);
      const localVarPath = `/location/find`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (id !== undefined) {
        localVarQueryParameter['id'] = id;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Partial address returned.
     * @summary search for an address
     * @param {string} text The search text to find. Ideally a postcode or the start of the address.
     * @param {string} acceptLanguage
     * @param {string} [containerId] A container for the search. This should only be another Id previously returned from this service when the Type of the result was not \&quot;Address\&quot;.
     * @param {string} [language] The preferred language for results. This should be a 2 or 4 character language code e.g. (en, fr, en-gb, en-us etc). Falls back to Accept-Language header if not set and defaults to \&quot;en\&quot; if neither are present.
     * @param {number} [limit] The maximum number of results to return.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    locationControllerSearch: async (
      text: string,
      acceptLanguage: string,
      containerId?: string,
      language?: string,
      limit?: number,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'text' is not null or undefined
      assertParamExists('locationControllerSearch', 'text', text);
      // verify required parameter 'acceptLanguage' is not null or undefined
      assertParamExists(
        'locationControllerSearch',
        'acceptLanguage',
        acceptLanguage
      );
      const localVarPath = `/location/search`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (text !== undefined) {
        localVarQueryParameter['text'] = text;
      }

      if (containerId !== undefined) {
        localVarQueryParameter['containerId'] = containerId;
      }

      if (language !== undefined) {
        localVarQueryParameter['language'] = language;
      }

      if (limit !== undefined) {
        localVarQueryParameter['limit'] = limit;
      }

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * LocationApi - functional programming interface
 * @export
 */
export const LocationApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = LocationApiAxiosParamCreator(configuration);
  return {
    /**
     * Returns the full address details based on the Id.
     * @summary find the address
     * @param {string} id The ID from a search result to retrieve the details for.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async locationControllerFind(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Address>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.locationControllerFind(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['LocationApi.locationControllerFind']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Partial address returned.
     * @summary search for an address
     * @param {string} text The search text to find. Ideally a postcode or the start of the address.
     * @param {string} acceptLanguage
     * @param {string} [containerId] A container for the search. This should only be another Id previously returned from this service when the Type of the result was not \&quot;Address\&quot;.
     * @param {string} [language] The preferred language for results. This should be a 2 or 4 character language code e.g. (en, fr, en-gb, en-us etc). Falls back to Accept-Language header if not set and defaults to \&quot;en\&quot; if neither are present.
     * @param {number} [limit] The maximum number of results to return.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async locationControllerSearch(
      text: string,
      acceptLanguage: string,
      containerId?: string,
      language?: string,
      limit?: number,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<Item>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.locationControllerSearch(
          text,
          acceptLanguage,
          containerId,
          language,
          limit,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['LocationApi.locationControllerSearch']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * LocationApi - factory interface
 * @export
 */
export const LocationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = LocationApiFp(configuration);
  return {
    /**
     * Returns the full address details based on the Id.
     * @summary find the address
     * @param {string} id The ID from a search result to retrieve the details for.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    locationControllerFind(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Address>> {
      return localVarFp
        .locationControllerFind(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Partial address returned.
     * @summary search for an address
     * @param {string} text The search text to find. Ideally a postcode or the start of the address.
     * @param {string} acceptLanguage
     * @param {string} [containerId] A container for the search. This should only be another Id previously returned from this service when the Type of the result was not \&quot;Address\&quot;.
     * @param {string} [language] The preferred language for results. This should be a 2 or 4 character language code e.g. (en, fr, en-gb, en-us etc). Falls back to Accept-Language header if not set and defaults to \&quot;en\&quot; if neither are present.
     * @param {number} [limit] The maximum number of results to return.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    locationControllerSearch(
      text: string,
      acceptLanguage: string,
      containerId?: string,
      language?: string,
      limit?: number,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<Item>> {
      return localVarFp
        .locationControllerSearch(
          text,
          acceptLanguage,
          containerId,
          language,
          limit,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * LocationApi - object-oriented interface
 * @export
 * @class LocationApi
 * @extends {BaseAPI}
 */
export class LocationApi extends BaseAPI {
  /**
   * Returns the full address details based on the Id.
   * @summary find the address
   * @param {string} id The ID from a search result to retrieve the details for.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationApi
   */
  public locationControllerFind(id: string, options?: RawAxiosRequestConfig) {
    return LocationApiFp(this.configuration)
      .locationControllerFind(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Partial address returned.
   * @summary search for an address
   * @param {string} text The search text to find. Ideally a postcode or the start of the address.
   * @param {string} acceptLanguage
   * @param {string} [containerId] A container for the search. This should only be another Id previously returned from this service when the Type of the result was not \&quot;Address\&quot;.
   * @param {string} [language] The preferred language for results. This should be a 2 or 4 character language code e.g. (en, fr, en-gb, en-us etc). Falls back to Accept-Language header if not set and defaults to \&quot;en\&quot; if neither are present.
   * @param {number} [limit] The maximum number of results to return.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof LocationApi
   */
  public locationControllerSearch(
    text: string,
    acceptLanguage: string,
    containerId?: string,
    language?: string,
    limit?: number,
    options?: RawAxiosRequestConfig
  ) {
    return LocationApiFp(this.configuration)
      .locationControllerSearch(
        text,
        acceptLanguage,
        containerId,
        language,
        limit,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * MarketingApi - axios parameter creator
 * @export
 */
export const MarketingApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given charger, get its marketing opportunties
     * @summary gets marketing opportunities for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    marketingControllerGetOpportunitiesForCharger: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'marketingControllerGetOpportunitiesForCharger',
        'ppid',
        ppid
      );
      const localVarPath = `/marketing/opportunities/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * MarketingApi - functional programming interface
 * @export
 */
export const MarketingApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    MarketingApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given charger, get its marketing opportunties
     * @summary gets marketing opportunities for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async marketingControllerGetOpportunitiesForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<MarketingOpportunitiesDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.marketingControllerGetOpportunitiesForCharger(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'MarketingApi.marketingControllerGetOpportunitiesForCharger'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * MarketingApi - factory interface
 * @export
 */
export const MarketingApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = MarketingApiFp(configuration);
  return {
    /**
     * For a given charger, get its marketing opportunties
     * @summary gets marketing opportunities for a given charger
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    marketingControllerGetOpportunitiesForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<MarketingOpportunitiesDTO> {
      return localVarFp
        .marketingControllerGetOpportunitiesForCharger(ppid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * MarketingApi - object-oriented interface
 * @export
 * @class MarketingApi
 * @extends {BaseAPI}
 */
export class MarketingApi extends BaseAPI {
  /**
   * For a given charger, get its marketing opportunties
   * @summary gets marketing opportunities for a given charger
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof MarketingApi
   */
  public marketingControllerGetOpportunitiesForCharger(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return MarketingApiFp(this.configuration)
      .marketingControllerGetOpportunitiesForCharger(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PaymentsApi - axios parameter creator
 * @export
 */
export const PaymentsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreatePaymentIntent: async (
      createPaymentRequest: CreatePaymentRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createPaymentRequest' is not null or undefined
      assertParamExists(
        'paymentControllerCreatePaymentIntent',
        'createPaymentRequest',
        createPaymentRequest
      );
      const localVarPath = `/payments/create-payment-intent`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createPaymentRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerSetupIntent: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/payments/setup-intent`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerWebhook: async (
      stripeSignature: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'stripeSignature' is not null or undefined
      assertParamExists(
        'paymentControllerWebhook',
        'stripeSignature',
        stripeSignature
      );
      const localVarPath = `/payments/webhook`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (stripeSignature != null) {
        localVarHeaderParameter['stripe-signature'] = String(stripeSignature);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PaymentsApi - functional programming interface
 * @export
 */
export const PaymentsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PaymentsApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerCreatePaymentIntent(
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateRegisteredUserPaymentResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerCreatePaymentIntent(
          createPaymentRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PaymentsApi.paymentControllerCreatePaymentIntent'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerSetupIntent(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateIntentResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerSetupIntent(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PaymentsApi.paymentControllerSetupIntent']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerWebhook(
          stripeSignature,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PaymentsApi.paymentControllerWebhook']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PaymentsApi - factory interface
 * @export
 */
export const PaymentsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PaymentsApiFp(configuration);
  return {
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreatePaymentIntent(
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateRegisteredUserPaymentResponse> {
      return localVarFp
        .paymentControllerCreatePaymentIntent(createPaymentRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerSetupIntent(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateIntentResponse> {
      return localVarFp
        .paymentControllerSetupIntent(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .paymentControllerWebhook(stripeSignature, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PaymentsApi - object-oriented interface
 * @export
 * @class PaymentsApi
 * @extends {BaseAPI}
 */
export class PaymentsApi extends BaseAPI {
  /**
   * Creates setup intent for payment
   * @summary create setup intent for payment
   * @param {CreatePaymentRequest} createPaymentRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerCreatePaymentIntent(
    createPaymentRequest: CreatePaymentRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerCreatePaymentIntent(createPaymentRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Creates setup intent for payment
   * @summary create setup intent for payment
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerSetupIntent(options?: RawAxiosRequestConfig) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerSetupIntent(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} stripeSignature
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerWebhook(
    stripeSignature: string,
    options?: RawAxiosRequestConfig
  ) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerWebhook(stripeSignature, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RemoteLockApi - axios parameter creator
 * @export
 */
export const RemoteLockApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    remoteLockControllerGetRemoteLockForCharger: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'remoteLockControllerGetRemoteLockForCharger',
        'ppid',
        ppid
      );
      const localVarPath = `/remote-lock/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {RemoteLockDTO} remoteLockDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    remoteLockControllerSetRemoteLockForCharger: async (
      ppid: string,
      remoteLockDTO: RemoteLockDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'remoteLockControllerSetRemoteLockForCharger',
        'ppid',
        ppid
      );
      // verify required parameter 'remoteLockDTO' is not null or undefined
      assertParamExists(
        'remoteLockControllerSetRemoteLockForCharger',
        'remoteLockDTO',
        remoteLockDTO
      );
      const localVarPath = `/remote-lock/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        remoteLockDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RemoteLockApi - functional programming interface
 * @export
 */
export const RemoteLockApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    RemoteLockApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async remoteLockControllerGetRemoteLockForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<RemoteLockDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.remoteLockControllerGetRemoteLockForCharger(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'RemoteLockApi.remoteLockControllerGetRemoteLockForCharger'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {RemoteLockDTO} remoteLockDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async remoteLockControllerSetRemoteLockForCharger(
      ppid: string,
      remoteLockDTO: RemoteLockDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<RemoteLockDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.remoteLockControllerSetRemoteLockForCharger(
          ppid,
          remoteLockDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'RemoteLockApi.remoteLockControllerSetRemoteLockForCharger'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RemoteLockApi - factory interface
 * @export
 */
export const RemoteLockApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RemoteLockApiFp(configuration);
  return {
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    remoteLockControllerGetRemoteLockForCharger(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<RemoteLockDTO> {
      return localVarFp
        .remoteLockControllerGetRemoteLockForCharger(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {RemoteLockDTO} remoteLockDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    remoteLockControllerSetRemoteLockForCharger(
      ppid: string,
      remoteLockDTO: RemoteLockDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<RemoteLockDTO> {
      return localVarFp
        .remoteLockControllerSetRemoteLockForCharger(
          ppid,
          remoteLockDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RemoteLockApi - object-oriented interface
 * @export
 * @class RemoteLockApi
 * @extends {BaseAPI}
 */
export class RemoteLockApi extends BaseAPI {
  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RemoteLockApi
   */
  public remoteLockControllerGetRemoteLockForCharger(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return RemoteLockApiFp(this.configuration)
      .remoteLockControllerGetRemoteLockForCharger(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {RemoteLockDTO} remoteLockDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RemoteLockApi
   */
  public remoteLockControllerSetRemoteLockForCharger(
    ppid: string,
    remoteLockDTO: RemoteLockDTO,
    options?: RawAxiosRequestConfig
  ) {
    return RemoteLockApiFp(this.configuration)
      .remoteLockControllerSetRemoteLockForCharger(ppid, remoteLockDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ReportsApi - axios parameter creator
 * @export
 */
export const ReportsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For given dates it should generate a report of charges in CSV format.
     * @summary Request a reports of charges
     * @param {ReportsDtoPayload} reportsDtoPayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    reportsControllerCreateReport: async (
      reportsDtoPayload: ReportsDtoPayload,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'reportsDtoPayload' is not null or undefined
      assertParamExists(
        'reportsControllerCreateReport',
        'reportsDtoPayload',
        reportsDtoPayload
      );
      const localVarPath = `/reports/charges`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        reportsDtoPayload,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ReportsApi - functional programming interface
 * @export
 */
export const ReportsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ReportsApiAxiosParamCreator(configuration);
  return {
    /**
     * For given dates it should generate a report of charges in CSV format.
     * @summary Request a reports of charges
     * @param {ReportsDtoPayload} reportsDtoPayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async reportsControllerCreateReport(
      reportsDtoPayload: ReportsDtoPayload,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.reportsControllerCreateReport(
          reportsDtoPayload,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ReportsApi.reportsControllerCreateReport']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ReportsApi - factory interface
 * @export
 */
export const ReportsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ReportsApiFp(configuration);
  return {
    /**
     * For given dates it should generate a report of charges in CSV format.
     * @summary Request a reports of charges
     * @param {ReportsDtoPayload} reportsDtoPayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    reportsControllerCreateReport(
      reportsDtoPayload: ReportsDtoPayload,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .reportsControllerCreateReport(reportsDtoPayload, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ReportsApi - object-oriented interface
 * @export
 * @class ReportsApi
 * @extends {BaseAPI}
 */
export class ReportsApi extends BaseAPI {
  /**
   * For given dates it should generate a report of charges in CSV format.
   * @summary Request a reports of charges
   * @param {ReportsDtoPayload} reportsDtoPayload
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ReportsApi
   */
  public reportsControllerCreateReport(
    reportsDtoPayload: ReportsDtoPayload,
    options?: RawAxiosRequestConfig
  ) {
    return ReportsApiFp(this.configuration)
      .reportsControllerCreateReport(reportsDtoPayload, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * RewardsApi - axios parameter creator
 * @export
 */
export const RewardsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Archive a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerArchiveAccount: async (
      bankAccountId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'bankAccountId' is not null or undefined
      assertParamExists(
        'rewardsControllerArchiveAccount',
        'bankAccountId',
        bankAccountId
      );
      const localVarPath = `/rewards/bank-accounts/{bankAccountId}`.replace(
        `{${'bankAccountId'}}`,
        encodeURIComponent(String(bankAccountId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a new rewards bank account for the authenticated user
     * @summary
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerCreateBankAccount: async (
      rewardsAccountDTO: RewardsAccountDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'rewardsAccountDTO' is not null or undefined
      assertParamExists(
        'rewardsControllerCreateBankAccount',
        'rewardsAccountDTO',
        rewardsAccountDTO
      );
      const localVarPath = `/rewards/bank-accounts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        rewardsAccountDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the user\'s bank accounts
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetBankAccounts: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/rewards/bank-accounts`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a list of rewards-related transactions the user has made
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetRewardsTransactions: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/rewards/transactions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Make a payout of the rewards balance to the user
     * @summary
     * @param {PayoutRequestDTOImpl} payoutRequestDTOImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerMakeRewardsPayout: async (
      payoutRequestDTOImpl: PayoutRequestDTOImpl,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'payoutRequestDTOImpl' is not null or undefined
      assertParamExists(
        'rewardsControllerMakeRewardsPayout',
        'payoutRequestDTOImpl',
        payoutRequestDTOImpl
      );
      const localVarPath = `/rewards/payout`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        payoutRequestDTOImpl,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {object} body
     * @param {string} [stripeSignature2] Used for verifying the request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerProxyWebhook: async (
      stripeSignature: string,
      body: object,
      stripeSignature2?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'stripeSignature' is not null or undefined
      assertParamExists(
        'rewardsControllerProxyWebhook',
        'stripeSignature',
        stripeSignature
      );
      // verify required parameter 'body' is not null or undefined
      assertParamExists('rewardsControllerProxyWebhook', 'body', body);
      const localVarPath = `/rewards/webhook`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (stripeSignature != null) {
        localVarHeaderParameter['stripe-signature'] = String(stripeSignature);
      }
      if (stripeSignature2 != null) {
        localVarHeaderParameter['Stripe-Signature'] = String(stripeSignature2);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerUpdateBankAccount: async (
      bankAccountId: string,
      rewardsAccountDTO: RewardsAccountDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'bankAccountId' is not null or undefined
      assertParamExists(
        'rewardsControllerUpdateBankAccount',
        'bankAccountId',
        bankAccountId
      );
      // verify required parameter 'rewardsAccountDTO' is not null or undefined
      assertParamExists(
        'rewardsControllerUpdateBankAccount',
        'rewardsAccountDTO',
        rewardsAccountDTO
      );
      const localVarPath = `/rewards/bank-accounts/{bankAccountId}`.replace(
        `{${'bankAccountId'}}`,
        encodeURIComponent(String(bankAccountId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        rewardsAccountDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * RewardsApi - functional programming interface
 * @export
 */
export const RewardsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = RewardsApiAxiosParamCreator(configuration);
  return {
    /**
     * Archive a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerArchiveAccount(
      bankAccountId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerArchiveAccount(
          bankAccountId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerArchiveAccount']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Create a new rewards bank account for the authenticated user
     * @summary
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerCreateBankAccount(
      rewardsAccountDTO: RewardsAccountDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<RewardsBankAccountDTOImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerCreateBankAccount(
          rewardsAccountDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerCreateBankAccount']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the user\'s bank accounts
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerGetBankAccounts(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<RewardsBankAccountDTOImpl>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerGetBankAccounts(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerGetBankAccounts']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a list of rewards-related transactions the user has made
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerGetRewardsTransactions(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<RewardsTransactionDTOImpl>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerGetRewardsTransactions(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'RewardsApi.rewardsControllerGetRewardsTransactions'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Make a payout of the rewards balance to the user
     * @summary
     * @param {PayoutRequestDTOImpl} payoutRequestDTOImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerMakeRewardsPayout(
      payoutRequestDTOImpl: PayoutRequestDTOImpl,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PayoutResponseDTOImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerMakeRewardsPayout(
          payoutRequestDTOImpl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerMakeRewardsPayout']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {object} body
     * @param {string} [stripeSignature2] Used for verifying the request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerProxyWebhook(
      stripeSignature: string,
      body: object,
      stripeSignature2?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerProxyWebhook(
          stripeSignature,
          body,
          stripeSignature2,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerProxyWebhook']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rewardsControllerUpdateBankAccount(
      bankAccountId: string,
      rewardsAccountDTO: RewardsAccountDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rewardsControllerUpdateBankAccount(
          bankAccountId,
          rewardsAccountDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['RewardsApi.rewardsControllerUpdateBankAccount']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * RewardsApi - factory interface
 * @export
 */
export const RewardsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = RewardsApiFp(configuration);
  return {
    /**
     * Archive a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerArchiveAccount(
      bankAccountId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .rewardsControllerArchiveAccount(bankAccountId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a new rewards bank account for the authenticated user
     * @summary
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerCreateBankAccount(
      rewardsAccountDTO: RewardsAccountDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<RewardsBankAccountDTOImpl> {
      return localVarFp
        .rewardsControllerCreateBankAccount(rewardsAccountDTO, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the user\'s bank accounts
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetBankAccounts(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<RewardsBankAccountDTOImpl>> {
      return localVarFp
        .rewardsControllerGetBankAccounts(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a list of rewards-related transactions the user has made
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerGetRewardsTransactions(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<RewardsTransactionDTOImpl>> {
      return localVarFp
        .rewardsControllerGetRewardsTransactions(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Make a payout of the rewards balance to the user
     * @summary
     * @param {PayoutRequestDTOImpl} payoutRequestDTOImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerMakeRewardsPayout(
      payoutRequestDTOImpl: PayoutRequestDTOImpl,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PayoutResponseDTOImpl> {
      return localVarFp
        .rewardsControllerMakeRewardsPayout(payoutRequestDTOImpl, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} stripeSignature
     * @param {object} body
     * @param {string} [stripeSignature2] Used for verifying the request
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerProxyWebhook(
      stripeSignature: string,
      body: object,
      stripeSignature2?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .rewardsControllerProxyWebhook(
          stripeSignature,
          body,
          stripeSignature2,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a users bank account
     * @summary
     * @param {string} bankAccountId The ID of the bank account
     * @param {RewardsAccountDTO} rewardsAccountDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rewardsControllerUpdateBankAccount(
      bankAccountId: string,
      rewardsAccountDTO: RewardsAccountDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .rewardsControllerUpdateBankAccount(
          bankAccountId,
          rewardsAccountDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * RewardsApi - object-oriented interface
 * @export
 * @class RewardsApi
 * @extends {BaseAPI}
 */
export class RewardsApi extends BaseAPI {
  /**
   * Archive a users bank account
   * @summary
   * @param {string} bankAccountId The ID of the bank account
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerArchiveAccount(
    bankAccountId: string,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerArchiveAccount(bankAccountId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a new rewards bank account for the authenticated user
   * @summary
   * @param {RewardsAccountDTO} rewardsAccountDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerCreateBankAccount(
    rewardsAccountDTO: RewardsAccountDTO,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerCreateBankAccount(rewardsAccountDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the user\'s bank accounts
   * @summary
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerGetBankAccounts(options?: RawAxiosRequestConfig) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerGetBankAccounts(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a list of rewards-related transactions the user has made
   * @summary
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerGetRewardsTransactions(
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerGetRewardsTransactions(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Make a payout of the rewards balance to the user
   * @summary
   * @param {PayoutRequestDTOImpl} payoutRequestDTOImpl
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerMakeRewardsPayout(
    payoutRequestDTOImpl: PayoutRequestDTOImpl,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerMakeRewardsPayout(payoutRequestDTOImpl, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} stripeSignature
   * @param {object} body
   * @param {string} [stripeSignature2] Used for verifying the request
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerProxyWebhook(
    stripeSignature: string,
    body: object,
    stripeSignature2?: string,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerProxyWebhook(
        stripeSignature,
        body,
        stripeSignature2,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a users bank account
   * @summary
   * @param {string} bankAccountId The ID of the bank account
   * @param {RewardsAccountDTO} rewardsAccountDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof RewardsApi
   */
  public rewardsControllerUpdateBankAccount(
    bankAccountId: string,
    rewardsAccountDTO: RewardsAccountDTO,
    options?: RawAxiosRequestConfig
  ) {
    return RewardsApiFp(this.configuration)
      .rewardsControllerUpdateBankAccount(
        bankAccountId,
        rewardsAccountDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SmartChargingApi - axios parameter creator
 * @export
 */
export const SmartChargingApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given PPID, capture a vehicle for smart charging
     * @summary capture a vehicle for smart charging
     * @param {string} ppid PPID of a given charger
     * @param {VehicleLinkRequestDtoImpl} vehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerCaptureVehicle: async (
      ppid: string,
      vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('smartChargingControllerCaptureVehicle', 'ppid', ppid);
      // verify required parameter 'vehicleLinkRequestDtoImpl' is not null or undefined
      assertParamExists(
        'smartChargingControllerCaptureVehicle',
        'vehicleLinkRequestDtoImpl',
        vehicleLinkRequestDtoImpl
      );
      const localVarPath =
        `/smart-charging/delegated-controls/{ppid}/vehicles`.replace(
          `{${'ppid'}}`,
          encodeURIComponent(String(ppid))
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        vehicleLinkRequestDtoImpl,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerCreateDelegatedControlStation: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'smartChargingControllerCreateDelegatedControlStation',
        'ppid',
        ppid
      );
      const localVarPath = `/smart-charging/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID, remove a given vehicle
     * @summary Removes a given vehicle from a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerDeleteVehicle: async (
      ppid: string,
      vehicleId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('smartChargingControllerDeleteVehicle', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'smartChargingControllerDeleteVehicle',
        'vehicleId',
        vehicleId
      );
      const localVarPath =
        `/smart-charging/delegated-controls/{ppid}/vehicles/{vehicleId}`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given user, get all chargers and the vehicles linked to them
     * @summary retrieve all vehicles and chargers for a user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerGetChargersAndVehicles: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/smart-charging/delegated-controls/vehicles`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID retrieve intents
     * @summary retrieve intents for a charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerGetIntents: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('smartChargingControllerGetIntents', 'ppid', ppid);
      const localVarPath = `/smart-charging/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerRemoveChargerFromDelegatedControl: async (
      ppid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'smartChargingControllerRemoveChargerFromDelegatedControl',
        'ppid',
        ppid
      );
      const localVarPath = `/smart-charging/delegated-controls/{ppid}`.replace(
        `{${'ppid'}}`,
        encodeURIComponent(String(ppid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given PPID and vehicle, set delegated control intents
     * @summary Sets delegated control intents of a given charger and vehicle
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId
     * @param {VehicleIntentsRequestDtoImpl} vehicleIntentsRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerSetVehicleIntents: async (
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists(
        'smartChargingControllerSetVehicleIntents',
        'ppid',
        ppid
      );
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'smartChargingControllerSetVehicleIntents',
        'vehicleId',
        vehicleId
      );
      // verify required parameter 'vehicleIntentsRequestDtoImpl' is not null or undefined
      assertParamExists(
        'smartChargingControllerSetVehicleIntents',
        'vehicleIntentsRequestDtoImpl',
        vehicleIntentsRequestDtoImpl
      );
      const localVarPath =
        `/smart-charging/delegated-controls/{ppid}/vehicles/{vehicleId}/intents`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        vehicleIntentsRequestDtoImpl,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {UpdateVehicleLinkRequestDtoImpl} updateVehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerUpdateVehicle: async (
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'ppid' is not null or undefined
      assertParamExists('smartChargingControllerUpdateVehicle', 'ppid', ppid);
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'smartChargingControllerUpdateVehicle',
        'vehicleId',
        vehicleId
      );
      // verify required parameter 'updateVehicleLinkRequestDtoImpl' is not null or undefined
      assertParamExists(
        'smartChargingControllerUpdateVehicle',
        'updateVehicleLinkRequestDtoImpl',
        updateVehicleLinkRequestDtoImpl
      );
      const localVarPath =
        `/smart-charging/delegated-controls/{ppid}/vehicles/{vehicleId}`
          .replace(`{${'ppid'}}`, encodeURIComponent(String(ppid)))
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateVehicleLinkRequestDtoImpl,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SmartChargingApi - functional programming interface
 * @export
 */
export const SmartChargingApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SmartChargingApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given PPID, capture a vehicle for smart charging
     * @summary capture a vehicle for smart charging
     * @param {string} ppid PPID of a given charger
     * @param {VehicleLinkRequestDtoImpl} vehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerCaptureVehicle(
      ppid: string,
      vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleLinkResponseDtoImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerCaptureVehicle(
          ppid,
          vehicleLinkRequestDtoImpl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerCaptureVehicle'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerCreateDelegatedControlStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerCreateDelegatedControlStation(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerCreateDelegatedControlStation'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID, remove a given vehicle
     * @summary Removes a given vehicle from a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerDeleteVehicle(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerDeleteVehicle(
          ppid,
          vehicleId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerDeleteVehicle'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given user, get all chargers and the vehicles linked to them
     * @summary retrieve all vehicles and chargers for a user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerGetChargersAndVehicles(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<ChargersAndVehicles>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerGetChargersAndVehicles(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerGetChargersAndVehicles'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID retrieve intents
     * @summary retrieve intents for a charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerGetIntents(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<DelegatedControlChargingStationResponseDtoImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerGetIntents(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerGetIntents'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerRemoveChargerFromDelegatedControl(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerRemoveChargerFromDelegatedControl(
          ppid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerRemoveChargerFromDelegatedControl'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given PPID and vehicle, set delegated control intents
     * @summary Sets delegated control intents of a given charger and vehicle
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId
     * @param {VehicleIntentsRequestDtoImpl} vehicleIntentsRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerSetVehicleIntents(
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SetVehicleIntent>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerSetVehicleIntents(
          ppid,
          vehicleId,
          vehicleIntentsRequestDtoImpl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerSetVehicleIntents'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {UpdateVehicleLinkRequestDtoImpl} updateVehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async smartChargingControllerUpdateVehicle(
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SmartChargingControllerUpdateVehicle200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.smartChargingControllerUpdateVehicle(
          ppid,
          vehicleId,
          updateVehicleLinkRequestDtoImpl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SmartChargingApi.smartChargingControllerUpdateVehicle'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SmartChargingApi - factory interface
 * @export
 */
export const SmartChargingApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SmartChargingApiFp(configuration);
  return {
    /**
     * For a given PPID, capture a vehicle for smart charging
     * @summary capture a vehicle for smart charging
     * @param {string} ppid PPID of a given charger
     * @param {VehicleLinkRequestDtoImpl} vehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerCaptureVehicle(
      ppid: string,
      vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleLinkResponseDtoImpl> {
      return localVarFp
        .smartChargingControllerCaptureVehicle(
          ppid,
          vehicleLinkRequestDtoImpl,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerCreateDelegatedControlStation(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .smartChargingControllerCreateDelegatedControlStation(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID, remove a given vehicle
     * @summary Removes a given vehicle from a given charger
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerDeleteVehicle(
      ppid: string,
      vehicleId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .smartChargingControllerDeleteVehicle(ppid, vehicleId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given user, get all chargers and the vehicles linked to them
     * @summary retrieve all vehicles and chargers for a user
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerGetChargersAndVehicles(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<ChargersAndVehicles>> {
      return localVarFp
        .smartChargingControllerGetChargersAndVehicles(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID retrieve intents
     * @summary retrieve intents for a charger
     * @param {string} ppid PPID of a given charger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerGetIntents(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<DelegatedControlChargingStationResponseDtoImpl> {
      return localVarFp
        .smartChargingControllerGetIntents(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerRemoveChargerFromDelegatedControl(
      ppid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .smartChargingControllerRemoveChargerFromDelegatedControl(ppid, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given PPID and vehicle, set delegated control intents
     * @summary Sets delegated control intents of a given charger and vehicle
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId
     * @param {VehicleIntentsRequestDtoImpl} vehicleIntentsRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerSetVehicleIntents(
      ppid: string,
      vehicleId: string,
      vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SetVehicleIntent> {
      return localVarFp
        .smartChargingControllerSetVehicleIntents(
          ppid,
          vehicleId,
          vehicleIntentsRequestDtoImpl,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {string} ppid PPID of a given charger
     * @param {string} vehicleId ID of a given vehicle
     * @param {UpdateVehicleLinkRequestDtoImpl} updateVehicleLinkRequestDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    smartChargingControllerUpdateVehicle(
      ppid: string,
      vehicleId: string,
      updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SmartChargingControllerUpdateVehicle200Response> {
      return localVarFp
        .smartChargingControllerUpdateVehicle(
          ppid,
          vehicleId,
          updateVehicleLinkRequestDtoImpl,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SmartChargingApi - object-oriented interface
 * @export
 * @class SmartChargingApi
 * @extends {BaseAPI}
 */
export class SmartChargingApi extends BaseAPI {
  /**
   * For a given PPID, capture a vehicle for smart charging
   * @summary capture a vehicle for smart charging
   * @param {string} ppid PPID of a given charger
   * @param {VehicleLinkRequestDtoImpl} vehicleLinkRequestDtoImpl
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerCaptureVehicle(
    ppid: string,
    vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerCaptureVehicle(
        ppid,
        vehicleLinkRequestDtoImpl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerCreateDelegatedControlStation(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerCreateDelegatedControlStation(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID, remove a given vehicle
   * @summary Removes a given vehicle from a given charger
   * @param {string} ppid PPID of a given charger
   * @param {string} vehicleId ID of a given vehicle
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerDeleteVehicle(
    ppid: string,
    vehicleId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerDeleteVehicle(ppid, vehicleId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given user, get all chargers and the vehicles linked to them
   * @summary retrieve all vehicles and chargers for a user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerGetChargersAndVehicles(
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerGetChargersAndVehicles(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID retrieve intents
   * @summary retrieve intents for a charger
   * @param {string} ppid PPID of a given charger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerGetIntents(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerGetIntents(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerRemoveChargerFromDelegatedControl(
    ppid: string,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerRemoveChargerFromDelegatedControl(ppid, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given PPID and vehicle, set delegated control intents
   * @summary Sets delegated control intents of a given charger and vehicle
   * @param {string} ppid PPID of a given charger
   * @param {string} vehicleId
   * @param {VehicleIntentsRequestDtoImpl} vehicleIntentsRequestDtoImpl
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerSetVehicleIntents(
    ppid: string,
    vehicleId: string,
    vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerSetVehicleIntents(
        ppid,
        vehicleId,
        vehicleIntentsRequestDtoImpl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {string} ppid PPID of a given charger
   * @param {string} vehicleId ID of a given vehicle
   * @param {UpdateVehicleLinkRequestDtoImpl} updateVehicleLinkRequestDtoImpl
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SmartChargingApi
   */
  public smartChargingControllerUpdateVehicle(
    ppid: string,
    vehicleId: string,
    updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl,
    options?: RawAxiosRequestConfig
  ) {
    return SmartChargingApiFp(this.configuration)
      .smartChargingControllerUpdateVehicle(
        ppid,
        vehicleId,
        updateVehicleLinkRequestDtoImpl,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SubscriptionsApi - axios parameter creator
 * @export
 */
export const SubscriptionsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Performs a confirmation of payee check
     * @summary
     * @param {SubscriptionConfirmationOfPayeeDTO} subscriptionConfirmationOfPayeeDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerDoConfirmationOfPayeeCheck: async (
      subscriptionConfirmationOfPayeeDTO: SubscriptionConfirmationOfPayeeDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionConfirmationOfPayeeDTO' is not null or undefined
      assertParamExists(
        'subscriptionsControllerDoConfirmationOfPayeeCheck',
        'subscriptionConfirmationOfPayeeDTO',
        subscriptionConfirmationOfPayeeDTO
      );
      const localVarPath = `/subscriptions/actions/SETUP_DIRECT_DEBIT_V1/confirmation-of-payee`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        subscriptionConfirmationOfPayeeDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the form data for check affordability action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetCheckAffordabilityFormData: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/subscriptions/actions/CHECK_AFFORDABILITY_V1/form-data`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get the form data for setup direct debit action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSetupDirectDebitFormData: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/subscriptions/actions/SETUP_DIRECT_DEBIT_V1/form-data`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a subscription action by id
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionActionById: async (
      subscriptionId: string,
      actionId: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionActionById',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'actionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerGetSubscriptionActionById',
        'actionId',
        actionId
      );
      const localVarPath = `/subscriptions/{subscriptionId}/actions/{actionId}`
        .replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        )
        .replace(`{${'actionId'}}`, encodeURIComponent(String(actionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get a subscription by id
     * @summary
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionById: async (
      id: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('subscriptionsControllerGetSubscriptionById', 'id', id);
      const localVarPath = `/subscriptions/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Lists all the subscriptions associated with the user
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptions: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/subscriptions`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Updates a given action of a subscription
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {SubscriptionsControllerUpdateSubscriptionActionRequest} subscriptionsControllerUpdateSubscriptionActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerUpdateSubscriptionAction: async (
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateSubscriptionActionRequest: SubscriptionsControllerUpdateSubscriptionActionRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'subscriptionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateSubscriptionAction',
        'subscriptionId',
        subscriptionId
      );
      // verify required parameter 'actionId' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateSubscriptionAction',
        'actionId',
        actionId
      );
      // verify required parameter 'subscriptionsControllerUpdateSubscriptionActionRequest' is not null or undefined
      assertParamExists(
        'subscriptionsControllerUpdateSubscriptionAction',
        'subscriptionsControllerUpdateSubscriptionActionRequest',
        subscriptionsControllerUpdateSubscriptionActionRequest
      );
      const localVarPath = `/subscriptions/{subscriptionId}/actions/{actionId}`
        .replace(
          `{${'subscriptionId'}}`,
          encodeURIComponent(String(subscriptionId))
        )
        .replace(`{${'actionId'}}`, encodeURIComponent(String(actionId)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        subscriptionsControllerUpdateSubscriptionActionRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SubscriptionsApi - functional programming interface
 * @export
 */
export const SubscriptionsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    SubscriptionsApiAxiosParamCreator(configuration);
  return {
    /**
     * Performs a confirmation of payee check
     * @summary
     * @param {SubscriptionConfirmationOfPayeeDTO} subscriptionConfirmationOfPayeeDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerDoConfirmationOfPayeeCheck(
      subscriptionConfirmationOfPayeeDTO: SubscriptionConfirmationOfPayeeDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionConfirmationOfPayeeResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerDoConfirmationOfPayeeCheck(
          subscriptionConfirmationOfPayeeDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerDoConfirmationOfPayeeCheck'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the form data for check affordability action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetCheckAffordabilityFormData(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<FormDataDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetCheckAffordabilityFormData(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetCheckAffordabilityFormData'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get the form data for setup direct debit action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSetupDirectDebitFormData(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<FormDataDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSetupDirectDebitFormData(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSetupDirectDebitFormData'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a subscription action by id
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptionActionById(
      subscriptionId: string,
      actionId: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionsControllerGetSubscriptionActionById200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptionActionById(
          subscriptionId,
          actionId,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptionActionById'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Get a subscription by id
     * @summary
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptionById(
      id: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptionById(
          id,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptionById'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Lists all the subscriptions associated with the user
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerGetSubscriptions(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionListDTO>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerGetSubscriptions(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerGetSubscriptions'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Updates a given action of a subscription
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {SubscriptionsControllerUpdateSubscriptionActionRequest} subscriptionsControllerUpdateSubscriptionActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async subscriptionsControllerUpdateSubscriptionAction(
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateSubscriptionActionRequest: SubscriptionsControllerUpdateSubscriptionActionRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<SubscriptionsControllerUpdateSubscriptionAction200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.subscriptionsControllerUpdateSubscriptionAction(
          subscriptionId,
          actionId,
          subscriptionsControllerUpdateSubscriptionActionRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'SubscriptionsApi.subscriptionsControllerUpdateSubscriptionAction'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SubscriptionsApi - factory interface
 * @export
 */
export const SubscriptionsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SubscriptionsApiFp(configuration);
  return {
    /**
     * Performs a confirmation of payee check
     * @summary
     * @param {SubscriptionConfirmationOfPayeeDTO} subscriptionConfirmationOfPayeeDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerDoConfirmationOfPayeeCheck(
      subscriptionConfirmationOfPayeeDTO: SubscriptionConfirmationOfPayeeDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionConfirmationOfPayeeResponse> {
      return localVarFp
        .subscriptionsControllerDoConfirmationOfPayeeCheck(
          subscriptionConfirmationOfPayeeDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the form data for check affordability action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetCheckAffordabilityFormData(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FormDataDTO> {
      return localVarFp
        .subscriptionsControllerGetCheckAffordabilityFormData(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get the form data for setup direct debit action
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSetupDirectDebitFormData(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<FormDataDTO> {
      return localVarFp
        .subscriptionsControllerGetSetupDirectDebitFormData(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a subscription action by id
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionActionById(
      subscriptionId: string,
      actionId: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionsControllerGetSubscriptionActionById200Response> {
      return localVarFp
        .subscriptionsControllerGetSubscriptionActionById(
          subscriptionId,
          actionId,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get a subscription by id
     * @summary
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptionById(
      id: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionDTO> {
      return localVarFp
        .subscriptionsControllerGetSubscriptionById(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Lists all the subscriptions associated with the user
     * @summary
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerGetSubscriptions(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionListDTO> {
      return localVarFp
        .subscriptionsControllerGetSubscriptions(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Updates a given action of a subscription
     * @summary
     * @param {string} subscriptionId
     * @param {string} actionId
     * @param {SubscriptionsControllerUpdateSubscriptionActionRequest} subscriptionsControllerUpdateSubscriptionActionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    subscriptionsControllerUpdateSubscriptionAction(
      subscriptionId: string,
      actionId: string,
      subscriptionsControllerUpdateSubscriptionActionRequest: SubscriptionsControllerUpdateSubscriptionActionRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<SubscriptionsControllerUpdateSubscriptionAction200Response> {
      return localVarFp
        .subscriptionsControllerUpdateSubscriptionAction(
          subscriptionId,
          actionId,
          subscriptionsControllerUpdateSubscriptionActionRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SubscriptionsApi - object-oriented interface
 * @export
 * @class SubscriptionsApi
 * @extends {BaseAPI}
 */
export class SubscriptionsApi extends BaseAPI {
  /**
   * Performs a confirmation of payee check
   * @summary
   * @param {SubscriptionConfirmationOfPayeeDTO} subscriptionConfirmationOfPayeeDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerDoConfirmationOfPayeeCheck(
    subscriptionConfirmationOfPayeeDTO: SubscriptionConfirmationOfPayeeDTO,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerDoConfirmationOfPayeeCheck(
        subscriptionConfirmationOfPayeeDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the form data for check affordability action
   * @summary
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetCheckAffordabilityFormData(
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetCheckAffordabilityFormData(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get the form data for setup direct debit action
   * @summary
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSetupDirectDebitFormData(
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSetupDirectDebitFormData(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a subscription action by id
   * @summary
   * @param {string} subscriptionId
   * @param {string} actionId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptionActionById(
    subscriptionId: string,
    actionId: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptionActionById(
        subscriptionId,
        actionId,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get a subscription by id
   * @summary
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptionById(
    id: string,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptionById(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Lists all the subscriptions associated with the user
   * @summary
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerGetSubscriptions(
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerGetSubscriptions(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Updates a given action of a subscription
   * @summary
   * @param {string} subscriptionId
   * @param {string} actionId
   * @param {SubscriptionsControllerUpdateSubscriptionActionRequest} subscriptionsControllerUpdateSubscriptionActionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionsApi
   */
  public subscriptionsControllerUpdateSubscriptionAction(
    subscriptionId: string,
    actionId: string,
    subscriptionsControllerUpdateSubscriptionActionRequest: SubscriptionsControllerUpdateSubscriptionActionRequest,
    options?: RawAxiosRequestConfig
  ) {
    return SubscriptionsApiFp(this.configuration)
      .subscriptionsControllerUpdateSubscriptionAction(
        subscriptionId,
        actionId,
        subscriptionsControllerUpdateSubscriptionActionRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * SupportApi - axios parameter creator
 * @export
 */
export const SupportApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Sends an email to the language-specific support team
     * @summary submit feedback to support
     * @param {string} acceptLanguage
     * @param {SubmitSupportFeedbackDTO} submitSupportFeedbackDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    supportControllerSubmitFeedback: async (
      acceptLanguage: string,
      submitSupportFeedbackDTO: SubmitSupportFeedbackDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'acceptLanguage' is not null or undefined
      assertParamExists(
        'supportControllerSubmitFeedback',
        'acceptLanguage',
        acceptLanguage
      );
      // verify required parameter 'submitSupportFeedbackDTO' is not null or undefined
      assertParamExists(
        'supportControllerSubmitFeedback',
        'submitSupportFeedbackDTO',
        submitSupportFeedbackDTO
      );
      const localVarPath = `/support/feedback`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        submitSupportFeedbackDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * SupportApi - functional programming interface
 * @export
 */
export const SupportApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = SupportApiAxiosParamCreator(configuration);
  return {
    /**
     * Sends an email to the language-specific support team
     * @summary submit feedback to support
     * @param {string} acceptLanguage
     * @param {SubmitSupportFeedbackDTO} submitSupportFeedbackDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async supportControllerSubmitFeedback(
      acceptLanguage: string,
      submitSupportFeedbackDTO: SubmitSupportFeedbackDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.supportControllerSubmitFeedback(
          acceptLanguage,
          submitSupportFeedbackDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SupportApi.supportControllerSubmitFeedback']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SupportApi - factory interface
 * @export
 */
export const SupportApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = SupportApiFp(configuration);
  return {
    /**
     * Sends an email to the language-specific support team
     * @summary submit feedback to support
     * @param {string} acceptLanguage
     * @param {SubmitSupportFeedbackDTO} submitSupportFeedbackDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    supportControllerSubmitFeedback(
      acceptLanguage: string,
      submitSupportFeedbackDTO: SubmitSupportFeedbackDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .supportControllerSubmitFeedback(
          acceptLanguage,
          submitSupportFeedbackDTO,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SupportApi - object-oriented interface
 * @export
 * @class SupportApi
 * @extends {BaseAPI}
 */
export class SupportApi extends BaseAPI {
  /**
   * Sends an email to the language-specific support team
   * @summary submit feedback to support
   * @param {string} acceptLanguage
   * @param {SubmitSupportFeedbackDTO} submitSupportFeedbackDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SupportApi
   */
  public supportControllerSubmitFeedback(
    acceptLanguage: string,
    submitSupportFeedbackDTO: SubmitSupportFeedbackDTO,
    options?: RawAxiosRequestConfig
  ) {
    return SupportApiFp(this.configuration)
      .supportControllerSubmitFeedback(
        acceptLanguage,
        submitSupportFeedbackDTO,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UsersApi - axios parameter creator
 * @export
 */
export const UsersApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Get the user info for the authenticated user
     * @summary Gets the current user\'s info
     * @param {string} [xAppVersion] The mobile app version currently used to determine if rewards should be returned
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerGetUser: async (
      xAppVersion?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/users`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (xAppVersion != null) {
        localVarHeaderParameter['x-app-version'] = String(xAppVersion);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Generates and returns a link for connecting a user\'s account to Enode
     * @summary Generates a link for connecting to Enode
     * @param {GetLinkSessionRequest} getLinkSessionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerLinkEnode: async (
      getLinkSessionRequest: GetLinkSessionRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'getLinkSessionRequest' is not null or undefined
      assertParamExists(
        'usersControllerLinkEnode',
        'getLinkSessionRequest',
        getLinkSessionRequest
      );
      const localVarPath = `/users/enode/link`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        getLinkSessionRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerTrackLogin: async (
      trackLoginRequest: TrackLoginRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'trackLoginRequest' is not null or undefined
      assertParamExists(
        'usersControllerTrackLogin',
        'trackLoginRequest',
        trackLoginRequest
      );
      const localVarPath = `/users/login/alert`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        trackLoginRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UsersApi - functional programming interface
 * @export
 */
export const UsersApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = UsersApiAxiosParamCreator(configuration);
  return {
    /**
     * Get the user info for the authenticated user
     * @summary Gets the current user\'s info
     * @param {string} [xAppVersion] The mobile app version currently used to determine if rewards should be returned
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersControllerGetUser(
      xAppVersion?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<ExtendedUserInfoResponseDto>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersControllerGetUser(
          xAppVersion,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.usersControllerGetUser']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Generates and returns a link for connecting a user\'s account to Enode
     * @summary Generates a link for connecting to Enode
     * @param {GetLinkSessionRequest} getLinkSessionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersControllerLinkEnode(
      getLinkSessionRequest: GetLinkSessionRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<GetLinkSessionResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersControllerLinkEnode(
          getLinkSessionRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.usersControllerLinkEnode']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersControllerTrackLogin(
      trackLoginRequest: TrackLoginRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersControllerTrackLogin(
          trackLoginRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['UsersApi.usersControllerTrackLogin']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UsersApi - factory interface
 * @export
 */
export const UsersApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UsersApiFp(configuration);
  return {
    /**
     * Get the user info for the authenticated user
     * @summary Gets the current user\'s info
     * @param {string} [xAppVersion] The mobile app version currently used to determine if rewards should be returned
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerGetUser(
      xAppVersion?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<ExtendedUserInfoResponseDto> {
      return localVarFp
        .usersControllerGetUser(xAppVersion, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Generates and returns a link for connecting a user\'s account to Enode
     * @summary Generates a link for connecting to Enode
     * @param {GetLinkSessionRequest} getLinkSessionRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerLinkEnode(
      getLinkSessionRequest: GetLinkSessionRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<GetLinkSessionResponse> {
      return localVarFp
        .usersControllerLinkEnode(getLinkSessionRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Generates an email of when and from which ip the user logged in
     * @summary Generates an email of when and from which ip the user logged in
     * @param {TrackLoginRequest} trackLoginRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersControllerTrackLogin(
      trackLoginRequest: TrackLoginRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .usersControllerTrackLogin(trackLoginRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UsersApi - object-oriented interface
 * @export
 * @class UsersApi
 * @extends {BaseAPI}
 */
export class UsersApi extends BaseAPI {
  /**
   * Get the user info for the authenticated user
   * @summary Gets the current user\'s info
   * @param {string} [xAppVersion] The mobile app version currently used to determine if rewards should be returned
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public usersControllerGetUser(
    xAppVersion?: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .usersControllerGetUser(xAppVersion, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Generates and returns a link for connecting a user\'s account to Enode
   * @summary Generates a link for connecting to Enode
   * @param {GetLinkSessionRequest} getLinkSessionRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public usersControllerLinkEnode(
    getLinkSessionRequest: GetLinkSessionRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .usersControllerLinkEnode(getLinkSessionRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Generates an email of when and from which ip the user logged in
   * @summary Generates an email of when and from which ip the user logged in
   * @param {TrackLoginRequest} trackLoginRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersApi
   */
  public usersControllerTrackLogin(
    trackLoginRequest: TrackLoginRequest,
    options?: RawAxiosRequestConfig
  ) {
    return UsersApiFp(this.configuration)
      .usersControllerTrackLogin(trackLoginRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UsersNotificationsApi - axios parameter creator
 * @export
 */
export const UsersNotificationsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerDeleteToken: async (
      token: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'token' is not null or undefined
      assertParamExists(
        'usersNotificationsControllerDeleteToken',
        'token',
        token
      );
      const localVarPath = `/users/notifications/token/{token}`.replace(
        `{${'token'}}`,
        encodeURIComponent(String(token))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary retrieves notifications tokens
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerGetTokens: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/users/notifications/token`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {FcmTokenDtoImpl} fcmTokenDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerSaveToken: async (
      fcmTokenDtoImpl: FcmTokenDtoImpl,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'fcmTokenDtoImpl' is not null or undefined
      assertParamExists(
        'usersNotificationsControllerSaveToken',
        'fcmTokenDtoImpl',
        fcmTokenDtoImpl
      );
      const localVarPath = `/users/notifications/token`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        fcmTokenDtoImpl,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UsersNotificationsApi - functional programming interface
 * @export
 */
export const UsersNotificationsApiFp = function (
  configuration?: Configuration
) {
  const localVarAxiosParamCreator =
    UsersNotificationsApiAxiosParamCreator(configuration);
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersNotificationsControllerDeleteToken(
      token: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersNotificationsControllerDeleteToken(
          token,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UsersNotificationsApi.usersNotificationsControllerDeleteToken'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary retrieves notifications tokens
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersNotificationsControllerGetTokens(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<Array<FcmTokenDtoImpl>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersNotificationsControllerGetTokens(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UsersNotificationsApi.usersNotificationsControllerGetTokens'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {FcmTokenDtoImpl} fcmTokenDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async usersNotificationsControllerSaveToken(
      fcmTokenDtoImpl: FcmTokenDtoImpl,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.usersNotificationsControllerSaveToken(
          fcmTokenDtoImpl,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'UsersNotificationsApi.usersNotificationsControllerSaveToken'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * UsersNotificationsApi - factory interface
 * @export
 */
export const UsersNotificationsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = UsersNotificationsApiFp(configuration);
  return {
    /**
     * Deletes a notification token associated with a user
     * @summary delete notification token
     * @param {string} token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerDeleteToken(
      token: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .usersNotificationsControllerDeleteToken(token, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Gets notifications tokens stored against a given user
     * @summary retrieves notifications tokens
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerGetTokens(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<Array<FcmTokenDtoImpl>> {
      return localVarFp
        .usersNotificationsControllerGetTokens(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Stores a notification token against a given user
     * @summary store notification token
     * @param {FcmTokenDtoImpl} fcmTokenDtoImpl
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    usersNotificationsControllerSaveToken(
      fcmTokenDtoImpl: FcmTokenDtoImpl,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .usersNotificationsControllerSaveToken(fcmTokenDtoImpl, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UsersNotificationsApi - object-oriented interface
 * @export
 * @class UsersNotificationsApi
 * @extends {BaseAPI}
 */
export class UsersNotificationsApi extends BaseAPI {
  /**
   * Deletes a notification token associated with a user
   * @summary delete notification token
   * @param {string} token
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersNotificationsApi
   */
  public usersNotificationsControllerDeleteToken(
    token: string,
    options?: RawAxiosRequestConfig
  ) {
    return UsersNotificationsApiFp(this.configuration)
      .usersNotificationsControllerDeleteToken(token, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Gets notifications tokens stored against a given user
   * @summary retrieves notifications tokens
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersNotificationsApi
   */
  public usersNotificationsControllerGetTokens(
    options?: RawAxiosRequestConfig
  ) {
    return UsersNotificationsApiFp(this.configuration)
      .usersNotificationsControllerGetTokens(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Stores a notification token against a given user
   * @summary store notification token
   * @param {FcmTokenDtoImpl} fcmTokenDtoImpl
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UsersNotificationsApi
   */
  public usersNotificationsControllerSaveToken(
    fcmTokenDtoImpl: FcmTokenDtoImpl,
    options?: RawAxiosRequestConfig
  ) {
    return UsersNotificationsApiFp(this.configuration)
      .usersNotificationsControllerSaveToken(fcmTokenDtoImpl, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VehiclesApi - axios parameter creator
 * @export
 */
export const VehiclesApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given vehicle, get all interventions
     * @summary get all interventions for a given vehicle
     * @param {string} vehicleId ID of the Vehicle
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    vehiclesControllerGetAllInterventions: async (
      vehicleId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'vehiclesControllerGetAllInterventions',
        'vehicleId',
        vehicleId
      );
      const localVarPath = `/vehicles/{vehicleId}/interventions`.replace(
        `{${'vehicleId'}}`,
        encodeURIComponent(String(vehicleId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * For a given intervention, get its information
     * @summary get a specific intervention
     * @param {string} vehicleId vehicleId of the vehicle
     * @param {string} interventionId ID of the intervention
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    vehiclesControllerGetIntervention: async (
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'vehicleId' is not null or undefined
      assertParamExists(
        'vehiclesControllerGetIntervention',
        'vehicleId',
        vehicleId
      );
      // verify required parameter 'interventionId' is not null or undefined
      assertParamExists(
        'vehiclesControllerGetIntervention',
        'interventionId',
        interventionId
      );
      const localVarPath =
        `/vehicles/{vehicleId}/interventions/{interventionId}`
          .replace(`{${'vehicleId'}}`, encodeURIComponent(String(vehicleId)))
          .replace(
            `{${'interventionId'}}`,
            encodeURIComponent(String(interventionId))
          );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VehiclesApi - functional programming interface
 * @export
 */
export const VehiclesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VehiclesApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given vehicle, get all interventions
     * @summary get all interventions for a given vehicle
     * @param {string} vehicleId ID of the Vehicle
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async vehiclesControllerGetAllInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionResponseDtoImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.vehiclesControllerGetAllInterventions(
          vehicleId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'VehiclesApi.vehiclesControllerGetAllInterventions'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * For a given intervention, get its information
     * @summary get a specific intervention
     * @param {string} vehicleId vehicleId of the vehicle
     * @param {string} interventionId ID of the intervention
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async vehiclesControllerGetIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<VehicleInterventionDtoImpl>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.vehiclesControllerGetIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VehiclesApi.vehiclesControllerGetIntervention']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VehiclesApi - factory interface
 * @export
 */
export const VehiclesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VehiclesApiFp(configuration);
  return {
    /**
     * For a given vehicle, get all interventions
     * @summary get all interventions for a given vehicle
     * @param {string} vehicleId ID of the Vehicle
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    vehiclesControllerGetAllInterventions(
      vehicleId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionResponseDtoImpl> {
      return localVarFp
        .vehiclesControllerGetAllInterventions(
          vehicleId,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * For a given intervention, get its information
     * @summary get a specific intervention
     * @param {string} vehicleId vehicleId of the vehicle
     * @param {string} interventionId ID of the intervention
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    vehiclesControllerGetIntervention(
      vehicleId: string,
      interventionId: string,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<VehicleInterventionDtoImpl> {
      return localVarFp
        .vehiclesControllerGetIntervention(
          vehicleId,
          interventionId,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VehiclesApi - object-oriented interface
 * @export
 * @class VehiclesApi
 * @extends {BaseAPI}
 */
export class VehiclesApi extends BaseAPI {
  /**
   * For a given vehicle, get all interventions
   * @summary get all interventions for a given vehicle
   * @param {string} vehicleId ID of the Vehicle
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public vehiclesControllerGetAllInterventions(
    vehicleId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .vehiclesControllerGetAllInterventions(vehicleId, acceptLanguage, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * For a given intervention, get its information
   * @summary get a specific intervention
   * @param {string} vehicleId vehicleId of the vehicle
   * @param {string} interventionId ID of the intervention
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VehiclesApi
   */
  public vehiclesControllerGetIntervention(
    vehicleId: string,
    interventionId: string,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return VehiclesApiFp(this.configuration)
      .vehiclesControllerGetIntervention(
        vehicleId,
        interventionId,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VersionApi - axios parameter creator
 * @export
 */
export const VersionApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/version`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VersionApi - functional programming interface
 * @export
 */
export const VersionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VersionApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.versionControllerGetVersion(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VersionApi.versionControllerGetVersion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VersionApi - factory interface
 * @export
 */
export const VersionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VersionApiFp(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .versionControllerGetVersion(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VersionApi - object-oriented interface
 * @export
 * @class VersionApi
 * @extends {BaseAPI}
 */
export class VersionApi extends BaseAPI {
  /**
   *
   * @summary get application version
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VersionApi
   */
  public versionControllerGetVersion(options?: RawAxiosRequestConfig) {
    return VersionApiFp(this.configuration)
      .versionControllerGetVersion(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
