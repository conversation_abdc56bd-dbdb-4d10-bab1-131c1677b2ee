# Mobile Api Maizzle

This library uses the Maizzle framework to compile Tailwind styled emails using templates.

This library was generated with [Nx](https://nx.dev).

## Build emails:

```
nx run mobile-api-maizzle:build
```

This outputs the compiled emails to assets/site-admin-api/email-templates, as defined in the config.js

## Serve

```
nx run mobile-api-maizzle:serve
```

This starts a dev server on port 3000, so compiled email templates can be viewed

### Documentation

Maizzle documentation is available at https://maizzle.com
