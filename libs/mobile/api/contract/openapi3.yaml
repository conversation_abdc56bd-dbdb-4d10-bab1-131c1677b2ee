openapi: 3.0.0
paths:
  /auth/users:
    post:
      operationId: Account<PERSON>lient<PERSON>ontroller_createNewUser
      summary: create a new user
      description: Creates a new user account
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserPayload'
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: &a1
        - Driver Account
  /auth/password-reset:
    post:
      operationId: AccountClientController_sendPasswordResetRequest
      summary: reset a user's password
      description: Sends a reset password email to the user's email address
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequestDto'
      responses:
        '201':
          description: ''
      tags: *a1
  /auth/email-verification:
    post:
      operationId: AccountClientController_sendEmailVerificationRequest
      summary: verify a user's email address
      description: Sends an email verification email to the user's email address
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      responses:
        '201':
          description: ''
      tags: *a1
      security:
        - bearer: []
  /auth/verify-and-change-email:
    post:
      operationId: AccountClientController_sendVerifyAndChangeEmail
      summary: changes a user's email address
      description: Sends a verification email to the user's new email address,
        updating the user's email address once the new email address has been
        verified
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendVerifyAndChangeEmailRequest'
      responses:
        '201':
          description: ''
      tags: *a1
      security:
        - bearer: []
  /auth/user:
    put:
      operationId: AccountClientController_updateUser
      summary: Proxy update user
      description: proxy call the call to driver account api
      parameters:
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserDto'
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailsDto'
      tags: *a1
      security:
        - bearer: []
    delete:
      operationId: AccountClientController_deleteUser
      summary: deletes the user
      description: Deletes the user account
      parameters: []
      responses:
        '202':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *a1
      security:
        - bearer: []
  /auth/recover-factor:
    post:
      operationId: AccountClientController_recoverFactor
      summary: 'Recover 2 factor autentication for user '
      description: Sends an email to the user to recover their 2 factor authentication
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendRecoverFactorRequest'
      responses:
        '200':
          description: ''
      tags: *a1
  /auth/telephone-codes:
    get:
      operationId: AccountClientController_getTelephoneCodes
      summary: retrieve available telephone codes
      description: Retrieves available telephone codes specified by the Firebase
        configuration
      parameters:
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      responses:
        '200':
          description: Successfully retrieved country codes
        '404':
          description: Firebase configuration could not be found
      tags: *a1
  /auth/sign-in-with-email:
    post:
      operationId: AccountClientController_signInWithMagicLink
      summary: sends a magic link to the given email
      description: For the given email, send a magic link
      parameters:
        - name: x-app-name
          required: false
          in: header
          description: The name of the requesting app
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInWithMagicLinkDto'
      responses:
        '200':
          description: ''
        '400':
          description: Returned if request not from a valid app
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a1
  /users:
    get:
      operationId: UsersController_getUser
      summary: Gets the current user's info
      description: Get the user info for the authenticated user
      parameters:
        - name: x-app-version
          required: false
          in: header
          description:
            The mobile app version currently used to determine if rewards
            should be returned
          schema:
            type: string
      responses:
        '200':
          description: The user profile information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedUserInfoResponseDto'
      tags: &a2
        - Users
      security: &a3
        - bearer: []
  /users/enode/link:
    post:
      operationId: UsersController_linkEnode
      summary: Generates a link for connecting to Enode
      description: Generates and returns a link for connecting a user's account to Enode
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLinkSessionRequest'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLinkSessionResponse'
      tags: *a2
      security: *a3
  /users/login/alert:
    post:
      operationId: UsersController_trackLogin
      summary: Generates an email of when and from which ip the user logged in
      description: Generates an email of when and from which ip the user logged in
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrackLoginRequest'
      responses:
        '200':
          description: ''
      tags: *a2
      security: *a3
  /users/notifications/token:
    get:
      operationId: UsersNotificationsController_getTokens
      summary: retrieves notifications tokens
      description: Gets notifications tokens stored against a given user
      parameters: []
      responses:
        '200':
          description: Successfully retrieved notifications tokens
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FcmTokenDtoImpl'
        '401':
          description: The user was unauthorised
      tags: &a4
        - Users Notifications
      security: &a5
        - bearer: []
    post:
      operationId: UsersNotificationsController_saveToken
      summary: store notification token
      description: Stores a notification token against a given user
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FcmTokenDtoImpl'
      responses:
        '201':
          description: The notification token was successfully stored
        '401':
          description: The user was unauthorised
      tags: *a4
      security: *a5
  /users/notifications/token/{token}:
    delete:
      operationId: UsersNotificationsController_deleteToken
      summary: delete notification token
      description: Deletes a notification token associated with a user
      parameters:
        - name: token
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Token was successfully deleted
        '401':
          description: The user was unauthorised
      tags: *a4
      security: *a5
  /rewards/bank-accounts:
    post:
      operationId: RewardsController_createBankAccount
      summary: ''
      description: Create a new rewards bank account for the authenticated user
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RewardsAccountDTO'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RewardsBankAccountDTOImpl'
        '400':
          description: Invalid Body
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '422':
          description: Returned when request failed confirmation of Payee
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmationOfPayeeDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a6
        - Rewards
      security: &a7
        - bearer: []
    get:
      operationId: RewardsController_getBankAccounts
      summary: ''
      description: Get the user's bank accounts
      parameters: []
      responses:
        '200':
          description: The user's bank accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RewardsBankAccountDTOImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a6
      security: *a7
  /rewards/transactions:
    get:
      operationId: RewardsController_getRewardsTransactions
      summary: ''
      description: Get a list of rewards-related transactions the user has made
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RewardsTransactionDTOImpl'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a6
      security: *a7
  /rewards/payout:
    post:
      operationId: RewardsController_makeRewardsPayout
      summary: ''
      description: Make a payout of the rewards balance to the user
      parameters: []
      responses:
        '201':
          description: ''
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '501':
          description: Feature implementation to be confirmed
      tags: *a6
      security: *a7
  /rewards/bank-accounts/{bankAccountId}:
    put:
      operationId: RewardsController_updateBankAccount
      summary: ''
      description: Update a users bank account
      parameters:
        - name: bankAccountId
          required: true
          in: path
          description: The ID of the bank account
          schema:
            example: a5c745a2-6d11-4518-998e-7ac22c8160af
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RewardsAccountDTO'
      responses:
        '201':
          description: The account was successfully updated
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Bank account not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '422':
          description: Returned when request failed confirmation of Payee
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmationOfPayeeDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a6
      security: *a7
    delete:
      operationId: RewardsController_archiveAccount
      summary: ''
      description: Archive a users bank account
      parameters:
        - name: bankAccountId
          required: true
          in: path
          description: The ID of the bank account
          schema:
            example: a5c745a2-6d11-4518-998e-7ac22c8160af
            type: string
      responses:
        '204':
          description: The account was successfully archived
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Bank account not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a6
      security: *a7
  /rewards/webhook:
    post:
      operationId: RewardsController_proxyWebhook
      parameters:
        - name: stripe-signature
          required: true
          in: header
          schema:
            type: string
        - name: Stripe-Signature
          in: header
          description: Used for verifying the request
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Buffer'
      responses:
        '200':
          description: The received event has been successfully sent for processing
        '201':
          description: ''
        '400':
          description: The event is not valid
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a6
      security: *a7
  /api3/v5/auth:
    get:
      operationId: Api3Controller_getUser
      summary: retrieve user info
      description: Retrieve user info from API3 based on the JWT token
      parameters:
        - name: include
          required: false
          in: query
          description: Query param for auth
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a8
        - API3
      security: &a9
        - bearer: []
  /api3/v5/units/{unitId}/firmware:
    get:
      operationId: Api3Controller_getCurrentFirmware
      summary: request firmware version
      description: Request firmware version
      parameters:
        - name: unitId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FirmwareStatusResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a8
      security: *a9
  /api3/v5/tariffs:
    post:
      operationId: Api3Controller_storeTariff
      summary: store tariff
      description: Request to store a new tariff or update an existing one
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TariffRequest'
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TariffResponse'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a8
      security: *a9
  /api3/v5/locales:
    get:
      operationId: Api3Controller_getLocales
      summary: get locales
      description: Request to get all the locales
      parameters:
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocaleResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a8
      security: *a9
  /api3/v5/charges:
    post:
      operationId: Api3Controller_claimCharge
      summary: claim a charge
      description: Claims a given charge
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeRequestDTO'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeRequestResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a8
      security: *a9
  /api3/v5/users/{id}/account/topup:
    post:
      operationId: Api3Controller_topUpAccount
      summary: top up an account
      description: Tops up the user's account
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountTopUpRequestDTO'
      responses:
        '200':
          description: ''
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a8
      security: *a9
  /carbon/intensity/{from}/fw48h/regionid/{regionId}:
    get:
      operationId: CarbonController_getCarbonIntensity48h
      summary: retrieve regional forecast 48 hours from date
      description: Retrieve, half-hourly, forecast data 48 hours from provided date
      parameters:
        - name: from
          required: true
          in: path
          description: Datetime is in ISO8601 and RFC3339 compliant format
            YYYY-MM-DDThh:mm:ssZ.
          schema:
            format: date-time
            example: 2023-03-23T17:00:00Z
            type: string
        - name: regionId
          required: true
          in: path
          description: 'Region ID of GB region. See list of Region IDs here:
            https://carbon-intensity.github.io/api-definitions/#region-list.'
          schema:
            example: '1'
            type: number
        - name: timezone
          required: false
          in: query
          description:
            'Timezone in which to normalise date transformation, as an IANA
            timezone name. Defaults to Etc/UTC. See list of time zones here:
            https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.'
          schema:
            example: America/New_York
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Forecast'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a10
        - Carbon Intensity
      security: &a11
        - bearer: []
  /carbon/intensity/{from}/fw30m/regionid/{regionId}:
    get:
      operationId: CarbonController_getCarbonIntensity30m
      summary:
        retrieve regional forecast for a 30minute window that includes the
        provided date
      description: Retrieve, half-hourly, forecast data that includes the provided date
      parameters:
        - name: from
          required: true
          in: path
          description: Datetime is in ISO8601 and RFC3339 compliant format
            YYYY-MM-DDThh:mm:ssZ.
          schema:
            format: date-time
            example: 2023-03-23T17:00:00Z
            type: string
        - name: regionId
          required: true
          in: path
          description: 'Region ID of GB region. See list of Region IDs here:
            https://carbon-intensity.github.io/api-definitions/#region-list.'
          schema:
            example: '1'
            type: number
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForecastSnapshot'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a10
      security: *a11
  /carbon/regions:
    get:
      operationId: CarbonController_getDnoRegions
      summary: get the list of DNO regions
      description: Returns a list of DNO regions.
      parameters: []
      responses:
        '200':
          description: Retrieve a list of DNO regions.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DnoRegions'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a10
      security: *a11
  /chargers:
    get:
      operationId: ChargersController_getChargers
      summary: retrieve all chargers
      description: For the authenticated user, return all their chargers
      parameters: []
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChargerResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a12
        - Chargers
      security: &a13
        - bearer: []
  /chargers/{ppid}/dnoregion:
    get:
      operationId: ChargersController_getDnoRegion
      summary: retrieve charger region
      description: For a given PSL / PPID, return the associated DNO region id.
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Region'
        '400':
          description:
            Returns bad request when a location does not exist for the provided
            charger PSL/PPID.
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description:
            Returns not found when a DNO region cannot be determined for the
            provided charger PSL/PPID.
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a12
      security: *a13
  /chargers/{ppid}/restrictions:
    get:
      operationId: ChargersController_getRestrictions
      summary: retrieve charger's allowance based on authenticated user
      description:
        For a given PSL / PPID and authenticated user, return the charger's
        usage allowance
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Restrictions'
        '400':
          description: Returns Bad Request when a PPID is in an invalid format
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returns Not Found when a charger is not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a12
      security: *a13
  /chargers/{ppid}/connectivity-status:
    get:
      operationId: ChargersController_getStatus
      summary:
        retrieve charger's connectivity status and energy offer statuses based
        on authenticated user
      description: For a given PPID and authenticated user, return the charger's
        connectivity status and energy offer statuses
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
        - name: user-agent
          required: false
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerConnectivityStatus'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returns Not Found when a connectivity status is not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /chargers/{ppid}/flex-enrolment:
    get:
      operationId: ChargersController_getFlexEnrolment
      summary: retrieve charger's flex enrolment
      description:
        For a given PPID and authenticated user, return the charger's flex
        enrolment
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerFlexEnrolment'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
    delete:
      operationId: ChargersController_deleteFlexEnrolment
      summary: delete a charger's flex enrolment
      description:
        For a given PPID and authenticated user, delete the charger's flex
        enrolment
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '204':
          description: ''
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /chargers/{ppid}/flex-requests:
    get:
      operationId: ChargersController_getFlexRequests
      summary: retrieve a list of active flex requests for this charger
      description: For a given PPID and authenticated user, return the charger's
        active flex requests
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FlexRequest'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /chargers/{ppid}/flex-requests/{id}:
    delete:
      operationId: ChargersController_deleteFlexRequest
      summary: delete a flex request
      description: Delete a flex request for the given charger
      parameters:
        - name: id
          required: true
          in: path
          description: ID of the flex event to opt out of
          schema:
            example: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
            type: string
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
      responses:
        '204':
          description: ''
        '400':
          description: Invalid flex request UUID provided
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
      tags: *a12
      security: *a13
  /chargers/{ppid}/solar/preferences:
    get:
      operationId: ChargersController_getSolarPreferences
      summary: retrieve solar preferences for this charger
      description:
        For a given PPID and authenticated user, return the charger's solar
        preferences
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SolarPreferences'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Response when charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Response when the connectivity status is not found
          content:
            application/json:
              schema:
                example:
                  statuscode: 404
                  message:
                    No connectivity status data available - it may be offline or have a
                    poor connection
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                example:
                  statuscode: 422
                  message: Sensor not installed
      tags: *a12
      security: *a13
    post:
      operationId: ChargersController_setSolarPreferences
      summary: sets solar preferences for this charger
      description:
        For a given PPID and authenticated user, sets the charger's solar
        preferences
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SolarPreferences'
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SolarPreferences'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SolarPreferences'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Response when charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Response when the connectivity status is not found
          content:
            application/json:
              schema:
                example:
                  statuscode: 404
                  message:
                    No connectivity status data available - it may be offline or have a
                    poor connection
        '410':
          description: Response when the charger is offline
          content:
            application/json:
              schema:
                example:
                  statusCode: 410
                  message: Charger is offline
        '502':
          description: Response when cannot update the asset configuration
          content:
            application/json:
              schema:
                example:
                  statusCode: 502
                  message: Cannot update asset configuration
      tags: *a12
      security: *a13
  /chargers/{ppid}/model-info:
    get:
      operationId: ChargersController_getChargerModelInfo
      summary: get model information for a given charger
      description: For a given PPID, get information about the charger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerModelInfoResponse'
        '404':
          description: Thrown when no charger with the given PPID could be found
        '500':
          description: Thrown when an unknown error occurs
      tags: *a12
      security: *a13
  /chargers/{ppid}/charge-overrides:
    post:
      operationId: ChargersController_createChargerOverrides
      summary: create charge overrides for a given charger
      description: For a given PPID, create a charge override
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeOverrideRequestDTO'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChargeOverrideScheduleResponse'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChargeOverrideScheduleResponse'
        '404':
          description: Thrown when no charger with the given PPID could be found
        '500':
          description: Thrown when an unknown error occurs
      tags: *a12
      security: *a13
    get:
      operationId: ChargersController_getChargerOverrides
      summary: get charge overrides for a given charger
      description: For a given PPID, get the charge override
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChargeOverrideScheduleResponse'
        '404':
          description: Thrown when no charger with the given PPID could be found
        '500':
          description: Thrown when an unknown error occurs
      tags: *a12
      security: *a13
    delete:
      operationId: ChargersController_deleteChargeOverride
      summary: delete charge overrides for a given charger
      description: For a given PPID, delete the charge override
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: ''
        '404':
          description: Thrown when no charger with the given PPID could be found
        '500':
          description: Thrown when an unknown error occurs
      tags: *a12
      security: *a13
  /chargers/{ppid}/firmware:
    get:
      operationId: ChargersController_getChargerFirmware
      summary: retrieve firmware for a given charger
      description: For a given PPID, retrieve the firmware
      parameters:
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FirmwareStatusResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /chargers/{ppid}/tariffs:
    get:
      operationId: ChargersController_getChargerTariffs
      summary: retrieve tariff for a given charger
      description: For a given PPID, retrieve the charger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargingStationTariffSearchDto'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
    post:
      operationId: ChargersController_setChargerTariffs
      summary: set tariff for a given charger
      description: For a given PPID, set the charger's tariff
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetChargerTariffDto'
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerTariffDto'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /chargers/{ppid}/tariffs/{tariffId}:
    put:
      operationId: ChargersController_updateChargerTariff
      summary: ''
      description:
        Given a PPID and tariff ID, replace the tariff details with the
        provided details
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
        - name: tariffId
          required: true
          in: path
          description: Id of a given tariff
          schema:
            example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetChargerTariffDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargerTariffDto'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '409':
          description: Tariff start dates overlap
          content:
            application/json:
              schema:
                example:
                  statusCode: 409
                  message: Conflict
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a12
      security: *a13
    delete:
      operationId: ChargersController_deleteTariff
      summary: Delete tariff for a given charger
      description: For a given PPID and TariffId, delete the charger's tariff
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PP-12003
            type: string
        - name: tariffId
          required: true
          in: path
          description: Id of a given tariff
          schema:
            example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
            type: string
      responses:
        '204':
          description: No content response
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Charger not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a12
      security: *a13
  /smart-charging/delegated-controls/{ppid}/vehicles:
    post:
      operationId: SmartChargingController_captureVehicle
      summary: capture a vehicle for smart charging
      description: For a given PPID, capture a vehicle for smart charging
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleLinkRequestDtoImpl'
      responses:
        '201':
          description: Returned when vehicle successfully captured
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleLinkResponseDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Thrown when no charger with the given PPID could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a14
        - Smart Charging
      security: &a15
        - bearer: []
  /smart-charging/delegated-controls/{ppid}/vehicles/{vehicleId}:
    patch:
      operationId: SmartChargingController_updateVehicle
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
        - name: vehicleId
          required: true
          in: path
          description: ID of a given vehicle
          schema:
            example: f05569a5-4ca1-4b76-8285-948aa322ef46
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateVehicleLinkRequestDtoImpl'
      responses:
        '200':
          description: Returned when vehicle successfully updated
          content:
            application/json:
              schema:
                anyOf:
                  - $ref: '#/components/schemas/ConnectedStatefulVehicleDtoImpl'
                  - $ref: '#/components/schemas/GenericStatefulVehicleDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description:
            Thrown when no charger with the given PPID could be found OR no
            vehicle with the given ID could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a14
      security: *a15
    delete:
      operationId: SmartChargingController_deleteVehicle
      summary: Removes a given vehicle from a given charger
      description: For a given PPID, remove a given vehicle
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
        - name: vehicleId
          required: true
          in: path
          description: ID of a given vehicle
          schema:
            example: f05569a5-4ca1-4b76-8285-948aa322ef46
            type: string
      responses:
        '200':
          description: Returned when vehicle successfully removed
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description:
            Thrown when charging station not found, delegated control charging
            station not found, vehicle not found, or delegated control charging
            station vehicles record not found.
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a14
      security: *a15
  /smart-charging/delegated-controls/vehicles:
    get:
      operationId: SmartChargingController_getChargersAndVehicles
      summary: retrieve all vehicles and chargers for a user
      description: For a given user, get all chargers and the vehicles linked to them
      parameters: []
      responses:
        '200':
          description: Returned when vehicles successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChargersAndVehicles'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a14
      security: *a15
  /smart-charging/delegated-controls/{ppid}:
    get:
      operationId: SmartChargingController_getIntents
      summary: retrieve intents for a charger
      description: For a given PPID retrieve intents
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
      responses:
        '200':
          description: Returned when intents successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DelegatedControlChargingStationResponseDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Thrown when no charger with the given PPID could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a14
      security: *a15
    put:
      operationId: SmartChargingController_createDelegatedControlStation
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
      responses:
        '201':
          description: Request has been made to add charger to delegated control
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: Thrown when no charger with the given PPID could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '422':
          description:
            Charging station is unsupported or is missing one of evse, serial
            number, mac address
          content:
            application/json:
              schema:
                example:
                  statusCode: 422
                  message: Unprocessable
      tags: *a14
      security: *a15
    delete:
      operationId: SmartChargingController_removeChargerFromDelegatedControl
      parameters:
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description:
            Returned when the charger is successfully removed from Delegated
            Control
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description:
            Thrown when charging station not found, delegated control charging
            station not found, vehicle not found, or delegated control charging
            station vehicles record not found.
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a14
      security: *a15
  /smart-charging/delegated-controls/{ppid}/vehicles/{vehicleId}/intents:
    put:
      operationId: SmartChargingController_setVehicleIntents
      summary: Sets delegated control intents of a given charger and vehicle
      description: For a given PPID and vehicle, set delegated control intents
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-12003
            type: string
        - name: vehicleId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VehicleIntentsRequestDtoImpl'
      responses:
        '200':
          description: Returned when vehicle successfully captured
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetVehicleIntent'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description:
            Thrown when charging station not found, delegated control charging
            station not found, vehicle not found, or delegated control charging
            station vehicles record not found.
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a14
      security: *a15
  /charges:
    get:
      operationId: ChargesController_getCharges
      summary: retrieve charger sessions
      description: For given dates it should return charger sessions.
      parameters:
        - name: from
          required: true
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          schema:
            example: 2022-10-12
            type: string
        - name: to
          required: true
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-02'
          schema:
            example: 2022-10-19
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargesResponse'
        '400':
          description: Returns bad request when from or to are not dates.
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a16
        - Charges
      security: &a17
        - bearer: []
  /charges/stats:
    get:
      operationId: ChargesController_getChargesStats
      summary: retrieve charger stats
      description: For given dates and inteval it should return charger stats.
      parameters:
        - name: from
          required: false
          in: query
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          schema:
            example: 2022-10-12
            type: string
        - name: to
          required: false
          in: query
          description: 'Statistics report inclusive end date eg: 2022-01-02'
          schema:
            example: 2022-10-19
            type: string
        - name: interval
          required: true
          in: query
          description: Time duration interval data should be provided in.
          schema:
            example: day
            enum:
              - day
              - week
              - month
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeStatsResponse'
        '400':
          description: Returns bad request when from or to are not dates.
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a16
      security: *a17
  /check-for-upgrade:
    get:
      operationId: CheckForUpgradeController_checkForUpgrade
      summary: 'returns whether an app version needs to be upgraded or not '
      description: For a given version number returns whether a new update is
        available and upgrade is required
      parameters:
        - required: true
          description: The app name
          name: app_name
          in: query
          schema:
            example: Pod Point
            type: string
        - required: true
          description: The app version
          name: app_version
          in: query
          schema:
            example: 3.14.1
            type: string
        - required: true
          description: The app platform
          name: platform
          in: query
          schema:
            example: android
            type: string
        - required: true
          description: The app environment
          name: environment
          in: query
          schema:
            example: production
            type: string
        - required: true
          description: The app language
          name: app_language
          in: query
          schema:
            example: en
            type: string
        - name: x-api-key
          in: header
          description: x-api-key for your project
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckForUpgrade'
        '400':
          description:
            Returns bad request response when x-api-key is missing from headers
            or when mandatory GET parameters are missing
          content:
            application/json:
              schema:
                example:
                  statuscode: 400
                  message: x-api-key is required
        '401':
          description: Returns unauthorized response when x-api-key is invalid
          content:
            application/json:
              schema:
                example:
                  statuscode: 401
                  message: Invalid x-api-key
      tags:
        - Check For Upgrade
  /energy/suppliers:
    get:
      operationId: EnergyController_getSuppliers
      summary: get a list of suppliers
      parameters: []
      responses:
        '200':
          description: Returns a list of energy suppliers
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SupplierDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Energy
      security:
        - bearer: []
  /expenses/groups/{organisationId}:
    post:
      operationId: ExpensesController_expenseChargeTo
      summary: Submitting a list of charges as expenses for a driver within a group.
      description: Submitting a list of charges as expenses for a driver within a group.
      parameters:
        - name: organisationId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExpensesRequestBody'
      responses:
        '201':
          description: Created response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExpensesResponse'
        '400':
          description: Response when Driver ID, Group ID or Expenses payload is malformed.
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '406':
          description: Response when organisationId is not a number
          content:
            application/json:
              schema:
                example:
                  statusCode: 406
                  message: Validation failed (numeric string is expected)
      tags:
        - Expenses
      security:
        - bearer: []
  /health:
    get:
      operationId: HealthController_check
      summary: get mobile API health
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &a18
                      database: &a19
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *a18
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *a18
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &a20
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *a19
                      redis: *a20
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      tags:
        - Healthcheck
  /:
    get:
      operationId: HelloController_getData
      summary: welcome message endpoint
      parameters: []
      responses:
        '200':
          description: Welcome message returned
      tags:
        - Example
  /location/search:
    get:
      operationId: LocationController_search
      summary: search for an address
      description: Partial address returned.
      parameters:
        - name: text
          required: true
          in: query
          description:
            The search text to find. Ideally a postcode or the start of the
            address.
          schema:
            type: string
        - name: containerId
          required: false
          in: query
          description:
            A container for the search. This should only be another Id
            previously returned from this service when the Type of the result
            was not "Address".
          schema:
            type: string
        - name: language
          required: false
          in: query
          description:
            The preferred language for results. This should be a 2 or 4
            character language code e.g. (en, fr, en-gb, en-us etc). Falls back
            to Accept-Language header if not set and defaults to "en" if neither
            are present.
          schema:
            type: string
        - name: limit
          required: false
          in: query
          description: The maximum number of results to return.
          schema:
            type: number
        - name: Accept-Language
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Item'
        '400':
          description: >-
            Bad Request

                message                           | error
                ----------------------------------|----------------------------------------------------------------
                1001 <USER> <GROUP> or Container Required | The Text or Container parameters were not supplied.
                1002 - Text or Container Invalid  | The Text or Container parameter was not recognised.
                1004 - Language Invalid           | The Language parameter was not recognised.
                1005 - No response                | The query didn't respond fast enough, it may be too complex.
                1006 - Invalid Input              | Bad input detected.
                1008 - Unauthorised Dataset       | A dataset was requested that is not authorised on this account.
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                  message:
                    type: string
                  error:
                    type: string
                example:
                  statuscode: 400
                  message: 1001 - Text or Container Required
                  error: The Text or Container parameters were not supplied.
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a21
        - Location
      security: &a22
        - bearer: []
  /location/find:
    get:
      operationId: LocationController_find
      summary: find the address
      description: Returns the full address details based on the Id.
      parameters:
        - name: id
          required: true
          in: query
          description: The ID from a search result to retrieve the details for.
          schema:
            example:
              id: GB|RM|A|8182563
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Address'
        '400':
          description: >-
            Bad Request

                message                 | error
                ------------------------|----------------------------------------------------------------------------------------------------
                1001 <USER> <GROUP> Invalid       | The Id parameter supplied was invalid.
                1002 - License Required | This record contains data that requires an additional license and is not available on your account.
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                  message:
                    type: string
                  error:
                    type: string
                example:
                  statuscode: 400
                  message: 1001 - Id Invalid
                  error: The Id parameter supplied was invalid.
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a21
      security: *a22
  /payments/setup-intent:
    post:
      operationId: PaymentController_setupIntent
      summary: create setup intent for payment
      description: Creates setup intent for payment
      parameters: []
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateIntentResponse'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateIntentResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
      tags: &a23
        - Payments
  /payments/create-payment-intent:
    post:
      operationId: PaymentController_createPaymentIntent
      summary: create setup intent for payment
      description: Creates setup intent for payment
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentRequest'
      responses:
        '201':
          description: Created response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRegisteredUserPaymentResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
      tags: *a23
      security:
        - bearer: []
  /payments/webhook:
    post:
      operationId: PaymentController_webhook
      parameters:
        - name: stripe-signature
          required: true
          in: header
          schema:
            type: string
      responses:
        '201':
          description: ''
      tags: *a23
  /remote-lock/{ppid}:
    get:
      operationId: RemoteLockController_getRemoteLockForCharger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-123456
            type: string
      responses:
        '200':
          description: Returns the status of remote lock for a given charger
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteLockDTO'
        '401':
          description: Returned when the request is not authenticated
        '403':
          description: Returned when the user does not own the given charger
        '404':
          description: Returned when the given charger is not found
        '500':
          description: Returned when an unknown error occurs
      tags: &a24
        - Remote Lock
      security: &a25
        - bearer: []
    post:
      operationId: RemoteLockController_setRemoteLockForCharger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoteLockDTO'
      responses:
        '200':
          description: Returns the status of remote lock for a given charger
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoteLockDTO'
        '401':
          description: Returned when the request is not authenticated
        '403':
          description: Returned when the user does not own the given charger
        '404':
          description: Returned when the given charger is not found
        '500':
          description: Returned when an unknown error occurs
        '501':
          description: Returned for chargers which do not support Remote Lock
      tags: *a24
      security: *a25
  /reports/charges:
    post:
      operationId: ReportsController_createReport
      summary: Request a reports of charges
      description: For given dates it should generate a report of charges in CSV format.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportsDtoPayload'
      responses:
        '200':
          description: OK response
        '201':
          description: ''
        '400':
          description: Returns bad request when from or to are not dates.
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Reports
      security:
        - bearer: []
  /reward-wallet:
    get:
      operationId: RewardWalletController_getRewardWallet
      summary: ''
      description: Get the reward wallet for the authenticated user
      parameters: []
      responses:
        '200':
          description: The user's reward wallet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RewardWalletDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returned if a wallet is not found for the user
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a26
        - Reward Wallet
      security: &a27
        - bearer: []
  /reward-wallet/payout:
    post:
      operationId: RewardWalletController_payoutRewardWallet
      summary: Payout the current contents of the reward wallet
      description: Payout the current contents of the reward wallet
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RewardWalletPayoutDTO'
      responses:
        '201':
          description: The payout was successfully created
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a26
      security: *a27
  /chargers/arch5/{ppid}:
    get:
      operationId: SsidPasswordController_getWifiCredentials
      summary: retrieve wifi credentials of arch5 charger
      description: For a given PSL / PPID of an arch5 charger, return the wifi credentials.
      parameters:
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiCredentials'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returns not found when no credentials have been found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags:
        - Chargers
      security:
        - bearer: []
  /support/feedback:
    post:
      operationId: SupportController_submitFeedback
      summary: submit feedback to support
      description: Sends an email to the language-specific support team
      parameters:
        - name: Accept-Language
          required: true
          in: header
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitSupportFeedbackDTO'
      responses:
        '200':
          description: ''
        '201':
          description: ''
        '400':
          description: ''
        '500':
          description: ''
      tags:
        - Support
  /loyalty-cards/tesco:
    get:
      operationId: TescoClubcardController_getClubcard
      summary: retrieve a tesco clubcard
      description: For the authenticated user, return their tesco clubcard
      parameters: []
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TescoClubcardResponse'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a28
        - Tesco
      security: &a29
        - bearer: []
    post:
      operationId: TescoClubcardController_createClubcard
      summary: create a tesco clubcard
      description: For the authenticated user, create a tesco clubcard
      parameters: []
      requestBody:
        required: true
        description: The request body for creating a tesco clubcard
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TescoClubcardResponse'
      responses:
        '201':
          description: Created response
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a28
      security: *a29
    delete:
      operationId: TescoClubcardController_deleteClubcard
      summary: delete a tesco clubcard
      description: For the authenticated user, delete their tesco clubcard
      parameters: []
      responses:
        '204':
          description: No content
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a28
      security: *a29
  /linky/{ppid}:
    get:
      operationId: LinkyController_getLinkyStatusForCharger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            example: PSL-123456
            type: string
      responses:
        '200':
          description: Returns the status of Linky for a given charger
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LinkyDTO'
        '401':
          description: Returned when the request is not authenticated
        '403':
          description: Returned when the user does not own the given charger
        '404':
          description: Returned when the given charger is not found
        '500':
          description: Returned when an unknown error occurs
      tags: &a30
        - Linky
      security: &a31
        - bearer: []
    post:
      operationId: LinkyController_setLinkyStatusForCharger
      parameters:
        - name: ppid
          required: true
          in: path
          description: PPID of a given charger
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetLinkyDTO'
      responses:
        '200':
          description: Returns the status of Linky for a given charger
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LinkyDTO'
        '401':
          description: Returned when the request is not authenticated
        '403':
          description: Returned when the user does not own the given charger
        '404':
          description: Returned when the given charger is not found
        '500':
          description: Returned when an unknown error occurs
        '501':
          description: Returned when a charger is not Linky capable
      tags: *a30
      security: *a31
  /vehicles/{vehicleId}/interventions:
    get:
      operationId: VehiclesController_getAllInterventions
      summary: get all interventions for a given vehicle
      description: For a given vehicle, get all interventions
      parameters:
        - name: vehicleId
          required: true
          in: path
          description: ID of the Vehicle
          schema:
            example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      responses:
        '200':
          description: Returns all the vehicle interventions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleInterventionResponseDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: No matching vehicle could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '422':
          description: Returned when requested for a non-Enode vehicle
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a32
        - Vehicles
      security: &a33
        - bearer: []
  /vehicles/{vehicleId}/interventions/{interventionId}:
    get:
      operationId: VehiclesController_getIntervention
      summary: get a specific intervention
      description: For a given intervention, get its information
      parameters:
        - name: vehicleId
          required: true
          in: path
          description: vehicleId of the vehicle
          schema:
            example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
            type: string
        - name: interventionId
          required: true
          in: path
          description: ID of the intervention
          schema:
            example: 3fa85f64-5717-4562-b3fc-2c963f66afa7
            type: string
        - name: Accept-Language
          in: header
          description: Indicate the preferred language for the response (see
            https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
          schema:
            type: string
            default: en
      responses:
        '200':
          description: Returns the given vehicle intervention
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VehicleInterventionDtoImpl'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: No matching intervention or vehicle could be found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '422':
          description: Returned when requested for a non-Enode vehicle
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a32
      security: *a33
  /version:
    get:
      operationId: VersionController_getVersion
      summary: get application version
      parameters: []
      responses:
        '200':
          description: application version
      tags:
        - Version
  /marketing/opportunities/{ppid}:
    get:
      operationId: MarketingController_getOpportunitiesForCharger
      summary: gets marketing opportunities for a given charger
      description: For a given charger, get its marketing opportunties
      parameters:
        - name: ppid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketingOpportunitiesDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '403':
          description: Charger is not linked to this account
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
      tags:
        - Marketing
  /subscriptions:
    get:
      operationId: SubscriptionsController_getSubscriptions
      summary: ''
      description: Lists all the subscriptions associated with the user
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionListDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a34
        - Subscriptions
      security: &a35
        - bearer: []
  /subscriptions/{subscriptionId}/documents:
    get:
      operationId: SubscriptionsController_getSubscriptionDocuments
      summary: ''
      description: Lists all documents associated with a subscription
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Retrieved subscription documents
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDocumentsDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
  /subscriptions/{subscriptionId}/documents/{documentId}:
    get:
      operationId: SubscriptionsController_getSubscriptionDocument
      summary: Get subscription document
      description: Downloads the PDF file of a given document
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
        - name: documentId
          required: true
          in: path
          description: The ID of the document
          schema:
            type: string
      responses:
        '200':
          description: ''
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returned when subscription or document does not exist
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
  /subscriptions/{subscriptionId}/direct-debit:
    get:
      operationId: SubscriptionsController_getSubscriptionDirectDebit
      summary: ''
      description: Retrieves direct debit details associated with a subscription
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Retrieved subscription direct debit details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDirectDebitDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returned when direct debit details are unavailable
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
  /subscriptions/{subscriptionId}/actions/{actionId}:
    patch:
      operationId: SubscriptionsController_updateSubscriptionAction
      summary: ''
      description: Updates a given action of a subscription
      parameters:
        - name: subscriptionId
          required: true
          in: path
          schema:
            type: string
        - name: actionId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/AffordabilityActionDTO'
                - $ref: '#/components/schemas/SetupDirectDebitActionDTO'
                - $ref: '#/components/schemas/UpdateHomeSurveyActionDTO'
                - $ref: '#/components/schemas/UpdateSignDocumentsActionDTO'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SubscriptionCheckAffordabilityActionDTO'
                  - $ref: '#/components/schemas/SubscriptionSetupDirectDebitActionDTO'
                  - $ref: '#/components/schemas/SubscriptionSurveyActionDTO'
                  - $ref: '#/components/schemas/SubscriptionSignDocumentsActionDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Returned if the user does own a subscription with the given ID
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
    get:
      operationId: SubscriptionsController_getSubscriptionActionById
      summary: ''
      description: Get a subscription action by id
      parameters:
        - name: subscriptionId
          required: true
          in: path
          schema:
            type: string
        - name: actionId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SubscriptionSurveyActionDTO'
                  - $ref: '#/components/schemas/SubscriptionInstallChargingStationActionDTO'
                  - $ref: '#/components/schemas/SubscriptionCheckAffordabilityActionDTO'
                  - $ref: '#/components/schemas/SubscriptionSetupDirectDebitActionDTO'
                  - $ref: '#/components/schemas/SubscriptionSignDocumentsActionDTO'
                  - $ref: '#/components/schemas/SubscriptionPayUpfrontFeeActionDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Subscription action not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
      tags: *a34
      security: *a35
  /subscriptions/actions/SETUP_DIRECT_DEBIT_V1/confirmation-of-payee:
    post:
      operationId: SubscriptionsController_doConfirmationOfPayeeCheck
      summary: ''
      description: Performs a confirmation of payee check
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionConfirmationOfPayeeDTO'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionConfirmationOfPayeeResponse'
        '400':
          description: ''
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
  /subscriptions/{id}:
    get:
      operationId: SubscriptionsController_getSubscriptionById
      summary: ''
      description: Get a subscription by id
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDTO'
        '400':
          description: Subscription ID is not a UUID
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '404':
          description: Subscription not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a34
      security: *a35
  /subscriptions/actions/CHECK_AFFORDABILITY_V1/form-data:
    get:
      operationId: SubscriptionsController_getCheckAffordabilityFormData
      summary: ''
      description: Get the form data for check affordability action
      parameters: []
      responses:
        '200':
          description: Returns the form data for check affordability action
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormDataDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
      tags: *a34
      security: *a35
  /subscriptions/actions/SETUP_DIRECT_DEBIT_V1/form-data:
    get:
      operationId: SubscriptionsController_getSetupDirectDebitFormData
      summary: ''
      description: Get the form data for setup direct debit action
      parameters: []
      responses:
        '200':
          description: Returns the form data for setup direct debit action
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormDataDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
      tags: *a34
      security: *a35
  /warranties/{ppid}:
    get:
      operationId: WarrantiesController_getWarranty
      summary: ''
      description: Retrieves the warranty associated with a given PPID
      parameters:
        - name: ppid
          required: true
          in: path
          description: The PPID to retrieve the warranty for
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WarrantyDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Warranties
      security:
        - bearer: []
  /payouts/bank-accounts:
    get:
      operationId: PayoutsController_getBankAccounts
      summary: Get bank accounts
      description: Gets bank accounts belonging to the authenticated user
      parameters: []
      responses:
        '200':
          description: A list of bank accounts belonging to the user
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BankAccountResponseDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a36
        - Payouts
      security: &a37
        - &a38
          bearer: []
    post:
      operationId: PayoutsController_createBankAccount
      summary: Create bank account
      description: Creates a bank account and co-responding recipient
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBankAccountDTO'
      responses:
        '201':
          description: The created bank account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponseDTO'
        '400':
          description: ''
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '422':
          description: Thrown if confirmation of payee fails
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountCOPFailedDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a36
      security: *a37
  /payouts/bank-accounts/{bankAccountId}:
    get:
      operationId: PayoutsController_getBankAccount
      summary: Get specific bank account
      description:
        Gets a given bank account belonging to the authenticated user based
        on its ID
      parameters:
        - name: bankAccountId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The given bank account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponseDTO'
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a36
      security: *a37
    delete:
      operationId: PayoutsController_deleteBankAccount
      summary: Delete bank account belonging to the authenticated user
      description: Deletes bank account belonging to the authenticated user
      parameters:
        - name: bankAccountId
          required: true
          in: path
          description: The id of the bank account to be deleted
          schema:
            type: string
      responses:
        '204':
          description: Returned on successful deletion of the bank account
        '401':
          description: Unauthorised request
          content:
            application/json:
              schema:
                example:
                  statusCode: 401
                  message: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a36
      security:
        - *a38
        - bearer: []
info:
  title: Mobile API
  description: Mobile Backend for Frontend API service
  version: '1.0'
  contact: {}
tags:
  - name: Carbon Intensity
    description:
      API which mirrors certain aspects of the Carbon Intensity API from
      the National Grid - https://carbonintensity.org.uk
  - name: Location
    description: API to capture and verify addresses
  - name: Chargers
    description: API to return data associated with a Pod Point charger
  - name: Check For Upgrade
    description: API to return whether an app version needs to be upgraded or not
  - name: Driver Account
    description: API for administration of authenticated users
  - name: API3
    description: API that proxies API3
servers: []
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      type: http
  schemas:
    MarketingDto:
      type: object
      properties:
        isConsentGiven:
          type: number
          example: 0
        type:
          type: string
          example: express
        copy:
          type: string
          example:
            I would like to receive updates about Pod Point products and services
            by email (and know that I can update my preferences from within any
            of the emails if I change my mind)
        origin:
          type: string
          example: opencharge-mobile-app
      required:
        - isConsentGiven
        - type
        - copy
        - origin
    ConsentDto:
      type: object
      properties:
        marketing:
          description: Marketing
          allOf:
            - $ref: '#/components/schemas/MarketingDto'
      required:
        - marketing
    PreferencesDto:
      type: object
      properties:
        unitOfDistance:
          type: string
          example: mi
      required:
        - unitOfDistance
    CreateUserPayload:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
        email:
          type: string
          description: Email
          example: <EMAIL>
        password:
          type: string
          description: password
          example: password1234
        consent:
          $ref: '#/components/schemas/ConsentDto'
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
      required:
        - first_name
        - last_name
        - locale
        - email
    CreateUserResponseDto:
      type: object
      properties:
        auth_id:
          type: string
          description: GIP userId
          example: fa95717e-cf98-4d2e-86cf-c12f580921f3
        created_at:
          type: string
          example: 2023-10-02 12:27:19
        id:
          type: string
          description: pk of user
          example: '910004932'
        password_reset_link:
          type: string
          description: password reset link
      required:
        - auth_id
        - created_at
        - id
    ResetPasswordRequestDto:
      type: object
      properties:
        email:
          type: string
          description: Email
        reset_password_continue_url:
          type: string
          description: Continue url
      required:
        - email
    SendVerifyAndChangeEmailRequest:
      type: object
      properties:
        newEmail:
          type: string
          description: New Email
      required:
        - newEmail
    UpdateUserDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
      required:
        - first_name
        - last_name
    UserDetailsDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
      required:
        - first_name
        - last_name
        - locale
    SendRecoverFactorRequest:
      type: object
      properties:
        email:
          type: string
          description: Email
        recover_factor_continue_url:
          type: string
          description: Redirect URL after factor recovery
      required:
        - email
    SignInWithMagicLinkDto:
      type: object
      properties:
        email:
          type: string
          description: The email to send the magic link to
      required:
        - email
    BalanceDto:
      type: object
      properties:
        currency:
          type: string
          example: GBP
        amount:
          type: number
          example: 500
      required:
        - currency
        - amount
    RewardsChargerDto:
      type: object
      properties:
        id:
          type: string
          description: The PPID of the charger
          example: PSL-12345
        miles:
          type: number
          description: The amount of rewardable miles for this charger
          example: 10.5
      required:
        - id
        - miles
    RewardsDto:
      type: object
      properties:
        totalMiles:
          type: number
          description: The total amount of reward miles from individual chargers
          example: 10.5
        balance:
          type: number
          description: The balance in the lowest unit of currency (pence for GBP)
          example: 5000
        currency:
          type: string
          description: The currency represented by the balance
          example: GBP
        payoutThreshold:
          type: number
          description: The minimum amount of rewards miles required for payout
          example: 150
        chargers:
          description: The chargers for which the user is eligible for the reward
          type: array
          items:
            $ref: '#/components/schemas/RewardsChargerDto'
      required:
        - totalMiles
        - balance
        - currency
        - payoutThreshold
        - chargers
    ExtendedUserInfoResponseDto:
      type: object
      properties:
        first_name:
          type: string
          description: First name
          example: John
        last_name:
          type: string
          description: Last name
          example: Doe
        locale:
          type: string
          description: Locale
          example: en
        email:
          type: string
          description: Email
          example: <EMAIL>
        uid:
          type: string
          description: auth id
          example: a3450f38-217a-4d0c-8eec-b7d63c6fd2e0
        preferences:
          $ref: '#/components/schemas/PreferencesDto'
        balance:
          $ref: '#/components/schemas/BalanceDto'
        rewards:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/RewardsDto'
        paymentProcessorId:
          type: string
          nullable: true
          example: cus_hfJJeHsFdaV2H3s
        emailVerified:
          type: boolean
        lastSignInTimestamp:
          format: date-time
          oneOf:
            - type: 'null'
            - type: string
              example: 1995-07-16T13:46:06.0Z
        accountCreationTimestamp:
          type: string
          example: 1995-07-16T13:46:06.0Z
        status:
          type: string
          enum:
            - active
            - disabled
        deletedAtTimestamp:
          type: string
          example: 1995-07-16T13:46:06.0Z
          nullable: true
      required:
        - first_name
        - last_name
        - locale
        - email
        - uid
        - balance
        - rewards
        - paymentProcessorId
        - emailVerified
        - lastSignInTimestamp
        - accountCreationTimestamp
        - status
        - deletedAtTimestamp
    GetLinkSessionRequest:
      type: object
      properties:
        vendor:
          type: string
          example: VOLVO
          description:
            By specifying a vendor, the brand selection step in Link UI will be
            skipped. Instead, your user will go directly to the service
            selection view (if applicable for the specified vendor), or to the
            review data access step.
        enodeUserId:
          type: string
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          description:
            A unique identifier identifying the user to be used with the link
            session generated at the capture vehicle stage
    GetLinkSessionResponse:
      type: object
      properties:
        linkToken:
          type: string
          example: WFiMjNjZDRANThlZmIyMzMtYTNjNS00Njk...
        linkUrl:
          type: string
          example: https://example.com
      required:
        - linkToken
        - linkUrl
    TrackLoginRequest:
      type: object
      properties:
        ipAddress:
          type: string
          example: 2a00:23ee:1320:109f:98e0:dcc6:b917:f126
        userAgent:
          type: string
          example:
            FirebaseAuth.iOS/8.15.0 com.podpoint.podpoint/3.26.0 iPhone/17.5.1
            hw/iPhone14_5,gzip(gfe),gzip(gfe)
        timestamp:
          type: string
          example: 2020-03-14 00:00:00
        authId:
          type: string
          example: 5357be96-1495-4951-8046-c2d59ba76c44
      required:
        - ipAddress
        - userAgent
        - timestamp
    FcmTokenDtoImpl:
      type: object
      properties:
        token:
          type: string
          description: The notification token you want to save
          example: d7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l
        timestamp:
          type: string
          description: The timestamp at which the notification token was created
          example: 2024-10-24T13:53:00.000Z
      required:
        - token
        - timestamp
    RewardsAccountAddressDTO:
      type: object
      properties:
        city:
          type: string
          description: The city of the address
        country:
          type: string
          description: The country of the address
        county:
          type: string
          description: The county of the address
        line1:
          type: string
          description: The first line of the address
        line2:
          type: string
          description: The second line of the address
        postcode:
          type: string
          description: The post code of the address
      required:
        - city
        - country
        - county
        - line1
        - postcode
    RewardsAccountDTO:
      type: object
      properties:
        firstName:
          type: string
          description: The first name of the person who owns the bank account
        lastName:
          type: string
          description: The last name of the person who owns the bank account
        address:
          description: The address of the person who owns the bank account
          allOf:
            - $ref: '#/components/schemas/RewardsAccountAddressDTO'
        accountNumber:
          type: string
          description: The account number of the bank account
        sortCode:
          type: string
          description: The sort code of the bank account
      required:
        - firstName
        - lastName
        - address
        - accountNumber
        - sortCode
    RewardsBankAccountDTOImpl:
      type: object
      properties:
        id:
          type: string
          description: The ID of the bank account
        name:
          type: string
          description: The name of the person attached to the bank account
        last4:
          type: string
          description: The last 4 digits
      required:
        - id
        - name
        - last4
    ConfirmationOfPayeeDTO:
      type: object
      properties:
        status:
          type: string
          description: The status of the confirmation of payee check
          enum:
            - MATCH
            - MISMATCH
            - PARTIAL_MATCH
            - UNAVAILABLE
        provided:
          type: string
          description: The name provided
          example: Jenny Rosen
        suggested:
          type: string
          nullable: true
          description:
            The suggested name associated with the bank account if status is
            PARTIAL
          example: Jennifier Rosen
      required:
        - status
        - provided
        - suggested
    RewardsTransactionDTOImpl:
      type: object
      properties:
        id:
          type: string
          description: The ID of the transaction
        milesClaimed:
          type: number
          description: The amount of miles claimed in this transaction
        rate:
          type: number
          description: The rate that was applied to convert miles to balance
        value:
          type: number
          description:
            The value of the transaction in the lowest currency unit (pence for
            GBP)
        currency:
          type: string
          description: The currency that the transaction is in
        status:
          type: string
          description: The current status of the transaction
          enum:
            - PENDING
            - CANCELLED
            - RETURNED
            - POSTED
            - FAILED
        createdAt:
          type: string
          description: The date/time this payment was made
      required:
        - id
        - milesClaimed
        - rate
        - value
        - currency
        - status
        - createdAt
    Buffer:
      type: object
      properties: {}
    User:
      type: object
      properties:
        email:
          type: string
        first_name:
          type: string
        hasHomeCharge:
          type: number
        id:
          type: number
        last_name:
          type: string
        role:
          type: string
        locale:
          type: string
        tariff:
          type: object
        unit:
          type: object
        vehicle:
          type: object
        account:
          type: object
        preferences:
          type: array
          items:
            type: object
        notifications:
          type: array
          items:
            type: object
      required:
        - email
        - first_name
        - id
        - last_name
        - role
        - locale
    UserResponse:
      type: object
      properties:
        users:
          $ref: '#/components/schemas/User'
      required:
        - users
    FirmwareStatusResponseManifestType:
      type: object
      properties:
        architecture:
          type: string
          enum:
            - arch1
            - arch2
            - arch2.4
            - arch3
            - arch5
        createdDate:
          type: string
          nullable: true
        details:
          type: object
          properties:
            rfidVersion:
              type: string
            dspVersion:
              type: string
            wifiVersion:
              type: string
        manifestId:
          type: string
          nullable: true
        status:
          type: string
          enum:
            - candidate
            - alpha
            - beta
            - release
            - archived
      required:
        - architecture
        - details
    FirmwareStatusResponseUpdateStatus:
      type: object
      properties:
        isUpdateAvailable:
          type: boolean
        status:
          type: string
          enum:
            - NOT_REQUESTED
            - NOT_ACCEPTED
            - IN_PROGRESS
            - FAILED
        updateId:
          type: string
        updateVersion:
          $ref: '#/components/schemas/FirmwareStatusResponseManifestType'
      required:
        - isUpdateAvailable
    FirmwareStatusResponseData:
      type: object
      properties:
        serialNumber:
          type: string
        versionInfo:
          $ref: '#/components/schemas/FirmwareStatusResponseManifestType'
        updateStatus:
          $ref: '#/components/schemas/FirmwareStatusResponseUpdateStatus'
      required:
        - serialNumber
        - versionInfo
        - updateStatus
    FirmwareStatusResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/FirmwareStatusResponseData'
      required:
        - data
    Tier:
      type: object
      properties:
        rate:
          type: number
        begin_time:
          type: string
        end_time:
          type: string
      required:
        - rate
    TariffRequest:
      type: object
      properties:
        user_id:
          type: number
          description: User Id
        energy_supplier_id:
          type: number
          nullable: true
          description: Energy supplier id
        tiers:
          description: A list of tarrifs
          type: array
          items:
            $ref: '#/components/schemas/Tier'
      required:
        - user_id
        - energy_supplier_id
        - tiers
    TariffResponse:
      type: object
      properties:
        id:
          type: number
        name:
          type: string
        energy_supplier_id:
          type: number
        currency:
          type: string
      required:
        - id
        - name
        - energy_supplier_id
        - currency
    LocaleResponse:
      type: object
      properties:
        data:
          nullable: true
          type: array
          items:
            type: object
        meta:
          type: object
      required:
        - meta
    ChargeRequestDTO:
      type: object
      properties:
        user:
          type: number
        pod:
          type: number
        door:
          type: number
        claimed_by:
          type: number
      required:
        - user
        - pod
        - door
    ChargeRequestResponse:
      type: object
      properties:
        id:
          type: number
      required:
        - id
    AccountTopUpRequestDTO:
      type: object
      properties:
        id:
          type: number
        currency:
          type: string
        amount:
          type: number
        card_id:
          type: string
        token:
          type: string
      required:
        - id
        - currency
        - amount
    Intensity:
      type: object
      properties:
        forecast:
          type: number
          description: The forecast Carbon intensity (gCO2/kWh) for the current half hour.
          example: 266
        index:
          type: string
          description:
            The forecast Carbon intensity (gCO2/kWh) index for the current half
            hour.
          example: moderate
      required:
        - forecast
        - index
    Period:
      type: object
      properties:
        from:
          type: string
          description:
            Start time in the format hh:mm. Time is based on a 24 hour system.
            Seconds are not given.
          example: 12:00
        to:
          type: string
          description:
            End time in the format hh:mm. Time is based on a 24 hour system.
            Seconds are not given.
          example: 12:30
        intensity:
          description: The carbon intensity for current half hour.
          allOf:
            - $ref: '#/components/schemas/Intensity'
      required:
        - from
        - to
        - intensity
    Entry:
      type: object
      properties:
        date:
          type: string
          description: Carbon intensity entries for the specified date
          example: 2023-04-13
        entries:
          description: The carbon intensity entries for a half hour period.
          type: array
          items:
            $ref: '#/components/schemas/Period'
      required:
        - date
        - entries
    Forecastdata:
      type: object
      properties:
        regionid:
          type: number
          description: The DNO region's ID.
          example: 3
        dnoregion:
          type: string
          description: The DNO region.
          example: Electricity North West
        shortname:
          type: string
          description: The DNO region's shortname.
          example: North West England
        dates:
          description: List of carbon intensity for 30min periods.
          type: array
          items:
            $ref: '#/components/schemas/Entry'
      required:
        - regionid
        - dnoregion
        - shortname
        - dates
    Forecast:
      type: object
      properties:
        data:
          description: The carbon intensity forecast data.
          allOf:
            - $ref: '#/components/schemas/Forecastdata'
      required:
        - data
    ForecastSnapshot:
      type: object
      properties:
        regionid:
          type: number
          description: The DNO region's ID.
          example: 3
        dnoregion:
          type: string
          description: The DNO region.
          example: Electricity North West
        shortname:
          type: string
          description: The DNO region's shortname.
          example: North West England
        data:
          description: Carbon intensity for 30min period.
          allOf:
            - $ref: '#/components/schemas/Period'
      required:
        - regionid
        - dnoregion
        - shortname
        - data
    DnoRegion:
      type: object
      properties:
        regionid:
          type: number
          description: The DNO region's Id.
          example: 3
        dnoregion:
          type: string
          description: The DNO region.
          example: Electricity North West
        shortname:
          type: string
          description: The DNO region's shortname.
          example: North West England
      required:
        - regionid
        - dnoregion
        - shortname
    DnoRegions:
      type: object
      properties:
        data:
          description: The list of available DNO regions
          type: array
          items:
            $ref: '#/components/schemas/DnoRegion'
      required:
        - data
    ChargerDelegatedControlResponse:
      type: object
      properties:
        status:
          type: string
          description: The delegated control status of this charger
          enum: &a40
            - UNKNOWN
            - ACTIVE
            - INACTIVE
            - PENDING
      required:
        - status
    ChargerModelInfoResponse:
      type: object
      properties:
        ledColourSet:
          type: string
          enum:
            - uk
            - eu
          description: The LED colour set used on the charger
        style:
          type: string
          enum:
            - solo
            - solo3
            - solo3s
          description: The style of charger
        colour:
          type: string
          enum:
            - white
            - black
          description: The colour of the charger
        architecture:
          type: string
          example: '5.0'
          description: The architecture of the charger
      required:
        - ledColourSet
        - style
        - colour
        - architecture
    ChargerResponse:
      type: object
      properties:
        ppid:
          type: string
          description: The PPID of the charger
          example: PSL-123456
        unitId:
          type: number
          description: The unit id of the charger
          example: 1
        timezone:
          type: string
          description: The timezone of the charger
          example: Etc/UTC
        linkedAt:
          type: string
          description: When the charger was linked to the user
          example: 2024-01-01T00:00:00.000Z
        delegatedControl:
          description: The delegated control information for the charger
          allOf:
            - $ref: '#/components/schemas/ChargerDelegatedControlResponse'
        modelInfo:
          description: The model information for the charger
          allOf:
            - $ref: '#/components/schemas/ChargerModelInfoResponse'
      required:
        - ppid
        - unitId
        - timezone
        - linkedAt
        - delegatedControl
        - modelInfo
    Region:
      type: object
      properties:
        regionid:
          type: number
          description: The DNO region's ID.
          example: 3
      required:
        - regionid
    Restrictions:
      type: object
      properties:
        chargeAllowed:
          type: boolean
          description: Boolean for whether the user is allowed to charge
          example: true
        minimumBalance:
          type: object
          description: Minimum balance required to use the charger
          example:
            amount: 30
            currency: GBP
        userBalance:
          type: object
          description: The user's current balance
          example:
            amount: 30
            currency: GBP
        chargeLimits:
          description: The amount of charge that can be used before stopping
          example:
            - amount: 10
              type: energy
              unit: kWh
          type: array
          items:
            type: string
      required:
        - chargeAllowed
        - minimumBalance
        - userBalance
    EvseComponent:
      type: object
      properties:
        id:
          type: number
        architecture:
          type: string
        connectivityState:
          type: object
        connectors:
          type: array
          items:
            type: object
        energyOfferStatus:
          type: object
        serialNumber:
          type: string
        macAddress:
          type: string
      required:
        - id
        - connectivityState
        - connectors
    ChargerConnectivityStatus:
      type: object
      properties:
        ppid:
          type: string
        connectedComponents:
          type: array
          items:
            type: string
        chargingStation:
          type: object
        evses:
          type: array
          items:
            $ref: '#/components/schemas/EvseComponent'
      required:
        - ppid
        - connectedComponents
    EnrolmentStatus:
      type: object
      properties:
        isEnrolled:
          type: boolean
        optOutUrl:
          type: string
      required:
        - isEnrolled
        - optOutUrl
    ChargerFlexEnrolment:
      type: object
      properties:
        ppid:
          type: string
        enrolmentStatus:
          $ref: '#/components/schemas/EnrolmentStatus'
      required:
        - ppid
        - enrolmentStatus
    FlexRequestLimit:
      type: object
      properties:
        unit:
          type: object
        value:
          type: number
      required:
        - unit
        - value
    FlexRequest:
      type: object
      properties:
        id:
          type: string
          example: d40ff7bc-cd94-4e4f-9d52-34a80e83e64b
        requestedAt:
          type: string
          description: When the flex request was created, in ISO 8601 format
          example: 2023-08-14T26:32:11Z
        startAt:
          type: string
          description: When the flex request is scheduled to start, in ISO 8601 format
          example: 2023-10-18T15:00:00Z
        endAt:
          type: string
          description: When the flex request is scheduled to end, in ISO 8601 format
          example: 2023-10-18T17:30:00Z
        direction:
          enum:
            - INCREASE
            - REDUCE
          type: string
          description: The direction of the flexibility request
          example: 2023-10-18T17:30:00Z
        limit:
          description: The limit of the flexibility request
          example:
            unit: KW
            value: 50
          allOf:
            - $ref: '#/components/schemas/FlexRequestLimit'
      required:
        - id
        - requestedAt
        - startAt
        - endAt
        - direction
        - limit
    SolarPreferences:
      type: object
      properties:
        powerGeneration:
          type: string
          example: ON
        solarMatching:
          type: string
          example: ON
        solarThreshold:
          type: number
          example: '0.2'
          description: 'Deprecated: please use solarMaxGridImport instead'
        solarMaxGridImport:
          type: number
          example: '0.2'
          description:
            The maximum amount of power in kWh to import from the grid.
            Replaces solarThreshold which has been deprecated
      required:
        - powerGeneration
        - solarMatching
    ChargeOverrideRequestDTO:
      type: object
      properties:
        endAt:
          type: string
        requestedAt:
          type: string
      required:
        - endAt
        - requestedAt
    ChargeOverrideScheduleChargingStation:
      type: object
      properties:
        addressId:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        mpan:
          type: string
          nullable: true
        ppid:
          type: string
          nullable: true
      required:
        - ppid
    ChargeOverrideScheduleEves:
      type: object
      properties:
        door:
          type: string
        ocppEvseId:
          type: number
      required:
        - door
        - ocppEvseId
    ChargeOverrideScheduleResponse:
      type: object
      properties:
        chargingStation:
          $ref: '#/components/schemas/ChargeOverrideScheduleChargingStation'
        deletedAt:
          type: string
          nullable: true
        endAt:
          type: string
          nullable: true
        evse:
          $ref: '#/components/schemas/ChargeOverrideScheduleEves'
        id:
          type: string
        receivedAt:
          type: string
        requestedAt:
          type: string
      required:
        - chargingStation
        - deletedAt
        - endAt
        - evse
        - id
        - receivedAt
        - requestedAt
    TariffInfoDtoImpl:
      type: object
      properties:
        days:
          type: array
          items:
            type: string
            enum:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
        start:
          type: string
          description: 'When the period starts (format: XX:XX:XX)'
        end:
          type: string
          description: 'When the period ends (format: XX:XX:XX)'
        price:
          type: number
          description: Price per unit (in pence)
      required:
        - days
        - start
        - end
        - price
    ChargerTariffDto:
      type: object
      properties:
        id:
          type: string
          description: The ID of the tariff
        effectiveFrom:
          type: string
          description: The date from which the tariff is effective (inclusive)
        maxChargePrice:
          type: number
          description: The maximum price during off-peak hours in pence
          nullable: true
        ppid:
          type: string
          description: The PPID of the charger related to the tariff
        supplierId:
          type: string
          nullable: true
          description: Reference to the supplier. Null if the supplier is unknown
        tariffInfo:
          description: Tariff information applicable to specific days and times.
          type: array
          items:
            $ref: '#/components/schemas/TariffInfoDtoImpl'
        timezone:
          type: string
          description: Timezone the tariff information applies to
          example: Europe/London
        cheapestUnitPrice:
          type: number
          description: The cheapest unit price (e.g., £0.10).
          example: 0.2
      required:
        - id
        - effectiveFrom
        - ppid
        - supplierId
        - tariffInfo
        - timezone
        - cheapestUnitPrice
    ChargingStationTariffSearchCriteriaDtoImpl:
      type: object
      properties:
        ppid:
          type: string
          description: Charging Station PPID
        effectiveFrom:
          type: string
          description: The effective from date
        effectiveTo:
          type: string
          description: The effective to date
      required:
        - ppid
    ChargingStationTariffSearchMetadataDtoImpl:
      type: object
      properties:
        criteria:
          $ref: '#/components/schemas/ChargingStationTariffSearchCriteriaDtoImpl'
      required:
        - criteria
    ChargingStationTariffSearchDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ChargerTariffDto'
        metadata:
          $ref: '#/components/schemas/ChargingStationTariffSearchMetadataDtoImpl'
      required:
        - data
        - metadata
    SetChargerTariffDto:
      type: object
      properties:
        effectiveFrom:
          type: string
          description: The date from which the tariff is effective (inclusive)
        maxChargePrice:
          type: number
          description: The maximum price during off-peak hours in pence
          nullable: true
        supplierId:
          type: string
          nullable: true
          description: Reference to the supplier. Null if the supplier is unknown
        tariffInfo:
          description: Tariff information applicable to specific days and times.
          type: array
          items:
            $ref: '#/components/schemas/TariffInfoDtoImpl'
        timezone:
          type: string
          description: Timezone the tariff information applies to
          example: Europe/London
      required:
        - effectiveFrom
        - supplierId
        - tariffInfo
        - timezone
    VehicleInformationImpl:
      type: object
      properties:
        brand:
          type: string
          nullable: true
          description: Vehicle brand
          example: Polestar
        model:
          type: string
          nullable: true
          description: Vehicle model
          example: '2'
        modelVariant:
          type: string
          nullable: true
          description: Vehicle model variant
          example: Long range
        vehicleRegistrationPlate:
          type: string
          nullable: true
          description: Vehicle registration plate
          example: ABC123
        displayName:
          type: string
          nullable: true
          description: Vehicle display name
          example: My car
        evDatabaseId:
          type: string
          description: The ID of the vehicle in the EV Database
    GenericChargeStateImpl:
      type: object
      properties:
        batteryCapacity:
          type: number
          description: Battery capacity
          example: 78
      required:
        - batteryCapacity
    VehicleRequestDtoImpl:
      type: object
      properties:
        vehicleInformation:
          description: Vehicle data
          allOf:
            - $ref: '#/components/schemas/VehicleInformationImpl'
        chargeState:
          description: Vehicle charge state data
          allOf:
            - $ref: '#/components/schemas/GenericChargeStateImpl'
        enodeUserId:
          type: string
          description: Enode user id
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
        enodeVehicleId:
          type: string
          description: Enode vehicle id
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
    VehicleIntentEntryDtoImpl:
      type: object
      properties:
        chargeByTime:
          type: string
          description: Charge by time
          example: 07:00:00
        chargeKWh:
          type: number
          description: Charge KWh
          example: 28
        dayOfWeek:
          type: string
          enum:
            - MONDAY
            - TUESDAY
            - WEDNESDAY
            - THURSDAY
            - FRIDAY
            - SATURDAY
            - SUNDAY
      required:
        - chargeByTime
        - chargeKWh
        - dayOfWeek
    VehicleLinkRequestDtoImpl:
      type: object
      properties:
        isPrimary:
          type: boolean
          description: Is this the primary charger for this vehicle
          example: true
        vehicle:
          description: Vehicle data
          allOf:
            - $ref: '#/components/schemas/VehicleRequestDtoImpl'
        intents:
          description: Charging intents
          type: array
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDtoImpl'
      required:
        - vehicle
        - intents
    VehicleIntentsResponseDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Id
          example: string
        details:
          description: Vehicle intent details
          type: array
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDtoImpl'
        maxPrice:
          type: number
          nullable: true
          description: The maximum price per kWh
          example: 0.25
      required:
        - id
        - details
        - maxPrice
    VehicleLinkResponseDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Id
          example: string
        isPrimary:
          type: boolean
          description: Is this the primary charger for this vehicle
          example: true
        isPluggedInToThisCharger:
          type: boolean
          description: Is this vehicle plugged into the charger
          example: true
        vehicle:
          description: Vehicle data
          anyOf: &a39
            - $ref: '#/components/schemas/ConnectedStatefulVehicleDtoImpl'
            - $ref: '#/components/schemas/GenericStatefulVehicleDtoImpl'
        intents:
          description: Intent data
          allOf:
            - $ref: '#/components/schemas/VehicleIntentsResponseDtoImpl'
      required:
        - id
        - isPrimary
        - isPluggedInToThisCharger
        - vehicle
        - intents
    GenericStatefulVehicleDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Id
          example: string
        vehicleInformation:
          description: Vehicle data
          allOf:
            - $ref: '#/components/schemas/VehicleInformationImpl'
        chargeState:
          description: Vehicle charge state data
          allOf:
            - $ref: '#/components/schemas/GenericChargeStateImpl'
        enodeUserId:
          type: string
          nullable: true
          description: The user's Enode ID
        enodeVehicleId:
          type: string
          nullable: true
          description: The vehicles's Enode ID
      required:
        - id
        - vehicleInformation
        - chargeState
    ConnectedChargeStateImpl:
      type: object
      properties:
        batteryCapacity:
          type: number
          description: Battery capacity
          example: 78
        batteryLevelPercent:
          type: number
          description: Battery level percent
          example: 78
          nullable: true
        chargeLimitPercent:
          type: number
          description: Charge limit percent
          example: 78
          nullable: true
        chargeRate:
          type: number
          description: Charge rate
          example: 78
          nullable: true
        chargeTimeRemaining:
          type: number
          description: Charge time remaining
          example: 78
          nullable: true
        isCharging:
          type: boolean
          description: Is charging?
          example: true
          nullable: true
        isFullyCharged:
          type: boolean
          description: Is fully charged?
          example: true
          nullable: true
        isPluggedIn:
          type: boolean
          description: Is plugged in?
          example: true
          nullable: true
        lastUpdated:
          type: string
          description: Last updated
          example: 2021-08-12T09:00:00Z
          nullable: true
        maxCurrent:
          type: number
          description: Max current
          example: 32
          nullable: true
        powerDeliveryState:
          type: string
          enum:
            - UNPLUGGED
            - PLUGGED_IN:NO_POWER
            - PLUGGED_IN:STOPPED
            - PLUGGED_IN:COMPLETE
            - PLUGGED_IN:CHARGING
            - UNKNOWN
            - PLUGGED_IN:INITIALIZING
            - PLUGGED_IN:FAULT
          nullable: true
        range:
          type: number
          description: Range
          example: 300
          nullable: true
      required:
        - batteryCapacity
        - batteryLevelPercent
        - chargeLimitPercent
        - chargeRate
        - chargeTimeRemaining
        - isCharging
        - isFullyCharged
        - isPluggedIn
        - lastUpdated
        - maxCurrent
        - powerDeliveryState
        - range
    InterventionDtoImpl:
      type: object
      properties:
        all:
          type: string
          description: The endpoint to extract all interventions
        chargeState:
          description: The individual interventions for charge state
          type: array
          items:
            type: string
        information:
          description: The individual interventions for vehicle information
          type: array
          items:
            type: string
      required:
        - all
        - chargeState
        - information
    ConnectedStatefulVehicleDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Vehicle Id
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6"
        lastSeen:
          type: string
          description: Last seen date
          example: 2021-08-12T09:00:00Z
          nullable: true
        enodeUserId:
          type: string
          description: Enode user id
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
        enodeVehicleId:
          type: string
          description: Enode vehicle id
          example: efc4b3b3-4b3b-4b3b-4b3b-4b3b4b3b4b3b
          nullable: true
        vehicleInformation:
          description: Vehicle data
          allOf:
            - $ref: '#/components/schemas/VehicleInformationImpl'
        chargeState:
          description: Vehicle charge state data
          allOf:
            - $ref: '#/components/schemas/ConnectedChargeStateImpl'
        interventions:
          description: Vehicle interventions
          allOf:
            - $ref: '#/components/schemas/InterventionDtoImpl'
      required:
        - id
        - vehicleInformation
        - chargeState
        - interventions
    UpdateVehicleLinkRequestDtoImpl:
      type: object
      properties:
        isPrimary:
          type: boolean
          description: Is this the primary charger for this vehicle
          example: true
        vehicle:
          description: Vehicle data
          allOf:
            - $ref: '#/components/schemas/VehicleRequestDtoImpl'
    VehicleChargeInfoDtoImpl:
      type: object
      properties:
        expectedChargeByTargetPercent:
          type: number
          nullable: true
        expectedChargeByTarget_kWh:
          type: number
          nullable: true
        fullChargeByTime:
          type: string
          nullable: true
      required:
        - expectedChargeByTargetPercent
        - expectedChargeByTarget_kWh
        - fullChargeByTime
    CurrentIntentDtoChargingStationImpl:
      type: object
      properties:
        ppid:
          type: string
          nullable: true
    CurrentIntentDtoImpl:
      type: object
      properties:
        canMeetTarget:
          type: boolean
          description: Whether or not the intent can meet the target
          nullable: true
        chargeDetail:
          description: The charge details
          allOf:
            - $ref: '#/components/schemas/VehicleChargeInfoDtoImpl'
        chargingStation:
          description: The charging station
          allOf:
            - $ref: '#/components/schemas/CurrentIntentDtoChargingStationImpl'
        cannotMeetTargetReason:
          type: string
          nullable: true
          description: The reason why the target can not be met
          enum:
            - PRICE
            - TIME
      required:
        - canMeetTarget
        - chargeDetail
        - chargingStation
        - cannotMeetTargetReason
    ExtendedVehicleLinksResponseDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Id
          example: string
        isPrimary:
          type: boolean
          description: Is this the primary charger for this vehicle
          example: true
        isPluggedInToThisCharger:
          type: boolean
          description: Is this vehicle plugged into the charger
          example: true
        vehicle:
          description: Vehicle data
          anyOf: *a39
        intents:
          description: Intent data
          allOf:
            - $ref: '#/components/schemas/VehicleIntentsResponseDtoImpl'
        currentIntent:
          description: The current intent for the vehicle
          nullable: true
          allOf:
            - $ref: '#/components/schemas/CurrentIntentDtoImpl'
      required:
        - id
        - isPrimary
        - isPluggedInToThisCharger
        - vehicle
        - intents
        - currentIntent
    ChargersAndVehicles:
      type: object
      properties:
        ppid:
          type: string
        vehicles:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedVehicleLinksResponseDtoImpl'
      required:
        - ppid
    DelegatedControlChargingStationResponseDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: Id
          example: string
        ppid:
          type: string
          description: Pod Point id
          example: string
        status:
          type: string
          enum: *a40
        createdAt:
          type: string
          description: Created at
          example: 2021-01-01T00:00:00Z
        vehicleLinks:
          description: Vehicle links
          type: array
          items:
            $ref: '#/components/schemas/VehicleLinkRequestDtoImpl'
      required:
        - id
        - ppid
        - createdAt
    VehicleIntentsRequestDtoImpl:
      type: object
      properties:
        intentDetails:
          description: Charging intents
          type: array
          items:
            $ref: '#/components/schemas/VehicleIntentEntryDtoImpl'
      required:
        - intentDetails
    VehicleIntentEntry:
      type: object
      properties:
        chargeByTime:
          type: string
          example: 07:00:00
        chargeKWh:
          type: number
          example: 28
        dayOfWeek:
          type: string
          enum:
            - MONDAY
            - TUESDAY
            - WEDNESDAY
            - THURSDAY
            - FRIDAY
            - SATURDAY
            - SUNDAY
      required:
        - chargeByTime
        - chargeKWh
        - dayOfWeek
    SetVehicleIntent:
      type: object
      properties:
        createdAt:
          type: string
          example: 2024-07-31T12:34:56.789Z
        updatedAt:
          type: string
          example: 2024-07-31T12:34:56.789Z
        delegatedControlChargingStationVehicleId:
          type: string
          example: e36468da-ffb7-47ef-ab94-62239384b78c
        id:
          type: string
          example: 7d06b432-6de3-425a-ae42-02f81bf5370f
        intentDetails:
          type: array
          items:
            $ref: '#/components/schemas/VehicleIntentEntry'
        maxPrice:
          type: number
          description: The maximum price per kWh
          example: 0.25
          nullable: true
      required:
        - createdAt
        - updatedAt
        - delegatedControlChargingStationVehicleId
        - id
        - intentDetails
        - maxPrice
    ChargesResponse:
      type: object
      properties:
        data:
          type: object
          example:
            '{"count":13,"charges":[{"id":"570b2ccf-824d-4076-9da5-00000468d01e","startedAt":"2023-11-24T06:57:18Z","endedAt":"2023-11-26T08:58:14Z","duration":21553,"energyTotal":23.3,"generationEnergyTotal":8.3,"gridEnergyTotal":15,"cost":{"amount":412,"currency":"GBP"},"expensedTo":{"id":"d8559592-d0d4-11ee-ac0b-f6d33c0a5173","name":"Adept
            Power Solutions
            Ltd"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-24T06:57:18Z","unpluggedAt":"2023-11-26T09:23:20Z","pluggedInDuration":181562}},{"id":"570b2ccf-824d-4076-9da5-0000045df49c","startedAt":"2023-11-16T07:04:30Z","endedAt":"2023-11-16T19:35:03Z","duration":24015,"energyTotal":19.4,"cost":{"amount":343,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-16T07:04:30Z","unpluggedAt":"2023-11-16T19:35:03Z","pluggedInDuration":45033}},{"id":"570b2ccf-824d-4076-9da5-0000045c854a","startedAt":"2023-11-15T06:37:48Z","endedAt":"2023-11-15T10:01:49Z","duration":11431,"energyTotal":19.4,"generationEnergyTotal":0,"gridEnergyTotal":19.4,"cost":{"amount":343,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-15T06:37:48Z","unpluggedAt":"2023-11-15T10:01:49Z","pluggedInDuration":12241}},{"id":"570b2ccf-824d-4076-9da5-00000457ef27","startedAt":"2023-11-11T17:17:57Z","endedAt":"2023-11-13T09:49:39Z","duration":21807,"energyTotal":21.9,"generationEnergyTotal":2.5,"gridEnergyTotal":19.4,"cost":{"amount":387,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-11T17:17:57Z","unpluggedAt":"2023-11-13T09:49:39Z","pluggedInDuration":145902}},{"id":"570b2ccf-824d-4076-9da5-0000045498ei","startedAt":"2023-11-09T12:27:22Z","endedAt":"2023-11-09T15:47:05Z","duration":11983,"energyTotal":4.8,"generationEnergyTotal":4.8,"gridEnergyTotal":0,"cost":{"amount":85,"currency":"GBP"},"expensedTo":{"id":"d8559592-d0d4-11ee-ac0b-f6d33c0a5174","name":"Adept
            Power Solutions
            Ltd"},"charger":{"type":"public","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-09T12:27:22Z","unpluggedAt":"2023-11-09T15:47:05Z","pluggedInDuration":11983}},{"id":"570b2ccf-824d-4076-9da5-0000045498ee","startedAt":"2023-11-09T12:27:22Z","endedAt":"2023-11-09T15:47:05Z","duration":11983,"energyTotal":4.8,"generationEnergyTotal":4.8,"gridEnergyTotal":0,"cost":{"amount":85,"currency":"GBP"},"expensedTo":{"id":"d8559592-d0d4-11ee-ac0b-f6d33c0a5173","name":"Adept
            Power Solutions
            Ltd"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-09T12:27:22Z","unpluggedAt":"2023-11-09T15:47:05Z","pluggedInDuration":11983}},{"id":"570b2ccf-824d-4076-9da5-0000047055a6","startedAt":"2023-11-29T11:02:27Z","endedAt":"2023-11-29T19:05:37Z","duration":27330,"energyTotal":21.1,"generationEnergyTotal":0,"gridEnergyTotal":21.1,"cost":{"amount":373,"currency":"GBP"},"expensedTo":{"id":"d8559592-d0d4-11ee-ac0b-f6d33c0a5173","name":"Adept
            Power Solutions
            Ltd"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-29T11:02:27Z","unpluggedAt":"2023-11-29T19:07:12Z","pluggedInDuration":29085}},{"id":"570b2ccf-824d-4076-9da5-00000454e9c6","startedAt":"2023-11-09T16:58:03Z","endedAt":"2023-11-10T14:16:55Z","duration":57022,"energyTotal":22.6,"generationEnergyTotal":3.2,"gridEnergyTotal":19.4,"cost":{"amount":400,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-09T16:58:03Z","unpluggedAt":"2023-11-10T14:16:57Z","pluggedInDuration":76734}},{"id":"570b2ccf-824d-4076-9da5-0000044f9e98","startedAt":"2023-11-05T16:06:23Z","endedAt":"2023-11-07T11:32:32Z","duration":142243,"energyTotal":50.8,"generationEnergyTotal":10.8,"gridEnergyTotal":40,"cost":{"amount":899,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-05T16:06:23Z","unpluggedAt":"2023-11-07T11:32:32Z","pluggedInDuration":156369}},{"id":"570b2ccf-824d-4076-9da5-0000046e8c38","startedAt":"2023-11-28T07:46:25Z","endedAt":"2023-11-28T09:21:49Z","duration":1954,"energyTotal":43.9,"generationEnergyTotal":3.9,"gridEnergyTotal":40,"cost":{"amount":777,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-28T07:02:55Z","unpluggedAt":"2023-11-28T09:21:49Z","pluggedInDuration":8334}},{"id":"570b2ccf-824d-4076-9da5-0000044ab068","startedAt":"2023-11-01T15:47:14Z","endedAt":"2023-11-05T08:45:48Z","duration":56192,"energyTotal":29.3,"generationEnergyTotal":10,"gridEnergyTotal":19.3,"cost":{"amount":518,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-01T15:47:14Z","unpluggedAt":"2023-11-05T09:25:55Z","pluggedInDuration":322721}},{"id":"570b2ccf-824d-4076-9da5-000004660721","startedAt":"2023-11-22T06:56:07Z","endedAt":"2023-11-22T17:41:10Z","duration":38703,"energyTotal":29.4,"generationEnergyTotal":0,"gridEnergyTotal":29.4,"cost":{"amount":520,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-22T06:56:07Z","unpluggedAt":"2023-11-22T18:42:19Z","pluggedInDuration":42372}},{"id":"570b2ccf-824d-4076-9da5-00000462d7e1","startedAt":"2023-11-19T18:08:01Z","endedAt":"2023-11-20T09:27:59Z","duration":47637,"energyTotal":20.2,"generationEnergyTotal":0,"gridEnergyTotal":20.2,"cost":{"amount":357,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-19T18:08:01Z","unpluggedAt":"2023-11-20T09:27:59Z","pluggedInDuration":55198}},{"id":"570b2ccf-824d-4076-9da5-0000045f9e63","startedAt":"2023-11-17T10:32:06Z","endedAt":"2023-11-18T07:32:33Z","duration":17393,"energyTotal":13.5,"generationEnergyTotal":0,"gridEnergyTotal":13.5,"cost":{"amount":239,"currency":"GBP"},"charger":{"type":"home","id":"PSL-130402","door":"A","pluggedInAt":"2023-11-17T10:32:06Z","unpluggedAt":"2023-11-18T09:02:40Z","pluggedInDuration":81034}}]}'
        meta:
          type: object
          example: '{"params":{"DriverID":"3c05a0ef-9e22-4a16-aab7-1e4026c6ff40","From":"2023-11-01","To":"2023-11-30"}}'
      required:
        - data
        - meta
    ChargeStatsResponse:
      type: object
      properties:
        data:
          type: object
          example: '{"summary":{"energy":{"home":{"grid":0,"generation":0,"total":319.6},"private":{"total":0},"public":{"total":0},"total":{"total":319.6}},"cost":{"home":[{"amount":5653,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":5653,"currency":"GBP"}]},"duration":{"home":479263,"private":0,"public":0,"total":479263}},"intervals":[{"from":"2023-10-30T00:00:00Z","to":"2023-11-06T00:00:00Z","stats":{"energy":{"home":{"grid":0,"generation":0,"total":80.1},"private":{"total":0},"public":{"total":0},"total":{"total":80.1}},"cost":{"home":[{"amount":1417,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":1417,"currency":"GBP"}]},"duration":{"home":198435,"private":0,"public":0,"total":198435}}},{"from":"2023-11-06T00:00:00Z","to":"2023-11-13T00:00:00Z","stats":{"energy":{"home":{"grid":0,"generation":0,"total":49.3},"private":{"total":0},"public":{"total":0},"total":{"total":49.3}},"cost":{"home":[{"amount":872,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":872,"currency":"GBP"}]},"duration":{"home":90812,"private":0,"public":0,"total":90812}}},{"from":"2023-11-13T00:00:00Z","to":"2023-11-20T00:00:00Z","stats":{"energy":{"home":{"grid":0,"generation":0,"total":72.5},"private":{"total":0},"public":{"total":0},"total":{"total":72.5}},"cost":{"home":[{"amount":1282,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":1282,"currency":"GBP"}]},"duration":{"home":100476,"private":0,"public":0,"total":100476}}},{"from":"2023-11-20T00:00:00Z","to":"2023-11-27T00:00:00Z","stats":{"energy":{"home":{"grid":0,"generation":0,"total":52.7},"private":{"total":0},"public":{"total":0},"total":{"total":52.7}},"cost":{"home":[{"amount":932,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":932,"currency":"GBP"}]},"duration":{"home":60256,"private":0,"public":0,"total":60256}}},{"from":"2023-11-27T00:00:00Z","to":"2023-12-04T00:00:00Z","stats":{"energy":{"home":{"grid":0,"generation":0,"total":65},"private":{"total":0},"public":{"total":0},"total":{"total":65}},"cost":{"home":[{"amount":1150,"currency":"GBP"}],"private":[{"amount":0,"currency":"GBP"}],"public":[{"amount":0,"currency":"GBP"}],"total":[{"amount":1150,"currency":"GBP"}]},"duration":{"home":29284,"private":0,"public":0,"total":29284}}}]}'
        meta:
          type: object
          example: '{"params":{"DriverID":"3c05a0ef-9e22-4a16-aab7-1e4026c6ff40","From":"2023-11-01","Interval":"week","To":"2023-11-30"}}'
      required:
        - data
        - meta
    QueryCheckForUpgrade:
      type: object
      properties:
        app_name:
          type: string
          description: The app name
          example: Pod Point
        app_version:
          type: string
          description: The app version
          example: 3.14.1
        platform:
          type: string
          description: The app platform
          example: android
        environment:
          type: string
          description: The app environment
          example: production
        app_language:
          type: string
          description: The app language
          example: en
      required:
        - app_name
        - app_version
        - platform
        - environment
        - app_language
    CheckForUpgrade:
      type: object
      properties:
        found:
          type: boolean
          description: Whether a version has been found or not
          example: true
        forceUpgrade:
          type: boolean
          description: Whether the app should force upgrade
          example: true
        message:
          type: boolean
          description: The message to display to the user
          example: true
        query:
          $ref: '#/components/schemas/QueryCheckForUpgrade'
      required:
        - found
        - forceUpgrade
        - message
        - query
    SupplierDtoImpl:
      type: object
      properties:
        id:
          type: string
          example: 4b98a608-6835-440f-aad4-61d639833218
        name:
          type: string
          example: EDF
        timeZone:
          type: string
          example: Europe/London
          enum:
            - Europe/London
            - Europe/Madrid
            - Europe/Paris
            - Etc/UTC
        icon:
          type: string
          example: https://cdn.pod-point.com/tariff-api/supplier-images/ep-edf.svg
        defaultMaxChargePrice:
          type: number
          example: 0.2
        defaultTariffInfo:
          type: array
          items:
            $ref: '#/components/schemas/TariffInfoDtoImpl'
      required:
        - id
        - name
        - timeZone
        - icon
        - defaultMaxChargePrice
        - defaultTariffInfo
    ExpensesRequestBody:
      type: object
      properties:
        expenses:
          example: '{"expenses":[{"chargeId":"14601384-f1b7-11ee-b628-f6d33c0a5174"},{"chargeId":"14601384-f1b7-11ee-b628-f6d33c0a5174"}]}'
          type: array
          items:
            type: object
      required:
        - expenses
    ExpensesResponse:
      type: object
      properties:
        expenses:
          example: '{"expenses":[{"chargeId":123,"id":456},{"chargeId":123,"id":456},{"chargeId":123,"id":456},{"chargeId":123,"id":456}]}'
          type: array
          items:
            type: object
      required:
        - expenses
    Item:
      type: object
      properties:
        Id:
          type: string
          description: This can be an address Id or a container Id for further results.
          example: gb-rm|rjSgi4YBX8V_g8REPjmo
        Description:
          type: string
          description: Descriptive information about the result.
          example: ' - 26 Addresses'
        Type:
          enum:
            - Address
            - Container
            - Postcode
            - Residential
            - Street
          type: string
          description:
            If the Type is "Address" then the Id can be passed to the Retrieve
            service. Any other Id should be passed as the Container to a further
            Find request to get more results.
          example: Container
        Text:
          type: string
          description: The name of the result.
          example: Banner Street London EC1Y 8QE
        Highlight:
          type: string
          description:
            A list of number ranges identifying the matched characters in the
            Text and Description.
          example: 21-25,26-29
      required:
        - Id
        - Description
        - Type
        - Text
        - Highlight
    Address:
      type: object
      properties:
        Id:
          type: string
          example: GB|RM|A|8182563
        DomesticId:
          type: string
          example: '8182563'
        Language:
          type: string
          example: ENG
        LanguageAlternatives:
          type: string
          example: ENG
        Department:
          type: string
        Company:
          type: string
          example: Pod Point Ltd
        SubBuilding:
          type: string
        BuildingNumber:
          type: string
        BuildingName:
          type: string
          example: Discovery House 28-42
        SecondaryStreet:
          type: string
        Street:
          type: string
          example: Banner Street
        Block:
          type: string
        Neighbourhood:
          type: string
        District:
          type: string
        City:
          type: string
          example: London
        Line1:
          type: string
          example: Discovery House
        Line2:
          type: string
          example: 28-42 Banner Street
        Line3:
          type: string
        Line4:
          type: string
        Line5:
          type: string
        AdminAreaName:
          type: string
          example: Islington
        AdminAreaCode:
          type: string
        Province:
          type: string
        ProvinceName:
          type: string
        ProvinceCode:
          type: string
        PostalCode:
          type: string
          example: EC1Y 8QE
        CountryName:
          type: string
          example: United Kingdom
        CountryIso2:
          type: string
          example: GB
        CountryIso3:
          type: string
          example: GBR
        CountryIsoNumber:
          type: string
          description: ISO numeric code for the country.
          example: '826'
        SortingNumber1:
          type: string
          example: '74116'
        SortingNumber2:
          type: string
        Barcode:
          type: string
          example: (EC1Y8QE4B6)
        POBoxNumber:
          type: string
        Label:
          type: string
          example: |-
            Pod Point Ltd
            Discovery House
            28-42 Banner Street
            LONDON
            EC1Y 8QE
            UNITED KINGDOM
        Type:
          type: string
          example: Commercial
        DataLevel:
          type: string
          example: Premise
        Field1:
          type: string
        Field2:
          type: string
        Field3:
          type: string
        Field4:
          type: string
        Field5:
          type: string
        Field6:
          type: string
        Field7:
          type: string
        Field8:
          type: string
        Field9:
          type: string
        Field10:
          type: string
        Field11:
          type: string
        Field12:
          type: string
        Field13:
          type: string
        Field14:
          type: string
        Field15:
          type: string
        Field16:
          type: string
        Field17:
          type: string
        Field18:
          type: string
        Field19:
          type: string
        Field20:
          type: string
      required:
        - Id
        - DomesticId
        - Language
        - LanguageAlternatives
        - Department
        - Company
        - SubBuilding
        - BuildingNumber
        - BuildingName
        - SecondaryStreet
        - Street
        - Block
        - Neighbourhood
        - District
        - City
        - Line1
        - Line2
        - Line3
        - Line4
        - Line5
        - AdminAreaName
        - AdminAreaCode
        - Province
        - ProvinceName
        - ProvinceCode
        - PostalCode
        - CountryName
        - CountryIso2
        - CountryIso3
        - CountryIsoNumber
        - SortingNumber1
        - SortingNumber2
        - Barcode
        - POBoxNumber
        - Label
        - Type
        - DataLevel
        - Field1
        - Field2
        - Field3
        - Field4
        - Field5
        - Field6
        - Field7
        - Field8
        - Field9
        - Field10
        - Field11
        - Field12
        - Field13
        - Field14
        - Field15
        - Field16
        - Field17
        - Field18
        - Field19
        - Field20
    CreateIntentResponse:
      type: object
      properties:
        customer:
          type: string
          description: The customer id
        setupIntent:
          type: string
          description: The setup intent secret
          nullable: true
        ephemeralKey:
          type: string
          description: The ephemeral key
      required:
        - customer
        - setupIntent
        - ephemeralKey
    CreatePaymentRequest:
      type: object
      properties:
        amount:
          type: number
          description: The amount to top up
          example: 20
        currency:
          type: string
          description: Currency of the amount that is to topup
          example: gbp
      required:
        - amount
        - currency
    CreateRegisteredUserPaymentResponse:
      type: object
      properties:
        paymentIntent:
          type: string
          description: The payment intent secret
          nullable: true
        customer:
          type: string
          description: The customer id
        ephemeralKey:
          type: string
          description: The ephemeral key
      required:
        - paymentIntent
        - customer
        - ephemeralKey
    RemoteLockDTO:
      type: object
      properties:
        offMode:
          type: boolean
          description: Whether or not remote lock is enabled
          nullable: true
      required:
        - offMode
    ReportsDtoPayload:
      type: object
      properties:
        from:
          type: string
          description: 'Statistics report inclusive start date eg: 2022-01-01'
          example: 2022-10-12
        to:
          type: string
          description: 'Statistics report inclusive end date eg: 2022-01-02'
          example: 2022-10-19
        distances:
          type: object
          description: Total and business distances
          example: '{"total": 10, "business": 5}'
        organisationOnly:
          type: boolean
          description: A filter for presenting only organisation only expenses
          example: 'true'
        unitOfDistance:
          type: string
          description: The unit of distance mi|km
          example: mi
        overrideEmailAddress:
          type: string
          description:
            Override the delivery email address for the report. Used for
            testing purposes only, does nothing in production.
          example: <EMAIL>
      required:
        - from
        - to
        - organisationOnly
        - unitOfDistance
    RewardWalletAllowanceDTO:
      type: object
      properties:
        balanceMiles:
          type: number
          description: The miles remaining in the user's allowance
          example: 6532
        annualAllowanceMiles:
          type: number
          description: The total miles a user can claim within a year
          example: 7500
      required:
        - balanceMiles
        - annualAllowanceMiles
    RewardWalletRewardsDTO:
      type: object
      properties:
        balanceMiles:
          type: number
          description: How many miles can be withdrawn
          example: 1018
        balanceGbp:
          type: number
          description: The cash value of the withdrawable balance
          example: 23.71
      required:
        - balanceMiles
        - balanceGbp
    RewardWalletPaymentsDTO:
      type: object
      properties:
        thresholdGbp:
          type: number
          description: The minimum cash value required to withdraw the balance
          example: 10
      required:
        - thresholdGbp
    RewardWalletDTO:
      type: object
      properties:
        allowance:
          description: The user's reward wallet allowance
          allOf:
            - $ref: '#/components/schemas/RewardWalletAllowanceDTO'
        rewards:
          description: The rewards a user has
          allOf:
            - $ref: '#/components/schemas/RewardWalletRewardsDTO'
        payments:
          description: Information related to reward payouts
          allOf:
            - $ref: '#/components/schemas/RewardWalletPaymentsDTO'
      required:
        - allowance
        - rewards
        - payments
    RewardWalletPayoutDTO:
      type: object
      properties:
        bankAccountId:
          type: string
          description: The bank account id belonging to the authenticated user to payout to
          example: 253e9473-e07e-4173-a86a-99786f068a29
      required:
        - bankAccountId
    WifiCredentials:
      type: object
      properties:
        ppid:
          type: string
        ssid:
          type: string
        password:
          type: string
    SubmitSupportFeedbackDTO:
      type: object
      properties:
        region:
          type: string
          description: The region the support case is relevant to
          example: es
          enum:
            - es
            - fr
        email:
          type: string
          description: The email of the user submitting the feedback
          example: <EMAIL>
        description:
          type: string
          description: The free-text entered by the user
          example: I am having trouble topping up my account! Please help.
        chargerName:
          type: string
          description: The name of the charger, if relevant
          example: Lara-Theo
        siteName:
          type: string
          description: The name of the site at which the charger is located, if relevant
          example: Battersea Power Station - Phase 3
        siteAddress:
          type: string
          description: The address of the site at which the charger is located, if relevant
          example: Battersea Power Station - Phase 3 Basement, Nine Elms, London, SW8 5BN
      required:
        - region
        - email
        - description
    TescoClubcardResponse:
      type: object
      properties:
        customerId:
          type: string
      required:
        - customerId
    LinkyDTO:
      type: object
      properties:
        linkyCapable:
          type: boolean
          nullable: true
          description: Wether or not the charger is Linky capable
        scheduleEnabled:
          type: boolean
          nullable: true
          description: Whether or not Linky is used for scheduling
      required:
        - linkyCapable
        - scheduleEnabled
    SetLinkyDTO:
      type: object
      properties:
        scheduleEnabled:
          type: boolean
          nullable: true
          description: Whether or not Linky is used for scheduling
      required:
        - scheduleEnabled
    VehicleInterventionResolutionDtoImpl:
      type: object
      properties:
        title:
          type: string
          description: The title of resolution
          example: Replace battery
        description:
          type: string
          description: The description of resolution
          example: Replace the battery with a new one
        access:
          type: string
          description: The access required to perform the resolution
          example: Physical
        agent:
          type: string
          description: The agent responsible for performing the resolution
          example: Device
      required:
        - title
        - description
        - access
        - agent
    VehicleInterventionDtoImpl:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the intervention
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        vendor:
          type: string
          nullable: true
          description: The vendor of the intervention
          example: string
        vendorType:
          type: string
          nullable: true
          description: The type of the intervention
          example: string
        brand:
          type: string
          nullable: true
          description: The brand of the vehicle
          example: Tesla
        introducedAt:
          type: string
          description: The date the intervention was introduced
          example: 2024-11-14T14:02:06.317Z
        domain:
          type: string
          description: The domain of the intervention
          enum:
            - Account
            - Device
          example: Account
        resolution:
          description: The resolution of the intervention
          allOf:
            - $ref: '#/components/schemas/VehicleInterventionResolutionDtoImpl'
      required:
        - id
        - vendor
        - vendorType
        - brand
        - introducedAt
        - domain
        - resolution
    VehicleInterventionResponseDtoImpl:
      type: object
      properties:
        chargeState:
          description: Interventions relating to charge state
          type: array
          items:
            $ref: '#/components/schemas/VehicleInterventionDtoImpl'
        information:
          description: Interventions relating to information
          type: array
          items:
            $ref: '#/components/schemas/VehicleInterventionDtoImpl'
      required:
        - chargeState
        - information
    MarketingOpportunitiesDTO:
      type: object
      properties:
        opportunities:
          type: array
          items:
            type: string
            enum:
              - REWARDS
              - TARIFF
              - MIGRATE
          description: A list of all the marketing opportunities a charger is eligible for
      required:
        - opportunities
    SubscriptionSurveyActionDataDTO:
      type: object
      properties:
        surveyUrl:
          type: string
          description: URL of the survey
      required:
        - surveyUrl
    SubscriptionSurveyActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: &a41
            - USER
            - SYSTEM
        status:
          type: string
          description: The status of the action
          enum: &a42
            - PENDING
            - SUCCESS
            - FAILURE
        type:
          type: string
          description: The type of action
          enum:
            - COMPLETE_HOME_SURVEY_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          $ref: '#/components/schemas/SubscriptionSurveyActionDataDTO'
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionInstallChargingStationActionDataDTO:
      type: object
      properties:
        ppid:
          type: string
          nullable: true
          description: The PPID of the charging station. Set once installed.
      required:
        - ppid
    SubscriptionInstallChargingStationActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: *a41
        status:
          type: string
          description: The status of the action
          enum: *a42
        type:
          type: string
          description: The type of action
          enum:
            - INSTALL_CHARGING_STATION_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          $ref: '#/components/schemas/SubscriptionInstallChargingStationActionDataDTO'
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionCheckAffordabilityDataDTO:
      type: object
      properties:
        applicationId:
          type: number
          nullable: true
          description: The ID of the application within the LMS
        loanId:
          type: number
          nullable: true
          description: The loan ID within the LMS
      required:
        - applicationId
        - loanId
    SubscriptionCheckAffordabilityActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: *a41
        status:
          type: string
          description: The status of the action
          enum: *a42
        type:
          type: string
          description: The type of action
          enum:
            - CHECK_AFFORDABILITY_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          $ref: '#/components/schemas/SubscriptionCheckAffordabilityDataDTO'
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionSetupDirectDebitActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: *a41
        status:
          type: string
          description: The status of the action
          enum: *a42
        type:
          type: string
          description: The type of action
          enum:
            - SETUP_DIRECT_DEBIT_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          type: object
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionSignLoanAgreementActionSignDocumentDTO:
      type: object
      properties:
        signingUrl:
          type: string
          description: The url to sign the document
        code:
          type: string
          description: The document type
          enum:
            - rca
            - ha
        signed:
          type: boolean
          description: Is the document signed
      required:
        - signingUrl
        - code
        - signed
    SubscriptionSignLoanAgreementActionDataDTO:
      type: object
      properties:
        documents:
          description: A list of documents to be signed
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionSignLoanAgreementActionSignDocumentDTO'
      required:
        - documents
    SubscriptionSignDocumentsActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: *a41
        status:
          type: string
          description: The status of the action
          enum: *a42
        type:
          type: string
          description: The type of action
          enum:
            - SIGN_DOCUMENTS_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          $ref: '#/components/schemas/SubscriptionSignLoanAgreementActionDataDTO'
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionPayUpfrontFeeActionDTO:
      type: object
      properties:
        subscriptionId:
          type: string
          description: ID of the subscription the action is associated with
        id:
          type: string
          description: The ID of the action
        owner:
          type: string
          description: Who is required to complete the action
          enum: *a41
        status:
          type: string
          description: The status of the action
          enum: *a42
        type:
          type: string
          description: The type of action
          enum:
            - PAY_UPFRONT_FEE_V1
        dependsOn:
          description: IDs of actions which this action depends on
          type: array
          items:
            type: string
        data:
          type: object
      required:
        - subscriptionId
        - id
        - owner
        - status
        - type
        - dependsOn
        - data
    SubscriptionOrderAddressDTO:
      type: object
      properties:
        line1:
          type: string
          description: Line 1 of the address
        line2:
          type: string
          nullable: true
          description: Line 2 of the address
        line3:
          type: string
          nullable: true
          description: Line 3 of the address
        postcode:
          type: string
          description: Postcode of the address
      required:
        - line1
        - line2
        - line3
        - postcode
    SubscriptionOrderDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the order
        orderedAt:
          type: string
          description: When the order was placed
        origin:
          type: string
          description: Where the order details originate from
          enum:
            - SALESFORCE
            - SALESFORCE_TEST
        address:
          description: The address of the person who placed the address
          allOf:
            - $ref: '#/components/schemas/SubscriptionOrderAddressDTO'
        email:
          type: string
          description: The email address of the person who placed the order
        firstName:
          type: string
          description: The first name of the person who placed the order
        lastName:
          type: string
          description: The last name of the person who placed the order
        mpan:
          type: string
          description: The person who placed the order's MPAN
        phoneNumber:
          type: string
          description: The phone number of the person who placed the order
        eCommerceId:
          type: string
          description: The eCommerceId associated with the order
      required:
        - id
        - orderedAt
        - origin
        - address
        - email
        - firstName
        - lastName
        - mpan
        - phoneNumber
        - eCommerceId
    SubscriptionPlanDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the plan
        allowanceMiles:
          type: number
          description: How many miles are allowed to be claimed
        allowancePeriod:
          type: string
          description: How long the allowance period is
          enum:
            - ANNUAL
        upfrontFeePounds:
          type: number
          description: The upfront fee required for the plan (in GBP)
        monthlyFeePounds:
          type: number
          description: The monthly fee required for the plan (in GBP)
        contractDurationMonths:
          type: number
          description: The number of months the contract lasts
        productCode:
          type: string
          description: The product code of the plan
        rateMilesPerKwh:
          type: number
          description: The conversion rate between miles and KWH
        ratePencePerMile:
          type: number
          description: The conversion rate between pence and miles
        type:
          type: string
          description: The type of plan
          enum:
            - POD_DRIVE
        milesRenewalDate:
          type: string
          nullable: true
          description: When the miles renew
          format: date-time
      required:
        - id
        - allowanceMiles
        - allowancePeriod
        - upfrontFeePounds
        - monthlyFeePounds
        - contractDurationMonths
        - productCode
        - rateMilesPerKwh
        - ratePencePerMile
        - type
        - milesRenewalDate
    SubscriptionDTO:
      type: object
      properties:
        actions:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/SubscriptionSurveyActionDTO'
              - $ref: '#/components/schemas/SubscriptionInstallChargingStationActionDTO'
              - $ref: '#/components/schemas/SubscriptionCheckAffordabilityActionDTO'
              - $ref: '#/components/schemas/SubscriptionSetupDirectDebitActionDTO'
              - $ref: '#/components/schemas/SubscriptionSignDocumentsActionDTO'
              - $ref: '#/components/schemas/SubscriptionPayUpfrontFeeActionDTO'
        createdAt:
          type: string
          description: When the subscription was created
          format: date-time
        deletedAt:
          type: string
          nullable: true
          description: When the subscription was deleted
          format: date-time
        activatedAt:
          type: string
          nullable: true
          description: When the subscription was activated
          format: date-time
        id:
          type: string
          description: The ID of the subscription
        order:
          $ref: '#/components/schemas/SubscriptionOrderDTO'
        plan:
          $ref: '#/components/schemas/SubscriptionPlanDTO'
        status:
          type: string
          description: The status of the subscription
          enum:
            - PENDING
            - ACTIVE
            - CANCELLED
            - SUSPENDED
            - ENDED
            - REJECTED
        updatedAt:
          type: string
          description: When the subscription was last updated
          format: date-time
      required:
        - actions
        - createdAt
        - deletedAt
        - activatedAt
        - id
        - order
        - plan
        - status
        - updatedAt
    SubscriptionListDTO:
      type: object
      properties:
        data:
          description: An array of subscriptions associated with the current user
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionDTO'
      required:
        - data
    SubscriptionDocumentDTO:
      type: object
      properties:
        issued:
          type: string
          description: When the subscription was last updated
          format: date-time
          example: 2025-05-28T18:28:21.959Z
        link:
          type: string
          description: URL to the document
          example: /subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48
        format:
          type: string
          description: Format of the document
          example: PDF
        active:
          type: boolean
          description: If this document is active
          example: true
        type:
          type: string
          description: The type of document
          enum:
            - rca
            - ha
      required:
        - issued
        - link
        - format
        - active
        - type
    SubscriptionDocumentsDTO:
      type: object
      properties:
        documents:
          description: An array of documents associated with the subscription
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionDocumentDTO'
      required:
        - documents
    SubscriptionDirectDebitDTO:
      type: object
      properties:
        accountNumberLastDigits:
          type: string
          description: The last 4 digits of the account number
          example: '1234'
        sortCodeLastDigits:
          type: string
          description: The last 2 digits of the sort code
          example: '23'
        nameOnAccount:
          type: string
          description: The name associated with the direct debit
          example: Mr Tom Wallace
        monthlyPaymentDay:
          type: number
          description: The day of the month in which payment is due
          example: 13
      required:
        - accountNumberLastDigits
        - sortCodeLastDigits
        - nameOnAccount
        - monthlyPaymentDay
    AffordabilityActionDataBillingAddressDTO:
      type: object
      properties:
        flat:
          type: string
        number:
          type: string
        name:
          type: string
        street:
          type: string
        town:
          type: string
        county:
          type: string
        postcode:
          type: string
      required:
        - street
        - town
        - postcode
    AffordabilityActionDataDTO:
      type: object
      properties:
        title:
          type: string
          description: Title of the customer
          enum:
            - MR
            - MRS
            - MISS
            - MS
            - MX
            - DR
            - OTHER
        firstName:
          type: string
          description: First name of the customer
          example: John
        middleNames:
          type: string
        lastName:
          type: string
          description: Last name of the customer
          example: Smith
        dateOfBirth:
          type: string
          description: Date of birth of the customer
          format: date
          example: 2025-01-01
        email:
          type: string
          description: Email address of the customer
          example: <EMAIL>
        phoneNumber:
          type: string
          description: Telephone number of the customer
          example: '+447234567890'
        maritalStatus:
          type: string
          enum:
            - SINGLE
            - LIVING_TOGETHER
            - COMMON_LAW
            - MARRIED
            - SEPERATED
            - DIVORCED
            - WIDOWED
            - OTHER
        residentialStatus:
          type: string
          enum:
            - LIVING_WITH_PARENTS
            - COUNCIL_TENANT
            - HOMEOWNER_WITH_MORTGAGE
            - HOMEOWNER_WITHOUT_MORTGAGE
            - PRIVATE_TENANT
            - HOUSING_ASSOCIATION
            - OTHER
        dependencies:
          type: string
          enum:
            - '0'
            - '1'
            - '2'
            - 3+
        employmentStatus:
          type: string
          enum:
            - SELF_EMPLOYED
            - PART_TIME
            - FULL_TIME
            - CONTRACT
            - RETIRED
            - ARMED_FORCES
            - HOME_MAKER
            - SINGLE_PARENT
            - DISABLED
            - UNEMPLOYED
        monthlyTakeHomePay:
          type: number
          example: 1000
        monthlyHousePayments:
          type: number
          example: 500
        monthlyTravelAndLivingExpenses:
          type: number
          example: 200
        monthlyHouseholdExpenses:
          type: number
          example: 300
        monthlyCreditPayments:
          type: number
          example: 100
        circumstancesRequireSupport:
          type: string
          enum:
            - SUPPORT_REQUESTED
            - NO_SUPPORT_REQUESTED
        billingAddress:
          description:
            At least one of billingAddress' flat, number or name fields must be
            provided
          allOf:
            - $ref: '#/components/schemas/AffordabilityActionDataBillingAddressDTO'
      required:
        - title
        - firstName
        - lastName
        - dateOfBirth
        - email
        - phoneNumber
        - maritalStatus
        - residentialStatus
        - dependencies
        - employmentStatus
        - monthlyTakeHomePay
        - monthlyHousePayments
        - monthlyTravelAndLivingExpenses
        - monthlyHouseholdExpenses
        - monthlyCreditPayments
        - circumstancesRequireSupport
        - billingAddress
    AffordabilityActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - CHECK_AFFORDABILITY_V1
        data:
          $ref: '#/components/schemas/AffordabilityActionDataDTO'
      required:
        - type
        - data
    SetupDirectDebitActionDataDTO:
      type: object
      properties:
        accountNumber:
          type: string
          description: Bank account number of the customer
          example: '********'
        sortCode:
          type: string
          description: Sort code of the customer’s bank
          example: '123456'
        accountName:
          type: string
          description: Name of account holder
          example: Joe Bloggs
        requiresMoreThanOneSignatory:
          type: boolean
          description: Request is from either one or more authorised signatory
        understandsDirectDebitGuarantee:
          type: boolean
          description: The account holder understands the direct debit guarantee
      required:
        - accountNumber
        - sortCode
        - accountName
        - requiresMoreThanOneSignatory
        - understandsDirectDebitGuarantee
    SetupDirectDebitActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - SETUP_DIRECT_DEBIT_V1
        data:
          $ref: '#/components/schemas/SetupDirectDebitActionDataDTO'
      required:
        - type
        - data
    UpdateHomeSurveyActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - COMPLETE_HOME_SURVEY_V1
        data:
          type: object
      required:
        - type
    UpdateSignDocumentsActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - SIGN_DOCUMENTS_V1
        data:
          type: object
      required:
        - type
    SubscriptionConfirmationOfPayeeDTO:
      type: object
      properties:
        accountName:
          type: string
          description: The account holder's name
          example: Mr John Smith
        sortCode:
          type: string
          pattern: /^[0-9]{1,6}$/
          description: The bank’s sort code
          example: '123456'
        accountNumber:
          type: string
          pattern: /^[0-9]{1,8}$/
          description: The bank account number
          example: '********'
      required:
        - accountName
        - sortCode
        - accountNumber
    SubscriptionConfirmationOfPayeeResponse:
      type: object
      properties:
        status:
          type: string
          description: The outcome of the check
          enum:
            - matched
            - partial_match
            - not_matched
        reason:
          type: string
          description: Provides context to the status of the check
          externalDocs:
            url: https://docs.acquired.com/docs/confirmation-of-payee-cop#reason-values
            description: Acquired COP Reason Values
        accountName:
          type: string
          description: The account holder's name
          example: Mr John Smith
      required:
        - status
        - accountName
    FieldValidatorDTO:
      type: object
      properties:
        type:
          type: string
      required:
        - type
    FieldValueDTO:
      type: object
      properties:
        id:
          type: string
        label:
          type: string
        value:
          type: string
      required:
        - value
    FieldHelpDTO:
      type: object
      properties:
        title:
          type: string
        content:
          type: string
    FormFieldDTO:
      type: object
      properties:
        type:
          type: string
        label:
          type: string
        validators:
          type: array
          items:
            $ref: '#/components/schemas/FieldValidatorDTO'
        id:
          type: string
        values:
          type: array
          items:
            $ref: '#/components/schemas/FieldValueDTO'
        help:
          $ref: '#/components/schemas/FieldHelpDTO'
      required:
        - type
        - label
    FormSectionDTO:
      type: object
      properties:
        title:
          type: string
        fields:
          type: array
          items:
            $ref: '#/components/schemas/FormFieldDTO'
      required:
        - title
        - fields
    FormDataDTO:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        sections:
          type: array
          items:
            $ref: '#/components/schemas/FormSectionDTO'
      required:
        - id
        - name
        - sections
    WarrantyDTO:
      type: object
      properties:
        startDate:
          type: string
          description: When the warranty started
          format: date
          example: 2025-01-01
        endDate:
          type: string
          description: When the warranty ended
          format: date
          example: 2025-01-01
        slaHours:
          type: number
          description: The service level agreement hours
          example: 48
        productCode:
          type: string
          enum:
            - LIFETIME_48_HOURS
          description: The product code of the warranty
          example: LIFETIME_48_HOURS
      required:
        - startDate
        - endDate
        - slaHours
        - productCode
    RecipientAddressDTO:
      type: object
      properties:
        line1:
          type: string
          description: Line 1 of the address
          example: 222 Gray's Inn Road
        line2:
          type: string
          description: Line 2 of the address
        city:
          type: string
          description: City of the address
          example: London
        county:
          type: string
          description: County of the address
          example: London
        postcode:
          type: string
          description: Postcode of the address
          example: WC1X 8HB
        country:
          type: string
          description: Country of the address
          example: GB
      required:
        - line1
        - city
        - county
        - postcode
        - country
    BankAccountResponseDTO:
      type: object
      properties:
        firstName:
          type: string
          description: The recipient's first name
          example: Jenny
        lastName:
          type: string
          description: The recipient's last name
          example: Rosen
        email:
          type: string
          description: The recipient's email address
          example: <EMAIL>
        address:
          description: The recipient's address
          allOf:
            - $ref: '#/components/schemas/RecipientAddressDTO'
        id:
          type: string
          description: The ID of the account
        nameOnAccount:
          type: string
          description: The name on the account
        accountNumber:
          type: string
          description: The last four digits of the account number
          example: '6241'
        sortCode:
          type: string
          description: The sort code of the account
          example: '123456'
      required:
        - firstName
        - lastName
        - email
        - address
        - id
        - nameOnAccount
        - accountNumber
        - sortCode
    BankAccountRecipientDTO:
      type: object
      properties:
        firstName:
          type: string
          description: The recipient's first name
          example: Jenny
        lastName:
          type: string
          description: The recipient's last name
          example: Rosen
        email:
          type: string
          description: The recipient's email address
          example: <EMAIL>
        address:
          description: The recipient's address
          allOf:
            - $ref: '#/components/schemas/RecipientAddressDTO'
      required:
        - firstName
        - lastName
        - email
        - address
    CreateBankAccountBankAccountDTO:
      type: object
      properties:
        nameOnAccount:
          type: string
          description: The full name on the bank account
          example: Jennifer Rosen
        accountNumber:
          type: string
          description: The account number
          example: '********'
        sortCode:
          type: string
          description: The sort code
          example: '108800'
      required:
        - nameOnAccount
        - accountNumber
        - sortCode
    CreateBankAccountDTO:
      type: object
      properties:
        recipient:
          description: The recipient who the bank account relates to
          allOf:
            - $ref: '#/components/schemas/BankAccountRecipientDTO'
        bankAccount:
          description: The bank account of the recipient
          allOf:
            - $ref: '#/components/schemas/CreateBankAccountBankAccountDTO'
      required:
        - recipient
        - bankAccount
    BankAccountCOPFailedDTO:
      type: object
      properties:
        status:
          enum:
            - MATCH
            - MISMATCH
            - PARTIAL_MATCH
            - UNAVAILABLE
          type: string
          description: The status of the confirmation of payee check
        provided:
          type: string
          description: The name provided
          example: Jenny Rosen
        suggested:
          type: string
          nullable: true
          description:
            The suggested name associated with the bank account if status is
            PARTIAL_MATCH
          example: Jennifer Rosen
      required:
        - status
        - provided
        - suggested
