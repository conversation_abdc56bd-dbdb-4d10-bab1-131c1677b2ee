# openapi.api.ChargersApi

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                                                                  | HTTP request                                   | Description                                                                                  |
| ------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | -------------------------------------------------------------------------------------------- |
| [**chargersControllerCreateChargerOverrides**](ChargersApi.md#chargerscontrollercreatechargeroverrides) | **POST** /chargers/{ppid}/charge-overrides     | create charge overrides for a given charger                                                  |
| [**chargersControllerDeleteChargeOverride**](ChargersApi.md#chargerscontrollerdeletechargeoverride)     | **DELETE** /chargers/{ppid}/charge-overrides   | delete charge overrides for a given charger                                                  |
| [**chargersControllerDeleteFlexEnrolment**](ChargersApi.md#chargerscontrollerdeleteflexenrolment)       | **DELETE** /chargers/{ppid}/flex-enrolment     | delete a charger's flex enrolment                                                            |
| [**chargersControllerDeleteFlexRequest**](ChargersApi.md#chargerscontrollerdeleteflexrequest)           | **DELETE** /chargers/{ppid}/flex-requests/{id} | delete a flex request                                                                        |
| [**chargersControllerDeleteTariff**](ChargersApi.md#chargerscontrollerdeletetariff)                     | **DELETE** /chargers/{ppid}/tariffs/{tariffId} | Delete tariff for a given charger                                                            |
| [**chargersControllerGetChargerFirmware**](ChargersApi.md#chargerscontrollergetchargerfirmware)         | **GET** /chargers/{ppid}/firmware              | retrieve firmware for a given charger                                                        |
| [**chargersControllerGetChargerModelInfo**](ChargersApi.md#chargerscontrollergetchargermodelinfo)       | **GET** /chargers/{ppid}/model-info            | get model information for a given charger                                                    |
| [**chargersControllerGetChargerOverrides**](ChargersApi.md#chargerscontrollergetchargeroverrides)       | **GET** /chargers/{ppid}/charge-overrides      | get charge overrides for a given charger                                                     |
| [**chargersControllerGetChargerTariffs**](ChargersApi.md#chargerscontrollergetchargertariffs)           | **GET** /chargers/{ppid}/tariffs               | retrieve tariff for a given charger                                                          |
| [**chargersControllerGetChargers**](ChargersApi.md#chargerscontrollergetchargers)                       | **GET** /chargers                              | retrieve all chargers                                                                        |
| [**chargersControllerGetDnoRegion**](ChargersApi.md#chargerscontrollergetdnoregion)                     | **GET** /chargers/{ppid}/dnoregion             | retrieve charger region                                                                      |
| [**chargersControllerGetFlexEnrolment**](ChargersApi.md#chargerscontrollergetflexenrolment)             | **GET** /chargers/{ppid}/flex-enrolment        | retrieve charger's flex enrolment                                                            |
| [**chargersControllerGetFlexRequests**](ChargersApi.md#chargerscontrollergetflexrequests)               | **GET** /chargers/{ppid}/flex-requests         | retrieve a list of active flex requests for this charger                                     |
| [**chargersControllerGetRestrictions**](ChargersApi.md#chargerscontrollergetrestrictions)               | **GET** /chargers/{ppid}/restrictions          | retrieve charger's allowance based on authenticated user                                     |
| [**chargersControllerGetSolarPreferences**](ChargersApi.md#chargerscontrollergetsolarpreferences)       | **GET** /chargers/{ppid}/solar/preferences     | retrieve solar preferences for this charger                                                  |
| [**chargersControllerGetStatus**](ChargersApi.md#chargerscontrollergetstatus)                           | **GET** /chargers/{ppid}/connectivity-status   | retrieve charger's connectivity status and energy offer statuses based on authenticated user |
| [**chargersControllerSetChargerTariffs**](ChargersApi.md#chargerscontrollersetchargertariffs)           | **POST** /chargers/{ppid}/tariffs              | set tariff for a given charger                                                               |
| [**chargersControllerSetSolarPreferences**](ChargersApi.md#chargerscontrollersetsolarpreferences)       | **POST** /chargers/{ppid}/solar/preferences    | sets solar preferences for this charger                                                      |
| [**chargersControllerUpdateChargerTariff**](ChargersApi.md#chargerscontrollerupdatechargertariff)       | **PUT** /chargers/{ppid}/tariffs/{tariffId}    |
| [**ssidPasswordControllerGetWifiCredentials**](ChargersApi.md#ssidpasswordcontrollergetwificredentials) | **GET** /chargers/arch5/{ppid}                 | retrieve wifi credentials of arch5 charger                                                   |

# **chargersControllerCreateChargerOverrides**

> List<ChargeOverrideScheduleResponse> chargersControllerCreateChargerOverrides(ppid, chargeOverrideRequestDTO)

create charge overrides for a given charger

For a given PPID, create a charge override

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final chargeOverrideRequestDTO = ChargeOverrideRequestDTO(); // ChargeOverrideRequestDTO |

try {
    final result = api_instance.chargersControllerCreateChargerOverrides(ppid, chargeOverrideRequestDTO);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerCreateChargerOverrides: $e\n');
}
```

### Parameters

| Name                         | Type                                                        | Description             | Notes |
| ---------------------------- | ----------------------------------------------------------- | ----------------------- | ----- |
| **ppid**                     | **String**                                                  | PPID of a given charger |
| **chargeOverrideRequestDTO** | [**ChargeOverrideRequestDTO**](ChargeOverrideRequestDTO.md) |                         |

### Return type

[**List<ChargeOverrideScheduleResponse>**](ChargeOverrideScheduleResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerDeleteChargeOverride**

> chargersControllerDeleteChargeOverride(ppid)

delete charge overrides for a given charger

For a given PPID, delete the charge override

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    api_instance.chargersControllerDeleteChargeOverride(ppid);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerDeleteChargeOverride: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerDeleteFlexEnrolment**

> chargersControllerDeleteFlexEnrolment(ppid)

delete a charger's flex enrolment

For a given PPID and authenticated user, delete the charger's flex enrolment

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    api_instance.chargersControllerDeleteFlexEnrolment(ppid);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerDeleteFlexEnrolment: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerDeleteFlexRequest**

> chargersControllerDeleteFlexRequest(id, ppid)

delete a flex request

Delete a flex request for the given charger

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final id = d40ff7bc-cd94-4e4f-9d52-34a80e83e64b; // String | ID of the flex event to opt out of
final ppid = PP-12003; // Object | PPID of a given charger

try {
    api_instance.chargersControllerDeleteFlexRequest(id, ppid);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerDeleteFlexRequest: $e\n');
}
```

### Parameters

| Name     | Type              | Description                        | Notes |
| -------- | ----------------- | ---------------------------------- | ----- |
| **id**   | **String**        | ID of the flex event to opt out of |
| **ppid** | [**Object**](.md) | PPID of a given charger            |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerDeleteTariff**

> chargersControllerDeleteTariff(ppid, tariffId)

Delete tariff for a given charger

For a given PPID and TariffId, delete the charger's tariff

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final tariffId = 3fa85f64-5717-4562-b3fc-2c963f66afa6; // String | Id of a given tariff

try {
    api_instance.chargersControllerDeleteTariff(ppid, tariffId);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerDeleteTariff: $e\n');
}
```

### Parameters

| Name         | Type       | Description             | Notes |
| ------------ | ---------- | ----------------------- | ----- |
| **ppid**     | **String** | PPID of a given charger |
| **tariffId** | **String** | Id of a given tariff    |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetChargerFirmware**

> FirmwareStatusResponse chargersControllerGetChargerFirmware(ppid)

retrieve firmware for a given charger

For a given PPID, retrieve the firmware

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = ppid_example; // String |

try {
    final result = api_instance.chargersControllerGetChargerFirmware(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetChargerFirmware: $e\n');
}
```

### Parameters

| Name     | Type       | Description | Notes |
| -------- | ---------- | ----------- | ----- |
| **ppid** | **String** |             |

### Return type

[**FirmwareStatusResponse**](FirmwareStatusResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetChargerModelInfo**

> ChargerModelInfoResponse chargersControllerGetChargerModelInfo(ppid)

get model information for a given charger

For a given PPID, get information about the charger

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetChargerModelInfo(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetChargerModelInfo: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**ChargerModelInfoResponse**](ChargerModelInfoResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetChargerOverrides**

> List<ChargeOverrideScheduleResponse> chargersControllerGetChargerOverrides(ppid)

get charge overrides for a given charger

For a given PPID, get the charge override

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetChargerOverrides(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetChargerOverrides: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**List<ChargeOverrideScheduleResponse>**](ChargeOverrideScheduleResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetChargerTariffs**

> ChargingStationTariffSearchDto chargersControllerGetChargerTariffs(ppid)

retrieve tariff for a given charger

For a given PPID, retrieve the charger

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetChargerTariffs(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetChargerTariffs: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**ChargingStationTariffSearchDto**](ChargingStationTariffSearchDto.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetChargers**

> List<ChargerResponse> chargersControllerGetChargers()

retrieve all chargers

For the authenticated user, return all their chargers

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();

try {
    final result = api_instance.chargersControllerGetChargers();
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetChargers: $e\n');
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

[**List<ChargerResponse>**](ChargerResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetDnoRegion**

> Region chargersControllerGetDnoRegion(ppid)

retrieve charger region

For a given PSL / PPID, return the associated DNO region id.

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetDnoRegion(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetDnoRegion: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**Region**](Region.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetFlexEnrolment**

> ChargerFlexEnrolment chargersControllerGetFlexEnrolment(ppid)

retrieve charger's flex enrolment

For a given PPID and authenticated user, return the charger's flex enrolment

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetFlexEnrolment(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetFlexEnrolment: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**ChargerFlexEnrolment**](ChargerFlexEnrolment.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetFlexRequests**

> List<FlexRequest> chargersControllerGetFlexRequests(ppid)

retrieve a list of active flex requests for this charger

For a given PPID and authenticated user, return the charger's active flex requests

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetFlexRequests(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetFlexRequests: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**List<FlexRequest>**](FlexRequest.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetRestrictions**

> Restrictions chargersControllerGetRestrictions(ppid)

retrieve charger's allowance based on authenticated user

For a given PSL / PPID and authenticated user, return the charger's usage allowance

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetRestrictions(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetRestrictions: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**Restrictions**](Restrictions.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetSolarPreferences**

> SolarPreferences chargersControllerGetSolarPreferences(ppid)

retrieve solar preferences for this charger

For a given PPID and authenticated user, return the charger's solar preferences

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger

try {
    final result = api_instance.chargersControllerGetSolarPreferences(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetSolarPreferences: $e\n');
}
```

### Parameters

| Name     | Type       | Description             | Notes |
| -------- | ---------- | ----------------------- | ----- |
| **ppid** | **String** | PPID of a given charger |

### Return type

[**SolarPreferences**](SolarPreferences.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerGetStatus**

> ChargerConnectivityStatus chargersControllerGetStatus(ppid, userAgent)

retrieve charger's connectivity status and energy offer statuses based on authenticated user

For a given PPID and authenticated user, return the charger's connectivity status and energy offer statuses

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final userAgent = userAgent_example; // String |

try {
    final result = api_instance.chargersControllerGetStatus(ppid, userAgent);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerGetStatus: $e\n');
}
```

### Parameters

| Name          | Type       | Description             | Notes      |
| ------------- | ---------- | ----------------------- | ---------- |
| **ppid**      | **String** | PPID of a given charger |
| **userAgent** | **String** |                         | [optional] |

### Return type

[**ChargerConnectivityStatus**](ChargerConnectivityStatus.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerSetChargerTariffs**

> ChargerTariffDto chargersControllerSetChargerTariffs(ppid, setChargerTariffDto)

set tariff for a given charger

For a given PPID, set the charger's tariff

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final setChargerTariffDto = SetChargerTariffDto(); // SetChargerTariffDto |

try {
    final result = api_instance.chargersControllerSetChargerTariffs(ppid, setChargerTariffDto);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerSetChargerTariffs: $e\n');
}
```

### Parameters

| Name                    | Type                                              | Description             | Notes |
| ----------------------- | ------------------------------------------------- | ----------------------- | ----- |
| **ppid**                | **String**                                        | PPID of a given charger |
| **setChargerTariffDto** | [**SetChargerTariffDto**](SetChargerTariffDto.md) |                         |

### Return type

[**ChargerTariffDto**](ChargerTariffDto.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerSetSolarPreferences**

> SolarPreferences chargersControllerSetSolarPreferences(ppid, solarPreferences)

sets solar preferences for this charger

For a given PPID and authenticated user, sets the charger's solar preferences

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final solarPreferences = SolarPreferences(); // SolarPreferences |

try {
    final result = api_instance.chargersControllerSetSolarPreferences(ppid, solarPreferences);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerSetSolarPreferences: $e\n');
}
```

### Parameters

| Name                 | Type                                        | Description             | Notes |
| -------------------- | ------------------------------------------- | ----------------------- | ----- |
| **ppid**             | **String**                                  | PPID of a given charger |
| **solarPreferences** | [**SolarPreferences**](SolarPreferences.md) |                         |

### Return type

[**SolarPreferences**](SolarPreferences.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **chargersControllerUpdateChargerTariff**

> ChargerTariffDto chargersControllerUpdateChargerTariff(ppid, tariffId, setChargerTariffDto)

Given a PPID and tariff ID, replace the tariff details with the provided details

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = PP-12003; // String | PPID of a given charger
final tariffId = 3fa85f64-5717-4562-b3fc-2c963f66afa6; // String | Id of a given tariff
final setChargerTariffDto = SetChargerTariffDto(); // SetChargerTariffDto |

try {
    final result = api_instance.chargersControllerUpdateChargerTariff(ppid, tariffId, setChargerTariffDto);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->chargersControllerUpdateChargerTariff: $e\n');
}
```

### Parameters

| Name                    | Type                                              | Description             | Notes |
| ----------------------- | ------------------------------------------------- | ----------------------- | ----- |
| **ppid**                | **String**                                        | PPID of a given charger |
| **tariffId**            | **String**                                        | Id of a given tariff    |
| **setChargerTariffDto** | [**SetChargerTariffDto**](SetChargerTariffDto.md) |                         |

### Return type

[**ChargerTariffDto**](ChargerTariffDto.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ssidPasswordControllerGetWifiCredentials**

> WifiCredentials ssidPasswordControllerGetWifiCredentials(ppid)

retrieve wifi credentials of arch5 charger

For a given PSL / PPID of an arch5 charger, return the wifi credentials.

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = ChargersApi();
final ppid = ppid_example; // String |

try {
    final result = api_instance.ssidPasswordControllerGetWifiCredentials(ppid);
    print(result);
} catch (e) {
    print('Exception when calling ChargersApi->ssidPasswordControllerGetWifiCredentials: $e\n');
}
```

### Parameters

| Name     | Type       | Description | Notes |
| -------- | ---------- | ----------- | ----- |
| **ppid** | **String** |             |

### Return type

[**WifiCredentials**](WifiCredentials.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
