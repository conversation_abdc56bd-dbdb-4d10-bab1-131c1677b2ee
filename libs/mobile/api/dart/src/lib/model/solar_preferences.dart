//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SolarPreferences {
  /// Returns a new [SolarPreferences] instance.
  SolarPreferences({
    required this.powerGeneration,
    required this.solarMatching,
    this.solarThreshold,
    this.solarMaxGridImport,
  });

  String powerGeneration;

  String solarMatching;

  /// Deprecated: please use solarMaxGridImport instead
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? solarThreshold;

  /// The maximum amount of power in kWh to import from the grid. Replaces solarThreshold which has been deprecated
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? solarMaxGridImport;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SolarPreferences &&
    other.powerGeneration == powerGeneration &&
    other.solarMatching == solarMatching &&
    other.solarThreshold == solarThreshold &&
    other.solarMaxGridImport == solarMaxGridImport;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (powerGeneration.hashCode) +
    (solarMatching.hashCode) +
    (solarThreshold == null ? 0 : solarThreshold!.hashCode) +
    (solarMaxGridImport == null ? 0 : solarMaxGridImport!.hashCode);

  @override
  String toString() => 'SolarPreferences[powerGeneration=$powerGeneration, solarMatching=$solarMatching, solarThreshold=$solarThreshold, solarMaxGridImport=$solarMaxGridImport]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'powerGeneration'] = this.powerGeneration;
      json[r'solarMatching'] = this.solarMatching;
    if (this.solarThreshold != null) {
      json[r'solarThreshold'] = this.solarThreshold;
    } else {
      json[r'solarThreshold'] = null;
    }
    if (this.solarMaxGridImport != null) {
      json[r'solarMaxGridImport'] = this.solarMaxGridImport;
    } else {
      json[r'solarMaxGridImport'] = null;
    }
    return json;
  }

  /// Returns a new [SolarPreferences] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SolarPreferences? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SolarPreferences[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SolarPreferences[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SolarPreferences(
        powerGeneration: mapValueOfType<String>(json, r'powerGeneration')!,
        solarMatching: mapValueOfType<String>(json, r'solarMatching')!,
        solarThreshold: num.parse('${json[r'solarThreshold']}'),
        solarMaxGridImport: num.parse('${json[r'solarMaxGridImport']}'),
      );
    }
    return null;
  }

  static List<SolarPreferences> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SolarPreferences>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SolarPreferences.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SolarPreferences> mapFromJson(dynamic json) {
    final map = <String, SolarPreferences>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SolarPreferences.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SolarPreferences-objects as value to a dart map
  static Map<String, List<SolarPreferences>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SolarPreferences>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SolarPreferences.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'powerGeneration',
    'solarMatching',
  };
}

