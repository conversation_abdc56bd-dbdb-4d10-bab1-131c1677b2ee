//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class ChargersApi {
  ChargersApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// create charge overrides for a given charger
  ///
  /// For a given PPID, create a charge override
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [ChargeOverrideRequestDTO] chargeOverrideRequestDTO (required):
  Future<Response> chargersControllerCreateChargerOverridesWithHttpInfo(String ppid, ChargeOverrideRequestDTO chargeOverrideRequestDTO,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/charge-overrides'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody = chargeOverrideRequestDTO;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// create charge overrides for a given charger
  ///
  /// For a given PPID, create a charge override
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [ChargeOverrideRequestDTO] chargeOverrideRequestDTO (required):
  Future<List<ChargeOverrideScheduleResponse>?> chargersControllerCreateChargerOverrides(String ppid, ChargeOverrideRequestDTO chargeOverrideRequestDTO,) async {
    final response = await chargersControllerCreateChargerOverridesWithHttpInfo(ppid, chargeOverrideRequestDTO,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<ChargeOverrideScheduleResponse>') as List)
        .cast<ChargeOverrideScheduleResponse>()
        .toList(growable: false);

    }
    return null;
  }

  /// delete charge overrides for a given charger
  ///
  /// For a given PPID, delete the charge override
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerDeleteChargeOverrideWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/charge-overrides'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// delete charge overrides for a given charger
  ///
  /// For a given PPID, delete the charge override
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<void> chargersControllerDeleteChargeOverride(String ppid,) async {
    final response = await chargersControllerDeleteChargeOverrideWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// delete a charger's flex enrolment
  ///
  /// For a given PPID and authenticated user, delete the charger's flex enrolment
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerDeleteFlexEnrolmentWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/flex-enrolment'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// delete a charger's flex enrolment
  ///
  /// For a given PPID and authenticated user, delete the charger's flex enrolment
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<void> chargersControllerDeleteFlexEnrolment(String ppid,) async {
    final response = await chargersControllerDeleteFlexEnrolmentWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// delete a flex request
  ///
  /// Delete a flex request for the given charger
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///   ID of the flex event to opt out of
  ///
  /// * [Object] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerDeleteFlexRequestWithHttpInfo(String id, Object ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/flex-requests/{id}'
      .replaceAll('{id}', id)
      .replaceAll('{ppid}', ppid.toString());

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// delete a flex request
  ///
  /// Delete a flex request for the given charger
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///   ID of the flex event to opt out of
  ///
  /// * [Object] ppid (required):
  ///   PPID of a given charger
  Future<void> chargersControllerDeleteFlexRequest(String id, Object ppid,) async {
    final response = await chargersControllerDeleteFlexRequestWithHttpInfo(id, ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Delete tariff for a given charger
  ///
  /// For a given PPID and TariffId, delete the charger's tariff
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] tariffId (required):
  ///   Id of a given tariff
  Future<Response> chargersControllerDeleteTariffWithHttpInfo(String ppid, String tariffId,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/tariffs/{tariffId}'
      .replaceAll('{ppid}', ppid)
      .replaceAll('{tariffId}', tariffId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete tariff for a given charger
  ///
  /// For a given PPID and TariffId, delete the charger's tariff
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] tariffId (required):
  ///   Id of a given tariff
  Future<void> chargersControllerDeleteTariff(String ppid, String tariffId,) async {
    final response = await chargersControllerDeleteTariffWithHttpInfo(ppid, tariffId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// retrieve firmware for a given charger
  ///
  /// For a given PPID, retrieve the firmware
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  Future<Response> chargersControllerGetChargerFirmwareWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/firmware'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve firmware for a given charger
  ///
  /// For a given PPID, retrieve the firmware
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  Future<FirmwareStatusResponse?> chargersControllerGetChargerFirmware(String ppid,) async {
    final response = await chargersControllerGetChargerFirmwareWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'FirmwareStatusResponse',) as FirmwareStatusResponse;
    
    }
    return null;
  }

  /// get model information for a given charger
  ///
  /// For a given PPID, get information about the charger
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetChargerModelInfoWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/model-info'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// get model information for a given charger
  ///
  /// For a given PPID, get information about the charger
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<ChargerModelInfoResponse?> chargersControllerGetChargerModelInfo(String ppid,) async {
    final response = await chargersControllerGetChargerModelInfoWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargerModelInfoResponse',) as ChargerModelInfoResponse;
    
    }
    return null;
  }

  /// get charge overrides for a given charger
  ///
  /// For a given PPID, get the charge override
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetChargerOverridesWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/charge-overrides'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// get charge overrides for a given charger
  ///
  /// For a given PPID, get the charge override
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<List<ChargeOverrideScheduleResponse>?> chargersControllerGetChargerOverrides(String ppid,) async {
    final response = await chargersControllerGetChargerOverridesWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<ChargeOverrideScheduleResponse>') as List)
        .cast<ChargeOverrideScheduleResponse>()
        .toList(growable: false);

    }
    return null;
  }

  /// retrieve tariff for a given charger
  ///
  /// For a given PPID, retrieve the charger
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetChargerTariffsWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/tariffs'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve tariff for a given charger
  ///
  /// For a given PPID, retrieve the charger
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<ChargingStationTariffSearchDto?> chargersControllerGetChargerTariffs(String ppid,) async {
    final response = await chargersControllerGetChargerTariffsWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargingStationTariffSearchDto',) as ChargingStationTariffSearchDto;
    
    }
    return null;
  }

  /// retrieve all chargers
  ///
  /// For the authenticated user, return all their chargers
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> chargersControllerGetChargersWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/chargers';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve all chargers
  ///
  /// For the authenticated user, return all their chargers
  Future<List<ChargerResponse>?> chargersControllerGetChargers() async {
    final response = await chargersControllerGetChargersWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<ChargerResponse>') as List)
        .cast<ChargerResponse>()
        .toList(growable: false);

    }
    return null;
  }

  /// retrieve charger region
  ///
  /// For a given PSL / PPID, return the associated DNO region id.
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetDnoRegionWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/dnoregion'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve charger region
  ///
  /// For a given PSL / PPID, return the associated DNO region id.
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Region?> chargersControllerGetDnoRegion(String ppid,) async {
    final response = await chargersControllerGetDnoRegionWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'Region',) as Region;
    
    }
    return null;
  }

  /// retrieve charger's flex enrolment
  ///
  /// For a given PPID and authenticated user, return the charger's flex enrolment
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetFlexEnrolmentWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/flex-enrolment'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve charger's flex enrolment
  ///
  /// For a given PPID and authenticated user, return the charger's flex enrolment
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<ChargerFlexEnrolment?> chargersControllerGetFlexEnrolment(String ppid,) async {
    final response = await chargersControllerGetFlexEnrolmentWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargerFlexEnrolment',) as ChargerFlexEnrolment;
    
    }
    return null;
  }

  /// retrieve a list of active flex requests for this charger
  ///
  /// For a given PPID and authenticated user, return the charger's active flex requests
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetFlexRequestsWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/flex-requests'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve a list of active flex requests for this charger
  ///
  /// For a given PPID and authenticated user, return the charger's active flex requests
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<List<FlexRequest>?> chargersControllerGetFlexRequests(String ppid,) async {
    final response = await chargersControllerGetFlexRequestsWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<FlexRequest>') as List)
        .cast<FlexRequest>()
        .toList(growable: false);

    }
    return null;
  }

  /// retrieve charger's allowance based on authenticated user
  ///
  /// For a given PSL / PPID and authenticated user, return the charger's usage allowance
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetRestrictionsWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/restrictions'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve charger's allowance based on authenticated user
  ///
  /// For a given PSL / PPID and authenticated user, return the charger's usage allowance
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Restrictions?> chargersControllerGetRestrictions(String ppid,) async {
    final response = await chargersControllerGetRestrictionsWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'Restrictions',) as Restrictions;
    
    }
    return null;
  }

  /// retrieve solar preferences for this charger
  ///
  /// For a given PPID and authenticated user, return the charger's solar preferences
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<Response> chargersControllerGetSolarPreferencesWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/solar/preferences'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve solar preferences for this charger
  ///
  /// For a given PPID and authenticated user, return the charger's solar preferences
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  Future<SolarPreferences?> chargersControllerGetSolarPreferences(String ppid,) async {
    final response = await chargersControllerGetSolarPreferencesWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SolarPreferences',) as SolarPreferences;
    
    }
    return null;
  }

  /// retrieve charger's connectivity status and energy offer statuses based on authenticated user
  ///
  /// For a given PPID and authenticated user, return the charger's connectivity status and energy offer statuses
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] userAgent:
  Future<Response> chargersControllerGetStatusWithHttpInfo(String ppid, { String? userAgent, }) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/connectivity-status'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (userAgent != null) {
      headerParams[r'user-agent'] = parameterToString(userAgent);
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve charger's connectivity status and energy offer statuses based on authenticated user
  ///
  /// For a given PPID and authenticated user, return the charger's connectivity status and energy offer statuses
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] userAgent:
  Future<ChargerConnectivityStatus?> chargersControllerGetStatus(String ppid, { String? userAgent, }) async {
    final response = await chargersControllerGetStatusWithHttpInfo(ppid,  userAgent: userAgent, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargerConnectivityStatus',) as ChargerConnectivityStatus;
    
    }
    return null;
  }

  /// set tariff for a given charger
  ///
  /// For a given PPID, set the charger's tariff
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [SetChargerTariffDto] setChargerTariffDto (required):
  Future<Response> chargersControllerSetChargerTariffsWithHttpInfo(String ppid, SetChargerTariffDto setChargerTariffDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/tariffs'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody = setChargerTariffDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// set tariff for a given charger
  ///
  /// For a given PPID, set the charger's tariff
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [SetChargerTariffDto] setChargerTariffDto (required):
  Future<ChargerTariffDto?> chargersControllerSetChargerTariffs(String ppid, SetChargerTariffDto setChargerTariffDto,) async {
    final response = await chargersControllerSetChargerTariffsWithHttpInfo(ppid, setChargerTariffDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargerTariffDto',) as ChargerTariffDto;
    
    }
    return null;
  }

  /// sets solar preferences for this charger
  ///
  /// For a given PPID and authenticated user, sets the charger's solar preferences
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [SolarPreferences] solarPreferences (required):
  Future<Response> chargersControllerSetSolarPreferencesWithHttpInfo(String ppid, SolarPreferences solarPreferences,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/solar/preferences'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody = solarPreferences;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// sets solar preferences for this charger
  ///
  /// For a given PPID and authenticated user, sets the charger's solar preferences
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [SolarPreferences] solarPreferences (required):
  Future<SolarPreferences?> chargersControllerSetSolarPreferences(String ppid, SolarPreferences solarPreferences,) async {
    final response = await chargersControllerSetSolarPreferencesWithHttpInfo(ppid, solarPreferences,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SolarPreferences',) as SolarPreferences;
    
    }
    return null;
  }

  /// 
  ///
  /// Given a PPID and tariff ID, replace the tariff details with the provided details
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] tariffId (required):
  ///   Id of a given tariff
  ///
  /// * [SetChargerTariffDto] setChargerTariffDto (required):
  Future<Response> chargersControllerUpdateChargerTariffWithHttpInfo(String ppid, String tariffId, SetChargerTariffDto setChargerTariffDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/{ppid}/tariffs/{tariffId}'
      .replaceAll('{ppid}', ppid)
      .replaceAll('{tariffId}', tariffId);

    // ignore: prefer_final_locals
    Object? postBody = setChargerTariffDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// 
  ///
  /// Given a PPID and tariff ID, replace the tariff details with the provided details
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  ///   PPID of a given charger
  ///
  /// * [String] tariffId (required):
  ///   Id of a given tariff
  ///
  /// * [SetChargerTariffDto] setChargerTariffDto (required):
  Future<ChargerTariffDto?> chargersControllerUpdateChargerTariff(String ppid, String tariffId, SetChargerTariffDto setChargerTariffDto,) async {
    final response = await chargersControllerUpdateChargerTariffWithHttpInfo(ppid, tariffId, setChargerTariffDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ChargerTariffDto',) as ChargerTariffDto;
    
    }
    return null;
  }

  /// retrieve wifi credentials of arch5 charger
  ///
  /// For a given PSL / PPID of an arch5 charger, return the wifi credentials.
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  Future<Response> ssidPasswordControllerGetWifiCredentialsWithHttpInfo(String ppid,) async {
    // ignore: prefer_const_declarations
    final path = r'/chargers/arch5/{ppid}'
      .replaceAll('{ppid}', ppid);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve wifi credentials of arch5 charger
  ///
  /// For a given PSL / PPID of an arch5 charger, return the wifi credentials.
  ///
  /// Parameters:
  ///
  /// * [String] ppid (required):
  Future<WifiCredentials?> ssidPasswordControllerGetWifiCredentials(String ppid,) async {
    final response = await ssidPasswordControllerGetWifiCredentialsWithHttpInfo(ppid,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'WifiCredentials',) as WifiCredentials;
    
    }
    return null;
  }
}
