//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ApiClient {
  ApiClient({this.basePath = 'http://localhost', this.authentication,});

  final String basePath;
  final Authentication? authentication;

  var _client = Client();
  final _defaultHeaderMap = <String, String>{};

  /// Returns the current HTTP [Client] instance to use in this class.
  ///
  /// The return value is guaranteed to never be null.
  Client get client => _client;

  /// Requests to use a new HTTP [Client] in this class.
  set client(Client newClient) {
    _client = newClient;
  }

  Map<String, String> get defaultHeaderMap => _defaultHeaderMap;

  void addDefaultHeader(String key, String value) {
     _defaultHeaderMap[key] = value;
  }

  // We don't use a Map<String, String> for queryParams.
  // If collectionFormat is 'multi', a key might appear multiple times.
  Future<Response> invokeAPI(
    String path,
    String method,
    List<QueryParam> queryParams,
    Object? body,
    Map<String, String> headerParams,
    Map<String, String> formParams,
    String? contentType,
  ) async {
    await authentication?.applyToParams(queryParams, headerParams);

    headerParams.addAll(_defaultHeaderMap);
    if (contentType != null) {
      headerParams['Content-Type'] = contentType;
    }

    final urlEncodedQueryParams = queryParams.map((param) => '$param');
    final queryString = urlEncodedQueryParams.isNotEmpty ? '?${urlEncodedQueryParams.join('&')}' : '';
    final uri = Uri.parse('$basePath$path$queryString');

    try {
      // Special case for uploading a single file which isn't a 'multipart/form-data'.
      if (
        body is MultipartFile && (contentType == null ||
        !contentType.toLowerCase().startsWith('multipart/form-data'))
      ) {
        final request = StreamedRequest(method, uri);
        request.headers.addAll(headerParams);
        request.contentLength = body.length;
        body.finalize().listen(
          request.sink.add,
          onDone: request.sink.close,
          // ignore: avoid_types_on_closure_parameters
          onError: (Object error, StackTrace trace) => request.sink.close(),
          cancelOnError: true,
        );
        final response = await _client.send(request);
        return Response.fromStream(response);
      }

      if (body is MultipartRequest) {
        final request = MultipartRequest(method, uri);
        request.fields.addAll(body.fields);
        request.files.addAll(body.files);
        request.headers.addAll(body.headers);
        request.headers.addAll(headerParams);
        final response = await _client.send(request);
        return Response.fromStream(response);
      }

      final msgBody = contentType == 'application/x-www-form-urlencoded'
        ? formParams
        : await serializeAsync(body);
      final nullableHeaderParams = headerParams.isEmpty ? null : headerParams;

      switch(method) {
        case 'POST': return await _client.post(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'PUT': return await _client.put(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'DELETE': return await _client.delete(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'PATCH': return await _client.patch(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'HEAD': return await _client.head(uri, headers: nullableHeaderParams,);
        case 'GET': return await _client.get(uri, headers: nullableHeaderParams,);
      }
    } on SocketException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'Socket operation failed: $method $path',
        error,
        trace,
      );
    } on TlsException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'TLS/SSL communication failed: $method $path',
        error,
        trace,
      );
    } on IOException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'I/O operation failed: $method $path',
        error,
        trace,
      );
    } on ClientException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'HTTP connection failed: $method $path',
        error,
        trace,
      );
    } on Exception catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'Exception occurred: $method $path',
        error,
        trace,
      );
    }

    throw ApiException(
      HttpStatus.badRequest,
      'Invalid HTTP operation: $method $path',
    );
  }

  Future<dynamic> deserializeAsync(String value, String targetType, {bool growable = false,}) async =>
    // ignore: deprecated_member_use_from_same_package
    deserialize(value, targetType, growable: growable);

  @Deprecated('Scheduled for removal in OpenAPI Generator 6.x. Use deserializeAsync() instead.')
  dynamic deserialize(String value, String targetType, {bool growable = false,}) {
    // Remove all spaces. Necessary for regular expressions as well.
    targetType = targetType.replaceAll(' ', ''); // ignore: parameter_assignments

    // If the expected target type is String, nothing to do...
    return targetType == 'String'
      ? value
      : fromJson(json.decode(value), targetType, growable: growable);
  }

  // ignore: deprecated_member_use_from_same_package
  Future<String> serializeAsync(Object? value) async => serialize(value);

  @Deprecated('Scheduled for removal in OpenAPI Generator 6.x. Use serializeAsync() instead.')
  String serialize(Object? value) => value == null ? '' : json.encode(value);

  /// Returns a native instance of an OpenAPI class matching the [specified type][targetType].
  static dynamic fromJson(dynamic value, String targetType, {bool growable = false,}) {
    try {
      switch (targetType) {
        case 'String':
          return value is String ? value : value.toString();
        case 'int':
          return value is int ? value : int.parse('$value');
        case 'double':
          return value is double ? value : double.parse('$value');
        case 'bool':
          if (value is bool) {
            return value;
          }
          final valueString = '$value'.toLowerCase();
          return valueString == 'true' || valueString == '1';
        case 'DateTime':
          return value is DateTime ? value : DateTime.tryParse(value);
        case 'AccountTopUpRequestDTO':
          return AccountTopUpRequestDTO.fromJson(value);
        case 'Address':
          return Address.fromJson(value);
        case 'BalanceDto':
          return BalanceDto.fromJson(value);
        case 'ChargeOverrideRequestDTO':
          return ChargeOverrideRequestDTO.fromJson(value);
        case 'ChargeOverrideScheduleChargingStation':
          return ChargeOverrideScheduleChargingStation.fromJson(value);
        case 'ChargeOverrideScheduleEves':
          return ChargeOverrideScheduleEves.fromJson(value);
        case 'ChargeOverrideScheduleResponse':
          return ChargeOverrideScheduleResponse.fromJson(value);
        case 'ChargeRequestDTO':
          return ChargeRequestDTO.fromJson(value);
        case 'ChargeRequestResponse':
          return ChargeRequestResponse.fromJson(value);
        case 'ChargeStatsResponse':
          return ChargeStatsResponse.fromJson(value);
        case 'ChargerConnectivityStatus':
          return ChargerConnectivityStatus.fromJson(value);
        case 'ChargerDelegatedControlResponse':
          return ChargerDelegatedControlResponse.fromJson(value);
        case 'ChargerFlexEnrolment':
          return ChargerFlexEnrolment.fromJson(value);
        case 'ChargerModelInfoResponse':
          return ChargerModelInfoResponse.fromJson(value);
        case 'ChargerResponse':
          return ChargerResponse.fromJson(value);
        case 'ChargerTariffDto':
          return ChargerTariffDto.fromJson(value);
        case 'ChargersAndVehicles':
          return ChargersAndVehicles.fromJson(value);
        case 'ChargesResponse':
          return ChargesResponse.fromJson(value);
        case 'ChargingStationTariffSearchCriteriaDtoImpl':
          return ChargingStationTariffSearchCriteriaDtoImpl.fromJson(value);
        case 'ChargingStationTariffSearchDto':
          return ChargingStationTariffSearchDto.fromJson(value);
        case 'ChargingStationTariffSearchMetadataDtoImpl':
          return ChargingStationTariffSearchMetadataDtoImpl.fromJson(value);
        case 'CheckForUpgrade':
          return CheckForUpgrade.fromJson(value);
        case 'ConfirmationOfPayeeDTO':
          return ConfirmationOfPayeeDTO.fromJson(value);
        case 'ConnectedChargeStateImpl':
          return ConnectedChargeStateImpl.fromJson(value);
        case 'ConnectedStatefulVehicleDtoImpl':
          return ConnectedStatefulVehicleDtoImpl.fromJson(value);
        case 'ConsentDto':
          return ConsentDto.fromJson(value);
        case 'CreateIntentResponse':
          return CreateIntentResponse.fromJson(value);
        case 'CreatePaymentRequest':
          return CreatePaymentRequest.fromJson(value);
        case 'CreateRegisteredUserPaymentResponse':
          return CreateRegisteredUserPaymentResponse.fromJson(value);
        case 'CreateUserPayload':
          return CreateUserPayload.fromJson(value);
        case 'CreateUserResponseDto':
          return CreateUserResponseDto.fromJson(value);
        case 'CurrentIntentDtoChargingStationImpl':
          return CurrentIntentDtoChargingStationImpl.fromJson(value);
        case 'CurrentIntentDtoImpl':
          return CurrentIntentDtoImpl.fromJson(value);
        case 'DelegatedControlChargingStationResponseDtoImpl':
          return DelegatedControlChargingStationResponseDtoImpl.fromJson(value);
        case 'DnoRegion':
          return DnoRegion.fromJson(value);
        case 'DnoRegions':
          return DnoRegions.fromJson(value);
        case 'EnrolmentStatus':
          return EnrolmentStatus.fromJson(value);
        case 'Entry':
          return Entry.fromJson(value);
        case 'EvseComponent':
          return EvseComponent.fromJson(value);
        case 'ExpensesRequestBody':
          return ExpensesRequestBody.fromJson(value);
        case 'ExpensesResponse':
          return ExpensesResponse.fromJson(value);
        case 'ExtendedUserInfoResponseDto':
          return ExtendedUserInfoResponseDto.fromJson(value);
        case 'ExtendedVehicleLinksResponseDtoImpl':
          return ExtendedVehicleLinksResponseDtoImpl.fromJson(value);
        case 'FcmTokenDtoImpl':
          return FcmTokenDtoImpl.fromJson(value);
        case 'FirmwareStatusResponse':
          return FirmwareStatusResponse.fromJson(value);
        case 'FirmwareStatusResponseData':
          return FirmwareStatusResponseData.fromJson(value);
        case 'FirmwareStatusResponseManifestType':
          return FirmwareStatusResponseManifestType.fromJson(value);
        case 'FirmwareStatusResponseManifestTypeDetails':
          return FirmwareStatusResponseManifestTypeDetails.fromJson(value);
        case 'FirmwareStatusResponseUpdateStatus':
          return FirmwareStatusResponseUpdateStatus.fromJson(value);
        case 'FlexRequest':
          return FlexRequest.fromJson(value);
        case 'FlexRequestLimit':
          return FlexRequestLimit.fromJson(value);
        case 'Forecast':
          return Forecast.fromJson(value);
        case 'ForecastSnapshot':
          return ForecastSnapshot.fromJson(value);
        case 'Forecastdata':
          return Forecastdata.fromJson(value);
        case 'GenericChargeStateImpl':
          return GenericChargeStateImpl.fromJson(value);
        case 'GenericStatefulVehicleDtoImpl':
          return GenericStatefulVehicleDtoImpl.fromJson(value);
        case 'GetLinkSessionRequest':
          return GetLinkSessionRequest.fromJson(value);
        case 'GetLinkSessionResponse':
          return GetLinkSessionResponse.fromJson(value);
        case 'HealthControllerCheck200Response':
          return HealthControllerCheck200Response.fromJson(value);
        case 'HealthControllerCheck200ResponseInfoValue':
          return HealthControllerCheck200ResponseInfoValue.fromJson(value);
        case 'HealthControllerCheck503Response':
          return HealthControllerCheck503Response.fromJson(value);
        case 'Intensity':
          return Intensity.fromJson(value);
        case 'InterventionDtoImpl':
          return InterventionDtoImpl.fromJson(value);
        case 'Item':
          return Item.fromJson(value);
        case 'LinkyDTO':
          return LinkyDTO.fromJson(value);
        case 'LocaleResponse':
          return LocaleResponse.fromJson(value);
        case 'LocationControllerFind400Response':
          return LocationControllerFind400Response.fromJson(value);
        case 'LocationControllerSearch400Response':
          return LocationControllerSearch400Response.fromJson(value);
        case 'MarketingDto':
          return MarketingDto.fromJson(value);
        case 'MarketingOpportunitiesDTO':
          return MarketingOpportunitiesDTO.fromJson(value);
        case 'PayoutRequestDTOImpl':
          return PayoutRequestDTOImpl.fromJson(value);
        case 'PayoutResponseDTOImpl':
          return PayoutResponseDTOImpl.fromJson(value);
        case 'Period':
          return Period.fromJson(value);
        case 'PreferencesDto':
          return PreferencesDto.fromJson(value);
        case 'QueryCheckForUpgrade':
          return QueryCheckForUpgrade.fromJson(value);
        case 'Region':
          return Region.fromJson(value);
        case 'RemoteLockDTO':
          return RemoteLockDTO.fromJson(value);
        case 'ReportsDtoPayload':
          return ReportsDtoPayload.fromJson(value);
        case 'ResetPasswordRequestDto':
          return ResetPasswordRequestDto.fromJson(value);
        case 'Restrictions':
          return Restrictions.fromJson(value);
        case 'RewardsAccountAddressDTO':
          return RewardsAccountAddressDTO.fromJson(value);
        case 'RewardsAccountDTO':
          return RewardsAccountDTO.fromJson(value);
        case 'RewardsBankAccountDTOImpl':
          return RewardsBankAccountDTOImpl.fromJson(value);
        case 'RewardsChargerDto':
          return RewardsChargerDto.fromJson(value);
        case 'RewardsDto':
          return RewardsDto.fromJson(value);
        case 'RewardsTransactionDTOImpl':
          return RewardsTransactionDTOImpl.fromJson(value);
        case 'SendRecoverFactorRequest':
          return SendRecoverFactorRequest.fromJson(value);
        case 'SendVerifyAndChangeEmailRequest':
          return SendVerifyAndChangeEmailRequest.fromJson(value);
        case 'SetChargerTariffDto':
          return SetChargerTariffDto.fromJson(value);
        case 'SetLinkyDTO':
          return SetLinkyDTO.fromJson(value);
        case 'SetVehicleIntent':
          return SetVehicleIntent.fromJson(value);
        case 'SmartChargingControllerUpdateVehicle200Response':
          return SmartChargingControllerUpdateVehicle200Response.fromJson(value);
        case 'SolarPreferences':
          return SolarPreferences.fromJson(value);
        case 'SubmitSupportFeedbackDTO':
          return SubmitSupportFeedbackDTO.fromJson(value);
        case 'SupplierDtoImpl':
          return SupplierDtoImpl.fromJson(value);
        case 'TariffInfoDtoImpl':
          return TariffInfoDtoImpl.fromJson(value);
        case 'TariffRequest':
          return TariffRequest.fromJson(value);
        case 'TariffResponse':
          return TariffResponse.fromJson(value);
        case 'Tier':
          return Tier.fromJson(value);
        case 'TrackLoginRequest':
          return TrackLoginRequest.fromJson(value);
        case 'UpdateUserDto':
          return UpdateUserDto.fromJson(value);
        case 'UpdateVehicleLinkRequestDtoImpl':
          return UpdateVehicleLinkRequestDtoImpl.fromJson(value);
        case 'User':
          return User.fromJson(value);
        case 'UserDetailsDto':
          return UserDetailsDto.fromJson(value);
        case 'UserResponse':
          return UserResponse.fromJson(value);
        case 'VehicleChargeInfoDtoImpl':
          return VehicleChargeInfoDtoImpl.fromJson(value);
        case 'VehicleInformationImpl':
          return VehicleInformationImpl.fromJson(value);
        case 'VehicleIntentEntry':
          return VehicleIntentEntry.fromJson(value);
        case 'VehicleIntentEntryDtoImpl':
          return VehicleIntentEntryDtoImpl.fromJson(value);
        case 'VehicleIntentsRequestDtoImpl':
          return VehicleIntentsRequestDtoImpl.fromJson(value);
        case 'VehicleIntentsResponseDtoImpl':
          return VehicleIntentsResponseDtoImpl.fromJson(value);
        case 'VehicleInterventionDtoImpl':
          return VehicleInterventionDtoImpl.fromJson(value);
        case 'VehicleInterventionResolutionDtoImpl':
          return VehicleInterventionResolutionDtoImpl.fromJson(value);
        case 'VehicleInterventionResponseDtoImpl':
          return VehicleInterventionResponseDtoImpl.fromJson(value);
        case 'VehicleLinkRequestDtoImpl':
          return VehicleLinkRequestDtoImpl.fromJson(value);
        case 'VehicleLinkResponseDtoImpl':
          return VehicleLinkResponseDtoImpl.fromJson(value);
        case 'VehicleLinkResponseDtoImplVehicle':
          return VehicleLinkResponseDtoImplVehicle.fromJson(value);
        case 'VehicleRequestDtoImpl':
          return VehicleRequestDtoImpl.fromJson(value);
        case 'WifiCredentials':
          return WifiCredentials.fromJson(value);
        default:
          dynamic match;
          if (value is List && (match = _regList.firstMatch(targetType)?.group(1)) != null) {
            return value
              .map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,))
              .toList(growable: growable);
          }
          if (value is Set && (match = _regSet.firstMatch(targetType)?.group(1)) != null) {
            return value
              .map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,))
              .toSet();
          }
          if (value is Map && (match = _regMap.firstMatch(targetType)?.group(1)) != null) {
            return Map<String, dynamic>.fromIterables(
              value.keys.cast<String>(),
              value.values.map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,)),
            );
          }
      }
    } on Exception catch (error, trace) {
      throw ApiException.withInner(HttpStatus.internalServerError, 'Exception during deserialization.', error, trace,);
    }
    throw ApiException(HttpStatus.internalServerError, 'Could not find a suitable class for deserialization',);
  }
}

/// Primarily intended for use in an isolate.
class DeserializationMessage {
  const DeserializationMessage({
    required this.json,
    required this.targetType,
    this.growable = false,
  });

  /// The JSON value to deserialize.
  final String json;

  /// Target type to deserialize to.
  final String targetType;

  /// Whether to make deserialized lists or maps growable.
  final bool growable;
}

/// Primarily intended for use in an isolate.
Future<dynamic> decodeAsync(DeserializationMessage message) async {
  // Remove all spaces. Necessary for regular expressions as well.
  final targetType = message.targetType.replaceAll(' ', '');

  // If the expected target type is String, nothing to do...
  return targetType == 'String'
    ? message.json
    : json.decode(message.json);
}

/// Primarily intended for use in an isolate.
Future<dynamic> deserializeAsync(DeserializationMessage message) async {
  // Remove all spaces. Necessary for regular expressions as well.
  final targetType = message.targetType.replaceAll(' ', '');

  // If the expected target type is String, nothing to do...
  return targetType == 'String'
    ? message.json
    : ApiClient.fromJson(
        json.decode(message.json),
        targetType,
        growable: message.growable,
      );
}

/// Primarily intended for use in an isolate.
Future<String> serializeAsync(Object? value) async => value == null ? '' : json.encode(value);
