/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { FirmwareStatusResponseManifestTypeDetails } from './firmwareStatusResponseManifestTypeDetails';

export interface FirmwareStatusResponseManifestType {
  architecture: FirmwareStatusResponseManifestType.ArchitectureEnum;
  createdDate?: string | null;
  details: FirmwareStatusResponseManifestTypeDetails;
  manifestId?: string | null;
  status?: FirmwareStatusResponseManifestType.StatusEnum;
}
export namespace FirmwareStatusResponseManifestType {
  export type ArchitectureEnum =
    | 'arch1'
    | 'arch2'
    | 'arch2.4'
    | 'arch3'
    | 'arch5';
  export const ArchitectureEnum = {
    Arch1: 'arch1' as ArchitectureEnum,
    Arch2: 'arch2' as ArchitectureEnum,
    Arch24: 'arch2.4' as ArchitectureEnum,
    Arch3: 'arch3' as ArchitectureEnum,
    Arch5: 'arch5' as ArchitectureEnum,
  };
  export type StatusEnum =
    | 'candidate'
    | 'alpha'
    | 'beta'
    | 'release'
    | 'archived';
  export const StatusEnum = {
    Candidate: 'candidate' as StatusEnum,
    Alpha: 'alpha' as StatusEnum,
    Beta: 'beta' as StatusEnum,
    Release: 'release' as StatusEnum,
    Archived: 'archived' as StatusEnum,
  };
}
