/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface ConnectedChargeStateImpl {
  /**
   * Battery capacity
   */
  batteryCapacity: number;
  /**
   * Battery level percent
   */
  batteryLevelPercent: number | null;
  /**
   * Charge limit percent
   */
  chargeLimitPercent: number | null;
  /**
   * Charge rate
   */
  chargeRate: number | null;
  /**
   * Charge time remaining
   */
  chargeTimeRemaining: number | null;
  /**
   * Is charging?
   */
  isCharging: boolean | null;
  /**
   * Is fully charged?
   */
  isFullyCharged: boolean | null;
  /**
   * Is plugged in?
   */
  isPluggedIn: boolean | null;
  /**
   * Last updated
   */
  lastUpdated: string | null;
  /**
   * Max current
   */
  maxCurrent: number | null;
  powerDeliveryState: ConnectedChargeStateImpl.PowerDeliveryStateEnum | null;
  /**
   * Range
   */
  range: number | null;
}
export namespace ConnectedChargeStateImpl {
  export type PowerDeliveryStateEnum =
    | 'UNPLUGGED'
    | 'PLUGGED_IN:NO_POWER'
    | 'PLUGGED_IN:STOPPED'
    | 'PLUGGED_IN:COMPLETE'
    | 'PLUGGED_IN:CHARGING'
    | 'UNKNOWN'
    | 'PLUGGED_IN:INITIALIZING'
    | 'PLUGGED_IN:FAULT';
  export const PowerDeliveryStateEnum = {
    Unplugged: 'UNPLUGGED' as PowerDeliveryStateEnum,
    PluggedInNoPower: 'PLUGGED_IN:NO_POWER' as PowerDeliveryStateEnum,
    PluggedInStopped: 'PLUGGED_IN:STOPPED' as PowerDeliveryStateEnum,
    PluggedInComplete: 'PLUGGED_IN:COMPLETE' as PowerDeliveryStateEnum,
    PluggedInCharging: 'PLUGGED_IN:CHARGING' as PowerDeliveryStateEnum,
    Unknown: 'UNKNOWN' as PowerDeliveryStateEnum,
    PluggedInInitializing: 'PLUGGED_IN:INITIALIZING' as PowerDeliveryStateEnum,
    PluggedInFault: 'PLUGGED_IN:FAULT' as PowerDeliveryStateEnum,
  };
}
