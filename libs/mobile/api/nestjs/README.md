# THIS IS AN AUTOGENERATED FILE

Inside an autogenerated lib.

## Next step

To actually use this lib, you must now run the `generate-sources` builder.

The `generate-sources` builder has been pre-configured with the options you supplied when creating this lib with the `api-lib` schematic.  
You can go check those options, and modify them if you need to, in `workspace.json`.

When you are good, generate the sources running:

```sh
nx run mobile-api-nestjs:generate-sources
```

You should run - either manually or via CI - this `generate-sources` builder every time the API spec file changes.

## Multiple libs

You can have multiple libs like this one, and configure them to use different OpenAPITools generators, or generate sources from different API spec files.

### Example

```sh
# Create a lib preconfigured to generate a TypeScript SDK based on Axios
nx generate @trumbitta/nx-plugin-openapi:api-lib api-axios --generator typescript-axios

# Create a lib preconfigured to generate HTML docs
nx generate @trumbitta/nx-plugin-openapi:api-lib api-docs --generator html

# Generate or update the TypeScript SDK based on Axios
nx run api-axios:generate-sources

# Generate or update the HTML API docs
nx run api-docs:generate-sources
```
