/**
 * Basic interface that persisted entities should meet
 *
 * @example
 * export class OrderEntity implements EntityInterface<string> {
 *  id: string; // enforces this key
 *  origin: OrderOrigin;
 *  orderedAt: Date;
 * }
 */
export interface EntityInterface<IdType> {
  id: IdType;
}

/**
 * Interface that paranoid persisted entities should meet
 *
 * @example
 * export class PlanEntity implements ParanoidEntityInterface<string> {
 *  id: string; // enforces this key
 *  details: PlanDetails;
 *  subscriptionId: UnpersistedOmit<string>;
 *  createdAt: Date; // enforces this key
 *  updatedAt: Date; // enforces this key
 *  deletedAt: Date | null; // enforces this key
 * }
 */
export interface ParanoidEntityInterface<IdType>
  extends EntityInterface<IdType> {
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}
