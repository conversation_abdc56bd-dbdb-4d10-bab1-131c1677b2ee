import { ApiHeader, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Headers,
  HttpCode,
  Post,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { SendVerifyAndChangeEmailRequest } from '@experience/mobile/driver-account/domain/auth';
import { UserEmailEnabledGuard } from '../guard/user-email-enabled.guard';
import { VerifyAndChangeEmailService } from './verify-and-change-email.service';

@ApiTags('Auth')
@Controller({
  path: 'auth',
  version: '1',
})
export class VerifyAndChangeEmailController {
  constructor(
    private readonly verifyAndChangeEmailService: VerifyAndChangeEmailService
  ) {}

  @Post('verify-and-change-email')
  @ApiOperation({
    summary: "changes a user's email address",
    description:
      "Sends a verification email to the user's new email address, updating the user's email address once the new email address has been verified",
  })
  @ApiHeader({
    name: 'x-app-name',
    description: 'The name of the requesting app',
    required: false,
  })
  @HttpCode(202)
  @UseGuards(UserEmailEnabledGuard)
  async updateEmail(
    @Body(ValidationPipe) request: SendVerifyAndChangeEmailRequest,
    @Headers('Accept-Language') language = 'en',
    @Headers('x-app-name') appName?: string
  ): Promise<void> {
    return this.verifyAndChangeEmailService.sendVerifyAndChangeEmail(
      request,
      language,
      appName
    );
  }
}
