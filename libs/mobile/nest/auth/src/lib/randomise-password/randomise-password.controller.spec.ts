import {
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/mobile/driver-account/typescript/domain-model-validation';
import { INestApplication } from '@nestjs/common';
import { RandomisePasswordController } from './randomise-password.controller';
import { RevokeTokenService } from '@experience/mobile/nest/revoke-token';
import { TEST_RANDOMISE_PASSWORD_REQUEST } from '@experience/mobile/driver-account/domain/auth';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 as uuid } from 'uuid';
import request from 'supertest';

jest.mock('@experience/mobile/nest/revoke-token');

describe('RandomisePasswordController', () => {
  let app: INestApplication;
  let revokeTokenService: RevokeTokenService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RandomisePasswordController],
      providers: [
        {
          provide: RevokeTokenService,
          useClass: RevokeTokenService,
        },
      ],
    }).compile();

    revokeTokenService = module.get<RevokeTokenService>(RevokeTokenService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('randomisePassword', () => {
    it('should return 200 on success', async () => {
      const mockRevokeTokenByEmail = jest
        .spyOn(revokeTokenService, 'revokeUserByEmail')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post('/auth/randomise-password')
        .send(TEST_RANDOMISE_PASSWORD_REQUEST);

      expect(response.status).toEqual(200);
      expect(mockRevokeTokenByEmail).toHaveBeenCalledWith(
        TEST_RANDOMISE_PASSWORD_REQUEST.email
      );
    });

    it('should throw a validation error when email address is missing', () => {
      request(app.getHttpServer())
        .post('/auth/randomise-password')
        .send({ email: undefined })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [
            COMMON_REQUIRED_ERROR,
            COMMON_EMAIL_MAX_LENGTH_ERROR,
            COMMON_INVALID_EMAIL_ERROR,
          ],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when email address is invalid', () => {
      request(app.getHttpServer())
        .post('/auth/randomise-password')
        .send({ email: 'hello' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when email address is too long', () => {
      request(app.getHttpServer())
        .post('/auth/randomise-password')
        .send({ email: uuid().repeat(10) + '@email.com' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_EMAIL_MAX_LENGTH_ERROR, COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        });
    });
  });
});
