import { Api3TokenService } from '@experience/mobile/nest/api3-token';
import { Auth } from 'firebase-admin/auth';
import {
  AuthModule,
  AuthResponse,
  AuthService,
} from '@experience/mobile/nest/auth-service';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { THIRD_PARTY_CLAIM_KEY } from './constants';
import { Test, TestingModule } from '@nestjs/testing';
import { TokenValidatorWithRemoteConfig } from './token.validator.rc';
import { UnauthorizedException } from '@nestjs/common';
import { createJWTUserToken } from '@experience/mobile/test/mocking';
import {
  generateToken,
  mockVerifyIdToken,
} from '@experience/mobile/test/mocking';

describe('TokenValidatorWithRemoteConfig', () => {
  let tokenValidator: TokenValidatorWithRemoteConfig;
  let api3TokenService: Api3TokenService;

  const TOKEN = generateToken('DRIVER_AUTH_PRIVATE_KEY', '30d');
  const API3_CLIENT_TOKEN = generateToken('DRIVER_AUTH_PRIVATE_KEY', '30d');

  const email = '<EMAIL>';
  const userId = 'xxxx';
  const id = 1;
  const GIP_TOKEN = createJWTUserToken(
    process.env.GIP_AUDIENCE,
    process.env.GIP_AUTH_PRIVATE_KEY,
    userId,
    email,
    id
  );
  const GIP_TOKEN_NON_VERIFIED = createJWTUserToken(
    process.env.GIP_AUDIENCE,
    process.env.GIP_AUTH_PRIVATE_KEY,
    userId,
    email,
    id,
    false
  );
  const GIP_TOKEN_THIRD_PARTY = createJWTUserToken(
    process.env.GIP_AUDIENCE,
    process.env.GIP_AUTH_PRIVATE_KEY,
    userId,
    email,
    id,
    true,
    { [THIRD_PARTY_CLAIM_KEY]: true }
  );

  const authResponse: AuthResponse = {
    token_type: 'Bearer',
    access_token: API3_CLIENT_TOKEN,
    expires_in: 90,
  };

  const createModule = async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AuthModule, ConfigModule, CacheModule.register()],
      providers: [Api3TokenService, TokenValidatorWithRemoteConfig],
    }).compile();

    api3TokenService = module.get<Api3TokenService>(Api3TokenService);
    jest
      .spyOn(AuthService.prototype, 'getToken')
      .mockResolvedValueOnce(authResponse);
    tokenValidator = module.get<TokenValidatorWithRemoteConfig>(
      TokenValidatorWithRemoteConfig
    );
  };
  beforeEach(async () => {
    jest.clearAllMocks();
    await createModule();
  });

  it('should be defined', () => {
    expect(api3TokenService).toBeDefined();
    expect(tokenValidator).toBeDefined();
  });

  it('Should throw an unauthorized exception when there is no token', async () => {
    await expect(() =>
      tokenValidator.validateTokens({ token: '' })
    ).rejects.toThrow(new UnauthorizedException());
  });

  it('Should throw an unauthorized exception when there is a GIP token with email unverified and the remote config check is on', async () => {
    mockVerifyIdToken(userId, email, false);
    await expect(() =>
      tokenValidator.validateTokens({ token: GIP_TOKEN_NON_VERIFIED })
    ).rejects.toThrow(new UnauthorizedException());
  });

  it('should throw a 401 unauthorized if a legacy auth token is used', async () => {
    await expect(
      tokenValidator.validateTokens({ token: TOKEN })
    ).rejects.toThrow(UnauthorizedException);
  });

  it('Should throw an unauthorized exception when the auth service token is not valid', async () => {
    await expect(() =>
      tokenValidator.validateTokens({ token: TOKEN + 'lll' })
    ).rejects.toThrow('Unauthorized');
  });

  it('Should return true when the GIP tokn is valid', async () => {
    mockVerifyIdToken(userId, email, true);
    const result = await tokenValidator.validateTokens({ token: GIP_TOKEN });
    expect(result).toBeTruthy();
    expect(typeof result).toBe('string');
    expect(Auth.prototype.verifyIdToken).toHaveBeenCalledWith(GIP_TOKEN, true);
  });

  it('Should throw an unauthorized exception when the GIP token is not valid', async () => {
    jest
      .spyOn(Auth.prototype, 'verifyIdToken')
      .mockRejectedValue(new Error('signature verification failed'));
    await expect(() =>
      tokenValidator.validateTokens({ token: GIP_TOKEN })
    ).rejects.toThrow(new Error('signature verification failed'));
  });

  it('Should throw an unauthorized exception when the GIP token valid but for a third-party', async () => {
    mockVerifyIdToken(userId, email, true);
    await expect(() =>
      tokenValidator.validateTokens({ token: GIP_TOKEN_THIRD_PARTY })
    ).rejects.toThrow(new UnauthorizedException());
  });
});
