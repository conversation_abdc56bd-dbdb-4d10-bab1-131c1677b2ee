import {
  CLIENT_REF,
  DELAY_BETWEEN_GET_CONFIGURATION,
  DELAY_BETWEEN_SET_AND_GET_CONFIGURATION,
  MAX_RETRY_RETRIEVE_SET_CONFIG_STATUS,
} from './device-config.constants';
import {
  ChargerNotFoundError,
  ChargerOfflineError,
} from '@experience/mobile/nest/exception';
import {
  ConfigurationKeys,
  Door,
  DoorConfigurationResponseData,
  GetApi,
  GetConfigurationRequestStatusStatusEnum,
  GetRequestStatusApi,
  SetApi,
  SetBatchConfiguration,
  SetConfiguration,
} from '@experience/shared/axios/assets-configuration-api-client';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  UnableToGetDeviceConfigError,
  UnableToSetDeviceConfigError,
} from './device-config.errors';
import { setTimeout as delay } from 'timers/promises';

interface GetDeviceConfigOptions {
  door?: Door;
  keys?: string[];
}

@Injectable()
export class DeviceConfigService {
  private readonly logger = new Logger(DeviceConfigService.name);

  constructor(
    private readonly getApi: GetApi,
    private readonly setApi: SetApi,
    private readonly statusApi: GetRequestStatusApi,
    @Inject(CLIENT_REF)
    private readonly clientRef: string
  ) {}

  static parseConfig(config: ConfigurationKeys): Record<string, string> {
    if (!config.configurationKey) return {};

    return Object.assign(
      {},
      ...config.configurationKey.map(
        ({ key, value }) => key && { [key]: value }
      )
    );
  }

  async getConfigValue(
    ppid: string,
    options?: GetDeviceConfigOptions
  ): Promise<DoorConfigurationResponseData> {
    this.logger.log(
      {
        ppid,
        door: options?.door ?? Door.A,
        keys: options?.keys ?? [],
      },
      'attempting to get config value from charger...'
    );

    try {
      const { status, data } = await this.getApi.getDoorConfiguration(
        ppid,
        options?.door ?? Door.A,
        options?.keys
      );

      if (status === 410) {
        throw new ChargerOfflineError();
      }

      if (!data?.data || status === 404) {
        throw new ChargerNotFoundError();
      }

      return data.data;
    } catch (error) {
      this.logger.error(
        {
          ppid,
          options,
          error,
        },
        'unable to get device config'
      );

      if (
        error instanceof ChargerOfflineError ||
        error instanceof ChargerNotFoundError
      ) {
        throw error;
      }

      throw new UnableToGetDeviceConfigError(ppid);
    }
  }

  private async hasSetConfigValue(
    requestId: string,
    requestAttempt = 0
  ): Promise<boolean> {
    const { data } = await this.statusApi.getRequestStatus(requestId);

    this.logger.log(
      {
        requestId,
        requestAttempt,
        data,
      },
      'checking for updated config value...'
    );

    switch (data.status) {
      case GetConfigurationRequestStatusStatusEnum.Requested:
        if (requestAttempt < MAX_RETRY_RETRIEVE_SET_CONFIG_STATUS) {
          await delay(DELAY_BETWEEN_GET_CONFIGURATION);

          return this.hasSetConfigValue(requestId, requestAttempt + 1);
        }

        return false;

      case GetConfigurationRequestStatusStatusEnum.Accepted:
        return true;

      default:
        return false;
    }
  }

  private async handleSetConfigValue(
    ppid: string,
    keyValues: Record<string, string>,
    {
      data,
      status,
    }: { data: SetBatchConfiguration | SetConfiguration; status: number }
  ) {
    if (status === 410) {
      throw new ChargerOfflineError();
    }

    if (!data || status === 404) {
      throw new ChargerNotFoundError();
    }

    if (!data.requestId) {
      this.logger.error(
        {
          ppid,
          data,
          status,
        },
        'no request ID returned for setting configuration value, unable to continue.'
      );

      throw new UnableToSetDeviceConfigError(ppid);
    }

    this.logger.log(
      {
        ppid,
        keyValues,
        requestId: data.requestId,
      },
      'set configuration value, waiting before continuing...'
    );

    await delay(DELAY_BETWEEN_SET_AND_GET_CONFIGURATION);

    const result = await this.hasSetConfigValue(data.requestId);

    if (!result) {
      throw new UnableToSetDeviceConfigError(ppid);
    }

    return keyValues;
  }

  async setBatchConfigValues(
    ppid: string,
    keyValues: Record<string, string>,
    door?: Door
  ): Promise<Record<string, string>> {
    this.logger.log(
      {
        ppid,
        keyValues,
        door,
      },
      'attempting to set config values for charger...'
    );

    try {
      const { status, data } = await this.setApi.batchSetDoorConfiguration(
        ppid,
        door ?? Door.A,
        {
          configuration: Object.keys(keyValues).map((key) => ({
            key,
            value: keyValues[key],
          })),
          clientRef: this.clientRef,
        }
      );

      return await this.handleSetConfigValue(ppid, keyValues, { status, data });
    } catch (error) {
      this.logger.error(
        {
          ppid,
          keyValues,
          door,
          error,
        },
        'unable to set device config'
      );

      if (
        error instanceof ChargerOfflineError ||
        error instanceof ChargerNotFoundError
      ) {
        throw error;
      }

      throw new UnableToSetDeviceConfigError(ppid);
    }
  }

  async setConfigValue(
    ppid: string,
    key: string,
    value: string,
    door?: Door
  ): Promise<Record<string, string>> {
    const keyValues = {
      [key]: value,
    };

    this.logger.log(
      {
        ppid,
        keyValues,
        door,
      },
      'attempting to set config value for charger...'
    );

    try {
      const { status, data } = await this.setApi.setDoorConfiguration(
        ppid,
        door ?? Door.A,
        {
          key,
          value,
          clientRef: this.clientRef,
        }
      );

      return await this.handleSetConfigValue(ppid, keyValues, { status, data });
    } catch (error) {
      this.logger.error(
        {
          ppid,
          keyValues,
          door,
          error,
        },
        'unable to set device config'
      );

      if (
        error instanceof ChargerOfflineError ||
        error instanceof ChargerNotFoundError
      ) {
        throw error;
      }

      throw new UnableToSetDeviceConfigError(ppid);
    }
  }
}
