{"name": "billing-api-msw", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/billing-api/api/msw/src", "projectType": "library", "tags": ["mobile"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/libs/mobile/billing/api/msw", "main": "libs/mobile/billing/api/msw/src/node.ts", "tsConfig": "libs/mobile/billing/api/msw/tsconfig.app.json", "webpackConfig": "libs/mobile/billing/api/msw/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "billing-api-api-msw:build"}, "configurations": {"development": {"buildTarget": "billing-api-msw:build:development"}, "production": {"buildTarget": "billing-api-msw:build:production"}}}, "generate-mocks": {"executor": "nx:run-commands", "options": {"commands": [{"description": "Generate mocks using configuration file.", "command": "npx msw-auto-mock libs/mobile/billing/api/contract/openapi3.yaml -o libs/mobile/billing/api/msw/src -c 200,201,202,204", "forwardAllArgs": false}, {"description": "Convert handlers.js to typescript", "command": "mv libs/mobile/billing/api/msw/src/handlers.js libs/mobile/billing/api/msw/src/handlers.ts", "forwardAllArgs": false}, {"description": "Convert and rename node.js to main.ts", "command": "mv libs/mobile/billing/api/msw/src/node.js libs/mobile/billing/api/msw/src/node.ts", "forwardAllArgs": false}, {"description": "<PERSON><PERSON> generated mocks.", "command": "npx nx lint billing-api-msw --fix", "forwardAllArgs": false}, {"description": "Format generated mocks.", "command": "npx prettier libs/mobile/billing/api/msw --write", "forwardAllArgs": false}], "parallel": false}, "outputs": ["{projectRoot}/src/models"]}}}