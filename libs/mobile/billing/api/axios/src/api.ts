/* tslint:disable */
/* eslint-disable */
/**
 * Billing API
 * Billing API service
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface CreateGuestPaymentResponse
 */
export interface CreateGuestPaymentResponse {
  /**
   * The payment intent secret
   * @type {string}
   * @memberof CreateGuestPaymentResponse
   */
  paymentIntent: string | null;
}
/**
 *
 * @export
 * @interface CreateIntentResponse
 */
export interface CreateIntentResponse {
  /**
   * The customer id
   * @type {string}
   * @memberof CreateIntentResponse
   */
  customer: string;
  /**
   * The setup intent secret
   * @type {string}
   * @memberof CreateIntentResponse
   */
  setupIntent: string | null;
  /**
   * The ephemeral key
   * @type {string}
   * @memberof CreateIntentResponse
   */
  ephemeralKey: string;
}
/**
 *
 * @export
 * @interface CreatePaymentRequest
 */
export interface CreatePaymentRequest {
  /**
   * The amount to top up
   * @type {number}
   * @memberof CreatePaymentRequest
   */
  amount: number;
  /**
   * Currency of the amount that is to topup
   * @type {string}
   * @memberof CreatePaymentRequest
   */
  currency: string;
}
/**
 *
 * @export
 * @interface CreateRegisteredUserPaymentResponse
 */
export interface CreateRegisteredUserPaymentResponse {
  /**
   * The payment intent secret
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  paymentIntent: string | null;
  /**
   * The customer id
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  customer: string;
  /**
   * The ephemeral key
   * @type {string}
   * @memberof CreateRegisteredUserPaymentResponse
   */
  ephemeralKey: string;
}
/**
 *
 * @export
 * @interface CustomerUpdateDTO
 */
export interface CustomerUpdateDTO {
  /**
   * The email to update
   * @type {string}
   * @memberof CustomerUpdateDTO
   */
  email: string;
}
/**
 *
 * @export
 * @interface CustomerUpdateResponse
 */
export interface CustomerUpdateResponse {
  /**
   * The email to update
   * @type {string}
   * @memberof CustomerUpdateResponse
   */
  email: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}

/**
 * CustomerApi - axios parameter creator
 * @export
 */
export const CustomerApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Updates a customer record for a user
     * @summary update a user\'s customer record
     * @param {string} authId
     * @param {CustomerUpdateDTO} customerUpdateDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    customerControllerUpdateCustomer: async (
      authId: string,
      customerUpdateDTO: CustomerUpdateDTO,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'authId' is not null or undefined
      assertParamExists('customerControllerUpdateCustomer', 'authId', authId);
      // verify required parameter 'customerUpdateDTO' is not null or undefined
      assertParamExists(
        'customerControllerUpdateCustomer',
        'customerUpdateDTO',
        customerUpdateDTO
      );
      const localVarPath = `/customer/{authId}`.replace(
        `{${'authId'}}`,
        encodeURIComponent(String(authId))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        customerUpdateDTO,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CustomerApi - functional programming interface
 * @export
 */
export const CustomerApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = CustomerApiAxiosParamCreator(configuration);
  return {
    /**
     * Updates a customer record for a user
     * @summary update a user\'s customer record
     * @param {string} authId
     * @param {CustomerUpdateDTO} customerUpdateDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async customerControllerUpdateCustomer(
      authId: string,
      customerUpdateDTO: CustomerUpdateDTO,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CustomerUpdateResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.customerControllerUpdateCustomer(
          authId,
          customerUpdateDTO,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CustomerApi.customerControllerUpdateCustomer']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CustomerApi - factory interface
 * @export
 */
export const CustomerApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CustomerApiFp(configuration);
  return {
    /**
     * Updates a customer record for a user
     * @summary update a user\'s customer record
     * @param {string} authId
     * @param {CustomerUpdateDTO} customerUpdateDTO
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    customerControllerUpdateCustomer(
      authId: string,
      customerUpdateDTO: CustomerUpdateDTO,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CustomerUpdateResponse> {
      return localVarFp
        .customerControllerUpdateCustomer(authId, customerUpdateDTO, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CustomerApi - object-oriented interface
 * @export
 * @class CustomerApi
 * @extends {BaseAPI}
 */
export class CustomerApi extends BaseAPI {
  /**
   * Updates a customer record for a user
   * @summary update a user\'s customer record
   * @param {string} authId
   * @param {CustomerUpdateDTO} customerUpdateDTO
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CustomerApi
   */
  public customerControllerUpdateCustomer(
    authId: string,
    customerUpdateDTO: CustomerUpdateDTO,
    options?: RawAxiosRequestConfig
  ) {
    return CustomerApiFp(this.configuration)
      .customerControllerUpdateCustomer(authId, customerUpdateDTO, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get billing API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get billing API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary get billing API health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary get billing API health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PaymentsApi - axios parameter creator
 * @export
 */
export const PaymentsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Creates setup intent for a guest user\'s payment
     * @summary create setup intent for guest payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreateGuestPaymentIntent: async (
      createPaymentRequest: CreatePaymentRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createPaymentRequest' is not null or undefined
      assertParamExists(
        'paymentControllerCreateGuestPaymentIntent',
        'createPaymentRequest',
        createPaymentRequest
      );
      const localVarPath = `/payments/create-payment-intent`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createPaymentRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Creates setup intent for a registered user\'s payment
     * @summary create setup intent for registered user payment
     * @param {string} uid
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreateRegisteredUserPaymentIntent: async (
      uid: string,
      createPaymentRequest: CreatePaymentRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists(
        'paymentControllerCreateRegisteredUserPaymentIntent',
        'uid',
        uid
      );
      // verify required parameter 'createPaymentRequest' is not null or undefined
      assertParamExists(
        'paymentControllerCreateRegisteredUserPaymentIntent',
        'createPaymentRequest',
        createPaymentRequest
      );
      const localVarPath = `/payments/create-payment-intent/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createPaymentRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerSetupIntent: async (
      uid: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'uid' is not null or undefined
      assertParamExists('paymentControllerSetupIntent', 'uid', uid);
      const localVarPath = `/payments/setup-intent/{uid}`.replace(
        `{${'uid'}}`,
        encodeURIComponent(String(uid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PaymentsApi - functional programming interface
 * @export
 */
export const PaymentsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PaymentsApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates setup intent for a guest user\'s payment
     * @summary create setup intent for guest payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerCreateGuestPaymentIntent(
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateGuestPaymentResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerCreateGuestPaymentIntent(
          createPaymentRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PaymentsApi.paymentControllerCreateGuestPaymentIntent'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Creates setup intent for a registered user\'s payment
     * @summary create setup intent for registered user payment
     * @param {string} uid
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerCreateRegisteredUserPaymentIntent(
      uid: string,
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateRegisteredUserPaymentResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerCreateRegisteredUserPaymentIntent(
          uid,
          createPaymentRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PaymentsApi.paymentControllerCreateRegisteredUserPaymentIntent'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async paymentControllerSetupIntent(
      uid: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CreateIntentResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.paymentControllerSetupIntent(
          uid,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PaymentsApi.paymentControllerSetupIntent']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PaymentsApi - factory interface
 * @export
 */
export const PaymentsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PaymentsApiFp(configuration);
  return {
    /**
     * Creates setup intent for a guest user\'s payment
     * @summary create setup intent for guest payment
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreateGuestPaymentIntent(
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateGuestPaymentResponse> {
      return localVarFp
        .paymentControllerCreateGuestPaymentIntent(
          createPaymentRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Creates setup intent for a registered user\'s payment
     * @summary create setup intent for registered user payment
     * @param {string} uid
     * @param {CreatePaymentRequest} createPaymentRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerCreateRegisteredUserPaymentIntent(
      uid: string,
      createPaymentRequest: CreatePaymentRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateRegisteredUserPaymentResponse> {
      return localVarFp
        .paymentControllerCreateRegisteredUserPaymentIntent(
          uid,
          createPaymentRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Creates setup intent for payment
     * @summary create setup intent for payment
     * @param {string} uid
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    paymentControllerSetupIntent(
      uid: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CreateIntentResponse> {
      return localVarFp
        .paymentControllerSetupIntent(uid, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PaymentsApi - object-oriented interface
 * @export
 * @class PaymentsApi
 * @extends {BaseAPI}
 */
export class PaymentsApi extends BaseAPI {
  /**
   * Creates setup intent for a guest user\'s payment
   * @summary create setup intent for guest payment
   * @param {CreatePaymentRequest} createPaymentRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerCreateGuestPaymentIntent(
    createPaymentRequest: CreatePaymentRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerCreateGuestPaymentIntent(createPaymentRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Creates setup intent for a registered user\'s payment
   * @summary create setup intent for registered user payment
   * @param {string} uid
   * @param {CreatePaymentRequest} createPaymentRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerCreateRegisteredUserPaymentIntent(
    uid: string,
    createPaymentRequest: CreatePaymentRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerCreateRegisteredUserPaymentIntent(
        uid,
        createPaymentRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Creates setup intent for payment
   * @summary create setup intent for payment
   * @param {string} uid
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PaymentsApi
   */
  public paymentControllerSetupIntent(
    uid: string,
    options?: RawAxiosRequestConfig
  ) {
    return PaymentsApiFp(this.configuration)
      .paymentControllerSetupIntent(uid, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VersionApi - axios parameter creator
 * @export
 */
export const VersionApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/version`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VersionApi - functional programming interface
 * @export
 */
export const VersionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VersionApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.versionControllerGetVersion(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VersionApi.versionControllerGetVersion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VersionApi - factory interface
 * @export
 */
export const VersionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VersionApiFp(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .versionControllerGetVersion(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VersionApi - object-oriented interface
 * @export
 * @class VersionApi
 * @extends {BaseAPI}
 */
export class VersionApi extends BaseAPI {
  /**
   *
   * @summary get application version
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VersionApi
   */
  public versionControllerGetVersion(options?: RawAxiosRequestConfig) {
    return VersionApiFp(this.configuration)
      .versionControllerGetVersion(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * WebhookApi - axios parameter creator
 * @export
 */
export const WebhookApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * create webhook endpoint for stripe
     * @summary create webhook endpoint for stripe
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerWebhook: async (
      stripeSignature: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'stripeSignature' is not null or undefined
      assertParamExists(
        'webhookControllerWebhook',
        'stripeSignature',
        stripeSignature
      );
      const localVarPath = `/webhook`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (stripeSignature != null) {
        localVarHeaderParameter['stripe-signature'] = String(stripeSignature);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * WebhookApi - functional programming interface
 * @export
 */
export const WebhookApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = WebhookApiAxiosParamCreator(configuration);
  return {
    /**
     * create webhook endpoint for stripe
     * @summary create webhook endpoint for stripe
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async webhookControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.webhookControllerWebhook(
          stripeSignature,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['WebhookApi.webhookControllerWebhook']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * WebhookApi - factory interface
 * @export
 */
export const WebhookApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = WebhookApiFp(configuration);
  return {
    /**
     * create webhook endpoint for stripe
     * @summary create webhook endpoint for stripe
     * @param {string} stripeSignature
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    webhookControllerWebhook(
      stripeSignature: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .webhookControllerWebhook(stripeSignature, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * WebhookApi - object-oriented interface
 * @export
 * @class WebhookApi
 * @extends {BaseAPI}
 */
export class WebhookApi extends BaseAPI {
  /**
   * create webhook endpoint for stripe
   * @summary create webhook endpoint for stripe
   * @param {string} stripeSignature
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof WebhookApi
   */
  public webhookControllerWebhook(
    stripeSignature: string,
    options?: RawAxiosRequestConfig
  ) {
    return WebhookApiFp(this.configuration)
      .webhookControllerWebhook(stripeSignature, options)
      .then((request) => request(this.axios, this.basePath));
  }
}
