{"name": "mobile-slick-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/slick/client/src", "projectType": "library", "tags": ["mobile"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/mobile/slick/client", "main": "libs/mobile/slick/client/src/index.ts", "tsConfig": "libs/mobile/slick/client/tsconfig.lib.json", "assets": ["libs/mobile/slick/client/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/mobile/slick/client/jest.config.ts"}}}}