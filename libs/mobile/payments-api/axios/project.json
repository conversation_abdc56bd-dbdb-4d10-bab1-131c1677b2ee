{"name": "payments-api-axios", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/payments/api/axios/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-axios -i ../contract/openapi3.yaml -o src", "rm openapitools.json", "npx prettier . --write"]}, "configurations": {"docker": {"cwd": "", "commands": ["docker run --rm -v $(pwd):/local -w /local openapitools/openapi-generator-cli generate -g typescript-axios -i libs/mobile/payments-api/contract/openapi3.yaml -o libs/mobile/payments-api/axios/src", "npx prettier libs/mobile/payments-api/axios/src --write"]}}}}, "implicitDependencies": [], "tags": ["mobile"]}