openapi: 3.0.0
paths:
  /health:
    get:
      operationId: HealthController_check
      summary: get Rewards API health
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &a1
                      database: &a2
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &a3
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *a2
                      redis: *a3
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      tags:
        - Healthcheck
  /bank-accounts/{authId}:
    get:
      operationId: BankAccountController_getAllBankAccounts
      summary: get all bank accounts for the given user
      parameters:
        - name: authId
          required: true
          in: path
          description: The Auth ID of the user who the bank accounts belong to
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BankAccountResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a4
        - Bank Accounts
    post:
      operationId: BankAccountController_createBankAccount
      summary: create bank account for rewards payout
      description: Create bank account for rewards payout
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth id of the user for which a bank account is to be created
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBankAccountRequestDTO'
      responses:
        '201':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponse'
        '404':
          description: Returned when billing account not found
        '422':
          description: Returned when request failed confirmation of Payee
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmationOfPayeeDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a4
  /bank-accounts/{authId}/{bankAccountId}:
    put:
      operationId: BankAccountController_updateBankAccount
      summary: update a bank account (archive old and create a new bank account)
      description: Update a bank account (archive and create a new bank account)
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth id of the user for which the bank account is to be updated
          schema:
            type: string
        - name: bankAccountId
          required: true
          in: path
          description: The bank account ID which should be updated
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBankAccountRequestDTO'
      responses:
        '201':
          description: Returned when account is successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountResponse'
        '404':
          description: Returned when bank account not found
        '422':
          description: Returned when request failed confirmation of Payee
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmationOfPayeeDTO'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a4
    delete:
      operationId: BankAccountController_archiveBankAccount
      summary: archive a bank account that will no longer be used
      description: Archive bank account that will no longer be used
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth id of the user for which the bank account is to be archived
          schema:
            type: string
        - name: bankAccountId
          required: true
          in: path
          description: The bank account ID which should be archived
          schema:
            type: string
      responses:
        '204':
          description: Returned when account is successfully archived
        '404':
          description: Returned when billing account or bank account not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a4
  /recipients/{authId}:
    post:
      operationId: RecipientsController_createBillingAccount
      summary: creates a stripe recipient for the given user
      description: For a given user, create a Stripe recipient account
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth ID of user who's account is being created
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRecipientDTO'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipientDTO'
        '404':
          description: User profile was not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Recipients
  /payments/{authId}/transactions:
    get:
      operationId: PaymentsController_getTransactions
      summary: retrieves rewards stripe transactions for the given user
      description: For a given user, retrieve their rewards Stripe transactions
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth ID of the user who's transactions we are retrieving
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionDTO'
        '404':
          description: User profile was not found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: &a5
        - payments
    post:
      operationId: PaymentsController_createPaymentTransaction
      summary: Pay a given user
      description: For a given user, pay them
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth ID of the user who is receiving the payment
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentTransactionDTO'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionDTO'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '403':
          description: Bank account does not belong to given user
          content:
            application/json:
              schema:
                example:
                  statusCode: 403
                  message: Forbidden
        '404':
          description: No bank account for the provided ID was found
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags: *a5
  /balance/{authId}:
    get:
      operationId: BalanceController_getRewardsBalance
      summary: retrieve rewards balance for the given authId
      description: Retrieve rewards balance for the given authId
      parameters:
        - name: authId
          required: true
          in: path
          description: The auth ID of the user whose rewards should be retrieved
          schema:
            type: string
      responses:
        '200':
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RewardsDto'
        '404':
          description: User was not found OR user has no chargers which support rewards
          content:
            application/json:
              schema:
                example:
                  statusCode: 404
                  message: Not Found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - balance
  /webhook:
    post:
      operationId: WebhookController_handleWebhook
      parameters:
        - name: Stripe-Signature
          in: header
          description: Used for verifying the request
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Buffer'
      responses:
        '200':
          description: The received event has been successfully sent for processing
        '400':
          description: The event is not valid
          content:
            application/json:
              schema:
                example:
                  statusCode: 400
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                example:
                  statusCode: 500
                  message: Internal Server Error
      tags:
        - Webhook
info:
  title: Payments API
  description: Payments API service
  version: 1.0.0
  contact: {}
tags: []
servers: []
components:
  schemas:
    BankAccountResponse:
      type: object
      properties:
        id:
          type: string
          description: The ID of the bank account
        name:
          type: string
          description: The name associated with the bank account
        last4:
          type: string
          description: The last 4 digits of the account number
      required:
        - id
        - name
        - last4
    StripeAccountAddressDTO:
      type: object
      properties:
        city:
          type: string
          description: The city of the address
          example: London
        country:
          type: string
          description: The country of the address
          example: GB
        county:
          type: string
          description: The county of the address
          example: London
        line1:
          type: string
          description: The first line of the address
          example: 222 Gray's Inn Road
        line2:
          type: string
          description: The second line of the address
        postcode:
          type: string
          description: The post code of the address
          example: WC1X 8HB
      required:
        - city
        - country
        - county
        - line1
        - postcode
    CreateBankAccountRequestDTO:
      type: object
      properties:
        name:
          type: string
          description: The name associated with the bank account for verification
          example: Jenny Rosen
        email:
          type: string
          description: The email associated with the recipient
        accountNumber:
          type: string
          description: The account number of the bank account
          example: '********'
        sortCode:
          type: string
          description: The sort code of the bank account
          example: '108800'
        address:
          description: The address for the billing account
          allOf:
            - $ref: '#/components/schemas/StripeAccountAddressDTO'
        firstName:
          type: string
          description: The first name for the billing account
          example: Jenny
        lastName:
          type: string
          description: The last name of the billing account
          example: Rosen
      required:
        - name
        - email
        - accountNumber
        - sortCode
        - address
        - firstName
        - lastName
    ConfirmationOfPayeeDTO:
      type: object
      properties:
        status:
          type: string
          description: The status of the confirmation of payee check
          enum:
            - MATCH
            - MISMATCH
            - PARTIAL_MATCH
            - UNAVAILABLE
        provided:
          type: string
          description: The name provided
          example: Jenny Rosen
        suggested:
          type: string
          description:
            The suggested name associated with the bank account if status is
            PARTIAL
          example: Jennifier Rosen
          nullable: true
      required:
        - status
        - provided
        - suggested
    CreateRecipientDTO:
      type: object
      properties:
        email:
          type: string
          description: The email for the recipient
        address:
          description: The address for the recipient
          allOf:
            - $ref: '#/components/schemas/StripeAccountAddressDTO'
        firstName:
          type: string
          description: The first name for the recipient
          example: Jenny
        lastName:
          type: string
          description: The last name of the recipient
          example: Rosen
      required:
        - email
        - address
        - firstName
        - lastName
    RecipientDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the recipient
      required:
        - id
    TransactionDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of the transaction
        value:
          type: number
          description:
            The value of the transaction in the lowest currency unit (pence for
            GBP)
        currency:
          type: string
          description: The currency that the transaction is in
        status:
          type: string
          description: The current status of the transaction
          enum:
            - PENDING
            - CANCELLED
            - RETURNED
            - POSTED
            - FAILED
        createdAt:
          type: string
          description: The date/time this payment was made
      required:
        - id
        - value
        - currency
        - status
        - createdAt
    CreatePaymentTransactionDTO:
      type: object
      properties:
        bankAccountId:
          type: string
          description: The ID of the bank account to make the payout from
        currency:
          type: string
          description: The currency to payout in
          enum:
            - GBP
        amount:
          type: number
          description: The amount to pay out in the lowest unit of the currency
      required:
        - bankAccountId
        - currency
        - amount
    RewardsChargerDto:
      type: object
      properties:
        id:
          type: string
          description: The PPID of the charger
          example: PSL-12345
        miles:
          type: number
          description: The amount of rewardable miles for this charger
          example: 10.5
      required:
        - id
        - miles
    RewardsDto:
      type: object
      properties:
        totalMiles:
          type: number
          description: The total amount of reward miles from individual chargers
          example: 10.5
        balance:
          type: number
          description: The balance in the lowest unit of currency (pence for GBP)
          example: 5000
        currency:
          type: string
          description: The currency represented by the balance
          example: GBP
        payoutThreshold:
          type: number
          description: The minimum amount of rewards miles required for payout
          example: 150
        chargers:
          description: The chargers for which the user is eligible for the reward
          type: array
          items:
            $ref: '#/components/schemas/RewardsChargerDto'
      required:
        - totalMiles
        - balance
        - currency
        - payoutThreshold
        - chargers
    Buffer:
      type: object
      properties: {}
