/* tslint:disable */
/* eslint-disable */
/**
 * Installer BFF
 * Backend for Frontend for the installer app
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from './base';

/**
 *
 * @export
 * @interface AnonymisedAddress
 */
export interface AnonymisedAddress {
  /**
   * postcode
   * @type {string}
   * @memberof AnonymisedAddress
   */
  postcode: string;
}
/**
 *
 * @export
 * @interface AnonymisedAppointment
 */
export interface AnonymisedAppointment {
  /**
   * appointment reference
   * @type {string}
   * @memberof AnonymisedAppointment
   */
  reference: string;
  /**
   * anonymised address object
   * @type {AnonymisedAddress}
   * @memberof AnonymisedAppointment
   */
  address: AnonymisedAddress;
}
/**
 *
 * @export
 * @interface AppointmentReferencePayload
 */
export interface AppointmentReferencePayload {
  /**
   * appointment reference
   * @type {string}
   * @memberof AppointmentReferencePayload
   */
  reference: string;
}
/**
 *
 * @export
 * @interface ChargerSettingsPayload
 */
export interface ChargerSettingsPayload {
  /**
   *
   * @type {DeviceInformation}
   * @memberof ChargerSettingsPayload
   */
  deviceInformation: DeviceInformation;
  /**
   *
   * @type {DeviceConfig}
   * @memberof ChargerSettingsPayload
   */
  deviceConfig?: DeviceConfig;
  /**
   *
   * @type {SensorReadings}
   * @memberof ChargerSettingsPayload
   */
  sensorReadings?: SensorReadings;
  /**
   *
   * @type {Connectivity}
   * @memberof ChargerSettingsPayload
   */
  connectivity?: Connectivity;
}
/**
 *
 * @export
 * @interface CheckForUpgrade
 */
export interface CheckForUpgrade {
  /**
   * Whether a version has been found or not
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  found: boolean;
  /**
   * Whether the app should force upgrade
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  forceUpgrade: boolean;
  /**
   * The message to display to the user
   * @type {boolean}
   * @memberof CheckForUpgrade
   */
  message: boolean;
  /**
   *
   * @type {QueryCheckForUpgrade}
   * @memberof CheckForUpgrade
   */
  query: QueryCheckForUpgrade;
}
/**
 *
 * @export
 * @interface Connectivity
 */
export interface Connectivity {
  /**
   *
   * @type {string}
   * @memberof Connectivity
   */
  wifiConnectedState: string;
  /**
   *
   * @type {number}
   * @memberof Connectivity
   */
  signalStrength: number;
  /**
   *
   * @type {string}
   * @memberof Connectivity
   */
  networkState: ConnectivityNetworkStateEnum;
  /**
   *
   * @type {string}
   * @memberof Connectivity
   */
  networkInterface?: ConnectivityNetworkInterfaceEnum;
}

export const ConnectivityNetworkStateEnum = {
  Disconnected: 'disconnected',
  Internet: 'internet',
  PodPoint: 'pod point',
} as const;

export type ConnectivityNetworkStateEnum =
  (typeof ConnectivityNetworkStateEnum)[keyof typeof ConnectivityNetworkStateEnum];
export const ConnectivityNetworkInterfaceEnum = {
  Disconnected: 'disconnected',
  Wifi: 'wifi',
  Ethernet: 'ethernet',
} as const;

export type ConnectivityNetworkInterfaceEnum =
  (typeof ConnectivityNetworkInterfaceEnum)[keyof typeof ConnectivityNetworkInterfaceEnum];

/**
 *
 * @export
 * @interface CreateUserProfilePayload
 */
export interface CreateUserProfilePayload {
  /**
   * First name
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  firstName: string;
  /**
   * Last name
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  lastName: string;
  /**
   * Phone number
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  phoneNumber: string;
  /**
   * The type of profile that\'s being created. Validation depends on this value
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  companyType: CreateUserProfilePayloadCompanyTypeEnum;
  /**
   * Company name must be included for companyType of company, but not for sole_trader
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  companyName?: string;
  /**
   * Company number must be included for companyType of company, but not for sole_trader
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  companyNumber?: string;
  /**
   * Allowing marketing consent
   * @type {boolean}
   * @memberof CreateUserProfilePayload
   */
  marketingConsent: boolean;
  /**
   * The type of installer
   * @type {string}
   * @memberof CreateUserProfilePayload
   */
  installerType?: CreateUserProfilePayloadInstallerTypeEnum;
}

export const CreateUserProfilePayloadCompanyTypeEnum = {
  Company: 'company',
  SoleTrader: 'sole_trader',
} as const;

export type CreateUserProfilePayloadCompanyTypeEnum =
  (typeof CreateUserProfilePayloadCompanyTypeEnum)[keyof typeof CreateUserProfilePayloadCompanyTypeEnum];
export const CreateUserProfilePayloadInstallerTypeEnum = {
  ThirdParty: 'THIRD_PARTY',
  Partner: 'PARTNER',
  PodPoint: 'POD_POINT',
} as const;

export type CreateUserProfilePayloadInstallerTypeEnum =
  (typeof CreateUserProfilePayloadInstallerTypeEnum)[keyof typeof CreateUserProfilePayloadInstallerTypeEnum];

/**
 *
 * @export
 * @interface DeviceConfig
 */
export interface DeviceConfig {
  /**
   *
   * @type {boolean}
   * @memberof DeviceConfig
   */
  powerGenerationSystemInstalled: boolean;
  /**
   *
   * @type {number}
   * @memberof DeviceConfig
   */
  breakerSize: number;
  /**
   *
   * @type {boolean}
   * @memberof DeviceConfig
   */
  powerBalancingSensorInstalled: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DeviceConfig
   */
  powerBalancingEnabled: boolean;
  /**
   *
   * @type {number}
   * @memberof DeviceConfig
   */
  householdMaxSupply: number;
  /**
   *
   * @type {number}
   * @memberof DeviceConfig
   */
  powerRatingPerPhase: number;
  /**
   *
   * @type {boolean}
   * @memberof DeviceConfig
   */
  outOfService: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DeviceConfig
   */
  linkySchedulesEnabled?: boolean;
  /**
   *
   * @type {string}
   * @memberof DeviceConfig
   */
  powerBalancingSensor?: DeviceConfigPowerBalancingSensorEnum;
}

export const DeviceConfigPowerBalancingSensorEnum = {
  CtClamp1: 'CT_CLAMP_1',
  Linky: 'LINKY',
  Array: 'ARRAY',
  None: 'NONE',
} as const;

export type DeviceConfigPowerBalancingSensorEnum =
  (typeof DeviceConfigPowerBalancingSensorEnum)[keyof typeof DeviceConfigPowerBalancingSensorEnum];

/**
 *
 * @export
 * @interface DeviceInformation
 */
export interface DeviceInformation {
  /**
   *
   * @type {string}
   * @memberof DeviceInformation
   */
  unitSKU: string;
  /**
   *
   * @type {string}
   * @memberof DeviceInformation
   */
  pcbSerialNumber: string;
  /**
   *
   * @type {string}
   * @memberof DeviceInformation
   */
  firmware: string;
  /**
   *
   * @type {string}
   * @memberof DeviceInformation
   */
  pslNumber: string;
  /**
   *
   * @type {string}
   * @memberof DeviceInformation
   */
  wifiMacAddress: string;
  /**
   * A or B respectively for left or right side of twin or null for single unit
   * @type {string}
   * @memberof DeviceInformation
   */
  socket?: DeviceInformationSocketEnum | null;
}

export const DeviceInformationSocketEnum = {
  A: 'A',
  B: 'B',
} as const;

export type DeviceInformationSocketEnum =
  (typeof DeviceInformationSocketEnum)[keyof typeof DeviceInformationSocketEnum];

/**
 *
 * @export
 * @interface HealthControllerCheck200Response
 */
export interface HealthControllerCheck200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck200Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface HealthControllerCheck200ResponseInfoValue
 */
export interface HealthControllerCheck200ResponseInfoValue {
  [key: string]: any;

  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck200ResponseInfoValue
   */
  status: string;
}
/**
 *
 * @export
 * @interface HealthControllerCheck503Response
 */
export interface HealthControllerCheck503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerCheck503Response
   */
  status?: string;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  info?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  error?: { [key: string]: HealthControllerCheck200ResponseInfoValue } | null;
  /**
   *
   * @type {{ [key: string]: HealthControllerCheck200ResponseInfoValue; }}
   * @memberof HealthControllerCheck503Response
   */
  details?: { [key: string]: HealthControllerCheck200ResponseInfoValue };
}
/**
 *
 * @export
 * @interface InstallSession
 */
export interface InstallSession {
  /**
   * Charger settings JSON object
   * @type {ChargerSettingsPayload}
   * @memberof InstallSession
   */
  chargerSettings: ChargerSettingsPayload;
  /**
   * Appointment reference payload
   * @type {AppointmentReferencePayload}
   * @memberof InstallSession
   */
  appointment?: AppointmentReferencePayload;
  /**
   * Completion date in ISO8601
   * @type {string}
   * @memberof InstallSession
   */
  completedAt?: string | null;
}
/**
 *
 * @export
 * @interface PcbSwapAttachDetachVirtualRequest
 */
export interface PcbSwapAttachDetachVirtualRequest {
  /**
   * The serial number of the new PCB that is being swapped in.
   * @type {string}
   * @memberof PcbSwapAttachDetachVirtualRequest
   */
  pcbSerialNumber: string;
  /**
   * The PSL Number of the unit that the new PCB is being swapped into.
   * @type {string}
   * @memberof PcbSwapAttachDetachVirtualRequest
   */
  pslNumber: string;
  /**
   * For twin units; the door this PCB swap is applicable to
   * @type {string}
   * @memberof PcbSwapAttachDetachVirtualRequest
   */
  door?: PcbSwapAttachDetachVirtualRequestDoorEnum;
}

export const PcbSwapAttachDetachVirtualRequestDoorEnum = {
  A: 'A',
  B: 'B',
} as const;

export type PcbSwapAttachDetachVirtualRequestDoorEnum =
  (typeof PcbSwapAttachDetachVirtualRequestDoorEnum)[keyof typeof PcbSwapAttachDetachVirtualRequestDoorEnum];

/**
 *
 * @export
 * @interface PcbSwapAttachVirtualResponse
 */
export interface PcbSwapAttachVirtualResponse {
  /**
   * Summary of the successful attaching of a virtual PCB.
   * @type {string}
   * @memberof PcbSwapAttachVirtualResponse
   */
  message: string;
}
/**
 *
 * @export
 * @interface PcbSwapDetachVirtualResponse
 */
export interface PcbSwapDetachVirtualResponse {
  /**
   * Summary of the successful detaching of a virtual PCB.
   * @type {string}
   * @memberof PcbSwapDetachVirtualResponse
   */
  message: string;
}
/**
 *
 * @export
 * @interface PcbSwapRequest
 */
export interface PcbSwapRequest {
  /**
   * The serial number of the new PCB that is being swapped in.
   * @type {string}
   * @memberof PcbSwapRequest
   */
  pcbSerialNumber: string;
  /**
   * The PSL Number of the unit that the new PCB is being swapped into.
   * @type {string}
   * @memberof PcbSwapRequest
   */
  pslNumber: string;
  /**
   * For twin units; the door this PCB swap is applicable to
   * @type {string}
   * @memberof PcbSwapRequest
   */
  door?: PcbSwapRequestDoorEnum;
}

export const PcbSwapRequestDoorEnum = {
  A: 'A',
  B: 'B',
} as const;

export type PcbSwapRequestDoorEnum =
  (typeof PcbSwapRequestDoorEnum)[keyof typeof PcbSwapRequestDoorEnum];

/**
 *
 * @export
 * @interface PcbSwapSuccessfulResponse
 */
export interface PcbSwapSuccessfulResponse {
  /**
   * Status of the successful PCB swap.
   * @type {string}
   * @memberof PcbSwapSuccessfulResponse
   */
  status: PcbSwapSuccessfulResponseStatusEnum;
  /**
   * Summary of the successful PCB swap.
   * @type {string}
   * @memberof PcbSwapSuccessfulResponse
   */
  message: string;
}

export const PcbSwapSuccessfulResponseStatusEnum = {
  Success: 'Success',
  PartiallySuccessful: 'Partially Successful',
} as const;

export type PcbSwapSuccessfulResponseStatusEnum =
  (typeof PcbSwapSuccessfulResponseStatusEnum)[keyof typeof PcbSwapSuccessfulResponseStatusEnum];

/**
 *
 * @export
 * @interface QueryCheckForUpgrade
 */
export interface QueryCheckForUpgrade {
  /**
   * The app name
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_name: string;
  /**
   * The app version
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_version: string;
  /**
   * The app platform
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  platform: string;
  /**
   * The app environment
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  environment: string;
  /**
   * The app language
   * @type {string}
   * @memberof QueryCheckForUpgrade
   */
  app_language: string;
}
/**
 *
 * @export
 * @interface ResetPasswordRequestDto
 */
export interface ResetPasswordRequestDto {
  /**
   * Email
   * @type {string}
   * @memberof ResetPasswordRequestDto
   */
  email: string;
  /**
   * Continue url
   * @type {string}
   * @memberof ResetPasswordRequestDto
   */
  reset_password_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendRecoverFactorRequest
 */
export interface SendRecoverFactorRequest {
  /**
   * Email
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  email: string;
  /**
   * Redirect URL after factor recovery
   * @type {string}
   * @memberof SendRecoverFactorRequest
   */
  recover_factor_continue_url?: string;
}
/**
 *
 * @export
 * @interface SendVerifyAndChangeEmailRequest
 */
export interface SendVerifyAndChangeEmailRequest {
  /**
   * New Email
   * @type {string}
   * @memberof SendVerifyAndChangeEmailRequest
   */
  newEmail: string;
}
/**
 *
 * @export
 * @interface SensorReadings
 */
export interface SensorReadings {
  /**
   *
   * @type {number}
   * @memberof SensorReadings
   */
  powerBalancingCt: number;
}
/**
 *
 * @export
 * @interface UpdateEmailRequest
 */
export interface UpdateEmailRequest {
  /**
   * New email
   * @type {string}
   * @memberof UpdateEmailRequest
   */
  newEmail: string;
}
/**
 *
 * @export
 * @interface UpsertInstallResponse
 */
export interface UpsertInstallResponse {
  /**
   * first completed at date
   * @type {string}
   * @memberof UpsertInstallResponse
   */
  firstCompletedAt?: string | null;
}
/**
 *
 * @export
 * @interface UserProfile
 */
export interface UserProfile {
  /**
   * Company
   * @type {string}
   * @memberof UserProfile
   */
  companyName?: string;
  /**
   * Company number
   * @type {string}
   * @memberof UserProfile
   */
  companyNumber?: string;
  /**
   * Company
   * @type {string}
   * @memberof UserProfile
   */
  companyType: UserProfileCompanyTypeEnum;
  /**
   * Email
   * @type {string}
   * @memberof UserProfile
   */
  email: string;
  /**
   * First name
   * @type {string}
   * @memberof UserProfile
   */
  firstName: string;
  /**
   * Last name
   * @type {string}
   * @memberof UserProfile
   */
  lastName: string;
  /**
   * Marketing consent
   * @type {boolean}
   * @memberof UserProfile
   */
  marketingConsent: boolean;
  /**
   * Phone number
   * @type {string}
   * @memberof UserProfile
   */
  phoneNumber: string;
  /**
   * The type of installer
   * @type {string}
   * @memberof UserProfile
   */
  installerType?: UserProfileInstallerTypeEnum;
}

export const UserProfileCompanyTypeEnum = {
  Company: 'company',
  SoleTrader: 'sole_trader',
} as const;

export type UserProfileCompanyTypeEnum =
  (typeof UserProfileCompanyTypeEnum)[keyof typeof UserProfileCompanyTypeEnum];
export const UserProfileInstallerTypeEnum = {
  ThirdParty: 'THIRD_PARTY',
  Partner: 'PARTNER',
  PodPoint: 'POD_POINT',
} as const;

export type UserProfileInstallerTypeEnum =
  (typeof UserProfileInstallerTypeEnum)[keyof typeof UserProfileInstallerTypeEnum];

/**
 * AccountApi - axios parameter creator
 * @export
 */
export const AccountApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create a new user profile
     * @summary create a new user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerCreateProfile: async (
      createUserProfilePayload: CreateUserProfilePayload,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createUserProfilePayload' is not null or undefined
      assertParamExists(
        'accountControllerCreateProfile',
        'createUserProfilePayload',
        createUserProfilePayload
      );
      const localVarPath = `/v1/account/profile`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createUserProfilePayload,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Delete a user profile
     * @summary delete a user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerDeleteProfile: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v1/account/profile`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'DELETE',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Retrieve a new user profile
     * @summary retrieve a new user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerGetProfile: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v1/account/profile`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a user\'s email with a new email for an existing email
     * @summary Update user\'s email
     * @param {UpdateEmailRequest} updateEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerUpdateEmail: async (
      updateEmailRequest: UpdateEmailRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'updateEmailRequest' is not null or undefined
      assertParamExists(
        'accountControllerUpdateEmail',
        'updateEmailRequest',
        updateEmailRequest
      );
      const localVarPath = `/v1/account/update-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update a user profile
     * @summary update a user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerUpdateProfile: async (
      createUserProfilePayload: CreateUserProfilePayload,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'createUserProfilePayload' is not null or undefined
      assertParamExists(
        'accountControllerUpdateProfile',
        'createUserProfilePayload',
        createUserProfilePayload
      );
      const localVarPath = `/v1/account/profile`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createUserProfilePayload,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AccountApi - functional programming interface
 * @export
 */
export const AccountApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AccountApiAxiosParamCreator(configuration);
  return {
    /**
     * Create a new user profile
     * @summary create a new user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountControllerCreateProfile(
      createUserProfilePayload: CreateUserProfilePayload,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountControllerCreateProfile(
          createUserProfilePayload,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AccountApi.accountControllerCreateProfile']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Delete a user profile
     * @summary delete a user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountControllerDeleteProfile(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountControllerDeleteProfile(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AccountApi.accountControllerDeleteProfile']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Retrieve a new user profile
     * @summary retrieve a new user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountControllerGetProfile(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserProfile>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountControllerGetProfile(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AccountApi.accountControllerGetProfile']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a user\'s email with a new email for an existing email
     * @summary Update user\'s email
     * @param {UpdateEmailRequest} updateEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountControllerUpdateEmail(
      updateEmailRequest: UpdateEmailRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UpdateEmailRequest>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountControllerUpdateEmail(
          updateEmailRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AccountApi.accountControllerUpdateEmail']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Update a user profile
     * @summary update a user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async accountControllerUpdateProfile(
      createUserProfilePayload: CreateUserProfilePayload,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.accountControllerUpdateProfile(
          createUserProfilePayload,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AccountApi.accountControllerUpdateProfile']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AccountApi - factory interface
 * @export
 */
export const AccountApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AccountApiFp(configuration);
  return {
    /**
     * Create a new user profile
     * @summary create a new user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerCreateProfile(
      createUserProfilePayload: CreateUserProfilePayload,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountControllerCreateProfile(createUserProfilePayload, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Delete a user profile
     * @summary delete a user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerDeleteProfile(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountControllerDeleteProfile(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Retrieve a new user profile
     * @summary retrieve a new user profile
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerGetProfile(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UserProfile> {
      return localVarFp
        .accountControllerGetProfile(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a user\'s email with a new email for an existing email
     * @summary Update user\'s email
     * @param {UpdateEmailRequest} updateEmailRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerUpdateEmail(
      updateEmailRequest: UpdateEmailRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UpdateEmailRequest> {
      return localVarFp
        .accountControllerUpdateEmail(updateEmailRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update a user profile
     * @summary update a user profile
     * @param {CreateUserProfilePayload} createUserProfilePayload
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    accountControllerUpdateProfile(
      createUserProfilePayload: CreateUserProfilePayload,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .accountControllerUpdateProfile(createUserProfilePayload, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AccountApi - object-oriented interface
 * @export
 * @class AccountApi
 * @extends {BaseAPI}
 */
export class AccountApi extends BaseAPI {
  /**
   * Create a new user profile
   * @summary create a new user profile
   * @param {CreateUserProfilePayload} createUserProfilePayload
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AccountApi
   */
  public accountControllerCreateProfile(
    createUserProfilePayload: CreateUserProfilePayload,
    options?: RawAxiosRequestConfig
  ) {
    return AccountApiFp(this.configuration)
      .accountControllerCreateProfile(createUserProfilePayload, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Delete a user profile
   * @summary delete a user profile
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AccountApi
   */
  public accountControllerDeleteProfile(options?: RawAxiosRequestConfig) {
    return AccountApiFp(this.configuration)
      .accountControllerDeleteProfile(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Retrieve a new user profile
   * @summary retrieve a new user profile
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AccountApi
   */
  public accountControllerGetProfile(options?: RawAxiosRequestConfig) {
    return AccountApiFp(this.configuration)
      .accountControllerGetProfile(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a user\'s email with a new email for an existing email
   * @summary Update user\'s email
   * @param {UpdateEmailRequest} updateEmailRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AccountApi
   */
  public accountControllerUpdateEmail(
    updateEmailRequest: UpdateEmailRequest,
    options?: RawAxiosRequestConfig
  ) {
    return AccountApiFp(this.configuration)
      .accountControllerUpdateEmail(updateEmailRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update a user profile
   * @summary update a user profile
   * @param {CreateUserProfilePayload} createUserProfilePayload
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AccountApi
   */
  public accountControllerUpdateProfile(
    createUserProfilePayload: CreateUserProfilePayload,
    options?: RawAxiosRequestConfig
  ) {
    return AccountApiFp(this.configuration)
      .accountControllerUpdateProfile(createUserProfilePayload, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * AppointmentsApi - axios parameter creator
 * @export
 */
export const AppointmentsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieve an appointment by reference
     * @summary retrieve a appointment by reference
     * @param {string} appointmentReference The unique reference for the appointment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    appointmentControllerGetAppointmentByReference: async (
      appointmentReference: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'appointmentReference' is not null or undefined
      assertParamExists(
        'appointmentControllerGetAppointmentByReference',
        'appointmentReference',
        appointmentReference
      );
      const localVarPath = `/v1/appointments/{appointmentReference}`.replace(
        `{${'appointmentReference'}}`,
        encodeURIComponent(String(appointmentReference))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AppointmentsApi - functional programming interface
 * @export
 */
export const AppointmentsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    AppointmentsApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve an appointment by reference
     * @summary retrieve a appointment by reference
     * @param {string} appointmentReference The unique reference for the appointment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async appointmentControllerGetAppointmentByReference(
      appointmentReference: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<AnonymisedAppointment>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.appointmentControllerGetAppointmentByReference(
          appointmentReference,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AppointmentsApi.appointmentControllerGetAppointmentByReference'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AppointmentsApi - factory interface
 * @export
 */
export const AppointmentsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AppointmentsApiFp(configuration);
  return {
    /**
     * Retrieve an appointment by reference
     * @summary retrieve a appointment by reference
     * @param {string} appointmentReference The unique reference for the appointment
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    appointmentControllerGetAppointmentByReference(
      appointmentReference: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<AnonymisedAppointment> {
      return localVarFp
        .appointmentControllerGetAppointmentByReference(
          appointmentReference,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AppointmentsApi - object-oriented interface
 * @export
 * @class AppointmentsApi
 * @extends {BaseAPI}
 */
export class AppointmentsApi extends BaseAPI {
  /**
   * Retrieve an appointment by reference
   * @summary retrieve a appointment by reference
   * @param {string} appointmentReference The unique reference for the appointment
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppointmentsApi
   */
  public appointmentControllerGetAppointmentByReference(
    appointmentReference: string,
    options?: RawAxiosRequestConfig
  ) {
    return AppointmentsApiFp(this.configuration)
      .appointmentControllerGetAppointmentByReference(
        appointmentReference,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * AuthApi - axios parameter creator
 * @export
 */
export const AuthApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerGetTelephoneCodes: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v2/auth/telephone-codes`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerRecoverFactor: async (
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendRecoverFactorRequest' is not null or undefined
      assertParamExists(
        'authActionsControllerRecoverFactor',
        'sendRecoverFactorRequest',
        sendRecoverFactorRequest
      );
      const localVarPath = `/v2/auth/recover-factor`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendRecoverFactorRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendEmailVerificationRequest: async (
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v2/auth/email-verification`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendPasswordResetRequest: async (
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'resetPasswordRequestDto' is not null or undefined
      assertParamExists(
        'authActionsControllerSendPasswordResetRequest',
        'resetPasswordRequestDto',
        resetPasswordRequestDto
      );
      const localVarPath = `/v2/auth/password-reset`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        resetPasswordRequestDto,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendVerifyAndChangeEmail: async (
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'sendVerifyAndChangeEmailRequest' is not null or undefined
      assertParamExists(
        'authActionsControllerSendVerifyAndChangeEmail',
        'sendVerifyAndChangeEmailRequest',
        sendVerifyAndChangeEmailRequest
      );
      const localVarPath = `/v2/auth/verify-and-change-email`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      if (acceptLanguage != null) {
        localVarHeaderParameter['Accept-Language'] = String(acceptLanguage);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        sendVerifyAndChangeEmailRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * AuthApi - functional programming interface
 * @export
 */
export const AuthApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = AuthApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async authActionsControllerGetTelephoneCodes(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.authActionsControllerGetTelephoneCodes(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthApi.authActionsControllerGetTelephoneCodes']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async authActionsControllerRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.authActionsControllerRecoverFactor(
          sendRecoverFactorRequest,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['AuthApi.authActionsControllerRecoverFactor']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async authActionsControllerSendEmailVerificationRequest(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.authActionsControllerSendEmailVerificationRequest(
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.authActionsControllerSendEmailVerificationRequest'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async authActionsControllerSendPasswordResetRequest(
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.authActionsControllerSendPasswordResetRequest(
          resetPasswordRequestDto,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.authActionsControllerSendPasswordResetRequest'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async authActionsControllerSendVerifyAndChangeEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.authActionsControllerSendVerifyAndChangeEmail(
          sendVerifyAndChangeEmailRequest,
          acceptLanguage,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'AuthApi.authActionsControllerSendVerifyAndChangeEmail'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * AuthApi - factory interface
 * @export
 */
export const AuthApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = AuthApiFp(configuration);
  return {
    /**
     * Retrieves available telephone codes specified by the Firebase configuration
     * @summary retrieve available telephone codes
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerGetTelephoneCodes(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .authActionsControllerGetTelephoneCodes(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends an email to the user to recover their 2 factor authentication
     * @summary Recover 2 factor autentication for user
     * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerRecoverFactor(
      sendRecoverFactorRequest: SendRecoverFactorRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .authActionsControllerRecoverFactor(
          sendRecoverFactorRequest,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends an email verification email to the user\'s email address
     * @summary verify a user\'s email address
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendEmailVerificationRequest(
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .authActionsControllerSendEmailVerificationRequest(
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a reset password email to the user\'s email address
     * @summary reset a user\'s password
     * @param {ResetPasswordRequestDto} resetPasswordRequestDto
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendPasswordResetRequest(
      resetPasswordRequestDto: ResetPasswordRequestDto,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .authActionsControllerSendPasswordResetRequest(
          resetPasswordRequestDto,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
     * @summary changes a user\'s email address
     * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
     * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    authActionsControllerSendVerifyAndChangeEmail(
      sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
      acceptLanguage?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .authActionsControllerSendVerifyAndChangeEmail(
          sendVerifyAndChangeEmailRequest,
          acceptLanguage,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * AuthApi - object-oriented interface
 * @export
 * @class AuthApi
 * @extends {BaseAPI}
 */
export class AuthApi extends BaseAPI {
  /**
   * Retrieves available telephone codes specified by the Firebase configuration
   * @summary retrieve available telephone codes
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public authActionsControllerGetTelephoneCodes(
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .authActionsControllerGetTelephoneCodes(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends an email to the user to recover their 2 factor authentication
   * @summary Recover 2 factor autentication for user
   * @param {SendRecoverFactorRequest} sendRecoverFactorRequest
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public authActionsControllerRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .authActionsControllerRecoverFactor(
        sendRecoverFactorRequest,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends an email verification email to the user\'s email address
   * @summary verify a user\'s email address
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public authActionsControllerSendEmailVerificationRequest(
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .authActionsControllerSendEmailVerificationRequest(
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a reset password email to the user\'s email address
   * @summary reset a user\'s password
   * @param {ResetPasswordRequestDto} resetPasswordRequestDto
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public authActionsControllerSendPasswordResetRequest(
    resetPasswordRequestDto: ResetPasswordRequestDto,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .authActionsControllerSendPasswordResetRequest(
        resetPasswordRequestDto,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Sends a verification email to the user\'s new email address, updating the user\'s email address once the new email address has been verified
   * @summary changes a user\'s email address
   * @param {SendVerifyAndChangeEmailRequest} sendVerifyAndChangeEmailRequest
   * @param {string} [acceptLanguage] Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AuthApi
   */
  public authActionsControllerSendVerifyAndChangeEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
    acceptLanguage?: string,
    options?: RawAxiosRequestConfig
  ) {
    return AuthApiFp(this.configuration)
      .authActionsControllerSendVerifyAndChangeEmail(
        sendVerifyAndChangeEmailRequest,
        acceptLanguage,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * CheckForUpgradeApi - axios parameter creator
 * @export
 */
export const CheckForUpgradeApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkForUpgradeControllerCheckForUpgrade: async (
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'appName' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appName',
        appName
      );
      // verify required parameter 'appVersion' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appVersion',
        appVersion
      );
      // verify required parameter 'platform' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'platform',
        platform
      );
      // verify required parameter 'environment' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'environment',
        environment
      );
      // verify required parameter 'appLanguage' is not null or undefined
      assertParamExists(
        'checkForUpgradeControllerCheckForUpgrade',
        'appLanguage',
        appLanguage
      );
      const localVarPath = `/v1/check-for-upgrade`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (appName !== undefined) {
        localVarQueryParameter['app_name'] = appName;
      }

      if (appVersion !== undefined) {
        localVarQueryParameter['app_version'] = appVersion;
      }

      if (platform !== undefined) {
        localVarQueryParameter['platform'] = platform;
      }

      if (environment !== undefined) {
        localVarQueryParameter['environment'] = environment;
      }

      if (appLanguage !== undefined) {
        localVarQueryParameter['app_language'] = appLanguage;
      }

      if (xApiKey != null) {
        localVarHeaderParameter['x-api-key'] = String(xApiKey);
      }
      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CheckForUpgradeApi - functional programming interface
 * @export
 */
export const CheckForUpgradeApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    CheckForUpgradeApiAxiosParamCreator(configuration);
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async checkForUpgradeControllerCheckForUpgrade(
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<CheckForUpgrade>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.checkForUpgradeControllerCheckForUpgrade(
          appName,
          appVersion,
          platform,
          environment,
          appLanguage,
          xApiKey,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'CheckForUpgradeApi.checkForUpgradeControllerCheckForUpgrade'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CheckForUpgradeApi - factory interface
 * @export
 */
export const CheckForUpgradeApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = CheckForUpgradeApiFp(configuration);
  return {
    /**
     * For a given version number returns whether a new update is available and upgrade is required
     * @summary returns whether an app version needs to be upgraded or not
     * @param {string} appName The app name
     * @param {string} appVersion The app version
     * @param {string} platform The app platform
     * @param {string} environment The app environment
     * @param {string} appLanguage The app language
     * @param {string} [xApiKey] x-api-key for your project
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    checkForUpgradeControllerCheckForUpgrade(
      appName: string,
      appVersion: string,
      platform: string,
      environment: string,
      appLanguage: string,
      xApiKey?: string,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<CheckForUpgrade> {
      return localVarFp
        .checkForUpgradeControllerCheckForUpgrade(
          appName,
          appVersion,
          platform,
          environment,
          appLanguage,
          xApiKey,
          options
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CheckForUpgradeApi - object-oriented interface
 * @export
 * @class CheckForUpgradeApi
 * @extends {BaseAPI}
 */
export class CheckForUpgradeApi extends BaseAPI {
  /**
   * For a given version number returns whether a new update is available and upgrade is required
   * @summary returns whether an app version needs to be upgraded or not
   * @param {string} appName The app name
   * @param {string} appVersion The app version
   * @param {string} platform The app platform
   * @param {string} environment The app environment
   * @param {string} appLanguage The app language
   * @param {string} [xApiKey] x-api-key for your project
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CheckForUpgradeApi
   */
  public checkForUpgradeControllerCheckForUpgrade(
    appName: string,
    appVersion: string,
    platform: string,
    environment: string,
    appLanguage: string,
    xApiKey?: string,
    options?: RawAxiosRequestConfig
  ) {
    return CheckForUpgradeApiFp(this.configuration)
      .checkForUpgradeControllerCheckForUpgrade(
        appName,
        appVersion,
        platform,
        environment,
        appLanguage,
        xApiKey,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExampleApi - axios parameter creator
 * @export
 */
export const ExampleApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExampleApi - functional programming interface
 * @export
 */
export const ExampleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ExampleApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.helloControllerGetData(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExampleApi.helloControllerGetData']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExampleApi - factory interface
 * @export
 */
export const ExampleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = ExampleApiFp(configuration);
  return {
    /**
     *
     * @summary welcome message endpoint
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    helloControllerGetData(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .helloControllerGetData(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExampleApi - object-oriented interface
 * @export
 * @class ExampleApi
 * @extends {BaseAPI}
 */
export class ExampleApi extends BaseAPI {
  /**
   *
   * @summary welcome message endpoint
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExampleApi
   */
  public helloControllerGetData(options?: RawAxiosRequestConfig) {
    return ExampleApiFp(this.configuration)
      .helloControllerGetData(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * HealthcheckApi - axios parameter creator
 * @export
 */
export const HealthcheckApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get installer bff health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheckSalesforce: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/health/salesforce`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * HealthcheckApi - functional programming interface
 * @export
 */
export const HealthcheckApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    HealthcheckApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get installer bff health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheck(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheck']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async healthControllerCheckSalesforce(
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<HealthControllerCheck200Response>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.healthControllerCheckSalesforce(
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['HealthcheckApi.healthControllerCheckSalesforce']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * HealthcheckApi - factory interface
 * @export
 */
export const HealthcheckApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = HealthcheckApiFp(configuration);
  return {
    /**
     *
     * @summary get installer bff health
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheck(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheck(options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthControllerCheckSalesforce(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<HealthControllerCheck200Response> {
      return localVarFp
        .healthControllerCheckSalesforce(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * HealthcheckApi - object-oriented interface
 * @export
 * @class HealthcheckApi
 * @extends {BaseAPI}
 */
export class HealthcheckApi extends BaseAPI {
  /**
   *
   * @summary get installer bff health
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheck(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheck(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthcheckApi
   */
  public healthControllerCheckSalesforce(options?: RawAxiosRequestConfig) {
    return HealthcheckApiFp(this.configuration)
      .healthControllerCheckSalesforce(options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * InstallsApi - axios parameter creator
 * @export
 */
export const InstallsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Create or update installation\'s image
     * @summary create or update installation\'s image
     * @param {string} [label] Image\\\&#39;s label
     * @param {string} [guid] Install\\\&#39;s guid
     * @param {File} [file]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    installImagesControllerCreateOrUpdate: async (
      label?: string,
      guid?: string,
      file?: File,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/v1/install-images`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;
      const localVarFormParams = new ((configuration &&
        configuration.formDataCtor) ||
        FormData)();

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (label !== undefined) {
        localVarFormParams.append('label', label as any);
      }

      if (guid !== undefined) {
        localVarFormParams.append('guid', guid as any);
      }

      if (file !== undefined) {
        localVarFormParams.append('file', file as any);
      }

      localVarHeaderParameter['Content-Type'] = 'multipart/form-data';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = localVarFormParams;

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create or update installation data
     * @summary create or update installation data
     * @param {string} guid The unique guid that is created on the client and used to reference the installation
     * @param {InstallSession} installSession
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    installsControllerUpdateInstall: async (
      guid: string,
      installSession: InstallSession,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'guid' is not null or undefined
      assertParamExists('installsControllerUpdateInstall', 'guid', guid);
      // verify required parameter 'installSession' is not null or undefined
      assertParamExists(
        'installsControllerUpdateInstall',
        'installSession',
        installSession
      );
      const localVarPath = `/v1/installs/{guid}`.replace(
        `{${'guid'}}`,
        encodeURIComponent(String(guid))
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        installSession,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * InstallsApi - functional programming interface
 * @export
 */
export const InstallsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = InstallsApiAxiosParamCreator(configuration);
  return {
    /**
     * Create or update installation\'s image
     * @summary create or update installation\'s image
     * @param {string} [label] Image\\\&#39;s label
     * @param {string} [guid] Install\\\&#39;s guid
     * @param {File} [file]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async installImagesControllerCreateOrUpdate(
      label?: string,
      guid?: string,
      file?: File,
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.installImagesControllerCreateOrUpdate(
          label,
          guid,
          file,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'InstallsApi.installImagesControllerCreateOrUpdate'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Create or update installation data
     * @summary create or update installation data
     * @param {string} guid The unique guid that is created on the client and used to reference the installation
     * @param {InstallSession} installSession
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async installsControllerUpdateInstall(
      guid: string,
      installSession: InstallSession,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<UpsertInstallResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.installsControllerUpdateInstall(
          guid,
          installSession,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['InstallsApi.installsControllerUpdateInstall']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * InstallsApi - factory interface
 * @export
 */
export const InstallsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = InstallsApiFp(configuration);
  return {
    /**
     * Create or update installation\'s image
     * @summary create or update installation\'s image
     * @param {string} [label] Image\\\&#39;s label
     * @param {string} [guid] Install\\\&#39;s guid
     * @param {File} [file]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    installImagesControllerCreateOrUpdate(
      label?: string,
      guid?: string,
      file?: File,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .installImagesControllerCreateOrUpdate(label, guid, file, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Create or update installation data
     * @summary create or update installation data
     * @param {string} guid The unique guid that is created on the client and used to reference the installation
     * @param {InstallSession} installSession
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    installsControllerUpdateInstall(
      guid: string,
      installSession: InstallSession,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<UpsertInstallResponse> {
      return localVarFp
        .installsControllerUpdateInstall(guid, installSession, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * InstallsApi - object-oriented interface
 * @export
 * @class InstallsApi
 * @extends {BaseAPI}
 */
export class InstallsApi extends BaseAPI {
  /**
   * Create or update installation\'s image
   * @summary create or update installation\'s image
   * @param {string} [label] Image\\\&#39;s label
   * @param {string} [guid] Install\\\&#39;s guid
   * @param {File} [file]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InstallsApi
   */
  public installImagesControllerCreateOrUpdate(
    label?: string,
    guid?: string,
    file?: File,
    options?: RawAxiosRequestConfig
  ) {
    return InstallsApiFp(this.configuration)
      .installImagesControllerCreateOrUpdate(label, guid, file, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create or update installation data
   * @summary create or update installation data
   * @param {string} guid The unique guid that is created on the client and used to reference the installation
   * @param {InstallSession} installSession
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InstallsApi
   */
  public installsControllerUpdateInstall(
    guid: string,
    installSession: InstallSession,
    options?: RawAxiosRequestConfig
  ) {
    return InstallsApiFp(this.configuration)
      .installsControllerUpdateInstall(guid, installSession, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * PCBSwapsApi - axios parameter creator
 * @export
 */
export const PCBSwapsApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     * Allows for whitelisted PPID/PSL numbers to attach a virtual PCB
     * @summary Attach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerAttachVirtualPcb: async (
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSwapAttachDetachVirtualRequest' is not null or undefined
      assertParamExists(
        'pcbSwapsControllerAttachVirtualPcb',
        'pcbSwapAttachDetachVirtualRequest',
        pcbSwapAttachDetachVirtualRequest
      );
      const localVarPath = `/v1/pcb-swaps/virtual-attach`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        pcbSwapAttachDetachVirtualRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Allows for whitelisted PPID/PSL numbers to detach a virtual PCB
     * @summary Detach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerDetachVirtualPcb: async (
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSwapAttachDetachVirtualRequest' is not null or undefined
      assertParamExists(
        'pcbSwapsControllerDetachVirtualPcb',
        'pcbSwapAttachDetachVirtualRequest',
        pcbSwapAttachDetachVirtualRequest
      );
      const localVarPath = `/v1/pcb-swaps/virtual-detach`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        pcbSwapAttachDetachVirtualRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *  Starts (or retries) an attempt to register the physical swap of a PCB in a charger.  This is a potentially long-running operation, so the request may take a while to complete. If a timeout occurs (either on the client, or a server response of 408), the request should be retried to continue waiting for the PCB swap to complete.
     * @summary Start or retry a PCB Swap
     * @param {PcbSwapRequest} pcbSwapRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerStartOrRetryPcbSwap: async (
      pcbSwapRequest: PcbSwapRequest,
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      // verify required parameter 'pcbSwapRequest' is not null or undefined
      assertParamExists(
        'pcbSwapsControllerStartOrRetryPcbSwap',
        'pcbSwapRequest',
        pcbSwapRequest
      );
      const localVarPath = `/v1/pcb-swaps`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        pcbSwapRequest,
        localVarRequestOptions,
        configuration
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * PCBSwapsApi - functional programming interface
 * @export
 */
export const PCBSwapsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = PCBSwapsApiAxiosParamCreator(configuration);
  return {
    /**
     * Allows for whitelisted PPID/PSL numbers to attach a virtual PCB
     * @summary Attach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async pcbSwapsControllerAttachVirtualPcb(
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PcbSwapAttachVirtualResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.pcbSwapsControllerAttachVirtualPcb(
          pcbSwapAttachDetachVirtualRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PCBSwapsApi.pcbSwapsControllerAttachVirtualPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * Allows for whitelisted PPID/PSL numbers to detach a virtual PCB
     * @summary Detach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async pcbSwapsControllerDetachVirtualPcb(
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PcbSwapDetachVirtualResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.pcbSwapsControllerDetachVirtualPcb(
          pcbSwapAttachDetachVirtualRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['PCBSwapsApi.pcbSwapsControllerDetachVirtualPcb']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *  Starts (or retries) an attempt to register the physical swap of a PCB in a charger.  This is a potentially long-running operation, so the request may take a while to complete. If a timeout occurs (either on the client, or a server response of 408), the request should be retried to continue waiting for the PCB swap to complete.
     * @summary Start or retry a PCB Swap
     * @param {PcbSwapRequest} pcbSwapRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async pcbSwapsControllerStartOrRetryPcbSwap(
      pcbSwapRequest: PcbSwapRequest,
      options?: RawAxiosRequestConfig
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string
      ) => AxiosPromise<PcbSwapSuccessfulResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.pcbSwapsControllerStartOrRetryPcbSwap(
          pcbSwapRequest,
          options
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'PCBSwapsApi.pcbSwapsControllerStartOrRetryPcbSwap'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * PCBSwapsApi - factory interface
 * @export
 */
export const PCBSwapsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = PCBSwapsApiFp(configuration);
  return {
    /**
     * Allows for whitelisted PPID/PSL numbers to attach a virtual PCB
     * @summary Attach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerAttachVirtualPcb(
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PcbSwapAttachVirtualResponse> {
      return localVarFp
        .pcbSwapsControllerAttachVirtualPcb(
          pcbSwapAttachDetachVirtualRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Allows for whitelisted PPID/PSL numbers to detach a virtual PCB
     * @summary Detach a virtual PCB
     * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerDetachVirtualPcb(
      pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PcbSwapDetachVirtualResponse> {
      return localVarFp
        .pcbSwapsControllerDetachVirtualPcb(
          pcbSwapAttachDetachVirtualRequest,
          options
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *  Starts (or retries) an attempt to register the physical swap of a PCB in a charger.  This is a potentially long-running operation, so the request may take a while to complete. If a timeout occurs (either on the client, or a server response of 408), the request should be retried to continue waiting for the PCB swap to complete.
     * @summary Start or retry a PCB Swap
     * @param {PcbSwapRequest} pcbSwapRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    pcbSwapsControllerStartOrRetryPcbSwap(
      pcbSwapRequest: PcbSwapRequest,
      options?: RawAxiosRequestConfig
    ): AxiosPromise<PcbSwapSuccessfulResponse> {
      return localVarFp
        .pcbSwapsControllerStartOrRetryPcbSwap(pcbSwapRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * PCBSwapsApi - object-oriented interface
 * @export
 * @class PCBSwapsApi
 * @extends {BaseAPI}
 */
export class PCBSwapsApi extends BaseAPI {
  /**
   * Allows for whitelisted PPID/PSL numbers to attach a virtual PCB
   * @summary Attach a virtual PCB
   * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PCBSwapsApi
   */
  public pcbSwapsControllerAttachVirtualPcb(
    pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PCBSwapsApiFp(this.configuration)
      .pcbSwapsControllerAttachVirtualPcb(
        pcbSwapAttachDetachVirtualRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Allows for whitelisted PPID/PSL numbers to detach a virtual PCB
   * @summary Detach a virtual PCB
   * @param {PcbSwapAttachDetachVirtualRequest} pcbSwapAttachDetachVirtualRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PCBSwapsApi
   */
  public pcbSwapsControllerDetachVirtualPcb(
    pcbSwapAttachDetachVirtualRequest: PcbSwapAttachDetachVirtualRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PCBSwapsApiFp(this.configuration)
      .pcbSwapsControllerDetachVirtualPcb(
        pcbSwapAttachDetachVirtualRequest,
        options
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *  Starts (or retries) an attempt to register the physical swap of a PCB in a charger.  This is a potentially long-running operation, so the request may take a while to complete. If a timeout occurs (either on the client, or a server response of 408), the request should be retried to continue waiting for the PCB swap to complete.
   * @summary Start or retry a PCB Swap
   * @param {PcbSwapRequest} pcbSwapRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof PCBSwapsApi
   */
  public pcbSwapsControllerStartOrRetryPcbSwap(
    pcbSwapRequest: PcbSwapRequest,
    options?: RawAxiosRequestConfig
  ) {
    return PCBSwapsApiFp(this.configuration)
      .pcbSwapsControllerStartOrRetryPcbSwap(pcbSwapRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * VersionApi - axios parameter creator
 * @export
 */
export const VersionApiAxiosParamCreator = function (
  configuration?: Configuration
) {
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion: async (
      options: RawAxiosRequestConfig = {}
    ): Promise<RequestArgs> => {
      const localVarPath = `/version`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * VersionApi - functional programming interface
 * @export
 */
export const VersionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = VersionApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.versionControllerGetVersion(options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['VersionApi.versionControllerGetVersion']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * VersionApi - factory interface
 * @export
 */
export const VersionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance
) {
  const localVarFp = VersionApiFp(configuration);
  return {
    /**
     *
     * @summary get application version
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    versionControllerGetVersion(
      options?: RawAxiosRequestConfig
    ): AxiosPromise<void> {
      return localVarFp
        .versionControllerGetVersion(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * VersionApi - object-oriented interface
 * @export
 * @class VersionApi
 * @extends {BaseAPI}
 */
export class VersionApi extends BaseAPI {
  /**
   *
   * @summary get application version
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof VersionApi
   */
  public versionControllerGetVersion(options?: RawAxiosRequestConfig) {
    return VersionApiFp(this.configuration)
      .versionControllerGetVersion(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
