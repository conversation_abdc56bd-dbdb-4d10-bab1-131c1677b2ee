/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface UpdateUserProfilePayload {
  /**
   * First name
   */
  firstName: string;
  /**
   * Last name
   */
  lastName: string;
  /**
   * Phone number
   */
  phoneNumber: string;
  /**
   * The type of profile that\'s being created. Validation depends on this value
   */
  companyType: UpdateUserProfilePayload.CompanyTypeEnum;
  /**
   * Company name must be included for companyType of company, but not for sole_trader
   */
  companyName?: string;
  /**
   * Company number must be included for companyType of company, but not for sole_trader
   */
  companyNumber?: string;
  /**
   * Allowing marketing consent
   */
  marketingConsent: boolean;
  /**
   * The type of installer
   */
  installerType?: UpdateUserProfilePayload.InstallerTypeEnum;
}
export namespace UpdateUserProfilePayload {
  export type CompanyTypeEnum = 'company' | 'sole_trader';
  export const CompanyTypeEnum = {
    Company: 'company' as CompanyTypeEnum,
    SoleTrader: 'sole_trader' as CompanyTypeEnum,
  };
  export type InstallerTypeEnum = 'THIRD_PARTY' | 'PARTNER' | 'POD_POINT';
  export const InstallerTypeEnum = {
    ThirdParty: 'THIRD_PARTY' as InstallerTypeEnum,
    Partner: 'PARTNER' as InstallerTypeEnum,
    PodPoint: 'POD_POINT' as InstallerTypeEnum,
  };
}
