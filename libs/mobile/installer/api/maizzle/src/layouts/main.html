<!DOCTYPE {{{ page.doctype || 'html' }}}>
<html
  lang="{{ page.language || 'en' }}"
  xmlns:v="urn:schemas-microsoft-com:vml"
>
  <head>
    <meta charset="{{ page.charset || 'utf-8' }}" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no, url=no"
    />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings
            xmlns:o="urn:schemas-microsoft-com:office:office"
          >
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'Quicksand', sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <if condition="page.title">
      <title>{{{ page.title }}}</title>
    </if>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Quicksand"
      rel="stylesheet"
      media="screen"
    />

    <style>
      @config 'tailwind.config.js'
      @tailwind components;
      @tailwind utilities;
    </style>
    <style>
      @font-face {
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        src: url(https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58m-xDwxUD2GF9Zc.woff)
          format('woff');
        unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
          U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309,
          U+0323, U+0329, U+1EA0-1EF9, U+20AB;
      }
      /* latin-ext */
      @font-face {
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        src: url(https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58i-xDwxUD2GF9Zc.woff)
          format('woff');
        unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F,
          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,
          U+A720-A7FF;
      }
      /* latin */
      @font-face {
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        src: url(https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-xDwxUD2GFw.woff)
          format('woff');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC,
          U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    </style>
    <stack name="head" />
  </head>
  <body
    class="[word-break:break-word] [-webkit-font-smoothing:antialiased] {{ page.bodyClass || '' }}"
  >
    <if condition="page.preheader">
      <div class="hidden">
        {{{ page.preheader }}}
        <each loop="item in Array.from(Array(150))">&#847; </each>
      </div>
    </if>
    <div
      role="article"
      aria-roledescription="email"
      aria-label="{{{ page.title || '' }}}"
      lang="{{ page.language || 'en' }}"
    >
      <div
        class="mx-auto py-5 dark-mode:!bg-neutral-800"
        style="background-color: white"
      >
        <div class="w-2/6 mt-6 mr-auto">
          <img src="https://@{{imageUrl}}/email-images/logo.png" />
        </div>
      </div>
      <yield />
      <div class="text-center bg-neutral-900 rounded-b-xl py-6">
        <div class="w-full text-center pb-6">
          <span class="btn_social">
            <a href="https://www.facebook.com/PodPoint/" class="no-underline">
              <img
                src="https://@{{imageUrl}}/email-images/social-facebook.png"
              />
            </a>
          </span>
          <span class="btn_social">
            <a
              href="https://twitter.com/Pod_Point?lang=en"
              class="no-underline"
            >
              <img
                src="https://@{{imageUrl}}/email-images/social-twitter.png"
              />
            </a>
          </span>
          <span class="btn_social">
            <a
              href="https://www.linkedin.com/company/pod-point-ltd-"
              class="no-underline"
            >
              <img
                src="https://@{{imageUrl}}/email-images/social-linkedin.png"
              />
            </a>
          </span>
        </div>
        <div>
          <div class="text-center">
            <p class="text-xs text-white">
              {{page.translations.get_more_from_your_vehicle}}
              <a
                href="https://pod-point.com/guides"
                style="color: #7bab37; text-decoration: none"
                >{{page.translations.our_guides}}</a
              >
            </p>
            <p class="text-xs text-white">
              {{page.translations.read_latest_news}}
              <a
                href="https://pod-point.com/electric-car-news"
                style="color: #7bab37; text-decoration: none"
                >{{page.translations.blog}}</a
              >
            </p>
          </div>
          <div class="text-center px-6 pb-6">
            <a
              href="https://pod-point.com/legal/privacy-and-cookies-policy"
              class="text-xs text-white float-left m-0"
              >{{page.translations.privacy_policy}}</a
            >
            <a
              href="https://pod-point.com/legal/policies"
              class="text-xs text-white float-right m-0"
              >{{page.translations.terms_conditions}}</a
            >
          </div>
        </div>
      </div>
      <div class="mt-4">
        <p class="text-neutral-500 text-xs font-bold text-center">
          Pod Point, 222 Gray's Inn Road, London, WC1X 8HB<br />
          {{page.translations.vat_number}}
        </p>
        <p class="text-neutral-500 text-xs font-bold text-center mt-5 pb-5">
          {{page.translations.copyright}}
        </p>
      </div>
    </div>
  </body>
</html>
