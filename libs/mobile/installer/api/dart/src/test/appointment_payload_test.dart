//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for AppointmentPayload
void main() {
  // final instance = AppointmentPayload();

  group('test AppointmentPayload', () {
    // appointment reference
    // String reference
    test('to test the property `reference`', () async {
      // TODO
    });

    // appointment order object
    // Order order
    test('to test the property `order`', () async {
      // TODO
    });

    // appointment address object
    // Address address
    test('to test the property `address`', () async {
      // TODO
    });

    // appointment location object
    // Location location
    test('to test the property `location`', () async {
      // TODO
    });

    // appointment mpan
    // String mpan
    test('to test the property `mpan`', () async {
      // TODO
    });

    // appointment use case
    // String useCase
    test('to test the property `useCase`', () async {
      // TODO
    });


  });

}
