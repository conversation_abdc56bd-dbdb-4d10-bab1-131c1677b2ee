//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for RemoveFactorRequest
void main() {
  // final instance = RemoveFactorRequest();

  group('test RemoveFactorRequest', () {
    // The email of the user removing the factor
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // The region of the phone
    // Object countryCode
    test('to test the property `countryCode`', () async {
      // TODO
    });

    // The phone number of the user removing the factor
    // String phoneNumber
    test('to test the property `phoneNumber`', () async {
      // TODO
    });


  });

}
