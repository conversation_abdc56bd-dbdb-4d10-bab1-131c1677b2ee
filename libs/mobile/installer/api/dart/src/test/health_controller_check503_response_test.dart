//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for HealthControllerCheck503Response
void main() {
  // final instance = HealthControllerCheck503Response();

  group('test HealthControllerCheck503Response', () {
    // String status
    test('to test the property `status`', () async {
      // TODO
    });

    // Map<String, HealthControllerCheck200ResponseInfoValue> info (default value: const {})
    test('to test the property `info`', () async {
      // TODO
    });

    // Map<String, HealthControllerCheck200ResponseInfoValue> error (default value: const {})
    test('to test the property `error`', () async {
      // TODO
    });

    // Map<String, HealthControllerCheck200ResponseInfoValue> details (default value: const {})
    test('to test the property `details`', () async {
      // TODO
    });


  });

}
