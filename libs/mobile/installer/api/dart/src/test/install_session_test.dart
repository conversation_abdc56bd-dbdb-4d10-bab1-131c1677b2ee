//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for InstallSession
void main() {
  // final instance = InstallSession();

  group('test InstallSession', () {
    // Charger settings JSON object
    // ChargerSettingsPayload chargerSettings
    test('to test the property `chargerSettings`', () async {
      // TODO
    });

    // Completion date in ISO8601
    // String completedAt
    test('to test the property `completedAt`', () async {
      // TODO
    });

    // Google Identity Platform user id
    // String authId
    test('to test the property `authId`', () async {
      // TODO
    });


  });

}
