# openapi.model.SendSignInWithEmailRequest

## Load the model package

```dart
import 'package:openapi/api.dart';
```

## Properties

| Name                 | Type       | Description                                                   | Notes |
| -------------------- | ---------- | ------------------------------------------------------------- | ----- |
| **email**            | **String** | Email                                                         |
| **continueUrl**      | **String** | Continue url                                                  |
| **originServiceUrl** | **String** | Origin service url, external service to authenticate the user |

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
