//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class AuthApi {
  AuthApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// verify a user's email address
  ///
  /// Sends an email verification email to the user's email address
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendEmailVerificationRequest] sendEmailVerificationRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> emailVerificationControllerSendEmailVerificationWithHttpInfo(SendEmailVerificationRequest sendEmailVerificationRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/email-verification';

    // ignore: prefer_final_locals
    Object? postBody = sendEmailVerificationRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// verify a user's email address
  ///
  /// Sends an email verification email to the user's email address
  ///
  /// Parameters:
  ///
  /// * [SendEmailVerificationRequest] sendEmailVerificationRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> emailVerificationControllerSendEmailVerification(SendEmailVerificationRequest sendEmailVerificationRequest, { String? xAppName, }) async {
    final response = await emailVerificationControllerSendEmailVerificationWithHttpInfo(sendEmailVerificationRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// reset a user's password
  ///
  /// Sends a reset password email to the user's email address
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendPasswordResetRequest] sendPasswordResetRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> passwordResetControllerSendPasswordResetWithHttpInfo(SendPasswordResetRequest sendPasswordResetRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/password-reset';

    // ignore: prefer_final_locals
    Object? postBody = sendPasswordResetRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// reset a user's password
  ///
  /// Sends a reset password email to the user's email address
  ///
  /// Parameters:
  ///
  /// * [SendPasswordResetRequest] sendPasswordResetRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> passwordResetControllerSendPasswordReset(SendPasswordResetRequest sendPasswordResetRequest, { String? xAppName, }) async {
    final response = await passwordResetControllerSendPasswordResetWithHttpInfo(sendPasswordResetRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sends a given email an alert to say that their password has updated
  ///
  /// For a given email, notify them of their password being updated
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] xAppName (required):
  ///   The name of the requesting app
  ///
  /// * [Object] body (required):
  Future<Response> passwordResetControllerSendPasswordResetAlertWithHttpInfo(String xAppName, Object body,) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/password-reset-alert';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    headerParams[r'x-app-name'] = parameterToString(xAppName);

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sends a given email an alert to say that their password has updated
  ///
  /// For a given email, notify them of their password being updated
  ///
  /// Parameters:
  ///
  /// * [String] xAppName (required):
  ///   The name of the requesting app
  ///
  /// * [Object] body (required):
  Future<void> passwordResetControllerSendPasswordResetAlert(String xAppName, Object body,) async {
    final response = await passwordResetControllerSendPasswordResetAlertWithHttpInfo(xAppName, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// randomise user password
  ///
  /// Secure an account by randomising the password
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [RandomisePasswordRequest] randomisePasswordRequest (required):
  Future<Response> randomisePasswordControllerRandomisePasswordWithHttpInfo(RandomisePasswordRequest randomisePasswordRequest,) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/randomise-password';

    // ignore: prefer_final_locals
    Object? postBody = randomisePasswordRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// randomise user password
  ///
  /// Secure an account by randomising the password
  ///
  /// Parameters:
  ///
  /// * [RandomisePasswordRequest] randomisePasswordRequest (required):
  Future<void> randomisePasswordControllerRandomisePassword(RandomisePasswordRequest randomisePasswordRequest,) async {
    final response = await randomisePasswordControllerRandomisePasswordWithHttpInfo(randomisePasswordRequest,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// recover a user's factor
  ///
  /// Sends a recover factor email to the user's email address
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendRecoverFactorRequest] sendRecoverFactorRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> recoverFactorControllerSendRecoverFactorWithHttpInfo(SendRecoverFactorRequest sendRecoverFactorRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/recover-factor';

    // ignore: prefer_final_locals
    Object? postBody = sendRecoverFactorRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// recover a user's factor
  ///
  /// Sends a recover factor email to the user's email address
  ///
  /// Parameters:
  ///
  /// * [SendRecoverFactorRequest] sendRecoverFactorRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> recoverFactorControllerSendRecoverFactor(SendRecoverFactorRequest sendRecoverFactorRequest, { String? xAppName, }) async {
    final response = await recoverFactorControllerSendRecoverFactorWithHttpInfo(sendRecoverFactorRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// remove factor
  ///
  /// Remove factor from an account
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [RemoveFactorRequest] removeFactorRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> removeFactorControllerRemoveFactorWithHttpInfo(RemoveFactorRequest removeFactorRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/factor';

    // ignore: prefer_final_locals
    Object? postBody = removeFactorRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// remove factor
  ///
  /// Remove factor from an account
  ///
  /// Parameters:
  ///
  /// * [RemoveFactorRequest] removeFactorRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> removeFactorControllerRemoveFactor(RemoveFactorRequest removeFactorRequest, { String? xAppName, }) async {
    final response = await removeFactorControllerRemoveFactorWithHttpInfo(removeFactorRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// remove factor by id
  ///
  /// Remove factor for a given authId and factorId
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] authId (required):
  ///
  /// * [String] factorId (required):
  Future<Response> removeFactorControllerRemoveFactorByIdWithHttpInfo(String authId, String factorId,) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/factor/{authId}/{factorId}'
      .replaceAll('{authId}', authId)
      .replaceAll('{factorId}', factorId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// remove factor by id
  ///
  /// Remove factor for a given authId and factorId
  ///
  /// Parameters:
  ///
  /// * [String] authId (required):
  ///
  /// * [String] factorId (required):
  Future<void> removeFactorControllerRemoveFactorById(String authId, String factorId,) async {
    final response = await removeFactorControllerRemoveFactorByIdWithHttpInfo(authId, factorId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// get factors
  ///
  /// Retrieve factors for a given authId
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] authId (required):
  Future<Response> retrieveFactorControllerGetFactorsWithHttpInfo(String authId,) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/factor/{authId}'
      .replaceAll('{authId}', authId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// get factors
  ///
  /// Retrieve factors for a given authId
  ///
  /// Parameters:
  ///
  /// * [String] authId (required):
  Future<List<Factor>?> retrieveFactorControllerGetFactors(String authId,) async {
    final response = await retrieveFactorControllerGetFactorsWithHttpInfo(authId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<Factor>') as List)
        .cast<Factor>()
        .toList(growable: false);

    }
    return null;
  }

  /// generate a sign in with email, email for a user
  ///
  /// Sends a sign in with email link to the user's email address
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendSignInWithEmailRequest] sendSignInWithEmailRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> signInWithEmailControllerSendMagicLoginLinkWithHttpInfo(SendSignInWithEmailRequest sendSignInWithEmailRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/sign-in-with-email';

    // ignore: prefer_final_locals
    Object? postBody = sendSignInWithEmailRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// generate a sign in with email, email for a user
  ///
  /// Sends a sign in with email link to the user's email address
  ///
  /// Parameters:
  ///
  /// * [SendSignInWithEmailRequest] sendSignInWithEmailRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> signInWithEmailControllerSendMagicLoginLink(SendSignInWithEmailRequest sendSignInWithEmailRequest, { String? xAppName, }) async {
    final response = await signInWithEmailControllerSendMagicLoginLinkWithHttpInfo(sendSignInWithEmailRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// retrieve available telephone codes
  ///
  /// Retrieves available telephone codes specified by the Firebase configuration
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> telephoneCodesControllerGetTelephoneCodesWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/telephone-codes';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// retrieve available telephone codes
  ///
  /// Retrieves available telephone codes specified by the Firebase configuration
  Future<void> telephoneCodesControllerGetTelephoneCodes() async {
    final response = await telephoneCodesControllerGetTelephoneCodesWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// changes a user's email address
  ///
  /// Sends a verification email to the user's new email address, updating the user's email address once the new email address has been verified
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendVerifyAndChangeEmailRequest] sendVerifyAndChangeEmailRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<Response> verifyAndChangeEmailControllerUpdateEmailWithHttpInfo(SendVerifyAndChangeEmailRequest sendVerifyAndChangeEmailRequest, { String? xAppName, }) async {
    // ignore: prefer_const_declarations
    final path = r'/v1/auth/verify-and-change-email';

    // ignore: prefer_final_locals
    Object? postBody = sendVerifyAndChangeEmailRequest;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (xAppName != null) {
      headerParams[r'x-app-name'] = parameterToString(xAppName);
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// changes a user's email address
  ///
  /// Sends a verification email to the user's new email address, updating the user's email address once the new email address has been verified
  ///
  /// Parameters:
  ///
  /// * [SendVerifyAndChangeEmailRequest] sendVerifyAndChangeEmailRequest (required):
  ///
  /// * [String] xAppName:
  ///   The name of the requesting app
  Future<void> verifyAndChangeEmailControllerUpdateEmail(SendVerifyAndChangeEmailRequest sendVerifyAndChangeEmailRequest, { String? xAppName, }) async {
    final response = await verifyAndChangeEmailControllerUpdateEmailWithHttpInfo(sendVerifyAndChangeEmailRequest,  xAppName: xAppName, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}
